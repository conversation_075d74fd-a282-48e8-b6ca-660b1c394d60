using System;
using System.Linq;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.ViewModels.Dock;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Enums;
using log4net;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// Chamber子系统状态自动更新功能使用示例
    /// </summary>
    public class ChamberAutoUpdateExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ChamberAutoUpdateExample));
        private readonly RobotStatusPanelViewModel _viewModel;
        private readonly S200McuCmdService _mcuService;

        public ChamberAutoUpdateExample()
        {
            // 获取服务实例
            _mcuService = S200McuCmdService.Instance;

            // 创建ViewModel实例（会自动初始化监听器）
            _viewModel = new RobotStatusPanelViewModel();
        }

        /// <summary>
        /// 演示自动更新功能
        /// </summary>
        public async Task DemonstrateAutoUpdate()
        {
            _logger.Info("开始演示Chamber状态自动更新功能");

            try
            {
                // 1. 确保设备已连接并启动监控
                await EnsureDeviceConnectedAndMonitoring();

                // 2. 监听状态变化
                MonitorStatusChanges();

                // 3. 模拟DI/DO值变化
                await SimulateDIChanges();

                // 4. 等待自动更新完成
                await Task.Delay(1000);

                _logger.Info("Chamber状态自动更新功能演示完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"演示过程中发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 确保设备连接并启动监控
        /// </summary>
        private async Task EnsureDeviceConnectedAndMonitoring()
        {
            // 检查设备连接状态
            if (!_mcuService.ChamberA.IsConnected)
            {
                _logger.Info("ChamberA设备未连接，尝试连接...");
                // 这里应该调用实际的连接方法
                // await _mcuService.ConnectAllDevicesAsync(...);
            }

            // 启动线圈监控（总是调用，内部会检查是否已经启动）
            _logger.Info("确保线圈监控已启动...");
            _mcuService.StartCoilsMonitoring();

            _logger.Info("设备连接和监控状态检查完成");
        }

        /// <summary>
        /// 监听状态变化
        /// </summary>
        private void MonitorStatusChanges()
        {
            var chamberAStatus = _viewModel.ChamberASubsystemStatus;
            var chamberBStatus = _viewModel.ChamberBSubsystemStatus;

            // 监听ChamberA状态变化
            chamberAStatus.PropertyChanged += (sender, e) =>
            {
                switch (e.PropertyName)
                {
                    case nameof(ChamberSubsystemStatus.SlitDoorStatus):
                        _logger.Info($"ChamberA Slit Door状态已更新: {chamberAStatus.SlitDoorStatus}");
                        break;
                    case nameof(ChamberSubsystemStatus.LiftPinStatus):
                        _logger.Info($"ChamberA Lift Pin状态已更新: {chamberAStatus.LiftPinStatus}");
                        break;
                    case nameof(ChamberSubsystemStatus.TriggerStatus):
                        _logger.Info($"ChamberA 触发状态已更新: {chamberAStatus.TriggerStatus}");
                        break;
                    case nameof(ChamberSubsystemStatus.RunStatus):
                        _logger.Info($"ChamberA 运行状态已更新: {chamberAStatus.RunStatus}");
                        break;
                }
            };

            // 监听ChamberB状态变化
            chamberBStatus.PropertyChanged += (sender, e) =>
            {
                switch (e.PropertyName)
                {
                    case nameof(ChamberSubsystemStatus.SlitDoorStatus):
                        _logger.Info($"ChamberB Slit Door状态已更新: {chamberBStatus.SlitDoorStatus}");
                        break;
                    case nameof(ChamberSubsystemStatus.LiftPinStatus):
                        _logger.Info($"ChamberB Lift Pin状态已更新: {chamberBStatus.LiftPinStatus}");
                        break;
                    case nameof(ChamberSubsystemStatus.TriggerStatus):
                        _logger.Info($"ChamberB 触发状态已更新: {chamberBStatus.TriggerStatus}");
                        break;
                    case nameof(ChamberSubsystemStatus.RunStatus):
                        _logger.Info($"ChamberB 运行状态已更新: {chamberBStatus.RunStatus}");
                        break;
                }
            };

            _logger.Info("ChamberA和ChamberB状态变化监听器已设置");
        }

        /// <summary>
        /// 模拟DI值变化（仅用于演示）
        /// </summary>
        private async Task SimulateDIChanges()
        {
            _logger.Info("开始模拟DI值变化...");

            // 注意：这只是演示代码，实际环境中DI值由硬件设备控制
            // 在真实环境中，当硬件状态改变时，线圈值会自动更新

            try
            {
                // 获取ChamberA的输入线圈集合
                var chaInputCoils = _mcuService.ChaInputCoils;

                if (chaInputCoils != null && chaInputCoils.Count > 0)
                {
                    _logger.Info($"找到 {chaInputCoils.Count} 个ChamberA输入线圈");

                    // 模拟PDI12 (Slit Door Open Sensor) 状态变化
                    var slitDoorOpenCoil = chaInputCoils.FirstOrDefault(c => c.IoCode == "PDI12");
                    if (slitDoorOpenCoil != null)
                    {
                        _logger.Info($"模拟PDI12状态变化: {slitDoorOpenCoil.Coilvalue} -> {!slitDoorOpenCoil.Coilvalue}");

                        // 注意：在实际应用中，不应该手动修改线圈值
                        // 这里仅用于演示自动更新机制
                        // slitDoorOpenCoil.Coilvalue = !slitDoorOpenCoil.Coilvalue;
                    }

                    // 等待一段时间让自动更新机制生效
                    await Task.Delay(600); // 等待超过防抖延迟时间
                }
                else
                {
                    _logger.Warn("未找到ChamberA输入线圈集合");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"模拟DI值变化时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 手动触发状态更新（对比用）
        /// </summary>
        public void ManualUpdate()
        {
            _logger.Info("手动触发Chamber状态更新");

            // 注意：由于UpdateChamberSubsystemStatus是私有方法，我们无法直接调用
            // 在实际应用中，可以通过UI按钮触发命令，或者创建公共方法
            // 这里我们通过模拟线圈值变化来触发自动更新

            _logger.Info("通过模拟线圈值变化触发自动更新机制");
            // 实际的手动更新需要通过UI界面的按钮来触发

            _logger.Info("手动更新完成");
        }

        /// <summary>
        /// 获取当前状态信息
        /// </summary>
        public void PrintCurrentStatus()
        {
            var chamberAStatus = _viewModel.ChamberASubsystemStatus;
            var chamberBStatus = _viewModel.ChamberBSubsystemStatus;

            _logger.Info("=== 当前ChamberA状态 ===");
            _logger.Info($"触发状态: {chamberAStatus.TriggerStatus}");
            _logger.Info($"运行状态: {chamberAStatus.RunStatus}");
            _logger.Info($"Slit Door状态: {chamberAStatus.SlitDoorStatus}");
            _logger.Info($"Lift Pin状态: {chamberAStatus.LiftPinStatus}");
            _logger.Info($"晶圆准备状态: {chamberAStatus.WaferReadyStatus}");
            _logger.Info($"腔体真空状态: {chamberAStatus.ChamberVacuumStatus}");
            _logger.Info($"负载锁真空状态: {chamberAStatus.LoadlockVacuumStatus}");

            _logger.Info("=== 当前ChamberB状态 ===");
            _logger.Info($"触发状态: {chamberBStatus.TriggerStatus}");
            _logger.Info($"运行状态: {chamberBStatus.RunStatus}");
            _logger.Info($"Slit Door状态: {chamberBStatus.SlitDoorStatus}");
            _logger.Info($"Lift Pin状态: {chamberBStatus.LiftPinStatus}");
            _logger.Info($"晶圆准备状态: {chamberBStatus.WaferReadyStatus}");
            _logger.Info($"腔体真空状态: {chamberBStatus.ChamberVacuumStatus}");
            _logger.Info($"负载锁真空状态: {chamberBStatus.LoadlockVacuumStatus}");
            _logger.Info("========================");
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Dispose()
        {
            _logger.Info("清理Chamber自动更新示例资源");

            // 在实际应用中，ViewModel会在适当的时候自动清理
            // 这里不需要手动清理事件监听器，因为它们由ViewModel管理
        }
    }

    /// <summary>
    /// 使用示例的入口点
    /// </summary>
    public static class ChamberAutoUpdateUsage
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ChamberAutoUpdateUsage));

        /// <summary>
        /// 运行示例
        /// </summary>
        public static async Task RunExample()
        {
            _logger.Info("开始运行Chamber自动更新功能示例");

            var example = new ChamberAutoUpdateExample();

            try
            {
                // 打印初始状态
                example.PrintCurrentStatus();

                // 演示自动更新功能
                await example.DemonstrateAutoUpdate();

                // 打印更新后状态
                example.PrintCurrentStatus();

                // 演示手动更新
                example.ManualUpdate();

                // 最终状态
                example.PrintCurrentStatus();
            }
            catch (Exception ex)
            {
                _logger.Error($"运行示例时发生错误: {ex.Message}", ex);
            }
            finally
            {
                example.Dispose();
            }

            _logger.Info("Chamber自动更新功能示例运行完成");
        }
    }
}
