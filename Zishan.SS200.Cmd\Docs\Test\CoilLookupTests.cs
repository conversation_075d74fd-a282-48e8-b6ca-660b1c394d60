using System;
using System.Linq;
using <PERSON>ishan.SS200.Cmd.Enums;
using <PERSON>ishan.SS200.Cmd.Enums.SS200.IOInterface.Chamber;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Shuttle;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// 线圈查找功能测试类
    /// 用于验证新的线圈查找方法是否正常工作
    /// </summary>
    public class CoilLookupTests
    {
        private readonly S200McuCmdService _mcuService;
        private readonly CoilStatusHelper _coilHelper;

        public CoilLookupTests()
        {
            _mcuService = S200McuCmdService.Instance;
            _coilHelper = new CoilStatusHelper(_mcuService);
        }

        /// <summary>
        /// 测试IO代码提取功能
        /// </summary>
        public void TestIOCodeExtraction()
        {
            Console.WriteLine("=== 测试IO代码提取功能 ===");

            // 测试Shuttle DI枚举
            var shuttleDI = EnuShuttleDICodes.SDI1_CassetteDoorUpSensor;
            var shuttleDICode = shuttleDI.GetIOCode();
            Console.WriteLine($"Shuttle DI: {shuttleDI} -> IO代码: {shuttleDICode}");

            // 测试Shuttle DO枚举
            var shuttleDO = EnuShuttleDOCodes.SDO1_CassetteDoorCylinderUp;
            var shuttleDOCode = shuttleDO.GetIOCode();
            Console.WriteLine($"Shuttle DO: {shuttleDO} -> IO代码: {shuttleDOCode}");

            // 测试Chamber DI枚举
            var chamberDI = EnuChamberDICodes.PDI12_SlitDoorOpenSensor;
            var chamberDICode = chamberDI.GetIOCode();
            Console.WriteLine($"Chamber DI: {chamberDI} -> IO代码: {chamberDICode}");

            // 测试Chamber DO枚举
            var chamberDO = EnuChamberDOCodes.PDO10_SlitDoorOpen;
            var chamberDOCode = chamberDO.GetIOCode();
            Console.WriteLine($"Chamber DO: {chamberDO} -> IO代码: {chamberDOCode}");

            // 测试Robot DI枚举
            var robotDI = EnuRobotDICodes.RDI1_PaddleSensor1Left;
            var robotDICode = robotDI.GetIOCode();
            Console.WriteLine($"Robot DI: {robotDI} -> IO代码: {robotDICode}");

            Console.WriteLine();
        }

        /// <summary>
        /// 测试枚举类型判断功能
        /// </summary>
        public void TestEnumTypeDetection()
        {
            Console.WriteLine("=== 测试枚举类型判断功能 ===");

            var shuttleDI = EnuShuttleDICodes.SDI1_CassetteDoorUpSensor;
            var shuttleDO = EnuShuttleDOCodes.SDO1_CassetteDoorCylinderUp;
            var chamberDI = EnuChamberDICodes.PDI12_SlitDoorOpenSensor;
            var robotDI = EnuRobotDICodes.RDI1_PaddleSensor1Left;

            Console.WriteLine($"{shuttleDI} 是DI: {shuttleDI.IsDigitalInput()}, 是DO: {shuttleDI.IsDigitalOutput()}");
            Console.WriteLine($"{shuttleDO} 是DI: {shuttleDO.IsDigitalInput()}, 是DO: {shuttleDO.IsDigitalOutput()}");
            Console.WriteLine($"{chamberDI} 是DI: {chamberDI.IsDigitalInput()}, 是DO: {chamberDI.IsDigitalOutput()}");
            Console.WriteLine($"{robotDI} 是DI: {robotDI.IsDigitalInput()}, 是DO: {robotDI.IsDigitalOutput()}");

            Console.WriteLine();
        }

        /// <summary>
        /// 测试线圈查找功能
        /// </summary>
        public void TestCoilLookup()
        {
            Console.WriteLine("=== 测试线圈查找功能 ===");

            try
            {
                // 测试Chamber线圈查找
                var chamberCoil = _mcuService.GetInputCoilByEnum(
                    EnuMcuDeviceType.ChamberA,
                    EnuChamberDICodes.PDI12_SlitDoorOpenSensor);

                if (chamberCoil != null)
                {
                    Console.WriteLine($"找到Chamber线圈: {chamberCoil.IoCode} - {chamberCoil.Title}");
                    Console.WriteLine($"  地址: {chamberCoil.Address}, 值: {chamberCoil.Coilvalue}");
                }
                else
                {
                    Console.WriteLine("未找到Chamber线圈");
                }

                // 测试Shuttle线圈查找
                var shuttleCoil = _mcuService.GetInputCoilByEnum(
                    EnuMcuDeviceType.Shuttle,
                    EnuShuttleDICodes.SDI1_CassetteDoorUpSensor);

                if (shuttleCoil != null)
                {
                    Console.WriteLine($"找到Shuttle线圈: {shuttleCoil.IoCode} - {shuttleCoil.Title}");
                    Console.WriteLine($"  地址: {shuttleCoil.Address}, 值: {shuttleCoil.Coilvalue}");
                }
                else
                {
                    Console.WriteLine("未找到Shuttle线圈");
                }

                // 测试Robot线圈查找
                var robotCoil = _mcuService.GetInputCoilByEnum(
                    EnuMcuDeviceType.Robot,
                    EnuRobotDICodes.RDI1_PaddleSensor1Left);

                if (robotCoil != null)
                {
                    Console.WriteLine($"找到Robot线圈: {robotCoil.IoCode} - {robotCoil.Title}");
                    Console.WriteLine($"  地址: {robotCoil.Address}, 值: {robotCoil.Coilvalue}");
                }
                else
                {
                    Console.WriteLine("未找到Robot线圈");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"线圈查找测试出错: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试CoilStatusHelper功能
        /// </summary>
        public void TestCoilStatusHelper()
        {
            Console.WriteLine("=== 测试CoilStatusHelper功能 ===");

            try
            {
                // 测试单个线圈状态查询
                var slitDoorOpenValue = _coilHelper.GetCoilValue(
                    EnuMcuDeviceType.ChamberA,
                    EnuChamberDICodes.PDI12_SlitDoorOpenSensor);
                Console.WriteLine($"Chamber A Slit Door Open 传感器值: {slitDoorOpenValue}");

                // 测试多个线圈状态查询
                var shuttleRotateActive = _coilHelper.AreAllCoilsActive(EnuMcuDeviceType.Shuttle,
                    EnuShuttleDICodes.SDI13_ShuttleRotateSensor1,
                    EnuShuttleDICodes.SDI14_ShuttleRotateSensor2);
                Console.WriteLine($"Shuttle旋转传感器都激活: {shuttleRotateActive}");

                // 测试Robot特定方法
                var paddle1HasWafer = _coilHelper.IsWaferDetectedOnPaddle(1);
                var paddle2HasWafer = _coilHelper.IsWaferDetectedOnPaddle(2);
                Console.WriteLine($"Robot Paddle 1 有晶圆: {paddle1HasWafer}");
                Console.WriteLine($"Robot Paddle 2 有晶圆: {paddle2HasWafer}");

                // 测试状态计算方法
                var slitDoorStatus = _coilHelper.CalculateSlitDoorStatus(EnuMcuDeviceType.ChamberA);
                Console.WriteLine($"Chamber A Slit Door 状态: {slitDoorStatus}");

                var liftPinStatus = _coilHelper.CalculateLiftPinStatus(EnuMcuDeviceType.ChamberA);
                Console.WriteLine($"Chamber A Lift Pin 状态: {liftPinStatus}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CoilStatusHelper测试出错: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试所有线圈状态获取
        /// </summary>
        public void TestGetAllCoilStatus()
        {
            Console.WriteLine("=== 测试所有线圈状态获取 ===");

            foreach (EnuMcuDeviceType deviceType in Enum.GetValues<EnuMcuDeviceType>())
            {
                try
                {
                    var allStatus = _coilHelper.GetAllCoilStatus(deviceType);
                    Console.WriteLine($"{deviceType} 设备线圈数量: {allStatus.Count}");

                    // 只显示前5个线圈状态作为示例
                    var firstFive = allStatus.Take(5);
                    foreach (var kvp in firstFive)
                    {
                        Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
                    }

                    if (allStatus.Count > 5)
                    {
                        Console.WriteLine($"  ... 还有 {allStatus.Count - 5} 个线圈");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取 {deviceType} 线圈状态出错: {ex.Message}");
                }

                Console.WriteLine();
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public void RunAllTests()
        {
            Console.WriteLine("开始运行线圈查找功能测试...\n");

            TestIOCodeExtraction();
            TestEnumTypeDetection();
            TestCoilLookup();
            TestCoilStatusHelper();
            TestGetAllCoilStatus();

            Console.WriteLine("所有测试完成！");
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        public void PerformanceTest()
        {
            Console.WriteLine("=== 性能测试 ===");

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // 执行1000次线圈查找
            for (int i = 0; i < 1000; i++)
            {
                var coil = _mcuService.GetInputCoilByEnum(
                    EnuMcuDeviceType.ChamberA,
                    EnuChamberDICodes.PDI12_SlitDoorOpenSensor);
            }

            stopwatch.Stop();
            Console.WriteLine($"1000次线圈查找耗时: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"平均每次查找耗时: {stopwatch.ElapsedMilliseconds / 1000.0}ms");

            Console.WriteLine();
        }
    }
}