[{"Item": "1", "Code": "RA1", "Content": "Robot system status is busy, command reject", "ChineseDescription": "机器人系统忙碌状态，指令被拒绝"}, {"Item": "2", "Code": "RA2", "Content": "Robot system status is alarm, command reject", "ChineseDescription": "机器人系统报警状态，指令被拒绝"}, {"Item": "3", "Code": "RA3", "Content": "Robot R-axis not at home position, T-axis motion error", "ChineseDescription": "R轴未在原点位置，T轴运动错误"}, {"Item": "4", "Code": "RA4", "Content": "Robot T-axis not in right position, PLS confirm to R-axis motion", "ChineseDescription": "T轴位置不正确，请确认R轴运动"}, {"Item": "5", "Code": "RA5", "Content": "Robot rotation time out", "ChineseDescription": "机器人旋转超时"}, {"Item": "6", "Code": "RA6", "Content": "Robot extension time out", "ChineseDescription": "机器人伸展超时"}, {"Item": "7", "Code": "RA7", "Content": "Robot lfit time out", "ChineseDescription": "机器人升降超时"}, {"Item": "8", "Code": "RA8", "Content": "CHA slit door not at open status, robot can not extend", "ChineseDescription": "CHA狭缝门未开启，机器人无法伸展"}, {"Item": "9", "Code": "RA9", "Content": "CHB slit door not at open status, robot can not extend", "ChineseDescription": "CHB狭缝门未开启，机器人无法伸展"}, {"Item": "10", "Code": "RA10", "Content": "Robot Z-axis not in right position, PLS confirm to R-axis motion", "ChineseDescription": "Z轴位置不正确，请确认R轴运动"}, {"Item": "11", "Code": "RA11", "Content": "Robot R-axis not at home position, Z-axis can not move to right position", "ChineseDescription": "R轴未在原点位置，Z轴无法移动到正确位置"}, {"Item": "12", "Code": "RA12", "Content": "Robot T-axis not in right position, Z-axis can not move to right position", "ChineseDescription": "T轴位置不正确，Z轴无法移动到正确位置"}, {"Item": "13", "Code": "RA13", "Content": "Shuttle position status not at right position, can not do pin search", "ChineseDescription": "传送室位置状态不正确，无法进行针脚搜索"}, {"Item": "14", "Code": "RA14", "Content": "paddle hit to pin ball, pin search failure", "ChineseDescription": "桨叶撞击针脚球，针脚搜索失败"}, {"Item": "15", "Code": "RA15", "Content": "pin ball status failure, pin search failure", "ChineseDescription": "针脚球状态故障，针脚搜索失败"}, {"Item": "16", "Code": "RA16", "Content": "Can not find pin ball, pin search filure", "ChineseDescription": "找不到针脚球，针脚搜索失败"}, {"Item": "17", "Code": "RA17", "Content": "Paddle status occupied or wafer status inconsistent, command reject", "ChineseDescription": "桨叶状态被占用或晶圆状态不一致，指令被拒绝"}, {"Item": "18", "Code": "RA18", "Content": "There is(are) wafer(s) on paddle, and pin search data invalid,command reject", "ChineseDescription": "桨叶上有晶圆且针脚搜索数据无效，指令被拒绝"}, {"Item": "19", "Code": "RA19", "Content": "BL slide out sensor detector wafer slide out", "ChineseDescription": "BL滑出传感器检测到晶圆滑出"}, {"Item": "20", "Code": "RA20", "Content": "BR slide out sensor detector wafer slide out", "ChineseDescription": "BR滑出传感器检测到晶圆滑出"}, {"Item": "21", "Code": "RA21", "Content": "BL and BR slide out sensor detector wafer slide out", "ChineseDescription": "BL和BR滑出传感器都检测到晶圆滑出"}, {"Item": "22", "Code": "RA22", "Content": "Pin search value delta out of setpoint", "ChineseDescription": "针脚搜索值偏差超出设定点"}, {"Item": "23", "Code": "RA23", "Content": "Shuttle 1 exist status is diable, command reject", "ChineseDescription": "传送室1存在状态禁用，指令被拒绝"}, {"Item": "24", "Code": "RA24", "Content": "Shuttle 2 exist status is diable, command reject", "ChineseDescription": "传送室2存在状态禁用，指令被拒绝"}, {"Item": "25", "Code": "RA25", "Content": "shuttle position status not at right position, can not move wafer", "ChineseDescription": "传送室位置状态不正确，无法移动晶圆"}, {"Item": "26", "Code": "RA26", "Content": "CHA exist status is disable, command reject", "ChineseDescription": "CHA存在状态禁用，指令被拒绝"}, {"Item": "27", "Code": "RA27", "Content": "CHB exist status is disable, command reject", "ChineseDescription": "CHB存在状态禁用，指令被拒绝"}, {"Item": "28", "Code": "RA28", "Content": "CHA trigger status is alarm, command reject", "ChineseDescription": "CHA触发状态报警，指令被拒绝"}, {"Item": "29", "Code": "RA29", "Content": "CHB trigger status is alarm, command reject", "ChineseDescription": "CHB触发状态报警，指令被拒绝"}, {"Item": "30", "Code": "RA30", "Content": "CHA run status is busy, command reject", "ChineseDescription": "CHA运行状态忙碌，指令被拒绝"}, {"Item": "31", "Code": "RA31", "Content": "CHB run status is busy, command reject", "ChineseDescription": "CHB运行状态忙碌，指令被拒绝"}, {"Item": "32", "Code": "RA32", "Content": "CHA run status is processing, command reject", "ChineseDescription": "CHA运行状态处理中，指令被拒绝"}, {"Item": "33", "Code": "RA33", "Content": "CHB run status is processing, command reject", "ChineseDescription": "CHB运行状态处理中，指令被拒绝"}, {"Item": "34", "Code": "RA34", "Content": "Cooling chamber trigger status is alarm, command reject", "ChineseDescription": "冷却室触发状态报警，指令被拒绝"}, {"Item": "35", "Code": "RA35", "Content": "Cooling chamber run status is not idle status, command reject", "ChineseDescription": "冷却室运行状态非空闲，指令被拒绝"}, {"Item": "36", "Code": "RA36", "Content": "cassette slot is not empty, can not put wafer to cassette slot", "ChineseDescription": "卡匣槽非空，无法将晶圆放入卡匣槽"}, {"Item": "37", "Code": "RA37", "Content": "cassette slot status inconsistent, command reject", "ChineseDescription": "卡匣槽状态不一致，指令被拒绝"}, {"Item": "38", "Code": "RA38", "Content": "smooth P1 wafer has been lost", "ChineseDescription": "光滑面P1晶圆丢失"}, {"Item": "39", "Code": "RA39", "Content": "smooth P2 wafer has been lost", "ChineseDescription": "光滑面P2晶圆丢失"}, {"Item": "40", "Code": "RA40", "Content": "smooth side both paddle wafer has been lost", "ChineseDescription": "光滑面双桨叶晶圆都丢失"}, {"Item": "41", "Code": "RA41", "Content": "nose P1 wafer has been lost", "ChineseDescription": "鼻端P1晶圆丢失"}, {"Item": "42", "Code": "RA42", "Content": "nose P2 wafer has been lost", "ChineseDescription": "鼻端P2晶圆丢失"}, {"Item": "43", "Code": "RA43", "Content": "nose side both paddle wafer has been lost", "ChineseDescription": "鼻端双桨叶晶圆都丢失"}, {"Item": "44", "Code": "RA44", "Content": "smooth P1 wafer put wafer failure", "ChineseDescription": "光滑面P1晶圆放置失败"}, {"Item": "45", "Code": "RA45", "Content": "smooth P2 wafer put wafer failure", "ChineseDescription": "光滑面P2晶圆放置失败"}, {"Item": "46", "Code": "RA46", "Content": "smooth side both paddle wafer put wafer failure", "ChineseDescription": "光滑面双桨叶晶圆放置失败"}, {"Item": "47", "Code": "RA47", "Content": "nose P1 wafer put wafer failure", "ChineseDescription": "鼻端P1晶圆放置失败"}, {"Item": "48", "Code": "RA48", "Content": "nose P2 wafer put wafer failure", "ChineseDescription": "鼻端P2晶圆放置失败"}, {"Item": "49", "Code": "RA49", "Content": "nose side both wafer put wafer failure", "ChineseDescription": "鼻端双桨叶晶圆放置失败"}, {"Item": "50", "Code": "RA50", "Content": "sensor show smooth P1 wafer status inconsistent with slot, move wafer failure", "ChineseDescription": "传感器显示光滑面P1晶圆状态与槽位不一致，移动晶圆失败"}, {"Item": "51", "Code": "RA51", "Content": "sensor show smooth P2 wafer status inconsistent with slot, move wafer failure", "ChineseDescription": "传感器显示光滑面P2晶圆状态与槽位不一致，移动晶圆失败"}, {"Item": "52", "Code": "RA52", "Content": "sensor show nose P1 wafer status inconsistent with slot, move wafer failure", "ChineseDescription": "传感器显示鼻端P1晶圆状态与槽位不一致，移动晶圆失败"}, {"Item": "53", "Code": "RA53", "Content": "sensor show nose P2 wafer status inconsistent with slot, move wafer failure", "ChineseDescription": "传感器显示鼻端P2晶圆状态与槽位不一致，移动晶圆失败"}, {"Item": "54", "Code": "RA54", "Content": "There is(are) wafer(s) in CHA, put wafer to CHA command reject", "ChineseDescription": "CHA中有晶圆，放置晶圆到CHA指令被拒绝"}, {"Item": "55", "Code": "RA55", "Content": "There is(are) wafer(s) in CHB, put wafer to CHA command reject", "ChineseDescription": "CHB中有晶圆，放置晶圆到CHA指令被拒绝"}, {"Item": "56", "Code": "RA56", "Content": "There is(are) wafer(s) in CT, put wafer to CHA command reject", "ChineseDescription": "CT中有晶圆，放置晶圆到CHA指令被拒绝"}, {"Item": "57", "Code": "RA57", "Content": "There is(are) wafer(s) in CB, put wafer to CHA command reject", "ChineseDescription": "CB中有晶圆，放置晶圆到CHA指令被拒绝"}, {"Item": "58", "Code": "RA58", "Content": "smooth P1 wafer status abnormal", "ChineseDescription": "光滑面P1晶圆状态异常"}, {"Item": "59", "Code": "RA59", "Content": "smooth P2 wafer status abnormal", "ChineseDescription": "光滑面P2晶圆状态异常"}, {"Item": "60", "Code": "RA60", "Content": "nose P1 wafer status abnormal", "ChineseDescription": "鼻端P1晶圆状态异常"}, {"Item": "61", "Code": "RA61", "Content": "nose P2 wafer status abnormal", "ChineseDescription": "鼻端P2晶圆状态异常"}, {"Item": "62", "Code": "RA62", "Content": "slot is exist, can not get wafer from cassette", "ChineseDescription": "槽位存在晶圆，无法从卡匣获取晶圆"}, {"Item": "63", "Code": "RA63", "Content": "no wafer in CHA, get wafer from CHA command reject", "ChineseDescription": "CHA中无晶圆，从CHA获取晶圆指令被拒绝"}, {"Item": "64", "Code": "RA64", "Content": "no wafer in CHB, get wafer from CHA command reject", "ChineseDescription": "CHB中无晶圆，从CHA获取晶圆指令被拒绝"}, {"Item": "65", "Code": "RA65", "Content": "no wafer in CT, get wafer from CHA command reject", "ChineseDescription": "CT中无晶圆，从CHA获取晶圆指令被拒绝"}, {"Item": "66", "Code": "RA66", "Content": "no wafer in CB, get wafer from CHA command reject", "ChineseDescription": "CB中无晶圆，从CHA获取晶圆指令被拒绝"}, {"Item": "67", "Code": "RA67", "Content": "robot motion error", "ChineseDescription": "机器人运动错误"}]