﻿using Prism.Mvvm;

using SqlSugar;
using System;

namespace Zishan.SS200.Cmd.Models.Shared
{
    /// <summary>
    /// 流程配方存储信息
    /// </summary>
    [SugarTable("RecipeSeqInfo", TableDescription = "流程配方存储信息")]
    public class RecipeSeqInfo : BindableBase
    {
        public RecipeSeqInfo()
        {
            //ModifyTime = DateTime.Now; // 初始化 ModifyTime
        }

        /// <summary>
        /// ID号
        /// </summary>
        [SugarColumn(IsIdentity = true, IsPrimaryKey = true, ColumnDescription = "ID号")]
        public int Id { get => _Id; set => SetProperty(ref _Id, value); }
        private int _Id;

        /// <summary>
        /// 流程配方名
        /// </summary>
        [SugarColumn(ColumnDescription = "流程配方名")]
        public string RecipeName { get => _RecipeName; set => SetProperty(ref _RecipeName, value); }
        private string _RecipeName;

        /// <summary>
        /// 腔体指定配方名称
        /// </summary>
        [SugarColumn(ColumnDescription = "腔体指定配方名称")]
        public string ChRecipeName { get => _ChRecipeName; set => SetProperty(ref _ChRecipeName, value); }
        private string _ChRecipeName;

        /// <summary>
        /// Cooling指定配方名称
        /// </summary>
        [SugarColumn(ColumnDescription = "Cooling指定配方名称")]
        public string CoolingRecipeName { get => _CoolingRecipeName; set => SetProperty(ref _CoolingRecipeName, value); }
        private string _CoolingRecipeName;

        /// <summary>
        /// CHA是否允许启用
        /// </summary>
        [SugarColumn(ColumnDescription = "CHA是否允许启用")]
        public bool ChaEnable { get => _ChaEnable; set => SetProperty(ref _ChaEnable, value); }
        private bool _ChaEnable;

        /// <summary>
        /// CHB是否允许启用
        /// </summary>
        [SugarColumn(ColumnDescription = "CHB是否允许启用")]
        public bool ChbEnable { get => _ChbEnable; set => SetProperty(ref _ChbEnable, value); }
        private bool _ChbEnable;

        /// <summary>
        /// CHC是否允许启用
        /// </summary>
        [SugarColumn(ColumnDescription = "CHC是否允许启用")]
        public bool ChcEnable { get => _ChcEnable; set => SetProperty(ref _ChcEnable, value); }
        private bool _ChcEnable;

        /// <summary>
        /// CHA是优先顺序
        /// </summary>
        [SugarColumn(ColumnDescription = "CHA是优先顺序")]
        public int ChaOrder { get => _ChaOrder; set => SetProperty(ref _ChaOrder, value); }
        private int _ChaOrder;

        /// <summary>
        /// CHB是优先顺序
        /// </summary>
        [SugarColumn(ColumnDescription = "CHB是优先顺序")]
        public int ChbOrder { get => _ChbOrder; set => SetProperty(ref _ChbOrder, value); }
        private int _ChbOrder;

        /// <summary>
        /// CHC是优先顺序
        /// </summary>
        [SugarColumn(ColumnDescription = "CHC是优先顺序")]
        public int ChcOrder { get => _ChcOrder; set => SetProperty(ref _ChcOrder, value); }
        private int _ChcOrder;

        /// <summary>
        /// 命令名称中文描述
        /// </summary>
        [SugarColumn(ColumnDescription = "命令名称中文描述", IsNullable = true)]
        public string DescriptionChs { get => _DescriptionChs; set => SetProperty(ref _DescriptionChs, value); }
        private string _DescriptionChs;

        /// <summary>
        /// 命令名称英文描述
        /// </summary>
        [SugarColumn(ColumnDescription = "命令名称英文描述", IsNullable = true)]
        public string DescriptionEn { get => _DescriptionEn; set => SetProperty(ref _DescriptionEn, value); }
        private string _DescriptionEn;

        /// <summary>
        /// 是否有效，有效为‘Y’，无效为‘N’
        /// </summary>
        [SugarColumn(ColumnDescription = "是否有效")]
        public string Valid { get; set; } = "Y";

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(ColumnDescription = "排序号")]
        public int? SortCode { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [SugarColumn(ColumnDescription = "备注信息")]
        public string? Remark { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnDescription = "修改时间", InsertServerTime = true, UpdateServerTime = true)]
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", InsertServerTime = false, IsOnlyIgnoreUpdate = true)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 判断内容是否相等
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public override bool Equals(object? obj)
        {
            if (obj == null || GetType() != obj.GetType())
            {
                return false;
            }

            var other = (RecipeSeqInfo)obj;

            return Id == other.Id &&
                   RecipeName == other.RecipeName &&
                   ChRecipeName == other.ChRecipeName &&
                   CoolingRecipeName == other.CoolingRecipeName &&
                   ChaEnable == other.ChaEnable &&
                   ChaOrder == other.ChaOrder &&
                   ChbEnable == other.ChbEnable &&
                   ChbOrder == other.ChbOrder &&
                   ChcEnable == other.ChcEnable &&
                   ChcOrder == other.ChcOrder &&
                   DescriptionChs == other.DescriptionChs &&
                   DescriptionEn == other.DescriptionEn &&
                   Valid == other.Valid &&
                   SortCode == other.SortCode &&
                   Remark == other.Remark &&
                   CreateTime == other.CreateTime;
        }

        public override int GetHashCode()
        {
            var hash1 = HashCode.Combine(Id, RecipeName, ChRecipeName, CoolingRecipeName, ChaEnable, ChaOrder, ChbEnable, ChbOrder);
            var hash2 = HashCode.Combine(ChcEnable, ChcOrder, DescriptionChs, DescriptionEn, Valid, SortCode, Remark, CreateTime);
            return HashCode.Combine(hash1, hash2);
        }

        public object Clone()
        {
            return this.MemberwiseClone();
        }

        public override string ToString()
        {
            string strMsg = $"Id:{Id},\n" +
                            $"RecipeName:{RecipeName},\n" +
                            $"ChRecipeName:{ChRecipeName},\n" +
                            $"CoolingRecipeName:{CoolingRecipeName},\n" +
                            $"ChaEnable:{ChaEnable},\n" +
                            $"ChbEnable:{ChbEnable},\n" +
                            $"ChcEnable:{ChcEnable},\n" +
                            $"ChaOrder:{ChaOrder},\n" +
                            $"ChbOrder:{ChbOrder},\n" +
                            $"ChcOrder:{ChcOrder},\n" +
                            $"DescriptionChs:{DescriptionChs},\n" +
                            $"DescriptionEn:{DescriptionEn},\n" +
                            $"Valid:{Valid},\n" +
                            $"SortCode:{SortCode},\n" +
                            $"Remark:{Remark},\n" +
                            $"ModifyTime:{ModifyTime},\n" +
                            $"CreateTime:{CreateTime}";
            return strMsg;
        }
    }
}