using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.IOInterface.Shuttle
{
    /// <summary>
    /// Shuttle数字输入(DI)代码枚举
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuShuttleDICodes
    {
        /// <summary>
        /// 晶圆盒门上升传感器 (SDI1: SDI_1)
        /// 光学传感器NPN - 到位:0
        /// </summary>
        [Description("晶圆盒门上升传感器")]
        SDI1_CassetteDoorUpSensor = 1,

        /// <summary>
        /// 晶圆盒门下降传感器 (SDI2: SDI_2)
        /// 光学传感器NPN - 到位:0
        /// </summary>
        [Description("晶圆盒门下降传感器")]
        SDI2_CassetteDoorDownSensor = 2,

        /// <summary>
        /// 晶圆盒巢伸出传感器 (SDI3: SDI_3)
        /// 光学传感器NPN - 到位:0
        /// </summary>
        [Description("晶圆盒巢伸出传感器")]
        SDI3_CassetteNestExtendSensor = 3,

        /// <summary>
        /// 晶圆盒巢收回传感器 (SDI4: SDI_4)
        /// 光学传感器NPN - 到位:0
        /// </summary>
        [Description("晶圆盒巢收回传感器")]
        SDI4_CassetteNestRetractSensor = 4,

        /// <summary>
        /// 晶圆盒巢原位开关 (SDI5: SDI_5)
        /// 开关 - 到位:0
        /// </summary>
        [Description("晶圆盒巢原位开关")]
        SDI5_CassetteNestHomeSwitch = 5,

        /// <summary>
        /// 存在传感器晶圆盒1 (SDI6: SDI_6)
        /// 开关 - 触发为低电平0，未触发为高电平1
        /// </summary>
        [Description("存在传感器晶圆盒1")]
        SDI6_PresentSensorCassette1 = 6,

        /// <summary>
        /// 存在传感器晶圆盒2 (SDI7: SDI_7)
        /// 开关 - 触发为低电平0，未触发为高电平1
        /// </summary>
        [Description("存在传感器晶圆盒2")]
        SDI7_PresentSensorCassette2 = 7,

        /// <summary>
        /// 存在传感器晶圆盒3 (SDI8: SDI_8)
        /// 开关 - 触发为低电平0，未触发为高电平1
        /// </summary>
        [Description("存在传感器晶圆盒3")]
        SDI8_PresentSensorCassette3 = 8,

        /// <summary>
        /// 存在传感器晶圆盒4 (SDI9: SDI_9)
        /// 开关 - 触发为低电平0，未触发为高电平1
        /// </summary>
        [Description("存在传感器晶圆盒4")]
        SDI9_PresentSensorCassette4 = 9,

        /// <summary>
        /// 水流开关 (SDI10: SDI_25)
        /// 开关 - 触发为低电平0，未触发为高电平1
        /// </summary>
        [Description("水流开关")]
        SDI10_WaterFlowSwitch = 10,

        /// <summary>
        /// 光幕1 (SDI11: SDI_10)
        /// 光学传感器NPN - 触发为低电平0，未触发为高电平1
        /// </summary>
        [Description("光幕1")]
        SDI11_LightCurtain1 = 11,

        /// <summary>
        /// 光幕2 (SDI12: SDI_11) - 备用
        /// 光学传感器NPN - spare
        /// </summary>
        [Description("光幕2")]
        SDI12_LightCurtain2 = 12,

        /// <summary>
        /// Shuttle旋转传感器1 (SDI13: SDI_12)
        /// 光学传感器NPN - 接近为高电平1，未接近为低电平0
        /// </summary>
        [Description("Shuttle旋转传感器1")]
        SDI13_ShuttleRotateSensor1 = 13,

        /// <summary>
        /// Shuttle旋转传感器2 (SDI14: SDI_13)
        /// 光学传感器NPN - 接近为高电平1，未接近为低电平0
        /// </summary>
        [Description("Shuttle旋转传感器2")]
        SDI14_ShuttleRotateSensor2 = 14,

        /// <summary>
        /// Shuttle上升传感器 (SDI15: SDI_14)
        /// 光学传感器NPN - 接近为高电平1，未接近为低电平0
        /// </summary>
        [Description("Shuttle上升传感器")]
        SDI15_ShuttleUpSensor = 15,

        /// <summary>
        /// Shuttle下降传感器 (SDI16: SDI_15)
        /// 光学传感器NPN - 接近为高电平1，未接近为低电平0
        /// </summary>
        [Description("Shuttle下降传感器")]
        SDI16_ShuttleDownSensor = 16,

        /// <summary>
        /// 晶圆滑出传感器1 FL (SDI17: SDI_16)
        /// 光学传感器NPN - 挡住为高电平1，未挡住为低电平0
        /// </summary>
        [Description("晶圆滑出传感器1 FL")]
        SDI17_WaferSlideOutSensor1FL = 17,

        /// <summary>
        /// 晶圆滑出传感器2 FR (SDI18: SDI_17)
        /// 光学传感器NPN - 挡住为高电平1，未挡住为低电平0
        /// </summary>
        [Description("晶圆滑出传感器2 FR")]
        SDI18_WaferSlideOutSensor2FR = 18,

        /// <summary>
        /// 晶圆滑出传感器3 BL (SDI19: SDI_18)
        /// 光学传感器NPN - 挡住为高电平1，未挡住为低电平0
        /// </summary>
        [Description("晶圆滑出传感器3 BL")]
        SDI19_WaferSlideOutSensor3BL = 19,

        /// <summary>
        /// 晶圆滑出传感器4 BR (SDI20: SDI_19)
        /// 光学传感器NPN - 挡住为高电平1，未挡住为低电平0
        /// </summary>
        [Description("晶圆滑出传感器4 BR")]
        SDI20_WaferSlideOutSensor4BR = 20,

        /// <summary>
        /// Shuttle前级开关 (SDI21: SDI_20)
        /// 开关
        /// </summary>
        [Description("Shuttle前级开关")]
        SDI21_ShuttleForlineSwitch = 21,

        /// <summary>
        /// Shuttle真空ISO阀门打开传感器 (SDI22: SDI_21)
        /// 接近传感器 - 到位:0
        /// </summary>
        [Description("Shuttle真空ISO阀门打开传感器")]
        SDI22_ShuttleVacuumIsoValveOpenSensor = 22,

        /// <summary>
        /// Shuttle真空ISO阀门关闭传感器 (SDI23: SDI_22)
        /// 接近传感器 - 到位:0
        /// </summary>
        [Description("Shuttle真空ISO阀门关闭传感器")]
        SDI23_ShuttleVacuumIsoValveCloseSensor = 23,

        /// <summary>
        /// XV交叉阀门打开传感器 (SDI24: SDI_23)
        /// 接近传感器 - 到位:0
        /// </summary>
        [Description("XV交叉阀门打开传感器")]
        SDI24_XvCrossValveOpenSensor = 24,

        /// <summary>
        /// XV交叉阀门关闭传感器 (SDI25: SDI_24)
        /// 接近传感器 - 到位:0
        /// </summary>
        [Description("XV交叉阀门关闭传感器")]
        SDI25_XvCrossValveCloseSensor = 25,

        /// <summary>
        /// 冷却腔泄漏传感器 (SDI26: SDI_30)
        /// </summary>
        [Description("冷却腔泄漏传感器")]
        SDI26_CoolingChamberLeakSensor = 26,

        /// <summary>
        /// 备用 (SDI27)
        /// </summary>
        [Description("备用")]
        SDI27_Spare = 27,

        /// <summary>
        /// 备用 (SDI28)
        /// </summary>
        [Description("备用")]
        SDI28_Spare = 28
    }
}