using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Globalization;
using log4net;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 性能数据分析器
    /// 用于分析 StopwatchHelper 生成的性能日志，识别性能瓶颈和趋势
    /// </summary>
    public class PerformanceAnalyzer
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(PerformanceAnalyzer));
        /// <summary>
        /// 性能记录数据结构
        /// </summary>
        public class PerformanceRecord
        {
            public DateTime Timestamp { get; set; }
            public string OperationName { get; set; } = string.Empty;
            public double ElapsedMs { get; set; }
            public string Location { get; set; } = string.Empty;
            public string FileName { get; set; } = string.Empty;
            public string MethodName { get; set; } = string.Empty;
            public int LineNumber { get; set; }
            public bool IsWarning { get; set; }
            public double? ThresholdMs { get; set; }
        }

        /// <summary>
        /// 性能统计结果
        /// </summary>
        public class PerformanceStatistics
        {
            public string OperationName { get; set; } = string.Empty;
            public int Count { get; set; }
            public double AverageMs { get; set; }
            public double MinMs { get; set; }
            public double MaxMs { get; set; }
            public double TotalMs { get; set; }
            public int WarningCount { get; set; }
            public double WarningRate => Count > 0 ? (double)WarningCount / Count * 100 : 0;
            public List<PerformanceRecord> SlowestRecords { get; set; } = new();
        }

        private static readonly Regex PerformanceLogRegex = new(
            @"(?<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\s+\[性能计时结果\]\s+操作:\s*(?<operation>.*?)\s*\|\s*耗时:\s*(?<elapsed>[\d.]+)ms.*?\|\s*位置:\s*(?<location>.*)",
            RegexOptions.Compiled | RegexOptions.IgnoreCase);

        private static readonly Regex WarningLogRegex = new(
            @"(?<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\s+\[性能警告\].*?操作:\s*(?<operation>.*?)\s*\|\s*耗时:\s*(?<elapsed>[\d.]+)ms.*?\|\s*位置:\s*(?<location>.*?)\s*\|\s*超过阈值:\s*(?<threshold>[\d.]+)ms",
            RegexOptions.Compiled | RegexOptions.IgnoreCase);

        /// <summary>
        /// 从日志文件解析性能记录
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        /// <returns>性能记录列表</returns>
        public List<PerformanceRecord> ParseLogFile(string logFilePath)
        {
            var records = new List<PerformanceRecord>();

            if (!File.Exists(logFilePath))
            {
                _logger.Warn($"性能日志文件不存在: {logFilePath}");
                return records;
            }

            try
            {
                var lines = File.ReadAllLines(logFilePath);
                
                foreach (var line in lines)
                {
                    // 尝试解析性能结果日志
                    var match = PerformanceLogRegex.Match(line);
                    if (match.Success)
                    {
                        var record = ParsePerformanceRecord(match, false);
                        if (record != null)
                            records.Add(record);
                        continue;
                    }

                    // 尝试解析性能警告日志
                    match = WarningLogRegex.Match(line);
                    if (match.Success)
                    {
                        var record = ParsePerformanceRecord(match, true);
                        if (record != null)
                            records.Add(record);
                    }
                }

                _logger.Info($"成功解析性能记录 {records.Count} 条，来源: {logFilePath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"解析性能日志文件失败: {logFilePath}", ex);
            }

            return records;
        }

        /// <summary>
        /// 解析单条性能记录
        /// </summary>
        private PerformanceRecord? ParsePerformanceRecord(Match match, bool isWarning)
        {
            try
            {
                var timestampStr = match.Groups["timestamp"].Value;
                var operationName = match.Groups["operation"].Value.Trim();
                var elapsedStr = match.Groups["elapsed"].Value;
                var location = match.Groups["location"].Value.Trim();

                if (!DateTime.TryParseExact(timestampStr, "yyyy-MM-dd HH:mm:ss.fff", 
                    CultureInfo.InvariantCulture, DateTimeStyles.None, out var timestamp))
                {
                    return null;
                }

                if (!double.TryParse(elapsedStr, out var elapsed))
                {
                    return null;
                }

                var record = new PerformanceRecord
                {
                    Timestamp = timestamp,
                    OperationName = operationName,
                    ElapsedMs = elapsed,
                    Location = location,
                    IsWarning = isWarning
                };

                // 解析位置信息
                ParseLocationInfo(location, record);

                // 解析阈值信息（仅警告日志）
                if (isWarning && match.Groups["threshold"].Success)
                {
                    if (double.TryParse(match.Groups["threshold"].Value, out var threshold))
                    {
                        record.ThresholdMs = threshold;
                    }
                }

                return record;
            }
            catch (Exception ex)
            {
                _logger.Debug($"解析性能记录失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 解析位置信息
        /// </summary>
        private void ParseLocationInfo(string location, PerformanceRecord record)
        {
            // 格式: FileName.cs:MethodName:LineNumber
            var parts = location.Split(':');
            if (parts.Length >= 3)
            {
                record.FileName = parts[0].Trim();
                record.MethodName = parts[1].Trim();
                if (int.TryParse(parts[2].Trim(), out var lineNumber))
                {
                    record.LineNumber = lineNumber;
                }
            }
        }

        /// <summary>
        /// 生成性能统计报告
        /// </summary>
        /// <param name="records">性能记录列表</param>
        /// <param name="topCount">显示前N个最慢的操作</param>
        /// <returns>统计结果</returns>
        public List<PerformanceStatistics> GenerateStatistics(List<PerformanceRecord> records, int topCount = 10)
        {
            var statistics = records
                .GroupBy(r => r.OperationName)
                .Select(g => new PerformanceStatistics
                {
                    OperationName = g.Key,
                    Count = g.Count(),
                    AverageMs = g.Average(r => r.ElapsedMs),
                    MinMs = g.Min(r => r.ElapsedMs),
                    MaxMs = g.Max(r => r.ElapsedMs),
                    TotalMs = g.Sum(r => r.ElapsedMs),
                    WarningCount = g.Count(r => r.IsWarning),
                    SlowestRecords = g.OrderByDescending(r => r.ElapsedMs).Take(3).ToList()
                })
                .OrderByDescending(s => s.AverageMs)
                .Take(topCount)
                .ToList();

            return statistics;
        }

        /// <summary>
        /// 分析性能趋势
        /// </summary>
        /// <param name="records">性能记录列表</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="intervalHours">时间间隔（小时）</param>
        /// <returns>趋势数据</returns>
        public List<(DateTime Time, double AverageMs, int Count)> AnalyzeTrend(
            List<PerformanceRecord> records, string operationName, int intervalHours = 1)
        {
            var operationRecords = records
                .Where(r => r.OperationName.Equals(operationName, StringComparison.OrdinalIgnoreCase))
                .OrderBy(r => r.Timestamp)
                .ToList();

            if (!operationRecords.Any())
                return new List<(DateTime, double, int)>();

            var startTime = operationRecords.First().Timestamp;
            var endTime = operationRecords.Last().Timestamp;
            var interval = TimeSpan.FromHours(intervalHours);

            var trendData = new List<(DateTime Time, double AverageMs, int Count)>();
            var currentTime = startTime;

            while (currentTime <= endTime)
            {
                var nextTime = currentTime.Add(interval);
                var intervalRecords = operationRecords
                    .Where(r => r.Timestamp >= currentTime && r.Timestamp < nextTime)
                    .ToList();

                if (intervalRecords.Any())
                {
                    trendData.Add((
                        currentTime,
                        intervalRecords.Average(r => r.ElapsedMs),
                        intervalRecords.Count
                    ));
                }

                currentTime = nextTime;
            }

            return trendData;
        }

        /// <summary>
        /// 识别性能退化
        /// </summary>
        /// <param name="records">性能记录列表</param>
        /// <param name="degradationThreshold">退化阈值（百分比）</param>
        /// <returns>退化操作列表</returns>
        public List<(string OperationName, double BaselineMs, double CurrentMs, double DegradationPercent)> 
            IdentifyPerformanceDegradation(List<PerformanceRecord> records, double degradationThreshold = 50.0)
        {
            var degradations = new List<(string, double, double, double)>();
            var now = DateTime.Now;
            var baselinePeriod = now.AddDays(-7); // 基线：7天前
            var currentPeriod = now.AddDays(-1);  // 当前：1天前

            var operationGroups = records.GroupBy(r => r.OperationName);

            foreach (var group in operationGroups)
            {
                var baselineRecords = group
                    .Where(r => r.Timestamp >= baselinePeriod.AddDays(-1) && r.Timestamp < baselinePeriod)
                    .ToList();

                var currentRecords = group
                    .Where(r => r.Timestamp >= currentPeriod && r.Timestamp < now)
                    .ToList();

                if (baselineRecords.Count >= 5 && currentRecords.Count >= 5) // 确保有足够的样本
                {
                    var baselineAvg = baselineRecords.Average(r => r.ElapsedMs);
                    var currentAvg = currentRecords.Average(r => r.ElapsedMs);
                    var degradationPercent = (currentAvg - baselineAvg) / baselineAvg * 100;

                    if (degradationPercent > degradationThreshold)
                    {
                        degradations.Add((group.Key, baselineAvg, currentAvg, degradationPercent));
                    }
                }
            }

            return degradations.OrderByDescending(d => d.Item4).ToList();
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        /// <param name="outputPath">输出文件路径</param>
        public void GeneratePerformanceReport(string logFilePath, string outputPath)
        {
            try
            {
                var records = ParseLogFile(logFilePath);
                if (!records.Any())
                {
                    _logger.Warn("没有找到性能记录，无法生成报告");
                    return;
                }

                var statistics = GenerateStatistics(records);
                var degradations = IdentifyPerformanceDegradation(records);

                using var writer = new StreamWriter(outputPath, false, System.Text.Encoding.UTF8);
                
                writer.WriteLine("# 性能分析报告");
                writer.WriteLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                writer.WriteLine($"分析记录数: {records.Count}");
                writer.WriteLine($"时间范围: {records.Min(r => r.Timestamp):yyyy-MM-dd HH:mm:ss} ~ {records.Max(r => r.Timestamp):yyyy-MM-dd HH:mm:ss}");
                writer.WriteLine();

                // 性能统计
                writer.WriteLine("## 性能统计 (按平均耗时排序)");
                writer.WriteLine("| 操作名称 | 调用次数 | 平均耗时(ms) | 最小耗时(ms) | 最大耗时(ms) | 总耗时(ms) | 警告次数 | 警告率(%) |");
                writer.WriteLine("|----------|----------|--------------|--------------|--------------|------------|----------|-----------|");
                
                foreach (var stat in statistics)
                {
                    writer.WriteLine($"| {stat.OperationName} | {stat.Count} | {stat.AverageMs:F2} | {stat.MinMs:F2} | {stat.MaxMs:F2} | {stat.TotalMs:F2} | {stat.WarningCount} | {stat.WarningRate:F1} |");
                }

                // 性能退化
                if (degradations.Any())
                {
                    writer.WriteLine();
                    writer.WriteLine("## 性能退化检测");
                    writer.WriteLine("| 操作名称 | 基线耗时(ms) | 当前耗时(ms) | 退化程度(%) |");
                    writer.WriteLine("|----------|--------------|--------------|-------------|");
                    
                    foreach (var degradation in degradations)
                    {
                        writer.WriteLine($"| {degradation.OperationName} | {degradation.BaselineMs:F2} | {degradation.CurrentMs:F2} | {degradation.DegradationPercent:F1} |");
                    }
                }

                _logger.Info($"性能报告已生成: {outputPath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"生成性能报告失败: {ex.Message}", ex);
            }
        }
    }
}
