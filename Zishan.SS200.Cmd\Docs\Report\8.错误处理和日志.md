# Zishan.SS200.Cmd 错误处理和日志机制

## 错误处理和日志概述

Zishan.SS200.Cmd应用程序实现了全面的错误处理和日志记录机制，确保应用程序在遇到异常情况时能够优雅地处理错误，同时保留足够的诊断信息。这种机制对于工业控制软件尤为重要，因为它们需要高可靠性和可诊断性。

## 日志系统实现

### 1. log4net配置

应用程序使用log4net作为日志框架，其配置在应用程序启动时加载：

```csharp
// 配置log4net
var log4netConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs", "Log4netConfig", "log4net.config");
XmlConfigurator.Configure(new FileInfo(log4netConfigPath));

//创建日志路径
var lotRootPath = Path.Combine(Golbal.WorkRootPath + @"\Logs");
if (!Directory.Exists(lotRootPath))
    Directory.CreateDirectory(lotRootPath);
AppLog.UpdateFolder(lotRootPath);
```

log4net配置文件通常包含以下设置：
- 日志级别定义（DEBUG、INFO、WARN、ERROR、FATAL）
- 日志输出目标（文件、控制台等）
- 日志格式（包括时间戳、日志级别、类名等）
- 日志文件滚动策略（按大小、按日期等）

### 2. 日志记录示例

在代码中，日志记录遵循一致的模式：

```csharp
// 类级别日志记录器
private readonly ILog _logger = LogManager.GetLogger(typeof(ModbusClientService));

// 使用示例
public async Task<bool> ConnectAsync(string ipAddress, int port, int timeout = 3000)
{
    try
    {
        _logger.Info($"正在连接到Modbus服务器 {ipAddress}:{port}");

        _tcpClient = new TcpClient();
        using var cts = new CancellationTokenSource(timeout);
        await _tcpClient.ConnectAsync(ipAddress, port).WaitAsync(cts.Token);

        _master = new ModbusFactory().CreateMaster(_tcpClient);
        _isConnected = true;

        _logger.Info("Modbus服务器连接成功");
        return true;
    }
    catch (Exception ex)
    {
        _strMsg = $"连接Modbus服务器失败: {ex.Message}";
        _logger.Error(_strMsg, ex);
        return false;
    }
}
```

### 3. 日志记录级别使用规范

应用程序中各日志级别的使用遵循明确的规范：

- **DEBUG**：详细的开发和调试信息
  ```csharp
  _logger.Debug($"读取保持寄存器 - 16进制值:{string.Join(",", result.Select(v => $"{v:X4}"))}");
  ```

- **INFO**：重要的应用程序状态变化
  ```csharp
  _logger.Info($"正在连接到Modbus服务器 {ipAddress}:{port}");
  _logger.Info("Modbus服务器连接成功");
  ```

- **WARN**：警告信息，不会中断程序但需要关注
  ```csharp
  _logger.Warn($"Modbus连接不稳定，尝试重新连接...");
  ```

- **ERROR**：错误信息，通常表示功能失败
  ```csharp
  _logger.Error($"连接Modbus服务器失败: {ex.Message}", ex);
  ```

- **FATAL**：致命错误，导致应用程序不能继续运行
  ```csharp
  _logger.Fatal($"发生严重错误，应用程序将终止: {ex.Message}", ex);
  ```

### 4. 辅助日志工具类

项目实现了日志辅助类 `AppLog`，提供了更高级别的日志功能：

```csharp
public static class AppLog
{
    // 更新日志文件夹
    public static void UpdateFolder(string logFolder)
    {
        // 设置日志路径
    }

    // 记录调试信息
    public static void Debug(string title, string text = "")
    {
        ILog log = LogManager.GetLogger($"Debug_{title}");
        log.Debug(text);
    }

    // 记录错误信息
    public static void Error(string title, Exception ex)
    {
        ILog log = LogManager.GetLogger($"Error_{title}");
        log.Error(ex.Message, ex);
    }

    // 记录信息
    public static void Info(string title, string text = "")
    {
        ILog log = LogManager.GetLogger($"Info_{title}");
        log.Info(text);
    }
}
```

## 异常处理机制

### 1. 全局异常处理

应用程序实现了全局未处理异常捕获，确保任何未捕获的异常都不会导致应用程序崩溃：

```csharp
protected override void OnStartup(StartupEventArgs e)
{
    // ...
    //UI线程的异常捕捉
    DispatcherUnhandledException += App_DispatcherUnhandledException;
    // ...
}

private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
{
    try
    {
        e.Handled = true;
        AppLog.Error($"未捕获错误_{MethodBase.GetCurrentMethod()?.Name}", e.Exception);
        if (e.Exception.InnerException == null)
        {
            MessageBox.Show("（1）发生了一个错误！请联系开发人员！" + Environment.NewLine
                               + "（2）错误源：" + e.Exception.Source + Environment.NewLine
                               + "（3）详细信息：" + e.Exception.Message + Environment.NewLine
                               + "（4）报错区域：" + e.Exception.StackTrace);
        }
        else
        {
            MessageBox.Show("（1）发生了一个错误！请联系开发人员！" + Environment.NewLine
                                + "（2）错误源：" + e.Exception.InnerException.Source + Environment.NewLine
                                + "（3）错误信息：" + e.Exception.Message + Environment.NewLine
                                + "（4）详细信息：" + e.Exception.InnerException.Message + Environment.NewLine
                                + "（5）报错区域：" + e.Exception.InnerException.StackTrace);
        }
    }
    catch (Exception ex)
    {
        AppLog.Error($"程序发生致命错误，将终止，请联系运营商！_{MethodBase.GetCurrentMethod()?.Name}", ex);
        //此时程序出现严重异常，将强制结束退出
        MessageBox.Show($"程序发生致命错误，将终止，请联系运营商！错误信息：{ex.Message}");
    }
}
```

这种机制确保：
- 所有未捕获的异常都会被记录
- 用户会收到友好的错误消息
- 程序可以在可能的情况下继续运行

### 2. 局部异常处理

在关键操作中，应用程序使用 try-catch 块进行局部异常处理：

```csharp
public async Task<ushort[]> ReadHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints)
{
    try
    {
        _logger.Debug($"正在读取保持寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}");
        var result = await _master.ReadHoldingRegistersAsync(slaveAddress, startAddress, numberOfPoints);
        _logger.Debug($"读取保持寄存器成功 - 16进制值:{string.Join(",", result.Select(v => $"{v:X4}"))}");
        return result;
    }
    catch (Exception ex)
    {
        _strMsg = $"读取保持寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}，错误信息：{ex.Message}";
        ShowThrottledError(_strMsg);
        throw;
    }
}
```

### 3. 错误信息节流

为了防止频繁的错误导致UI卡顿和日志过载，应用程序实现了错误消息节流机制：

```csharp
private DateTime _lastErrorTime = DateTime.MinValue;
private static readonly TimeSpan ErrorThrottleInterval = TimeSpan.FromSeconds(3);

private void ShowThrottledError(string message)
{
    var now = DateTime.Now;
    if (now - _lastErrorTime >= ErrorThrottleInterval)
    {
        HcGrowlExtensions.Error(message);
        _lastErrorTime = now;
    }
    // 始终记录日志，不受节流影响
    _logger.Error(message);
}
```

这种机制确保：
- UI不会被频繁的错误消息淹没
- 所有错误仍然被记录到日志
- 用户获得及时但不过度的反馈

### 4. 超时处理

对于可能长时间运行的操作，应用程序实现了超时处理机制：

```csharp
public async Task<bool> ConnectAsync(string ipAddress, int port, int timeout = 3000)
{
    try
    {
        _tcpClient = new TcpClient();
        using var cts = new CancellationTokenSource(timeout);
        await _tcpClient.ConnectAsync(ipAddress, port).WaitAsync(cts.Token);
        
        // 连接成功处理...
        return true;
    }
    catch (OperationCanceledException)
    {
        _strMsg = $"连接Modbus服务器超时";
        ShowThrottledError(_strMsg);
        return false;
    }
    catch (Exception ex)
    {
        // 其他异常处理...
        return false;
    }
}
```

### 5. 重试机制

对于可能由于暂时性问题失败的操作，应用程序实现了重试机制：

```csharp
public async Task<bool> TryConnectWithRetryAsync(string ipAddress, int port, int retryCount = 3, int retryDelayMs = 1000)
{
    for (int i = 0; i < retryCount; i++)
    {
        try
        {
            var result = await ConnectAsync(ipAddress, port);
            if (result)
                return true;
        }
        catch (Exception ex)
        {
            _logger.Warn($"连接尝试 {i+1}/{retryCount} 失败: {ex.Message}");
        }
        
        if (i < retryCount - 1)
        {
            _logger.Info($"等待 {retryDelayMs}ms 后重试...");
            await Task.Delay(retryDelayMs);
        }
    }
    
    _logger.Error($"在 {retryCount} 次尝试后仍然无法连接到 {ipAddress}:{port}");
    return false;
}
```

## 错误展示与用户反馈

### 1. 错误消息框

应用程序使用MessageBox向用户显示关键错误：

```csharp
MessageBox.Show($"读取App配置文件失败，请联系开发人员！\r\n错误信息：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
```

### 2. 状态栏通知

对于非关键错误，应用程序使用状态栏通知：

```csharp
StatusBar.SetMessage($"连接失败: {ex.Message}", StatusMessageType.Error);
```

### 3. 轻量级通知

使用HandyControl提供的轻量级通知功能：

```csharp
HcGrowlExtensions.Info($"命令执行成功！");
HcGrowlExtensions.Warning($"命令执行失败：result={result.ToString()}");
```

## 异常恢复机制

### 1. 连接恢复

当检测到连接断开时，应用程序会尝试自动恢复连接：

```csharp
private async Task MonitorConnectionAsync(CancellationToken cancellationToken)
{
    while (!cancellationToken.IsCancellationRequested)
    {
        try
        {
            if (!_isConnected)
            {
                _logger.Info($"检测到连接断开，尝试重新连接...");
                await ConnectAsync(ModbusHost, ModbusPort);
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"连接监控错误: {ex.Message}", ex);
        }
        
        await Task.Delay(5000, cancellationToken);
    }
}
```

### 2. 状态重置

在发生错误后，应用程序会重置相关状态：

```csharp
private void ResetState()
{
    _isProcessing = false;
    _currentCommand = null;
    // 重置其他状态...
}
```

### 3. 资源清理

应用程序确保在发生异常时正确清理资源：

```csharp
public void Dispose()
{
    try
    {
        _logger.Info("正在释放资源");
        _pollingCts?.Cancel();
        _pollingCts?.Dispose();
        IsConnected = false;
        _modbusClient?.DisconnectAsync().Wait();
    }
    catch (Exception ex)
    {
        _logger.Error($"释放资源时发生错误: {ex.Message}", ex);
    }
}
```

## 日志分析和监控

### 1. 日志文件命名和组织

应用程序使用结构化的日志文件命名和组织：

```
Logs/
  ├── AppLog_yyyy-MM-dd.log     // 应用程序一般日志
  ├── ErrorLog_yyyy-MM-dd.log   // 错误日志
  ├── ModbusLog_yyyy-MM-dd.log  // Modbus通信日志
  └── Debug_yyyy-MM-dd.log      // 调试日志（仅在调试模式）
```

### 2. 日志轮转策略

使用log4net的日志轮转功能防止日志文件过大：

```xml
<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
    <file value="Logs\AppLog.log" />
    <appendToFile value="true" />
    <rollingStyle value="Date" />
    <datePattern value="yyyyMMdd" />
    <maxSizeRollBackups value="10" />
    <maximumFileSize value="10MB" />
    <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
    </layout>
</appender>
```

## 总结

Zishan.SS200.Cmd应用程序实现了全面而强大的错误处理和日志机制，具有以下特点：

1. **多层次错误处理**：全局和局部异常处理相结合
2. **详细的日志记录**：使用log4net提供灵活的日志配置
3. **用户友好的错误反馈**：适当的错误消息和通知
4. **错误恢复机制**：连接重试和资源清理
5. **性能优化**：错误消息节流防止UI卡顿
6. **结构化日志组织**：便于故障排查和分析

这种设计确保应用程序在复杂的工业环境中能够稳定运行，同时提供足够的诊断信息帮助开发人员排查问题。 