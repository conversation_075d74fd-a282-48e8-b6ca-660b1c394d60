# NPN传感器逻辑修复说明

## 修复概述

本次修复主要解决了CoilStatusHelper.cs中NPN传感器逻辑不一致的问题。根据配置文件`McuDeviceCoilName.json`中的传感器类型和备注信息，统一了所有传感器的逻辑处理。

## 传感器类型和逻辑说明

### 1. 位置传感器（NPN类型）
**特征**: `"sensorType": "optic sensor NPN"` 或 `"接近传感器NPN"`，`"remark": "到位:0"`

**逻辑**: 到位时为0（false），未到位时为1（true）

**涉及传感器**:
- Chamber: PDI1-PDI4, PDI12-PDI15 (Slit Door, Lift Pin, 阀门传感器)
- Shuttle: SDI1-SDI4, SDI22-SDI25 (晶圆盒门/巢传感器, 阀门传感器)

### 2. 存在检测传感器（开关类型）
**特征**: `"sensorType": "switch"`，`"remark": "触发为低电平0，未触发为高电平1"`

**逻辑**: 触发时为0（false），未触发时为1（true）

**涉及传感器**:
- Shuttle: SDI6-SDI9 (晶圆盒存在传感器)

### 3. 特殊逻辑传感器

#### 3.1 Paddle传感器（Robot）
**特征**: `"sensorType": "optic sensor NPN"`，`"remark": "no wafer:0 wafer:1"`

**逻辑**: 有晶圆时为1（true），无晶圆时为0（false）

**涉及传感器**: RDI1-RDI2

#### 3.2 Pin搜索传感器（Robot）
**特征**: `"sensorType": "switch"`，`"remark": "detect:0 no detect:1"`

**逻辑**: 检测到时为0（false），未检测到时为1（true）

**涉及传感器**: RDI3-RDI4

#### 3.3 旋转/位置传感器（Shuttle）
**特征**: `"sensorType": "optic sensor NPN"`，`"remark": "接近为高电平1，未接近为低电平0"`

**逻辑**: 接近时为1（true），未接近时为0（false）

**涉及传感器**: SDI13-SDI16

## 修复的方法

### 1. IsShuttleCassetteDoorAtPosition
**修复前**: 直接使用原始传感器值
```csharp
return GetCoilValue(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1_CassetteDoorUpSensor);
```

**修复后**: 应用NPN逻辑转换
```csharp
// 注意：传感器为NPN类型，到位时为0（false），未到位时为1（true）
return !GetCoilValue(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1_CassetteDoorUpSensor);
```

### 2. IsShuttleCassetteNestAtPosition
**修复**: 同样应用NPN逻辑转换

### 3. CalculateCassetteDoorNestStatus
**修复前**: 直接使用原始传感器值进行状态判断

**修复后**: 先转换NPN逻辑，再进行状态判断
```csharp
// 获取门传感器状态 - 注意：SDI1,SDI2为NPN类型，到位时为0
var doorUpRaw = GetCoilValue(deviceType, EnuShuttleDICodes.SDI1_CassetteDoorUpSensor);
var doorDownRaw = GetCoilValue(deviceType, EnuShuttleDICodes.SDI2_CassetteDoorDownSensor);
var doorUp = !doorUpRaw;    // 转换NPN逻辑：到位时为true
var doorDown = !doorDownRaw; // 转换NPN逻辑：到位时为true
```

### 4. CalculateShuttlePositionStatus
**修复**: 修复巢传感器（SDI4, SDI5）的NPN逻辑转换

### 5. IsCassettePresent
**修复前**: 直接使用原始传感器值
```csharp
return GetCoilValue(EnuMcuDeviceType.Shuttle, sensorCode);
```

**修复后**: 应用开关传感器逻辑转换
```csharp
// 注意：存在传感器为开关类型，触发为低电平0，未触发为高电平1
// 当晶圆盒存在时，传感器被触发，值为0，所以需要取反
var sensorValue = GetCoilValue(EnuMcuDeviceType.Shuttle, sensorCode);
return !sensorValue; // 传感器值为0时表示晶圆盒存在
```

## 未修改的方法（逻辑已正确）

### 1. Chamber相关方法
- `CalculateSlitDoorStatus`: 已正确应用NPN逻辑
- `CalculateLiftPinStatus`: 已正确应用NPN逻辑
- `CalculateIsoValveStatus`: 已正确应用NPN逻辑
- `CalculateThrottleValveStatus`: 已正确应用NPN逻辑

### 2. Robot相关方法
- `IsWaferDetectedOnPaddle`: Paddle传感器逻辑特殊，已正确
- `IsPinDetected`: 已正确应用Pin搜索传感器逻辑

### 3. Shuttle阀门方法
- `CalculateShuttleValveStatus`: 阀门传感器逻辑已正确

## 验证建议

1. **单元测试**: 为修复的方法编写单元测试，验证不同传感器值下的状态计算结果
2. **集成测试**: 在实际设备上测试传感器状态变化时的响应
3. **UI验证**: 检查状态面板中显示的传感器状态是否与实际物理状态一致

## 注意事项

1. **配置文件依赖**: 传感器逻辑基于`McuDeviceCoilName.json`配置文件，如果配置发生变化，需要相应更新代码逻辑
2. **文档更新**: 相关的README和示例代码已更新，反映正确的传感器逻辑
3. **向后兼容**: 修复保持了方法签名不变，确保现有调用代码无需修改
