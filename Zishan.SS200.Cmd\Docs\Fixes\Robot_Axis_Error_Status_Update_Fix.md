# Robot轴错误代码状态更新修复

## 问题描述

在`RobotStatusPanelViewModel.cs`中，轴错误代码产生时Robot子系统状态更新存在以下问题：

1. **主要问题**：`UpdateRobotMainStatus`方法中使用了`else if`逻辑检查轴错误代码，导致只检查第一个有错误的轴，而不是检查所有轴的错误状态
2. **次要问题**：现有寄存器监听器设置不完整，缺少`UpdateRobotSubsystemStatus()`调用
3. **逻辑不一致**：重复的监听器设置且处理逻辑不统一

## 原始问题代码

### UpdateRobotMainStatus方法问题
```csharp
// 检查T轴、R轴、Z轴错误代码
if (RobotAlarmRegisters.Count > 0 && RobotAlarmRegisters[0].Value != 0)
{
    // T轴有报警
    hasAlarm = true;
}
else if (RobotAlarmRegisters.Count > 1 && RobotAlarmRegisters[1].Value != 0)
{
    // R轴有报警
    hasAlarm = true;
}
else if (RobotAlarmRegisters.Count > 2 && RobotAlarmRegisters[2].Value != 0)
{
    // Z轴有报警
    hasAlarm = true;
}
```

**问题**：使用`else if`意味着如果T轴有错误，就不会检查R轴和Z轴的错误状态。

### 监听器设置问题
```csharp
// 现有寄存器监听器只更新UI属性，没有调用状态更新
RobotAlarmRegisters[0].PropertyChanged += (s, e) =>
{
    if (e.PropertyName == nameof(ModbusRegister.Value))
    {
        OnPropertyChanged(nameof(TAxisErrorInfo));
        // 缺少：UpdateRobotSubsystemStatus();
    }
};
```

## 修复方案

### 1. 修复UpdateRobotMainStatus方法

将`else if`改为独立的`if`语句，确保检查所有轴的错误状态：

```csharp
// 检查T轴错误代码
if (RobotAlarmRegisters.Count > 0 && RobotAlarmRegisters[0].Value != 0)
{
    hasAlarm = true;
    alarmAxes.Add($"T轴(0x{RobotAlarmRegisters[0].Value:X4})");
    _logger?.Debug($"检测到T轴错误: 0x{RobotAlarmRegisters[0].Value:X4}");
}

// 检查R轴错误代码
if (RobotAlarmRegisters.Count > 1 && RobotAlarmRegisters[1].Value != 0)
{
    hasAlarm = true;
    alarmAxes.Add($"R轴(0x{RobotAlarmRegisters[1].Value:X4})");
    _logger?.Debug($"检测到R轴错误: 0x{RobotAlarmRegisters[1].Value:X4}");
}

// 检查Z轴错误代码
if (RobotAlarmRegisters.Count > 2 && RobotAlarmRegisters[2].Value != 0)
{
    hasAlarm = true;
    alarmAxes.Add($"Z轴(0x{RobotAlarmRegisters[2].Value:X4})");
    _logger?.Debug($"检测到Z轴错误: 0x{RobotAlarmRegisters[2].Value:X4}");
}
```

### 2. 统一监听器处理逻辑

确保现有寄存器监听器也调用`UpdateRobotSubsystemStatus()`：

```csharp
case 0: // T轴错误代码
    register.PropertyChanged += (sender, args) =>
    {
        if (args.PropertyName == nameof(ModbusRegister.Value))
        {
            OnPropertyChanged(nameof(TAxisErrorInfo));
            // 轴错误代码产生需要更新子系统Robot状态更新
            UpdateRobotSubsystemStatus();
        }
    };
    break;
```

## 修复效果

1. **正确的错误检测**：现在会检查所有轴的错误状态，任何轴有错误都会正确设置Robot状态为Alarm
2. **完整的状态更新**：错误代码变化时会正确触发Robot子系统状态更新
3. **详细的日志记录**：添加了详细的错误检测日志，便于调试和监控
4. **统一的处理逻辑**：所有寄存器监听器使用相同的处理逻辑

## 测试验证

修复后应验证以下场景：

1. **单轴错误**：T轴、R轴、Z轴分别单独出现错误时，Robot状态应正确设置为Alarm
2. **多轴错误**：多个轴同时出现错误时，Robot状态应正确设置为Alarm，并记录所有错误轴
3. **错误恢复**：所有轴错误清除后，Robot状态应恢复为Idle
4. **UI更新**：错误信息应正确显示在UI的ToolTip中
5. **状态表更新**：Robot子系统状态表应正确反映当前状态

## 相关文件

- `Zishan.SS200.Cmd/ViewModels/Dock/RobotStatusPanelViewModel.cs`
- `Zishan.SS200.Cmd/Models/SS200/SubSystemStatus/Robot/RobotSubsystemStatus.cs`
- `Zishan.SS200.Cmd/Models/SS200/SubSystemStatus/Robot/EnuRobotStatus.cs`
