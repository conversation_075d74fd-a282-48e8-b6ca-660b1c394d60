﻿TrasferWafer(Nose端/Smooth端，源端，目标端)
GetWafer(Nose端/Smooth端，源端,SlotXX) 内容子方法
PutWafer(Nose端/Smooth端，目标端,SlotXX) 内容子方法

## 半导体设备Robot Wafer晶圆搬运流程说明

### 方法说明

- **TrasferWafer**：主方法，控制晶圆从源端到目标端的完整搬运流程
  - 参数：
    - `Nose端/Smooth端`：指定使用机器人的哪个端口
    - `源端`：晶圆的起始位置（Cassette、CHA、CHB、CT、CB等）
    - `目标端`：晶圆的目标位置（Cassette、CHA、CHB、CT、CB等）

- **GetWafer**：从源端获取晶圆的子方法
  - 参数：
    - `Nose端/Smooth端`：使用的机器人端口
    - `源端`：晶圆的起始位置
    - `SlotXX`：晶圆盒中的插槽号

- **PutWafer**：将晶圆放置到目标端的子方法
  - 参数：
    - `Nose端/Smooth端`：使用的机器人端口
    - `目标端`：晶圆的目标位置
    - `SlotXX`：晶圆盒中的插槽号

### 晶圆搬运流程图

```mermaid
flowchart TD
    classDef processNode fill:#f9f9f9,stroke:#333,stroke-width:1px
    classDef decisionNode fill:#e1f5fe,stroke:#0277bd,stroke-width:1px
    classDef startEndNode fill:#9ccc65,stroke:#33691e,stroke-width:1px,color:#fff
    classDef errorNode fill:#ef5350,stroke:#c62828,stroke-width:1px,color:#fff
    classDef axisNode fill:#ffecb3,stroke:#ff8f00,stroke-width:1px
    classDef commandNode fill:#e8eaf6,stroke:#3949ab,stroke-width:1px

    %% 主流程起点
    Start([开始]) --> TrasferWafer["TrasferWafer\n(Nose端/Smooth端，源端，目标端)"]
    TrasferWafer --> HomeAxis["三轴回原点\n(AR9:T轴归零, AR19:R轴归零, AR33:Z轴归零)"]
    
    %% 分支到GetWafer和PutWafer
    HomeAxis --> GetWaferProcess["GetWafer\n(Nose端/Smooth端，源端，SlotXX)"]
    HomeAxis --> PutWaferProcess["PutWafer\n(Nose端/Smooth端，目标端，SlotXX)"]
    
    %% GetWafer流程详细分支
    GetWaferProcess --> CheckGetSource{"源端类型?"}
    CheckGetSource -->|Cassette| GetFromCassette["从晶圆盒取晶圆\n(AR47:从晶圆盒取晶圆到Nose端挡板\n或AR43:从晶圆盒取晶圆)"]
    CheckGetSource -->|腔体| GetFromChamber["从腔体取晶圆\n(AR49:从工艺腔室A取晶圆\nAR51:从工艺腔室B取晶圆\nAR53:从冷却TOP取晶圆\nAR55:从冷却BOTTOM取晶圆)"]
    
    %% GetWafer从Cassette取晶圆详细步骤
    GetFromCassette --> CheckGetEndType1{"端口类型?"}
    CheckGetEndType1 -->|Nose端| NoseGetCassetteSteps["1. AR8:T轴Nose端移动到晶圆盒\n2. AR18:R轴Nose端移动到晶圆盒\n3. AR35:Z轴Nose端取位置从插槽\n4. 开启真空吸附\n5. AR69:晶圆盒到Nose端挡板状态交换\n6. AR19:R轴归零"]
    CheckGetEndType1 -->|Smooth端| SmoothGetCassetteSteps["1. AR4:T轴Smooth端移动到晶圆盒\n2. AR14:R轴Smooth端移动到晶圆盒\n3. AR34:Z轴Smooth端取位置从插槽\n4. 开启真空吸附\n5. AR57:晶圆盒到Smooth端挡板状态交换\n6. AR19:R轴归零"]
    
    %% GetWafer从腔体取晶圆详细步骤
    GetFromChamber --> CheckGetEndType2{"端口类型?"}
    CheckGetEndType2 -->|Nose端| NoseGetChamberSteps["1. 移动T轴到对应腔体(AR5/AR6/AR7)\n2. 伸展R轴(AR15/AR16/AR17)\n3. 移动Z轴到取片高度(AR25/AR26/AR27/AR28)\n4. 开启真空吸附\n5. 执行对应状态交换(AR72/AR74/AR76/AR78)\n6. AR19:R轴归零"]
    CheckGetEndType2 -->|Smooth端| SmoothGetChamberSteps["1. 移动T轴到对应腔体(AR1/AR2/AR3)\n2. 伸展R轴(AR11/AR12/AR13)\n3. 移动Z轴到取片高度(AR21/AR22/AR23/AR24)\n4. 开启真空吸附\n5. 执行对应状态交换(AR61/AR63/AR65/AR67)\n6. AR19:R轴归零"]
    
    %% PutWafer流程详细分支
    PutWaferProcess --> CheckPutTarget{"目标端类型?"}
    CheckPutTarget -->|Cassette| PutToCassette["放晶圆到晶圆盒\n(AR59:从Smooth端挡板放晶圆到晶圆盒\n或AR70:从Nose端挡板放晶圆到晶圆盒)"]
    CheckPutTarget -->|腔体| PutToChamber["放晶圆到腔体\n(AR50:放晶圆到工艺腔室A\nAR52:放晶圆到工艺腔室B\nAR54:放晶圆到冷却TOP\nAR56:放晶圆到冷却BOTTOM)"]
    
    %% PutWafer到Cassette详细步骤
    PutToCassette --> CheckPutEndType1{"端口类型?"}
    CheckPutEndType1 -->|Nose端| NosePutCassetteSteps["1. AR8:T轴Nose端移动到晶圆盒\n2. AR18:R轴Nose端移动到晶圆盒\n3. AR37:Z轴Nose端放位置到插槽\n4. 释放真空\n5. AR68:Nose端挡板到晶圆盒状态交换\n6. AR19:R轴归零\n7. 检查挡板状态(RDI1/RDI2)"]
    CheckPutEndType1 -->|Smooth端| SmoothPutCassetteSteps["1. AR4:T轴Smooth端移动到晶圆盒\n2. AR14:R轴Smooth端移动到晶圆盒\n3. AR36:Z轴Smooth端放位置到插槽\n4. 释放真空\n5. AR58:Smooth端挡板到晶圆盒状态交换\n6. AR19:R轴归零\n7. 检查挡板状态(RDI1/RDI2)"]
    
    %% PutWafer到腔体详细步骤
    PutToChamber --> CheckPutEndType2{"端口类型?"}
    CheckPutEndType2 -->|Nose端| NosePutChamberSteps["1. 移动T轴到对应腔体(AR5/AR6/AR7)\n2. 伸展R轴(AR15/AR16/AR17)\n3. 移动Z轴到放片高度(AR31/AR32)\n4. 释放真空\n5. 执行对应状态交换(AR71/AR73/AR75/AR77)\n6. AR19:R轴归零"]
    CheckPutEndType2 -->|Smooth端| SmoothPutChamberSteps["1. 移动T轴到对应腔体(AR1/AR2/AR3)\n2. 伸展R轴(AR11/AR12/AR13)\n3. 移动Z轴到放片高度(AR29/AR30)\n4. 释放真空\n5. 执行对应状态交换(AR60/AR62/AR64/AR66)\n6. AR19:R轴归零"]
    
    %% 汇总到共同的操作步骤
    NoseGetCassetteSteps --> CommonOperations
    SmoothGetCassetteSteps --> CommonOperations
    NoseGetChamberSteps --> CommonOperations
    SmoothGetChamberSteps --> CommonOperations
    NosePutCassetteSteps --> CommonOperations
    SmoothPutCassetteSteps --> CommonOperations
    NosePutChamberSteps --> CommonOperations
    SmoothPutChamberSteps --> CommonOperations
    
    %% 共同的下一步操作
    CommonOperations["不同参数的动作\n1. T轴移动到目标位置\n2. ShutDown开关控制\n3. R轴伸缩控制Wafer调运\n4. Z轴取放位置和高度控制"]
    
    %% 错误处理和报警
    CommonOperations --> ErrorCheck{"执行状态检查"}
    ErrorCheck -->|正常| Complete([完成])
    ErrorCheck -->|错误| AlarmHandler["错误处理\n(RA36-RA49报警代码)"]
    AlarmHandler --> ErrorReset["报警复位\n(AR1:报警复位命令)"]
    ErrorReset --> Reinitialize["重新初始化"]
    Reinitialize --> TrasferWafer
    
    %% 应用类样式
    class Start,Complete startEndNode
    class TrasferWafer,GetWaferProcess,PutWaferProcess processNode
    class CheckGetSource,CheckPutTarget,CheckGetEndType1,CheckGetEndType2,CheckPutEndType1,CheckPutEndType2,ErrorCheck decisionNode
    class HomeAxis,CommonOperations axisNode
    class GetFromCassette,GetFromChamber,PutToCassette,PutToChamber,NoseGetCassetteSteps,SmoothGetCassetteSteps,NoseGetChamberSteps,SmoothGetChamberSteps,NosePutCassetteSteps,SmoothPutCassetteSteps,NosePutChamberSteps,SmoothPutChamberSteps commandNode
    class AlarmHandler,ErrorReset,Reinitialize errorNode
```

### 关键命令说明

#### 轴控制命令
- **T轴命令 (AR1-AR10)**：控制机器人的水平旋转运动，在不同工作位置之间切换
- **R轴命令 (AR11-AR20)**：控制机器人的径向伸缩运动，伸入或收回机械臂
- **Z轴命令 (AR21-AR39)**：控制机器人的垂直升降运动，调整高度以便抓取或放置晶圆

#### 晶圆取放命令
- **取晶圆命令 (AR43-AR55)**：从不同位置取晶圆的命令
- **放晶圆命令 (AR46-AR70)**：放置晶圆到不同位置的命令

#### 状态交换命令
- **晶圆盒状态交换 (AR57-AR69)**：晶圆盒与挡板之间的状态交换命令
- **工艺腔室状态交换 (AR60-AR74)**：工艺腔室与挡板之间的状态交换命令
- **冷却腔状态交换 (AR64-AR78)**：冷却腔与挡板之间的状态交换命令

#### 错误处理
- **报警代码 (RA36-RA49)**：各种错误情况的报警代码
- **报警复位 (AR1)**：重置报警状态的命令