# 子系统配置访问器 (SubsystemConfigureAccessor)

## 概述

子系统配置访问器提供了统一、类型安全的方式来访问SS200系统中所有子系统的配置参数。按照`Zishan.SS200.Cmd\Config\SS200\SubsystemConfigure\`目录结构进行分类，为每个子系统提供专门的配置访问器。

## 架构设计

### 分层结构

```
SS200InterLockMain.SubsystemConfigure
├── Robot (RobotConfigureAccessor)
│   ├── 配置设置参数 (RPS1-RPS29)
│   └── 位置参数 (RP1-RP28)
├── ChamberA (ChamberConfigureAccessor)
│   └── 处理室配置参数 (PPS1-PPS46)
├── ChamberB (ChamberConfigureAccessor)
│   └── 处理室配置参数 (PPS1-PPS46)
├── Shuttle (ShuttleConfigureAccessor)
│   └── 穿梭室配置参数 (SPS1-SPS23)
└── MainSystem (MainSystemConfigureAccessor)
    └── 主系统配置参数 (SSC1-SSC14)
```

### 核心类

1. **ConfigPropertyAccessor**: 配置属性访问器基类
2. **RobotConfigureAccessor**: Robot子系统配置访问器
3. **ChamberConfigureAccessor**: Chamber子系统配置访问器
4. **ShuttleConfigureAccessor**: Shuttle子系统配置访问器
5. **MainSystemConfigureAccessor**: MainSystem配置访问器
6. **SubsystemConfigureAccessor**: 统一配置访问入口

## 使用方法

### 基本访问模式

```csharp
// 获取SS200InterLockMain实例
var ss200Main = SS200InterLockMain.Instance;

// 访问子系统配置
var robotConfig = ss200Main.SubsystemConfigure.Robot;
var chamberAConfig = ss200Main.SubsystemConfigure.ChamberA;
var shuttleConfig = ss200Main.SubsystemConfigure.Shuttle;
var mainSystemConfig = ss200Main.SubsystemConfigure.MainSystem;
```

### Robot配置访问

```csharp
// 访问Robot配置设置参数
var rotateSpeed = robotConfig.RPS1_RobotRotateSpeed;
var extendSpeed = robotConfig.RPS2_RobotExtendSpeed;
var rotateTimeout = robotConfig.RPS8_RobotRotateTimeout;

// 访问Robot位置参数
var tAxisSmoothToCHA = robotConfig.RP1_TAxisSmoothToCHA;
var tAxisSmoothToCHB = robotConfig.RP2_TAxisSmoothToCHB;
var rAxisSmoothExtend = robotConfig.RP10_RAxisSmoothExtendFaceToCHA;
var zAxisZeroPosition = robotConfig.RP27_ZAxisZeroPosition;

// 获取参数值和详细信息
Console.WriteLine($"旋转速度: {rotateSpeed?.Value} {rotateSpeed?.Unit}");
Console.WriteLine($"T轴smooth到CHA: {tAxisSmoothToCHA?.Value} {tAxisSmoothToCHA?.Unit}");
Console.WriteLine($"参数描述: {rotateSpeed?.Content}");
```

### Chamber配置访问

```csharp
// 访问Chamber配置参数
var slitDoorMinTime = chamberAConfig.PPS1_SlitDoorMotionMinTime;
var vacuumPressure = chamberAConfig.PPS9_ChamberVacuumPressureSetpoint;
var rf1Power = chamberAConfig.PPS19_Rf1PowerSetpoint;
var temperature = chamberAConfig.PPS23_TemperatureSetpoint;

// 显示参数信息
Console.WriteLine($"狭缝门最小时间: {slitDoorMinTime?.Value} {slitDoorMinTime?.Unit}");
Console.WriteLine($"真空压力设定: {vacuumPressure?.Value} {vacuumPressure?.Unit}");
```

### Shuttle配置访问

```csharp
// 访问Shuttle配置参数
var nestMinTime = shuttleConfig.SPS1_CassetteNestExtendRetractMinTime;
var shuttleUpTime = shuttleConfig.SPS3_ShuttleUpDownMinTime;
var transferPressure = shuttleConfig.SPS12_ShuttleTransferPressure;

// 显示参数信息
Console.WriteLine($"卡匣巢最小时间: {nestMinTime?.Value} {nestMinTime?.Unit}");
Console.WriteLine($"传输压力: {transferPressure?.Value} {transferPressure?.Unit}");
```

### MainSystem配置访问

```csharp
// 访问MainSystem配置参数
var waferSize1 = mainSystemConfig.SSC1_Shuttle1WaferSize;
var waferSize2 = mainSystemConfig.SSC2_Shuttle2WaferSize;
var cassetteType = mainSystemConfig.SSC6_CassetteNestType;
var coolingTemp = mainSystemConfig.SSC11_TemperatureForCoolingChamber;

// 显示参数信息
Console.WriteLine($"Shuttle1晶圆尺寸: {waferSize1?.Value} {waferSize1?.Unit}");
Console.WriteLine($"卡匣巢类型: {cassetteType?.Value}");
```

## 配置参数详细信息

每个配置参数访问器提供以下属性：

- **Value**: 参数值 (int)
- **Content**: 参数描述 (string)
- **Unit**: 参数单位 (string)
- **Code**: 参数代码 (string)
- **AxisType**: 轴类型 (int)
- **Range**: 参数范围 (string)

## 参数分类说明

### Robot配置参数 (RPS1-RPS30)

- **RPS1-RPS7**: 运动速度参数
- **RPS8-RPS11**: 超时时间参数
- **RPS12-RPS14**: 插销搜索参数
- **RPS15-RPS17**: 轴偏差参数
- **RPS18-RPS24**: 晶圆位置参数
- **RPS25-RPS29**: 状态检查参数
- **RPS30**: 插销搜索Z轴高度参数

### Chamber配置参数 (PPS1-PPS46)

- **PPS1-PPS8**: 机械运动时间参数
- **PPS9-PPS12**: 压力控制参数
- **PPS13-PPS16**: 气体流量参数
- **PPS17-PPS22**: RF功率参数
- **PPS23-PPS25**: 温度控制参数

### Shuttle配置参数 (SPS1-SPS23)

- **SPS1-SPS8**: 机械运动时间参数
- **SPS9-SPS11**: 传感器使能参数
- **SPS12-SPS21**: 压力控制参数
- **SPS22-SPS23**: 回填时间参数

### MainSystem配置参数 (SSC1-SSC14)

- **SSC1-SSC2**: 晶圆尺寸配置
- **SSC3-SSC7**: 硬件配置参数
- **SSC8-SSC10**: 工艺控制参数
- **SSC11-SSC12**: 温度控制参数
- **SSC13-SSC14**: 传输控制参数

## 特性

1. **类型安全**: 使用强类型枚举和属性访问
2. **缓存机制**: 内置缓存提高访问性能
3. **异常处理**: 优雅处理配置文件缺失或参数错误
4. **单位支持**: 自动识别并提供参数单位信息
5. **实时更新**: 支持配置文件热更新
6. **统一接口**: 所有子系统使用相同的访问模式

## 注意事项

1. 配置参数访问器会自动从相应的配置提供者获取最新值
2. 如果配置文件不存在或参数缺失，访问器会返回null
3. 建议在使用前检查返回值是否为null
4. 配置参数的修改需要通过相应的配置提供者进行

## 示例代码

完整的使用示例请参考：`Zishan.SS200.Cmd\Docs\Examples\SubsystemConfigureAccessorExample.cs`
