﻿using System.Windows;
using Zishan.SS200.Cmd.ViewModels;

namespace Zishan.SS200.Cmd.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 在窗口加载后执行初始化命令
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.InitializeCommand.Execute(null);
            }
        }
    }
}
