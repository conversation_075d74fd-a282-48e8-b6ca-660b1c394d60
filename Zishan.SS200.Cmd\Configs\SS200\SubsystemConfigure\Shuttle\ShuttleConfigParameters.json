{"configureSettings": [{"id": 1, "code": "SPS1", "description": "cassette nest extend/retract min time", "value": 3, "unit": "s"}, {"id": 2, "code": "SPS2", "description": "cassette nest extend/retract max time", "value": 5, "unit": "s"}, {"id": 3, "code": "SPS3", "description": "shuttle up/down min time", "value": 10, "unit": "s"}, {"id": 4, "code": "SPS4", "description": "shuttle up/down max time", "value": 20, "unit": "s"}, {"id": 5, "code": "SPS5", "description": "shuttle rotate max time", "value": 15, "unit": "s"}, {"id": 6, "code": "SPS6", "description": "cassette door motion min time", "value": 10, "unit": "s"}, {"id": 7, "code": "SPS7", "description": "cassette door motion max time", "value": 20, "unit": "s"}, {"id": 8, "code": "SPS8", "description": "spare", "value": "N/A", "unit": "N/A"}, {"id": 9, "code": "SPS9", "description": "ISO valve motion max time", "value": 3, "unit": "s"}, {"id": 10, "code": "SPS10", "description": "slide out front sensor enable", "value": "True", "unit": "N/A"}, {"id": 11, "code": "SPS11", "description": "slide out back sensor enable", "value": "True", "unit": "N/A"}, {"id": 12, "code": "SPS12", "description": "shuttle transfer pressure", "value": 2, "unit": "<PERSON><PERSON>"}, {"id": 13, "code": "SPS13", "description": "delta pressure for shuttle up/down", "value": 50, "unit": "<PERSON><PERSON>"}, {"id": 14, "code": "SPS14", "description": "loadlock ATM pressure minimum", "value": 730, "unit": "<PERSON><PERSON>"}, {"id": 15, "code": "SPS15", "description": "shuttle ATM pressure minimum", "value": 730, "unit": "<PERSON><PERSON>"}, {"id": 16, "code": "SPS16", "description": "transfer pressure", "value": 2, "unit": "<PERSON><PERSON>"}, {"id": 17, "code": "SPS17", "description": "pump down shuttle max time", "value": 10, "unit": "min"}, {"id": 18, "code": "SPS18", "description": "loadlock ATM pressure setpoint", "value": 760, "unit": "<PERSON><PERSON>"}, {"id": 19, "code": "SPS19", "description": "shuttle ATM pressure minimum setpoint", "value": 760, "unit": "<PERSON><PERSON>"}, {"id": 20, "code": "SPS20", "description": "shuttle pressure offset", "value": 0, "unit": "<PERSON><PERSON>"}, {"id": 21, "code": "SPS21", "description": "loadlock pressure offset", "value": 0, "unit": "<PERSON><PERSON>"}, {"id": 22, "code": "SPS22", "description": "shuttle backfill max time", "value": 10, "unit": "min"}, {"id": 23, "code": "SPS23", "description": "loadlock backfill max time", "value": 10, "unit": "min"}]}