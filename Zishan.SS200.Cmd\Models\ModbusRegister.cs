using CommunityToolkit.Mvvm.ComponentModel;

namespace Zishan.SS200.Cmd.Models
{
    /// <summary>
    /// Modbus寄存器基类
    /// </summary>
    public partial class ModbusRegister : BaseModbusRegister
    {
        public ModbusRegister()
        {
        }

        public ModbusRegister(ushort address)
        {
            base.Address = address;
        }

        /// <summary>
        /// 合并值类型
        /// </summary>
        [ObservableProperty]
        private ModbusCombineValueType combinevalueValueType = ModbusCombineValueType.Register16;

        /// <summary>
        /// 当合并值类型改变时更新描述
        /// </summary>
        partial void OnCombinevalueValueTypeChanged(ModbusCombineValueType value)
        {
            OnPropertyChanged(nameof(CombinevalueValueTypeDescribe));
        }

        /// <summary>
        /// 获取合并值类型描述信息
        /// </summary>
        private string _combinevalueValueTypeDescribe;

        public string CombinevalueValueTypeDescribe
        {
            get => combinevalueValueType switch
            {
                ModbusCombineValueType.Register16 => "16位单寄存器",
                ModbusCombineValueType.Register32 => "32位浮点数(CDAB)",
                ModbusCombineValueType.Register32_Int => "32位整数(CDAB)",
                ModbusCombineValueType.Register32_Int_ABCD => "32位整数(ABCD大端对齐)",
                ModbusCombineValueType.String => "ASCII字符串",
                _ => "未知类型"
            };
            set => SetProperty(ref _combinevalueValueTypeDescribe, value);
        }

        /// <summary>
        /// 是否为高字，
        /// </summary>
        [ObservableProperty]
        private bool isHighWord;

        /// <summary>
        /// 低位寄存器地址
        /// </summary>
        [ObservableProperty]
        private int lowRegisterAddress;

        /// <summary>
        /// 高位寄存器地址
        /// </summary>
        [ObservableProperty]
        private int highRegisterAddress;

        /// <summary>
        /// 低位寄存器地址对应的值
        /// </summary>
        [ObservableProperty]
        private ushort lowRegisterValue;

        /// <summary>
        /// 高位寄存器地址对应的值
        /// </summary>
        [ObservableProperty]
        private ushort highRegisterValue;

        /// <summary>
        /// 寄存器类型
        /// </summary>
        [ObservableProperty]
        private ModbusRegisterType registerType;

        /// <summary>
        /// Modbus寄存器值 合并后32位值,因为是步进马达的步数，所以值为int
        /// </summary>
        [ObservableProperty]
        private int combinevalue;

        /// <summary>
        /// Modbus寄存器值 合并后ASCII String值
        /// </summary>
        [ObservableProperty]
        private string combineASCIIStringValue;

        /// <summary>
        /// 是否正在编辑
        /// </summary>
        [ObservableProperty]
        private bool isEditing;

        /// <summary>
        /// 是否为当前活动编辑项
        /// </summary>
        [ObservableProperty]
        private bool isActiveEditing;

        /// <summary>
        /// 编辑前的原始值
        /// </summary>
        [ObservableProperty]
        private float originalValue;
    }

    /// <summary>
    /// Modbus寄存器类型
    /// </summary>
    public enum ModbusRegisterType
    {
        /// <summary>
        /// 输入寄存器（只读）
        /// </summary>
        Input,

        /// <summary>
        /// 保持寄存器（读写）
        /// </summary>
        Holding
    }

    public enum ModbusCombineValueType
    {
        /// <summary>
        /// 单寄存器16位
        /// </summary>
        Register16,

        /// <summary>
        /// 双寄存器 32位，用于转化为32位 CDAB Float型
        /// </summary>
        Register32,

        /// <summary>
        /// 双寄存器 32位，用于转化为32位 CDAB int型
        /// </summary>
        Register32_Int,

        /// <summary>
        /// 双寄存器 32位，用于转化为32位 ABCD 大端对齐 int型
        /// </summary>
        Register32_Int_ABCD,

        /// <summary>
        /// ASCII字符串
        /// </summary>
        String,
    }
}