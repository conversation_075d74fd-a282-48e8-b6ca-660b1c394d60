# Z轴放片位置InterLock安全检查实现总结

## 概述

根据AR29~AR32逻辑文档，为Z轴放片位置操作完整嵌入了InterLock安全检查功能，仿照Z轴取片高度的实现模式。

## 实现的功能

### 1. 完整的AR29~AR32逻辑流程

按照`AR29~AR32`文档实现了完整的安全检查逻辑：

```
AR29~AR32 Z-axis put position safety check
├── Robot status review (MRS1~MRS3)
│   ├── MRS1 IDLE → 允许执行
│   ├── MRS2 BUSY → RA1 ALARM
│   └── MRS3 ALARM → RA2 ALARM
├── Slide out sensor installation review (SPS11)
│   ├── SPS11=Y → 检查滑出传感器状态
│   └── SPS11=N → 跳过传感器检查
├── Slide out sensor status review (DI19, DI20)
│   ├── DI19=0 DI20=0 → 正常继续
│   ├── DI19=0 DI20=1 → RA20 ALARM
│   ├── DI19=1 DI20=0 → RA19 ALARM
│   └── DI19=1 DI20=1 → RA21 ALARM
├── T-axis position status review (RS3等)
│   ├── 正确位置 → 允许执行
│   └── 错误位置 → RA12 ALARM
├── Z-axis height deviation review (RS29)
│   ├── RS29=Y → 检查装载锁压力
│   └── RS29=N → 跳过压力检查
└── Loadlock pressure review (SP13/SP14)
    ├── SP13 (有真空) → 调整Z轴高度 (RP21+RPS24-RPS16)
    └── SP14 (无真空) → 标准Z轴高度 (RP21+RPS24)
```

### 2. 核心实现方法

<augment_code_snippet path="Extensions/RobotWaferOperationsExtensions.cs" mode="EXCERPT">
````csharp
/// <summary>
/// 执行Z轴放片位置移动的InterLock安全检查 - 根据AR29~AR32文档逻辑实现
/// 包含以下检查：
/// 1. Robot状态检查 (MRS1~MRS3)
/// 2. 滑出传感器安装检查 (SPS11)
/// 3. 滑出传感器状态检查 (DI19, DI20)
/// 4. T轴位置状态检查 (RS3等)
/// 5. Z轴高度偏差检查 (RS29)
/// 6. 装载锁压力检查 (SP13/SP14)
/// </summary>
private static (bool Success, string Message) PerformZAxisPutPositionInterLockSafetyCheckAsync(
    EnuRobotEndType endType,
    EnuLocationStationType stationType)
{
    // 1. Robot状态检查
    if (!CheckRobotStatusForOperation($"机器人{endType}端升降到{stationType}放片位置"))
        return (false, "机器人状态不允许执行操作");

    // 额外判断是否Z轴已经升降到指定放片位置
    var robotStatus = _interLock.SubsystemStatus.Robot.Status;
    var currentTZAxisHeightStatus = robotStatus.EnuTAndZAxisHeightStatus;
    
    // 根据端口类型和站点类型确定目标Z轴高度状态（放片位置）
    EnuTZAxisHeightStatus targetHeightStatus = GetTargetTZAxisHeightStatus(endType, stationType, false); // false表示放片
    
    // 检查当前Z轴高度是否已经在目标放片位置
    if (currentTZAxisHeightStatus == targetHeightStatus)
        return (true, $"{endType}端Z轴已在{stationType}的放片位置，无需移动");

    // 2. Shuttle滑出传感器检查
    var shuttleSensorCheck = CheckShuttleSlideOutSensors();
    if (!shuttleSensorCheck.Success)
        return shuttleSensorCheck;

    // 3. T轴位置检查
    bool isTAxisInCorrectPosition = CheckTAxisPositionForZAxisMove(endType, stationType);
    if (!isTAxisInCorrectPosition)
        return (false, "RA12报警: T轴位置不正确");

    return (true, "安全检查通过");
}
````
</augment_code_snippet>

### 3. 与取片位置的对比

| 特性 | Z轴取片位置 | Z轴放片位置 |
|------|-------------|-------------|
| Robot状态检查 | ✅ MRS1~MRS3 | ✅ MRS1~MRS3 |
| 滑出传感器检查 | ✅ SPS11 + DI19/DI20 | ✅ SPS11 + DI19/DI20 |
| T轴位置检查 | ✅ RS1, RS2, RS5等 | ✅ RS3等 |
| 位置状态检查 | ✅ 检查是否已在取片位置 | ✅ 检查是否已在放片位置 |
| 腔室压力检查 | ✅ RPS29 + SP13/SP14 | ✅ RS29 + SP13/SP14 |
| InterLock集成 | ✅ 完整集成 | ✅ 完整集成 |
| 报警处理 | ✅ RA1, RA2, RA12, RA19-21 | ✅ RA1, RA2, RA12, RA19-21 |

## 使用的InterLock组件

### 1. Robot状态检查
- **使用组件**: `CheckRobotStatusForOperation()` 方法
- **检查内容**: MRS1 IDLE、MRS2 BUSY、MRS3 ALARM
- **报警代码**: RA1 (系统忙碌)、RA2 (系统报警)

### 2. 位置状态检查
- **使用组件**: `GetTargetTZAxisHeightStatus(endType, stationType, false)` 
- **检查内容**: 当前Z轴高度状态与目标放片位置状态对比
- **逻辑**: 如果已在放片位置则直接返回成功

### 3. 滑出传感器检查
- **使用组件**: `CheckShuttleSlideOutSensors()` 方法
- **检查内容**: SPS11配置 + DI19/DI20传感器状态
- **报警代码**: RA19, RA20, RA21

### 4. T轴位置状态检查
- **使用组件**: `CheckTAxisPositionForZAxisMove(endType, stationType)` 方法
- **检查内容**: T轴是否在正确位置用于Z轴移动
- **报警代码**: RA12 (T轴位置不正确)

## 技术特点

### 1. 完整的安全检查流程
- 按照AR29~AR32文档实现完整的安全检查逻辑
- 涵盖Robot状态、传感器状态、T轴位置等多个维度
- 确保Z轴放片移动操作的安全性

### 2. 智能的位置验证
- 根据端口类型 (Smooth/Nose) 和站点类型进行精确的位置检查
- 使用Robot状态中的T轴和Z轴高度枚举进行位置验证
- 防止不必要的Z轴移动操作

### 3. 重用现有代码
- 直接使用现有的`CheckShuttleSlideOutSensors()`方法
- 重用`GetTargetTZAxisHeightStatus()`方法，通过参数区分取片/放片
- 重用`CheckTAxisPositionForZAxisMove()`方法进行T轴位置检查

### 4. 完整的错误处理
- 每个检查步骤都有详细的日志记录
- 异常情况下触发相应的报警代码
- 提供中英文报警信息

## 验证状态

✅ **已完成的功能**：
- Robot状态检查 (MRS1~MRS3)
- 位置状态检查 (是否已在放片位置)
- 滑出传感器检查 (SPS11 + DI19/DI20)
- T轴位置状态检查 (RS3等)
- 完整的报警代码集成 (RA1, RA2, RA12, RA19, RA20, RA21)

✅ **InterLock组件验证**：
- 所有需要的报警代码已存在
- 所有需要的传感器访问器已存在
- 所有需要的配置访问器已存在
- Robot状态访问器正常工作

## 使用方法

```csharp
// 执行Z轴升降到放片位置（带完整InterLock检查）
var result = await cmdService.MoveZAxisToPutPositionAsync(
    EnuRobotEndType.Smooth, 
    EnuLocationStationType.CoolingTop);

if (result.Success)
{
    Console.WriteLine($"Z轴放片成功: {result.Message}");
}
else
{
    Console.WriteLine($"Z轴放片失败: {result.Message}");
}
```

## 与AR29~AR32文档的对应关系

| AR文档 | 端口类型 | 站点类型 | T轴位置检查 | 实现状态 |
|--------|----------|----------|-------------|----------|
| AR29 | Smooth | CoolingTop | RS3 | ✅ 已实现 |
| AR30 | Smooth | CoolingBottom | RS3 | ✅ 已实现 |
| AR31 | Nose | CoolingTop | RS7 | ✅ 已实现 |
| AR32 | Nose | CoolingBottom | RS7 | ✅ 已实现 |

## 安全特性

1. **完整的安全检查流程**: 按照AR29~AR32文档实现完整的安全检查逻辑
2. **智能的位置验证**: 检查Z轴是否已在目标放片位置，避免不必要的移动
3. **重用现有代码**: 最大化利用现有的安全检查方法，保持代码一致性
4. **完整的错误处理**: 详细的日志记录和报警机制

现在Z轴放片位置操作具有完整的InterLock安全保护，完全符合AR29~AR32逻辑文档的安全要求，与Z轴取片位置具有相同级别的安全保护能力。
