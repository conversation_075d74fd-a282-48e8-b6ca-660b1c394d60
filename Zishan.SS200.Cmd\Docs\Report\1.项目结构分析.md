# Zishan.SS200.Cmd 项目结构分析

## 目录结构

项目采用标准MVVM架构组织目录结构：

```
Zishan.SS200.Cmd/
├── Adorners/             // UI装饰器
├── App.xaml              // 应用程序入口XAML
├── App.xaml.cs           // 应用程序入口代码
├── Assets/               // 资源文件目录
│   └── Images/           // 图像资源
├── Behavior/             // UI行为
├── Common/               // 通用工具类
├── Config/               // 配置类
├── Configs/              // 配置文件目录
│   ├── Config.ini        // 主配置文件
│   ├── Log4netConfig/    // 日志配置
│   └── SHTLParameter.json// 设备参数
├── Converters/           // WPF值转换器
├── Enums/                // 枚举定义
├── Extensions/           // 扩展方法
├── Models/               // 数据模型
├── Services/             // 服务层
├── ValidationRules/      // 验证规则
├── ViewModels/           // 视图模型
│   └── DesignViewModels/ // 设计时视图模型
├── Views/                // 视图
└── Zishan.SS200.Cmd.csproj // 项目文件
```

## 关键文件概述

### 1. 项目配置文件

- **Zishan.SS200.Cmd.csproj**：项目配置文件，定义了项目依赖、资源和编译选项
- **Configs/Config.ini**：应用程序配置文件，包含Modbus连接参数等配置
- **Configs/Log4netConfig/log4net.config**：日志配置文件

### 2. 应用程序入口

- **App.xaml/App.xaml.cs**：应用程序入口，负责启动流程、依赖注入和初始化

### 3. 视图文件

- **Views/MainWindow.xaml**：主窗口视图，定义了UI布局和交互元素

### 4. 视图模型

- **ViewModels/MainWindowViewModel.cs**：主窗口视图模型，提供数据绑定和命令实现

### 5. 服务层

- **Services/IModbusClientService.cs**：Modbus客户端服务接口定义
- **Services/ModbusClientService.cs**：Modbus客户端服务实现
- **Services/S200McuCmdService.cs**：设备命令服务
- **Services/ModbusRegisterService.cs**：Modbus寄存器操作服务
- **Services/ConfigurationService.cs**：配置服务

### 6. 数据模型

- **Models/CmdTaskHandlel.cs**：命令任务处理模型
- **Models/ModbusRegister.cs**：Modbus寄存器模型
- **Models/BaseModbusRegister.cs**：基础Modbus寄存器模型

### 7. 枚举定义

- **Enums/EnuShuttleCmdName.cs**：Shuttle命令枚举
- **Enums/EnuRobotCmdName.cs**：机器人命令枚举

### 8. 转换器

- **Converters/InvertBooleanConverter.cs**：布尔值取反转换器
- **Converters/DescriptionConverter.cs**：描述转换器
- **Converters/AndMultiValueConverter.cs**：与逻辑转换器

### 9. 全局配置

- **Common/Golbal.cs**：全局静态变量和常量
- **Config/IniConfig.cs**：INI配置类

## 技术栈概述

1. **UI框架**：WPF (.NET 8.0 Windows)
2. **架构模式**：MVVM (使用Prism框架)
3. **依赖注入**：DryIoc (通过Prism集成)
4. **数据绑定**：CommunityToolkit.Mvvm
5. **UI控件库**：HandyControl
6. **通信协议**：Modbus TCP (使用NModbus库)
7. **配置管理**：INI文件 (使用ini-parser-netstandard)
8. **日志系统**：log4net
9. **序列化**：Newtonsoft.Json

项目整体结构清晰，遵循了MVVM架构的最佳实践，将UI、业务逻辑和数据访问分离，使代码更易维护和测试。 