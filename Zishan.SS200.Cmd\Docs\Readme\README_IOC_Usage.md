# IOC容器使用指南

## 概述

为了方便从IOC容器中获取实例，我们在`App`类中封装了静态方法`GetInstance<T>()`，提供了更简洁和安全的IOC容器访问方式。

## 基本用法

### 1. 获取已注册的实例

```csharp
// 获取SS200InterLock实例
var interLock = App.GetInstance<SS200InterLock>();

// 获取IS200McuCmdService实例
var mcuService = App.GetInstance<IS200McuCmdService>();

// 获取MainWindowViewModel实例
var mainViewModel = App.GetInstance<MainWindowViewModel>();

// 获取SubsystemStatus实例
var subsystemStatus = App.GetInstance<SubsystemStatus>();
```

### 2. 带参数获取实例

```csharp
// 带参数获取实例（使用ValueTuple格式）
var instance = App.GetInstance<SomeService>((typeof(string), "paramValue"), (typeof(int), 42));
```

## 优势

### 1. 简洁性
- **原来的方式**：
```csharp
var a = System.Windows.Application.Current as App;
IContainerProvider c = a.Container;
var instance = c.Resolve<SomeType>();
```

- **新的方式**：
```csharp
var instance = App.GetInstance<SomeType>();
```

### 2. 安全性
- 自动检查应用程序是否已初始化
- 提供详细的错误信息和日志记录
- 类型安全的泛型方法

### 3. 错误处理
- 当应用程序未初始化时抛出`InvalidOperationException`
- 自动记录错误日志，便于调试
- 提供清晰的错误消息

## 使用场景

### 1. 在ViewModel中获取服务实例
```csharp
public class SomeViewModel : ObservableObject
{
    private readonly IS200McuCmdService _mcuService;
    
    public SomeViewModel()
    {
        // 从IOC容器获取服务实例
        _mcuService = App.GetInstance<IS200McuCmdService>();
    }
}
```

### 2. 在服务中获取其他服务
```csharp
public class SomeService
{
    private readonly IModbusClientService _modbusService;
    
    public SomeService()
    {
        _modbusService = App.GetInstance<IModbusClientService>();
    }
}
```

### 3. 在工具类中获取配置
```csharp
public static class SomeUtility
{
    public static void DoSomething()
    {
        var config = App.GetInstance<IniConfig>();
        // 使用配置...
    }
}
```

## 注意事项

1. **应用程序初始化**：确保在应用程序完全初始化后再使用此方法
2. **类型注册**：确保要获取的类型已在IOC容器中注册
3. **生命周期管理**：注意实例的生命周期（单例、瞬态等）
4. **异常处理**：建议在使用时添加适当的异常处理

## 错误处理示例

```csharp
try
{
    var service = App.GetInstance<ISomeService>();
    // 使用服务...
}
catch (InvalidOperationException ex)
{
    // 处理应用程序未初始化或容器不可用的错误
    Console.WriteLine($"IOC容器错误: {ex.Message}");
}
catch (Exception ex)
{
    // 处理其他错误
    Console.WriteLine($"获取实例失败: {ex.Message}");
}
```

## 已注册的类型

以下类型已在IOC容器中注册为单例：

- `IS200McuCmdService` → `S200McuCmdService`
- `IModbusClientService` → `ModbusClientService`
- `MainWindowViewModel`
- `RobotStatusPanelViewModel`
- `ModbusDICoilsPanelViewModel`
- `ModbusDOCoilsPanelViewModel`
- `LogViewModel`
- `TransferWaferViewModel`
- `IntegerConversionViewModel`
- `SubsystemStatus`
- `SS200InterLock`

## 迁移指南

如果你正在使用旧的IOC容器访问方式，可以按照以下步骤迁移：

1. **查找旧代码**：
```csharp
var app = Application.Current as App;
var container = app.Container;
var instance = container.Resolve<SomeType>();
```

2. **替换为新方式**：
```csharp
var instance = App.GetInstance<SomeType>();
```

3. **添加异常处理**（可选）：
```csharp
try
{
    var instance = App.GetInstance<SomeType>();
    // 使用实例...
}
catch (InvalidOperationException ex)
{
    // 处理错误...
}
```

这种方式提供了更简洁、更安全的IOC容器访问方式，同时保持了良好的错误处理和日志记录。 