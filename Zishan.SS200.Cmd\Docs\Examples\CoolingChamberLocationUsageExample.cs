using System;
using <PERSON>ishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Extensions;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// 冷却腔位置使用示例
    /// 演示如何解决T轴/R轴只需要CoolingChamber，而Z轴需要CoolingTop/CoolingBottom的问题
    /// </summary>
    public class CoolingChamberLocationUsageExample
    {
        /// <summary>
        /// T轴控制示例 - 使用区域级别位置
        /// </summary>
        public void TAxisControlExample()
        {
            Console.WriteLine("=== T轴控制示例 ===");

            // 场景1：直接使用CoolingChamber
            var tAxisTarget = EnuLocationStationType.CoolingChamber;
            Console.WriteLine($"T轴目标位置: {tAxisTarget.GetLocationDescription()}");
            Console.WriteLine($"使用场景: {tAxisTarget.GetUsageScenario()}");
            
            // 场景2：从精确位置转换为T轴位置
            var preciseTarget = EnuLocationStationType.CoolingTop;
            var tAxisLocation = preciseTarget.GetTAxisLocation();
            Console.WriteLine($"精确位置 {preciseTarget} → T轴位置 {tAxisLocation}");
            Console.WriteLine($"说明: T轴不关心是上层还是下层，统一使用区域位置");
            Console.WriteLine();
        }

        /// <summary>
        /// R轴控制示例 - 使用区域级别位置
        /// </summary>
        public void RAxisControlExample()
        {
            Console.WriteLine("=== R轴控制示例 ===");

            // R轴伸缩到冷却腔
            var rAxisTarget = EnuLocationStationType.CoolingChamber;
            Console.WriteLine($"R轴目标位置: {rAxisTarget.GetLocationDescription()}");
            
            // 从不同精确位置转换
            var coolingBottom = EnuLocationStationType.CoolingBottom;
            var rAxisLocation = coolingBottom.GetRAxisLocation();
            Console.WriteLine($"精确位置 {coolingBottom} → R轴位置 {rAxisLocation}");
            Console.WriteLine($"说明: R轴伸缩距离相同，不区分上下层");
            Console.WriteLine();
        }

        /// <summary>
        /// Z轴控制示例 - 使用精确位置
        /// </summary>
        public void ZAxisControlExample()
        {
            Console.WriteLine("=== Z轴控制示例 ===");

            // Z轴必须使用精确位置
            var zAxisTargetTop = EnuLocationStationType.CoolingTop;
            var zAxisTargetBottom = EnuLocationStationType.CoolingBottom;
            
            Console.WriteLine($"Z轴上层目标: {zAxisTargetTop.GetLocationDescription()}");
            Console.WriteLine($"Z轴下层目标: {zAxisTargetBottom.GetLocationDescription()}");
            
            // 从区域位置转换为Z轴位置
            var areaLocation = EnuLocationStationType.CoolingChamber;
            var zAxisLocation = areaLocation.GetZAxisLocation();
            Console.WriteLine($"区域位置 {areaLocation} → Z轴位置 {zAxisLocation} (默认上层)");
            Console.WriteLine($"说明: Z轴必须知道精确高度，默认选择上层");
            Console.WriteLine();
        }

        /// <summary>
        /// 完整的Robot移动示例
        /// </summary>
        public void CompleteRobotMovementExample()
        {
            Console.WriteLine("=== 完整Robot移动示例 ===");

            // 目标：将晶圆移动到冷却腔上层
            var targetLocation = EnuLocationStationType.CoolingTop;
            Console.WriteLine($"任务: 移动到 {targetLocation.GetLocationDescription()}");
            Console.WriteLine();

            // 各轴使用不同的位置抽象
            var tAxisLocation = targetLocation.GetTAxisLocation();
            var rAxisLocation = targetLocation.GetRAxisLocation();
            var zAxisLocation = targetLocation.GetZAxisLocation();

            Console.WriteLine("各轴使用的位置:");
            Console.WriteLine($"  T轴: {tAxisLocation} - {tAxisLocation.GetUsageScenario()}");
            Console.WriteLine($"  R轴: {rAxisLocation} - {rAxisLocation.GetUsageScenario()}");
            Console.WriteLine($"  Z轴: {zAxisLocation} - {zAxisLocation.GetUsageScenario()}");
            Console.WriteLine();

            // 验证位置关系
            Console.WriteLine("位置关系验证:");
            Console.WriteLine($"  {zAxisLocation} 属于 {tAxisLocation} 区域? {zAxisLocation.BelongsToCoolingArea(tAxisLocation)}");
            Console.WriteLine($"  T轴和Z轴在同一冷却区域? {tAxisLocation.InSameCoolingArea(zAxisLocation)}");
            Console.WriteLine();
        }

        /// <summary>
        /// 位置转换示例
        /// </summary>
        public void LocationConversionExample()
        {
            Console.WriteLine("=== 位置转换示例 ===");

            // 精确位置 → 区域位置
            var coolingTop = EnuLocationStationType.CoolingTop;
            var coolingBottom = EnuLocationStationType.CoolingBottom;
            
            Console.WriteLine("精确位置 → 区域位置:");
            Console.WriteLine($"  {coolingTop} → {coolingTop.ToCoolingChamberArea()}");
            Console.WriteLine($"  {coolingBottom} → {coolingBottom.ToCoolingChamberArea()}");
            Console.WriteLine();

            // 区域位置 → 精确位置
            var coolingChamber = EnuLocationStationType.CoolingChamber;
            var defaultPrecise = coolingChamber.ToDefaultCoolingPreciseLocation();
            var allPrecise = coolingChamber.GetCoolingPreciseLocations();
            
            Console.WriteLine("区域位置 → 精确位置:");
            Console.WriteLine($"  {coolingChamber} → 默认: {defaultPrecise}");
            Console.WriteLine($"  {coolingChamber} → 所有: [{string.Join(", ", allPrecise)}]");
            Console.WriteLine();
        }

        /// <summary>
        /// 实际代码使用示例
        /// </summary>
        public void PracticalCodeExample()
        {
            Console.WriteLine("=== 实际代码使用示例 ===");

            // 模拟Robot移动方法
            void MoveTAxis(EnuLocationStationType location)
            {
                var tAxisLoc = location.GetTAxisLocation();
                Console.WriteLine($"T轴旋转到: {tAxisLoc}");
            }

            void MoveRAxis(EnuLocationStationType location)
            {
                var rAxisLoc = location.GetRAxisLocation();
                Console.WriteLine($"R轴伸缩到: {rAxisLoc}");
            }

            void MoveZAxis(EnuLocationStationType location)
            {
                var zAxisLoc = location.GetZAxisLocation();
                Console.WriteLine($"Z轴升降到: {zAxisLoc}");
            }

            // 测试不同的输入位置
            var testLocations = new[]
            {
                EnuLocationStationType.CoolingChamber,  // 区域位置
                EnuLocationStationType.CoolingTop,      // 精确位置
                EnuLocationStationType.CoolingBottom    // 精确位置
            };

            foreach (var location in testLocations)
            {
                Console.WriteLine($"\n输入位置: {location}");
                MoveTAxis(location);
                MoveRAxis(location);
                MoveZAxis(location);
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 位置判断示例
        /// </summary>
        public void LocationJudgmentExample()
        {
            Console.WriteLine("=== 位置判断示例 ===");

            var locations = new[]
            {
                EnuLocationStationType.ChamberA,
                EnuLocationStationType.CoolingChamber,
                EnuLocationStationType.CoolingTop,
                EnuLocationStationType.CoolingBottom
            };

            foreach (var location in locations)
            {
                Console.WriteLine($"{location}:");
                Console.WriteLine($"  是冷却位置? {location.IsCoolingLocation()}");
                Console.WriteLine($"  是冷却精确位置? {location.IsCoolingPreciseLocation()}");
                Console.WriteLine($"  是工艺腔室? {location.IsProcessChamberLocation()}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static void RunAllExamples()
        {
            var example = new CoolingChamberLocationUsageExample();
            
            example.TAxisControlExample();
            example.RAxisControlExample();
            example.ZAxisControlExample();
            example.CompleteRobotMovementExample();
            example.LocationConversionExample();
            example.PracticalCodeExample();
            example.LocationJudgmentExample();
            
            Console.WriteLine("=== 解决方案总结 ===");
            Console.WriteLine("1. 添加了CoolingChamber枚举值，用于T轴和R轴");
            Console.WriteLine("2. 保留了CoolingTop和CoolingBottom，用于Z轴精确控制");
            Console.WriteLine("3. 提供了转换扩展方法，支持不同精度级别的位置转换");
            Console.WriteLine("4. 各轴可以根据需要使用合适的位置抽象级别");
            Console.WriteLine("5. 概念清晰：区域级别 vs 精确级别");
        }
    }
}
