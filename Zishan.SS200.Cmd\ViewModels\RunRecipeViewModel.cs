﻿using System;
using System.Collections.Generic;
using MaterialDesignThemes.Wpf;
using Prism.Commands;
using Prism.Ioc;
using Prism.Mvvm;
using Prism.Regions;
using Prism.Services.Dialogs;
using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using Wu.ViewModels;
using Wu.Wpf.Common;

using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.DTO;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Models.Shared;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using log4net;

using Prism.Services.Dialogs;

using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Mvvm;
using Zishan.SS200.Cmd.Constants;

namespace Zishan.SS200.Cmd.ViewModels
{
    public partial class RunRecipeViewModel : ViewModel, IDialogHostAware
    {
        #region 字段

        private readonly ILog _logger = LogManager.GetLogger(typeof(RunRecipeViewModel));
        private readonly IContainerProvider _provider;
        //private readonly IDialogHostService _dialogHost;

        //private readonly DBAccessService _dBAccessService;

        private DialogParameters param;

        public DelegateCommand<string> SaveCommand { get; set; }
        public DelegateCommand CancelCommand { get; set; }

        #endregion 字段

        #region 属性

        public string DialogHostName { get; set; } = "RootDialog";

        /// <summary>
        /// /左边条码输入
        /// </summary>
        [ObservableProperty]
        private string leftBarcode;

        /// <summary>
        /// 右边条码输入
        /// </summary>
        [ObservableProperty]
        private string rightBarcode;

        /// <summary>
        /// 当前配方名列表
        /// </summary>
        [ObservableProperty]
        private RecipeNames curRecipeName;

        /// <summary>
        /// 选中的配方
        /// </summary>
        [ObservableProperty]
        private string curSelectedRecipe;

        partial void OnCurSelectedRecipeChanged(string value)
        {
            CurDbRecipeSeqInfo = CurRecipeName.DbRecipeSeqInfo.FirstOrDefault(t => t.RecipeName == value);

            // 更新当前选中的配方名
            CurRecipeName.CurSelectedRecipeName = CurRecipeName.RecipeNameList.FirstOrDefault(t => t.KeyValue == value);
            //if (CurDbRecipeSeqInfo != null)
            //{
            //    MessageBox.Show($"{CurDbRecipeSeqInfo.RecipeName}配方信息：\r\n{CurDbRecipeSeqInfo.ToString()}，请确认这些配方配置是否OK", "流程配方确认信息★★★", MessageBoxButton.OK, MessageBoxImage.Warning);
            //}
        }

        /// <summary>
        /// 当前选中的数据库流程配方信息
        /// </summary>
        [ObservableProperty]
        private RecipeSeqInfo curDbRecipeSeqInfo;

        /// <summary>
        /// 制定配方列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> recipeList;

        /// <summary>
        /// Wafer数量列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<int> waferCountList;

        /// <summary>
        /// Left 已经选中的WaferSlot列表
        /// </summary>
        [ObservableProperty]
        private List<int> leftWaferSlotSelected;

        /// <summary>
        /// Right 已经选中的WaferSlot列表
        /// </summary>
        [ObservableProperty]
        private List<int> rightWaferSlotSelected;

        /// <summary>
        /// Top Count
        /// </summary>
        [ObservableProperty]
        private int top;

        partial void OnTopChanged(int value)
        {
            if (value < 0)
            {
                Top = 0;
            }
            UpdateSelectedItems();
        }

        /// <summary>
        /// Bottom Count
        /// </summary>
        [ObservableProperty]
        private int bottom;

        partial void OnBottomChanged(int value)
        {
            if (value < 0)
            {
                Bottom = 0;
            }
            UpdateSelectedItems();
        }

        /// <summary>
        /// CurrentDto
        /// </summary>
        [ObservableProperty]
        private object currentDto = new();

        /// <summary>
        /// LeftSelectedItems
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<int> leftSelectedItems = new ObservableCollection<int>();

        /// <summary>
        /// RightSelectedItems
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<int> rightSelectedItems = new ObservableCollection<int>();

        #endregion 属性

        #region 构造函数

        public RunRecipeViewModel()
        {
        }

        public RunRecipeViewModel(IContainerProvider provider, IDialogHostService dialogHost, RecipeNames recipeNames)
        {
            _provider = provider;

            CurRecipeName = recipeNames;

            //_dBAccessService = new DBAccessService(App.AppIniConfig.DatabaseAccessType);

            //重新选中原来的配方
            if (CurRecipeName != null && CurRecipeName.CurSelectedRecipeName != null && !string.IsNullOrEmpty(CurRecipeName.CurSelectedRecipeName.KeyValue))
            {
                CurSelectedRecipe = CurRecipeName.CurSelectedRecipeName.KeyValue;
            }
            else
            {
                CurSelectedRecipe = string.Empty;
            }

            //SaveCommand = new DelegateCommand<string>(Save);
            CancelCommand = new DelegateCommand(Cancel);

            WaferCountList = new ObservableCollection<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25 };
            LeftWaferSlotSelected = new List<int>();
            RightWaferSlotSelected = new List<int>();

            //var blResult = _dBAccessService.GenerateBarCode(out string leftBarCode, out string rightBarCode);
            var blResult = false;
            if (blResult)
            {
                // LeftBarcode = leftBarCode;
                // RightBarcode = rightBarCode;
            }
            else
            {
                LeftBarcode = "123456789";
                RightBarcode = "987654321";
                _logger.Debug($"生成条码失败，使用默认条码：{LeftBarcode}，{RightBarcode}");
            }

            //if (!Common.Golbal.IsDevDebug)
            //{
            //    RecipeList = new ObservableCollection<string> { "配方A" };
            //}
            //else
            //{
            //    RecipeList = new ObservableCollection<string> { "配方ABC", "配方A", "配方B", "配方C", "配方AB", "配方AC", "配方BC" };
            //}

            RecipeList = new ObservableCollection<string>();
            if (!Golbal.IsDevDebug)
            {
                //根据dev扩展文件名获取SS200Modbus配方列表
                List<string> tempOpenRecipeList = PubHelper.GetRecipeList(Golbal.WorkRootPath, "*.dev");

                var tempRecipName = CurRecipeName.RecipeNameList.Where(t => tempOpenRecipeList.Contains(t.KeyValue)).Select(t => t.KeyValue).ToList();
                RecipeList.AddRange(tempRecipName);

                //var tempRecipName = CurRecipeName.RecipeNameList.FirstOrDefault(t => t.KeyValue.Equals("配方A"));
                //if (tempRecipName != null)
                //{
                //    RecipeList = new ObservableCollection<string> { tempRecipName.KeyValue };
                //}
                //else
                //{
                //    MessageBox.Show("未找到 配方A，针对目前IR400只有一个CHA的配方，请配置", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                //}
            }
            else
            {
                var tempRecipName = CurRecipeName.RecipeNameList.Select(t => t.KeyValue).ToList();
                RecipeList.AddRange(tempRecipName);

                //RecipeList = new ObservableCollection<string> { "配方ABC", "配方A", "配方B", "配方C", "配方AB", "配方AC", "配方BC" };
            }

            //if (Golbal.IsDevDebug)
            //{
            //    Top = 1;
            //}
            //else
            //{
            //    Top = 3;
            //}
            Bottom = 1;
        }

        /// <summary>
        /// 打开该弹窗时执行
        /// </summary>
        public void OnDialogOpened(IDialogParameters parameters)
        {
            //获取传递的参数，来自远程EAP传递的配方信息
            if (parameters != null && parameters.ContainsKey(EAPInfoConstants.TagDialogRemoteRunRecipeInfo))
            {
                var cuRunRecipeInfo = parameters.GetValue<DtoRunRecipeInfo>(EAPInfoConstants.TagDialogRemoteRunRecipeInfo);
                if (cuRunRecipeInfo != null)
                {
                    LeftBarcode = cuRunRecipeInfo.LeftBarcode;
                    RightBarcode = cuRunRecipeInfo.RightBarcode;
                    CurSelectedRecipe = CurRecipeName.RecipeNameList.FirstOrDefault(t => t.KeyId == Convert.ToInt32(cuRunRecipeInfo.RecipeName))?.KeyValue;//Aspen2 配方使用的是Int型
                    Top = cuRunRecipeInfo.Top;
                    Bottom = cuRunRecipeInfo.Bottom;

                    //远程EAP延迟调用OnRunRecipe
                    Task.Run(async () =>
                    {
                        await Task.Delay(10000);
                        Application.Current.Dispatcher.Invoke(() => OnRunRecipe());
                    });
                }

                //var getResult = await employeeService.GetSinglePersonalStorageAsync(oldDto);
                //if (getResult != null && getResult.Status)
                //{
                //    CurrentDto = getResult.Result;
                //}
            }
        }

        #endregion 构造函数

        #region 命令

        /// <summary>
        /// 执行命令，不使用
        /// </summary>
        //public DelegateCommand<string> ExecuteCommand { get; private set; }
        DelegateCommand IDialogHostAware.SaveCommand { get; set; } = new DelegateCommand(() => { });

        [RelayCommand]
        private void OnRunRecipe()
        {
            if (string.IsNullOrEmpty(CurSelectedRecipe))
            {
                MessageBox.Show("配方不能为空，请选择配方", "选择配方", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (Top <= 0 || Bottom <= 0)
            {
                MessageBox.Show($"Top={Top}，Bottom={Bottom}，请选择正确的Wafer槽位区间，上层Top为结束位，下层Bottom为起始位，不能<0", "上下槽区间范围设置错误",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (Top < Bottom)
            {
                MessageBox.Show($"Top={Top}，Bottom={Bottom}，Top不能小于Bottom", "Top不能小于Bottom", MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                return;
            }

            if (!DialogHost.IsDialogOpen(DialogHostName))
                return;
            //添加返回的参数
            param = new DialogParameters();
            param.Add("ButtonType", "Run");

            var leftRecipeIds = new List<EnuWaferMappingStatus>(new EnuWaferMappingStatus[DtoRunRecipeInfo.SlotMaxCount]);
            var rightRecipeIds = new List<EnuWaferMappingStatus>(new EnuWaferMappingStatus[DtoRunRecipeInfo.SlotMaxCount]);

            for (int i = 0; i < DtoRunRecipeInfo.SlotMaxCount; i++)
            {
                leftRecipeIds[i] = EnuWaferMappingStatus.NotExist;
                rightRecipeIds[i] = EnuWaferMappingStatus.NotExist;
                if (LeftSelectedItems.Contains(i))
                {
                    leftRecipeIds[i] = EnuWaferMappingStatus.Exist;
                }

                if (RightSelectedItems.Contains(i))
                {
                    rightRecipeIds[i] = EnuWaferMappingStatus.Exist;
                }
            }

            DtoRunRecipeInfo runRecipeInfo = new DtoRunRecipeInfo()
            {
                LeftBarcode = LeftBarcode,
                RightBarcode = RightBarcode,
                RecipeName = CurSelectedRecipe,
                Top = Top,
                Bottom = Bottom,
                LeftRecipeIds = leftRecipeIds,
                RightRecipeIds = rightRecipeIds
            };
            runRecipeInfo.CalculateRecipeIds();

            LeftWaferSlotSelected = LeftWaferSlotSelected;

            param.Add(EAPInfoConstants.TagDialogCurRunRecipeInfo, runRecipeInfo);
            param.Add(nameof(LeftBarcode), LeftBarcode);

            //关闭窗口,并返回参数
            DialogHost.Close(DialogHostName, new DialogResult(ButtonResult.OK, param));
        }

        [RelayCommand]
        private void OnStop()
        {
            //添加返回的参数
            param = new DialogParameters();
            param.Add("ButtonType", "OnStop");

            //关闭窗口,并返回参数
            DialogHost.Close(DialogHostName, new DialogResult(ButtonResult.OK, param));
        }

        #endregion 命令

        #region 方法

        /// <summary>
        /// 更新选中的项目
        /// </summary>
        public void UpdateSelectedItems()
        {
            if (Bottom <= Top)
            {
                var leftItems = Enumerable.Range(Bottom, Top - Bottom + 1).ToList();
                var rightItems = new List<int>(leftItems);
                UpdateSelectedItems(leftItems, rightItems);
            }
        }

        /// <summary>
        /// 更新左侧已选择的晶圆槽位
        /// </summary>
        /// <param name="selectedItems">选中的项目</param>
        public void UpdateLeftWaferSlotSelected(System.Collections.IList selectedItems)
        {
            LeftWaferSlotSelected = selectedItems.Cast<int>().ToList();
        }

        /// <summary>
        /// 更新右侧已选择的晶圆槽位
        /// </summary>
        /// <param name="selectedItems">选中的项目</param>
        public void UpdateRightWaferSlotSelected(System.Collections.IList selectedItems)
        {
            RightWaferSlotSelected = selectedItems.Cast<int>().ToList();
        }

        public void UpdateSelectedItems(IList<int> leftItems, IList<int> rightItems)
        {
            LeftSelectedItems = new ObservableCollection<int>(leftItems);
            RightSelectedItems = new ObservableCollection<int>(rightItems);
            LeftWaferSlotSelected = new List<int>(leftItems);
            RightWaferSlotSelected = new List<int>(rightItems);

            //列表选中的内容动态更新Bottom到Top的范围
            if (leftItems.Count > 0 && rightItems.Count > 0)
            {
                Bottom = Math.Min(leftItems.Min(), rightItems.Min());
                Top = Math.Max(leftItems.Max(), rightItems.Max());
            }
            else if (leftItems.Count > 0)
            {
                Bottom = leftItems.Min();
                Top = leftItems.Max();
            }
            else if (rightItems.Count > 0)
            {
                Bottom = rightItems.Min();
                Top = rightItems.Max();
            }
        }

        /// <summary>
        /// 取消
        /// </summary>
        private void Cancel()
        {
            //若窗口处于打开状态则关闭
            if (DialogHost.IsDialogOpen(DialogHostName))
                DialogHost.Close(DialogHostName, new DialogResult(ButtonResult.No));
        }

        #endregion 方法
    }
}