# Zishan.SS200.Cmd 目录结构

## 📁 项目架构概览

**Zishan.SS200.Cmd** 采用现代化的企业级WPF应用程序目录结构，严格遵循分层架构设计原则和领域驱动设计（DDD）理念。项目结构清晰地体现了关注点分离、单一职责原则和依赖倒置原则，确保了代码的高内聚、低耦合，为系统的可维护性、可扩展性和可测试性奠定了坚实的基础。

### 🎯 设计理念
- **分层架构**：清晰的六层架构设计，每层职责明确
- **模块化设计**：功能模块独立，便于并行开发和维护
- **标准化命名**：统一的命名规范，提高代码可读性
- **可扩展性**：预留扩展接口，支持功能增强和新设备接入

### 🏗️ 架构设计原则
- **分层架构**：表示层、业务逻辑层、基础设施层、数据层清晰分离
- **模块化设计**：功能模块独立，便于维护和扩展
- **依赖注入**：通过IoC容器管理对象生命周期
- **配置驱动**：通过配置文件控制系统行为
- **资源管理**：静态资源和动态配置分离管理

## 项目根目录结构

```
Zishan.SS200.Cmd/
├── 📁 Adorners/                    # WPF装饰器层 - UI增强组件
├── 📁 Assets/                      # 静态资源层 - 图片、图标、文档
├── 📁 Behaviors/                   # WPF行为层 - UI交互行为
├── 📁 Commands/                    # 命令处理层 - 设备命令处理器
├── 📁 Common/                      # 通用工具层 - 公共工具和帮助类
├── 📁 Config/                      # 配置解析层 - 配置文件解析器
├── 📁 Configs/                     # 配置数据层 - 各类配置文件
├── 📁 Constants/                   # 常量定义层 - 系统常量和枚举
├── 📁 Converters/                  # 数据转换层 - MVVM数据转换器
├── 📁 DTO/                         # 数据传输层 - 数据传输对象
├── 📁 Docs/                        # 文档管理层 - 项目文档和说明
├── 📁 DragDropHandler/             # 拖拽处理层 - 拖拽操作处理器
├── 📁 Enums/                       # 枚举定义层 - 业务枚举类型
├── 📁 Extensions/                  # 扩展方法层 - 类型扩展和工具扩展
├── 📁 Interface/                   # 接口定义层 - 系统接口契约
├── 📁 Models/                      # 数据模型层 - 业务实体和数据模型
├── 📁 Mvvm/                        # MVVM基础层 - MVVM基类和框架
├── 📁 Services/                    # 服务业务层 - 核心业务服务
├── 📁 UserControls/                # 用户控件层 - 自定义UI控件
├── 📁 Utilities/                   # 工具类层 - 专用工具类
├── 📁 ValidationRules/             # 验证规则层 - 数据验证规则
├── 📁 ViewModels/                  # 视图模型层 - MVVM视图模型
├── 📁 Views/                       # 视图界面层 - WPF用户界面
├── 📄 App.xaml                     # 应用程序定义文件
├── 📄 App.xaml.cs                  # 应用程序启动逻辑
├── 📄 README.md                    # 项目主要说明文档
├── 📄 README-CURSOR.md             # Cursor编辑器说明
├── 📄 ToDo.md                      # 项目待办事项列表
└── 📄 Zishan.SS200.Cmd.csproj     # MSBuild项目配置文件
```

## 分层架构说明

### 表示层 (Presentation Layer)
- **Views/** - WPF视图界面
- **ViewModels/** - MVVM视图模型
- **UserControls/** - 自定义用户控件
- **Converters/** - 数据绑定转换器
- **Behaviors/** - UI交互行为
- **Adorners/** - UI装饰器

### 业务逻辑层 (Business Logic Layer)
- **Services/** - 核心业务服务
- **Commands/** - 设备命令处理
- **Models/** - 业务数据模型
- **DTO/** - 数据传输对象

### 基础设施层 (Infrastructure Layer)
- **Common/** - 通用工具和帮助类
- **Config/** - 配置管理和解析
- **Extensions/** - 类型扩展方法
- **Utilities/** - 专用工具类
- **Interface/** - 接口定义

### 数据层 (Data Layer)
- **Configs/** - 配置文件存储
- **Assets/** - 静态资源文件
- **Docs/** - 文档资源

## 核心目录详细分析

### 1. Adorners/ - WPF装饰器层
**功能职责**：提供UI元素的视觉增强和装饰功能

**核心文件**：
- **SimpleCircleAdorner.cs** - 圆形装饰器实现
  - 功能：为UI元素添加圆形边框装饰
  - 应用场景：设备状态指示器、选中状态标识
  - 技术特点：继承Adorner基类，支持自定义渲染

### 2. Assets/ - 静态资源层
**功能职责**：管理应用程序的静态资源文件

**目录结构**：
- **Images/** - 图像资源目录
  - **logo.ico** - 应用程序主图标（16x16, 32x32, 48x48多尺寸）
  - **CalibrationContent.png** - 设备校准界面背景图
  - **plate.png** - 晶圆盘可视化图像
  - **device_icons/** - 设备状态图标集合
- **MyModbus_Simulation/** - Modbus仿真资源
  - 包含仿真配置文件和测试数据
- **TransferWafer/** - 晶圆传输可视化资源
  - 晶圆传输动画素材和路径配置
- **Documentation/** - 嵌入式文档资源
  - **robot_control_commands.md** - 机器人控制命令参考手册

### 3. Behaviors/ - WPF行为层
**功能职责**：实现可重用的UI交互行为

**核心文件**：
- **EllipseProgressBehavior.cs** - 椭圆进度条行为
  - 功能：为椭圆控件添加进度显示能力
  - 应用：设备运行进度可视化
  - 特性：支持动画效果和自定义样式
- **ListBoxScrollToBottomBehavior.cs** - 列表自动滚动行为
  - 功能：列表框新增项目时自动滚动到底部
  - 应用：日志显示窗口的实时更新
  - 特性：支持条件滚动和平滑动画
- **SelectedItemsBehavior.cs** - 多选项行为
  - 功能：增强ListBox的多选功能
  - 应用：批量设备选择和操作
  - 特性：支持MVVM绑定和命令触发

### 4. Commands/ - 命令处理层
**功能职责**：实现设备命令的处理和执行逻辑

**架构设计**：
- **BaseDeviceCommandHandler.cs** - 命令处理器抽象基类
  - 定义命令执行的标准流程
  - 提供参数验证和异常处理框架
  - 实现命令执行状态监控
- **设备专用命令处理器**：
  - **ShuttleCommandHandler.cs** - Shuttle设备命令处理
    - 支持X/Y/Z轴运动控制命令
    - 实现位置定位和速度控制
    - 提供安全限位检查
  - **RobotCommandHandler.cs** - Robot设备命令处理
    - 支持机械臂运动控制
    - 实现晶圆抓取和放置逻辑
    - 提供碰撞检测和路径规划
  - **ChaCommandHandler.cs** - ChamberA设备命令处理
    - 支持工艺参数设置
    - 实现温度和压力控制
    - 提供工艺流程监控
  - **ChbCommandHandler.cs** - ChamberB设备命令处理
    - 与ChamberA功能类似
    - 支持并行工艺处理
- **DeviceCommandFactory.cs** - 命令工厂
  - 实现命令处理器的创建和管理
  - 支持动态命令处理器注册
  - 提供命令处理器缓存机制
- **IDeviceCommandHandler.cs** - 命令处理器接口
  - 定义命令处理器的标准契约
  - 支持异步命令执行
  - 提供命令结果回调机制

**命令规范**：
- **CommandSpec/** - 命令规范定义目录
  - 包含各设备支持的命令定义
  - 命令参数规范和验证规则
  - 命令执行超时和重试配置

### 5. Common/ - 通用工具层
**功能职责**：提供系统级的通用工具和帮助类

**核心组件**：
- **日志管理**：
  - **AppLog.cs** - 应用程序日志封装
    - 封装log4net功能
    - 提供结构化日志记录
    - 支持日志级别动态调整
- **配置管理**：
  - **ConfigProviderBase.cs** - 配置提供者基类
    - 定义配置加载和验证框架
    - 支持配置热更新
    - 提供配置变更通知机制
  - **ConfigProviderGenerator.cs** - 配置提供者生成器
    - 动态生成强类型配置类
    - 支持配置验证和默认值
    - 提供配置文档自动生成
- **数据处理**：
  - **JsonHelper.cs** - JSON序列化工具
    - 封装Newtonsoft.Json功能
    - 提供类型安全的序列化
    - 支持自定义转换器
  - **EnumDictionary.cs** - 枚举字典工具
    - 提供枚举值的快速查找
    - 支持枚举描述和本地化
    - 实现枚举值缓存机制
- **系统工具**：
  - **Global.cs** - 全局变量和常量管理
    - 定义系统级常量
    - 管理全局状态变量
    - 提供系统信息访问
  - **StopwatchHelper.cs** - 性能计时工具
    - 提供高精度计时功能
    - 支持性能统计和分析
    - 实现计时结果格式化
- **外部集成**：
  - **RedisHelper.cs** - Redis缓存工具
    - 封装StackExchange.Redis
    - 提供分布式缓存功能
    - 支持缓存过期和更新策略
- **测试支持**：
  - **S200MockStatus.cs** - 设备状态模拟器
    - 提供设备状态模拟功能
    - 支持测试场景配置
    - 实现状态变化模拟

### 6. Config/ - 配置解析器
- **BatchCommandParser.cs** - 批量命令解析器
- **CmdParameterParser.cs** - 命令参数解析器
- **ErrorCodeInfoParser.cs** - 错误代码信息解析器
- **ErrorCodeInfoParserExample.cs** - 错误代码解析器示例
- **IniConfig.cs** - INI配置文件解析器
- **MotorAlarmInforsera.cs** - 电机报警信息序列化器
- **SS200/** - SS200系统配置

### 7. Configs/ - 配置文件
- **AlarmInfo/** - 报警信息配置
  - **MotorAlarmInfo.json** - 电机报警信息
- **CmdParameter/** - 命令参数配置
  - **ChamberAParameter.json** - ChamberA参数配置
  - **ChamberBParameter.json** - ChamberB参数配置
  - **RobotParameter.json** - Robot参数配置
  - **ShuttleParameter.json** - Shuttle参数配置
- **ErrorCodeInfo/** - 错误代码信息
  - **RobotAlarmInfo.json** - Robot报警信息
  - **SHTLAlarmInfo.json** - Shuttle报警信息
- **IoConfigInfo/** - IO配置信息
  - **McuDeviceCoilName.json** - MCU设备线圈名称配置
- **Log4netConfig/** - 日志配置
  - **log4net.config** - log4net配置文件
- **LoopConfigTest/** - 循环配置测试
  - **BatchCommands.json** - 批量命令配置
- **Recipe/** - 配方配置
  - **IR400RecipeNames.json** - IR400配方名称
- **SS200/** - SS200系统配置
- **Config.ini** - 主配置文件

### 8. Constants/ - 常量定义
- **EAPConstants..cs** - EAP常量定义
- **IoInterface.cs** - IO接口常量
- **Shuttle.cs** - Shuttle相关常量
- **SlitDoorPLCCmdConstants.cs** - 狭缝门PLC命令常量
- **SubsystemConfigure/** - 子系统配置常量

### 9. Converters/ - 数据转换器
- **AndMultiValueConverter.cs** - 与逻辑多值转换器
- **BoolToStateConverter.cs** - 布尔到状态转换器
- **BoolToVisibilityConverter.cs** - 布尔到可见性转换器
- **BooleanConverters.cs** - 布尔转换器集合
- **BooleanToColorConverter.cs** - 布尔到颜色转换器
- **BooleanToStringConverter.cs** - 布尔到字符串转换器
- **DescriptionConverter.cs** - 描述转换器
- **EnumDescriptionTypeConverter.cs** - 枚举描述类型转换器
- **IntToStringConverter.cs** - 整数到字符串转换器
- **InverseBooleanConverter.cs** - 反向布尔转换器
- **MultiCommandParameterConverter.cs** - 多命令参数转换器
- **StringToBooleanConverter.cs** - 字符串到布尔转换器
- **TimeSpanConverter.cs** - 时间跨度转换器
- **ValueToColorConverter.cs** - 值到颜色转换器

### 10. DTO/ - 数据传输对象
- **DtoRunRecipeInfo.cs** - 运行配方信息DTO

### 11. Docs/ - 项目文档
- **CoilLookupSolution.md** - 线圈查找解决方案
- **Examples/** - 示例文档
- **Fixes/** - 修复记录
- **Issure/** - 问题记录
- **Manual/** - 使用手册
- **Readme/** - 说明文档
- **Report/** - 报告文档
- **Test/** - 测试文档
- **Todo/** - 待办事项
- **logic/** - 逻辑文档
- **通讯协议/** - 通讯协议文档

### 12. DragDropHandler/ - 拖拽处理
- **WaferDragDropHandler.cs** - 晶圆拖拽处理器

### 13. Enums/ - 枚举定义
- **Basic/** - 基础枚举
- **Command/** - 命令枚举
- **McuCmdIndex/** - MCU命令索引枚举
- **Process/** - 流程枚举
- **SS200/** - SS200系统枚举
- **CassetteRunStatusEnum.cs** - 卡匣运行状态枚举
- **EnuArmFetchSide.cs** - 机械臂取放侧枚举
- **EnuChamberGoAction.cs** - 腔体动作枚举
- **EnuDatabaseAccessType.cs** - 数据库访问类型枚举
- **EnuLogDataType.cs** - 日志数据类型枚举
- **EnuMcuDeviceType.cs** - MCU设备类型枚举
- **EnuProcessStatus.cs** - 流程状态枚举
- **EnuWaferStatus.cs** - 晶圆状态枚举
- **EnuWorkStatus.cs** - 工作状态枚举

### 14. Extensions/ - 扩展方法
- **HcGrowlExtensions.cs** - HandyControl消息扩展
- **IOEnumExtensions.cs** - IO枚举扩展
- **IoDeviceInfoExtensions.cs** - IO设备信息扩展
- **PassWordExtensions.cs** - 密码扩展
- **PrismManager.cs** - Prism管理器
- **RobotWaferOperationsExtensions.cs** - 机器人晶圆操作扩展
- **S200McuCmdServiceExtensions.cs** - S200MCU命令服务扩展

### 15. Interface/ - 接口定义
- **IReadAllAxisParameters.cs** - 读取所有轴参数接口
- **IWaferDragDropInfo.cs** - 晶圆拖拽信息接口

### 16. Models/ - 数据模型
- **Aspen/** - Aspen相关模型
- **History/** - 历史数据模型
- **IR400/** - IR400设备模型
- **Process/** - 流程模型
- **Recipe/** - 配方模型
- **SS200/** - SS200系统模型
- **Shared/** - 共享模型
- **BaseContainer.cs** - 基础容器模型
- **BaseModbusRegister.cs** - 基础Modbus寄存器模型
- **BatchCommand.cs** - 批量命令模型
- **BufferChamber.cs** - 缓冲腔体模型
- **CmdTaskHandlel.cs** - 命令任务处理模型
- **DeviceTypeOption.cs** - 设备类型选项模型
- **Loadlock.cs** - 负载锁模型
- **LogData.cs** - 日志数据模型
- **MainHost.cs** - 主机模型
- **ModbusCoil.cs** - Modbus线圈模型
- **ModbusRegister.cs** - Modbus寄存器模型
- **OperateResult.cs** - 操作结果模型
- **StatusProperty.cs** - 状态属性模型

### 17. Mvvm/ - MVVM基类
- **RegionViewModelBase.cs** - 区域视图模型基类
- **ViewModel.cs** - 视图模型基类

### 18. Services/ - 服务层
- **CoilStatusHelper.cs** - 线圈状态帮助服务
- **ConfigurationService.cs** - 配置服务
- **IModbusClientService.cs** - Modbus客户端服务接口
- **ModbusClientService.cs** - Modbus客户端服务实现
- **ModbusRegisterService.cs** - Modbus寄存器服务
- **S200McuCmdService.cs** - S200 MCU命令服务
- **S200McuCmdServiceExample.cs** - S200 MCU命令服务示例
- **UILogService.cs** - UI日志服务
- **Interfaces/** - 服务接口定义

### 19. UserControls/ - 用户控件
- **Arm.xaml/.cs** - 机械臂控件
- **UContainer.xaml/.cs** - 通用容器控件

### 20. Utilities/ - 工具类
- **ModbusCommandUtility.cs** - Modbus命令工具类

### 21. ValidationRules/ - 验证规则
- **BindingProxy.cs** - 绑定代理
- **GreaterThan0ValidationRule.cs** - 大于0验证规则
- **NumericValidationRule.cs** - 数值验证规则
- **ValidationParams.cs** - 验证参数

### 22. ViewModels/ - 视图模型
- **DesignViewModels/** - 设计时视图模型
- **DialogViewModels/** - 对话框视图模型
- **Dock/** - 停靠视图模型
- **BasicCommandTestViewModel.cs** - 基础命令测试视图模型
- **IntegerConversionViewModel.cs** - 整数转换视图模型
- **MainWindowViewModel.cs** - 主窗口视图模型
- **RunRecipeViewModel.cs** - 运行配方视图模型
- **S200McuCmdPanelViewModel.cs** - S200 MCU命令面板视图模型
- **TransferWaferViewModel.cs** - 晶圆传输视图模型

### 23. Views/ - 视图
- **Converters/** - 视图转换器
- **Dialogs/** - 对话框视图
- **Dock/** - 停靠视图
- **BasicCommandTest.xaml/.cs** - 基础命令测试视图
- **IntegerConversionView.xaml/.cs** - 整数转换视图
- **MainWindow.xaml/.cs** - 主窗口视图
- **RunRecipe.xaml/.cs** - 运行配方视图
- **S200McuCmdPanel.xaml/.cs** - S200 MCU命令面板视图
- **TransferWafer.xaml/.cs** - 晶圆传输视图

## 项目文件说明

### 核心配置文件
- **App.xaml** - WPF应用程序定义文件，定义全局资源和启动窗口
- **App.xaml.cs** - 应用程序代码隐藏文件，包含启动逻辑和依赖注入配置
- **Zishan.SS200.Cmd.csproj** - 项目文件，定义依赖项、编译选项和资源配置

### 文档文件
- **README.md** - 项目主要说明文档，包含项目介绍、技术栈和使用指南
- **README-CURSOR.md** - Cursor编辑器相关说明
- **ToDo.md** - 项目待办事项列表

## 目录设计原则

1. **功能分离**：按功能模块组织代码，便于维护和扩展
2. **层次清晰**：遵循MVVM架构，视图、视图模型、模型分离
3. **配置集中**：所有配置文件统一放在Configs目录
4. **资源管理**：图片、文档等资源文件分类存放
5. **接口抽象**：接口定义独立，便于依赖注入和测试
6. **工具复用**：通用工具类和扩展方法集中管理

## 📊 目录关系图

### 项目目录依赖关系

```mermaid
graph TB
    subgraph "🖥️ 表示层目录"
        A[Views/] --> A1[MainWindow.xaml<br/>主窗口]
        A --> A2[S200McuCmdPanel.xaml<br/>设备控制面板]
        A --> A3[TransferWafer.xaml<br/>晶圆搬运界面]
        A --> A4[BasicCommandTest.xaml<br/>命令测试界面]

        B[ViewModels/] --> B1[MainWindowViewModel.cs<br/>主窗口视图模型]
        B --> B2[S200McuCmdPanelViewModel.cs<br/>设备控制视图模型]
        B --> B3[TransferWaferViewModel.cs<br/>晶圆搬运视图模型]

        C[UserControls/] --> C1[自定义用户控件]
        D[Converters/] --> D1[数据转换器]
        E[Behaviors/] --> E1[UI行为]
    end

    subgraph "⚙️ 业务逻辑层目录"
        F[Services/] --> F1[S200McuCmdService.cs<br/>设备命令服务]
        F --> F2[ModbusClientService.cs<br/>Modbus通信服务]
        F --> F3[ConfigurationService.cs<br/>配置管理服务]
        F --> F4[BatchCommandParser.cs<br/>批量命令解析]

        G[Commands/] --> G1[设备命令处理器]
        H[Models/] --> H1[业务数据模型]
        I[DTO/] --> I1[数据传输对象]
    end

    subgraph "🔧 基础设施层目录"
        J[Common/] --> J1[Golbal.cs<br/>全局变量]
        J --> J2[通用工具类]

        K[Config/] --> K1[IniConfig.cs<br/>配置解析器]
        L[Extensions/] --> L1[扩展方法]
        M[Utilities/] --> M1[专用工具类]
        N[Interface/] --> N1[接口定义]
        O[Helpers/] --> O1[帮助类]
    end

    subgraph "💾 数据层目录"
        P[Configs/] --> P1[Config.ini<br/>主配置文件]
        P --> P2[SS200/<br/>系统配置]
        P --> P3[CmdParameter/<br/>命令参数]
        P --> P4[Log4netConfig/<br/>日志配置]

        Q[Assets/] --> Q1[Images/<br/>图片资源]
        Q --> Q2[静态资源文件]

        R[Docs/] --> R1[项目文档]
        R --> R2[API文档]
    end

    %% 依赖关系
    A --> B
    B --> F
    F --> H
    F --> G
    H --> I
    F --> J
    F --> K
    B --> L
    F --> N
    F --> O
    F --> P
    A --> Q
```

### 配置文件组织结构

```mermaid
graph TB
    A[Configs/ 配置根目录] --> B[Config.ini 主配置文件]
    A --> C[SS200/ 系统配置目录]
    A --> D[CmdParameter/ 命令参数目录]
    A --> E[Log4netConfig/ 日志配置目录]
    A --> F[AlarmInfo/ 报警信息目录]
    A --> G[ErrorCodeInfo/ 错误代码目录]
    A --> H[IoConfigInfo/ IO配置目录]
    A --> I[Recipe/ 配方配置目录]
    A --> J[LoopConfigTest/ 循环测试配置]

    C --> C1[AlarmCode/ 报警代码配置]
    C --> C2[SubsystemConfigure/ 子系统配置]
    C --> C3[IOInterface/ IO接口配置]

    C1 --> C11[Robot/ 机器人报警]
    C1 --> C12[ChamberA/ 腔体A报警]
    C1 --> C13[ChamberB/ 腔体B报警]
    C1 --> C14[Shuttle/ 传输报警]

    D --> D1[RobotParameter.json 机器人参数]
    D --> D2[ShuttleParameter.json 传输参数]
    D --> D3[ChamberAParameter.json 腔体A参数]
    D --> D4[ChamberBParameter.json 腔体B参数]

    E --> E1[log4net.config 日志配置文件]

    H --> H1[McuDeviceCoilName.json 设备线圈名称]

    I --> I1[IR400RecipeNames.json 配方名称]

    J --> J1[BatchCommands.json 批量命令配置]
```

## 🎯 目录设计最佳实践

### 1. 分层架构体现
- **表示层**：Views、ViewModels、UserControls、Converters
- **业务层**：Services、Commands、Models、DTO
- **基础设施层**：Common、Config、Extensions、Utilities
- **数据层**：Configs、Assets、Docs

### 2. 模块化组织
- **功能模块**：按业务功能组织相关文件
- **技术模块**：按技术职责分离不同类型的代码
- **配置模块**：统一的配置文件管理

### 3. 可维护性设计
- **命名规范**：清晰的文件和目录命名
- **职责单一**：每个目录只负责特定功能
- **依赖清晰**：明确的依赖关系和引用方向
