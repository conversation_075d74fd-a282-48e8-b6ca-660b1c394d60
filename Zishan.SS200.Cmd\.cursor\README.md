# Cursor 配置说明

本项目使用 Cursor 作为首选开发环境，并包含一系列配置文件以提供一致的开发体验。

## 配置文件结构

```
.cursor/
├── common/                 # 通用规范文件
│   ├── automation.mdc      # 自动化测试规范
│   ├── document.mdc        # 文档编写规范
│   ├── error-handling.mdc  # 错误处理规范
│   ├── general.mdc         # 项目通用规范
│   ├── git.mdc             # Git提交规范
│   ├── gitflow.mdc         # Git工作流规范
│   ├── logging.mdc         # 日志记录规范
│   ├── performance.mdc     # 性能优化规范
│   └── security.mdc        # 安全性规范
├── rules/                  # 语言特定规则
│   ├── c-sharpcn.mdc       # C#语言规范
│   ├── modbus-communication.mdc # Modbus通信规范
│   └── wpf-mvvm.mdc        # WPF和MVVM开发规范
├── tasks/                  # 任务配置
│   ├── build.json          # 构建任务配置
│   └── test.json           # 测试任务配置
├── templates/              # 代码模板
│   ├── model.cs            # 模型类模板
│   ├── service.cs          # 服务类模板
│   ├── view.xaml           # 视图模板
│   └── viewmodel.cs        # ViewModel模板
├── launch.json             # 调试配置
├── mcp.json                # Cursor MCP配置
└── rules.json              # Cursor规则配置
```

项目根目录：

- `.cursorignore`：指定 Cursor 应忽略的文件和目录
- `.cursorsettings`：Cursor 编辑器设置
- `.cursorrc`：Cursor AI 助手配置
- `.editorconfig`：编辑器通用配置

## 主要功能

### 1. AI 助手配置

`.cursorrc` 文件配置了 AI 助手的行为，包括：

- 提示设置：指导 AI 以工业自动化和机器人控制系统专家的身份回答
- 项目上下文：提供项目的基本描述
- 代码风格指南：定义代码风格和最佳实践
- 文件模板：提供常用文件的模板

### 2. 代码模板

在 `.cursor/templates/` 目录下提供了常用的代码模板：

- `model.cs`：使用 CommunityToolkit.Mvvm 的模型类模板
- `service.cs`：服务类模板，支持依赖注入和异步操作
- `view.xaml`：XAML 视图模板
- `viewmodel.cs`：使用 CommunityToolkit.Mvvm 的 ViewModel 模板

使用模板：

```
cursor template view_model ClassName=MyFeature NAMESPACE=Zishan.SS200.Cmd.ViewModels
```

### 3. 编码规范

包含多种规范文件，分为通用规范和语言特定规范：

- 通用规范：文档、Git、测试、日志、错误处理等
- 语言规范：C#、WPF/MVVM、Modbus 通信等

### 4. 编辑器设置

`.cursorsettings` 文件配置了编辑器行为：

- 使用 Claude 3.7 Sonnet 作为默认 AI 模型
- 设置字体、缩进、格式化选项等
- 配置文件排除规则和搜索选项
- 推荐安装的扩展

### 5. 任务和调试配置

- `tasks/build.json`：定义构建、发布、清理等任务
- `tasks/test.json`：定义测试相关任务，包括覆盖率测试
- `launch.json`：定义调试配置，支持启动、附加和测试调试

## 使用方法

### 安装 Cursor

1. 从[Cursor 官网](https://cursor.sh/)下载并安装 Cursor
2. 打开项目文件夹

### 使用代码模板

```bash
# 创建ViewModel
cursor template view_model CLASS_NAME=FeatureViewModel NAMESPACE=Zishan.SS200.Cmd.ViewModels

# 创建服务
cursor template service CLASS_NAME=DataService NAMESPACE=Zishan.SS200.Cmd.Services

# 创建模型
cursor template model CLASS_NAME=FeatureModel NAMESPACE=Zishan.SS200.Cmd.Models

# 创建视图
cursor template view CLASS_NAME=FeatureView NAMESPACE=Zishan.SS200.Cmd.Views
```

### 使用 AI 助手

1. 在编辑器中按下 `Ctrl+K` 或点击右下角的 AI 助手图标
2. 输入问题或请求，AI 助手会根据配置的上下文和规则提供帮助

### 运行命令

```bash
# 构建项目
cursor run build

# 运行项目
cursor run run

# 运行测试
cursor run test

# 清理项目
cursor run clean
```

### 使用任务

Cursor 支持 VS Code 风格的任务系统，可以通过以下方式使用：

1. 按下 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Tasks: Run Task" 并选择
3. 从列表中选择要运行的任务

可用任务包括：

- `build`：构建项目
- `publish`：发布项目
- `watch`：监视文件变化并自动构建
- `clean`：清理项目
- `restore`：还原依赖项
- `test`：运行所有测试
- `test-with-coverage`：运行测试并生成覆盖率报告
- `test-specific`：运行特定类别的测试

### 调试应用

1. 按下 `F5` 启动调试
2. 使用调试工具栏控制调试会话
3. 设置断点、监视变量、评估表达式等

可用的调试配置：

- `启动`：启动应用并附加调试器
- `附加到进程`：附加到已运行的进程
- `调试单元测试`：启动并调试单元测试

## 自定义配置

### 修改 AI 助手设置

编辑 `.cursorrc` 文件，可以调整：

- AI 提示设置
- 项目上下文描述
- 代码风格指南
- 文件模板

### 添加新规则

1. 在 `.cursor/common/` 或 `.cursor/rules/` 目录下创建新的 `.mdc` 文件
2. 编辑 `.cursor/rules.json` 文件，在 `settings.rules` 部分添加新规则

### 添加新模板

1. 在 `.cursor/templates/` 目录下创建新模板文件
2. 编辑 `.cursorrc` 文件，在 `templates` 部分添加新模板配置

### 添加新任务

1. 编辑 `.cursor/tasks/build.json` 或 `.cursor/tasks/test.json` 文件
2. 按照 VS Code 任务格式添加新任务

### 添加新调试配置

编辑 `.cursor/launch.json` 文件，按照 VS Code 调试配置格式添加新配置
