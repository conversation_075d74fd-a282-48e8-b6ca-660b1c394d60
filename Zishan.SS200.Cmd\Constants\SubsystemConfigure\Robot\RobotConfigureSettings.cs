using System;
using <PERSON>ishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;

namespace Zishan.SS200.Cmd.Constants.SubsystemConfigure.Robot
{
    /// <summary>
    /// 机器人配置参数常量
    /// </summary>
    public static class RobotConfigureSettings
    {
        /// <summary>
        /// 获取轴移动速度
        /// </summary>
        /// <param name="axisType">轴类型</param>
        /// <returns>对应轴的移动速度</returns>
        public static int GetAxisSpeed(EnuRobotAxisType axisType)
        {
            return RobotConfigureSettingsProvider.Instance.GetAxisSpeed(axisType);
        }

        /// <summary>
        /// 获取是否启用RTZ轴慢速模式
        /// </summary>
        /// <returns>是否启用RTZ轴慢速模式</returns>
        public static bool GetIsRobotMoveSlowly()
        {
            return RobotConfigureSettingsProvider.Instance.GetIsRobotMoveSlowly();
        }

        /// <summary>
        /// 获取轴慢速移动速度百分比
        /// </summary>
        /// <param name="axisType">轴类型</param>
        /// <returns>对应轴的慢速移动速度百分比</returns>
        public static int GetAxisSlowSpeed(EnuRobotAxisType axisType)
        {
            return RobotConfigureSettingsProvider.Instance.GetAxisSlowSpeed(axisType);
        }

        /// <summary>
        /// 获取轴移动最大时间
        /// </summary>
        /// <param name="axisType">轴类型</param>
        /// <returns>对应轴的移动最大时间（秒）</returns>
        public static int GetAxisMaxTime(EnuRobotAxisType axisType)
        {
            return RobotConfigureSettingsProvider.Instance.GetAxisMaxTime(axisType);
        }

        /// <summary>
        /// 获取轴回零最大时间
        /// </summary>
        /// <param name="axisType">轴类型</param>
        /// <returns>对应轴的回零最大时间（秒）</returns>
        public static int GetAxisHomeMaxTime(EnuRobotAxisType axisType)
        {
            return RobotConfigureSettingsProvider.Instance.GetAxisHomeMaxTime(axisType);
        }

        /// <summary>
        /// 获取T轴回零偏差步数
        /// </summary>
        /// <returns>T轴回零偏差步数</returns>
        public static int GetTAxisHomeDeviation()
        {
            return RobotConfigureSettingsProvider.Instance.GetTAxisHomeDeviation();
        }

        /// <summary>
        /// 获取R轴零位的T轴偏差
        /// </summary>
        /// <returns>R轴零位的T轴偏差</returns>
        public static int GetTAxisDeviationForRZero()
        {
            return RobotConfigureSettingsProvider.Instance.GetTAxisDeviationForRZero();
        }

        /// <summary>
        /// 获取反馈真空的Z轴步进偏差
        /// </summary>
        /// <returns>反馈真空的Z轴步进偏差</returns>
        public static int GetZAxisDeviationForVacuum()
        {
            return RobotConfigureSettingsProvider.Instance.GetZAxisDeviationForVacuum();
        }

        /// <summary>
        /// 获取穿梭无真空的Z轴步进偏差
        /// </summary>
        /// <returns>穿梭无真空的Z轴步进偏差</returns>
        public static int GetZAxisDeviationForNoVacuum()
        {
            return RobotConfigureSettingsProvider.Instance.GetZAxisDeviationForNoVacuum();
        }

        /// <summary>
        /// 获取晶圆尺寸的ZPin补偿值
        /// </summary>
        /// <param name="waferSize">晶圆尺寸</param>
        /// <returns>对应晶圆尺寸的Z轴位置</returns>
        public static int GetWaferZPinByWaferSize(EnuWaferSize waferSize)
        {
            return RobotConfigureSettingsProvider.Instance.GetWaferZPosition(waferSize);
        }

        /// <summary>
        /// 获取晶圆盒Z轴取放晶圆增量
        /// </summary>
        /// <param name="waferSize">晶圆尺寸</param>
        /// <returns>对应晶圆尺寸的Z轴取放晶圆增量</returns>
        public static int GetCassetteZPutDelta(EnuWaferSize waferSize)
        {
            return RobotConfigureSettingsProvider.Instance.GetCassetteZPutDelta(waferSize);
        }

        /// <summary>
        /// 获取冷却腔Z轴取放晶圆增量
        /// </summary>
        /// <returns>冷却腔Z轴取放晶圆增量</returns>
        public static int GetCoolingChamberZDelta()
        {
            return RobotConfigureSettingsProvider.Instance.GetCoolingChamberZDelta();
        }

        /// <summary>
        /// 获取Pin Search最大偏差值【指的左右2边Setp最大偏差值不能超过制定值】
        /// </summary>
        /// <returns>Pin Search最大偏差值</returns>
        public static int GetPinSearchMaxDelta()
        {
            return RobotConfigureSettingsProvider.Instance.GetPinSearchMaxDelta();
        }

        /// <summary>
        /// 获取晶圆实际状态检查设置
        /// </summary>
        /// <returns>是否检查晶圆实际状态</returns>
        public static bool GetWaferStatusCheck()
        {
            return RobotConfigureSettingsProvider.Instance.GetWaferStatusCheck();
        }

        /// <summary>
        /// 获取机器人旋转时Z轴高度
        /// </summary>
        /// <returns>机器人旋转时Z轴高度</returns>
        public static int GetZHeightForRotation()
        {
            return RobotConfigureSettingsProvider.Instance.GetZHeightForRotation();
        }

        /// <summary>
        /// 获取可以做完Pin Seach最低距离步数（机限位置）
        /// </summary>
        /// <returns>插销搜索最低步进</returns>
        public static int GetPinSearchLowestStep()
        {
            return RobotConfigureSettingsProvider.Instance.GetPinSearchLowestStep();
        }

        /// <summary>
        /// 获取腔室压力检查设置
        /// </summary>
        /// <returns>是否检查腔室压力</returns>
        public static bool GetChamberPressureReview()
        {
            return RobotConfigureSettingsProvider.Instance.GetChamberPressureReview();
        }

        /// <summary>
        /// 获取配置参数值
        /// </summary>
        /// <param name="code">参数代码 (如 "RPS1")</param>
        /// <returns>参数值</returns>
        public static int GetSettingValue(EnuRobotConfigureSettingCodes enuRobotConfigureSettingCodes)
        {
            return RobotConfigureSettingsProvider.Instance.GetSettingValue(enuRobotConfigureSettingCodes);
        }
    }
}