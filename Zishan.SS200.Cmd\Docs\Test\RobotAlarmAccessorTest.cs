using System;
using <PERSON>ishan.SS200.Cmd.Models.SS200;
using <PERSON>ishan.SS200.Cmd.Enums.SS200.AlarmCode.Robot;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// Robot报警访问器测试类
    /// 用于验证所有Robot报警代码访问器是否正常工作
    /// </summary>
    public class RobotAlarmAccessorTest
    {
        /// <summary>
        /// 测试所有Robot报警代码访问器
        /// </summary>
        public static void TestAllRobotAlarmAccessors()
        {
            try
            {
                Console.WriteLine("开始测试Robot报警代码访问器...");
                
                var interLock = SS200InterLockMain.Instance;
                var robotAlarm = interLock.AlarmCode.Robot;

                // 测试RA1-RA10
                TestAccessor("RA1", robotAlarm.RA1_SystemBusyReject);
                TestAccessor("RA2", robotAlarm.RA2_SystemAlarmReject);
                TestAccessor("RA3", robotAlarm.RA3_RAxisNotHomeError);
                TestAccessor("RA4", robotAlarm.RA4_TAxisPositionError);
                TestAccessor("RA5", robotAlarm.RA5_RotationTimeout);
                TestAccessor("RA6", robotAlarm.RA6_ExtensionTimeout);
                TestAccessor("RA7", robotAlarm.RA7_LiftTimeout);
                TestAccessor("RA8", robotAlarm.RA8_CHASlitDoorNotOpen);
                TestAccessor("RA9", robotAlarm.RA9_CHBSlitDoorNotOpen);
                TestAccessor("RA10", robotAlarm.RA10_ZAxisPositionError);

                // 测试RA11-RA20
                TestAccessor("RA11", robotAlarm.RA11_RAxisNotHomeZAxisError);
                TestAccessor("RA12", robotAlarm.RA12_TAxisPositionZAxisError);
                TestAccessor("RA13", robotAlarm.RA13_ShuttlePositionPinSearchError);
                TestAccessor("RA14", robotAlarm.RA14_PaddleHitPinBall);
                TestAccessor("RA15", robotAlarm.RA15_PinBallStatusFailure);
                TestAccessor("RA16", robotAlarm.RA16_CannotFindPinBall);
                TestAccessor("RA17", robotAlarm.RA17_PaddleOccupiedOrWaferInconsistent);
                TestAccessor("RA18", robotAlarm.RA18_WaferOnPaddlePinSearchInvalid);
                TestAccessor("RA19", robotAlarm.RA19_BLSlideOutDetected);
                TestAccessor("RA20", robotAlarm.RA20_BRSlideOutDetected);

                // 测试RA21-RA30
                TestAccessor("RA21", robotAlarm.RA21_BLAndBRSlideOutDetected);
                TestAccessor("RA22", robotAlarm.RA22_PinSearchValueDeltaOutOfRange);
                TestAccessor("RA23", robotAlarm.RA23_Shuttle1ExistDisabled);
                TestAccessor("RA24", robotAlarm.RA24_Shuttle2ExistDisabled);
                TestAccessor("RA25", robotAlarm.RA25_ShuttlePositionWaferMoveError);
                TestAccessor("RA26", robotAlarm.RA26_CHAExistDisabled);
                TestAccessor("RA27", robotAlarm.RA27_CHBExistDisabled);
                TestAccessor("RA28", robotAlarm.RA28_CHATriggerAlarm);
                TestAccessor("RA29", robotAlarm.RA29_CHBTriggerAlarm);
                TestAccessor("RA30", robotAlarm.RA30_CHARunBusy);

                // 测试RA31-RA40
                TestAccessor("RA31", robotAlarm.RA31_CHBRunBusy);
                TestAccessor("RA32", robotAlarm.RA32_CHARunProcessing);
                TestAccessor("RA33", robotAlarm.RA33_CHBRunProcessing);
                TestAccessor("RA34", robotAlarm.RA34_CoolingChamberTriggerAlarm);
                TestAccessor("RA35", robotAlarm.RA35_CoolingChamberNotIdle);
                TestAccessor("RA36", robotAlarm.RA36_CassetteSlotNotEmpty);
                TestAccessor("RA37", robotAlarm.RA37_CassetteSlotInconsistent);
                TestAccessor("RA38", robotAlarm.RA38_SmoothP1WaferLost);
                TestAccessor("RA39", robotAlarm.RA39_SmoothP2WaferLost);
                TestAccessor("RA40", robotAlarm.RA40_SmoothBothPaddleWaferLost);

                // 测试RA41-RA50
                TestAccessor("RA41", robotAlarm.RA41_NoseP1WaferLost);
                TestAccessor("RA42", robotAlarm.RA42_NoseP2WaferLost);
                TestAccessor("RA43", robotAlarm.RA43_NoseBothPaddleWaferLost);
                TestAccessor("RA44", robotAlarm.RA44_SmoothP1PutWaferFailure);
                TestAccessor("RA45", robotAlarm.RA45_SmoothP2PutWaferFailure);
                TestAccessor("RA46", robotAlarm.RA46_SmoothBothPaddlePutWaferFailure);
                TestAccessor("RA47", robotAlarm.RA47_NoseP1PutWaferFailure);
                TestAccessor("RA48", robotAlarm.RA48_NoseP2PutWaferFailure);
                TestAccessor("RA49", robotAlarm.RA49_NoseBothPutWaferFailure);
                TestAccessor("RA50", robotAlarm.RA50_SmoothP1WaferStatusInconsistent);

                // 测试RA51-RA60
                TestAccessor("RA51", robotAlarm.RA51_SmoothP2WaferStatusInconsistent);
                TestAccessor("RA52", robotAlarm.RA52_NoseP1WaferStatusInconsistent);
                TestAccessor("RA53", robotAlarm.RA53_NoseP2WaferStatusInconsistent);
                TestAccessor("RA54", robotAlarm.RA54_WaferInCHAPutReject);
                TestAccessor("RA55", robotAlarm.RA55_WaferInCHBPutReject);
                TestAccessor("RA56", robotAlarm.RA56_WaferInCTPutReject);
                TestAccessor("RA57", robotAlarm.RA57_WaferInCBPutReject);
                TestAccessor("RA58", robotAlarm.RA58_SmoothP1WaferStatusAbnormal);
                TestAccessor("RA59", robotAlarm.RA59_SmoothP2WaferStatusAbnormal);
                TestAccessor("RA60", robotAlarm.RA60_NoseP1WaferStatusAbnormal);

                // 测试RA61-RA67
                TestAccessor("RA61", robotAlarm.RA61_NoseP2WaferStatusAbnormal);
                TestAccessor("RA62", robotAlarm.RA62_SlotCannotGetWaferFromCassette);
                TestAccessor("RA63", robotAlarm.RA63_NoWaferInCHAGetReject);
                TestAccessor("RA64", robotAlarm.RA64_NoWaferInCHBGetReject);
                TestAccessor("RA65", robotAlarm.RA65_NoWaferInCTGetReject);
                TestAccessor("RA66", robotAlarm.RA66_NoWaferInCBGetReject);
                TestAccessor("RA67", robotAlarm.RA67_RobotMotionError);

                Console.WriteLine("所有Robot报警代码访问器测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试单个访问器
        /// </summary>
        /// <param name="code">报警代码</param>
        /// <param name="accessor">访问器</param>
        private static void TestAccessor(string code, AlarmPropertyAccessor accessor)
        {
            try
            {
                if (accessor != null)
                {
                    string content = accessor.Content ?? "未定义";
                    string chsContent = accessor.ChsContent ?? "未定义";
                    Console.WriteLine($"{code}: {content} | 中文: {chsContent}");
                }
                else
                {
                    Console.WriteLine($"{code}: 访问器为空");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{code}: 访问器测试失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 测试特定报警代码的访问器
        /// </summary>
        /// <param name="alarmCode">报警代码枚举</param>
        public static void TestSpecificAlarmCode(EnuRobotAlarmCodes alarmCode)
        {
            try
            {
                var interLock = SS200InterLockMain.Instance;
                var robotAlarm = interLock.AlarmCode.Robot;

                // 使用反射获取对应的访问器属性
                var propertyName = GetAccessorPropertyName(alarmCode);
                var robotAlarmType = robotAlarm.GetType();
                var property = robotAlarmType.GetProperty(propertyName);
                
                if (property != null)
                {
                    var accessor = property.GetValue(robotAlarm) as AlarmPropertyAccessor;
                    TestAccessor(alarmCode.ToString(), accessor);
                }
                else
                {
                    Console.WriteLine($"{alarmCode}: 未找到对应的访问器属性");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试{alarmCode}时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据报警代码枚举获取对应的访问器属性名
        /// </summary>
        /// <param name="alarmCode">报警代码枚举</param>
        /// <returns>访问器属性名</returns>
        private static string GetAccessorPropertyName(EnuRobotAlarmCodes alarmCode)
        {
            // 这里可以根据需要实现映射逻辑
            // 目前简单返回枚举名称，实际使用时可能需要更复杂的映射
            return alarmCode.ToString();
        }
    }
}
