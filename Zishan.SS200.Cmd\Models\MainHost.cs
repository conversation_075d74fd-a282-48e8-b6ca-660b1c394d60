﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models
{
    public class MainHost : BaseContainer
    {
        /// <summary>
        /// EAP通讯状态
        /// </summary>
        public EnuEAPCommunicationStatus EAPCommunicationStatus { get => _EAPCommunicationStatus; set => SetProperty(ref _EAPCommunicationStatus, value); }
        private EnuEAPCommunicationStatus _EAPCommunicationStatus;

        /// <summary>
        /// 工作模式
        /// </summary>
        public EnuMode Mode { get => _Mode; set => SetProperty(ref _Mode, value); }
        private EnuMode _Mode;

        /// <summary>
        /// 工艺剩余总时间
        /// </summary>
        public TimeSpan TimeRemain { get => _TimeRemain; set => SetProperty(ref _TimeRemain, value); }
        private TimeSpan _TimeRemain = TimeSpan.Zero;

        public MainHost()
        {
        }
    }
}