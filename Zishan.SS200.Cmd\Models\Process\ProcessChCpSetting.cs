﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

// using Zishan.Robot.Shared.AttributeExtend;
// using Zishan.Robot.Shared.Enums;

namespace Zishan.SS200.Cmd.Models.Process
{
    /// <summary>
    /// CH、CP腔体工艺流程参数
    /// </summary>
    public class ProcessChCpSetting : BindableBase
    {
        #region CH Process参数设置

        /// <summary>
        ///  CHA 工艺流程参数设置
        /// </summary>
        public ProcessChSetting ChaProcessSetting { get; set; }

        /// <summary>
        ///  CHB 工艺流程参数设置
        /// </summary>
        public ProcessChSetting ChbProcessSetting { get; set; }

        /// <summary>
        ///  CHC 工艺流程参数设置
        /// </summary>
        public ProcessChSetting ChcProcessSetting { get; set; }

        #endregion CH Process参数设置

        /// <summary>
        ///  CP 工艺流程参数设置
        /// </summary>
        public ProcessCpSetting CpProcessSetting { get; set; }
    }
}