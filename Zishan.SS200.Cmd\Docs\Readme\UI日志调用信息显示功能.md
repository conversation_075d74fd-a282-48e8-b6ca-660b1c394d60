# UI日志调用信息显示功能

## 功能概述

UI日志服务现在支持自动显示调用方法名和行号信息，帮助开发者更好地追踪日志来源和调试代码。此功能通过ini配置文件控制，可以根据需要启用或禁用。

## 配置方式

### 1. 配置文件设置

在 `Config.ini` 文件的 `[General]` 节中添加以下配置项：

```ini
[General]
;是否在UI日志中显示调用方法名和行号，默认：False
ShowCallerInfoInUILog = False
```

**配置值说明：**
- `True`: 启用调用信息显示，日志中会包含行号和方法名
- `False`: 禁用调用信息显示，只显示日志内容（默认）

### 2. 代码中的配置读取

配置项会在应用启动时自动读取到 `App.AppIniConfig.ShowCallerInfoInUILog` 属性中。

## 使用效果

### 启用调用信息显示时 (ShowCallerInfoInUILog = True)

```
开始晶圆传输 (Line: 156, Method: TransferWaferAsync)
    移动T轴到ChamberA位置 (Line: 234, Method: MoveTAxisToLocationAsync)
    ✅ T轴移动成功 (Line: 245, Method: MoveTAxisToLocationAsync)
    执行取片操作 (Line: 278, Method: GetWaferAsync)
    ✅ 取片操作完成 (Line: 289, Method: GetWaferAsync)
✅ 晶圆传输完成 (Line: 167, Method: TransferWaferAsync)
```

### 禁用调用信息显示时 (ShowCallerInfoInUILog = False)

```
开始晶圆传输
    移动T轴到ChamberA位置
    ✅ T轴移动成功
    执行取片操作
    ✅ 取片操作完成
✅ 晶圆传输完成
```

## 技术实现

### 1. 自动获取调用信息

使用C#的 `CallerLineNumber` 和 `CallerMemberName` 特性自动获取调用方的行号和方法名：

```csharp
public static void AddLog(string log, 
    [CallerLineNumber] int lineNumber = 0, 
    [CallerMemberName] string memberName = "")
```

### 2. 配置驱动的格式化

根据配置决定是否在日志消息中包含调用信息：

```csharp
private static string FormatLogMessage(string log, int lineNumber, string memberName)
{
    bool showCallerInfo = App.AppIniConfig?.ShowCallerInfoInUILog ?? false;
    
    if (showCallerInfo)
    {
        return $"{log} (Line: {lineNumber}, Method: {memberName})";
    }
    else
    {
        return log;
    }
}
```

## 支持的日志方法

所有UILogService的日志方法都支持调用信息显示：

- `AddLog()` - 普通日志
- `AddSuccessLog()` - 成功日志
- `AddErrorLog()` - 错误日志
- `AddWarningLog()` - 警告日志
- `AddInfoLog()` - 信息日志
- `AddLogAndIncreaseIndent()` - 带缩进增加的日志
- `DecreaseIndentAndAddLog()` - 带缩进减少的日志
- 以及其他所有日志方法

## 使用建议

### 开发阶段
- 建议启用调用信息显示 (`ShowCallerInfoInUILog = True`)
- 便于调试和追踪代码执行路径
- 快速定位问题代码位置

### 生产环境
- 建议禁用调用信息显示 (`ShowCallerInfoInUILog = False`)
- 保持日志简洁，提高可读性
- 减少日志文件大小

## 测试验证

可以运行测试方法验证功能：

```csharp
// 在HierarchicalLoggingTest类中
await HierarchicalLoggingTest.TestCallerInfoDisplay();
```

或运行完整测试套件：

```csharp
await HierarchicalLoggingTest.RunAllTests();
```

## 注意事项

1. **性能影响**: 启用调用信息显示会略微增加日志处理开销，但影响很小
2. **配置热更新**: 目前需要重启应用才能使配置更改生效
3. **线程安全**: 功能完全线程安全，可在多线程环境中使用
4. **向后兼容**: 现有代码无需修改，自动支持新功能

## 故障排除

### 配置不生效
- 检查Config.ini文件是否存在且格式正确
- 确认配置项在正确的节 `[General]` 中
- 重启应用使配置生效

### 调用信息显示异常
- 检查日志中是否有配置读取错误信息
- 确认App.AppIniConfig已正确初始化
- 查看应用启动日志中的配置加载信息
