# IO配置信息说明

本目录包含SS200系统的IO配置信息文件。

## McuDeviceCoilName.json

### 文件说明
该文件定义了各个子系统的IO接口详细信息，包括数字输入(DI)、数字输出(DO)和RS485通信接口。

### JSON结构

#### 顶层结构
```json
{
  "SubsystemName": {
    "DiNames": { ... },
    "DONames": { ... }
  }
}
```

#### IO设备信息结构
每个IO设备包含以下详细信息：

```json
{
  "1": {
    "name": "设备名称",
    "ioCode": "IO代码 (如: RDI1, SDI1, PDO1)",
    "mcuIo": "MCU IO映射 (如: SDI_26/RDI_26, PDI_9)",
    "ioType": "IO类型 (DI, DO)",
    "sensorType": "传感器类型 (如: optic sensor NPN, switch)",
    "controlType": "控制类型 (如: EV, position control)",
    "remark": "备注信息 (如: no wafer:0 wafer:1, enable:1)"
  }
}
```

### 支持的子系统

#### 1. Robot (机器人)
- **数字输入(DI)**: RDI1-RDI4
  - Paddle传感器 (光学传感器NPN)
  - Pin搜索传感器 (开关)

#### 2. Shuttle (Shuttle)
- **数字输入(DI)**: SDI1-SDI28
  - 晶圆盒门和巢传感器
  - 存在传感器
  - Shuttle位置传感器
  - 晶圆滑出传感器
  - 阀门传感器
- **数字输出(DO)**: SDO1-SDO25
  - 晶圆盒门和巢控制
  - 阀门控制
  - Shuttle运动控制
  - 指示灯和蜂鸣器

#### 3. ChamberA/ChamberB (工艺腔体)
- **数字输入(DI)**: PDI1-PDI15
  - CV/TV阀门传感器
  - 真空开关
  - 等离子传感器
  - 温控器
  - Slit Door和Lift Pin传感器
- **数字输出(DO)**: PDO1-PDO16
  - T/V阀门控制
  - CV和气体控制
  - Slit Door和Lift Pin控制
  - RF和加热器控制

### 传感器类型说明

#### 数字输入传感器类型
- **optic sensor NPN**: 光学传感器NPN类型
- **接近传感器NPN**: 接近检测传感器
- **switch**: 开关类型传感器

#### 数字输出控制类型
- **EV**: 电磁阀控制
- **position control**: 位置控制
- **position feedback**: 位置反馈

### 电平逻辑说明

#### 常见逻辑
- **到位检测**: 到位=0, 未到位=1
- **触发检测**: 触发=0, 未触发=1
- **接近检测**: 接近=1, 未接近=0
- **遮挡检测**: 遮挡=1, 未遮挡=0
- **真空检测**: 有真空=1, 无真空=0
- **等离子检测**: 等离子开=0, 等离子关=1
- **温度检测**: 过温=0, 温度正常=1
- **输出控制**: 使能=1, 禁用=0

### 代码使用示例

#### C# 代码示例
```csharp
// 加载配置
var options = new JsonSerializerOptions
{
    PropertyNameCaseInsensitive = true,
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
};

var config = JsonSerializer.Deserialize<Dictionary<string, DeviceCoilConfig>>(json, options);

// 获取Robot的DI1信息
if (config.TryGetValue("Robot", out var robotConfig))
{
    if (robotConfig.DiNames.TryGetValue("1", out var di1Info))
    {
        Console.WriteLine($"名称: {di1Info.Name}");
        Console.WriteLine($"IO代码: {di1Info.IoCode}");
        Console.WriteLine($"MCU IO: {di1Info.McuIo}");
        Console.WriteLine($"传感器类型: {di1Info.SensorType}");
        Console.WriteLine($"备注: {di1Info.Remark}");
        
        // 使用扩展方法获取完整描述
        string fullDesc = di1Info.GetFullDescription("Robot", 1, "DI");
    }
}
```

### 配置文件维护

#### 添加新设备
1. 在对应子系统下添加新的IO条目
2. 确保索引号连续且唯一
3. 填写完整的设备信息

#### 修改现有设备
1. 更新对应的设备信息字段
2. 确保IO代码和MCU IO映射正确
3. 更新备注信息以反映最新的电平逻辑

#### 验证配置
使用 `JsonConfigTest.RunAllTests()` 方法验证配置文件的正确性。

### 注意事项

1. **索引从1开始**: 配置文件中的索引从1开始，与程序中从0开始的地址需要进行转换
2. **大小写敏感**: JSON属性名称需要与C#类属性匹配
3. **必填字段**: `name` 字段是必填的，其他字段可以为空
4. **编码格式**: 文件必须使用UTF-8编码保存
5. **JSON语法**: 确保JSON语法正确，避免多余的逗号和括号不匹配

### 相关文件

- `S200McuCmdService.cs`: 主要的配置加载和使用代码
- `IoDeviceInfoExtensions.cs`: IO设备信息扩展方法
- `JsonConfigTest.cs`: 配置文件测试代码
