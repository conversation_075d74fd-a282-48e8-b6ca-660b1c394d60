﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Prism.Mvvm;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// Container 类表示一个容器。
    /// </summary>
    public class Container : BindableBase
    {
        #region 字段

        private EnuChamberName _ChamberName;

        //private readonly ObservableCollection<Wafer> _wafers = new ObservableCollection<Wafer>();

        //private int _CurWaferCount;
        //private int _LeftWaferCount;

        private WaferAction _LeftWaferAction;
        private WaferAction _RightWaferAction;
        //private bool _HaveWafer;
        //private bool _IsMove;
        //private bool _IsMoveChecked;
        //private bool _IsReplace;
        //private bool _IsReplaceChecked;
        //private bool _IsCreate;
        //private bool _IsCreateChecked;
        //private bool _IsDelete;
        //private bool _IsDeleteChecked;
        //private bool _CanRunCreateOrDeleteCmd;

        #endregion 字段

        #region 属性

        public string Title { get; set; } = "标题：";

        /// <summary>
        /// 未知面，针对非机械臂选Unknown
        /// </summary>
        public EnuArmFetchSide ArmFetchSide { get; set; }

        /// <summary>
        /// 腔标题
        /// </summary>
        public EnuChamberName ChamberName
        {
            get { return _ChamberName; }
            set { _ChamberName = value; RaisePropertyChanged(); }
        }

        /// <summary>
        /// 腔体总容量
        /// </summary>
        public int Capacity { get; set; }

        /// <summary>
        /// 加工处理时间，单位：ms
        /// </summary>
        public int ProcessingTime { get; set; }

        /// <summary>
        /// 检查腔体是否已经处理完成
        /// </summary>
        public bool IsProcessed { get; set; }

        ///// <summary>
        ///// 左边Wafe展示
        ///// </summary>
        ////public ObservableCollection<Wafer> Wafers
        ////{
        ////    get { return _wafers; }
        ////    //set { _wafers = value; CalWaferAction(); RaisePropertyChanged(); }
        ////}

        ///// <summary>
        ///// 容器当前Wafer数量
        ///// </summary>
        ////public int CurWaferCount
        ////{
        ////    get { return _CurWaferCount; }
        ////    set { _CurWaferCount = value; RaisePropertyChanged(); }
        ////}

        ///// <summary>
        ///// 容器剩余Wafer数量
        ///// </summary>
        //public int RemainWaferCount
        //{
        //    get { return _LeftWaferCount; }
        //    set { _LeftWaferCount = value; RaisePropertyChanged(); }
        //}

        /// <summary>
        /// 左边Wafer状态
        /// </summary>
        public WaferAction LeftWaferAction
        {
            get { return _LeftWaferAction; }
            set { _LeftWaferAction = value; RaisePropertyChanged(); }
        }

        /// <summary>
        /// 右边Wafer状态
        /// </summary>
        public WaferAction RightWaferAction
        {
            get { return _RightWaferAction; }
            set { _RightWaferAction = value; RaisePropertyChanged(); }
        }

        ///// <summary>
        ///// 容器状态信息，传递到前端
        ///// </summary>
        //public WaferAction LeftWaferAction
        //{
        //    get => _LeftWaferAction;
        //    set => _LeftWaferAction = value;
        //}

        ///// <summary>
        ///// 是否有Wafer
        ///// </summary>
        //public bool HaveWafer
        //{
        //    get { return _HaveWafer; }
        //    set
        //    {
        //        _HaveWafer = value;
        //        //JudgeByHaveWafer();
        //        RaisePropertyChanged();
        //    }
        //}

        ///// <summary>
        ///// Wafer是否可移动【前提有Wafer】
        ///// </summary>
        //public bool IsMove
        //{
        //    get { return _IsMove; }
        //    set { _IsMove = value; RaisePropertyChanged(); }
        //}

        //public bool IsMoveChecked
        //{
        //    get { return _IsMoveChecked; }
        //    set { _IsMoveChecked = value; UpdateTime = DateTime.Now; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// Wafer是否可替换【前提没有Wafer】
        ///// </summary>
        //public bool IsReplace
        //{
        //    get { return _IsReplace; }
        //    set { _IsReplace = value; RaisePropertyChanged(); }
        //}

        //public bool IsReplaceChecked
        //{
        //    get { return _IsReplaceChecked; }
        //    set { _IsReplaceChecked = value; UpdateTime = DateTime.Now; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// Wafer是否可替换【前提没有Wafer】
        ///// </summary>
        //public bool IsCreate
        //{
        //    get { return _IsCreate; }
        //    set { _IsCreate = value; RaisePropertyChanged(); }
        //}

        //public bool IsCreateChecked
        //{
        //    get { return _IsCreateChecked; }
        //    set { _IsCreateChecked = value; CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// Wafer是否可删除【前提有Wafer】
        ///// </summary>
        //public bool IsDelete
        //{
        //    get { return _IsDelete; }
        //    set { _IsDelete = value; RaisePropertyChanged(); }
        //}

        //public bool IsDeleteChecked
        //{
        //    get { return _IsDeleteChecked; }
        //    set { _IsDeleteChecked = value; CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// 是否可以运行创建或者删除操作
        ///// </summary>
        //public bool CanRunCreateOrDeleteCmd
        //{
        //    get { return _CanRunCreateOrDeleteCmd; }
        //    set { _CanRunCreateOrDeleteCmd = value; RaisePropertyChanged(); }
        //}

        //public DateTime UpdateTime { get; set; }

        #endregion 属性

        #region 构造函数

        /// <summary>
        /// 容器构造函数
        /// </summary>
        /// <param name="name">名称</param>
        /// <param name="processingTime">加工处理时间，单位：ms</param>
        /// <param name="capacity">Wafer容量</param>
        public Container(EnuChamberName name, int processingTime, int capacity = 2)
        {
            ChamberName = name;
            ProcessingTime = processingTime;

            _LeftWaferAction = new WaferAction(EnuChamberWaferSide.LeftWafers, capacity);
            _RightWaferAction = new WaferAction(EnuChamberWaferSide.RightWafers, capacity);
            Capacity = capacity * 2;
            CalWaferAction();
        }

        #endregion 构造函数

        #region 方法

        /// <summary>
        /// 计算关联逻辑判断
        /// </summary>
        public void CalWaferAction()
        {
            //var leftWafers = Wafers.Where(w => w.WaferNo % 2 == 1).ToList();
            //LeftWaferAction.Wafers.Clear();
            //LeftWaferAction.Wafers.AddRange(leftWafers);
            LeftWaferAction.CalWaferAction();

            //var rightWafers = Wafers.Where(w => w.WaferNo % 2 == 0).ToList();
            //RightWaferAction.Wafers.Clear();
            //RightWaferAction.Wafers.AddRange(rightWafers);
            RightWaferAction.CalWaferAction();

            //HaveWafer = this.Wafers.Any();
            //IsMove = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以移出
            //if (!IsMove) { IsMoveChecked = false; }
            //IsReplace = !IsFull();//剩余容量>=2多可以移入
            //if (!IsReplace) { IsReplaceChecked = false; }

            //IsCreate = !IsFull();//剩余容量>=2多可以创建
            //if (!IsCreate) { IsCreateChecked = false; }
            //IsDelete = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以删除
            //if (!IsDelete) { IsDeleteChecked = false; }

            //CurWaferCount = Wafers.Count / 2;

            //RemainWaferCount = Capacity - Wafers.Count;

            //CanRunCreateOrDeleteCmd = IsCreate || IsDelete;
            //CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked;

            /*
            _LeftWaferAction.HaveWafer = Wafers.Any();
            _LeftWaferAction.JudgeByHaveWafer();

            //_RightWaferAction.HaveWafer = _RightWafer.Any();
            //_RightWaferAction.JudgeByHaveWafer();

            RemainWaferCount = Wafers.Count;
            //RightWaferCount = _RightWafer.Count();
            */
        }

        ///// <summary>
        ///// 关联逻辑判断
        ///// </summary>

        //public void JudgeByHaveWafer()
        //{
        //    IsMove = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以移出
        //    if (!IsMove) { IsMoveChecked = false; }
        //    IsReplace = !IsFull();//剩余容量>=2多可以移入
        //    if (!IsReplace) { IsReplaceChecked = false; }

        //    IsCreate = !IsFull();//剩余容量>=2多可以创建
        //    if (!IsCreate) { IsCreateChecked = false; }
        //    IsDelete = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以删除
        //    if (!IsDelete) { IsDeleteChecked = false; }

        //    //CanRunCreateOrDeleteCmd = IsCreate || IsDelete;
        //    CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked;

        //    //_IsMove = HaveWafer;
        //    //_IsReplace = !HaveWafer;

        //    //IsCreate = !HaveWafer;
        //    //_IsDelete = HaveWafer;

        //    //_CanRunCreateOrDeleteCmd = IsCreate || IsDelete;
        //}

        /// <summary>
        /// 检查腔体是否满了
        /// </summary>
        /// <returns></returns>
        public bool IsFull()
        {
            var leftIsFull = _LeftWaferAction.IsFull();
            var RightIsFull = _RightWaferAction.IsFull();
            return leftIsFull || RightIsFull;
        }

        /// <summary>
        /// 检查腔体是否为空
        /// </summary>
        /// <returns></returns>
        public bool IsEmpty()
        {
            var isLeftWaferEmpty = _LeftWaferAction.IsEmpty();
            var isRightWaferEmpty = _RightWaferAction.IsEmpty();
            return isLeftWaferEmpty && isRightWaferEmpty;
        }

        /// <summary>
        /// 检查腔体是否有未完成的Wafer
        /// </summary>
        /// <returns></returns>
        public virtual bool HasUnfinishedWafer()
        {
            var leftHasUnfinishedWafer = _LeftWaferAction.HasUnfinishedWafer();
            var RightHasUnfinishedWafer = _RightWaferAction.HasUnfinishedWafer();
            return leftHasUnfinishedWafer || RightHasUnfinishedWafer;
        }

        /// <summary>
        /// 向腔体中添加晶圆,批量加，最多2个一起加
        /// </summary>
        /// <param name="wafers"></param>
        /// <param name="messageInfo"></param>
        public bool AddWafers(List<Wafer> wafers, out string messageInfo)
        {
            bool blResult = false;
            messageInfo = string.Empty;
            if (!IsFull())
            {
                LeftWaferAction.Wafers.AddRange(wafers.Where(w => w.ChamberWaferSide == EnuChamberWaferSide.LeftWafers).ToList());
                RightWaferAction.Wafers.AddRange(wafers.Where(w => w.ChamberWaferSide == EnuChamberWaferSide.RightWafers).ToList());
                CalWaferAction();
                IsProcessed = false;
                blResult = true;
            }
            else
            {
                messageInfo = $"{ChamberName}容量{Capacity}，当前数量{LeftWaferAction.Wafers.Count + RightWaferAction.Wafers.Count},容量已满了，无法放入!!!";
            }
            return blResult;
        }

        /// <summary>
        /// 从腔体中移出晶圆,有限制，最多2个一起减
        /// </summary>
        /// <returns></returns>

        public List<Wafer> RemoveWafers(int moveCount = 1, EnuWaferRemoveMode enuWaferRemoveMode = EnuWaferRemoveMode.Rear, int waferNo = -1)
        {
            var removewafers = new List<Wafer>(moveCount);
            for (int i = 0; i < moveCount && (LeftWaferAction.Wafers.Count > 0 || RightWaferAction.Wafers.Count > 0); i++)
            {
                Wafer leftWafer = null;
                Wafer rightWafer = null;
                int minLeftWaferNo = -1;
                int minRightWaferNo = -1;

                if (enuWaferRemoveMode != EnuWaferRemoveMode.specified)//当waferNo = 0时，按左边、右边最小值
                {
                    if (LeftWaferAction.Wafers.Count > 0)
                    {
                        minLeftWaferNo = LeftWaferAction.Wafers.Min(t => t.WaferNo);
                    }
                    if (RightWaferAction.Wafers.Count > 0)
                    {
                        minRightWaferNo = RightWaferAction.Wafers.Min(t => t.WaferNo);
                    }

                    if (minLeftWaferNo > -1 && minRightWaferNo >= 1 && minLeftWaferNo != minRightWaferNo)
                    {
                        waferNo = minLeftWaferNo < minRightWaferNo ? minLeftWaferNo : minRightWaferNo;
                    }
                    else
                    {
                        waferNo = minLeftWaferNo > minRightWaferNo ? minLeftWaferNo : minRightWaferNo;
                    }
                }
                else
                {
                    //更加WaferNo获取左边、右边Wafer对象
                }

                switch (enuWaferRemoveMode)
                {
                    case EnuWaferRemoveMode.Front:
                        if (waferNo == -1)//一致，正常成对处理
                        {
                            if (LeftWaferAction.Wafers.Count > 0)
                            {
                                leftWafer = LeftWaferAction.Wafers[0];
                                LeftWaferAction.Wafers.RemoveAt(0);
                            }
                            if (RightWaferAction.Wafers.Count > 0)
                            {
                                rightWafer = RightWaferAction.Wafers[0];
                                RightWaferAction.Wafers.RemoveAt(0);
                            }
                        }
                        else//不一致，按waferNo取
                        {
                            leftWafer = LeftWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);
                            rightWafer = RightWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);

                            if (leftWafer != null)
                            {
                                LeftWaferAction.Wafers.Remove(leftWafer);
                            }
                            if (rightWafer != null)
                            {
                                RightWaferAction.Wafers.Remove(rightWafer);
                            }
                        }
                        break;

                    case EnuWaferRemoveMode.Rear:
                        if (waferNo == -1)//一致，正常成对处理
                        {
                            if (LeftWaferAction.Wafers.Count > 0)
                            {
                                leftWafer = LeftWaferAction.Wafers[^1];
                                LeftWaferAction.Wafers.RemoveAt(LeftWaferAction.Wafers.Count - 1);
                            }
                            if (RightWaferAction.Wafers.Count > 0)
                            {
                                rightWafer = RightWaferAction.Wafers[^1];
                                RightWaferAction.Wafers.RemoveAt(RightWaferAction.Wafers.Count - 1);
                            }
                        }
                        else//不一致，按waferNo取
                        {
                            leftWafer = LeftWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);
                            rightWafer = RightWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);

                            if (leftWafer != null)
                            {
                                LeftWaferAction.Wafers.Remove(leftWafer);
                            }
                            if (rightWafer != null)
                            {
                                RightWaferAction.Wafers.Remove(rightWafer);
                            }
                        }
                        break;

                    case EnuWaferRemoveMode.specified:
                        leftWafer = LeftWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);
                        rightWafer = RightWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);

                        if (leftWafer != null)
                        {
                            LeftWaferAction.Wafers.Remove(leftWafer);
                        }
                        if (rightWafer != null)
                        {
                            RightWaferAction.Wafers.Remove(rightWafer);
                        }
                        break;
                }

                if (leftWafer != null)
                {
                    removewafers.Add(leftWafer);
                    if (leftWafer.WaferNo == Golbal.CurLeftWafersId)//判断删除末尾的才可以
                    {
                        Golbal.CurLeftWafersId--;
                    }
                }
                if (rightWafer != null)
                {
                    removewafers.Add(rightWafer);
                    if (rightWafer.WaferNo == Golbal.CurRightWafersId)//判断删除末尾的才可以
                    {
                        Golbal.CurRightWafersId--;
                    }
                }
            }
            CalWaferAction();
            return removewafers;
        }

        /// <summary>
        /// 处理晶圆（异步版本）
        /// ✅ 优化报告修复：使用异步延迟，不阻塞UI线程
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        public virtual async Task<bool> ProcessWafersAsync(CancellationToken cancellationToken = default)
        {
            if (ProcessingTime > 0)
            {
                Console.WriteLine($"正在等待{ChamberName}处理......");
                await Task.Delay(ProcessingTime, cancellationToken);  // 异步延迟，不阻塞UI线程
                IsProcessed = true;
                Console.WriteLine($"完成处理{ChamberName}");
            }
            return IsProcessed;
        }

        /// <summary>
        /// 处理晶圆（同步版本 - 保持向后兼容）
        /// ⚠️ 已过时：此方法会阻塞UI线程，请使用ProcessWafersAsync()异步版本
        /// </summary>
        /// <returns></returns>
        [Obsolete("此方法会阻塞UI线程，请使用ProcessWafersAsync()异步版本")]
        public virtual bool ProcessWafers()
        {
            if (ProcessingTime > 0)
            {
                Console.WriteLine($"正在等待{ChamberName}处理......");

                Thread.Sleep(ProcessingTime);  // 模拟加工时间
                IsProcessed = true;

                Console.WriteLine($"完成处理{ChamberName}");
            }

            return IsProcessed;
        }

        /// <summary>
        ///
        /// </summary>
        public virtual void ClearWafer()
        {
            LeftWaferAction.Wafers.Clear();
            RightWaferAction.Wafers.Clear();
            CalWaferAction();
        }

        /// <summary>
        /// 腔信息
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"{ChamberName}【Left={LeftWaferAction.ToString()}；Right={RightWaferAction.ToString()}】";
        }

        #endregion 方法
    }
}