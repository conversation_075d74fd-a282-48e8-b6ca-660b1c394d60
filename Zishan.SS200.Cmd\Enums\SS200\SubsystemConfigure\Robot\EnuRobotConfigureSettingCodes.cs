﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot
{
    /// <summary>
    /// Robot 设置参数设置代码枚举类型
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuRobotConfigureSettingCodes
    {
        /// <summary>
        /// robot rotate speed
        /// </summary>
        [Description("robot rotate speed")]
        RPS1 = 0,

        /// <summary>
        /// robot extend speed
        /// </summary>
        [Description("robot extend speed")]
        RPS2 = 1,

        /// <summary>
        /// robot up down speed
        /// </summary>
        [Description("robot up down speed")]
        RPS3 = 2,

        /// <summary>
        /// robot move slowly
        /// </summary>
        [Description("robot move slowly")]
        RPS4 = 3,

        /// <summary>
        /// T-axis move speed slowly
        /// </summary>
        [Description("T-axis move speed slowly")]
        RPS5 = 4,

        /// <summary>
        /// R-axis move speed slowly
        /// </summary>
        [Description("R-axis move speed slowly")]
        RPS6 = 5,

        /// <summary>
        /// Z-axis move speed slowly
        /// </summary>
        [Description("Z-axis move speed slowly")]
        RPS7 = 6,

        /// <summary>
        /// robot rotate max time
        /// </summary>
        [Description("robot rotate max time")]
        RPS8 = 7,

        /// <summary>
        /// robot extend max time
        /// </summary>
        [Description("robot extend max time")]
        RPS9 = 8,

        /// <summary>
        /// robot up down max time
        /// </summary>
        [Description("robot up down max time")]
        RPS10 = 9,

        /// <summary>
        /// Deviation step for T-axis home
        /// </summary>
        [Description("Deviation step for T-axis home")]
        RPS11 = 10,

        /// <summary>
        /// robot home T-axis max time
        /// </summary>
        [Description("robot home T-axis max time")]
        RPS12 = 11,

        /// <summary>
        /// robot home R-axis max time
        /// </summary>
        [Description("robot home R-axis max time")]
        RPS13 = 12,

        /// <summary>
        /// robot home Z-axis max time
        /// </summary>
        [Description("robot home Z-axis max time")]
        RPS14 = 13,

        /// <summary>
        /// T-axis deviation for R-axis zero
        /// </summary>
        [Description("T-axis deviation for R-axis zero")]
        RPS15 = 14,

        /// <summary>
        /// Z-axis step deviation for loadlock vacuum
        /// </summary>
        [Description("Z-axis step deviation for loadlock vacuum")]
        RPS16 = 15,

        /// <summary>
        /// Z-axis step deviation for shuttle no vacuum
        /// </summary>
        [Description("Z-axis step deviation for shuttle no vacuum")]
        RPS17 = 16,

        /// <summary>
        /// zpin wafer 4"/5"
        /// </summary>
        [Description("zpin wafer 4\"/5\"")]
        RPS18 = 17,

        /// <summary>
        /// zpin wafer 6"
        /// </summary>
        [Description("zpin wafer 6\"")]
        RPS19 = 18,

        /// <summary>
        /// zpin wafer 8"
        /// </summary>
        [Description("zpin wafer 8\"")]
        RPS20 = 19,

        /// <summary>
        /// Z-axis get/put wafer delta cassette 4"
        /// </summary>
        [Description("Z-axis get/put wafer delta cassette 4\"")]
        RPS21 = 20,

        /// <summary>
        /// Z-axis get/put wafer delta cassette 6"
        /// </summary>
        [Description("Z-axis get/put wafer delta cassette 6\"")]
        RPS22 = 21,

        /// <summary>
        /// Z-axis get/put wafer delta cassette 8"
        /// </summary>
        [Description("Z-axis get/put wafer delta cassette 8\"")]
        RPS23 = 22,

        /// <summary>
        /// Z-axis get/put wafer delta cooling chamber
        /// </summary>
        [Description("Z-axis get/put wafer delta cooling chamber")]
        RPS24 = 23,

        /// <summary>
        /// max delta value for pin search
        /// </summary>
        [Description("max delta value for pin search")]
        RPS25 = 24,

        /// <summary>
        /// wafer actually status check
        /// </summary>
        [Description("wafer actually status check")]
        RPS26 = 25,

        /// <summary>
        /// Z-axis height for robot rotation
        /// </summary>
        [Description("Z-axis height for robot rotation")]
        RPS27 = 26,

        /// <summary>
        /// lowest step for pin search
        /// </summary>
        [Description("lowest step for pin search")]
        RPS28 = 27,

        /// <summary>
        /// chambere pressure review
        /// </summary>
        [Description("chambere pressure review")]
        RPS29 = 28,

        /// <summary>
        /// Z-axis height for pin search
        /// </summary>
        [Description("Z-axis height for pin search")]
        RPS30 = 29
    }
}