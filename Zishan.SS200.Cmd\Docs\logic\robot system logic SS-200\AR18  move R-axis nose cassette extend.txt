﻿AR18 
move R-axis nose cassette extend
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			shuttle position status review
SSD1~SSD7 or others position
				SSD1/SSD3
					shuttle 2 exist status review
SS43 SS44
						SS43
							slide out sensor installation review
SPS11
								SPS11=Y
									slide out sensor status reiew
										DI19=0 DI20=0
											Robot T-axis position review
RS8 or others position
												RS8
													AR20-RP17
														command done
												others position
													RA4 ALARM
														confirm
															AR20-RP17
																command done
														cancel
															command cancel
										DI19=0 DI20=1
											RA20 ALARM
										DI19=1 DI20=0
											RA19 ALARM
										DI19=1 DI20=1
											RA21 ALARM
								SPS11=N
									Robot T-axis position review
RS8 or others position
										RS8
											AR20-RP17
												command done
										others position
											RA4 ALARM
												confirm
													AR20-RP17
														command done
												cancel
													command cancel
						SS44
							RA24 ALARM
				SSD2/SSD4
					shuttle 1 exist status review
SS45 SS46
						SS45
							slide out sensor installation review
SPS11
								SPS11=Y
									slide out sensor status reiew
										DI19=0 DI20=0
											Robot T-axis position review
RS8 or others position
												RS8
													AR20-RP17
														command done
												others position
													RA4 ALARM
														confirm
															AR20-RP17
																command done
														cancel
															command cancel
										DI19=0 DI20=1
											RA20 ALARM
										DI19=1 DI20=0
											RA19 ALARM
										DI19=1 DI20=1
											RA21 ALARM
								SPS11=N
									Robot T-axis position review
RS8 or others position
										RS8
											AR20-RP17
												command done
										others position
											RA4 ALARM
												confirm
													AR20-RP17
														command done
												cancel
													command cancel
						SS46
							RA23 ALARM
				SSD5/SSD6/SSD7 or others status
					RA25 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM