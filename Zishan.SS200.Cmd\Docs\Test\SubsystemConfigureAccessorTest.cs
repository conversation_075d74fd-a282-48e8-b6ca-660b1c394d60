using System;
using System.Diagnostics;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// 子系统配置访问器测试类
    /// 用于验证完善的配置访问器功能是否正常工作
    /// </summary>
    public class SubsystemConfigureAccessorTest
    {
        /// <summary>
        /// 测试Robot配置访问器
        /// </summary>
        public static bool TestRobotConfigureAccessor()
        {
            try
            {
                Console.WriteLine("测试Robot配置访问器...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 测试配置设置参数访问
                var rotateSpeed = robotConfig.RPS1_RobotRotateSpeed;
                var extendSpeed = robotConfig.RPS2_RobotExtendSpeed;
                var upDownSpeed = robotConfig.RPS3_RobotUpDownSpeed;
                var pinSearchHeight = robotConfig.RPS30_ZAxisHeightForPinSearch;

                Console.WriteLine($"  RPS1 旋转速度访问器: {(rotateSpeed != null ? "✓" : "✗")}");
                Console.WriteLine($"  RPS2 伸展速度访问器: {(extendSpeed != null ? "✓" : "✗")}");
                Console.WriteLine($"  RPS3 上下速度访问器: {(upDownSpeed != null ? "✓" : "✗")}");
                Console.WriteLine($"  RPS30 插销搜索Z轴高度访问器: {(pinSearchHeight != null ? "✓" : "✗")}");
                
                // 测试位置参数访问
                var tAxisPos = robotConfig.RP1_TAxisSmoothToCHA;
                var rAxisPos = robotConfig.RP2_TAxisSmoothToCHB;

                Console.WriteLine($"  RP1 T轴smooth到CHA访问器: {(tAxisPos != null ? "✓" : "✗")}");
                Console.WriteLine($"  RP2 T轴smooth到CHB访问器: {(rAxisPos != null ? "✓" : "✗")}");
                
                Console.WriteLine("Robot配置访问器测试完成");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Robot配置访问器测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试Chamber配置访问器
        /// </summary>
        public static bool TestChamberConfigureAccessor()
        {
            try
            {
                Console.WriteLine("测试Chamber配置访问器...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var chamberConfig = ss200Main.SubsystemConfigure.ChamberA;
                
                // 测试基本配置参数访问
                var slitDoorMinTime = chamberConfig.PPS1_SlitDoorMotionMinTime;
                var slitDoorMaxTime = chamberConfig.PPS2_SlitDoorMotionMaxTime;
                var liftPinMinTime = chamberConfig.PPS3_LiftPinMotionMinTime;
                
                Console.WriteLine($"  PPS1 狭缝门最小时间访问器: {(slitDoorMinTime != null ? "✓" : "✗")}");
                Console.WriteLine($"  PPS2 狭缝门最大时间访问器: {(slitDoorMaxTime != null ? "✓" : "✗")}");
                Console.WriteLine($"  PPS3 升降销最小时间访问器: {(liftPinMinTime != null ? "✓" : "✗")}");
                
                // 测试压力和功率参数
                var vacuumPressure = chamberConfig.PPS9_ChamberVacuumPressureSetpoint;
                var rf1Power = chamberConfig.PPS19_Rf1PowerSetpoint;
                
                Console.WriteLine($"  PPS9 真空压力设定访问器: {(vacuumPressure != null ? "✓" : "✗")}");
                Console.WriteLine($"  PPS19 RF1功率设定访问器: {(rf1Power != null ? "✓" : "✗")}");
                
                Console.WriteLine("Chamber配置访问器测试完成");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Chamber配置访问器测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试Shuttle配置访问器
        /// </summary>
        public static bool TestShuttleConfigureAccessor()
        {
            try
            {
                Console.WriteLine("测试Shuttle配置访问器...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var shuttleConfig = ss200Main.SubsystemConfigure.Shuttle;
                
                // 测试基本配置参数访问
                var nestMinTime = shuttleConfig.SPS1_CassetteNestExtendRetractMinTime;
                var nestMaxTime = shuttleConfig.SPS2_CassetteNestExtendRetractMaxTime;
                var shuttleUpMinTime = shuttleConfig.SPS3_ShuttleUpDownMinTime;
                
                Console.WriteLine($"  SPS1 卡匣巢最小时间访问器: {(nestMinTime != null ? "✓" : "✗")}");
                Console.WriteLine($"  SPS2 卡匣巢最大时间访问器: {(nestMaxTime != null ? "✓" : "✗")}");
                Console.WriteLine($"  SPS3 Shuttle上下最小时间访问器: {(shuttleUpMinTime != null ? "✓" : "✗")}");
                
                // 测试压力参数
                var transferPressure = shuttleConfig.SPS12_ShuttleTransferPressure;
                var deltaPressure = shuttleConfig.SPS13_DeltaPressureForShuttleUpDown;
                
                Console.WriteLine($"  SPS12 传输压力访问器: {(transferPressure != null ? "✓" : "✗")}");
                Console.WriteLine($"  SPS13 压差访问器: {(deltaPressure != null ? "✓" : "✗")}");
                
                Console.WriteLine("Shuttle配置访问器测试完成");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Shuttle配置访问器测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试MainSystem配置访问器
        /// </summary>
        public static bool TestMainSystemConfigureAccessor()
        {
            try
            {
                Console.WriteLine("测试MainSystem配置访问器...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var mainSystemConfig = ss200Main.SubsystemConfigure.MainSystem;
                
                // 测试基本配置参数访问
                var waferSize1 = mainSystemConfig.SSC1_Shuttle1WaferSize;
                var waferSize2 = mainSystemConfig.SSC2_Shuttle2WaferSize;
                var chamberLocation = mainSystemConfig.SSC3_ChamberLocation;
                
                Console.WriteLine($"  SSC1 Shuttle1晶圆尺寸访问器: {(waferSize1 != null ? "✓" : "✗")}");
                Console.WriteLine($"  SSC2 Shuttle2晶圆尺寸访问器: {(waferSize2 != null ? "✓" : "✗")}");
                Console.WriteLine($"  SSC3 腔室位置访问器: {(chamberLocation != null ? "✓" : "✗")}");
                
                // 测试其他参数
                var cassetteType = mainSystemConfig.SSC6_CassetteNestType;
                var priorityTime = mainSystemConfig.SSC8_PriorityEffective;
                var coolingTemp = mainSystemConfig.SSC11_TemperatureForCoolingChamber;
                
                Console.WriteLine($"  SSC6 卡匣巢类型访问器: {(cassetteType != null ? "✓" : "✗")}");
                Console.WriteLine($"  SSC8 优先级时间访问器: {(priorityTime != null ? "✓" : "✗")}");
                Console.WriteLine($"  SSC11 冷却温度访问器: {(coolingTemp != null ? "✓" : "✗")}");
                
                Console.WriteLine("MainSystem配置访问器测试完成");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MainSystem配置访问器测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试配置访问器的性能
        /// </summary>
        public static bool TestConfigureAccessorPerformance()
        {
            try
            {
                Console.WriteLine("测试配置访问器性能...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var stopwatch = Stopwatch.StartNew();
                
                // 测试多次访问的性能（应该利用缓存）
                for (int i = 0; i < 1000; i++)
                {
                    var robotConfig = ss200Main.SubsystemConfigure.Robot;
                    var rotateSpeed = robotConfig.RPS1_RobotRotateSpeed;
                    var chamberConfig = ss200Main.SubsystemConfigure.ChamberA;
                    var slitDoorTime = chamberConfig.PPS1_SlitDoorMotionMinTime;
                }
                
                stopwatch.Stop();
                Console.WriteLine($"  1000次访问耗时: {stopwatch.ElapsedMilliseconds}ms");
                Console.WriteLine($"  平均每次访问: {stopwatch.ElapsedMilliseconds / 1000.0:F3}ms");
                
                bool performanceOk = stopwatch.ElapsedMilliseconds < 1000; // 应该在1秒内完成
                Console.WriteLine($"  性能测试: {(performanceOk ? "✓" : "✗")}");
                
                Console.WriteLine("配置访问器性能测试完成");
                return performanceOk;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"配置访问器性能测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 子系统配置访问器测试开始 ===\n");
            
            bool robotTest = TestRobotConfigureAccessor();
            Console.WriteLine();
            
            bool chamberTest = TestChamberConfigureAccessor();
            Console.WriteLine();
            
            bool shuttleTest = TestShuttleConfigureAccessor();
            Console.WriteLine();
            
            bool mainSystemTest = TestMainSystemConfigureAccessor();
            Console.WriteLine();
            
            bool performanceTest = TestConfigureAccessorPerformance();
            Console.WriteLine();
            
            // 汇总测试结果
            int passedTests = 0;
            int totalTests = 5;
            
            if (robotTest) passedTests++;
            if (chamberTest) passedTests++;
            if (shuttleTest) passedTests++;
            if (mainSystemTest) passedTests++;
            if (performanceTest) passedTests++;
            
            Console.WriteLine("=== 测试结果汇总 ===");
            Console.WriteLine($"通过测试: {passedTests}/{totalTests}");
            Console.WriteLine($"测试状态: {(passedTests == totalTests ? "全部通过 ✓" : "部分失败 ✗")}");
            
            if (passedTests == totalTests)
            {
                Console.WriteLine("\n🎉 所有配置访问器测试通过！配置访问器已成功完善。");
            }
            else
            {
                Console.WriteLine("\n⚠️ 部分测试失败，请检查配置文件和提供者实现。");
            }
        }
    }
}
