using System;
using log4net;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// SS200InterLockMain RTZ轴位置集成测试
    /// 验证RTZAxisPosition功能是否正确集成到SS200InterLockMain中
    /// </summary>
    public class SS200InterLockMainRTZIntegrationTest
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(SS200InterLockMainRTZIntegrationTest));

        /// <summary>
        /// 测试RTZAxisPosition属性是否正确添加到SS200InterLockMain
        /// </summary>
        public static void TestRTZAxisPositionIntegration()
        {
            try
            {
                _logger.Info("=== SS200InterLockMain RTZ轴位置集成测试 ===");

                // 获取SS200InterLockMain实例
                var interlock = SS200InterLockMain.Instance;
                _logger.Info("✓ SS200InterLockMain实例获取成功");

                // 验证RTZAxisPosition属性是否存在
                var rtzPosition = interlock.RTZAxisPosition;
                if (rtzPosition != null)
                {
                    _logger.Info("✓ RTZAxisPosition属性存在且不为null");
                }
                else
                {
                    _logger.Error("✗ RTZAxisPosition属性为null");
                    return;
                }

                // 验证RTZAxisPosition的类型
                string typeName = rtzPosition.GetType().Name;
                if (typeName == "RTZAxisPositionAccessor")
                {
                    _logger.Info($"✓ RTZAxisPosition类型正确: {typeName}");
                }
                else
                {
                    _logger.Error($"✗ RTZAxisPosition类型错误: {typeName}");
                    return;
                }

                _logger.Info("=== RTZ轴位置集成测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"RTZ轴位置集成测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试RTZAxisPosition的基本功能
        /// </summary>
        public static void TestRTZAxisPositionBasicFunctionality()
        {
            try
            {
                _logger.Info("=== RTZAxisPosition基本功能测试 ===");

                var interlock = SS200InterLockMain.Instance;
                var rtzPosition = interlock.RTZAxisPosition;

                // 测试数据有效性检查
                bool isDataValid = rtzPosition.IsRTZPositionDataValid;
                _logger.Info($"数据有效性检查: {isDataValid}");

                if (!isDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过功能测试");
                    return;
                }

                // 测试基本位置访问
                _logger.Info("--- 测试基本位置访问 ---");
                try
                {
                    int tAxisStep = rtzPosition.CurrentTAxisStep;
                    int rAxisStep = rtzPosition.CurrentRAxisStep;
                    int zAxisStep = rtzPosition.CurrentZAxisStep;

                    _logger.Info($"✓ T轴步进值获取成功: {tAxisStep}");
                    _logger.Info($"✓ R轴步进值获取成功: {rAxisStep}");
                    _logger.Info($"✓ Z轴步进值获取成功: {zAxisStep}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 基本位置访问失败: {ex.Message}");
                }

                // 测试物理单位访问
                _logger.Info("--- 测试物理单位访问 ---");
                try
                {
                    double tAxisDegree = rtzPosition.CurrentTAxisDegree;
                    double rAxisLength = rtzPosition.CurrentRAxisLength;
                    double zAxisHeight = rtzPosition.CurrentZAxisHeight;

                    _logger.Info($"✓ T轴角度获取成功: {tAxisDegree:F2}°");
                    _logger.Info($"✓ R轴长度获取成功: {rAxisLength:F2}mm");
                    _logger.Info($"✓ Z轴高度获取成功: {zAxisHeight:F2}mm");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 物理单位访问失败: {ex.Message}");
                }

                // 测试组合访问
                _logger.Info("--- 测试组合访问 ---");
                try
                {
                    var (t, r, z) = rtzPosition.GetCurrentRTZSteps();
                    _logger.Info($"✓ RTZ轴步进值组合获取成功: T={t}, R={r}, Z={z}");

                    var (tDeg, rLen, zHeight) = rtzPosition.GetCurrentRTZPhysicalValues();
                    _logger.Info($"✓ RTZ轴物理值组合获取成功: T={tDeg:F2}°, R={rLen:F2}mm, Z={zHeight:F2}mm");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 组合访问失败: {ex.Message}");
                }

                // 测试格式化显示
                _logger.Info("--- 测试格式化显示 ---");
                try
                {
                    string displayText = rtzPosition.GetRTZPositionDisplayText();
                    string simpleText = rtzPosition.GetRTZPositionSimpleText();

                    _logger.Info($"✓ 详细显示文本: {displayText}");
                    _logger.Info($"✓ 简化显示文本: {simpleText}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 格式化显示失败: {ex.Message}");
                }

                // 测试安全检查
                _logger.Info("--- 测试安全检查 ---");
                try
                {
                    bool allSafe = rtzPosition.AreAllAxesInSafeRange;
                    bool tSafe = rtzPosition.IsAxisPositionInSafeRange("T");
                    bool rSafe = rtzPosition.IsAxisPositionInSafeRange("R");
                    bool zSafe = rtzPosition.IsAxisPositionInSafeRange("Z");

                    _logger.Info($"✓ 整体安全状态: {allSafe}");
                    _logger.Info($"✓ T轴安全状态: {tSafe}");
                    _logger.Info($"✓ R轴安全状态: {rSafe}");
                    _logger.Info($"✓ Z轴安全状态: {zSafe}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 安全检查失败: {ex.Message}");
                }

                _logger.Info("=== RTZAxisPosition基本功能测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"RTZAxisPosition基本功能测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试统一访问架构
        /// </summary>
        public static void TestUnifiedAccessArchitecture()
        {
            try
            {
                _logger.Info("=== 统一访问架构测试 ===");

                var interlock = SS200InterLockMain.Instance;

                // 验证五大访问器都存在
                _logger.Info("--- 验证五大访问器 ---");
                
                bool ioInterfaceExists = interlock.IOInterface != null;
                bool alarmCodeExists = interlock.AlarmCode != null;
                bool subsystemConfigureExists = interlock.SubsystemConfigure != null;
                bool subsystemStatusExists = interlock.SubsystemStatus != null;
                bool rtzAxisPositionExists = interlock.RTZAxisPosition != null;

                _logger.Info($"✓ IOInterface存在: {ioInterfaceExists}");
                _logger.Info($"✓ AlarmCode存在: {alarmCodeExists}");
                _logger.Info($"✓ SubsystemConfigure存在: {subsystemConfigureExists}");
                _logger.Info($"✓ SubsystemStatus存在: {subsystemStatusExists}");
                _logger.Info($"✓ RTZAxisPosition存在: {rtzAxisPositionExists}");

                bool allAccessorsExist = ioInterfaceExists && alarmCodeExists && 
                                       subsystemConfigureExists && subsystemStatusExists && 
                                       rtzAxisPositionExists;

                if (allAccessorsExist)
                {
                    _logger.Info("✓ 五大访问器架构完整");
                }
                else
                {
                    _logger.Error("✗ 访问器架构不完整");
                }

                // 验证访问模式一致性
                _logger.Info("--- 验证访问模式一致性 ---");
                
                // 所有访问器都应该通过SS200InterLockMain.Instance访问
                string accessPattern = "SS200InterLockMain.Instance.RTZAxisPosition";
                _logger.Info($"✓ RTZ轴位置访问模式: {accessPattern}");

                _logger.Info("=== 统一访问架构测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"统一访问架构测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试与UiViewModel的集成
        /// </summary>
        public static void TestUiViewModelIntegration()
        {
            try
            {
                _logger.Info("=== UiViewModel集成测试 ===");

                // 验证UiViewModel是否能正确访问RTZ轴位置
                _logger.Info("验证UiViewModel访问模式:");
                _logger.Info("代码示例: var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;");
                _logger.Info("属性访问: int tAxisStep = rtzPosition.CurrentTAxisStep;");
                _logger.Info("方法调用: string displayText = rtzPosition.GetRTZPositionDisplayText();");

                // 模拟UiViewModel的访问方式
                var interlock = SS200InterLockMain.Instance;
                var rtzPosition = interlock.RTZAxisPosition;

                if (rtzPosition.IsRTZPositionDataValid)
                {
                    // 模拟属性绑定
                    int tAxisStep = rtzPosition.CurrentTAxisStep;
                    double tAxisDegree = rtzPosition.CurrentTAxisDegree;
                    bool isDataValid = rtzPosition.IsRTZPositionDataValid;
                    bool isAllSafe = rtzPosition.AreAllAxesInSafeRange;

                    _logger.Info($"✓ 模拟属性绑定成功: TAxisStep={tAxisStep}, TAxisDegree={tAxisDegree:F2}°");
                    _logger.Info($"✓ 模拟状态绑定成功: IsDataValid={isDataValid}, IsAllSafe={isAllSafe}");

                    // 模拟方法调用
                    string displayText = rtzPosition.GetRTZPositionDisplayText();
                    string simpleText = rtzPosition.GetRTZPositionSimpleText();

                    _logger.Info($"✓ 模拟方法调用成功: DisplayText={displayText}");
                    _logger.Info($"✓ 模拟方法调用成功: SimpleText={simpleText}");
                }
                else
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过UiViewModel集成测试");
                }

                _logger.Info("=== UiViewModel集成测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"UiViewModel集成测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 运行所有集成测试
        /// </summary>
        public static void RunAllIntegrationTests()
        {
            _logger.Info("开始运行SS200InterLockMain RTZ轴位置所有集成测试...");

            TestRTZAxisPositionIntegration();
            Console.WriteLine();

            TestRTZAxisPositionBasicFunctionality();
            Console.WriteLine();

            TestUnifiedAccessArchitecture();
            Console.WriteLine();

            TestUiViewModelIntegration();

            _logger.Info("所有SS200InterLockMain RTZ轴位置集成测试运行完成");
        }
    }
}
