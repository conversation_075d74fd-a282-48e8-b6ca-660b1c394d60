﻿using System.Collections.Generic;
using <PERSON><PERSON>an.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// 冷却腔体，继承自 Chamber 类，用于冷却 Wafer。
    /// </summary>
    public class CoolingChamber : BContainer
    {
        public CoolingChamber(EnuChamberName name, int processTime = 1000, int capacity = 1) : base(name, processTime, capacity)
        {
        }

        //冷却晶圆
        public List<Wafer> CoolWafers()
        {
            var cooledWafers = new List<Wafer>();
            if (LeftWaferAction.Wafers.Count > 0 || RightWaferAction.Wafers.Count > 0)
            {
                ProcessWafers();
                cooledWafers = new List<Wafer>(LeftWaferAction.Wafers);
                cooledWafers.AddRange(RightWaferAction.Wafers);
                LeftWaferAction.Wafers.Clear();
                RightWaferAction.Wafers.Clear();
                CalWaferAction();
            }
            return cooledWafers;
        }
    }
}