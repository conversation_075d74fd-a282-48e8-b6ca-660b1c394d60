﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

// using Prism.Navigation.Regions;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.ViewModels;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.ViewModels.DesignViewModels
{
    /// <summary>
    /// 设计时视图模型，用于XAML设计器中显示模拟数据
    /// </summary>
    public class MainViewDesignViewModel : MainWindowViewModel
    {
        private static MainViewDesignViewModel _Instance;
        public static MainViewDesignViewModel Instance => _Instance ??= new();

        public MainViewDesignViewModel() : base()
        {
            // 设计时数据，覆盖基类的设置
            this.Title = "设计时 - <PERSON>ishan.SS200.Cmd";

            // 设置命令相关的模拟数据
            //this.DynamicParameters = "0x0001,0x0002,0x0003";

            // 设置设备状态的模拟数据
            var mockDeviceStatuses = new Dictionary<EnuMcuDeviceType, DeviceStatus>
            {
                { EnuMcuDeviceType.Shuttle, DeviceStatus.Connected },
                { EnuMcuDeviceType.Robot, DeviceStatus.Disconnected },
                { EnuMcuDeviceType.ChamberA, DeviceStatus.Busy },
                { EnuMcuDeviceType.ChamberB, DeviceStatus.Error }
            };
            this.DeviceStatuses = mockDeviceStatuses;

            // 添加日志的模拟数据
            var logItems = new ObservableCollection<LogInfo>
            {
                new LogInfo { Index = 1, Message = "设计时示例日志1" },
                new LogInfo { Index = 2, Message = "设计时示例日志2" },
                new LogInfo { Index = 3, Message = "设计时示例日志3" }
            };
        }

        // public MainViewDesignViewModel() : base(
        //     new ModbusClientService(),
        //     new S200McuCmdService(new ModbusClientService()),
        //     new RegionManager())
        // {
        //     this.Title = "设计时显示内容";
        // }
    }
}