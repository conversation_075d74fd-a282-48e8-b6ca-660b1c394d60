<?xml version="1.0"?>
<configuration>
	<log4net>
		<root>
			<!--日志等级 OFF > FATAL > ERROR > WARN > INFO > DEBUG > ALL-->
			<level value="ALL"/>
			<!--<appender-ref ref="RollingFileAppender"/>-->
			<appender-ref ref="CustomRollingFileAppender"/>
			<!--<appender-ref ref="ConsoleAppender"/>-->
		</root>


		<!--自定义RollingFileAppender 实现自动删除过期日志-->
		<appender name="CustomRollingFileAppender" type="Zishan.Ocr.Models.CustomRollingFileAppender">
			<!--日志的存储路径-->
			<file value="log/" />
			<!--是否向文件中追加日志-->
			<appendToFile value="true" />
			<!--滚动模式 复合-->
			<rollingStyle value="Composite" />
			<!--计数类型为1，2，3…-->
			<!--<param name="CountDirection" value="1"/>-->
			<!--日志文件名是否是固定不变的 不变则只写到一个文件中-->
			<staticLogFileName value="false" />
			<!--日志文件名格式为:yyyy-MM-dd.log-->
			<datePattern value="yyyyMMdd'.log'" />
			
			
			<!--最多产生的同名日志文件数，超过则只保留最新的n个。设定值value="－1"为不限文件数  同名的日志保存数量-->
			<maxSizeRollBackups value="5" />
			<!--日志文件大小-->
			<maximumFileSize value="100MB" />
			
			
			<!-- 自定义参数： 根据 "删除依据" 删除N天前的数据-->
			<OutDateDays value="15"/>
			<!-- 自定义参数：日志目录下的最大文件数量，以 "删除依据" 为排序条件，越接近当前时间的保留 -->
			<param name="MaximumFileCount" value="10"/>
			<!-- 自定义参数：删除依据-->
			<!--     创建时间      CreationTime-->
			<!--     访问时间      LastAccessTime-->
			<!--     最后修改时间  LastWriteTime-->
			<param name="DeleteStyle" value="CreationTime"/>


			<!--多线程时采用最小锁定-->
			<!--<lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>-->

			<!--其中layout节点的配置说明：
				%m(message):输出的日志消息；
				%n(newline):换行；
				%d(datetime):输出当前语句运行的时刻；
				%r(runtime):输出程序从运行到执行到当前语句时消耗的毫秒数；
				%t(threadid):当前语句所在的线程ID ；
				%p(priority): 日志的当前日志级别；
				%c(class):当前日志对象的名称；
				%L：输出语句所在的行号；
				%F：输出语句所在的文件名；
				%-10：表示最小长度为10，如果不够，则用空格填充；-->
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%date [%thread] %-5level %logger: %message%newline"/>
			</layout>
		</appender>

		
		
		
		<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
			<!--日志的存储路径-->
			<file value="log/" />
			<!--是否向文件中追加日志-->
			<appendToFile value="true" />
			<!--滚动模式 复合-->
			<rollingStyle value="Composite" />
			<!--计数类型为1，2，3…-->
			<!--<param name="CountDirection" value="1"/>-->
			<!--日志文件名是否是固定不变的 不变则只写到一个文件中-->
			<staticLogFileName value="false" />
			<!--日志文件名格式为:yyyy-MM-dd.log-->
			<datePattern value="yyyyMMdd'.log'" />
			<!--最多产生的日志文件数，超过则只保留最新的n个。设定值value="－1"为不限文件数  同名的日志保存数量-->
			<maxSizeRollBackups value="5" />
			<!--日志文件大小-->
			<maximumFileSize value="100MB" />
			<!--<param name="MaxSizeRollBackups" value="10" />-->

			<!--多线程时采用最小锁定-->
			<!--<lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>-->
			
			<!--其中layout节点的配置说明：
				%m(message):输出的日志消息；
				%n(newline):换行；
				%d(datetime):输出当前语句运行的时刻；
				%r(runtime):输出程序从运行到执行到当前语句时消耗的毫秒数；
				%t(threadid):当前语句所在的线程ID ；
				%p(priority): 日志的当前日志级别；
				%c(class):当前日志对象的名称；
				%L：输出语句所在的行号；
				%F：输出语句所在的文件名；
				%-10：表示最小长度为10，如果不够，则用空格填充；-->
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%date [%thread] %-5level %logger: %message%newline"/>
			</layout>
		</appender>

		<appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
			<layout type="log4net.Layout.PatternLayout" value="%date [%thread] %-5level %logger: %message%newline"/>
		</appender>
	</log4net>
</configuration>