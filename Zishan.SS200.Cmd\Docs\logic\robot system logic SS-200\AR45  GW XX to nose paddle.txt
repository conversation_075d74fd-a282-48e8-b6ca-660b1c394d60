﻿AR45
GW XX to nose paddle
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			shuttle position status review
SSD1~SSD7
				SSD1
					shuttle 2 exist status review
SS45 or SS46
						SS45 
shuttle 2 enable
							paddle status review
RS54
								RS54=0
									pin search status review
RS56
										RS56=1
											AR8
T-axis nose to cassette
												AR35
Move Z-axis height nose get slot xx
													AR18
R-axis nose cassette extend
														AR37
Move Z-axis height nose put slot xx
															AR68
wafer cassette to nose paddle
																command done
										RS56=0
											AR42
pin search
												AR8
T-axis nose to cassette
													AR35
Move Z-axis height nose get slot xx
														AR18
R-axis nose cassette extend
															AR37
Move Z-axis height nose put slot xx
																AR68
wafer cassette to nose paddle
																	command done
								RS54=1
									RA17 ALARM
						SS46 
shuttle 2 disable
							RA24 ALARM
				SSD2
					shuttle 1 exist status review
SS43 or SS44
						SS43 
shuttle 1 enable
							paddle status review
RS54
								RS54=0
									pin search status review
RS56
										RS56=1
											AR8
T-axis nose to cassette
												AR35
Move Z-axis height nose get slot xx
													AR18
R-axis nose cassette extend
														AR37
Move Z-axis height nose put slot xx
															AR68
wafer cassette to nose paddle
																command done
										RS56=0
											AR42
pin search
												AR8
T-axis nose to cassette
													AR35
Move Z-axis height nose get slot xx
														AR18
R-axis nose cassette extend
															AR37
Move Z-axis height nose put slot xx
																AR68
wafer cassette to nose paddle
																	command done
								RS54=1
									RA17 ALARM
						SS44 
shuttle 1 disable
							RA23 ALARM
				others status
					RA25 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM