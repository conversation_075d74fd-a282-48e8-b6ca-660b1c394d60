﻿<prism:PrismApplication
    x:Class="Zishan.SS200.Cmd.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:prism="http://prismlibrary.com/">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme
                    BaseTheme="Dark"
                    PrimaryColor="DeepPurple"
                    SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  转换器  -->
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
            <converters:BoolToStateConverter x:Key="BoolToStateConverter" />
            <converters:ValueToColorConverter x:Key="ValueToColorConverter" />
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />

            <!--  Badge 控件样式 - 已移除自定义样式以避免与HandyControl内部动画冲突  -->
            <!--  如需自定义Badge样式，请确保包含HandyControl期望的所有命名元素  -->
        </ResourceDictionary>
    </Application.Resources>
</prism:PrismApplication>