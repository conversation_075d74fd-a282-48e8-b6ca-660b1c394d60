using Zishan.SS200.Cmd.Enums;
using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Zishan.SS200.Cmd.Converters
{
    public class WorkStatusBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is EnuWorkStatus workStatus)
            {
                switch (workStatus)
                {
                    case EnuWorkStatus.Idle:
                        return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#ff2a3c52"));
                    //return Brushes.White;

                    case EnuWorkStatus.Alarm:
                        return Brushes.Red;

                    case EnuWorkStatus.Run:
                        return Brushes.Blue;

                    case EnuWorkStatus.Busy:
                        return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#ff4dff73"));
                    //return Brushes.Yellow;

                    case EnuWorkStatus.Process:
                        return Brushes.Cyan;

                    case EnuWorkStatus.Mapping:
                        return Brushes.Purple;

                    default:
                        return Binding.DoNothing;
                }
            }
            return Binding.DoNothing;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}