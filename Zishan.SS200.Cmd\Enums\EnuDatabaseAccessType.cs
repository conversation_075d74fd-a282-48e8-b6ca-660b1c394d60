﻿using System.ComponentModel;
using System.Text.Json.Serialization;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 数据库访问类型，Dev本地数据库开发模式，Test测试模式，Product_In(无尘室内网络wuchenshi)生产模式，Product_Out(无尘室外zishan网络)生产模式
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum EnuDatabaseAccessType
    {
        /// <summary>
        /// Dev本地数据库开发模式
        /// </summary>
        [Description("Dev本地数据库开发模式")]
        Dev = 0,

        /// <summary>
        /// Test测试模式
        /// </summary>
        [Description("Test测试模式")]
        Test = 1,

        /// <summary>
        /// Product_In(无尘室内网络wuchenshi)生产模式
        /// </summary>
        [Description("Product_In(无尘室内网络wuchenshi)生产模式")]
        Product_In = 2,

        /// <summary>
        /// Product_Out(无尘室外zishan网络)生产模式
        /// </summary>
        [Description("Product_Out(无尘室外zishan网络)生产模式")]
        Product_Out = 3
    }
}