using System;
using <PERSON>ishan.SS200.Cmd.Models.SS200;
using <PERSON>ishan.SS200.Cmd.Enums.SS200.AlarmCode.Shuttle;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// SA39访问器验证测试类
    /// 专门验证SA39访问器是否正确实现
    /// </summary>
    public class SA39AccessorVerificationTest
    {
        /// <summary>
        /// 验证SA39访问器
        /// </summary>
        public static void VerifySA39Accessor()
        {
            try
            {
                Console.WriteLine("=== SA39访问器验证测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var shuttleAlarm = interLock.AlarmCode.Shuttle;

                // 测试SA39访问器
                Console.WriteLine("\n测试SA39访问器:");
                var sa39Accessor = shuttleAlarm.SA39_ShuttlePressureNotATMBackfillFailure;
                
                if (sa39Accessor != null)
                {
                    string content = sa39Accessor.Content ?? "未定义";
                    string chsContent = sa39Accessor.ChsContent ?? "未定义";
                    
                    Console.WriteLine($"✅ SA39访问器创建成功");
                    Console.WriteLine($"   英文内容: {content}");
                    Console.WriteLine($"   中文内容: {chsContent}");
                    
                    // 验证内容是否符合预期
                    bool contentCorrect = content.Contains("shuttle pressure") && 
                                        content.Contains("ATM status") && 
                                        content.Contains("backfill loadlock failure");
                    
                    bool chsContentCorrect = chsContent.Contains("传送室压力") && 
                                           chsContent.Contains("大气状态") && 
                                           chsContent.Contains("装载锁回填失败");
                    
                    if (contentCorrect)
                    {
                        Console.WriteLine("✅ 英文内容验证通过");
                    }
                    else
                    {
                        Console.WriteLine("❌ 英文内容验证失败");
                    }
                    
                    if (chsContentCorrect)
                    {
                        Console.WriteLine("✅ 中文内容验证通过");
                    }
                    else
                    {
                        Console.WriteLine("❌ 中文内容验证失败");
                    }
                    
                    if (contentCorrect && chsContentCorrect)
                    {
                        Console.WriteLine("🎉 SA39访问器验证完全通过！");
                    }
                }
                else
                {
                    Console.WriteLine("❌ SA39访问器为空");
                }
                
                Console.WriteLine("\n=== SA39访问器验证测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ SA39访问器验证测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 验证枚举值对应关系
        /// </summary>
        public static void VerifyEnumMapping()
        {
            try
            {
                Console.WriteLine("=== SA39枚举映射验证测试 ===");
                
                // 验证SA39枚举值
                var sa39Enum = EnuShuttleAlarmCodes.SA39;
                Console.WriteLine($"SA39枚举值: {sa39Enum}");
                Console.WriteLine($"SA39枚举数值: {(int)sa39Enum}");
                
                // 验证枚举描述
                var description = sa39Enum.ToString();
                Console.WriteLine($"SA39枚举描述: {description}");
                
                if ((int)sa39Enum == 38) // SA39应该是第39个，索引为38
                {
                    Console.WriteLine("✅ SA39枚举值正确 (索引38)");
                }
                else
                {
                    Console.WriteLine($"❌ SA39枚举值错误，期望38，实际{(int)sa39Enum}");
                }
                
                Console.WriteLine("=== SA39枚举映射验证测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ SA39枚举映射验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 完整验证SA39
        /// </summary>
        public static void CompleteVerification()
        {
            try
            {
                Console.WriteLine("🔍 开始SA39完整验证...\n");
                
                VerifyEnumMapping();
                Console.WriteLine();
                VerifySA39Accessor();
                
                Console.WriteLine("\n🎯 SA39完整验证结束");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ SA39完整验证过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证Shuttle报警代码总数
        /// </summary>
        public static void VerifyTotalCount()
        {
            try
            {
                Console.WriteLine("=== Shuttle报警代码总数验证 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var shuttleAlarm = interLock.AlarmCode.Shuttle;
                var shuttleAlarmType = shuttleAlarm.GetType();
                
                // 获取所有以SA开头的属性
                var properties = shuttleAlarmType.GetProperties();
                int saPropertyCount = 0;
                
                Console.WriteLine("发现的Shuttle报警代码访问器:");
                foreach (var property in properties)
                {
                    if (property.Name.StartsWith("SA") && property.Name.Contains("_"))
                    {
                        saPropertyCount++;
                        Console.WriteLine($"  {saPropertyCount:D2}. {property.Name}");
                    }
                }
                
                Console.WriteLine($"\n📊 统计结果:");
                Console.WriteLine($"   总计发现: {saPropertyCount} 个Shuttle报警代码访问器");
                Console.WriteLine($"   预期数量: 39 个");
                
                if (saPropertyCount == 39)
                {
                    Console.WriteLine("✅ Shuttle报警代码访问器数量正确！");
                    Console.WriteLine("🎉 包含SA39在内的所有39个访问器都已正确定义！");
                }
                else
                {
                    Console.WriteLine($"❌ 数量不匹配，缺少 {39 - saPropertyCount} 个访问器");
                }
                
                Console.WriteLine("=== Shuttle报警代码总数验证完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 总数验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有验证测试
        /// </summary>
        public static void RunAllVerifications()
        {
            Console.WriteLine("🚀 开始SA39访问器全面验证测试\n");
            
            CompleteVerification();
            Console.WriteLine();
            VerifyTotalCount();
            
            Console.WriteLine("\n🏁 SA39访问器全面验证测试完成！");
        }
    }
}
