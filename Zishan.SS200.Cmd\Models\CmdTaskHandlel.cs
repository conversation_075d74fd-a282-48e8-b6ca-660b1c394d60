﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NModbus;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Models
{
    /*
    CMD Index   Info
   0	S1 SD
   1	S2 SU
   2	S3 SR1
   3	S4 SR2
   4	S5 CD CD
   5	S6 OD CD
   6	S7 TS1
   7	S8 TS2
   8	S9 SC
   9	S10 RC
   10	S11 OV SV
   11	S12 CV SV
   12	S13 OV SB
   13	S14 CV SB
   14	S15 OV XV
   15	S16 CV XV
   16	S17 OV BL
   17	S18 CV BL
   18	S19 OV LB
   19	S20 CV LB
   */

    /// <summary>
    /// 命令执行状态
    /// </summary>
    public enum TaskHandleStatus
    {
        /// <summary>
        /// 空闲
        /// </summary>
        Idle = 0,

        /// <summary>
        /// 触发执行
        /// </summary>
        Trigger = 1,

        /// <summary>
        /// 执行中
        /// </summary>
        Running = 2,

        /// <summary>
        /// 执行完成
        /// </summary>
        Complete = 4
    }

    /// <summary>
    /// 命令执行结果
    /// </summary>
    public enum TaskHandleResult
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success = 0,

        /// <summary>
        /// 失败
        /// </summary>
        Failed = 1,

        /// <summary>
        /// 超时
        /// </summary>
        Timeout = 2,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 3
    }

    /// <summary>
    /// CmdTaskHandlel - 命令任务处理类
    /// 用于处理Modbus通信命令的执行、监控和管理
    /// </summary>
    public class CmdTaskHandlel
    {
        /*
        Status Flag(SF)		1	1	0
        Run Flag(RF)		0	4	0
        Run Info(RUI1)		0	0/1/2/...	0
        Return Info(REI)		0	0/1/2/...	0
        CMD Start Addr(CSA)		0x0012	0x0012	0
        CMD Len(CL)		3	3	0
        指令执行过程:
    1.向非handle区写入指令数据及参数( 例:向0x0012地址写入CMD Index, Parameter )
    2.向Task Handle1区域写入代表触发的状态及更新指令所在地址及长度信息
    3.一直轮询读取Task Handle1, 在RF_Reg值为0x0004时任务执行完成, 并且RI_Reg值有效
(在ReadComplete Run位置位读取之后会将当前Task Handle的所有寄存器都清零)

    4.若在Task Handle1执行期间有新的动作需要执行, 可以在未使用的非Handle区域写入
指令及参数, 再将未使用的Task Handle写入触发状态及地址和长度信息。

         */

        #region 属性定义

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 任务描述
        /// </summary>
        public string TaskDescription { get; set; } = string.Empty;

        /// <summary>
        /// Status Flag(SF) - 状态标志
        /// </summary>
        public ModbusRegister StatusFlag { get; set; }

        /// <summary>
        /// Run Flag(RF) - 运行标志
        /// </summary>
        public ModbusRegister RunFlag { get; set; }

        /// <summary>
        /// Run Info(RUI1) - 运行信息
        /// </summary>
        public ModbusRegister RunInfo { get; set; }

        /// <summary>
        /// Return Info(REI) - 返回信息
        /// </summary>
        public ModbusRegister ReturnInfo { get; set; }

        /// <summary>
        /// CMD Start Addr(CSA) - 命令起始地址
        /// </summary>
        public ModbusRegister CmdStartAddr { get; set; }

        /// <summary>
        /// CMD Len(CL) - 命令长度
        /// </summary>
        public ModbusRegister CmdLen { get; set; }

        /// <summary>
        /// 非handle区：命令参数列表
        /// </summary>
        public List<ModbusRegister> CmdParams { get; set; }

        #endregion 属性定义

        #region 构造函数

        /// <summary>
        /// 构造函数：初始化任务处理器
        /// </summary>
        /// <param name="cmdStartAddr">命令寄存器起始地址列表 [SF,RF,RUI1,REI,CSA,CL]</param>
        /// <param name="cmdParStartAddr">命令参数起始地址列表</param>
        public CmdTaskHandlel(List<ushort> cmdStartAddr, List<ushort> cmdParStartAddr)
        {
            if (cmdStartAddr == null || cmdStartAddr.Count != 6)
                throw new ArgumentException("命令寄存器地址列表必须包含6个地址");

            // 初始化寄存器
            StatusFlag = new ModbusRegister(cmdStartAddr[0]);
            RunFlag = new ModbusRegister(cmdStartAddr[1]);
            RunInfo = new ModbusRegister(cmdStartAddr[2]);
            ReturnInfo = new ModbusRegister(cmdStartAddr[3]);
            CmdStartAddr = new ModbusRegister(cmdStartAddr[4]);
            CmdLen = new ModbusRegister(cmdStartAddr[5]);

            // 初始化参数列表
            CmdParams = new List<ModbusRegister>();
            if (cmdParStartAddr != null)
            {
                foreach (var addr in cmdParStartAddr)
                {
                    CmdParams.Add(new ModbusRegister(addr));
                }
            }
        }

        #endregion 构造函数

        #region 命令执行方法

        /// <summary>
        /// 清空使用命令参数寄存器为0
        /// </summary>
        /// <param name="master"></param>
        /// <param name="slaveId"></param>
        /// <param name="timeout"></param>
        /// <returns></returns>
        public async Task<TaskHandleResult> ExecuteClearAllAsync(IModbusMaster master, byte slaveId, int RegisterCount = 64)
        {
            try
            {
                await Task.Run(() => master.WriteMultipleRegisters(slaveId, 0, Enumerable.Repeat<ushort>(0, RegisterCount).ToArray()));
                return TaskHandleResult.Success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"执行命令失败: {ex.Message}");
                return TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="cmdStartAddr">命令起始地址</param>
        /// <param name="cmdData">命令数据</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> ExecuteCommandAsync(IModbusMaster master, byte slaveId, EnuShuttleCmdIndex enuShuttleCmdIndex, List<ushort> cmdPars, int timeout = 5000)
        {
            try
            {
                /*
                指令执行过程:
                    1.向非handle区写入指令数据及参数(例: 向0x0030地址写入CMD Index, Parameter)
                    2.向Task Handle1区域写入代表触发的状态及更新指令所在地址及长度信息
                    3.一直轮询读取Task Handle1, 在RF_Reg值为0x0004时任务执行完成, 并且RI_Reg值有效
                (在ReadComplete Run位置位读取之后会将当前Task Handle的所有寄存器都清零)

                    4.若在Task Handle1执行期间有新的动作需要执行, 可以在未使用的非Handle区域写入
                指令及参数, 再将未使用的Task Handle写入触发状态及地址和长度信息。
                */

                //  1.向非handle区写入指令数据及参数(例: 向0x0030地址写入CMD Index, Parameter)
                var cmdData = new List<ushort> { (ushort)enuShuttleCmdIndex };
                foreach (var parValue in cmdPars)
                {
                    cmdData.Add(parValue);
                }
                await Task.Run(() => master.WriteMultipleRegisters(slaveId, CmdParams.FirstOrDefault().Address, cmdData.ToArray()));

                // 2. 向Task Handle1区域写入代表触发的状态及更新指令所在地址及长度信息

                await Task.Run(() =>
                {
                    master.WriteMultipleRegisters(slaveId, StatusFlag.Address, new ushort[]
                    {
                        (ushort)TaskHandleStatus.Trigger,  // Status Flag
                        0,                                 // Run Flag
                        0,                                 // Run Info
                        0,                                 // Return Info
                        CmdParams[0].Address,                      // CMD Start Addr
                        (ushort)cmdData.Count            // CMD Len
                    });
                });

                // 3.一直轮询读取Task Handle1, 在RF_Reg值为0x0004时任务执行完成, 并且RI_Reg值有效(在ReadComplete Run位置位读取之后会将当前Task Handle的所有寄存器都清零)
                return await WaitForCompletionAsync(master, slaveId, timeout);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"执行命令失败: {ex.Message}");
                return TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 执行带参数的命令
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="cmdIndex">命令索引</param>
        /// <param name="parameters">命令参数</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>执行结果</returns>
        // public async Task<TaskHandleResult> ExecuteCommandWithParamsAsync(IModbusMaster master, byte slaveId, ushort cmdIndex, ushort[] parameters, int timeout = 5000)
        // {
        //     try
        //     {
        //         // 1. 写入参数
        //         if (parameters != null && parameters.Length > 0)
        //         {
        //             for (int i = 0; i < Math.Min(parameters.Length, CmdParams.Count); i++)
        //             {
        //                 await Task.Run(() => master.WriteSingleRegister(slaveId, CmdParams[i].Address, parameters[i]));
        //             }
        //         }
        //
        //         // 2. 执行命令
        //         return await ExecuteCommandAsync(master, slaveId, cmdIndex, new ushort[] { cmdIndex }, timeout);
        //     }
        //     catch (Exception ex)
        //     {
        //         Console.WriteLine($"执行带参数命令失败: {ex.Message}");
        //         return TaskHandleResult.Failed;
        //     }
        // }

        #endregion 命令执行方法

        #region 状态监控方法

        /// <summary>
        /// 监控任务状态
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <returns>当前状态、运行信息和返回信息</returns>
        public async Task<(TaskHandleStatus Status, ushort RunInfo, ushort ReturnInfo)> MonitorStatusAsync(IModbusMaster master, byte slaveId)
        {
            try
            {
                var registers = await Task.Run(() => master.ReadHoldingRegisters(slaveId, StatusFlag.Address, 4));
                return ((TaskHandleStatus)registers[1], registers[2], registers[3]);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"监控状态失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 等待任务完成
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> WaitForCompletionAsync(IModbusMaster master, byte slaveId, int timeout = 5000)
        {
            var startTime = DateTime.Now;
            while (true)
            {
                if ((DateTime.Now - startTime).TotalMilliseconds > timeout)
                {
                    return TaskHandleResult.Timeout;
                }

                var (status, runInfo, returnInfo) = await MonitorStatusAsync(master, slaveId);

                // 检查执行状态
                switch (status)
                {
                    case TaskHandleStatus.Complete:
                        return returnInfo == 0 ? TaskHandleResult.Success : TaskHandleResult.Failed;

                    case TaskHandleStatus.Idle:
                        // 如果返回空闲状态，可能是执行出错
                        if (returnInfo != 0)
                            return TaskHandleResult.Failed;
                        break;
                }

                await Task.Delay(100);
            }
        }

        #endregion 状态监控方法

        #region 任务控制方法

        /// <summary>
        /// 取消任务执行
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        public async Task CancelExecutionAsync(IModbusMaster master, byte slaveId)
        {
            try
            {
                // 清除所有寄存器
                await Task.Run(() => master.WriteMultipleRegisters(slaveId, StatusFlag.Address, new ushort[] { 0, 0, 0, 0, 0, 0 }));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取消任务失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 重置任务状态
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        public async Task ResetTaskAsync(IModbusMaster master, byte slaveId)
        {
            try
            {
                await CancelExecutionAsync(master, slaveId);
                // 可以添加其他重置逻辑
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重置任务失败: {ex.Message}");
                throw;
            }
        }

        #endregion 任务控制方法

        #region 辅助方法

        /// <summary>
        /// 获取任务状态描述
        /// </summary>
        /// <param name="status">任务状态</param>
        /// <returns>状态描述</returns>
        public static string GetStatusDescription(TaskHandleStatus status)
        {
            return status switch
            {
                TaskHandleStatus.Idle => "空闲",
                TaskHandleStatus.Trigger => "触发执行",
                TaskHandleStatus.Running => "执行中",
                TaskHandleStatus.Complete => "执行完成",
                _ => "未知状态"
            };
        }

        /// <summary>
        /// 获取执行结果描述
        /// </summary>
        /// <param name="result">执行结果</param>
        /// <returns>结果描述</returns>
        public static string GetResultDescription(TaskHandleResult result)
        {
            return result switch
            {
                TaskHandleResult.Success => "成功",
                TaskHandleResult.Failed => "失败",
                TaskHandleResult.Timeout => "超时",
                TaskHandleResult.Cancelled => "已取消",
                _ => "未知结果"
            };
        }

        #endregion 辅助方法
    }
}