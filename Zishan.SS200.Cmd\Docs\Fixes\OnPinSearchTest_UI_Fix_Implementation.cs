// OnPinSearchTest UI卡死修复实现代码
// 文件：BasicCommandTestViewModel.cs

using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.Input;

namespace Zishan.SS200.Cmd.ViewModels
{
    public partial class BasicCommandTestViewModel
    {
        /// <summary>
        /// PinSearch 测试 - 修复版本（解决UI卡死问题）
        /// </summary>
        [RelayCommand]
        private async Task OnPinSearchTest()
        {
            if (IsExecutingCommand)
                return;

            try
            {
                // 显示安全确认对话框，包含循环次数信息
                string loopInfo = LoopCount == -1 ? "无限循环" : $"{LoopCount}次";
                var confirmResult = MessageBox.Show(
                    "⚠️ 安全提示 ⚠️\n\n" +
                    "即将执行机械臂 PinSearch 操作，请确认：\n\n" +
                    "✓ 机械臂周围无人员和障碍物\n" +
                    "✓ 已检查机械臂PinSearch 运动路径，确保不会有碰撞的可能\n" +
                    "✓ 供电插排按钮及时可断电使用作为紧急按钮使用\n\n" +
                    $"执行次数：{loopInfo}\n\n" +
                    "确认执行 PinSearch 操作吗？",
                    "PinSearch 安全确认",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning,
                    MessageBoxResult.No);

                // 如果用户选择取消，则退出
                if (confirmResult != MessageBoxResult.Yes)
                {
                    UILogService.AddLog("用户取消了 PinSearch 操作");
                    HcGrowlExtensions.Info("已取消 PinSearch 操作", waitTime: 3);
                    return;
                }

                // 检查设备连接状态
                if (_mcuCmdService == null || !_mcuCmdService.Robot.IsConnected)
                {
                    string errorMsg = _mcuCmdService == null ? "命令服务为空，请检查依赖注入" : "请先连接Robot设备";
                    UILogService.AddErrorLog(errorMsg);
                    HcGrowlExtensions.Warning(errorMsg);
                    return;
                }

                // 设置执行状态
                IsExecutingCommand = true;
                _cancellationTokenSource = new CancellationTokenSource();

                // 显示开始信息
                UILogService.AddInfoLog($"开始执行机器人 PinSearch 测试 - 循环模式: {loopInfo}");
                HcGrowlExtensions.Info($"正在执行 PinSearch 测试 ({loopInfo})...", waitTime: 3);

                // 🔥 关键修复：将整个循环逻辑移到后台线程
                await Task.Run(async () =>
                {
                    await ExecutePinSearchLoopAsync(_cancellationTokenSource.Token);
                }, _cancellationTokenSource.Token);

                // 显示完成信息
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    UILogService.AddInfoLog("PinSearch 测试全部完成");
                    HcGrowlExtensions.Info("PinSearch 测试全部完成", waitTime: 3);
                });
            }
            catch (OperationCanceledException)
            {
                // 处理用户取消操作
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    UILogService.AddWarningLog("PinSearch 测试已被用户取消");
                    HcGrowlExtensions.Warning("PinSearch 测试已取消", waitTime: 2);
                });
            }
            catch (Exception ex)
            {
                // 处理其他异常
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    string errorMsg = $"PinSearch 测试异常: {ex.Message}";
                    UILogService.AddErrorLog(errorMsg);
                    HcGrowlExtensions.Error(errorMsg);
                    _logger.Error("PinSearch 测试异常", ex);
                });
            }
            finally
            {
                // 清理资源
                IsExecutingCommand = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 在后台线程执行PinSearch循环逻辑
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ExecutePinSearchLoopAsync(CancellationToken cancellationToken)
        {
            int currentLoop = 0;
            bool isInfiniteLoop = LoopCount == -1;
            int remainingCount = RemainingLoopCount;

            try
            {
                // 循环执行Pin Search测试
                while (remainingCount != 0 && !cancellationToken.IsCancellationRequested)
                {
                    currentLoop++;

                    // 🔥 关键修复：在UI线程上更新状态（批量更新）
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        string currentLoopInfo = isInfiniteLoop ? $"第{currentLoop}次" : $"第{currentLoop}次 (剩余{remainingCount}次)";
                        UILogService.AddLogAndIncreaseIndent($"=== {currentLoopInfo} PinSearch 测试开始 ===");
                    });

                    // 检查取消状态
                    if (await CheckCancellationAsync(cancellationToken))
                        break;

                    // 清零PinSearch结果数据
                    await ClearPinSearchDataAsync();

                    // 检查取消状态
                    if (await CheckCancellationAsync(cancellationToken))
                        break;

                    // 执行 Smooth 端 PinSearch（在后台线程）
                    var smoothResult = await ExecuteSinglePinSearchAsync(EnuRobotEndType.Smooth, cancellationToken);
                    if (!smoothResult.Success)
                    {
                        await LogErrorAsync($"Smooth 端 PinSearch 测试失败: {smoothResult.Message}");
                        break;
                    }

                    // 检查取消状态
                    if (await CheckCancellationAsync(cancellationToken))
                        break;

                    // 短暂延迟
                    await Task.Delay(100, cancellationToken);

                    // 执行 Nose 端 PinSearch（在后台线程）
                    var noseResult = await ExecuteSinglePinSearchAsync(EnuRobotEndType.Nose, cancellationToken);
                    if (!noseResult.Success)
                    {
                        await LogErrorAsync($"Nose 端 PinSearch 测试失败: {noseResult.Message}");
                        break;
                    }

                    // 🔥 关键修复：批量更新UI结果
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        UpdatePinSearchResults(smoothResult, noseResult, currentLoop);
                        string currentLoopInfo = isInfiniteLoop ? $"第{currentLoop}次" : $"第{currentLoop}次 (剩余{remainingCount}次)";
                        UILogService.DecreaseIndentAndAddSuccessLog($"=== {currentLoopInfo} PinSearch 测试完成 ===");
                    });

                    // 更新循环计数
                    if (!isInfiniteLoop && remainingCount > 0)
                    {
                        remainingCount--;
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            RemainingLoopCount = remainingCount;
                            UILogService.AddLog($"剩余循环次数: {remainingCount}");
                        });
                    }

                    // 如果还有剩余循环，等待一段时间
                    if (remainingCount != 0 && !cancellationToken.IsCancellationRequested)
                    {
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            UILogService.AddLog("等待2秒后开始下一次循环...");
                        });
                        await Task.Delay(2000, cancellationToken);
                    }
                }

                // 更新最终状态
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    GetCurrentRTZPosition();
                    string completionMessage = cancellationToken.IsCancellationRequested
                        ? $"PinSearch 测试已取消 (已完成{currentLoop - 1}次)"
                        : $"PinSearch 测试全部完成 (共执行{currentLoop}次)";
                    UILogService.AddInfoLog(completionMessage);
                });
            }
            catch (OperationCanceledException)
            {
                // 正常的取消操作，重新抛出
                throw;
            }
            catch (Exception ex)
            {
                // 记录异常
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    UILogService.AddErrorLog($"PinSearch循环执行异常: {ex.Message}");
                    _logger.Error("PinSearch循环执行异常", ex);
                });
            }
        }

        /// <summary>
        /// 检查取消状态
        /// </summary>
        private async Task<bool> CheckCancellationAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    UILogService.AddWarningLog("检测到取消请求，正在停止PinSearch测试...");
                });
                return true;
            }
            return false;
        }

        /// <summary>
        /// 清零PinSearch数据
        /// </summary>
        private async Task ClearPinSearchDataAsync()
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                UILogService.AddInfoLog("清零PinSearch结果数据...");
            });

            // 获取当前机器人状态
            var robotStatus = _interlock.SubsystemStatus.Robot.Status;
            robotStatus.PinSearchStatus = false;
            robotStatus.EnuPinSearchDataEffective = EnuPinSearchStatus.None;

            // 清零服务中的基准值
            _mcuCmdService.SmoothBasePinSearchValue = 0;
            _mcuCmdService.NoseBasePinSearchValue = 0;

            // 清零机器人状态中的PinSearch值
            robotStatus.Shuttle1PinSearchSmoothP1 = 0;
            robotStatus.Shuttle1PinSearchSmoothP2 = 0;
            robotStatus.Shuttle1PinSearchNoseP3 = 0;
            robotStatus.Shuttle1PinSearchNoseP4 = 0;
            robotStatus.Shuttle2PinSearchSmoothP1 = 0;
            robotStatus.Shuttle2PinSearchSmoothP2 = 0;
            robotStatus.Shuttle2PinSearchNoseP3 = 0;
            robotStatus.Shuttle2PinSearchNoseP4 = 0;

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                UILogService.AddSuccessLog("PinSearch结果数据已清零");
            });
        }

        /// <summary>
        /// 执行单次PinSearch操作
        /// </summary>
        private async Task<(bool Success, string Message, int PinSearchP1Value, int PinSearchP2Value)> 
            ExecuteSinglePinSearchAsync(EnuRobotEndType endType, CancellationToken cancellationToken)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                return await _mcuCmdService.PinSearchAsync(endType, IsTRZAxisReturnZeroed);
            }
            catch (OperationCanceledException)
            {
                return (false, "操作已取消", 0, 0);
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        private async Task LogErrorAsync(string message)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                UILogService.AddErrorLog(message);
                HcGrowlExtensions.Error(message);
            });
        }

        /// <summary>
        /// 更新PinSearch结果到UI
        /// </summary>
        private void UpdatePinSearchResults(
            (bool Success, string Message, int PinSearchP1Value, int PinSearchP2Value) smoothResult,
            (bool Success, string Message, int PinSearchP1Value, int PinSearchP2Value) noseResult,
            int currentLoop)
        {
            var robotStatus = _interlock.SubsystemStatus.Robot.Status;

            if (smoothResult.Success)
            {
                robotStatus.Shuttle1PinSearchSmoothP1 = smoothResult.PinSearchP1Value;
                robotStatus.Shuttle1PinSearchSmoothP2 = smoothResult.PinSearchP2Value;
                string msg = $"执行 Smooth 端 PinSearch 测试成功: {smoothResult.Message}，P1Value={smoothResult.PinSearchP1Value}，P2Value={smoothResult.PinSearchP2Value}，平均值={_mcuCmdService.SmoothBasePinSearchValue}";
                UILogService.AddSuccessLog(msg);
            }

            if (noseResult.Success)
            {
                robotStatus.Shuttle1PinSearchNoseP3 = noseResult.PinSearchP1Value;
                robotStatus.Shuttle1PinSearchNoseP4 = noseResult.PinSearchP2Value;
                robotStatus.PinSearchStatus = true;
                robotStatus.EnuPinSearchDataEffective = EnuPinSearchStatus.Valid;
                string msg = $"执行 Nose 端 PinSearch 测试成功: {noseResult.Message}，P1Value={noseResult.PinSearchP1Value}，P2Value={noseResult.PinSearchP2Value}，平均值={_mcuCmdService.NoseBasePinSearchValue}";
                UILogService.AddSuccessLog(msg);
            }
        }
    }
}
