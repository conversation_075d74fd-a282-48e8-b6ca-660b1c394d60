using System;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// RTZ轴位置访问器使用示例
    /// 演示方案二：通过SS200InterLockMain统一访问入口使用RTZ轴位置功能
    /// </summary>
    public class RTZAxisPositionAccessorExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RTZAxisPositionAccessorExample));

        /// <summary>
        /// 演示基本的RTZ轴位置访问功能
        /// </summary>
        public static void DemonstrateBasicAccess()
        {
            try
            {
                _logger.Info("=== RTZ轴位置基本访问示例 ===");

                // 通过SS200InterLockMain统一入口获取RTZ轴位置访问器
                var interlock = SS200InterLockMain.Instance;
                var rtzPosition = interlock.RTZAxisPosition;

                // 检查数据有效性
                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，请确保Robot设备已连接");
                    return;
                }

                _logger.Info("RTZ轴位置数据有效，开始演示基本访问功能...");

                // 1. 基本位置访问（步进值）
                _logger.Info("--- 基本位置访问（步进值） ---");
                int tAxisStep = rtzPosition.CurrentTAxisStep;
                int rAxisStep = rtzPosition.CurrentRAxisStep;
                int zAxisStep = rtzPosition.CurrentZAxisStep;

                _logger.Info($"T轴步进位置: {tAxisStep} steps");
                _logger.Info($"R轴步进位置: {rAxisStep} steps");
                _logger.Info($"Z轴步进位置: {zAxisStep} steps");

                // 2. 物理单位访问
                _logger.Info("--- 物理单位访问 ---");
                double tAxisDegree = rtzPosition.CurrentTAxisDegree;
                double rAxisLength = rtzPosition.CurrentRAxisLength;
                double zAxisHeight = rtzPosition.CurrentZAxisHeight;

                _logger.Info($"T轴角度: {tAxisDegree:F2}°");
                _logger.Info($"R轴长度: {rAxisLength:F2}mm");
                _logger.Info($"Z轴高度: {zAxisHeight:F2}mm");

                // 3. 组合访问
                _logger.Info("--- 组合访问 ---");
                var (t, r, z) = rtzPosition.GetCurrentRTZSteps();
                _logger.Info($"RTZ轴步进值组合: T={t}, R={r}, Z={z}");

                var (tDeg, rLen, zHeight) = rtzPosition.GetCurrentRTZPhysicalValues();
                _logger.Info($"RTZ轴物理值组合: T={tDeg:F2}°, R={rLen:F2}mm, Z={zHeight:F2}mm");

                _logger.Info("=== 基本访问示例完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"基本访问示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示安全检查和数据验证功能
        /// </summary>
        public static void DemonstrateSafetyChecks()
        {
            try
            {
                _logger.Info("=== RTZ轴位置安全检查示例 ===");

                var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;

                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过安全检查示例");
                    return;
                }

                // 1. 整体安全检查
                bool allSafe = rtzPosition.AreAllAxesInSafeRange;
                _logger.Info($"所有轴都在安全范围内: {allSafe}");

                // 2. 单独轴安全检查
                bool tAxisSafe = rtzPosition.IsAxisPositionInSafeRange("T");
                bool rAxisSafe = rtzPosition.IsAxisPositionInSafeRange("R");
                bool zAxisSafe = rtzPosition.IsAxisPositionInSafeRange("Z");

                _logger.Info($"T轴安全状态: {tAxisSafe}");
                _logger.Info($"R轴安全状态: {rAxisSafe}");
                _logger.Info($"Z轴安全状态: {zAxisSafe}");

                // 3. 详细位置信息
                var detailedInfo = rtzPosition.GetDetailedPositionInfo();
                _logger.Info($"详细位置信息: {detailedInfo}");

                _logger.Info("=== 安全检查示例完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"安全检查示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示格式化显示功能
        /// </summary>
        public static void DemonstrateFormattedDisplay()
        {
            try
            {
                _logger.Info("=== RTZ轴位置格式化显示示例 ===");

                var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;

                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过格式化显示示例");
                    return;
                }

                // 1. 详细显示文本
                string displayText = rtzPosition.GetRTZPositionDisplayText();
                _logger.Info($"详细显示文本: {displayText}");

                // 2. 简化显示文本
                string simpleText = rtzPosition.GetRTZPositionSimpleText();
                _logger.Info($"简化显示文本: {simpleText}");

                // 3. JSON格式文本
                string jsonText = rtzPosition.GetRTZPositionJsonText();
                _logger.Info($"JSON格式文本:\n{jsonText}");

                // 4. 诊断信息
                string diagnosticInfo = rtzPosition.GetDiagnosticInfo();
                _logger.Info($"诊断信息:\n{diagnosticInfo}");

                _logger.Info("=== 格式化显示示例完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"格式化显示示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示实时监控功能
        /// </summary>
        public static async Task DemonstrateRealTimeMonitoring()
        {
            try
            {
                _logger.Info("=== RTZ轴位置实时监控示例 ===");

                var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;

                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过实时监控示例");
                    return;
                }

                // 记录初始位置
                var initialInfo = rtzPosition.GetDetailedPositionInfo();
                _logger.Info($"初始位置: {initialInfo}");

                // 模拟实时监控（5次，每次间隔1秒）
                for (int i = 1; i <= 5; i++)
                {
                    await Task.Delay(1000);

                    var currentInfo = rtzPosition.GetDetailedPositionInfo();
                    _logger.Info($"第{i}次监控: {currentInfo}");

                    // 检查位置变化
                    if (currentInfo.TAxisStep != initialInfo.TAxisStep ||
                        currentInfo.RAxisStep != initialInfo.RAxisStep ||
                        currentInfo.ZAxisStep != initialInfo.ZAxisStep)
                    {
                        _logger.Info("检测到RTZ轴位置变化！");
                        
                        // 检查安全状态
                        if (!currentInfo.IsAllAxesSafe)
                        {
                            _logger.Warn("警告：检测到轴位置超出安全范围！");
                        }
                    }
                }

                _logger.Info("=== 实时监控示例完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"实时监控示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示与UiViewModel的集成使用
        /// </summary>
        public static void DemonstrateUiViewModelIntegration()
        {
            try
            {
                _logger.Info("=== UiViewModel集成使用示例 ===");

                // 注意：这里需要实际的UiViewModel实例，示例中使用模拟方式
                _logger.Info("演示如何在UiViewModel中使用RTZ轴位置访问器：");

                _logger.Info("代码示例：");
                _logger.Info("// 在UiViewModel中");
                _logger.Info("public int TAxisStep => SS200InterLockMain.Instance.RTZAxisPosition.CurrentTAxisStep;");
                _logger.Info("public string GetRTZDisplayText() => SS200InterLockMain.Instance.RTZAxisPosition.GetRTZPositionDisplayText();");
                _logger.Info("public bool IsRTZDataValid => SS200InterLockMain.Instance.RTZAxisPosition.IsRTZPositionDataValid;");

                // 直接通过SS200InterLockMain访问（模拟UiViewModel的使用方式）
                var interlock = SS200InterLockMain.Instance;
                
                if (interlock.RTZAxisPosition.IsRTZPositionDataValid)
                {
                    string displayText = interlock.RTZAxisPosition.GetRTZPositionDisplayText();
                    _logger.Info($"通过统一入口获取的显示文本: {displayText}");
                    
                    bool allSafe = interlock.RTZAxisPosition.AreAllAxesInSafeRange;
                    _logger.Info($"通过统一入口检查的安全状态: {allSafe}");
                }

                _logger.Info("=== UiViewModel集成使用示例完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"UiViewModel集成使用示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static async Task RunAllExamples()
        {
            _logger.Info("开始运行RTZ轴位置访问器所有示例...");

            DemonstrateBasicAccess();
            Console.WriteLine();

            DemonstrateSafetyChecks();
            Console.WriteLine();

            DemonstrateFormattedDisplay();
            Console.WriteLine();

            await DemonstrateRealTimeMonitoring();
            Console.WriteLine();

            DemonstrateUiViewModelIntegration();

            _logger.Info("所有RTZ轴位置访问器示例运行完成");
        }
    }
}
