using System.Collections.Generic;

namespace Zishan.SS200.Cmd.Models
{
    /// <summary>
    /// 批量命令项
    /// </summary>
    public class BatchCommandItem
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 命令名称
        /// </summary>
        public string CommandName { get; set; }
        
        /// <summary>
        /// 执行后延迟时间(毫秒)
        /// </summary>
        public int DelayAfterExecution { get; set; } = 1000; // 默认延迟1秒
        
        /// <summary>
        /// 动态参数字典
        /// </summary>
        public Dictionary<string, ushort> DynamicParameters { get; set; } = new Dictionary<string, ushort>();
    }

    /// <summary>
    /// 批量命令序列
    /// </summary>
    public class BatchCommandSequence
    {
        /// <summary>
        /// 序列名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 序列描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 循环执行次数, 1表示执行一次(不循环), 0值会被忽略(当IsInfiniteLoop为true时)
        /// </summary>
        public int LoopCount { get; set; } = 1;
        
        /// <summary>
        /// 是否无限循环
        /// </summary>
        public bool IsInfiniteLoop { get; set; } = false;
        
        /// <summary>
        /// 循环间隔延迟时间(毫秒)
        /// </summary>
        public int LoopDelayMs { get; set; } = 2000; // 默认循环间隔2秒
        
        /// <summary>
        /// 命令列表
        /// </summary>
        public List<BatchCommandItem> Commands { get; set; } = new List<BatchCommandItem>();
    }

    /// <summary>
    /// 批量命令配置
    /// </summary>
    public class BatchCommandConfig
    {
        /// <summary>
        /// 批量序列列表
        /// </summary>
        public List<BatchCommandSequence> BatchSequences { get; set; } = new List<BatchCommandSequence>();
    }
} 