# GetFirstFinishedChamberName方法修复说明

## 问题描述

当设置`CurRunRecipeInfo.Top=7, CurRunRecipeInfo.Bottom=6`运行时，最后一片在B腔体，但程序不继续运行下去。调试时发现`CurPLcsignalSimulation.GetFirstFinishedChamberName(CurRunRecipeInfo.RecipeName)`返回`EnuChamberName.CHA`而不是`EnuChamberName.CHB`。

## 问题分析

### 原始方法逻辑问题

原始的`GetFirstFinishedChamberName`方法根据完成时间判断最早完成的腔体：

```csharp
public EnuChamberName GetFirstFinishedChamberName(string recipeFileName)
{
    // 判断启用的腔体
    bool isEnableCha = recipeFileName.Contains("A");
    bool isEnableChb = recipeFileName.Contains("B");
    bool isEnableChc = recipeFileName.Contains("C");

    DateTime earliestTime = DateTime.MaxValue;
    
    // 返回最早完成的腔体
    if (ChaProcessFinished && isEnableCha && ChaDateTimeProcessFinished < earliestTime)
    {
        result = EnuChamberName.CHA;
    }
    // ...
}
```

### 问题根因

1. **多腔体配方场景**：当使用"配方AB"或"配方ABC"时，多个腔体都启用
2. **时间优先逻辑**：方法总是返回最早完成的腔体，即使该腔体已经没有晶圆
3. **使用场景不匹配**：在TransferWaferViewModel中，需要的是"有晶圆且已完成"的腔体，而不是"最早完成"的腔体

## 修复方案

### 1. 删除原方法，替换为GetFinishedChamberWithWafer方法

**重要说明**：由于`GetFirstFinishedChamberName`方法只有1处调用（在新方法中作为回退逻辑），且该回退逻辑实际上不会被执行到，因此删除原方法，简化代码结构。

```csharp
/// <summary>
/// 获取当前有晶圆且已完成处理的腔体名称（优先返回有晶圆的腔体）
/// </summary>
/// <param name="recipeFileName">配方文件名</param>
/// <returns>有晶圆且已完成处理的腔体名称</returns>
public EnuChamberName GetFinishedChamberWithWafer(string recipeFileName)
{
    // 判断哪些腔体启用
    bool isEnableCha = recipeFileName.Contains("A");
    bool isEnableChb = recipeFileName.Contains("B");
    bool isEnableChc = recipeFileName.Contains("C");

    // 优先检查有晶圆且已完成的腔体
    if (isEnableChb && ChbHasWafer && (ChbProcessFinished ?? false))
    {
        return EnuChamberName.CHB;
    }
    if (isEnableCha && ChaHasWafer && (ChaProcessFinished ?? false))
    {
        return EnuChamberName.CHA;
    }
    if (isEnableChc && ChcHasWafer && (ChcProcessFinished ?? false))
    {
        return EnuChamberName.CHC;
    }

    // 如果没有找到有晶圆且已完成的腔体，返回默认值
    return EnuChamberName.LoadLock;
}
```

### 2. 修改调用逻辑

在TransferWaferViewModel.cs中，将需要考虑晶圆状态的调用点改为使用`GetFinishedChamberWithWafer`：

**修改的调用点**：
- 第2495行：CHA相关调用
- 第2589行：CHB相关调用
- 第2683行：CHC相关调用
- 第2752行：CHA相关调用（处理剩余工艺）
- 第2778行：CHB相关调用（处理剩余工艺）
- 第2802行：CHC相关调用（处理剩余工艺）

```csharp
// 修改前
CurPLcsignalSimulation.GetFirstFinishedChamberName(CurRunRecipeInfo.RecipeName) == EnuChamberName.CHB

// 修改后
CurPLcsignalSimulation.GetFinishedChamberWithWafer(CurRunRecipeInfo.RecipeName) == EnuChamberName.CHB
```

## 修复效果

### 修复前的问题场景

1. 配方"配方AB"启用CHA和CHB
2. CHA先完成处理，ChaDateTimeProcessFinished较早
3. CHB后完成处理，但CHB仍有晶圆
4. `GetFirstFinishedChamberName`返回CHA（因为时间更早）
5. 条件判断失败，程序不继续运行

### 修复后的正确逻辑

1. 配方"配方AB"启用CHA和CHB
2. `GetFinishedChamberWithWafer`优先检查有晶圆且已完成的腔体
3. 如果CHB有晶圆且已完成，返回CHB
4. 条件判断成功，程序继续运行

## 测试建议

1. **单腔体配方测试**：使用"配方A"、"配方B"、"配方C"测试
2. **多腔体配方测试**：使用"配方AB"、"配方AC"、"配方BC"、"配方ABC"测试
3. **边界条件测试**：
   - 所有腔体都没有晶圆
   - 所有腔体都有晶圆但未完成
   - 部分腔体有晶圆且已完成

## 相关文件

- `Zishan.SS200.Cmd/Models/IR400/PLcsignalSimulation.cs` - 新增GetFinishedChamberWithWafer方法
- `Zishan.SS200.Cmd/ViewModels/TransferWaferViewModel.cs` - 修改调用逻辑
- `Zishan.SS200.Cmd/Configs/Recipe/IR400RecipeNames.json` - 配方配置参考
