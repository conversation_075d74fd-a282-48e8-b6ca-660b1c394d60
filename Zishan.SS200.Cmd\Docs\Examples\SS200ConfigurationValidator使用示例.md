# SS200ConfigurationValidator 使用示例

## 概述

`SS200ConfigurationValidator` 是一个专门用于验证SS200InterLockMain中读取到的数据是否与JSON配置文件一致的验证器。它可以帮助确保系统配置的一致性和正确性。

## 功能特性

### ✅ **验证功能**
- **AlarmCode验证**：验证报警代码的一致性
- **SubsystemConfigure验证**：验证配置参数的一致性
- **完整性检查**：检查JSON中的项目是否都在代码中有对应，代码中的项目是否都在JSON中存在

### 📄 **数据导出功能**
- **IOInterface数据导出**：导出当前IO接口数据为JSON文件
- **SubsystemStatus数据导出**：导出当前子系统状态数据为JSON文件

### 🔍 **验证时机**
- **启动时自动验证**：程序启动时自动执行验证
- **手动调用验证**：可以手动调用验证方法

## 使用方法

### 1. 启动时自动验证

验证器已经集成到应用程序启动流程中，会在程序启动时自动执行：

```csharp
// 在App.xaml.cs的OnInitialized方法中自动调用
protected override void OnInitialized()
{
    // ... 其他初始化代码

    // 启动时进行配置验证
    PerformStartupValidation();
}
```

**自动验证功能**：
- 验证所有67个Robot报警代码
- 验证所有28个Robot位置参数
- 导出当前IO接口状态
- 导出子系统运行状态
- 显示验证结果消息框

### 2. 手动调用验证

```csharp
// 从IOC容器获取验证器实例
var validator = App.GetInstance<SS200ConfigurationValidator>();

// 执行完整验证
var result = validator.ValidateAll();

// 检查验证结果
if (result.IsValid)
{
    Console.WriteLine("✅ 配置验证通过");
}
else
{
    Console.WriteLine("❌ 配置验证失败");
    foreach (var error in result.Errors)
    {
        Console.WriteLine($"错误: {error}");
    }
}
```

### 3. 分别验证不同类型

```csharp
var validator = App.GetInstance<SS200ConfigurationValidator>();

// 只验证报警代码
var alarmResult = validator.ValidateAlarmCodes();

// 只验证配置参数
var configResult = validator.ValidateConfigurationParameters();
```

### 4. 导出当前数据

```csharp
var validator = App.GetInstance<SS200ConfigurationValidator>();

// 导出当前数据为JSON文件
validator.ExportCurrentDataToJson();

// 导出的文件位置：
// - IOInterface数据：Configs/SS200/Exported/IOInterface_Current.json
// - SubsystemStatus数据：Configs/SS200/Exported/SubsystemStatus_Current.json
```

## 验证结果处理

### ValidationResult 结构

```csharp
public class ValidationResult
{
    public bool IsValid { get; set; }           // 验证是否通过
    public List<string> Errors { get; set; }   // 错误列表
    public List<string> Warnings { get; set; } // 警告列表
    public List<string> InfoMessages { get; set; } // 信息列表
    
    public string GetSummary() // 获取验证结果摘要
}
```

### 结果展示

```csharp
var result = validator.ValidateAll();

// 显示详细结果
Console.WriteLine(result.GetSummary());

// 输出示例：
// 验证结果: ✅ 通过
// 信息 (3):
//   • Robot报警代码验证完成，检查了 7 个关键报警代码
//   • Robot位置参数验证完成，检查了 10 个关键位置参数
//   • 配置参数验证完成
```

## 验证内容详解

### 1. Robot报警代码验证

**完整验证所有67个报警代码**：
- **RA1-RA10**: 基础系统报警（系统忙碌、报警拒绝、轴位置错误等）
- **RA11-RA20**: 运动控制报警（轴位置错误、Pin搜索、传感器检测等）
- **RA21-RA30**: 设备状态报警（Shuttle位置、Chamber状态等）
- **RA31-RA40**: 运行状态报警（Chamber运行状态、晶圆丢失等）
- **RA41-RA50**: 晶圆处理报警（晶圆丢失、放置失败等）
- **RA51-RA60**: 传感器状态报警（晶圆状态不一致、移动失败等）
- **RA61-RA67**: 高级功能报警（晶圆状态异常、运动错误等）

**验证算法**：
- 智能描述比较（忽略大小写和空格差异）
- 完整性检查（JSON和代码双向验证）
- 详细统计报告（通过、不一致、缺失数量）

### 2. Robot位置参数验证

**完整验证所有28个位置参数**：
- **RP1-RP9**: T轴位置参数（Smooth/Nose到各位置、零位）
- **RP10-RP18**: R轴位置参数（伸展面向各位置、零位）
- **RP19-RP28**: Z轴位置参数（各高度位置、零位、Pin搜索高度）

**验证算法**：
- 精确数值比较（JSON中的value与代码中的Value）
- 描述匹配验证（JSON中的description与代码中的Content）
- 单位一致性检查（确保单位信息正确）
- 完整性验证（参数完整对应检查）

### 3. 数据一致性检查

- **代码一致性**：验证代码标识符是否匹配
- **数值一致性**：验证配置参数的数值是否一致（精确比较）
- **描述一致性**：验证描述信息是否匹配（智能比较）
- **完整性检查**：验证JSON和代码中的项目是否完整对应
- **统计报告**：提供详细的验证通过、不一致、缺失统计

## 导出的JSON文件结构

### IOInterface数据结构

```json
{
  "ExportTime": "2025-01-14T10:30:00",
  "Robot": {
    "RDI1_PaddleSensor1Left": {
      "Value": false,
      "Content": "Paddle传感器1左侧"
    }
  },
  "Shuttle": {
    "SDI1_CassetteDoorUpSensor": {
      "Value": false,
      "Content": "晶圆盒门上升传感器"
    }
  },
  "ChamberA": { /* Chamber A IO数据 */ },
  "ChamberB": { /* Chamber B IO数据 */ }
}
```

### SubsystemStatus数据结构

```json
{
  "ExportTime": "2025-01-14T10:30:00",
  "Robot": {
    "RobotStatus": {
      "Value": "Idle",
      "Description": "机器人基本状态"
    },
    "TAxisSmoothDestination": {
      "Value": "CHA",
      "Description": "T轴Smooth目的地"
    }
  },
  "Shuttle": { /* Shuttle状态数据 */ },
  "ChamberA": { /* Chamber A状态数据 */ },
  "ChamberB": { /* Chamber B状态数据 */ }
}
```

## 日志记录

验证器会详细记录验证过程：

```
2025-01-14 10:30:00 [INFO] 开始启动时配置验证...
2025-01-14 10:30:01 [INFO] IOInterface数据已导出到: Configs/SS200/Exported/IOInterface_Current.json
2025-01-14 10:30:01 [INFO] SubsystemStatus数据已导出到: Configs/SS200/Exported/SubsystemStatus_Current.json
2025-01-14 10:30:02 [INFO] Robot报警代码验证完成，检查了 7 个关键报警代码
2025-01-14 10:30:02 [INFO] Robot位置参数验证完成，检查了 10 个关键位置参数
2025-01-14 10:30:02 [INFO] 配置验证完成，结果: 通过
```

## 错误处理

### 常见错误类型

1. **文件不存在**
   ```
   警告: Robot报警代码JSON文件不存在: Configs/SS200/AlarmCode/Robot/RobotErrorCodes.json
   ```

2. **数据不一致**
   ```
   警告: Robot报警代码 RA1 描述不一致: 代码中='系统忙碌拒绝', JSON中='System Busy Reject'
   ```

3. **数据缺失**
   ```
   错误: Robot报警代码 RA1 在JSON文件中不存在
   ```

### 异常处理

验证器具有完善的异常处理机制：
- 单个验证失败不会影响其他验证
- 验证异常会被记录到日志
- 用户会收到友好的错误提示
- 验证失败不会阻止程序正常运行

## 最佳实践

1. **定期验证**：建议在配置文件更新后手动执行验证
2. **查看日志**：验证结果会详细记录在日志中，便于问题排查
3. **备份配置**：在修改配置文件前先备份原文件
4. **版本控制**：将配置文件纳入版本控制，便于追踪变更

## 扩展功能

如需添加新的验证项目，可以：

1. 在 `ValidateAlarmCodes` 方法中添加新的报警代码验证
2. 在 `ValidateConfigurationParameters` 方法中添加新的配置参数验证
3. 在数据导出方法中添加新的数据提取逻辑

验证器采用模块化设计，便于扩展和维护。
