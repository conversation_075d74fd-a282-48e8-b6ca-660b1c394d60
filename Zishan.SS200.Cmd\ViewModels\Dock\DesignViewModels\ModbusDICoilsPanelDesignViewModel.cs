﻿using System;
using System.Collections.ObjectModel;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.ViewModels.Dock;

namespace Zishan.SS200.Cmd.ViewModels.Dock.DesignViewModels
{
    /// <summary>
    /// 设计时视图模型，用于XAML设计器中显示模拟数据
    /// </summary>
    public class ModbusDICoilsPanelDesignViewModel : ModbusDICoilsPanelViewModel
    {
        public bool IsShuttleCoilReadingSupported { get; } = true;
        public bool IsRobotCoilReadingSupported { get; } = true;
        public bool IsChaCoilReadingSupported { get; } = false;
        public bool IsChbCoilReadingSupported { get; } = true;

        // 添加Robot报警寄存器设计数据
        public ObservableCollection<ModbusRegister> RobotAlarmRegisters { get; } = new ObservableCollection<ModbusRegister>
        {
            // T轴报警错误码
            new ModbusRegister { Address = 0, Value = 0x0001, Description = "T轴报警错误码" },
            // R轴报警错误码
            new ModbusRegister { Address = 1, Value = 0x0002, Description = "R轴报警错误码" },
            // Z轴报警错误码
            new ModbusRegister { Address = 2, Value = 0x0003, Description = "Z轴报警错误码" },

            // T轴位置(高位)
            new ModbusRegister { Address = 3, Value = 0x0000, Description = "T轴位置高位", Combinevalue = 100 },
            // T轴位置(低位)
            new ModbusRegister { Address = 4, Value = 0x0064, Description = "T轴位置低位" },

            // R轴位置(高位)
            new ModbusRegister { Address = 5, Value = 0x0000, Description = "R轴位置高位", Combinevalue = 200 },
            // R轴位置(低位)
            new ModbusRegister { Address = 6, Value = 0x00C8, Description = "R轴位置低位" },

            // Z轴位置(高位)
            new ModbusRegister { Address = 7, Value = 0x0000, Description = "Z轴位置高位", Combinevalue = 300 },
            // Z轴位置(低位)
            new ModbusRegister { Address = 8, Value = 0x012C, Description = "Z轴位置低位" },

            // Pin Search
            new ModbusRegister { Address = 9, Value = 0x0000, Description = "PinSearch P1 H" , Combinevalue = 4096},
            new ModbusRegister { Address = 10, Value = 0x1000, Description = "PinSearch P1 L" },
            new ModbusRegister { Address = 11, Value = 0x0000, Description = "PinSearch P2 H" , Combinevalue = 4101},
            new ModbusRegister { Address = 12, Value = 0x1005, Description = "PinSearch P2 L" },
        };

        public ObservableCollection<ModbusCoil> ShuttleInputCoils { get; } = new ObservableCollection<ModbusCoil>
        {
            new ModbusCoil { Address = 0, Title = "传感器1", Description = "传感器1的详细描述", Coilvalue = true },
            new ModbusCoil { Address = 1, Title = "传感器2", Description = "传感器2的详细描述", Coilvalue = false },
            new ModbusCoil { Address = 2, Title = "位置检测", Description = "位置检测传感器", Coilvalue = true },
            new ModbusCoil { Address = 3, Title = "急停按钮", Description = "急停按钮状态", Coilvalue = false }
        };

        public ObservableCollection<ModbusCoil> RobotInputCoils { get; } = new ObservableCollection<ModbusCoil>
        {
            new ModbusCoil { Address = 10, Title = "机器人就绪", Description = "机器人就绪状态", Coilvalue = true },
            new ModbusCoil { Address = 11, Title = "机器人运行", Description = "机器人运行状态", Coilvalue = true },
            new ModbusCoil { Address = 12, Title = "机器人错误", Description = "机器人错误状态", Coilvalue = false },
            new ModbusCoil { Address = 13, Title = "机器人暂停", Description = "机器人暂停状态", Coilvalue = false }
        };

        public ObservableCollection<ModbusCoil> ChaInputCoils { get; } = new ObservableCollection<ModbusCoil>
        {
            new ModbusCoil { Address = 20, Title = "CHA门开关", Description = "CHA门开关状态", Coilvalue = false },
            new ModbusCoil { Address = 21, Title = "CHA气压", Description = "CHA气压状态", Coilvalue = true },
            new ModbusCoil { Address = 22, Title = "CHA温度", Description = "CHA温度状态", Coilvalue = true },
            new ModbusCoil { Address = 23, Title = "CHA报警", Description = "CHA报警状态", Coilvalue = false }
        };

        public ObservableCollection<ModbusCoil> ChbInputCoils { get; } = new ObservableCollection<ModbusCoil>
        {
            new ModbusCoil { Address = 30, Title = "CHB门开关", Description = "CHB门开关状态", Coilvalue = true },
            new ModbusCoil { Address = 31, Title = "CHB气压", Description = "CHB气压状态", Coilvalue = true },
            new ModbusCoil { Address = 32, Title = "CHB温度", Description = "CHB温度状态", Coilvalue = false },
            new ModbusCoil { Address = 33, Title = "CHB报警", Description = "CHB报警状态", Coilvalue = false }
        };

        /// <summary>
        /// T轴步进值
        /// </summary>
        public int TAxisStep => RobotAlarmRegisters.Count > 3 ? RobotAlarmRegisters[3].Combinevalue : 0;

        /// <summary>
        /// R轴步进值
        /// </summary>
        public int RAxisStep => RobotAlarmRegisters.Count > 5 ? RobotAlarmRegisters[5].Combinevalue : 0;

        /// <summary>
        /// Z轴步进值
        /// </summary>
        public int ZAxisStep => RobotAlarmRegisters.Count > 7 ? RobotAlarmRegisters[7].Combinevalue : 0;

        /// <summary>
        /// T旋转轴角度值（度）
        /// 转换公式：步进值/100000*360
        /// </summary>
        public double TAxisDegree => TAxisStep / 100000.0 * 360.0;

        /// <summary>
        /// R伸缩轴长度值（mm）
        /// 转换公式：L = Sin(步进值/50000*360)*2*208.96
        /// </summary>
        public double RAxisLength => Math.Sin((RAxisStep / 50000.0) * (Math.PI / 180) * 360) * 2 * 208.96;

        /// <summary>
        /// Z上下轴高度值（mm）
        /// 转换公式：步进值/1000*5
        /// </summary>
        public double ZAxisHeight => ZAxisStep / 1000.0 * 5.0;

        /// <summary>
        /// Robot Smooth 做Pin Search 获取到的值，已经计算中心值
        /// </summary>
        public int SmoothBasePinSearchValue { get; set; } = 100;

        /// </summary>
        public int NoseBasePinSearchValue { get; set; } = 200;
    }
}