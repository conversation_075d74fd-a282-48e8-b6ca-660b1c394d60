using System.ComponentModel;

namespace Zishan.SS200.Cmd.Enums.SS200.IOInterface.Shuttle
{
    /// <summary>
    /// Shuttle阀门类型枚举
    /// 用于区分不同的Shuttle阀门
    /// </summary>
    public enum ShuttleValveType
    {
        /// <summary>
        /// Shuttle ISO阀门 (SSD14-SSD15)
        /// </summary>
        [Description("Shuttle ISO阀门")]
        ShuttleIso = 1,

        /// <summary>
        /// Shuttle XV阀门 (SSD16-SSD17)
        /// </summary>
        [Description("Shuttle XV阀门")]
        ShuttleXv = 2,

        /// <summary>
        /// Shuttle回填阀门 (SSD18-SSD19)
        /// </summary>
        [Description("Shuttle回填阀门")]
        ShuttleBackfill = 3,

        /// <summary>
        /// 负载锁排气阀门 (SSD20-SSD21)
        /// </summary>
        [Description("负载锁排气阀门")]
        LoadlockBleed = 4,

        /// <summary>
        /// 负载锁回填阀门 (SSD22-SSD23)
        /// </summary>
        [Description("负载锁回填阀门")]
        LoadlockBackfill = 5
    }
}
