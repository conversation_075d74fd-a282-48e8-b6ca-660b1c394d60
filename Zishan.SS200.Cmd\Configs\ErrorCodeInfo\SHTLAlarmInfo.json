{"0000": {"cause": "", "kind": "成功"}, "0001": {"cause": "I2C通讯错误或者RS485通讯失败(超时, 指令错误等)", "kind": "硬件错误"}, "0002": {"cause": "执行指令时参数长度或者数值不对", "kind": "参数错误"}, "0003": {"cause": "请检查运行指令是否支持", "kind": "未定义指令"}, "0004": {"cause": "", "kind": "SA1 system busy command reject"}, "0005": {"cause": "", "kind": "SA2 system alarm command reject"}, "0006": {"cause": "", "kind": "SA3 cassette nest move time out"}, "0007": {"cause": "", "kind": "SA4 cassette nest move speed too high"}, "0008": {"cause": "", "kind": "SA5 cassette nest position condition failure"}, "0009": {"cause": "", "kind": "SA6 shuttle move time out"}, "000A": {"cause": "", "kind": "SA7 shuttle move too fast"}, "000B": {"cause": "", "kind": "SA8 shuttle up down position condition failure"}, "000C": {"cause": "", "kind": "SA9 shuttle rotate time out"}, "000D": {"cause": "", "kind": "SA10 shuttle rotate position condition failure"}, "000E": {"cause": "", "kind": "SA11 cassette door move time out"}, "000F": {"cause": "", "kind": "SA12 cassette door move too fast"}, "0010": {"cause": "", "kind": "SA13 cassette door position condition failure"}, "0011": {"cause": "", "kind": "SA14 open CV time out"}, "0012": {"cause": "", "kind": "SA15 CV position status failure"}, "0013": {"cause": "", "kind": "SA16 close CV time out"}, "0014": {"cause": "", "kind": "SA17 open XV time out"}, "0015": {"cause": "", "kind": "SA18 XV position status failure"}, "0016": {"cause": "", "kind": "SA19 close XV time out"}}