---
description: 本规则文件提供了全面的C#最佳实践、编码标准和常用模式指南，用于编写可维护、高性能和安全的代码。
globs: **/*.cs
---
# C#最佳实践和编码标准

本文档提供了全面的C#最佳实践、编码标准和常用模式指南，用于编写可维护、高性能和安全的代码。它涵盖了C#开发的各个方面，包括代码组织、常用模式、性能考虑、安全最佳实践、测试方法、常见陷阱和工具使用。

**库信息：**
- 名称：c-sharp
- 标签：language（语言）, microsoft（微软）, dotnet（.NET）, backend（后端）

## 1. 代码组织和结构

良好的代码组织对于可维护性、可扩展性和协作至关重要。以下是组织C#代码的一些最佳实践：

### 1.1. 目录结构最佳实践

*   **项目根目录：** 包含解决方案文件（.sln）和项目目录。
*   **项目目录：** 包含项目文件（.csproj）、源代码和其他项目相关文件。
    *   `src/`：包含主要源代码。
        *   `Models/`：数据模型和DTO（数据传输对象）。
        *   `Services/`：业务逻辑和服务类。
        *   `Controllers/`：API控制器（如适用）。
        *   `Repositories/`：数据访问逻辑。
        *   `Utilities/`：辅助类和实用函数。
        *   `Exceptions/`：自定义异常定义。
        *   `Interfaces/`：接口定义，用于抽象。
        *   `Configuration/`：配置相关类。
    *   `tests/`：包含单元测试、集成测试和端到端测试。
    *   `docs/`：项目文档。
    *   `build/`：构建脚本和配置文件。
    *   `Properties/`：程序集信息和项目设置。

示例：

MyProject/
├── MyProject.sln
├── MyProject/
│   ├── MyProject.csproj
│   ├── src/
│   │   ├── Models/
│   │   ├── Services/
│   │   ├── Controllers/
│   │   ├── Repositories/
│   │   ├── Utilities/
│   │   ├── Exceptions/
│   │   ├── Interfaces/
│   │   └── Configuration/
│   ├── Properties/
│   │   └── AssemblyInfo.cs
│   └── appsettings.json
├── MyProject.Tests/
│   ├── MyProject.Tests.csproj
│   └── UnitTests/
└── README.md

### 1.2. 文件命名约定

*   **类：** 使用PascalCase（例如，`MyClass.cs`）
*   **接口：** 使用IPascalCase（例如，`IMyInterface.cs`）
*   **枚举：** 使用PascalCase（例如，`MyEnum.cs`）
*   **结构体：** 使用PascalCase（例如，`MyStruct.cs`）
*   **委托：** 使用PascalCase（例如，`MyDelegate.cs`）
*   **配置文件：** appsettings.json, config.xml
*   **测试文件：** `MyClassTests.cs`

### 1.3. 模块组织

*   **命名空间：** 使用命名空间来分组相关的类和接口。遵循一致的命名空间命名约定（例如，`CompanyName.ProjectName.ModuleName`）。
*   **程序集：** 将大型项目分割成多个程序集以改善构建时间、减少依赖关系并实现代码重用。在创建程序集时考虑功能或领域边界。
*   **NuGet包：** 使用NuGet包来管理依赖项并在项目间共享代码。

### 1.4. 组件架构

*   **分层架构：** 将应用程序分为不同的层（如表示层、业务逻辑层、数据访问层）以促进关注点分离和可测试性。
*   **微服务架构：** 对于大型和复杂的应用程序，考虑使用微服务架构，将应用程序组成小型、独立的服务。
*   **依赖注入（DI）：** 使用DI来管理组件之间的依赖关系，提高可测试性和可维护性。流行的DI容器包括Autofac、Ninject和Microsoft.Extensions.DependencyInjection。

### 1.5. 代码拆分策略

*   **按功能：** 将相关功能的代码分组到单独的模块或程序集中。
*   **按层：** 基于架构层（如表示层、业务逻辑层、数据访问层）分离代码。
*   **按职责：** 将类和方法拆分成更小、更专注的工作单元。
*   **部分类：** 使用部分类将大型类拆分成多个文件以便更好地组织（谨慎使用）。

## 2. 常用模式和反模式

理解常用设计模式和反模式对于编写有效和可维护的C#代码至关重要。

### 2.1. 设计模式

*   **单例模式（Singleton）：** 确保一个类只有一个实例，并提供对它的全局访问点。
*   **工厂模式（Factory）：** 提供一个创建对象的接口，而无需指定它们的具体类。
*   **抽象工厂（Abstract Factory）：** 提供一个创建相关对象族的接口，而无需指定它们的具体类。
*   **建造者模式（Builder）：** 将复杂对象的构建与其表示分离，使同样的构建过程可以创建不同的表示。
*   **观察者模式（Observer）：** 定义对象之间的一对多依赖关系，当一个对象改变状态时，其所有依赖者都会收到通知并自动更新。
*   **策略模式（Strategy）：** 定义一系列算法，封装每个算法，并使它们可以互换。策略模式使算法可以独立于使用它的客户端而变化。
*   **依赖注入（DI）：** 一种技术，通过它一个对象（或静态方法）提供另一个对象的依赖。这有助于解耦组件。
*   **仓储模式（Repository）：** 在领域和数据映射层之间进行调解，表现得像内存中的领域对象集合。
*   **工作单元（Unit of Work）：** 维护受业务事务影响的对象列表，并协调更改的写出。
*   **异步编程模式（TAP, EAP, APM）：** 使用async/await高效处理异步操作。

### 2.2. 常见任务的推荐方法

*   **字符串操作：** 在循环中使用`StringBuilder`进行高效的字符串连接。
*   **文件I/O：** 使用`using`语句或`try-finally`块确保正确释放文件资源。
*   **数据访问：** 使用Entity Framework Core或Dapper等ORM简化数据访问。
*   **异步操作：** 使用`async`和`await`进行非阻塞异步操作。
*   **配置管理：** 使用`Microsoft.Extensions.Configuration`管理应用程序配置。
*   **日志记录：** 使用Serilog或NLog等日志框架进行结构化日志记录。

### 2.3. 反模式和代码异味

*   **上帝类（God Class）：** 一个做太多事情且职责过多的类。
*   **长方法（Long Method）：** 过长且复杂的方法。
*   **特性依恋（Feature Envy）：** 一个方法访问其他对象的数据比访问自己的数据更多。
*   **霰弹式修改（Shotgun Surgery）：** 对代码的一处修改需要对多处进行更改。
*   **数据泥团（Data Clump）：** 在多个地方一起出现的数据组。
*   **基本类型偏执（Primitive Obsession）：** 使用基本类型而不是为领域概念创建自定义类。
*   **switch语句（而非多态）：** 使用大型switch语句而不是利用多态。
*   **魔法数字/字符串：** 直接在代码中硬编码值而不是使用常量或配置设置。
*   **忽略异常：** 捕获异常但未正确处理。
*   **空catch块：** 捕获异常但什么都不做。
*   **过度注释：** 编写从代码本身就很明显的过多注释。
*   **死代码：** 永远不会执行的代码。

### 2.4. 状态管理最佳实践

*   **无状态服务：** 尽可能将服务设计为无状态，以提高可扩展性和可靠性。
*   **会话状态：** 谨慎使用会话状态，仅在必要时使用。考虑在Web应用程序中使用分布式缓存来存储会话状态。
*   **缓存：** 使用缓存通过在内存中存储频繁访问的数据来提高性能。
*   **Redux/Flux：** 对于复杂的UI应用程序，考虑使用Redux或Flux等状态管理库。
*   **不可变数据结构：** 使用不可变数据结构来简化状态管理并防止意外的副作用。

### 2.5. 错误处理模式

*   **Try-Catch-Finally：** 使用`try-catch-finally`块处理异常并确保正确的资源清理。
*   **异常筛选器：** 使用异常筛选器基于特定条件捕获特定异常。
*   **自定义异常：** 为应用程序中的特定错误条件创建自定义异常类型。
*   **记录异常：** 记录异常时提供足够的上下文以帮助调试。
*   **优雅降级：** 优雅地处理错误并向用户提供信息性错误消息。
*   **早抛出，晚捕获：** 尽早检测错误并在更高层次处理异常。

## 3. 性能考虑

优化C#代码的性能对于创建响应迅速和高效的应用程序至关重要。

### 3.1. 优化技术

*   **避免装箱和拆箱：** 值类型的装箱和拆箱可能代价高昂。使用泛型来避免装箱和拆箱。
*   **适当使用值类型：** 对于小型、不可变的数据结构，值类型（结构体）可能比引用类型（类）更高效。
*   **最小化对象分配：** 对象分配可能代价高昂。尽可能重用对象。
*   **使用`StringBuilder`进行字符串连接：** 特别是在循环中，`StringBuilder`比使用`+`运算符进行字符串连接更高效。
*   **优化LINQ查询：** 谨慎使用LINQ，避免不必要的迭代或计算。考虑使用`AsParallel()`进行大型集合的并行处理（谨慎使用，因为并行性会增加复杂性）。
*   **使用异步编程：** 使用`async`和`await`避免阻塞主线程并提高响应性。
*   **避免过度锁定：** 最小化锁的使用以防止争用并提高并发性。
*   **使用延迟初始化：** 仅在需要时初始化对象，以避免不必要的初始化开销。
*   **分析代码：** 使用分析工具识别性能瓶颈并相应地进行优化。Visual Studio Profiler、dotTrace和PerfView是不错的选择。

### 3.2. 内存管理考虑

*   **垃圾回收：** 理解垃圾回收器的工作原理，避免产生过多垃圾。
*   **释放资源：** 实现`IDisposable`接口并使用`using`语句确保正确释放资源（如文件流、数据库连接）。
*   **弱引用：** 使用弱引用来持有对象的引用而不阻止它们被垃圾回收。
*   **对象池：** 使用对象池来重用对象并减少分配开销。
*   **大对象堆（LOH）：** 注意大对象堆，避免不必要地分配大对象。

### 3.3. 渲染优化（如适用）

*   **UI虚拟化：** 使用UI虚拟化仅渲染大型列表或网格中的可见项。
*   **减少过度绘制：** 最小化像素被重复绘制的次数。
*   **批量渲染：** 批量处理渲染操作以减少绘制调用的次数。
*   **使用硬件加速：** 使用硬件加速将渲染任务卸载到GPU。

### 3.4. 包大小优化（如适用）

*   **树摇（Tree Shaking）：** 从包中移除未使用的代码。
*   **代码压缩：** 压缩代码以减小其大小。
*   **代码压缩：** 使用Gzip或Brotli压缩代码。
*   **图像优化：** 优化图像以减小其大小而不牺牲质量。

### 3.5. 延迟加载策略

*   **延迟初始化：** 使用`Lazy<T>`仅在访问对象时初始化它们。
*   **虚拟代理：** 使用虚拟代理仅在需要时加载相关数据。
*   **显式加载：** 使用Entity Framework Core中的`Include`等方法显式加载相关数据。

## 4. 安全最佳实践

安全性应该是C#开发中的主要关注点，以防止漏洞和攻击。

### 4.1. 常见漏洞和预防

*   **SQL注入：** 参数化数据库查询以防止SQL注入攻击。
*   **跨站脚本（XSS）：** 对用户输入进行编码以防止XSS攻击。
*   **跨站请求伪造（CSRF）：** 使用防伪令牌防止CSRF攻击。
*   **身份验证和授权漏洞：** 实现安全的身份验证和授权机制。 