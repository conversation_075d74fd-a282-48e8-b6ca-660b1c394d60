# Chamber报警代码访问器完成说明

## 概述

已成功完成SS200InterLockMain中所有Chamber报警代码的访问器定义，共计65个报警代码（PAC1-PAC65）。

## 完成的访问器列表

### 系统状态报警 (PAC1-PAC3)
- `PAC1_SystemAbnormalReject` - chamber system status abnormal, command reject
- `PAC2_SystemBusyReject` - CHX run status or robot run status is busy, command reject
- `PAC3_RunProcessingReject` - CHX run status is processing, command reject

### 压力控制报警 (PAC4, PAC7, PAC21, PAC25, PAC39-PAC44)
- `PAC4_DeltaPressureOutOfSetpoint` - delta pressure about CHX and loadlock out of setpoint
- `PAC7_PressureNotVacuumSlitDoorError` - chamber pressure not at vacuum status, can not open slit door
- `PAC21_ForelineNotVacuumISOError` - foreline not at vacuum status, can not open ISO valve
- `PAC25_NoProcessVacuumCMError` - chamber no process vacuum, can not open CM valve
- `PAC39_CVValveNotOpenPressureError` - CV valve status is no opened, pressure control failure
- `PAC40_CMValveNotOpenPressureError` - CM valve not at open position, pressure control failure
- `PAC41_NoGasValveOpenPressureError` - no open gas valve, can not control pressure
- `PAC42_PressureServoTimeout` - pressure servo time out
- `PAC43_PressureFlowFault` - pressure flow fault
- `PAC44_PressureUnstable` - pressure unstable

### 狭缝门控制报警 (PAC5-PAC9, PAC17-PAC18, PAC48)
- `PAC5_SlitDoorSensorAbnormal` - slit door sensor position status abnormal
- `PAC6_SlitDoorSensorFailure` - slit door sensor failure or system error
- `PAC8_SlitDoorOpenTimeout` - slit door open time out
- `PAC9_SlitDoorOpenTooFast` - slit door open too fast
- `PAC17_RobotRAxisSlitDoorError` - robot R-axis not at right position, slit door motion failure
- `PAC18_RobotNotIdleSlitDoorError` - robot run status is not idle, slit door motion failure
- `PAC48_SlitDoorNotCloseRFError` - slit door not close, can not turn on RF

### 升降针控制报警 (PAC10-PAC16)
- `PAC10_LiftPinSensorAbnormal` - lift pin sensor position status abnormal
- `PAC11_LiftPinCylinderBetween` - lift pin sensor show cylinder is between, lift pin motion failure
- `PAC12_LiftPinOpenTimeout` - lift pin open time out
- `PAC13_LiftPinOpenTooFast` - lift pin open too fast
- `PAC14_LiftPinSensorFailure` - lift pin sensor failure or system error
- `PAC15_RobotRAxisLiftPinError` - robot R-axis not at right position, lift pin motion failure
- `PAC16_RobotNotIdleLiftPinError` - robot run status is not idle, lift pin motion failure

### 阀门控制报警 (PAC19-PAC26)
- `PAC19_ThrottleValveSensorFailure` - throttle valve position sensor failure or flag position error
- `PAC20_ThrottleValveTimeout` - throttle valve motion time out
- `PAC22_ISOValveSensorFailure` - chamber ISO valve sensor failure or position error
- `PAC23_CVValveOpenTimeout` - CV valve open time out
- `PAC24_ISONotOpenCMError` - ISO valve not at open position, can not open CM valve
- `PAC26_CMNotOpenGasError` - CM valve not at open position, can not open gas valve

### 气体控制报警 (PAC27-PAC38)
- `PAC27_Gas1ServoTimeout` - gas 1 servo time out
- `PAC28_Gas2ServoTimeout` - gas 2 servo time out
- `PAC29_Gas3ServoTimeout` - gas 3 servo time out
- `PAC30_Gas4ServoTimeout` - gas 4 servo time out
- `PAC31_Gas1FlowFault` - gas1 flow fault
- `PAC32_Gas2FlowFault` - gas2 flow fault
- `PAC33_Gas3FlowFault` - gas3 flow fault
- `PAC34_Gas4FlowFault` - gas4 flow fault
- `PAC35_Gas1FlowUnstable` - gas1 flow unstable
- `PAC36_Gas2FlowUnstable` - gas2 flow unstable
- `PAC37_Gas3FlowUnstable` - gas3 flow unstable
- `PAC38_Gas4FlowUnstable` - gas4 flow unstable

### 温度控制报警 (PAC45-PAC47)
- `PAC45_TemperatureServoTimeout` - temperature servo time out
- `PAC46_TemperatureOutOfSetpoint` - temperature out of setpoint
- `PAC47_TemperatureUnstable` - temperature unstable

### RF功率控制报警 (PAC49-PAC64)
- `PAC49_NoVacuumRFOff` - chamber no vacuum, RF off
- `PAC50_ISONotOpenRFError` - ISO valve not open, can not turn on RF
- `PAC51_CMNotOpenRFError` - CM valve not at open position, can not turn on RF
- `PAC52_NoPlasmaLeftHead` - no plasma was detected on CHX left head
- `PAC53_NoPlasmaRightHead` - no plasma was detected on CHX right head
- `PAC54_RF1PowerForwardTimeout` - RF1 power forward to setpoint time out
- `PAC55_RF1ReflectorPowerOutOfLimit` - RF1 reflector power out of limit
- `PAC56_RF2PowerForwardTimeout` - RF2 power forward to setpoint time out
- `PAC57_RF2ReflectorPowerOutOfLimit` - RF2 reflector power out of limit
- `PAC58_RF1ForwardPowerOutOfSetpoint` - RF1 forward power out of setpoint
- `PAC59_RF2ForwardPowerOutOfSetpoint` - RF2 forward power out of setpoint
- `PAC60_RF1ForwardPowerUnstable` - RF1 forward power output unstable
- `PAC61_RF2ForwardPowerUnstable` - RF2 forward power output unstable
- `PAC62_RFOffFailure` - RF off failure
- `PAC63_RF1ReflectorPowerUnstable` - RF1 reflector power output unstable
- `PAC64_RF2ReflectorPowerUnstable` - RF2 reflector power output unstable

### 泵浦控制报警 (PAC65)
- `PAC65_ChamberPumpDownTimeout` - Chamber pump down time out

## 使用方法

### 基本用法
```csharp
// 获取单例实例
var interLock = SS200InterLockMain.Instance;

// 访问Chamber报警代码
var chamberAlarm = interLock.AlarmCode.ChamberA;

// 获取报警内容
string alarmContent = chamberAlarm.PAC1_SystemAbnormalReject.Content;
string alarmChsContent = chamberAlarm.PAC1_SystemAbnormalReject.ChsContent;

// 示例：检查系统异常报警
if (chamberAlarm.PAC1_SystemAbnormalReject.Content != null)
{
    Console.WriteLine($"PAC1报警: {chamberAlarm.PAC1_SystemAbnormalReject.Content}");
    Console.WriteLine($"中文描述: {chamberAlarm.PAC1_SystemAbnormalReject.ChsContent}");
}
```

### 批量访问
```csharp
// 测试所有报警代码访问器
ChamberAlarmAccessorTest.TestAllChamberAlarmAccessors();

// 运行简单测试
ChamberAlarmAccessorTest.RunAllTests();
```

## 实现特点

1. **线程安全**: 使用ConcurrentDictionary实现缓存，支持多线程并发访问
2. **延迟加载**: 访问器采用延迟加载模式，只在首次访问时创建
3. **缓存机制**: 已创建的访问器会被缓存，避免重复创建
4. **统一接口**: 所有访问器都返回AlarmPropertyAccessor类型，提供统一的访问接口
5. **完整覆盖**: 覆盖了所有65个Chamber报警代码，无遗漏

## 测试验证

提供了完整的测试类`ChamberAlarmAccessorTest`，可以验证：
- 所有访问器是否正常创建
- 报警内容是否正确获取
- 中文描述是否正确显示
- 异常情况的处理

## 编译状态

✅ 编译成功，返回码：0
✅ 无编译错误
✅ 可以正常使用

## 注意事项

1. 确保ChaErrorCodesProvider.Instance已正确初始化
2. 确保ChaErrorCodes.json配置文件存在且格式正确
3. 访问器返回null时需要进行空值检查
4. 建议在使用前先测试访问器的可用性

## 后续扩展

如需添加新的Chamber报警代码：
1. 在EnuChaAlarmCodes枚举中添加新的报警代码
2. 在ChaErrorCodes.json中添加对应的配置
3. 在ChamberAlarmAccessor类中添加对应的访问器属性
4. 更新测试代码以包含新的报警代码

## 完成状态

✅ 所有65个Chamber报警代码访问器已完成
✅ 测试代码已提供
✅ 文档已完善
✅ 编译成功，无错误
✅ 可以正常使用
