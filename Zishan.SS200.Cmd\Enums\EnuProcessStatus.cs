﻿using System.ComponentModel;

using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 工艺处理结果状态，未完成、工艺故障、自动完成、手动完成
    /// </summary>
    [TypeConverter(typeof(EnuProcessStatus))]
    public enum EnuProcessStatus
    {
        /// <summary>
        /// 未完成全部工艺
        /// </summary>
        [Description("UnfinishedAllProcess")]
        UnfinishedAllProcess = 0,

        /// <summary>
        /// 工艺故障，并且未完成全部工艺
        /// </summary>
        [Description("UnfinishedAllProcessByProcessFailures")]
        UnfinishedAllProcessByProcessFailures = 1,

        /// <summary>
        /// 全部工艺自动完成
        /// </summary>
        [Description("FinishedAllProcessByAuto")]
        FinishedAllProcessByAuto = 2,

        /// <summary>
        /// 全部工艺手动完成
        /// </summary>
        [Description("FinishedAllProcessByManuel")]
        FinishedAllProcessByManuel = 3,
    }
}