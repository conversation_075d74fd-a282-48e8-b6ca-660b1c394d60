﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Robot
{
    /// <summary>
    /// Robot 报警代码枚举类型
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuRobotAlarmCodes
    {
        /// <summary>
        /// Robot system status is busy, command reject
        /// </summary>
        [Description("Robot system status is busy, command reject")]
        RA1 = 0,

        /// <summary>
        /// Robot system status is alarm, command reject
        /// </summary>
        [Description("Robot system status is alarm, command reject")]
        RA2 = 1,

        /// <summary>
        /// Robot R-axis not at home position, T-axis motion error
        /// </summary>
        [Description("Robot R-axis not at home position, T-axis motion error")]
        RA3 = 2,

        /// <summary>
        /// Robot T-axis not in right position, PLS confirm to R-axis motion
        /// </summary>
        [Description("Robot T-axis not in right position, PLS confirm to R-axis motion")]
        RA4 = 3,

        /// <summary>
        /// Robot rotation time out
        /// </summary>
        [Description("Robot rotation time out")]
        RA5 = 4,

        /// <summary>
        /// Robot extension time out
        /// </summary>
        [Description("Robot extension time out")]
        RA6 = 5,

        /// <summary>
        /// Robot lift time out
        /// </summary>
        [Description("Robot lift time out")]
        RA7 = 6,

        /// <summary>
        /// CHA slit door not at open status, robot can not extend
        /// </summary>
        [Description("CHA slit door not at open status, robot can not extend")]
        RA8 = 7,

        /// <summary>
        /// CHB slit door not at open status, robot can not extend
        /// </summary>
        [Description("CHB slit door not at open status, robot can not extend")]
        RA9 = 8,

        /// <summary>
        /// Robot Z-axis not in right position, PLS confirm to R-axis motion
        /// </summary>
        [Description("Robot Z-axis not in right position, PLS confirm to R-axis motion")]
        RA10 = 9,

        /// <summary>
        /// Robot R-axis not at home position, Z-axis can not move to right position
        /// </summary>
        [Description("Robot R-axis not at home position, Z-axis can not move to right position")]
        RA11 = 10,

        /// <summary>
        /// Robot T-axis not in right position, Z-axis can not move to right position
        /// </summary>
        [Description("Robot T-axis not in right position, Z-axis can not move to right position")]
        RA12 = 11,

        /// <summary>
        /// Shuttle position status not at right position, can not do pin search
        /// </summary>
        [Description("Shuttle position status not at right position, can not do pin search")]
        RA13 = 12,

        /// <summary>
        /// paddle hit to pin ball, pin search failure
        /// </summary>
        [Description("paddle hit to pin ball, pin search failure")]
        RA14 = 13,

        /// <summary>
        /// pin ball status failure, pin search failure
        /// </summary>
        [Description("pin ball status failure, pin search failure")]
        RA15 = 14,

        /// <summary>
        /// Can not find pin ball, pin search filure
        /// </summary>
        [Description("Can not find pin ball, pin search filure")]
        RA16 = 15,

        /// <summary>
        /// Paddle status occupied or wafer status inconsistent, command reject
        /// </summary>
        [Description("Paddle status occupied or wafer status inconsistent, command reject")]
        RA17 = 16,

        /// <summary>
        /// There is(are) wafer(s) on paddle, and pin search data invalid command reject
        /// </summary>
        [Description("There is(are) wafer(s) on paddle, and pin search data invalid command reject")]
        RA18 = 17,

        /// <summary>
        /// BL slide out sensor detector wafer slide out
        /// </summary>
        [Description("BL slide out sensor detector wafer slide out")]
        RA19 = 18,

        /// <summary>
        /// BR slide out sensor detector wafer slide out
        /// </summary>
        [Description("BR slide out sensor detector wafer slide out")]
        RA20 = 19,

        /// <summary>
        /// BL and BR slide out sensor detector wafer slide out
        /// </summary>
        [Description("BL and BR slide out sensor detector wafer slide out")]
        RA21 = 20,

        /// <summary>
        /// Pin search value delta out of setpoint
        /// </summary>
        [Description("Pin search value delta out of setpoint")]
        RA22 = 21,

        /// <summary>
        /// Shuttle 1 exist status is diable, command reject
        /// </summary>
        [Description("Shuttle 1 exist status is diable, command reject")]
        RA23 = 22,

        /// <summary>
        /// Shuttle 2 exist status is diable, command reject
        /// </summary>
        [Description("Shuttle 2 exist status is diable, command reject")]
        RA24 = 23,

        /// <summary>
        /// shuttle position status not at right position, can not move wafer
        /// </summary>
        [Description("shuttle position status not at right position, can not move wafer")]
        RA25 = 24,

        /// <summary>
        /// CHA exist status is disable, command reject
        /// </summary>
        [Description("CHA exist status is disable, command reject")]
        RA26 = 25,

        /// <summary>
        /// CHB exist status is disable, command reject
        /// </summary>
        [Description("CHB exist status is disable, command reject")]
        RA27 = 26,

        /// <summary>
        /// CHA trigger status is alarm, command reject
        /// </summary>
        [Description("CHA trigger status is alarm, command reject")]
        RA28 = 27,

        /// <summary>
        /// CHB trigger status is alarm, command reject
        /// </summary>
        [Description("CHB trigger status is alarm, command reject")]
        RA29 = 28,

        /// <summary>
        /// CHA run status is busy, command reject
        /// </summary>
        [Description("CHA run status is busy, command reject")]
        RA30 = 29,

        /// <summary>
        /// CHB run status is busy, command reject
        /// </summary>
        [Description("CHB run status is busy, command reject")]
        RA31 = 30,

        /// <summary>
        /// CHA run status is processing, command reject
        /// </summary>
        [Description("CHA run status is processing, command reject")]
        RA32 = 31,

        /// <summary>
        /// CHB run status is processing, command reject
        /// </summary>
        [Description("CHB run status is processing, command reject")]
        RA33 = 32,

        /// <summary>
        /// Cooling chamber trigger status is alarm, command reject
        /// </summary>
        [Description("Cooling chamber trigger status is alarm, command reject")]
        RA34 = 33,

        /// <summary>
        /// Cooling chamber run status is not idle status, command reject
        /// </summary>
        [Description("Cooling chamber run status is not idle status, command reject")]
        RA35 = 34,

        /// <summary>
        /// cassette slot is not empty, can not put wafer to cassette slot
        /// </summary>
        [Description("cassette slot is not empty, can not put wafer to cassette slot")]
        RA36 = 35,

        /// <summary>
        /// cassette slot status inconsistent, command reject
        /// </summary>
        [Description("cassette slot status inconsistent, command reject")]
        RA37 = 36,

        /// <summary>
        /// smooth P1 wafer has been lost
        /// </summary>
        [Description("smooth P1 wafer has been lost")]
        RA38 = 37,

        /// <summary>
        /// smooth P2 wafer has been lost
        /// </summary>
        [Description("smooth P2 wafer has been lost")]
        RA39 = 38,

        /// <summary>
        /// smooth side both paddle wafer has been lost
        /// </summary>
        [Description("smooth side both paddle wafer has been lost")]
        RA40 = 39,

        /// <summary>
        /// nose P1 wafer has been lost
        /// </summary>
        [Description("nose P1 wafer has been lost")]
        RA41 = 40,

        /// <summary>
        /// nose P2 wafer has been lost
        /// </summary>
        [Description("nose P2 wafer has been lost")]
        RA42 = 41,

        /// <summary>
        /// nose side both paddle wafer has been lost
        /// </summary>
        [Description("nose side both paddle wafer has been lost")]
        RA43 = 42,

        /// <summary>
        /// smooth P1 wafer put wafer failure
        /// </summary>
        [Description("smooth P1 wafer put wafer failure")]
        RA44 = 43,

        /// <summary>
        /// smooth P2 wafer put wafer failure
        /// </summary>
        [Description("smooth P2 wafer put wafer failure")]
        RA45 = 44,

        /// <summary>
        /// smooth side both paddle wafer put wafer failure
        /// </summary>
        [Description("smooth side both paddle wafer put wafer failure")]
        RA46 = 45,

        /// <summary>
        /// nose P1 wafer put wafer failure
        /// </summary>
        [Description("nose P1 wafer put wafer failure")]
        RA47 = 46,

        /// <summary>
        /// nose P2 wafer put wafer failure
        /// </summary>
        [Description("nose P2 wafer put wafer failure")]
        RA48 = 47,

        /// <summary>
        /// nose side both wafer put wafer failure
        /// </summary>
        [Description("nose side both wafer put wafer failure")]
        RA49 = 48,

        /// <summary>
        /// sensor show smooth P1 wafer status disconsister with slot, move wafer failure
        /// </summary>
        [Description("sensor show smooth P1 wafer status disconsister with slot, move wafer failure")]
        RA50 = 49,

        /// <summary>
        /// sensor show smooth P2 wafer status disconsister with slot, move wafer failure
        /// </summary>
        [Description("sensor show smooth P2 wafer status disconsister with slot, move wafer failure")]
        RA51 = 50,

        /// <summary>
        /// sensor show nose P1 wafer status disconsister with slot, move wafer failure
        /// </summary>
        [Description("sensor show nose P1 wafer status disconsister with slot, move wafer failure")]
        RA52 = 51,

        /// <summary>
        /// sensor show nose P2 wafer status disconsister with slot, move wafer failure
        /// </summary>
        [Description("sensor show nose P2 wafer status disconsister with slot, move wafer failure")]
        RA53 = 52,

        /// <summary>
        /// There is(are) wafer(s) in CHA, put wafer to CHA command reject
        /// </summary>
        [Description("There is(are) wafer(s) in CHA, put wafer to CHA command reject")]
        RA54 = 53,

        /// <summary>
        /// There is(are) wafer(s) in CHB, put wafer to CHA command reject
        /// </summary>
        [Description("There is(are) wafer(s) in CHB, put wafer to CHA command reject")]
        RA55 = 54,

        /// <summary>
        /// There is(are) wafer(s) in CT, put wafer to CHA command reject
        /// </summary>
        [Description("There is(are) wafer(s) in CT, put wafer to CHA command reject")]
        RA56 = 55,

        /// <summary>
        /// There is(are) wafer(s) in CB, put wafer to CHA command reject
        /// </summary>
        [Description("There is(are) wafer(s) in CB, put wafer to CHA command reject")]
        RA57 = 56,

        /// <summary>
        /// smooth P1 wafer status abnormal
        /// </summary>
        [Description("smooth P1 wafer status abnormal")]
        RA58 = 57,

        /// <summary>
        /// smooth P2 wafer status abnormal
        /// </summary>
        [Description("smooth P2 wafer status abnormal")]
        RA59 = 58,

        /// <summary>
        /// nose P1 wafer status abnormal
        /// </summary>
        [Description("nose P1 wafer status abnormal")]
        RA60 = 59,

        /// <summary>
        /// nose P2 wafer status abnormal
        /// </summary>
        [Description("nose P2 wafer status abnormal")]
        RA61 = 60,

        /// <summary>
        /// slot is can not get wafer from cassette
        /// </summary>
        [Description("slot is can not get wafer from cassette")]
        RA62 = 61,

        /// <summary>
        /// no wafer in CHA, get wafer from CHA command reject
        /// </summary>
        [Description("no wafer in CHA, get wafer from CHA command reject")]
        RA63 = 62,

        /// <summary>
        /// no wafer in CHB, get wafer from CHA command reject
        /// </summary>
        [Description("no wafer in CHB, get wafer from CHA command reject")]
        RA64 = 63,

        /// <summary>
        /// no wafer in CT, get wafer from CHA command reject
        /// </summary>
        [Description("no wafer in CT, get wafer from CHA command reject")]
        RA65 = 64,

        /// <summary>
        /// no wafer in CB, get wafer from CHA command reject
        /// </summary>
        [Description("no wafer in CB, get wafer from CHA command reject")]
        RA66 = 65,

        /// <summary>
        /// robot motion error
        /// </summary>
        [Description("robot motion error")]
        RA67 = 66
    }
}