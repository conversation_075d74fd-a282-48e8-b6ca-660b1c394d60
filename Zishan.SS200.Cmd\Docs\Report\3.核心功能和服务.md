# Zishan.SS200.Cmd 核心功能和服务

## 1. Modbus客户端服务

Modbus客户端服务是与PLC设备通信的核心组件，负责建立和维护Modbus TCP连接，以及执行读写操作。

### IModbusClientService 接口

```csharp
public interface IModbusClientService : IDisposable
{
    bool IsConnected { get; }
    IModbusMaster Master { get; }
    Task<bool> ConnectAsync(string ipAddress, int port, int timeout = 3000);
    Task DisconnectAsync();
    Task<ushort[]> ReadHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints);
    Task<ushort[]> ReadInputRegistersAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints);
    Task WriteHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort[] values);
    Task WriteSingleRegisterAsync(byte slaveAddress, ushort address, ushort value);
}
```

### ModbusClientService 实现

ModbusClientService是IModbusClientService的具体实现，提供了以下功能：

1. **连接管理**：建立和断开Modbus TCP连接
2. **读写操作**：读取和写入寄存器值
3. **错误处理**：捕获和记录异常，防止运行时崩溃
4. **错误节流**：对频繁发生的错误进行节流，避免UI卡顿和日志膨胀

关键实现：

```csharp
// 错误节流机制
private DateTime _lastErrorTime = DateTime.MinValue;
private static readonly TimeSpan ErrorThrottleInterval = TimeSpan.FromSeconds(3);

private void ShowThrottledError(string message)
{
    var now = DateTime.Now;
    if (now - _lastErrorTime >= ErrorThrottleInterval)
    {
        HcGrowlExtensions.Error(message);
        _lastErrorTime = now;
    }
    // 始终记录日志，不受节流影响
    _logger.Error(message);
}
```

## 2. 命令任务处理模型

CmdTaskHandlel类是命令处理的核心模型，负责管理Modbus命令的发送和监控。

### 关键特性

1. **状态管理**：管理命令执行状态（空闲、触发、执行中、完成）
2. **任务监控**：监控命令执行状态，支持超时检测
3. **命令执行**：提供统一的命令执行接口

```csharp
public enum TaskHandleStatus
{
    Idle = 0,       // 空闲
    Trigger = 1,    // 触发执行
    Running = 2,    // 执行中
    Complete = 4    // 执行完成
}

public enum TaskHandleResult
{
    Success = 0,    // 成功
    Failed = 1,     // 失败
    Timeout = 2     // 超时
}
```

### 命令执行流程

1. 向非handle区写入指令数据及参数
2. 向Task Handle区域写入触发状态及指令信息
3. 轮询Task Handle状态，等待执行完成
4. 读取执行结果，重置状态

```csharp
public async Task<TaskHandleResult> ExecuteCommandAsync(IModbusMaster master, byte slaveId, EnuCmdIndexInfo enuCmdIndexInfo, List<ushort> cmdPars, int timeout = 5000)
{
    try
    {
        // 1. 向非handle区写入指令数据和参数
        await WriteCommandParams(master, slaveId, enuCmdIndexInfo, cmdPars);
        
        // 2. 设置命令起始地址和长度
        await master.WriteSingleRegisterAsync(slaveId, CmdStartAddr.Address, CmdParams[0].Address);
        await master.WriteSingleRegisterAsync(slaveId, CmdLen.Address, (ushort)cmdPars.Count);
        
        // 3. 触发执行
        await master.WriteSingleRegisterAsync(slaveId, StatusFlag.Address, (ushort)TaskHandleStatus.Trigger);
        
        // 4. 等待完成
        return await WaitForCompletionAsync(master, slaveId, timeout);
    }
    catch (Exception ex)
    {
        // 异常处理
        return TaskHandleResult.Failed;
    }
}
```

## 3. S200McuCmdService 服务

S200McuCmdService是一个高级封装服务，负责管理特定设备(SS200)的命令执行。它基于Modbus客户端服务，提供更高层次的命令抽象。

### 主要功能

1. **自动连接**：管理与设备的连接，支持断线重连
2. **命令分组**：将设备命令分为不同组，如Shuttle、机器人等
3. **命令执行**：提供友好的命令执行接口

```csharp
// 使用_shuttleMcuCmdService服务功能如下：
// 1. 插入IP、Port内部自动连接Modbus TCP，断开有重连机制
// 2. 服务命令调用：response = await _shuttleMcuCmdService.Shuttle.Run(EnuShuttleCmdName.S1_SD, CMDs.pars, ref RunInf);
// 3. 服务命令调用：response = await mcuTask.Robot.Run(CMDs.S2SD, CMDs.pars, ref RunInf);
// 4. 服务命令调用：response = await mcuTask.Cha.Run(CMDs.S3SD, CMDs.pars, ref RunInf);
// 5. 服务命令调用：response = await mcuTask.Chb.Run(CMDs.S4SD, CMDs.pars, ref RunInf);
```

## 4. ModbusRegisterService 服务

ModbusRegisterService负责管理Modbus寄存器的读写操作，提供寄存器缓存和周期性轮询功能。

### 主要功能

1. **寄存器定义**：定义和管理Modbus寄存器及其映射关系
2. **周期性轮询**：定期读取关键寄存器值，更新系统状态
3. **数据转换**：处理不同数据类型的转换（如32位浮点数到16位寄存器）

## 5. ConfigurationService 服务

ConfigurationService负责管理应用程序配置，提供配置读写和验证功能。

### 主要功能

1. **配置加载**：加载INI配置文件
2. **配置验证**：验证配置参数有效性
3. **配置应用**：将配置应用到系统组件

## 6. App全局服务管理

App.xaml.cs中的RegisterTypes方法负责注册和配置所有服务：

```csharp
protected override void RegisterTypes(IContainerRegistry containerRegistry)
{
    // 注册 ModbusClientService 为单例服务
    containerRegistry.RegisterSingleton<IModbusClientService, ModbusClientService>();

    // 注册 ModbusRegisterService
    containerRegistry.RegisterSingleton<ModbusRegisterService>();

    // 注册 MainWindowViewModel 为单例
    containerRegistry.RegisterSingleton<MainWindowViewModel>();

    // 注册视图和视图模型
    // containerRegistry.RegisterForNavigation<IR400View, IR400ViewModel>();
}
```

## 7. 服务间协作

这些服务之间形成了明确的依赖和协作关系：

1. **MainWindowViewModel** 依赖 ModbusClientService 和 S200McuCmdService
2. **S200McuCmdService** 依赖 ModbusClientService
3. **ModbusRegisterService** 依赖 ModbusClientService

这种分层设计确保了关注点分离和代码重用，使系统更易于维护和扩展。

## 8. 错误处理与恢复

服务层实现了全面的错误处理和恢复机制：

1. **异常捕获**：捕获和记录所有异常
2. **重试机制**：关键操作失败时支持重试
3. **状态恢复**：系统异常后的状态恢复
4. **断线重连**：网络断开后的自动重连

## 总结

Zishan.SS200.Cmd的核心服务体现了良好的软件设计原则，包括单一职责原则、接口隔离原则和依赖倒置原则。通过这些服务，应用程序能够可靠地与工业设备通信，执行命令，监控状态，并提供友好的用户界面。 