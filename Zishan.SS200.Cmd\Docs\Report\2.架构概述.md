# Zishan.SS200.Cmd 架构概述

## 架构模式

Zishan.SS200.Cmd项目采用**MVVM (Model-View-ViewModel)** 架构模式，并利用**Prism框架**实现依赖注入和模块化设计。这种架构选择为应用程序带来以下优势：

1. **关注点分离**：将用户界面(View)、业务逻辑(ViewModel)和数据模型(Model)明确分开
2. **可测试性**：ViewModel不依赖于View，便于单元测试
3. **代码重用**：相同的ViewModel可以用于不同的View
4. **维护性**：简化复杂度，使得代码更容易维护和理解

## 核心架构组件

### 1. 视图层 (View)

- **技术实现**：WPF XAML
- **核心文件**：Views/MainWindow.xaml 及其他视图文件
- **责任**：
  - 定义用户界面
  - 通过数据绑定和命令将用户操作委托给ViewModel
  - 使用HandyControl等UI控件库增强用户体验

### 2. 视图模型层 (ViewModel)

- **技术实现**：CommunityToolkit.Mvvm 
- **核心文件**：ViewModels/MainWindowViewModel.cs 及其他视图模型
- **责任**：
  - 暴露视图需要的数据和命令
  - 实现业务逻辑
  - 协调模型操作
  - 维护视图状态

### 3. 模型层 (Model)

- **技术实现**：普通C#类和专用模型类
- **核心文件**：Models目录下的各个模型类
- **责任**：
  - 数据存储和访问
  - 业务实体和逻辑封装
  - 与外部系统(如Modbus设备)交互

## 依赖注入与服务定位

项目使用**Prism.DryIoc**实现依赖注入，提供松耦合和可测试性：

```csharp
protected override void RegisterTypes(IContainerRegistry containerRegistry)
{
    // 注册 ModbusClientService 为单例服务
    containerRegistry.RegisterSingleton<IModbusClientService, ModbusClientService>();

    // 注册 ModbusRegisterService
    containerRegistry.RegisterSingleton<ModbusRegisterService>();

    // 注册 MainWindowViewModel 为单例
    containerRegistry.RegisterSingleton<MainWindowViewModel>();

    // 注册视图和视图模型
    // containerRegistry.RegisterForNavigation<IR400View, IR400ViewModel>();
}
```

## 服务层设计

项目采用服务层模式来封装关键功能：

1. **ModbusClientService**：封装Modbus通信功能
2. **S200McuCmdService**：提供设备控制命令
3. **ConfigurationService**：管理配置信息
4. **ModbusRegisterService**：管理Modbus寄存器操作

这些服务通过依赖注入提供给ViewModels，实现了关注点分离和代码重用。

## 命令模式实现

项目使用**CommunityToolkit.Mvvm**的`RelayCommand`模式来实现命令绑定：

```csharp
[RelayCommand]
private async Task RunCmdTest(string parameter)
{
    // 命令实现...
}
```

这种实现方式简化了命令处理代码，提高了可读性和可维护性。

## 数据绑定机制

项目使用WPF的双向数据绑定和CommunityToolkit.Mvvm的`ObservableProperty`属性来实现数据绑定：

```csharp
[ObservableProperty]
private bool isConnected;
```

这种方式减少了样板代码，提高了代码简洁性。

## 错误处理策略

项目实现了全局异常处理机制，在App.xaml.cs中通过`DispatcherUnhandledException`事件捕获和记录未处理异常：

```csharp
DispatcherUnhandledException += App_DispatcherUnhandledException;
```

## 资源管理

项目使用WPF资源字典管理样式和资源：

```xml
<ResourceDictionary.MergedDictionaries>
    <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml" />
    <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
</ResourceDictionary.MergedDictionaries>
```

## 设计模式应用

项目中应用了多种设计模式：

1. **单例模式**：确保关键服务只有一个实例
2. **工厂模式**：ModbusFactory创建Modbus主站
3. **命令模式**：封装请求为对象
4. **观察者模式**：通过数据绑定实现
5. **策略模式**：不同命令策略的实现

## 总结

Zishan.SS200.Cmd项目采用了成熟的MVVM架构模式和现代依赖注入技术，展现了优秀的软件设计实践。通过清晰的关注点分离和模块化设计，项目实现了高度的可维护性、可测试性和可扩展性。 