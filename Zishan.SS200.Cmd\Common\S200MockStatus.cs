﻿using log4net;
using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// S200设备状态管理类，用于集中管理各种设备的状态判断逻辑
    /// </summary>
    public class S200MockStatus
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(S200MockStatus));
        private static readonly Lazy<S200MockStatus> _instance = new Lazy<S200MockStatus>(() => new S200MockStatus());

        /// <summary>
        /// 单例实例
        /// </summary>
        public static S200MockStatus Instance => _instance.Value;

        private S200MockStatus()
        {
            // 私有构造函数，确保单例模式
        }

        #region 通用设备状态检查

        /// <summary>
        /// 检查设备是否处于空闲状态
        /// </summary>
        /// <param name="device">要检查的设备</param>
        /// <param name="deviceName">设备名称（用于日志）</param>
        /// <returns>设备是否空闲，为false表示设备忙碌或错误状态</returns>
        public bool IsDeviceIdle(McuDevice device, string deviceName)
        {
            if (device == null)
            {
                _logger.Error($"{deviceName}设备实例为空");
                return false;
            }

            if (device.Status == DeviceStatus.Busy)
            {
                _logger.Warn($"{deviceName}当前处于忙碌状态，无法执行命令");
                return false;
            }

            if (device.Status == DeviceStatus.Error)
            {
                _logger.Error($"{deviceName}当前处于错误状态，无法执行命令");
                return false;
            }

            if (device.Status == DeviceStatus.Disconnected)
            {
                _logger.Error($"{deviceName}当前已断开连接，无法执行命令");
                return false;
            }

            return true;
        }

        #endregion 通用设备状态检查

        #region 机器人状态检查

        /// <summary>
        /// 检查机器人状态是否允许执行命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="logErrorCode">日志错误代码前缀</param>
        /// <returns>是否允许继续执行</returns>
        public bool CheckRobotStatus(IS200McuCmdService cmdService, string logErrorCode = "RA")
        {
            // 1. 检查机器人状态
            if (cmdService.Robot.Status == DeviceStatus.Busy)
            {
                _logger.Error($"机器人当前处于忙碌状态(MRS2 BUSY)，报警{logErrorCode}1");
                return false;
            }

            if (cmdService.Robot.Status == DeviceStatus.Error)
            {
                _logger.Error($"机器人当前处于报警状态(MRS3 ALARM)，报警{logErrorCode}2");
                return false;
            }

            if (cmdService.Robot.Status == DeviceStatus.Disconnected)
            {
                _logger.Error($"机器人当前已断开连接，报警{logErrorCode}3");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 获取机器人是否在指定位置
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="positionName">位置名称，例如"RS1"、"RS18"</param>
        /// <returns>是否在指定位置</returns>
        public async Task<bool> IsRobotAtPositionAsync(IS200McuCmdService cmdService, string positionName)
        {
            // 这里需要实际代码来检查机器人位置
            // 具体实现取决于系统中如何读取机器人位置信息

            // 假设位置值存储在某个寄存器中，实际实现需要根据具体硬件接口进行调整
            ushort positionIndex = positionName switch
            {
                "RS1" => 1,  // 假设位置RS1对应值1
                "RS18" => 18, // 假设位置RS18对应值18
                "RS7" => 7,  // 假设位置RS7对应值7
                "RS8" => 8,  // 假设位置RS8对应值8
                _ => 0
            };

            // 临时返回，实际代码需要实现位置检测
            bool blResult = positionName switch
            {
                "RS1" => false,// cmdService.Robot == positionIndex, 已经到达目标RS1位置
                "RS18" => true, //cmdService.Robot == positionIndex, Z轴在RS18 0点位置
                "RP1" => true, // cmdService.Robot == positionIndex, Z轴在RP1 0点位置
                "RP2" => false, // cmdService.Robot == positionIndex, Z轴在RP2 0点位置
                "RP3" => true,
                _ => false
            };

            return await Task.FromResult(blResult);
        }

        #endregion 机器人状态检查

        #region Shuttle状态检查

        /// <summary>
        /// 检查Shuttle状态是否允许执行命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="logErrorCode">日志错误代码前缀</param>
        /// <returns>是否允许继续执行</returns>
        public bool CheckShuttleStatus(IS200McuCmdService cmdService, string logErrorCode = "SA")
        {
            if (cmdService.Shuttle.Status == DeviceStatus.Busy)
            {
                _logger.Warn($"Shuttle当前处于忙碌状态，无法执行命令，报警{logErrorCode}1");
                return false;
            }

            if (cmdService.Shuttle.Status == DeviceStatus.Error)
            {
                _logger.Error($"Shuttle当前处于错误状态，无法执行命令，报警{logErrorCode}2");
                return false;
            }

            if (cmdService.Shuttle.Status == DeviceStatus.Disconnected)
            {
                _logger.Error($"Shuttle当前已断开连接，无法执行命令，报警{logErrorCode}3");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 获取Shuttle传感器状态
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="sensorIndex">传感器索引</param>
        /// <returns>传感器状态</returns>
        public async Task<bool> GetShuttleSensorStatusAsync(IS200McuCmdService cmdService, int sensorIndex)
        {
            // 实际代码需要读取Shuttle的传感器状态
            // 根据索引(DI19、DI20等)读取对应的状态位

            if (cmdService.ShuttleInputCoils != null &&
                cmdService.ShuttleInputCoils.Count > sensorIndex &&
                sensorIndex >= 0)
            {
                var coil = cmdService.ShuttleInputCoils[sensorIndex];
                return coil != null && coil.Coilvalue == true;
            }

            // 默认返回false
            return await Task.FromResult(false);
        }

        /// <summary>
        /// 根据传感器状态获取报警代码
        /// </summary>
        /// <param name="di19">DI19状态</param>
        /// <param name="di20">DI20状态</param>
        /// <returns>报警代码</returns>
        public string GetShuttleAlarmCodeForSensorStatus(bool di19, bool di20)
        {
            if (di19 && !di20) return "SA19";
            if (!di19 && di20) return "SA20";
            if (di19 && di20) return "SA21";
            return string.Empty; // 无报警
        }

        #endregion Shuttle状态检查

        #region 工艺腔室状态检查

        /// <summary>
        /// 检查工艺腔室A状态是否允许执行命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="logErrorCode">日志错误代码前缀</param>
        /// <returns>是否允许继续执行</returns>
        public bool CheckChaStatus(IS200McuCmdService cmdService, string logErrorCode = "CA")
        {
            if (cmdService.ChamberA.Status == DeviceStatus.Busy)
            {
                _logger.Warn($"工艺腔室A当前处于忙碌状态，无法执行命令，报警{logErrorCode}1");
                return false;
            }

            if (cmdService.ChamberA.Status == DeviceStatus.Error)
            {
                _logger.Error($"工艺腔室A当前处于错误状态，无法执行命令，报警{logErrorCode}2");
                return false;
            }

            if (cmdService.ChamberA.Status == DeviceStatus.Disconnected)
            {
                _logger.Error($"工艺腔室A当前已断开连接，无法执行命令，报警{logErrorCode}3");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 检查工艺腔室B状态是否允许执行命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="logErrorCode">日志错误代码前缀</param>
        /// <returns>是否允许继续执行</returns>
        public bool CheckChbStatus(IS200McuCmdService cmdService, string logErrorCode = "CB")
        {
            if (cmdService.ChamberB.Status == DeviceStatus.Busy)
            {
                _logger.Warn($"工艺腔室B当前处于忙碌状态，无法执行命令，报警{logErrorCode}1");
                return false;
            }

            if (cmdService.ChamberB.Status == DeviceStatus.Error)
            {
                _logger.Error($"工艺腔室B当前处于错误状态，无法执行命令，报警{logErrorCode}2");
                return false;
            }

            if (cmdService.ChamberB.Status == DeviceStatus.Disconnected)
            {
                _logger.Error($"工艺腔室B当前已断开连接，无法执行命令，报警{logErrorCode}3");
                return false;
            }

            return true;
        }

        #endregion 工艺腔室状态检查

        #region 传感器通用方法

        /// <summary>
        /// 获取传感器状态
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="sensorName">传感器名称</param>
        /// <returns>传感器状态</returns>
        public async Task<bool> GetSensorStatusAsync(IS200McuCmdService cmdService, string sensorName)
        {
            // 实际代码需要根据传感器名称读取对应的传感器状态
            // 可以通过映射表或其他方式实现传感器名到索引的映射

            switch (sensorName)
            {
                case "SPS11":
                    // 特定传感器状态的获取逻辑
                    return await Task.FromResult(false);

                case "SPS12":
                    return await Task.FromResult(true);

                default:
                    _logger.Warn($"未知的传感器名称: {sensorName}");
                    return await Task.FromResult(false);
            }
        }

        #endregion 传感器通用方法

        #region 错误处理方法

        /// <summary>
        /// 根据错误代码获取机器人详细错误信息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <returns>错误详细信息</returns>
        public string GetRobotErrorDetail(ushort errorCode)
        {
            return errorCode switch
            {
                0x0001 => "电机未使能",
                0x0002 => "电机过载",
                0x0003 => "位置溢出",
                0x0004 => "通信错误",
                0x0005 => "机械臂被阻挡",
                0x0101 => "Z轴移动到RPS23位置失败",
                0x0102 => "T轴移动到RP1位置失败",
                0x0103 => "Shuttle传感器状态异常",
                _ => $"未知错误: 0x{errorCode:X4}"
            };
        }

        /// <summary>
        /// 根据错误代码获取工艺腔室A详细错误信息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <returns>错误详细信息</returns>
        public string GetChaErrorDetail(ushort errorCode)
        {
            return errorCode switch
            {
                0x0001 => "真空度不满足要求",
                0x0002 => "安全联锁阻止操作",
                0x0003 => "门锁机构故障",
                0x0004 => "门驱动电机故障",
                0x0005 => "超时",
                _ => $"未知错误: 0x{errorCode:X4}"
            };
        }

        /// <summary>
        /// 根据错误代码获取Shuttle详细错误信息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <returns>错误详细信息</returns>
        public string GetShuttleErrorDetail(ushort errorCode)
        {
            return errorCode switch
            {
                0x0001 => "气缸未就位",
                0x0002 => "气压不足",
                0x0003 => "传感器未触发",
                0x0004 => "安全联锁阻止操作",
                0x0005 => "超时",
                _ => $"未知错误: 0x{errorCode:X4}"
            };
        }

        /// <summary>
        /// 根据设备类型和错误代码获取详细错误信息
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="errorCode">错误代码</param>
        /// <returns>错误详细信息</returns>
        public string GetErrorDetail(EnuMcuDeviceType deviceType, ushort errorCode)
        {
            return deviceType switch
            {
                EnuMcuDeviceType.Robot => GetRobotErrorDetail(errorCode),
                EnuMcuDeviceType.Shuttle => GetShuttleErrorDetail(errorCode),
                EnuMcuDeviceType.ChamberA => GetChaErrorDetail(errorCode),
                EnuMcuDeviceType.ChamberB => GetChaErrorDetail(errorCode), // 假设ChB和ChA使用相同的错误码
                _ => $"未知设备类型: {deviceType}, 错误代码: 0x{errorCode:X4}"
            };
        }

        #endregion 错误处理方法
    }
}