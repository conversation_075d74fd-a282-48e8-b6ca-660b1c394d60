- [x] position value 对应一个维度的值，还是3个维度的值？当前对应的位置值是1维度 ✅ 2025-05-19
- [x] Z-axis height to pin search 每次Cassette框，计算出来的基准值吗?是的，Shuttle1/Shuttle2 Smooth/Nose对应都有基准值，用于计算Slot取放高度 ✅ 2025-05-19
- [x] Parameter setting中max time行程的最大时间，具体什么意思？一次运行不能超过这个时间最大值 ✅ 2025-05-19
- [x] R-axis smooth cooling chamber extend  cooling有2对，上下2对吗，伸出长度都一样吧？一样，取放靠Z轴上下取放，但是Cha/Chb靠顶针实现 ✅ 2025-05-19
- [x] T轴旋转，某面向点位的最近距离计算优化，比如：Cassette 0°位置，当前270°，要面朝Cassette有个旋转方式：1：270°->0°；2：270°->360°，取哪一种方式？直接动到制定的站位 ✅ 2025-05-22
- [x] 初始化Smooth端对准Cassette ✅ 2025-05-26
- [x] 3轴回原点方式：1、根据校准后的参数回原点   ~~2、三轴绝对值回0点~~ ✅ 2025-06-03
- [x] MCU设备类型改为枚举类型 ✅ 2025-06-04
- [ ] SelectedDevice 也改为枚举类型，没有使用
- [ ] MCU运行参数，动态值通过参数传入，固定的通话Json配置档读取，包含前面的配置参数
- [ ] Modbus命令发送支持多任务区并行发送
- [ ] OperateResult命令返回统一封装
- [x] UI停止按钮：急停、当前小指令做完停止，后面恢复，也就是暂停继续功能怎么实现: ✅ 2025-06-06
- [ ] 二级指令，比如：PutWafer 到Cassette伸进去一半，wafer slide out sensor 3 BL/ 4 BR感应到，或者TZ轴,出现中断停止，出现abort、retry，需要相应轴RTZ回原点，再重新跑该二级指令 ⏫ 
- [ ] 从Cassette取放片的时候，要考虑2个滑片传感器被感应到，取放片那一刻不考虑，其它时刻要考虑
- [ ] S200McuCmdService需要取消Instance，全部使用Prism的Service，避免多实例干扰问题
- [x] 单轴运动到制定位置反应慢，MCU已经修复OK ✅ 2025-06-19

