# T轴摆正偏差计算使用示例

## 概述

本文档展示了如何在T轴归零流程中使用 `CalculateAlignmentDeviation` 方法实现T轴摆正功能。

## 使用场景

### 1. T轴归零流程中的摆正处理

在 `ZeroTAxisAsync` 方法中，当T轴不在零位时，需要先进行摆正处理：

```csharp
// 获取当前RTZ轴位置
var currentPosition = _interLock.RTZAxisPosition.GetCurrentRTZSteps();
UILogService.AddLog($"当前RTZ轴位置: T={currentPosition.TAxisStep}, R={currentPosition.RAxisStep}, Z={currentPosition.ZAxisStep}");

// 计算摆正偏差
var alignmentPosition = CalculateAlignmentDeviation(currentPosition.TAxisStep);

if (alignmentPosition != 0)
{
    // 执行T轴旋转到最接近的标准位置
    UILogService.AddLog($"T轴旋转到位置{alignmentPosition}进行摆正...");
    var rotateResult = await MoveTAxisToPositionAsync(cmdService, alignmentPosition);
    
    if (!rotateResult.Success)
    {
        UILogService.AddErrorLog($"T轴摆正失败: {rotateResult.Message}");
        return rotateResult;
    }
    UILogService.AddSuccessLog("T轴摆正完成");
}
else
{
    UILogService.AddLog("T轴已在标准位置，无需摆正");
}
```

### 2. 实际运行示例

#### 示例1：T轴位置接近Smooth端ChamberA

```
当前T轴位置: 50000步
计算结果：
- RP1(50100): 距离100步 ← 最接近
- RP2(25000): 距离25000步
- RP5(50): 距离49950步
- RP8(50050): 距离50步

选择：RP8(50050)，因为距离最小(50步)
执行：T轴旋转到50050位置
```

#### 示例2：T轴位置在零位附近

```
当前T轴位置: 100步
计算结果：
- RP4(0): 距离100步 ← 最接近
- RP5(50): 距离50步 ← 实际最接近

选择：RP5(50)，因为距离最小(50步)
执行：T轴旋转到50位置
```

#### 示例3：T轴已在标准位置

```
当前T轴位置: 25000步（正好在RP2位置）
计算结果：
- RP2(25000): 距离0步 ← 完全匹配
- RP7(25000): 距离0步 ← 完全匹配

选择：返回0（无需摆正）
执行：跳过摆正步骤
```

## 算法优势

### 1. 自动选择最优位置
- 自动计算到所有预设位置的距离
- 选择距离最小的位置进行摆正
- 避免不必要的长距离移动

### 2. 安全性考虑
- 只移动到预设的安全位置
- 避免移动到可能发生碰撞的位置
- 支持chamber和L/L环境的不同处理

### 3. 高效性
- 一次计算找到最优解
- 减少多次试探性移动
- 最小化机械磨损

## 配置参数说明

当前系统中的T轴位置参数配置：

| 参数 | 位置值(步) | 描述 | 端口类型 | 目标位置 | 备注 |
|------|-----------|------|----------|----------|------|
| RP1  | 50100     | T轴Smooth端到工艺腔室A | Smooth | ChamberA | |
| RP2  | 25000     | T轴Smooth端到工艺腔室B | Smooth | ChamberB | |
| RP3  | 75000     | T轴Smooth端到冷却腔 | Smooth | CoolingChamber | 面向方向，无上下之分 |
| RP4  | 0         | T轴Smooth端到晶圆盒 | Smooth | Cassette | |
| RP5  | 50        | T轴Nose端到工艺腔室A | Nose | ChamberA | |
| RP6  | 75000     | T轴Nose端到工艺腔室B | Nose | ChamberB | |
| RP7  | 25000     | T轴Nose端到冷却腔 | Nose | CoolingChamber | 面向方向，无上下之分 |
| RP8  | 50050     | T轴Nose端到晶圆盒 | Nose | Cassette | |

**注意**：CoolingChamber表示T轴面向冷却腔的方向，不区分上下层。只有Z轴才需要区分CoolingTop和CoolingBottom。

## 日志监控

方法执行时会产生详细的日志输出，便于监控和调试：

```
[INFO] 开始计算T轴摆正偏差，当前T轴位置: 50000
[INFO] RP1位置: 50100, 距离: 100
[INFO] RP2位置: 25000, 距离: 25000
[INFO] RP3位置: 75000, 距离: 25000
[INFO] RP4位置: 0, 距离: 50000
[INFO] RP5位置: 50, 距离: 49950
[INFO] RP6位置: 75000, 距离: 25000
[INFO] RP7位置: 25000, 距离: 25000
[INFO] RP8位置: 50050, 距离: 50
[INFO] 最接近的位置: RP8(50050), 最小距离: 50
[INFO] 需要摆正到位置: RP8(50050)
[INFO] T轴旋转到位置50050进行摆正...
[SUCCESS] T轴摆正完成
```

## 错误处理

方法包含完善的错误处理机制：

```csharp
try
{
    // 摆正偏差计算逻辑
}
catch (Exception ex)
{
    UILogService.AddErrorLog($"计算T轴摆正偏差异常: {ex.Message}");
    return 0; // 异常情况下返回0，不执行摆正
}
```

异常情况下：
- 记录详细错误日志
- 返回0表示不执行摆正
- 继续执行后续的归零流程
- 不会中断整个操作流程
