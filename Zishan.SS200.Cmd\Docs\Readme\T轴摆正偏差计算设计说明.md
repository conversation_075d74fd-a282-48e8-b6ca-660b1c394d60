# T轴摆正偏差计算设计说明

## 概述

T轴摆正偏差计算是机器人T轴归零流程中的关键步骤，用于确保T轴在归零前先移动到最近的标准位置，避免在非标准位置进行归零操作可能导致的碰撞风险。

## 设计理念

### 核心思想：最小距离摆正

当T轴不在零位时，系统会：
1. 计算当前T轴位置到所有预设标准位置（RP1-RP8）的距离
2. 选择距离最小的标准位置
3. 先移动到该标准位置进行摆正
4. 然后继续执行归零流程

### 安全考虑

**为什么需要摆正？**
- **Chamber环境**：若在chamber中，先摆正再缩回，避免与slit door碰撞
- **L/L环境**：摆正时必然不会撞到slit door，确保安全
- **精确定位**：确保从标准位置开始归零，提高精度

## 技术实现

### 位置参数映射

```
T轴位置参数（RP1-RP8）对应关系：
┌─────────────────────────────────────────────────────────┐
│  端口类型  │  目标位置  │  参数  │      描述           │
├─────────────────────────────────────────────────────────┤
│  Smooth   │  ChamberA  │  RP1   │ T轴Smooth端到工艺腔室A │
│  Smooth   │  ChamberB  │  RP2   │ T轴Smooth端到工艺腔室B │
│  Smooth   │  Cooling   │  RP3   │ T轴Smooth端到冷却腔    │
│  Smooth   │  Cassette  │  RP4   │ T轴Smooth端到晶圆盒    │
│  Nose     │  ChamberA  │  RP5   │ T轴Nose端到工艺腔室A   │
│  Nose     │  ChamberB  │  RP6   │ T轴Nose端到工艺腔室B   │
│  Nose     │  Cooling   │  RP7   │ T轴Nose端到冷却腔      │
│  Nose     │  Cassette  │  RP8   │ T轴Nose端到晶圆盒      │
└─────────────────────────────────────────────────────────┘
```

### 重要设计区别：T轴 vs Z轴

**T轴旋转特点**：
- 只需要面向目标方向
- 对于冷却腔，不区分上下层（Top/Bottom）
- 使用CoolingChamber表示"面向冷却腔方向"的概念

**Z轴升降特点**：
- 需要精确的高度控制
- 必须区分CoolingTop和CoolingBottom
- 每个高度位置都有具体的步数值

### 算法流程

```
输入：当前T轴位置（步数）
│
├─ 获取所有RP1-RP8位置参数值
│
├─ 计算距离：distance = ABS(当前位置 - RPx位置)
│
├─ 找到最小距离及对应位置
│
├─ 判断：
│  ├─ 距离 = 0 → 已在标准位置，返回0（无需摆正）
│  └─ 距离 > 0 → 返回最接近的位置值
│
└─ 输出：目标摆正位置（步数）或0
```

## 实际应用场景

### 场景1：T轴位置接近某个标准位置

```
当前位置：50000步
计算结果：
- RP1(50100): 距离100步
- RP8(50050): 距离50步 ← 最接近
选择：移动到50050位置进行摆正
```

### 场景2：T轴已在标准位置

```
当前位置：25000步（正好在RP2位置）
计算结果：距离0步
选择：无需摆正，直接继续归零流程
```

### 场景3：T轴在两个标准位置之间

```
当前位置：37500步
计算结果：
- RP2(25000): 距离12500步 ← 更接近
- RP6(75000): 距离37500步
选择：移动到25000位置进行摆正
```

## 错误处理

### 异常安全

```csharp
try
{
    // 摆正偏差计算逻辑
}
catch (Exception ex)
{
    UILogService.AddErrorLog($"计算T轴摆正偏差异常: {ex.Message}");
    return 0; // 异常情况下返回0，不执行摆正
}
```

**设计原则**：
- 异常情况下不执行摆正操作
- 继续执行后续归零流程
- 不中断整个操作序列
- 记录详细错误日志

## 日志监控

### 详细日志输出

```
[INFO] 开始计算T轴摆正偏差，当前T轴位置: 50000
[INFO] RP1位置: 50100, 距离: 100
[INFO] RP2位置: 25000, 距离: 25000
[INFO] RP3位置: 75000, 距离: 25000
[INFO] RP4位置: 0, 距离: 50000
[INFO] RP5位置: 50, 距离: 49950
[INFO] RP6位置: 75000, 距离: 25000
[INFO] RP7位置: 25000, 距离: 25000
[INFO] RP8位置: 50050, 距离: 50
[INFO] 最接近的位置: RP8(50050), 最小距离: 50
[INFO] 需要摆正到位置: RP8(50050)
```

### 监控要点

- 当前T轴位置
- 所有RP位置的距离计算
- 最终选择的摆正位置
- 异常情况的错误信息

## 性能优化

### 算法复杂度

- **时间复杂度**：O(8) = O(1) - 固定8个位置参数
- **空间复杂度**：O(8) = O(1) - 固定字典大小
- **执行效率**：单次计算，无循环迭代

### 优化特点

- 一次计算找到最优解
- 避免多次试探性移动
- 最小化机械磨损
- 减少操作时间

## 维护说明

### 配置参数

位置参数存储在：`RobotPositionParameters.json`
```json
{
  "code": "RP1",
  "description": "T-axis smooth to CHA",
  "value": 50100
}
```

### 修改注意事项

1. **参数一致性**：确保RP1-RP8参数与实际机械位置匹配
2. **安全验证**：修改参数后需要进行安全测试
3. **文档更新**：参数变更时同步更新相关文档
4. **版本控制**：重要参数变更需要版本记录

## 总结

T轴摆正偏差计算方法通过智能算法选择最优摆正位置，确保机器人T轴归零操作的安全性和精确性。该方法充分考虑了T轴旋转的特点，与Z轴精确高度控制形成互补，共同构成了完整的机器人位置控制体系。
