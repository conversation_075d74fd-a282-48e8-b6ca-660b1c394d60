{"0010": {"almrst_clr": true, "cause": ["电流ON时，电动机轴指令位置与检测位置的偏差超出<位置偏差过大Alarm>参数的设定值。", "负载过大或相对负载加减速时间或加减速斜率过短。", "超过了定位压推SD运行的动作范围。"], "dispose": ["请减小负载。", "请延长加减速时间或减缓加减速斜率。", "请增大运行电流。", "请修改运行数据。"], "kind": "位置偏差过大"}, "0020": {"almrst_clr": false, "cause": ["电动机、电缆线及驱动器输出电路短路。"], "dispose": ["请切断电源，确认电动机、电缆线及驱动器是否破损，再重新接通电源。"], "kind": "过电流"}, "0021": {"almrst_clr": true, "cause": ["驱动器的内部温度达到了规格值的上限。"], "dispose": ["请改善通风条件。"], "kind": "主电路过热"}, "0022": {"almrst_clr": true, "cause": ["电源电压超过了容许值。", "紧急停止了较大的惯性负载。", "执行了升降运行。"], "dispose": ["请确认电源的输入电压。", "请减小负载。", "请延长加减速时间或减缓加减速斜率。"], "kind": "过压"}, "0023": {"almrst_clr": true, "cause": ["运行过程中，主电源断开。"], "dispose": ["请确认主电源是否已正常接通。"], "kind": "主电源关闭"}, "0025": {"almrst_clr": true, "cause": ["电源瞬间断开或电压不足。"], "dispose": ["请确认电源的输入电压。"], "kind": "不足电压"}, "0026": {"almrst_clr": true, "cause": ["ABZO传感器的检测温度达到了规格值的上限。"], "dispose": ["请确认电动机的散热状态。", "请改善通风条件。"], "kind": "电动机过热"}, "0028": {"almrst_clr": false, "cause": ["运行中，检测到传感器异常。"], "dispose": ["请断开电源，确认电动机的连接，重新接通电源。"], "kind": "传感器异常"}, "002A": {"almrst_clr": false, "cause": ["驱动器与ABZO传感器之间的通信发生了异常。"], "dispose": ["请断开电源，确认ABZO传感器的连接，重新接通电源。"], "kind": "ABZO传感器通信异常"}, "0030": {"almrst_clr": true, "cause": ["超过最大转矩的负载，施加了超过<过载Alarm>参数的设定值的时间。"], "dispose": ["请减小负载。", "请延长加减速时间或减缓加减速斜率。", "请增大运行电流。"], "kind": "过载"}, "0031": {"almrst_clr": true, "cause": ["电动机输出轴的检测速度超出规格值"], "dispose": ["请修改<电子减速机>参数，将电动机输出轴的速度设定成低于规格值。", "加速时如发生过冲，请延长加速时间或减缓加速斜率。"], "kind": "超速"}, "0033": {"almrst_clr": false, "cause": ["ABZO传感器的原点信息损坏。"], "dispose": ["请先执行位置预置或原点返回运行，再重新设定原点。"], "kind": "绝对位置异常"}, "0034": {"almrst_clr": true, "cause": ["指令脉冲的频率超出规格值。"], "dispose": ["请降低指令脉冲的频率。"], "kind": "指令脉冲异常"}, "0041": {"almrst_clr": false, "cause": ["驱动器的保存数据损坏。"], "dispose": ["请初始化所有参数。"], "kind": "EEPROM异常"}, "0042": {"almrst_clr": false, "cause": ["接通电源时，检测到ABZO传感器异常。"], "dispose": ["请断开电源，确认ABZO传感器的连接，重新接通电源。"], "kind": "初始时传感器异常"}, "0043": {"almrst_clr": false, "cause": ["接通电源时，电动机出现旋转动作。"], "dispose": ["请修改负载状态等，使得接通电源时，电动机输出轴不会因外力而出现运转。"], "kind": "初始时旋转异常"}, "0044": {"almrst_clr": false, "cause": ["ABZO传感器的保存数据损坏。"], "dispose": ["请使用维修指令<ZSG-PRESET>重新设定Z相。", "请执行MEXE02的<清除TRIP运转量>或维修指令<TRIP运转量清除>。"], "kind": "编码器EEPROM异常"}, "0045": {"almrst_clr": false, "cause": ["连接了不匹配驱动器的电动机。"], "dispose": ["请确认驱动器品名与电动机品名，按正确组合连接。"], "kind": "电动机组合异常"}, "004A": {"almrst_clr": true, "cause": ["在坐标不确定的状态下开始绝对定位运行。"], "dispose": ["请执行位置预置或原点返回运行。"], "kind": "原点返回未完成"}, "0051": {"almrst_clr": false, "cause": ["再生电阻未正确连接。", "再生电阻异常过热。"], "dispose": ["不使用再生电阻时，请将CN1的TH1与TH2端子进行短路。", "请正确连接再生电阻。", "超出了再生电阻的容许再生电力。请重新调节负载及运行条件。"], "kind": "再生电阻过热(仅AC电源驱动器)"}, "0053": {"almrst_clr": false, "cause": ["从HWTO1输入或HWTO2输入中的一个变成OFF到另一个变成OFF之间的时间超过了<HWTO-双重系统异常检测延迟时间>参数的设定值。", "检测到与上述现象相当的电路的故障。"], "dispose": ["请延长<HWTO-双重系统异常检测延迟时间>参数。", "请确认HWTO1输入和HWTO2输入的配线。"], "kind": "HWTO输入电路异常"}, "0060": {"almrst_clr": true, "cause": ["<FW-LS/RV-LS输入动作>参数为<发生Alarm>时，检测到FW-LS输入和RV-LS输入等两种输入。"], "dispose": ["请确认已设置的传感器逻辑与<接点设定>参数。"], "kind": "±LS同时输入"}, "0061": {"almrst_clr": true, "cause": ["在3传感器方式或2传感器方式的原点返回运行中，检测出与运行方向相反的LS输入。"], "dispose": ["请确认传感器配线。"], "kind": "±LS反连接"}, "0062": {"almrst_clr": true, "cause": ["原点返回运行中，施加了意想不到的负载。", "FW-LS、RV-LS传感器和原点传感器的设置位置接近。", "在检测到FW-LS输入和RV-LS输入两种输入的状态下，执行了原点返回运行。", "原点返回运行结束时位置预置处理失败。", "同一方向旋转方式的原点返回运行时，减速停止过程中越过了原点传感器。"], "dispose": ["请确认负载。", "请修改传感器的设置位置和电动机的运行开始方向。", "请确认已设置的传感器逻辑与<接点设定>参数。", "请确保原点返回结束时不会施加大于最大转矩的负载。", "请修改原点传感器的规格和<(HOME)原点返回加减速>参数。"], "kind": "原点返回运行异常"}, "0063": {"almrst_clr": true, "cause": ["在3传感器方式的原点返回运行时，在FW-LS输入和RV-LS输入之间未检测出HOMES输入。"], "dispose": ["请将原点传感器设置于FW-LS传感器和RV-LS传感器之间。"], "kind": "HOMES未检测出"}, "0064": {"almrst_clr": true, "cause": ["在原点返回运行中，无法检测出TIM输出、ZSG输出及SLIT输入。"], "dispose": ["请调整负载的结合状态及原点传感器的位置，以便HOMES输入为ON期间，这些信号变成ON。", "不使用信号时，请将<原点返回TIM/ZSG信号检测>参数及<原点返回SLIT传感器检测>参数设定成<无效>。"], "kind": "TIM、Z、SLIT信号异常"}, "0066": {"almrst_clr": true, "cause": ["<FW-LS/RV-LS输入动作>参数为<发生Alarm>时，检测到FW-LS输入或RV-LS输入等两种输入。"], "dispose": ["解除Alarm后，请通过运行或手动从传感器脱出。"], "kind": "硬件超程"}, "0067": {"almrst_clr": true, "cause": ["<软件超程>参数为<发生Alarm>时，达到了软件极限。"], "dispose": ["请修改运行数据。", "解除Alarm后，请通过运行或手动从传感器脱出。"], "kind": "软件超程"}, "0068": {"almrst_clr": true, "cause": ["<HWTO动作>参数为<有Alarm>时，HWTO1输入或HWTO2输入变成了OFF。"], "dispose": ["请将HWTO1 输入和HWTO2 输入设定成ON。"], "kind": "HWTO输入检测"}, "006A": {"almrst_clr": true, "cause": ["原点返回运行中偏置移动时检测出FW-LS输入或RV-LS输入。"], "dispose": ["请确认偏置值。"], "kind": "原点返回运行偏置异常"}, "006D": {"almrst_clr": true, "cause": ["原点设定完成的产品达到了保存在ABZO传感器的机构极限。"], "dispose": ["请确认移动量 (位置)。", "解除Alarm后，请通过运行或手动从传感器脱出。"], "kind": "机械超程"}, "0070": {"almrst_clr": true, "cause": ["运行速度为0的数据，执行了数据存储运行。", "循环设定为无效时，执行了循环运行。", "以超过<机构保护>参数的设定值的运行速度或运行电流运行。", "DG二系列执行了压推运行或压推原点返回运行。"], "dispose": ["请确认运行数据。", "请确认循环设定。", "机构保护参数的设定值可通过MEXE02的组合信息监视确认。", "DG二系列无法执行压推运行或压推原点返回运行。"], "kind": "运行数据异常"}, "0071": {"almrst_clr": false, "cause": ["通过<电子减速机>参数设定的分辨率超出规格范围。"], "dispose": ["请修改<电子减速机>参数，将分辨率设定在规格的范围以内。"], "kind": "电子减速机设定异常"}, "0072": {"almrst_clr": false, "cause": ["通过<电子减速机>参数设定的分辨率和<循环设定>参数冲突，接通了电源。"], "dispose": ["请正确设定循环设定，重新接通电源。"], "kind": "循环设定异常"}, "0081": {"almrst_clr": true, "cause": ["运行过程中，网络转换器的上位网络处于解列状态。"], "dispose": ["请确认上位网络的连接器及电缆线。"], "kind": "网络总线异常"}, "0083": {"almrst_clr": false, "cause": ["通信速度设定开关(BAUD)超出规格。"], "dispose": ["请确认BAUD开关。"], "kind": "通信用开关设定异常"}, "0084": {"almrst_clr": true, "cause": ["RS-485通信的连续异常次数达到了<通信异常Alarm>参数的设定值。", "与网络转换器之间的通信中连续检测到3次异常。"], "dispose": ["请确认与上位系统的连接。", "请确认RS-485 通信的设定。", "请确认与网络转换器的连接。"], "kind": "RS-485 通信异常"}, "0085": {"almrst_clr": true, "cause": ["即使经过<通信超时>参数设定的时间，也没有进行和上位系统之间的通信。", "超过200ms以上，未能与网络转换器之间取得通信。"], "dispose": ["请确认与上位系统的连接。", "请确认与网络转换器的连接。"], "kind": "RS-485 通信超时"}, "008E": {"almrst_clr": true, "cause": ["在网络转换器发生了Alarm。"], "dispose": ["请确认网络转换器的Alarm代码。"], "kind": "网络转换器异常"}, "00F0": {"almrst_clr": false, "cause": ["CPU误动作。"], "dispose": ["请重新接通电源。"], "kind": "CPU异常"}}