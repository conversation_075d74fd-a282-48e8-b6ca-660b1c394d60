using System;
using CommunityToolkit.Mvvm.ComponentModel;
using System.ComponentModel;
using Zishan.SS200.Cmd.Enums.Basic;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot
{
    /// <summary>
    /// Robot子系统状态模型
    /// </summary>
    public partial class RobotSubsystemStatus : ObservableObject
    {
        #region 基本状态

        /// <summary>
        /// Robot状态
        /// </summary>
        [ObservableProperty]
        private EnuRobotStatus enuRobotStatus;

        #endregion 基本状态

        #region T轴状态

        /// <summary>
        /// T轴Smooth目的地
        /// </summary>
        [ObservableProperty]
        private EnuLocationStationType enuTAxisSmoothDestination = EnuLocationStationType.None;

        /// <summary>
        /// T轴Nose目的地
        /// </summary>
        [ObservableProperty]
        private EnuLocationStationType enuTAxisNoseDestination = EnuLocationStationType.None;

        /// <summary>
        /// T-axis zero position
        /// </summary>
        [ObservableProperty]
        private bool tAxisIsZeroPosition;

        #endregion T轴状态

        #region R轴状态

        /// <summary>
        /// T轴和R轴Smooth扩展目的地 (RS10-RS13)
        /// </summary>
        [ObservableProperty]
        private EnuLocationStationType enuTAndRAxisSmoothExtendDestination = EnuLocationStationType.None;

        /// <summary>
        /// T轴和R轴Nose扩展目的地 (RS14-RS17)
        /// </summary>
        [ObservableProperty]
        private EnuLocationStationType enuTAndRAxisNoseExtendDestination = EnuLocationStationType.None;

        /// <summary>
        /// R-axis zero
        /// </summary>
        [ObservableProperty]
        private bool rAxisIsZeroPosition;

        #endregion R轴状态

        #region T轴和Z轴高度状态

        /// <summary>
        /// T轴和Z轴高度状态 (RS19-RS26, RS29-RS32)
        /// 使用枚举表示唯一状态，同时只能存在一种状态
        /// </summary>
        [ObservableProperty]
        private EnuTZAxisHeightStatus enuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.None;

        /// <summary>
        /// Z轴零位置
        /// </summary>
        // [property: DisplayName("Z轴零位置")]
        [ObservableProperty]
        private bool zAxisIsZeroPosition;

        /// <summary>
        /// Z轴安全旋转高度	RPS27
        /// </summary>
        // [property: DisplayName("Z轴安全旋转高度")]
        [ObservableProperty]
        private bool zAxisisHeightForRotation;

        #endregion T轴和Z轴高度状态

        #region Cassette 位置的Z轴高度：其它地方已经计算过了，这里只需要存储即可

        /*

        //Cassette 位置的Z轴高度：其它地方已经计算过了，这里只需要存储即可

         T-axis and Z-axis height smooth to shuttle 1 slot x get RS33 A XX(8inch)   shuttle 1端Smoth、Nose晶圆：取片高度 T:RP4 Z:(RS41+RS42)/2+(x-1)*1270+RPS20
            RS33 B XX(6inch)       T:RP4 Z:(RS41+RS42)/2+(x-1)*952+RPS19
            RS33 C XX(4inch)       T:RP4 Z:(RS41+RS42)/2+(x-1)*952+RPS18
        T-axis and Z-axis height nose to shuttle 1 slot x get RS34 A XX(8inch)       T:RP8 Z:(RS43+RS44)/2+(x-1)*1270+RPS20
            RS34 B XX(6inch)       T:RP8 Z:(RS43+RS44)/2+(x-1)*952+RPS19
            RS34 C XX(4inch)       T:RP8 Z:(RS43+RS44)/2+(x-1)*952+RPS18
        T-axis and Z-axis height smooth to shuttle 1 slot x put RS35 A XX(8inch)   "shuttle 1端Smoth、Nose晶圆：放片高度
        （+加上不同Cassette尺寸取放片偏差）"	T:RP4      Z:(RS41+RS42)/2+(x-1)*1270+RPS20+RPS23
	        RS35 B XX(6inch)       T:RP4 Z:(RS41+RS42)/2+(x-1)*952+RPS19+RPS22
            RS35 C XX(4inch)       T:RP4 Z:(RS41+RS42)/2+(x-1)*952+RPS18+RPS21
        T-axis and Z-axis height nose to shuttle 1 slot x put RS36 A XX(8inch)       T:RP8 Z:(RS43+RS44)/2+(x-1)*1270+RPS20+RPS23
            RS36 B XX(6inch)       T:RP8 Z:(RS43+RS44)/2+(x-1)*952+RPS19+RPS22
            RS36 C XX(4inch)       T:RP8 Z:(RS43+RS44)/2+(x-1)*952+RPS18+RPS21
        设计这个晶圆盒槽位状态的数据结构，用于存储Shuttle1和Shuttle2的槽位状态
        */

        #endregion Cassette 位置的Z轴高度：其它地方已经计算过了，这里只需要存储即可

        #region Pin Search状态

        /// <summary>
        /// Pin Search状态
        /// </summary>
        [ObservableProperty]
        private bool pinSearchStatus;

        /// <summary>
        /// Pin Search数据有效
        /// </summary>
        [ObservableProperty]
        private EnuPinSearchStatus enuPinSearchDataEffective;

        #region S1 pin search p2RS42smooth to shutle 1 (计算取放片基准点)

        /// <summary>
        /// Shuttle1 Smooth端 Pin Search基准点1
        /// </summary>
        [ObservableProperty]
        private int shuttle1PinSearchSmoothP1;

        /// <summary>
        /// Shuttle1 Smooth端 Pin Search基准点2
        /// </summary>
        [ObservableProperty]
        private int shuttle1PinSearchSmoothP2;

        #endregion S1 pin search p2RS42smooth to shutle 1 (计算取放片基准点)

        #region nose to shuttle 1 (计算取放片基准点)

        /// <summary>
        /// Shuttle1 Nose端 Pin Search基准点3
        /// </summary>
        [ObservableProperty]
        private int shuttle1PinSearchNoseP3;

        /// <summary>
        /// Shuttle1 Nose端 Pin Search基准点4
        /// </summary>
        [ObservableProperty]
        private int shuttle1PinSearchNoseP4;

        #endregion nose to shuttle 1 (计算取放片基准点)

        #region S2 pin search p2RS46smooth to shuttle 2 (计算取放片基准点)

        /// <summary>
        /// Shuttle2 Smooth端 Pin Search基准点1
        /// </summary>
        [ObservableProperty]
        private ushort shuttle2PinSearchSmoothP1;

        /// <summary>
        /// Shuttle2 Smooth端 Pin Search基准点2
        /// </summary>
        [ObservableProperty]
        private ushort shuttle2PinSearchSmoothP2;

        #endregion S2 pin search p2RS46smooth to shuttle 2 (计算取放片基准点)

        #region nose to shuttle 2 (计算取放片基准点)

        /// <summary>
        /// Shuttle2 Nose端 Pin Search基准点3
        /// </summary>
        [ObservableProperty]
        private int shuttle2PinSearchNoseP3;

        /// <summary>
        /// Shuttle2 Nose端 Pin Search基准点4
        /// </summary>
        [ObservableProperty]
        private int shuttle2PinSearchNoseP4;

        #endregion nose to shuttle 2 (计算取放片基准点)

        #endregion Pin Search状态

        #region Paddle状态

        #region 双臂4个 Paddle上Wafer存在否

        /// <summary>
        /// Smooth端Paddle P1状态
        /// </summary>
        [ObservableProperty]
        private bool smoothPaddleP1Status;

        /// <summary>
        /// Smooth端Paddle P2状态
        /// </summary>
        [ObservableProperty]
        private bool smoothPaddleP2Status;

        /// <summary>
        /// Nose端Paddle P3状态
        /// </summary>
        [ObservableProperty]
        private bool nosePaddleP3Status;

        /// <summary>
        /// Nose端Paddle P4状态
        /// </summary>
        [ObservableProperty]
        private bool nosePaddleP4Status;

        #endregion 双臂4个 Paddle上Wafer存在否

        /// <summary>
        /// Smooth端paddle状态
        /// </summary>
        [ObservableProperty]
        private bool smoothPaddleStatus;

        /// <summary>
        /// Nose端paddle状态
        /// </summary>
        [ObservableProperty]
        private bool nosePaddleStatus;

        #endregion Paddle状态

        public override string ToString()
        {
            var sb = new System.Text.StringBuilder();

            // Robot主状态
            sb.AppendLine($"EnuRobotStatus = {EnuRobotStatus}");

            #region T轴状态

            sb.AppendLine($"EnuTAxisSmoothDestination = {EnuTAxisSmoothDestination}");
            sb.AppendLine($"EnuTAxisNoseDestination = {EnuTAxisNoseDestination}");
            sb.AppendLine($"TAxisIsZeroPosition = {TAxisIsZeroPosition}");

            #endregion T轴状态

            #region R轴状态

            sb.AppendLine($"EnuTAndRAxisSmoothExtendDestination = {EnuTAndRAxisSmoothExtendDestination}");
            sb.AppendLine($"EnuTAndRAxisNoseExtendDestination = {EnuTAndRAxisNoseExtendDestination}");
            sb.AppendLine($"RAxisIsZeroPosition = {RAxisIsZeroPosition}");

            #endregion R轴状态

            // T轴和Z轴高度状态
            sb.AppendLine($"EnuTAndZAxisHeightStatus = {EnuTAndZAxisHeightStatus}");
            sb.AppendLine($"ZAxisIsZeroPosition = {ZAxisIsZeroPosition}");
            sb.AppendLine($"PinSearchStatus = {PinSearchStatus}");
            sb.AppendLine($"EnuPinSearchDataEffective = {EnuPinSearchDataEffective}");

            // Pin Search相关属性

            // Shuttle1 Pin Search相关属性
            sb.AppendLine($"Shuttle1PinSearchSmoothP1 = {Shuttle1PinSearchSmoothP1}");
            sb.AppendLine($"Shuttle1PinSearchSmoothP2 = {Shuttle1PinSearchSmoothP2}");
            sb.AppendLine($"Shuttle1PinSearchNoseP3 = {Shuttle1PinSearchNoseP3}");
            sb.AppendLine($"Shuttle1PinSearchNoseP4 = {Shuttle1PinSearchNoseP4}");

            // Shuttle2 Pin Search相关属性
            sb.AppendLine($"Shuttle2PinSearchSmoothP1 = {Shuttle2PinSearchSmoothP1}");
            sb.AppendLine($"Shuttle2PinSearchSmoothP2 = {Shuttle2PinSearchSmoothP2}");
            sb.AppendLine($"Shuttle2PinSearchNoseP3 = {Shuttle2PinSearchNoseP3}");
            sb.AppendLine($"Shuttle2PinSearchNoseP4 = {Shuttle2PinSearchNoseP4}");

            // Paddle状态
            sb.AppendLine($"SmoothPaddleP1Status = {SmoothPaddleP1Status}");
            sb.AppendLine($"SmoothPaddleP2Status = {SmoothPaddleP2Status}");
            sb.AppendLine($"NosePaddleP3Status = {NosePaddleP3Status}");
            sb.AppendLine($"NosePaddleP4Status = {NosePaddleP4Status}");

            // Paddle状态
            sb.AppendLine($"SmoothPaddleStatus = {SmoothPaddleStatus}");
            sb.AppendLine($"NosePaddleStatus = {NosePaddleStatus}");
            sb.AppendLine($"SmoothPaddleStatus = {SmoothPaddleStatus}");
            sb.AppendLine($"NosePaddleStatus = {NosePaddleStatus}");

            // Shuttle槽位状态（如有定义）
            // sb.AppendLine($"ShuttleSlotStatus = {ShuttleSlotStatus}");

            // 可根据需要继续添加其他状态属性
            return sb.ToString();
        }

        //
        // #region Shuttle槽位状态
        //
        // /// <summary>
        // /// Shuttle槽位状态
        // /// </summary>
        // [ObservableProperty]
        // private ShuttleSlotStatusEnum shuttleSlotStatus;
        //
        // #endregion Shuttle槽位状态
    }
}