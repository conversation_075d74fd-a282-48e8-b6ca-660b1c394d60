﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using Zishan.SS200.Cmd.Models.SS200.AlarmCode;
using <PERSON>ishan.SS200.Cmd.Models.SS200.SubsystemConfigure;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;

namespace Zishan.SS200.Cmd.Models.SS200
{
    /*
 为了方便调用，封装一个单例 SS200InterLock，通过设计模式，比如工厂模式，实现统一入口访问：访问设备IO，比如通过CoilStatusHelper、访问设备计算后的状态，如：
    RobotSubsystemStatus.cs、ChamberSubsystemStatus.cs、ShuttleSubsystemStatus.cs，报警信息，比如根据枚举EnuChaAlarmCodes获取报警信息，统一访问入口访问，例如：

//IO调用
SS200.IOinterface.Robot.RDI1_枚举用途描述.Value（动态计算属性）
SS200.IOinterface.Robot.RDI1_枚举用途描述.Content（预先设置值）
SS200.IOinterface.ChamberA.PDI1_枚举用途描述.Value（动态计算属性）
SS200.IOinterface.ChamberB.PDI2_枚举用途描述.Content（预先设置值）
//报警调用
SS200.AlarmCode.Robt.RA1_枚举用途描述.Content（预先设置值）
SS200.AlarmCode.Robt.RA1_枚举用途描述.ChsContent（预先设置值）
SS200.AlarmCode.Robt.RA1_枚举用途描述.Cause（预先设置值）

//子系统配置调用
SS200.SubsystemConfigure.PositionValue.RP1_枚举用途描述.Value（预先设置值）
SS200.SubsystemConfigure.PositionValue.RP1_枚举用途描述.Range（预先设置值）

//子系统状态调用
SS200.SubsystemStatus.Robot.RS1_枚举用途描述.Value（动态计算属性）
SS200.SubsystemStatus.Robot.RS1_枚举用途描述.Content（预先设置值）

SS200.SubsystemStatus.ChamberA.PositonS. SlitDoorStatus.Open（动态计算属性，能尽量使用枚举，确保唯一性）
*/

    /// <summary>
    /// 机械臂报警错误码枚举，表示机械臂的错误状态
    /// </summary>
    public enum EnuRobotErrorCode
    {
        /// <summary>
        /// A面 Nose端,主要用来抓Cassettes的Wafer
        /// </summary>

        RA1_Robot系统忙被拒绝 = 0,
        RA2 = 2,
        RA3 = 3,
    }

    /// <summary>
    /// SS200InterLock类，表示SS200设备的互锁状态条件信息和报警信息【取消 SS200InterLock的引用，文件代码暂时保留】
    /// </summary>
    public class SS200InterLock
    {
        #region 传感器IO配置信息，能实时反馈过来

        public IOInterface IoInterface { get; set; } = new();

        #endregion 传感器IO配置信息，能实时反馈过来

        #region 报警信息

        public Dictionary<EnuRobotErrorCode, AlarmItem> DicRobotAlarmItems { get; set; } = new();

        public List<AlarmItem> RobotAlarmItems { get; set; } = new();

        public List<AlarmItem> ShuttleAlarmItems { get; set; } = new();

        public List<AlarmItem> ChaAlarmItems { get; set; } = new();

        public List<AlarmItem> ChbAlarmItems { get; set; } = new();

        #endregion 报警信息

        #region 配置信息

        public List<ConfigureSetting> RobotPositionValue { get; set; } = new();
        public List<ConfigureSetting> RobotConfigureSettings { get; set; } = new();

        public List<ConfigureSetting> ChaConfigureSettings { get; set; } = new();
        public List<ConfigureSetting> ChbConfigureSettings { get; set; } = new();

        public List<ConfigureSetting> CoolingConfigureSettings { get; set; } = new();

        public List<ConfigureSetting> ShuttleConfigureSettings { get; set; } = new();

        public List<ConfigureSetting> MainSystemConfigureSettings { get; set; } = new();

        #endregion 配置信息

        #region 状态信息

        public ObservableCollection<RobotSubsystemStatus> RobotStatuValue { get; set; } = new();

        #endregion 状态信息
    }
}