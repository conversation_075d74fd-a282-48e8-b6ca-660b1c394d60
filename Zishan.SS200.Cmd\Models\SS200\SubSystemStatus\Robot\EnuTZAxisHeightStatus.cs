using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot
{
    /// <summary>
    /// T轴和Z轴高度状态枚举
    /// </summary>
    public enum EnuTZAxisHeightStatus
    {
        /// <summary>
        /// 无状态
        /// </summary>
        [Description("无状态")]
        None = 0,

        /// <summary>
        /// Smoth端到CHA
        /// </summary>
        [Description("Smoth端到CHA")]
        SmoothToCHA = 1,

        /// <summary>
        /// Smoth端到CHB
        /// </summary>
        [Description("Smoth端到CHB")]
        SmoothToCHB = 2,

        /// <summary>
        /// Smoth端到CT get
        /// </summary>
        [Description("Smoth端到CT get")]
        SmoothToCTGet = 3,

        /// <summary>
        /// Smoth端到CB get
        /// </summary>
        [Description("Smoth端到CB get")]
        SmoothToCBGet = 4,

        /// <summary>
        /// Nose端到CHA
        /// </summary>
        [Description("Nose端到CHA")]
        NoseToCHA = 5,

        /// <summary>
        /// Nose端到CHB
        /// </summary>
        [Description("Nose端到CHB")]
        NoseToCHB = 6,

        /// <summary>
        /// Nose端到CT get
        /// </summary>
        [Description("Nose端到CT get")]
        NoseToCTGet = 7,

        /// <summary>
        /// Nose端到CB get
        /// </summary>
        [Description("Nose端到CB get")]
        NoseToCBGet = 8,

        /// <summary>
        /// Z轴零位置
        /// </summary>
        [Description("Z轴零位置")]
        ZAxisZeroPosition = 9,

        /// <summary>
        /// T轴和Z轴高度Pin搜索
        /// </summary>
        [Description("T轴和Z轴高度Pin搜索")]
        PinSearch = 10,

        /// <summary>
        /// Smoth端到CT put
        /// </summary>
        [Description("Smoth端到CT put")]
        SmoothToCTPut = 11,

        /// <summary>
        /// Smoth端到CB put
        /// </summary>
        [Description("Smoth端到CB put")]
        SmoothToCBPut = 12,

        /// <summary>
        /// Nose端到CT put
        /// </summary>
        [Description("Nose端到CT put")]
        NoseToCTPut = 13,

        /// <summary>
        /// Nose端到CB put
        /// </summary>
        [Description("Nose端到CB put")]
        NoseToCBPut = 14
    }
}