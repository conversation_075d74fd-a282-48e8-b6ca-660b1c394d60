using System;
using System.Collections.Generic;
using System.Linq;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// Robot位置参数访问器测试类
    /// 用于验证所有RP1-RP28位置参数访问器的功能
    /// </summary>
    public class RobotPositionParametersAccessorTest
    {
        /// <summary>
        /// 测试所有T轴位置参数访问器
        /// </summary>
        public static bool TestTAxisPositionAccessors()
        {
            try
            {
                Console.WriteLine("测试T轴位置参数访问器...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                var tAxisAccessors = new Dictionary<string, object>
                {
                    ["RP1"] = robotConfig.RP1_TAxisSmoothToCHA,
                    ["RP2"] = robotConfig.RP2_TAxisSmoothToCHB,
                    ["RP3"] = robotConfig.RP3_TAxisSmoothToCoolingChamber,
                    ["RP4"] = robotConfig.RP4_TAxisSmoothToCassette,
                    ["RP5"] = robotConfig.RP5_TAxisNoseToCHA,
                    ["RP6"] = robotConfig.RP6_TAxisNoseToCHB,
                    ["RP7"] = robotConfig.RP7_TAxisNoseToCoolingChamber,
                    ["RP8"] = robotConfig.RP8_TAxisNoseToCassette,
                    ["RP9"] = robotConfig.RP9_TAxisZero
                };
                
                int successCount = 0;
                foreach (var accessor in tAxisAccessors)
                {
                    bool success = accessor.Value != null;
                    Console.WriteLine($"  {accessor.Key} T轴参数访问器: {(success ? "✓" : "✗")}");
                    if (success) successCount++;
                }
                
                Console.WriteLine($"T轴参数访问器测试完成: {successCount}/{tAxisAccessors.Count}");
                return successCount == tAxisAccessors.Count;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"T轴位置参数访问器测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试所有R轴位置参数访问器
        /// </summary>
        public static bool TestRAxisPositionAccessors()
        {
            try
            {
                Console.WriteLine("测试R轴位置参数访问器...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                var rAxisAccessors = new Dictionary<string, object>
                {
                    ["RP10"] = robotConfig.RP10_RAxisSmoothExtendFaceToCHA,
                    ["RP11"] = robotConfig.RP11_RAxisSmoothExtendFaceToCHB,
                    ["RP12"] = robotConfig.RP12_RAxisNoseExtendFaceToCHA,
                    ["RP13"] = robotConfig.RP13_RAxisNoseExtendFaceToCHB,
                    ["RP14"] = robotConfig.RP14_RAxisSmoothFaceToCoolingChamberAndExtend,
                    ["RP15"] = robotConfig.RP15_RAxisNoseExtendFaceToCoolingChamber,
                    ["RP16"] = robotConfig.RP16_RAxisSmoothFaceToCassetteAndExtend,
                    ["RP17"] = robotConfig.RP17_RAxisNoseFaceToCassetteAndExtend,
                    ["RP18"] = robotConfig.RP18_RAxisZeroPosition
                };
                
                int successCount = 0;
                foreach (var accessor in rAxisAccessors)
                {
                    bool success = accessor.Value != null;
                    Console.WriteLine($"  {accessor.Key} R轴参数访问器: {(success ? "✓" : "✗")}");
                    if (success) successCount++;
                }
                
                Console.WriteLine($"R轴参数访问器测试完成: {successCount}/{rAxisAccessors.Count}");
                return successCount == rAxisAccessors.Count;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"R轴位置参数访问器测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试所有Z轴位置参数访问器
        /// </summary>
        public static bool TestZAxisPositionAccessors()
        {
            try
            {
                Console.WriteLine("测试Z轴位置参数访问器...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                var zAxisAccessors = new Dictionary<string, object>
                {
                    ["RP19"] = robotConfig.RP19_ZAxisHeightAtSmoothToCHA,
                    ["RP20"] = robotConfig.RP20_ZAxisHeightAtSmoothToCHB,
                    ["RP21"] = robotConfig.RP21_ZAxisHeightAtSmoothToCT,
                    ["RP22"] = robotConfig.RP22_ZAxisHeightAtSmoothToCB,
                    ["RP23"] = robotConfig.RP23_ZAxisHeightAtNoseToCHA,
                    ["RP24"] = robotConfig.RP24_ZAxisHeightAtNoseToCHB,
                    ["RP25"] = robotConfig.RP25_ZAxisHeightAtNoseToCTGet,
                    ["RP26"] = robotConfig.RP26_ZAxisHeightAtNoseToCBGet,
                    ["RP27"] = robotConfig.RP27_ZAxisZeroPosition,
                    ["RP28"] = robotConfig.RP28_ZAxisHeightToPinSearch
                };
                
                int successCount = 0;
                foreach (var accessor in zAxisAccessors)
                {
                    bool success = accessor.Value != null;
                    Console.WriteLine($"  {accessor.Key} Z轴参数访问器: {(success ? "✓" : "✗")}");
                    if (success) successCount++;
                }
                
                Console.WriteLine($"Z轴参数访问器测试完成: {successCount}/{zAxisAccessors.Count}");
                return successCount == zAxisAccessors.Count;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Z轴位置参数访问器测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试位置参数的轴类型分类
        /// </summary>
        public static bool TestAxisTypeClassification()
        {
            try
            {
                Console.WriteLine("测试位置参数轴类型分类...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 测试T轴参数的轴类型
                var rp1 = robotConfig.RP1_TAxisSmoothToCHA;
                var rp9 = robotConfig.RP9_TAxisZero;
                
                // 测试R轴参数的轴类型
                var rp10 = robotConfig.RP10_RAxisSmoothExtendFaceToCHA;
                var rp18 = robotConfig.RP18_RAxisZeroPosition;
                
                // 测试Z轴参数的轴类型
                var rp19 = robotConfig.RP19_ZAxisHeightAtSmoothToCHA;
                var rp28 = robotConfig.RP28_ZAxisHeightToPinSearch;
                
                bool tAxisTypeCorrect = rp1?.AxisType == 1 && rp9?.AxisType == 1;
                bool rAxisTypeCorrect = rp10?.AxisType == 2 && rp18?.AxisType == 2;
                bool zAxisTypeCorrect = rp19?.AxisType == 3 && rp28?.AxisType == 3;
                
                Console.WriteLine($"  T轴类型分类: {(tAxisTypeCorrect ? "✓" : "✗")} (RP1: {rp1?.AxisType}, RP9: {rp9?.AxisType})");
                Console.WriteLine($"  R轴类型分类: {(rAxisTypeCorrect ? "✓" : "✗")} (RP10: {rp10?.AxisType}, RP18: {rp18?.AxisType})");
                Console.WriteLine($"  Z轴类型分类: {(zAxisTypeCorrect ? "✓" : "✗")} (RP19: {rp19?.AxisType}, RP28: {rp28?.AxisType})");
                
                return tAxisTypeCorrect && rAxisTypeCorrect && zAxisTypeCorrect;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"轴类型分类测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试位置参数的完整性
        /// </summary>
        public static bool TestPositionParametersCompleteness()
        {
            try
            {
                Console.WriteLine("测试位置参数完整性...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 使用反射获取所有RP开头的属性
                var rpProperties = robotConfig.GetType()
                    .GetProperties()
                    .Where(p => p.Name.StartsWith("RP") && p.Name.Contains("_"))
                    .ToList();
                
                Console.WriteLine($"  发现RP位置参数访问器数量: {rpProperties.Count}");
                
                // 应该有28个RP参数 (RP1-RP28)
                bool countCorrect = rpProperties.Count == 28;
                Console.WriteLine($"  参数数量正确性: {(countCorrect ? "✓" : "✗")} (期望: 28, 实际: {rpProperties.Count})");
                
                // 检查是否包含所有RP1-RP28
                var expectedRPs = Enumerable.Range(1, 28).Select(i => $"RP{i}").ToList();
                var actualRPs = rpProperties.Select(p => p.Name.Substring(0, p.Name.IndexOf('_'))).ToList();
                
                var missingRPs = expectedRPs.Except(actualRPs).ToList();
                var extraRPs = actualRPs.Except(expectedRPs).ToList();
                
                bool completenessCorrect = missingRPs.Count == 0 && extraRPs.Count == 0;
                Console.WriteLine($"  参数完整性: {(completenessCorrect ? "✓" : "✗")}");
                
                if (missingRPs.Any())
                {
                    Console.WriteLine($"    缺失参数: {string.Join(", ", missingRPs)}");
                }
                
                if (extraRPs.Any())
                {
                    Console.WriteLine($"    多余参数: {string.Join(", ", extraRPs)}");
                }
                
                return countCorrect && completenessCorrect;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"位置参数完整性测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行所有Robot位置参数访问器测试
        /// </summary>
        public static void RunAllPositionParametersTests()
        {
            Console.WriteLine("=== Robot位置参数访问器测试开始 ===\n");
            
            bool tAxisTest = TestTAxisPositionAccessors();
            Console.WriteLine();
            
            bool rAxisTest = TestRAxisPositionAccessors();
            Console.WriteLine();
            
            bool zAxisTest = TestZAxisPositionAccessors();
            Console.WriteLine();
            
            bool axisTypeTest = TestAxisTypeClassification();
            Console.WriteLine();
            
            bool completenessTest = TestPositionParametersCompleteness();
            Console.WriteLine();
            
            // 汇总测试结果
            int passedTests = 0;
            int totalTests = 5;
            
            if (tAxisTest) passedTests++;
            if (rAxisTest) passedTests++;
            if (zAxisTest) passedTests++;
            if (axisTypeTest) passedTests++;
            if (completenessTest) passedTests++;
            
            Console.WriteLine("=== Robot位置参数测试结果汇总 ===");
            Console.WriteLine($"通过测试: {passedTests}/{totalTests}");
            Console.WriteLine($"测试状态: {(passedTests == totalTests ? "全部通过 ✓" : "部分失败 ✗")}");
            
            if (passedTests == totalTests)
            {
                Console.WriteLine("\n🎉 所有Robot位置参数访问器测试通过！RP1-RP28访问器已完整实现。");
            }
            else
            {
                Console.WriteLine("\n⚠️ 部分Robot位置参数测试失败，请检查配置文件和实现。");
            }
        }
    }
}
