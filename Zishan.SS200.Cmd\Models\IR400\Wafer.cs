﻿using Prism.Mvvm;
using <PERSON><PERSON>an.SS200.Cmd.Enums;

// using Zishan.Robot.Shared.Enums;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// Wafer类
    /// </summary>
    public class Wafer : BindableBase
    {
        /// <summary>
        /// CassetteID，及流水号ID号
        /// </summary>
        public string CassetteId { get => _CassetteId; set => SetProperty(ref _CassetteId, value); }
        private string _CassetteId;

        /// <summary>
        /// Wafer编号：1~25
        /// </summary>
        public int WaferNo { get => _WaferNo; private init => SetProperty(ref _WaferNo, value); }
        private readonly int _WaferNo;

        public string PrefixedWaferNo
        {
            get { return "槽_" + WaferNo; }
        }

        public string Name { get; set; }

        /// <summary>
        /// True在左边Cassette,否则在右边Cassette
        /// </summary>
        //public bool IsInLeft { get; set; }

        /// <summary>
        /// Wafer所在腔体WaferSide：左边、右边
        /// </summary>

        public EnuChamberWaferSide ChamberWaferSide { get; set; }

        /// <summary>
        /// 未知面，针对非机械臂选Unknown
        /// </summary>
        public EnuArmFetchSide ArmFetchSide { get; set; }

        /// <summary>
        /// Wafer状态
        /// </summary>
        public EnuWaferStatus WaferStatus
        {
            get { return _WaferStatus; }
            set
            {
                SetProperty(ref _WaferStatus, value);
                CurWaferToolTip = $"L{WaferNo}，当前状态：{WaferStatus}";
            }
        }
        private EnuWaferStatus _WaferStatus;

        /// <summary>
        /// WaferToolTip提示
        /// </summary>
        public string CurWaferToolTip { get => _CurWaferToolTip; set => SetProperty(ref _CurWaferToolTip, value); }
        private string _CurWaferToolTip;

        // /// <summary>
        // /// 扫片状态
        // /// </summary>
        // public EnuWaferMappingStatus MappingStatus { get => _MappingStatus; set => SetProperty(ref _MappingStatus, value); }
        // private EnuWaferMappingStatus _MappingStatus;

        /// <summary>
        /// 工艺处理结果状态，未完成、工艺故障、自动完成、手动完成
        /// </summary>
        public EnuProcessStatus ProcessStatus { get => _ProcessStatus; set => SetProperty(ref _ProcessStatus, value); }
        private EnuProcessStatus _ProcessStatus;

        // /// <summary>
        // /// 位置号:Slot号，在Cassette的Slot号
        // /// </summary>
        // public Pos WaferPos { get => _WaferPos; set => SetProperty(ref _WaferPos, value); }
        // private Pos _WaferPos;

        /// <summary>
        /// 整个工艺是否处理完成
        /// </summary>
        public bool IsFinisheded { get => _IsFinisheded; set => SetProperty(ref _IsFinisheded, value); }
        private bool _IsFinisheded;

        ///// <summary>
        ///// wafer整个机械臂抓取的历史记录
        ///// </summary>
        ////public List<ArmFetchHistory> ListArmFetchHistory { get; set; }

        public Wafer(EnuChamberWaferSide chamberWaferSide, int id)
        {
            ChamberWaferSide = chamberWaferSide;
            WaferNo = id;
            Name = $"Wafer_{id}";
            WaferStatus = EnuWaferStatus.Have;
        }

        public override string ToString()
        {
            string strResult = $"Wafer_{WaferNo}{(ChamberWaferSide == EnuChamberWaferSide.LeftWafers ? "L" : "R")}_{(IsFinisheded ? "完成" : "未完")}";

            if (ArmFetchSide != EnuArmFetchSide.Unknow)
            {
                strResult += $"[{ArmFetchSide.ToString()}]";
            }
            return strResult;
        }

        /// <summary>
        /// 拖拽显示的字符串
        /// </summary>
        /// <returns></returns>
        public string ToShortString()
        {
            return $"Wafer_{WaferNo}_{(IsFinisheded ? "完成" : "未完")}";
        }
    }
}