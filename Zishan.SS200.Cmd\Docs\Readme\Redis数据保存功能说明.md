# Redis数据保存功能说明

## 概述

`SavaDataToRedis()` 方法提供了完整的 InterLock 数据保存到 Redis 的功能，支持实时监控和数据分析。该功能经过完善，现在支持多种数据类型的保存和灵活的配置选项。

## 功能特性

### 1. 多数据类型支持
- **状态快照**：完整的子系统状态数据
- **IO接口数据**：数字输入/输出状态和传感器值
- **报警数据**：当前活跃报警和报警统计
- **配置数据**：系统配置参数
- **轴位置数据**：RTZ轴的实时位置信息
- **时间序列数据**：用于趋势分析的历史数据

### 2. 灵活的保存选项
```csharp
// 完整保存（默认）
await SavaDataToRedis();

// 选择性保存
await SavaDataToRedis(
    includeIOData: true,      // 包含IO数据
    includeAlarmData: true,   // 包含报警数据
    includeConfigData: false, // 不包含配置数据
    includeAxisData: true     // 包含轴位置数据
);
```

### 3. 多种存储结构
- **String**：完整数据快照，便于版本对比
- **Hash**：结构化数据，便于字段级查询
- **SortedSet**：时间序列数据，便于趋势分析

## 数据结构设计

### 1. Redis 键命名规范
```
SS200:Status:Current        - 当前完整状态快照
SS200:Status:History:{time} - 历史状态快照
SS200:Status:Hash          - Hash结构状态数据
SS200:IO:Current           - 当前IO状态
SS200:Alarm:Current        - 当前报警状态
SS200:Config:Current       - 当前配置信息
SS200:Axis:Position        - 轴位置信息
SS200:Timeline:Status      - 时间序列状态数据
```

### 2. 数据过期策略
| 数据类型 | 过期时间 | 说明 |
|---------|---------|------|
| 当前状态 | 不过期 | 始终保持最新状态 |
| 历史快照 | 24小时 | 保留一天的历史记录 |
| IO状态 | 1小时 | 实时性要求高 |
| 报警信息 | 7天 | 保留一周的报警历史 |
| 配置信息 | 不过期 | 配置变更较少 |
| 轴位置 | 1小时 | 实时位置数据 |
| 时间序列 | 1000条记录 | 滚动保留最新数据 |

## 保存的数据内容

### 1. 状态快照数据
```json
{
  "Timestamp": "2024-01-01 12:00:00.000",
  "UnixTimestamp": 1704067200,
  "DeviceInfo": {
    "DeviceType": "SS200",
    "Version": "1.0.0",
    "Location": "Production Line"
  },
  "SubsystemStatus": {
    "Robot": { /* Robot状态对象 */ },
    "ChamberA": { /* Chamber A状态对象 */ },
    "ChamberB": { /* Chamber B状态对象 */ },
    "Shuttle": { /* Shuttle状态对象 */ }
  }
}
```

### 2. IO接口数据
```json
{
  "Timestamp": "2024-01-01 12:00:00.000",
  "Robot": {
    "DI": {
      "RDI1_PaddleSensor1Left": {
        "Value": true,
        "Content": "Paddle传感器1左侧",
        "IsActive": true
      },
      "RDI2_PaddleSensor2Right": {
        "Value": false,
        "Content": "Paddle传感器2右侧",
        "IsActive": false
      },
      "RDI3_PinSearch1": {
        "Value": true,
        "Content": "Pin搜索1",
        "IsActive": true
      },
      "RDI4_PinSearch2": {
        "Value": false,
        "Content": "Pin搜索2",
        "IsActive": false
      }
    }
  },
  "ChamberA": {
    "DI": {
      "PDI12_SlitDoorOpenSensor": {
        "Value": false,
        "Content": "Slit Door打开传感器",
        "IsActive": false
      },
      "PDI13_SlitDoorCloseSensor": {
        "Value": true,
        "Content": "Slit Door关闭传感器",
        "IsActive": true
      }
    },
    "DO": {
      "PDO10_SlitDoorOpen": {
        "Value": false,
        "Content": "Slit Door打开",
        "IsActive": false
      },
      "PDO11_SlitDoorClose": {
        "Value": true,
        "Content": "Slit Door关闭",
        "IsActive": true
      }
    }
  },
  "ChamberB": { /* 与Chamber A相同结构 */ },
  "Shuttle": {
    "DI": {
      "SDI1_CassetteDoorUpSensor": {
        "Value": true,
        "Content": "晶圆盒门上升传感器",
        "IsActive": true
      },
      "SDI6_PresentSensorCassette1": {
        "Value": false,
        "Content": "存在传感器晶圆盒1",
        "IsActive": false
      }
    },
    "DO": {
      "SDO1_CassetteDoorCylinderUp": {
        "Value": false,
        "Content": "晶圆盒门气缸上升",
        "IsActive": false
      }
    }
  }
}
```

### 3. 轴位置数据
```json
{
  "Timestamp": "2024-01-01 12:00:00.000",
  "Robot": {
    "TAxis": {
      "CurrentPosition": 12345,
      "CurrentDegree": 45.67,
      "IsZeroPosition": false,
      "SmoothDestination": "ChamberA",
      "NoseDestination": "None"
    },
    "RAxis": {
      "CurrentPosition": 23456,
      "CurrentLength": 123.45,
      "IsZeroPosition": true,
      "SmoothExtendDestination": "ChamberA",
      "NoseExtendDestination": "None"
    },
    "ZAxis": {
      "CurrentPosition": 34567,
      "CurrentHeight": 67.89,
      "IsZeroPosition": false,
      "HeightStatus": "ChamberAGet"
    }
  }
}
```

### 4. 报警数据
```json
{
  "Timestamp": "2024-01-01 12:00:00.000",
  "ActiveAlarms": {
    "Robot": {
      "RA1_SystemBusyReject": {
        "Content": "Robot system status is busy, command reject",
        "ChsContent": "机器人系统忙碌状态，指令被拒绝",
        "Code": "RA1",
        "Cause": "系统忙碌",
        "Item": 1
      },
      "RA2_SystemAlarmReject": {
        "Content": "Robot system status is alarm, command reject",
        "ChsContent": "机器人系统报警状态，指令被拒绝",
        "Code": "RA2",
        "Cause": "系统报警",
        "Item": 2
      }
    },
    "ChamberA": {
      "AlarmSystemAvailable": true,
      "LastCheckTime": "2024-01-01 12:00:00.000",
      "Note": "Chamber A报警系统正常运行"
    },
    "Shuttle": {
      "SA1_SystemBusyReject": {
        "Content": "alarm 1 system busy command reject",
        "ChsContent": "报警1：系统忙碌，指令被拒绝",
        "Code": "SA1",
        "Cause": "系统忙碌",
        "Item": 1
      }
    }
  },
  "AlarmStatistics": {
    "TotalAlarmTypes": 4,
    "RobotAlarmCount": 6,
    "ChamberAAlarmCount": 3,
    "ChamberBAlarmCount": 1,
    "ShuttleAlarmCount": 3,
    "LastUpdateTime": "2024-01-01 12:00:00.000"
  }
}
```

### 5. 配置数据
```json
{
  "Timestamp": "2024-01-01 12:00:00.000",
  "MainSystem": {
    "SSC1_Shuttle1WaferSize": 8,
    "SSC2_Shuttle2WaferSize": 8,
    "SSC3_ChamberLocation": "CHA and CHB",
    "SSC6_CassetteNestType": "fixed",
    "ConfigCount": 4,
    "LastUpdateTime": "2024-01-01 12:00:00.000"
  },
  "Robot": {
    "RP1_TAxisSmoothToCHA": 12345,
    "RP2_TAxisSmoothToCHB": 23456,
    "RP4_TAxisSmoothToCassette": 34567,
    "RP5_TAxisNoseToCHA": 45678,
    "RP6_TAxisNoseToCHB": 56789,
    "RP8_TAxisNoseToCassette": 67890,
    "ConfigCount": 6,
    "LastUpdateTime": "2024-01-01 12:00:00.000"
  },
  "ChamberA": {
    "ConfigSystemAvailable": true,
    "LastUpdateTime": "2024-01-01 12:00:00.000",
    "Note": "Chamber A配置系统正常"
  },
  "Shuttle": {
    "ConfigSystemAvailable": true,
    "LastUpdateTime": "2024-01-01 12:00:00.000",
    "Note": "Shuttle配置系统正常"
  }
}
```

## 性能特性

### 1. 异步执行
- 所有Redis操作都在后台线程执行
- 不阻塞UI线程
- 支持并发操作

### 2. 错误处理
- 连接状态检查
- 异常捕获和日志记录
- 操作结果返回
- 性能监控（执行时间统计）

### 3. 数据管理
- 自动清理过期数据
- 时间序列数据滚动保留
- 内存使用优化

## 使用示例

### 1. 基本使用
```csharp
// 在ViewModel中调用
private async void SaveDataToRedis()
{
    var success = await SavaDataToRedis();
    if (success)
    {
        AppLog.Info("数据保存成功");
    }
    else
    {
        AppLog.Error("数据保存失败");
    }
}
```

### 2. 定时保存
```csharp
// 设置定时器定期保存数据
private void SetupRedisDataSaving()
{
    var timer = new DispatcherTimer();
    timer.Interval = TimeSpan.FromSeconds(30); // 每30秒保存一次
    timer.Tick += async (sender, e) => await SavaDataToRedis();
    timer.Start();
}
```

### 3. 条件保存
```csharp
// 根据条件选择保存的数据类型
private async Task SaveDataConditionally()
{
    bool isProductionMode = App.AppIniConfig.DatabaseAccessType == EnuDatabaseAccessType.Product_In;
    
    await SavaDataToRedis(
        includeIOData: true,
        includeAlarmData: true,
        includeConfigData: isProductionMode, // 生产模式才保存配置
        includeAxisData: true
    );
}
```

## 监控和诊断

### 1. 日志记录
- 操作成功/失败日志
- 执行时间统计
- 错误详情记录

### 2. 性能监控
```csharp
// 示例日志输出
"InterLock数据保存到Redis成功，耗时: 45ms"
"保存InterLock数据到Redis异常，耗时: 123ms"
```

### 3. 数据验证
- Redis连接状态检查
- 数据有效性验证
- 操作结果确认

## 配置要求

### 1. Redis连接配置
确保 `RedisHelper` 已正确配置连接参数：
- 开发环境：`DESKTOP-ISP61GU:6379`
- 测试环境：`DESKTOP-ISP61GU:6379`
- 生产环境：`192.168.30.30:6379` 或 `192.168.110.102:6379`

### 2. 数据库选择
默认使用 Redis 数据库 6 (`RedisDBEnum.Six`)

### 3. 权限要求
确保Redis用户具有以下权限：
- String 操作权限
- Hash 操作权限
- SortedSet 操作权限
- 过期时间设置权限

## 注意事项

1. **连接检查**：保存前会检查Redis连接状态
2. **数据大小**：大量数据可能影响性能，建议监控数据大小
3. **网络延迟**：网络问题可能导致保存失败
4. **内存使用**：时间序列数据会占用Redis内存，注意监控
5. **并发访问**：多个客户端同时访问时注意数据一致性

## 扩展建议

1. **数据压缩**：对大数据对象进行压缩存储
2. **批量操作**：使用Redis管道提高批量操作性能
3. **数据分片**：大量数据可考虑分片存储
4. **监控告警**：添加Redis操作失败的告警机制
5. **数据备份**：重要数据考虑备份策略
