﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using log4net;

namespace Zishan.SS200.Cmd.Config
{
    /// <summary>
    /// 电机告警信息
    /// </summary>
    public class MotorAlarmInfo
    {
        [JsonPropertyName("cause")]
        public List<string> Cause { get; set; }

        [JsonPropertyName("dispose")]
        public List<string> Dispose { get; set; }

        [JsonPropertyName("kind")]
        public string Kind { get; set; }

        [JsonPropertyName("almrst_clr")]
        public bool AlarmResetClear { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string Code { get; set; }

        public override string ToString()
        {
            string causeText = Cause != null && Cause.Count > 0 ? Cause[0] : string.Empty;
            return $"Kind={Kind}, Cause={causeText}";
        }
    }

    /// <summary>
    /// 电机告警信息解析器
    /// </summary>
    public class MotorAlarmInfoParser
    {
        private readonly Dictionary<string, MotorAlarmInfo> _alarmInfos;
        private readonly string _configFilePath;
        private static readonly ILog _logger = LogManager.GetLogger(typeof(MotorAlarmInfoParser));

        /// <summary>
        /// 初始化电机告警信息解析器
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        public MotorAlarmInfoParser(string configFilePath)
        {
            _configFilePath = configFilePath;
            _alarmInfos = new Dictionary<string, MotorAlarmInfo>();
            LoadConfig();
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    _logger.Error($"电机告警信息配置文件不存在: {_configFilePath}");
                    throw new FileNotFoundException($"Motor alarm info configuration file not found: {_configFilePath}");
                }

                var jsonString = File.ReadAllText(_configFilePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var alarmInfos = JsonSerializer.Deserialize<Dictionary<string, MotorAlarmInfo>>(jsonString, options);
                if (alarmInfos != null)
                {
                    foreach (var alarm in alarmInfos)
                    {
                        var alarmInfo = alarm.Value;
                        alarmInfo.Code = alarm.Key;
                        _alarmInfos[alarm.Key] = alarmInfo;
                    }
                    _logger.Info($"成功加载电机告警信息配置文件，包含{_alarmInfos.Count}条告警信息");
                }
                else
                {
                    _logger.Error($"电机告警信息配置文件反序列化失败，返回null: {_configFilePath}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"加载电机告警信息配置文件失败: {ex.Message}", ex);
                throw new InvalidOperationException($"Failed to load motor alarm info configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取电机告警信息
        /// </summary>
        /// <param name="code">错误代码</param>
        /// <returns>电机告警信息</returns>
        public MotorAlarmInfo GetAlarmInfo(string code)
        {
            if (_alarmInfos.TryGetValue(code, out var alarmInfo))
            {
                return alarmInfo;
            }
            _logger.Warn($"未找到电机告警信息: {code}");
            throw new KeyNotFoundException($"Motor alarm info with code '{code}' not found");
        }

        /// <summary>
        /// 尝试获取电机告警信息
        /// </summary>
        /// <param name="code">错误代码</param>
        /// <param name="alarmInfo">电机告警信息</param>
        /// <returns>是否成功获取告警信息</returns>
        public bool TryGetAlarmInfo(string code, out MotorAlarmInfo alarmInfo)
        {
            _logger.Debug($"尝试获取电机告警信息，错误代码: {code}");
            
            // 尝试直接获取
            if (_alarmInfos.TryGetValue(code, out alarmInfo))
            {
                _logger.Debug($"成功找到电机告警信息: {code} -> {alarmInfo.Kind}");
                return true;
            }
            
            // 如果为0，表示无错误
            if (code == "0" || code == "0000" || code == "0x0000" || code == "0x0")
            {
                _logger.Debug("电机告警代码为0，表示无错误");
                alarmInfo = null;
                return false;
            }
            
            // 尝试不同格式（去掉前缀，确保4位，等）
            string cleanCode = code.Replace("0x", "").Trim().ToUpper();
            if (_alarmInfos.TryGetValue(cleanCode, out alarmInfo))
            {
                _logger.Debug($"使用清理后的代码找到电机告警信息: {cleanCode} -> {alarmInfo.Kind}");
                return true;
            }
            
            // 尝试使用4位格式（补0）
            string paddedCode = cleanCode.PadLeft(4, '0');
            if (_alarmInfos.TryGetValue(paddedCode, out alarmInfo))
            {
                _logger.Debug($"使用补零后的代码找到电机告警信息: {paddedCode} -> {alarmInfo.Kind}");
                return true;
            }
            
            _logger.Warn($"未找到匹配的电机告警信息: {code}");
            alarmInfo = null;
            return false;
        }

        /// <summary>
        /// 获取所有电机告警信息
        /// </summary>
        /// <returns>所有电机告警信息</returns>
        public IEnumerable<MotorAlarmInfo> GetAllAlarmInfos()
        {
            return _alarmInfos.Values;
        }

        /// <summary>
        /// 获取可通过复位清除的告警信息
        /// </summary>
        /// <returns>可通过复位清除的告警信息</returns>
        public IEnumerable<MotorAlarmInfo> GetResetClearableAlarmInfos()
        {
            var result = new List<MotorAlarmInfo>();
            foreach (var alarm in _alarmInfos.Values)
            {
                if (alarm.AlarmResetClear)
                {
                    result.Add(alarm);
                }
            }
            return result;
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfig()
        {
            _alarmInfos.Clear();
            LoadConfig();
        }
    }
}