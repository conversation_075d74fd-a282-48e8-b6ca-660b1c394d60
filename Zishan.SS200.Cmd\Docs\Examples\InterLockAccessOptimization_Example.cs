using System;
using Zishan.SS200.Cmd.Helpers;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// InterLock 访问优化示例
    /// 展示如何使用 InterLockAccessHelper 来优化 InterLock 调用
    /// </summary>
    public class InterLockAccessOptimizationExample
    {
        /// <summary>
        /// 运行所有优化示例
        /// </summary>
        public static void RunAllExamples()
        {
            Console.WriteLine("=== InterLock 访问优化示例 ===");

            // 1. 基本优化示例
            BasicOptimizationExample();

            // 2. 批量传感器读取示例
            BatchSensorReadingExample();

            // 3. 批量配置读取示例
            BatchConfigReadingExample();

            // 4. 状态检查优化示例
            StatusCheckOptimizationExample();

            // 5. 复杂业务逻辑优化示例
            ComplexBusinessLogicExample();

            Console.WriteLine("=== 所有优化示例执行完成 ===");
        }

        /// <summary>
        /// 基本优化示例：对比优化前后的代码
        /// </summary>
        private static void BasicOptimizationExample()
        {
            Console.WriteLine("\n--- 基本优化示例 ---");

            // ❌ 优化前：重复调用单例
            Console.WriteLine("优化前的代码（重复调用单例）：");
            var robotValue_Old = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
            var chamberValue_Old = SS200InterLockMain.Instance.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
            var shuttleValue_Old = SS200InterLockMain.Instance.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Value;

            // ✅ 优化后：使用助手类缓存实例
            Console.WriteLine("优化后的代码（使用助手类）：");
            InterLockAccessHelper.WithIOInterface(io =>
            {
                var robotValue = io.Robot.RDI1_PaddleSensor1Left.Value;
                var chamberValue = io.ChamberA.PDI12_SlitDoorOpenSensor.Value;
                var shuttleValue = io.Shuttle.SDI6_PresentSensorCassette1.Value;

                Console.WriteLine($"Robot传感器: {robotValue}, Chamber传感器: {chamberValue}, Shuttle传感器: {shuttleValue}");
            });
        }

        /// <summary>
        /// 批量传感器读取示例
        /// </summary>
        private static void BatchSensorReadingExample()
        {
            Console.WriteLine("\n--- 批量传感器读取示例 ---");

            // 使用批量读取方法
            var sensorValues = InterLockAccessHelper.GetSensorValues(
                io => io.Robot.RDI1_PaddleSensor1Left.Value,
                io => io.Robot.RDI2_PaddleSensor2Right.Value,
                io => io.ChamberA.PDI12_SlitDoorOpenSensor.Value,
                io => io.ChamberB.PDI12_SlitDoorOpenSensor.Value,
                io => io.Shuttle.SDI6_PresentSensorCassette1.Value
            );

            Console.WriteLine("批量传感器读取结果：");
            Console.WriteLine($"Robot Paddle1 Left: {sensorValues[0]}");
            Console.WriteLine($"Robot Paddle1 Right: {sensorValues[1]}");
            Console.WriteLine($"Chamber A Slit Door: {sensorValues[2]}");
            Console.WriteLine($"Chamber B Slit Door: {sensorValues[3]}");
            Console.WriteLine($"Shuttle Cassette1: {sensorValues[4]}");
        }

        /// <summary>
        /// 批量配置读取示例
        /// </summary>
        private static void BatchConfigReadingExample()
        {
            Console.WriteLine("\n--- 批量配置读取示例 ---");

            // 使用批量读取方法
            var configValues = InterLockAccessHelper.GetConfigValues(
                config => config.Robot.RP1_TAxisSmoothToCHA?.Value ?? 0,
                config => config.Robot.RPS1_RobotRotateSpeed?.Value ?? 0,
                config => config.ChamberA.PPS1_SlitDoorMotionMinTime?.Value ?? 0,
                config => config.Shuttle.SPS1_CassetteNestExtendRetractMinTime?.Value ?? 0
            );

            Console.WriteLine("批量配置读取结果：");
            Console.WriteLine($"Robot T轴位置: {configValues[0]}");
            Console.WriteLine($"Robot 旋转速度: {configValues[1]}");
            Console.WriteLine($"Chamber A 门运动时间: {configValues[2]}");
            Console.WriteLine($"Shuttle 巢伸缩时间: {configValues[3]}");
        }

        /// <summary>
        /// 状态检查优化示例
        /// </summary>
        private static void StatusCheckOptimizationExample()
        {
            Console.WriteLine("\n--- 状态检查优化示例 ---");

            // 使用便捷方法获取状态
            var robotStatus = InterLockAccessHelper.GetRobotStatus();
            var chamberAStatus = InterLockAccessHelper.GetChamberAStatus();
            var shuttleStatus = InterLockAccessHelper.GetShuttleStatus();

            if (robotStatus != null)
            {
                Console.WriteLine($"Robot状态: {robotStatus.EnuRobotStatus}");
                Console.WriteLine($"T轴零位: {robotStatus.TAxisIsZeroPosition}");
                Console.WriteLine($"R轴零位: {robotStatus.RAxisIsZeroPosition}");
            }

            // 使用位置检查方法
            bool isRobotAtZero = InterLockAccessHelper.CheckRobotPosition(status =>
                status.TAxisIsZeroPosition && status.RAxisIsZeroPosition && status.ZAxisIsZeroPosition);

            Console.WriteLine($"Robot是否在零位: {isRobotAtZero}");
        }

        /// <summary>
        /// 复杂业务逻辑优化示例
        /// </summary>
        private static void ComplexBusinessLogicExample()
        {
            Console.WriteLine("\n--- 复杂业务逻辑优化示例 ---");

            // ❌ 优化前：多次重复调用
            Console.WriteLine("优化前的复杂逻辑：");
            var result_old = CheckRobotReadyForOperation_Old();
            Console.WriteLine($"Robot准备状态（优化前）: {result_old}");

            // ✅ 优化后：使用助手类
            Console.WriteLine("优化后的复杂逻辑：");
            var result_new = CheckRobotReadyForOperation_New();
            Console.WriteLine($"Robot准备状态（优化后）: {result_new}");
        }

        /// <summary>
        /// 优化前的复杂业务逻辑（多次重复调用）
        /// </summary>
        private static bool CheckRobotReadyForOperation_Old()
        {
            // 多次重复调用 SS200InterLockMain.Instance
            var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
            var paddleSensor1 = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
            var paddleSensor2 = SS200InterLockMain.Instance.IOInterface.Robot.RDI2_PaddleSensor2Right.Value;
            var slideOutSensorEnable = SS200InterLockMain.Instance.SubsystemConfigure.Shuttle.SPS11_SlideOutBackSensorEnable.BoolValue;
            var rotateSpeed = SS200InterLockMain.Instance.SubsystemConfigure.Robot.RPS1_RobotRotateSpeed?.Value ?? 0;

            return robotStatus != null &&
                   robotStatus.TAxisIsZeroPosition &&
                   robotStatus.RAxisIsZeroPosition &&
                   !paddleSensor1 &&
                   !paddleSensor2 &&
                   rotateSpeed > 0;
        }

        /// <summary>
        /// 优化后的复杂业务逻辑（使用助手类）
        /// </summary>
        private static bool CheckRobotReadyForOperation_New()
        {
            return InterLockAccessHelper.WithInterLock(interLock =>
            {
                // 一次性缓存所有需要的对象
                var robotStatus = interLock.SubsystemStatus.Robot.Status;
                var robotIO = interLock.IOInterface.Robot;
                var shuttleConfig = interLock.SubsystemConfigure.Shuttle;
                var robotConfig = interLock.SubsystemConfigure.Robot;

                // 执行业务逻辑
                var paddleSensor1 = robotIO.RDI1_PaddleSensor1Left.Value;
                var paddleSensor2 = robotIO.RDI2_PaddleSensor2Right.Value;
                var slideOutSensorEnable = shuttleConfig.SPS11_SlideOutBackSensorEnable.BoolValue;
                var rotateSpeed = robotConfig.RPS1_RobotRotateSpeed?.Value ?? 0;

                return robotStatus != null &&
                       robotStatus.TAxisIsZeroPosition &&
                       robotStatus.RAxisIsZeroPosition &&
                       !paddleSensor1 &&
                       !paddleSensor2 &&
                       rotateSpeed > 0;
            });
        }
    }
}