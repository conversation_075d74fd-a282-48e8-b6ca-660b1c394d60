# SS200状态监控系统功能实现快速参考

## 🚀 快速理解

### 核心功能一览

| 功能 | 描述 | 实现方式 |
|------|------|----------|
| 手动/自动模式 | 切换数据更新方式 | `TriggerStatusSingalByHand` 属性控制 |
| 实时数据更新 | 自动监控硬件状态 | 500ms定时器 + ModbusCoil |
| 设备类型过滤 | 按设备筛选显示 | `SelectedDeviceType` 属性过滤 |
| 搜索过滤 | 按名称搜索状态 | `SearchText` 属性实时过滤 |
| 手动编辑 | 用户可编辑状态值 | CheckBox/ComboBox/TextBox |
| 批量操作 | 更新/恢复修改 | `UpdateModifiedValues()` / `RestoreAllValues()` |
| 垂直居中 | 表格内容对齐 | DataGridCell + ElementStyle |

## 🔧 关键代码片段

### 1. 模式切换核心逻辑

```csharp
// 手动模式属性
public bool TriggerStatusSingalByHand
{
    get => _triggerStatusSingalByHand;
    set
    {
        if (SetProperty(ref _triggerStatusSingalByHand, value))
        {
            if (value)
                SwitchToManualMode();
            else
                RestoreAutoMode();
        }
    }
}

// 恢复自动模式时立即重新计算
private void RestoreAutoMode()
{
    // 立即重新计算所有子系统状态
    UpdateShuttleSubsystemStatus();
    
    // 强制更新状态属性，绕过手动模式检查
    var originalMode = TriggerStatusSingalByHand;
    TriggerStatusSingalByHand = false;
    UpdateStatusPropertiesCore();
    TriggerStatusSingalByHand = originalMode;
}
```

### 2. 三层更新架构

```csharp
// 第一层：决策层 - 检查是否允许更新
private void UpdateStatusProperties()
{
    if (TriggerStatusSingalByHand) return; // 手动模式拒绝自动更新
    UpdateStatusPropertiesCore();
}

// 第二层：过滤层 - 处理设备类型过滤
private void UpdateStatusPropertiesForDeviceFilter()
{
    UpdateStatusPropertiesCore(); // 设备过滤无论什么模式都执行
}

// 第三层：执行层 - 实际更新逻辑
private void UpdateStatusPropertiesCore()
{
    StatusProperties.Clear();
    AddSubsystemProperties();
}
```

### 3. 智能过滤实现

```csharp
private void AddSubsystemProperties()
{
    var properties = GetAllProperties();
    
    // 设备类型过滤
    if (SelectedDeviceType?.Value != "All")
    {
        properties = properties.Where(p => p.DeviceType == SelectedDeviceType.Value);
    }
    
    // 搜索文本过滤
    if (!string.IsNullOrWhiteSpace(SearchText))
    {
        properties = properties.Where(p => 
            p.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
    }
    
    foreach (var prop in properties)
    {
        StatusProperties.Add(prop);
    }
}
```

### 4. 修改状态检测

```csharp
public bool IsModified
{
    get
    {
        return !IsValueEqualToOriginal(EditValue, OriginalValue);
    }
}

private bool IsValueEqualToOriginal(object editValue, object originalValue)
{
    if (editValue == null && originalValue == null) return true;
    if (editValue == null || originalValue == null) return false;
    
    // 不区分大小写比较，处理 "True" vs "true" 等情况
    return string.Equals(editValue.ToString(), originalValue.ToString(), 
                        StringComparison.OrdinalIgnoreCase);
}
```

### 5. 垂直居中样式

```xml
<!-- DataGridCell 容器级对齐 -->
<Style TargetType="DataGridCell">
    <Setter Property="VerticalContentAlignment" Value="Center" />
    <Setter Property="HorizontalContentAlignment" Value="Left" />
</Style>

<!-- TextBlock 元素级对齐 -->
<DataGridTextColumn.ElementStyle>
    <Style TargetType="TextBlock">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Left" />
    </Style>
</DataGridTextColumn.ElementStyle>
```

## 🎯 常用操作

### 添加新的状态属性

```csharp
// 1. 在对应的子系统状态类中添加属性
public bool NewStatusProperty { get; set; }

// 2. 在 AddSubsystemProperties 中添加映射
StatusProperties.Add(new StatusProperty
{
    Name = "新状态属性",
    DeviceType = "Robot",
    PropertyType = "Boolean",
    Value = robotStatus.NewStatusProperty,
    // ... 其他属性
});
```

### 添加新的设备类型

```csharp
// 在 DeviceTypeOptions 中添加新选项
DeviceTypeOptions.Add(new DeviceTypeOption 
{ 
    DisplayName = "新设备", 
    Value = "NewDevice" 
});
```

### 自定义编辑控件

```xml
<!-- 在 DataGridTemplateColumn 中添加新的编辑模板 -->
<DataGridTemplateColumn Header="自定义编辑">
    <DataGridTemplateColumn.CellTemplate>
        <DataTemplate>
            <CustomControl Value="{Binding EditValue, UpdateSourceTrigger=PropertyChanged}" />
        </DataTemplate>
    </DataGridTemplateColumn.CellTemplate>
</DataGridTemplateColumn>
```

## 🐛 常见问题快速解决

### 问题1: 表格不显示数据
```csharp
// 检查初始化顺序
public RobotStatusPanelViewModel()
{
    StatusProperties = new ObservableCollection<StatusProperty>(); // 必须先初始化
    InitializeDeviceTypes();
    InitializeCommands();
    LoadInitialData(); // 最后加载数据
}
```

### 问题2: 编辑不触发按钮状态更新
```xml
<!-- 确保绑定包含 UpdateSourceTrigger -->
<CheckBox IsChecked="{Binding EditValue, UpdateSourceTrigger=PropertyChanged}" />
<ComboBox SelectedItem="{Binding EditValue, UpdateSourceTrigger=PropertyChanged}" />
```

### 问题3: 搜索在手动模式不工作
```csharp
// 搜索操作直接调用核心方法
partial void OnSearchTextChanged(string value)
{
    UpdateStatusPropertiesCore(); // 不要调用 UpdateStatusProperties()
}
```

### 问题4: 垂直居中不生效
```xml
<!-- 需要同时设置容器和元素对齐 -->
<!-- 1. 容器对齐 -->
<Style TargetType="DataGridCell">
    <Setter Property="VerticalContentAlignment" Value="Center" />
</Style>

<!-- 2. 元素对齐 -->
<DataGridTextColumn.ElementStyle>
    <Style TargetType="TextBlock">
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>
</DataGridTextColumn.ElementStyle>
```

## 📊 性能优化提示

### 1. 大量数据处理
```csharp
// 使用批量操作避免频繁UI更新
StatusProperties.Clear();
var newProperties = GetAllProperties().ToList(); // 先计算完成
foreach (var prop in newProperties)
{
    StatusProperties.Add(prop); // 再批量添加
}
```

### 2. 防抖动更新
```csharp
private DispatcherTimer _debounceTimer;

private void TriggerDelayedUpdate()
{
    _debounceTimer?.Stop();
    _debounceTimer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(300) };
    _debounceTimer.Tick += (s, e) => {
        _debounceTimer.Stop();
        UpdateStatusProperties();
    };
    _debounceTimer.Start();
}
```

### 3. 内存优化
```csharp
// 及时清理事件订阅
public void Dispose()
{
    _updateTimer?.Stop();
    _debounceTimer?.Stop();
    // 取消事件订阅
    ModbusCoil.PropertyChanged -= OnModbusCoilChanged;
}
```

## 🔍 调试技巧

### 1. 启用详细日志
```csharp
private void LogStatusUpdate(string operation, int count)
{
    Debug.WriteLine($"[StatusUpdate] {operation}: {count} properties updated at {DateTime.Now:HH:mm:ss.fff}");
}
```

### 2. 监控性能
```csharp
private void MeasureUpdatePerformance()
{
    var stopwatch = Stopwatch.StartNew();
    UpdateStatusPropertiesCore();
    stopwatch.Stop();
    
    if (stopwatch.ElapsedMilliseconds > 100) // 超过100ms记录
    {
        Debug.WriteLine($"Slow update detected: {stopwatch.ElapsedMilliseconds}ms");
    }
}
```

### 3. 验证数据一致性
```csharp
private void ValidateDataConsistency()
{
    var modifiedCount = StatusProperties.Count(p => p.IsModified);
    var hasModified = StatusProperties.Any(p => p.IsModified);
    
    Debug.Assert(UpdateValuesCommand.CanExecute(null) == hasModified, 
                "Command state inconsistent with data state");
}
```

## 📚 扩展指南

### 添加新功能的步骤

1. **数据模型**: 在StatusProperty或子系统状态类中添加属性
2. **业务逻辑**: 在ViewModel中添加处理方法
3. **UI界面**: 在XAML中添加对应的控件和绑定
4. **测试验证**: 编写单元测试确保功能正常

### 最佳实践

- **单一职责**: 每个方法只做一件事
- **防御编程**: 添加空值检查和异常处理
- **性能考虑**: 避免在UI线程执行耗时操作
- **用户体验**: 提供及时的视觉反馈

这个快速参考指南帮助开发者快速理解和修改系统功能，提高开发效率。
