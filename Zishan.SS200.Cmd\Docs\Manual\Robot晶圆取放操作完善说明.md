# Robot晶圆取放操作完善说明

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| **文档名称** | Robot晶圆取放操作完善说明 |
| **版本** | v1.0 |
| **创建日期** | 2025-01-17 |
| **适用系统** | SS200半导体设备控制系统 |
| **文档类型** | 技术改进说明 |
| **维护部门** | 技术开发部 |

## 📖 目录

- [1. 概述](#1-概述)
- [2. 主要改进内容](#2-主要改进内容)
- [3. 安全检查机制](#3-安全检查机制)
- [4. 操作流程优化](#4-操作流程优化)
- [5. 代码结构改进](#5-代码结构改进)
- [6. 使用示例](#6-使用示例)
- [7. 注意事项](#7-注意事项)

## 1. 概述

本次完善主要针对`RobotWaferOperationsExtensions.cs`文件中的晶圆取放操作进行了全面优化，增强了安全性、可靠性和可维护性。

### 1.1 改进目标
- **增强安全性**：添加完整的InterLock安全检查机制
- **提高可靠性**：增加晶圆状态验证和错误处理
- **优化流程**：根据不同位置类型采用相应的操作模式
- **改善日志**：提供详细的操作日志和状态反馈

## 2. 主要改进内容

### 2.1 GetWaferAsync方法改进

#### 🔧 **改进前**
- 缺少安全检查
- 没有晶圆状态验证
- 操作流程不够清晰

#### ✅ **改进后**
- 添加InterLock安全检查
- 增加源位置晶圆状态验证
- 根据位置类型采用不同操作模式
- 完善错误处理和日志记录

#### 📋 **新增功能**
```csharp
// 1. InterLock安全检查
var interlockResult = await CheckInterLockAsync(cmdService, sourceStationType);

// 2. 源位置晶圆状态验证
var waferCheckResult = await CheckWaferPresenceAsync(cmdService, sourceStationType, slotNumber, true);

// 3. 根据位置类型选择操作模式
switch (sourceStationType)
{
    case EnuLocationStationType.ChamberA:
    case EnuLocationStationType.ChamberB:
        // 工艺腔体模式：顶针升降
        break;
        
    case EnuLocationStationType.Cassette:
    case EnuLocationStationType.CoolingTop:
    case EnuLocationStationType.CoolingBottom:
        // 晶圆盒/冷却腔模式：Z轴升降
        break;
}
```

### 2.2 PutWaferAsync方法改进

#### 🔧 **改进前**
- 缺少目标位置状态检查
- 没有Robot机械臂状态验证
- 操作逻辑不够清晰

#### ✅ **改进后**
- 添加完整的安全检查流程
- 增加目标位置和Robot机械臂状态验证
- 优化操作流程和错误处理

#### 📋 **新增功能**
```csharp
// 1. InterLock安全检查
var interlockResult = await CheckInterLockAsync(cmdService, targetStationType);

// 2. 目标位置状态验证（确认无晶圆）
var waferCheckResult = await CheckWaferPresenceAsync(cmdService, targetStationType, slotNumber, false);

// 3. Robot机械臂状态验证（确认有晶圆）
var armWaferCheckResult = await CheckRobotArmWaferAsync(cmdService, endType, true);

// 4. 根据位置类型执行不同的放片操作
switch (targetStationType)
{
    case EnuLocationStationType.ChamberA:
    case EnuLocationStationType.ChamberB:
        // 工艺腔体模式：顶针升降放片
        break;
        
    case EnuLocationStationType.Cassette:
    case EnuLocationStationType.CoolingTop:
    case EnuLocationStationType.CoolingBottom:
        // 晶圆盒/冷却腔模式：Z轴升降放片
        break;
}
```

## 3. 安全检查机制

### 3.1 CheckInterLockAsync方法

**功能**：执行系统安全状态检查

**检查项目**：
- 系统急停状态
- 安全门锁状态
- 气压状态
- 其他安全条件

```csharp
private static async Task<(bool Success, string Message)> CheckInterLockAsync(
    IS200McuCmdService cmdService,
    EnuLocationStationType stationType)
```

### 3.2 CheckWaferPresenceAsync方法

**功能**：检查指定位置的晶圆存在状态

**支持位置**：
- 工艺腔体（ChamberA、ChamberB）
- 晶圆盒（Cassette）
- 冷却腔（CoolingTop、CoolingBottom）

```csharp
private static async Task<(bool Success, string Message)> CheckWaferPresenceAsync(
    IS200McuCmdService cmdService,
    EnuLocationStationType stationType,
    int slotNumber,
    bool shouldHaveWafer)
```

### 3.3 CheckRobotArmWaferAsync方法

**功能**：检查Robot机械臂上的晶圆状态

**支持端口**：
- Nose端
- Smooth端

```csharp
private static async Task<(bool Success, string Message)> CheckRobotArmWaferAsync(
    IS200McuCmdService cmdService,
    EnuRobotEndType endType,
    bool shouldHaveWafer)
```

## 4. 操作流程优化

### 4.1 取片操作流程

```mermaid
graph TD
    A[开始取片] --> B[InterLock安全检查]
    B --> C[确认源位置有晶圆]
    C --> D[T轴移动到源位置]
    D --> E[Z轴移动到取片高度]
    E --> F[R轴伸入到源位置]
    F --> G{判断位置类型}
    G -->|工艺腔体| H[等待顶针升降取片]
    G -->|晶圆盒/冷却腔| I[Z轴上升托起晶圆]
    H --> J[R轴归零]
    I --> J
    J --> K[T轴旋转到安全角度]
    K --> L[取片完成]
```

### 4.2 放片操作流程

```mermaid
graph TD
    A[开始放片] --> B[InterLock安全检查]
    B --> C[确认目标位置无晶圆]
    C --> D[确认Robot机械臂有晶圆]
    D --> E[T轴移动到目标位置]
    E --> F[Z轴移动到放片高度]
    F --> G[R轴伸入到目标位置]
    G --> H{判断位置类型}
    H -->|工艺腔体| I[等待顶针升降放片]
    H -->|晶圆盒/冷却腔| J[Z轴下降放置晶圆]
    I --> K[R轴归零]
    J --> K
    K --> L[T轴旋转到安全角度]
    L --> M[放片完成]
```

## 5. 代码结构改进

### 5.1 新增区域

```csharp
#region 安全检查方法
// 包含所有安全检查相关的私有方法
// - CheckInterLockAsync
// - CheckWaferPresenceAsync  
// - CheckRobotArmWaferAsync
#endregion
```

### 5.2 方法签名改进

**GetWaferAsync方法**：
```csharp
/// <summary>
/// 从指定位置获取晶圆
/// 支持工艺腔体（顶针升降模式）和晶圆盒/冷却腔（Z轴升降模式）
/// </summary>
public static async Task<(bool Success, string Message)> GetWaferAsync(
    this IS200McuCmdService cmdService,
    EnuRobotEndType endType,
    EnuLocationStationType sourceStationType,
    int slotNumber = 0)
```

**PutWaferAsync方法**：
```csharp
/// <summary>
/// 将晶圆放置到指定位置
/// 支持工艺腔体（顶针升降模式）和晶圆盒/冷却腔（Z轴升降模式）
/// </summary>
public static async Task<(bool Success, string Message)> PutWaferAsync(
    this IS200McuCmdService cmdService,
    EnuRobotEndType endType,
    EnuLocationStationType targetStationType,
    int slotNumber = 0)
```

## 6. 使用示例

### 6.1 从晶圆盒取片

```csharp
// 从晶圆盒Slot 5取片（使用Nose端）
var result = await cmdService.GetWaferAsync(
    EnuRobotEndType.Nose,
    EnuLocationStationType.Cassette,
    5);

if (result.Success)
{
    Console.WriteLine($"取片成功: {result.Message}");
}
else
{
    Console.WriteLine($"取片失败: {result.Message}");
}
```

### 6.2 放片到工艺腔体

```csharp
// 放片到ChamberA（使用Smooth端）
var result = await cmdService.PutWaferAsync(
    EnuRobotEndType.Smooth,
    EnuLocationStationType.ChamberA);

if (result.Success)
{
    Console.WriteLine($"放片成功: {result.Message}");
}
else
{
    Console.WriteLine($"放片失败: {result.Message}");
}
```

### 6.3 冷却腔操作

```csharp
// 从CoolingTop取片
var getResult = await cmdService.GetWaferAsync(
    EnuRobotEndType.Nose,
    EnuLocationStationType.CoolingTop);

// 放片到CoolingBottom
var putResult = await cmdService.PutWaferAsync(
    EnuRobotEndType.Nose,
    EnuLocationStationType.CoolingBottom);
```

## 7. 注意事项

### 7.1 安全要求

⚠️ **强制性检查**
- 所有操作前必须通过InterLock安全检查
- 必须验证源/目标位置的晶圆状态
- 必须确认Robot机械臂状态

⚠️ **操作限制**
- 禁止在安全检查失败时强制执行操作
- 禁止跳过晶圆状态验证
- 禁止在系统报警状态下进行操作

### 7.2 错误处理

- 所有异常都会被捕获并返回详细错误信息
- 操作失败时会记录详细的错误日志
- 支持操作中断和状态恢复

### 7.3 扩展性

- 安全检查方法预留了具体实现接口
- 支持添加新的位置类型和操作模式
- 日志系统支持不同级别的信息记录

---

**注意**：本次改进为晶圆取放操作提供了完整的安全保障和错误处理机制，确保系统的可靠性和安全性。在实际使用中，需要根据具体的硬件配置完善传感器读取和InterLock检查的具体实现。
