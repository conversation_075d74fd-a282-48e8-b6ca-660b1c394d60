using System;
using System.Threading;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// PinSearch取消功能测试类
    /// 用于验证CancellationToken功能是否正常工作
    /// </summary>
    public class PinSearchCancellationTest
    {
        private readonly IS200McuCmdService _mcuCmdService;

        public PinSearchCancellationTest(IS200McuCmdService mcuCmdService)
        {
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
        }

        /// <summary>
        /// 测试1：验证取消令牌是否正确传递到底层
        /// </summary>
        public async Task<bool> TestCancellationTokenPropagation()
        {
            UILogService.AddLogAndIncreaseIndent("=== 测试取消令牌传递 ===");

            try
            {
                var cts = new CancellationTokenSource();
                
                // 立即取消，测试是否能快速响应
                cts.Cancel();

                var startTime = DateTime.Now;
                
                try
                {
                    var result = await _mcuCmdService.PinSearchAsync(
                        EnuRobotEndType.Smooth, 
                        false, 
                        cts.Token);
                    
                    UILogService.AddErrorLog("测试失败：应该抛出OperationCanceledException");
                    return false;
                }
                catch (OperationCanceledException)
                {
                    var elapsed = DateTime.Now - startTime;
                    UILogService.AddSuccessLog($"测试成功：正确抛出OperationCanceledException，耗时: {elapsed.TotalMilliseconds}ms");
                    
                    // 验证响应时间是否合理（应该很快）
                    if (elapsed.TotalSeconds < 1)
                    {
                        UILogService.AddSuccessLog("响应时间合理，取消令牌工作正常");
                        return true;
                    }
                    else
                    {
                        UILogService.AddWarningLog($"响应时间较长: {elapsed.TotalSeconds}秒");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"测试异常: {ex.Message}");
                return false;
            }
            finally
            {
                UILogService.DecreaseIndentAndAddLog("=== 取消令牌传递测试结束 ===");
            }
        }

        /// <summary>
        /// 测试2：验证延迟取消功能
        /// </summary>
        public async Task<bool> TestDelayedCancellation()
        {
            UILogService.AddLogAndIncreaseIndent("=== 测试延迟取消功能 ===");

            try
            {
                var cts = new CancellationTokenSource();
                
                // 设置2秒后取消
                cts.CancelAfter(TimeSpan.FromSeconds(2));

                var startTime = DateTime.Now;
                
                try
                {
                    var result = await _mcuCmdService.PinSearchAsync(
                        EnuRobotEndType.Nose, 
                        false, 
                        cts.Token);
                    
                    var elapsed = DateTime.Now - startTime;
                    UILogService.AddLog($"PinSearch完成，耗时: {elapsed.TotalSeconds}秒");
                    
                    if (result.Success)
                    {
                        UILogService.AddSuccessLog("PinSearch在取消前完成");
                        return true;
                    }
                    else
                    {
                        UILogService.AddWarningLog($"PinSearch失败: {result.Message}");
                        return false;
                    }
                }
                catch (OperationCanceledException)
                {
                    var elapsed = DateTime.Now - startTime;
                    UILogService.AddSuccessLog($"测试成功：操作在 {elapsed.TotalSeconds:F1} 秒后被取消");
                    
                    // 验证取消时间是否接近预期（2秒左右）
                    if (elapsed.TotalSeconds >= 1.8 && elapsed.TotalSeconds <= 2.5)
                    {
                        UILogService.AddSuccessLog("取消时间符合预期");
                        return true;
                    }
                    else
                    {
                        UILogService.AddWarningLog($"取消时间不符合预期，期望2秒，实际: {elapsed.TotalSeconds:F1}秒");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"测试异常: {ex.Message}");
                return false;
            }
            finally
            {
                UILogService.DecreaseIndentAndAddLog("=== 延迟取消测试结束 ===");
            }
        }

        /// <summary>
        /// 测试3：验证正常执行不受影响
        /// </summary>
        public async Task<bool> TestNormalExecutionWithToken()
        {
            UILogService.AddLogAndIncreaseIndent("=== 测试正常执行不受影响 ===");

            try
            {
                var cts = new CancellationTokenSource();
                
                // 设置较长的超时时间，确保正常执行不被打断
                cts.CancelAfter(TimeSpan.FromMinutes(1));

                var startTime = DateTime.Now;
                
                var result = await _mcuCmdService.PinSearchAsync(
                    EnuRobotEndType.Smooth, 
                    false, 
                    cts.Token);
                
                var elapsed = DateTime.Now - startTime;
                UILogService.AddLog($"PinSearch执行完成，耗时: {elapsed.TotalSeconds:F1}秒");
                
                if (result.Success)
                {
                    UILogService.AddSuccessLog($"测试成功：PinSearch正常执行完成");
                    UILogService.AddLog($"结果: {result.Message}");
                    UILogService.AddLog($"P1值: {result.PinSearchP1Value}, P2值: {result.PinSearchP2Value}");
                    return true;
                }
                else
                {
                    UILogService.AddWarningLog($"PinSearch执行失败: {result.Message}");
                    // 即使失败，只要不是因为取消，也算测试通过
                    return !result.Message.Contains("取消") && !result.Message.Contains("Cancelled");
                }
            }
            catch (OperationCanceledException)
            {
                UILogService.AddErrorLog("测试失败：不应该被取消");
                return false;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"测试异常: {ex.Message}");
                return false;
            }
            finally
            {
                UILogService.DecreaseIndentAndAddLog("=== 正常执行测试结束 ===");
            }
        }

        /// <summary>
        /// 测试4：验证向后兼容性
        /// </summary>
        public async Task<bool> TestBackwardCompatibility()
        {
            UILogService.AddLogAndIncreaseIndent("=== 测试向后兼容性 ===");

            try
            {
                // 使用原有的方法签名（不带CancellationToken）
                var result = await _mcuCmdService.PinSearchAsync(EnuRobotEndType.Smooth, false);
                
                UILogService.AddSuccessLog("向后兼容性测试通过：原有方法调用正常");
                
                if (result.Success)
                {
                    UILogService.AddLog($"执行结果: {result.Message}");
                }
                else
                {
                    UILogService.AddLog($"执行失败: {result.Message}");
                }
                
                return true;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"向后兼容性测试失败: {ex.Message}");
                return false;
            }
            finally
            {
                UILogService.DecreaseIndentAndAddLog("=== 向后兼容性测试结束 ===");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task<bool> RunAllTests()
        {
            UILogService.AddLogAndIncreaseIndent("=== PinSearch取消功能完整测试 ===");

            bool allTestsPassed = true;

            try
            {
                // 测试1：取消令牌传递
                UILogService.AddLog("开始测试1：取消令牌传递...");
                bool test1 = await TestCancellationTokenPropagation();
                allTestsPassed &= test1;
                
                await Task.Delay(1000); // 测试间隔

                // 测试2：延迟取消
                UILogService.AddLog("开始测试2：延迟取消...");
                bool test2 = await TestDelayedCancellation();
                allTestsPassed &= test2;
                
                await Task.Delay(1000); // 测试间隔

                // 测试3：正常执行
                UILogService.AddLog("开始测试3：正常执行...");
                bool test3 = await TestNormalExecutionWithToken();
                allTestsPassed &= test3;
                
                await Task.Delay(1000); // 测试间隔

                // 测试4：向后兼容性
                UILogService.AddLog("开始测试4：向后兼容性...");
                bool test4 = await TestBackwardCompatibility();
                allTestsPassed &= test4;

                // 输出测试结果
                if (allTestsPassed)
                {
                    UILogService.AddSuccessLog("🎉 所有测试通过！PinSearch取消功能工作正常");
                }
                else
                {
                    UILogService.AddErrorLog("❌ 部分测试失败，请检查实现");
                }

                return allTestsPassed;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"测试执行异常: {ex.Message}");
                return false;
            }
            finally
            {
                UILogService.DecreaseIndentAndAddLog("=== PinSearch取消功能完整测试结束 ===");
            }
        }
    }

    /// <summary>
    /// 测试运行器
    /// </summary>
    public static class PinSearchCancellationTestRunner
    {
        /// <summary>
        /// 运行测试的静态方法
        /// </summary>
        public static async Task<bool> RunTests(IS200McuCmdService mcuCmdService)
        {
            if (mcuCmdService == null)
            {
                UILogService.AddErrorLog("测试失败：mcuCmdService为null");
                return false;
            }

            var tester = new PinSearchCancellationTest(mcuCmdService);
            return await tester.RunAllTests();
        }
    }
}
