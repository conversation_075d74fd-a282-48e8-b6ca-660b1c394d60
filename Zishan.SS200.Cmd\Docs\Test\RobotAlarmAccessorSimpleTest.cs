using System;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Robot;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// Robot报警访问器简单测试类
    /// 用于验证Robot报警代码访问器的基本功能
    /// </summary>
    public class RobotAlarmAccessorSimpleTest
    {
        /// <summary>
        /// 简单测试Robot报警代码访问器
        /// </summary>
        public static void SimpleTest()
        {
            try
            {
                Console.WriteLine("=== Robot报警代码访问器简单测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var robotAlarm = interLock.AlarmCode.Robot;

                // 测试前10个报警代码
                Console.WriteLine("\n测试前10个Robot报警代码:");
                TestAccessor("RA1", robotAlarm.RA1_SystemBusyReject);
                TestAccessor("RA2", robotAlarm.RA2_SystemAlarmReject);
                TestAccessor("RA3", robotAlarm.RA3_RAxisNotHomeError);
                TestAccessor("RA4", robotAlarm.RA4_TAxisPositionError);
                TestAccessor("RA5", robotAlarm.RA5_RotationTimeout);
                TestAccessor("RA6", robotAlarm.RA6_ExtensionTimeout);
                TestAccessor("RA7", robotAlarm.RA7_LiftTimeout);
                TestAccessor("RA8", robotAlarm.RA8_CHASlitDoorNotOpen);
                TestAccessor("RA9", robotAlarm.RA9_CHBSlitDoorNotOpen);
                TestAccessor("RA10", robotAlarm.RA10_ZAxisPositionError);

                // 测试中间的一些报警代码
                Console.WriteLine("\n测试中间的一些Robot报警代码:");
                TestAccessor("RA30", robotAlarm.RA30_CHARunBusy);
                TestAccessor("RA31", robotAlarm.RA31_CHBRunBusy);
                TestAccessor("RA40", robotAlarm.RA40_SmoothBothPaddleWaferLost);
                TestAccessor("RA50", robotAlarm.RA50_SmoothP1WaferStatusInconsistent);

                // 测试最后几个报警代码
                Console.WriteLine("\n测试最后几个Robot报警代码:");
                TestAccessor("RA63", robotAlarm.RA63_NoWaferInCHAGetReject);
                TestAccessor("RA64", robotAlarm.RA64_NoWaferInCHBGetReject);
                TestAccessor("RA65", robotAlarm.RA65_NoWaferInCTGetReject);
                TestAccessor("RA66", robotAlarm.RA66_NoWaferInCBGetReject);
                TestAccessor("RA67", robotAlarm.RA67_RobotMotionError);

                Console.WriteLine("\n=== Robot报警代码访问器简单测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试单个访问器
        /// </summary>
        /// <param name="code">报警代码</param>
        /// <param name="accessor">访问器</param>
        private static void TestAccessor(string code, AlarmPropertyAccessor accessor)
        {
            try
            {
                if (accessor != null)
                {
                    string content = accessor.Content ?? "未定义";
                    string chsContent = accessor.ChsContent ?? "未定义";
                    Console.WriteLine($"  {code}: {content}");
                    Console.WriteLine($"       中文: {chsContent}");
                }
                else
                {
                    Console.WriteLine($"  {code}: 访问器为空");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  {code}: 访问器测试失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 统计测试 - 验证所有67个访问器都已定义
        /// </summary>
        public static void CountTest()
        {
            try
            {
                Console.WriteLine("=== Robot报警代码访问器统计测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var robotAlarm = interLock.AlarmCode.Robot;
                var robotAlarmType = robotAlarm.GetType();
                
                // 获取所有以RA开头的属性
                var properties = robotAlarmType.GetProperties();
                int raPropertyCount = 0;
                
                foreach (var property in properties)
                {
                    if (property.Name.StartsWith("RA") && property.Name.Contains("_"))
                    {
                        raPropertyCount++;
                        Console.WriteLine($"  发现访问器: {property.Name}");
                    }
                }
                
                Console.WriteLine($"\n总计发现 {raPropertyCount} 个Robot报警代码访问器");
                
                if (raPropertyCount == 67)
                {
                    Console.WriteLine("✅ 所有67个Robot报警代码访问器都已正确定义！");
                }
                else
                {
                    Console.WriteLine($"❌ 预期67个访问器，实际发现{raPropertyCount}个");
                }
                
                Console.WriteLine("=== Robot报警代码访问器统计测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"统计测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            SimpleTest();
            Console.WriteLine();
            CountTest();
        }
    }
}
