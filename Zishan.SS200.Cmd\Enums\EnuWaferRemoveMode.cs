﻿using System.ComponentModel;

using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 体Wafer移除方式：从开头移除、从尾部移除、指定移除
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuWaferRemoveMode
    {
        /// <summary>
        /// 从开头移除
        /// </summary>
        [Description("开头移除")]
        Front = 0,

        /// <summary>
        /// 从尾部移除
        /// </summary>
        [Description("尾部移除")]
        Rear = 1,

        /// <summary>
        /// 指定移除
        /// </summary>
        [Description("指定移除")]
        specified = 2
    }
}