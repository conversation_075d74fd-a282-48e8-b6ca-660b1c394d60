using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace $NAMESPACE$
{
    /// <summary>
    /// $CLASS_NAME$ 模型类
    /// </summary>
    public partial class $CLASS_NAME$ : ObservableValidator
    {
        /// <summary>
        /// ID属性
        /// </summary>
        [ObservableProperty]
private int _id;

/// <summary>
/// 名称属性 - 带验证
/// </summary>
[ObservableProperty]
[NotifyDataErrorInfo]
[Required(ErrorMessage = "名称不能为空")]
[StringLength(50, MinimumLength = 2, ErrorMessage = "名称长度必须在2-50个字符之间")]
private string _name;

/// <summary>
/// 描述属性
/// </summary>
[ObservableProperty]
private string _description;

/// <summary>
/// 创建时间属性
/// </summary>
[ObservableProperty]
private DateTime _createdAt = DateTime.Now;

/// <summary>
/// 是否启用属性
/// </summary>
[ObservableProperty]
private bool _isEnabled = true;

/// <summary>
/// 数值属性 - 带验证
/// </summary>
[ObservableProperty]
[NotifyDataErrorInfo]
[Range(0, 100, ErrorMessage = "数值必须在0-100之间")]
private double _value;

/// <summary>
/// 类型枚举属性
/// </summary>
[ObservableProperty]
private ItemType _itemType = ItemType.Normal;

/// <summary>
/// 标签集合属性
/// </summary>
[ObservableProperty]
private List<string> _tags = new List<string>();

/// <summary>
/// 默认构造函数
/// </summary>
public $CLASS_NAME$()
        {
        }

/// <summary>
/// 带参数的构造函数
/// </summary>
/// <param name="name">名称</param>
/// <param name="description">描述</param>
public $CLASS_NAME$(string name, string description)
        {
            _name = name;
_description = description;
        }

        /// <summary>
        /// 验证所有属性
        /// </summary>
        /// <returns>验证是否通过</returns>
        public bool ValidateAll()
{
    ValidateAllProperties();
    return !HasErrors;
}

/// <summary>
/// 重置为默认值
/// </summary>
public void Reset()
{
    Name = string.Empty;
    Description = string.Empty;
    IsEnabled = true;
    Value = 0;
    ItemType = ItemType.Normal;
    Tags.Clear();
}

/// <summary>
/// 创建副本
/// </summary>
/// <returns>对象副本</returns>
public $CLASS_NAME$ Clone()
        {
            return new $CLASS_NAME$
            {
                Id = Id,
                Name = Name,
                Description = Description,
                CreatedAt = CreatedAt,
                IsEnabled = IsEnabled,
                Value = Value,
                ItemType = ItemType,
                Tags = new List<string>(Tags)
            };
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>对象字符串表示</returns>
        public override string ToString()
{
    return $"{Id}: {Name} ({ItemType})";
}
    }

    /// <summary>
    /// 项目类型枚举
    /// </summary>
    public enum ItemType
{
    /// <summary>
    /// 普通类型
    /// </summary>
    Normal = 0,

    /// <summary>
    /// 重要类型
    /// </summary>
    Important = 1,

    /// <summary>
    /// 紧急类型
    /// </summary>
    Urgent = 2,

    /// <summary>
    /// 低优先级类型
    /// </summary>
    LowPriority = 3
}
} 