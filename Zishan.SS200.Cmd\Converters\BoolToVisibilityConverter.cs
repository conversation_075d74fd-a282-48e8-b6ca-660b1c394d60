using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 将布尔值转换为可见性的转换器
    /// </summary>
    public class BoolToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 是否反转结果
        /// </summary>
        public bool Inverse { get; set; } = false;

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool bValue = false;
            
            if (value is bool boolValue)
            {
                bValue = boolValue;
            }
            else if (value is int intValue)
            {
                bValue = intValue != 0;
            }
            else if (value is string strValue)
            {
                bValue = !string.IsNullOrEmpty(strValue);
            }
            
            // 如果有参数且是字符串"inverse"或"invert"，反转结果
            if (parameter != null && (parameter.ToString().ToLower() == "inverse" || parameter.ToString().ToLower() == "invert"))
            {
                bValue = !bValue;
            }
            
            // 根据Inverse属性决定是否反转结果
            if (Inverse)
            {
                bValue = !bValue;
            }
            
            return bValue ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                bool result = visibility == Visibility.Visible;
                
                // 如果有参数且是字符串"inverse"或"invert"，反转结果
                if (parameter != null && (parameter.ToString().ToLower() == "inverse" || parameter.ToString().ToLower() == "invert"))
                {
                    result = !result;
                }
                
                // 根据Inverse属性决定是否反转结果
                if (Inverse)
                {
                    result = !result;
                }
                
                return result;
            }
            
            return DependencyProperty.UnsetValue;
        }
    }
} 