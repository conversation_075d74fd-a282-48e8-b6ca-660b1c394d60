- [ ] SelectedDevice 也改为枚举类型，没有使用
- [ ] MCU运行参数，动态值通过参数传入，固定的通话Json配置档读取，包含前面的配置参数
- [ ] Modbus命令发送支持多任务区并行发送
- [ ] OperateResult命令返回统一封装
- [x] UI停止按钮：急停、当前小指令做完停止，后面恢复，也就是暂停继续功能怎么实现: ✅ 2025-06-06
- [ ] 二级指令，比如：PutWafer 到Cassette伸进去一半，wafer slide out sensor 3 BL/ 4 BR感应到，或者TZ轴,出现中断停止，出现abort、retry，需要相应轴RTZ回原点，再重新跑该二级指令 ⏫ 
- [ ] 从Cassette取放片的时候，要考虑2个滑片传感器被感应到，取放片那一刻不考虑，其它时刻要考虑
- [ ] S200McuCmdService需要取消Instance，全部使用Prism的Service，避免多实例干扰问题
- [ ] Robot命令轴分类整理：1、轴分类；2、Smooth、Nose端分类；3、最后选择具体的腔体，比如：ChamberA、ChamberB、Cooling
- [ ] 通过[RelayCommand]特性实现UI命令绑定，需要加前缀ON某某，代表这个方法是命令，UI端通过某某Command绑定
- [ ] 类使用Region分类整理：字段、属性、构造函数、事件命令、方法，避免乱序
- [ ] UI绑定的StatusProperties可观察属性列表：
1、手动触发启用后，状态表的数据停止自动更新（比如：通过RTZ轴值和DI值更新将停止）
2、手动触发启用后，可以手动点击Bool值CheckBox，枚举值Combox选择，修改表格内容，然后表格右边添加按钮操作：更新值，恢复值
- [ ] Robot一级、二级、三级指令使用枚举类型与Robot状态位置枚举类型不一致，无法通过枚举类型判断，需要修改保持一致
- [ ] Robot状态位置枚举类型与Robot指令枚举类型保持一致，避免混淆
- [ ] Robot状态RTZ轴位置也要放到统一入口访问SS200参数设置、IO设置、报警信息、状态表
- [ ] 参数设置Shuttle，Process chamber也要放到统一入口访问SS200参数设置、IO设置、报警信息、状态表
- [ ] RT旋转速度为慢RPS4，需要根据配置参数传递到Robot参数设置json文件
- [ ] 设备状态也需要同步到统一入口访问
- [ ] UI状态表数据不更新：数据通过统一入口状态表数据更新，UI界面数据却没有更新
- [ ] 统一入口访问添加单元测试用例验证获取到的参数设置、IO设置、报警信息、状态表无误，方便后续修改批量验证，测试热加载

