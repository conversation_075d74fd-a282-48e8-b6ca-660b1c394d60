item	alarm code	content	remark
1	SLR1	all process chamber offline, pls comfirm	
	SLR2	main system is not automatic mode,can not run sequence	
	SLR3	waiting for wafer exchange time out	
	SLR4	CHA offline, move wafer to CHA failure	
	SLR5	CHB offline, move wafer to CHB failure	
	SLR6	CHA and CHB all offline, move wafer to chamber failure	
	SLR7	CHA trigger status is alarm, move wafer to CHA failure	
	SLR8	CHB trigger status is alarm, move wafer to CHB failure	
	SLR9	CHA/B trigger status is alarm, move wafer to chamber failure	
	SLR10	cooling chamber trigger status is alarm, move wafer to cooling chamber failure	
	SLR11	main system alarm, sequence can not continue.	
	SLR12		



item	status code	content	remark	
PL1	slot xx sequence status	"PL1=0 standby
PL1=1 doing
PL1=2 done
PL1=3 return to cass, but not finished"	
	PL2	slot xx process chamber recipe status	"PL2=0 standby
PL2=1 doing
PL2=done"	
	PL3	shuttle 1 lot sequence status	"PL3=0 standby
PL3=1 doing
PL3=2 done 
PL3=3 none"	
	PL4	shuttle 2 lot sequence status	"PL4=0 standby
PL4=1 doing
PL4=2 done
PL4=none"	
	PL5	active shuttle	PL5=0 shuttle 1	shuttle status:SSD2
			PL5=1 shuttle 2	shuttle status:SSD1	
	PL5	CHA recipe remain time	recipe中(A1+A2+---A xx+B1+B2+B xx)-run time, if PL2=0, PL3=0	
	PL6	CHB recipe remain time	recipe中(A1+A2+---A xx+B1+B3+B xx)-run time, if PL2=0, PL4=0	
	PL7	CT recipe remain time		
	PL8	CB recipe remain time		
	PL9	sequence remain time		
	PL10	Slot XX all recipe status	PL10=0 when all PL2==2, otherwise PL10=1	
	PL11	robot action done trigger for SAC2	PL11=0, SAC3 wafer swap done	
	PL12			

item	configure	code	value	range/selectable	unit
1	exchange wafer max waiting time	SLC1	1800	1~10000	s




Item	configure	parameter	value	range	remark
1	Top wafer No.	SQ1		1-26	
2	Bottom wafer No.	SQ2		1-26	
3	Total exsit wafer for sequence	SQ3		1-26	SQ3=SQ1-SQ2+1
4	sequence direction	SQ4		0-1	"SQ4=0, Top to bottom
SQ4=1, Bottom to top"
5	sequence station	SQ5		1-6	1=CHA, 2=CHB, 3=CHA/CHB, 4=CT, 5=CB, 6=CASS
	sequence station num	SQ6		1-10	
	recipe num	SQ7			


Item	configure	interlock	value	range
1	add sequence unfinished wafer to sequence again	SQL1	"SQL1=1, add
SQL1=0, no"	0-1
