# 线圈查找解决方案 - 快速使用指南

## 🎯 解决的问题

在`ShuttleSlotStatusManager.cs`和`ChamberSubsystemStatus.cs`中，您现在可以通过设备类型和DI/DO枚举类型轻松获取线圈实体对象，用于状态计算。

## 🚀 快速开始

### 1. 基本用法

```csharp
// 获取服务实例
var mcuService = S200McuCmdService.Instance;

// 方法1：直接使用服务方法
var slitDoorOpenCoil = mcuService.GetInputCoilByEnum(
    EnuMcuDeviceType.ChamberA, 
    EnuChamberDICodes.PDI12_SlitDoorOpenSensor);

bool isOpen = slitDoorOpenCoil?.Coilvalue ?? false;

// 方法2：使用辅助类（推荐）
var coilHelper = new CoilStatusHelper(mcuService);
bool isOpen2 = coilHelper.GetCoilValue(
    EnuMcuDeviceType.ChamberA, 
    EnuChamberDICodes.PDI12_SlitDoorOpenSensor);
```

### 2. 在状态管理器中的应用

#### Chamber状态计算

```csharp
public class ChamberStatusManager
{
    private readonly CoilStatusHelper _coilHelper;
    
    public ChamberStatusManager()
    {
        _coilHelper = new CoilStatusHelper(S200McuCmdService.Instance);
    }
    
    public void UpdateChamberStatus(EnuMcuDeviceType deviceType)
    {
        // 使用内置计算方法
        var slitDoorStatus = _coilHelper.CalculateSlitDoorStatus(deviceType);
        var liftPinStatus = _coilHelper.CalculateLiftPinStatus(deviceType);
        
        // 手动计算阀门状态
        var isoOpen = _coilHelper.GetCoilValue(deviceType, EnuChamberDICodes.PDI1_CvOpenSensor);
        var isoClose = _coilHelper.GetCoilValue(deviceType, EnuChamberDICodes.PDI2_CvCloseSensor);
        
        // 应用到状态对象
        // chamberStatus.SlitDoorStatus = slitDoorStatus;
        // chamberStatus.LiftPinStatus = liftPinStatus;
    }
}
```

#### Shuttle状态计算

```csharp
public class ShuttleStatusManager
{
    private readonly CoilStatusHelper _coilHelper;

    public ShuttleStatusManager()
    {
        _coilHelper = new CoilStatusHelper(S200McuCmdService.Instance);
    }

    public void UpdateShuttleStatus()
    {
        // 检查晶圆盒门位置
        var doorUp = _coilHelper.IsShuttleCassetteDoorAtPosition(true);
        var doorDown = _coilHelper.IsShuttleCassetteDoorAtPosition(false);

        // 检查晶圆盒巢位置
        var nestExtended = _coilHelper.IsShuttleCassetteNestAtPosition(true);
        var nestRetracted = _coilHelper.IsShuttleCassetteNestAtPosition(false);

        // 检查多个传感器组合
        var rotateSensorsActive = _coilHelper.AreAllCoilsActive(
            EnuMcuDeviceType.Shuttle,
            EnuShuttleDICodes.SDI13_ShuttleRotateSensor1,
            EnuShuttleDICodes.SDI14_ShuttleRotateSensor2);
    }
}
```

#### Robot状态计算

```csharp
public class RobotStatusManager
{
    private readonly CoilStatusHelper _coilHelper;
    
    public RobotStatusManager()
    {
        _coilHelper = new CoilStatusHelper(S200McuCmdService.Instance);
    }
    
    public void UpdateRobotStatus()
    {
        // 检查Paddle传感器
        var paddle1HasWafer = _coilHelper.IsWaferDetectedOnPaddle(1);
        var paddle2HasWafer = _coilHelper.IsWaferDetectedOnPaddle(2);
        
        // 检查Pin搜索传感器
        var pin1Detected = _coilHelper.IsPinDetected(1);
        var pin2Detected = _coilHelper.IsPinDetected(2);
    }
}
```

## 📋 主要方法

### S200McuCmdService扩展方法

| 方法                       | 说明         | 示例                                                                                        |
| -------------------------- | ------------ | ------------------------------------------------------------------------------------------- |
| `GetInputCoilByEnum<T>()`  | 获取DI线圈   | `GetInputCoilByEnum(EnuMcuDeviceType.ChamberA, EnuChamberDICodes.PDI12_SlitDoorOpenSensor)` |
| `GetOutputCoilByEnum<T>()` | 获取DO线圈   | `GetOutputCoilByEnum(EnuMcuDeviceType.ChamberA, EnuChamberDOCodes.PDO10_SlitDoorOpen)`      |
| `GetCoilByEnum<T>()`       | 自动判断类型 | `GetCoilByEnum(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1_CassetteDoorUpSensor)`      |

### CoilStatusHelper便捷方法

| 方法                        | 说明                 | 示例                                                                                  |
| --------------------------- | -------------------- | ------------------------------------------------------------------------------------- |
| `GetCoilValue<T>()`         | 获取线圈布尔值       | `GetCoilValue(EnuMcuDeviceType.ChamberA, EnuChamberDICodes.PDI12_SlitDoorOpenSensor)` |
| `AreAllCoilsActive<T>()`    | 检查多个线圈都为true | `AreAllCoilsActive(deviceType, enum1, enum2, enum3)`                                  |
| `IsAnyCoilActive<T>()`      | 检查任意线圈为true   | `IsAnyCoilActive(deviceType, enum1, enum2, enum3)`                                    |
| `CalculateSlitDoorStatus()` | 计算Slit Door状态    | `CalculateSlitDoorStatus(EnuMcuDeviceType.ChamberA)`                                  |
| `CalculateLiftPinStatus()`  | 计算Lift Pin状态     | `CalculateLiftPinStatus(EnuMcuDeviceType.ChamberA)`                                   |

## 🔧 支持的设备和枚举

### 设备类型
- `EnuMcuDeviceType.Shuttle`
- `EnuMcuDeviceType.Robot`
- `EnuMcuDeviceType.ChamberA`
- `EnuMcuDeviceType.ChamberB`

### 枚举类型
- **Shuttle**: `EnuShuttleDICodes`, `EnuShuttleDOCodes`
- **Chamber**: `EnuChamberDICodes`, `EnuChamberDOCodes`
- **Robot**: `EnuRobotDICodes`

## ⚡ 性能提示

1. **重用实例**: 在类中创建一个`CoilStatusHelper`实例并重用
2. **批量查询**: 使用`AreAllCoilsActive`或`IsAnyCoilActive`进行批量查询
3. **缓存结果**: 对于不经常变化的状态，考虑缓存计算结果

## 🐛 调试技巧

```csharp
// 查看所有线圈状态
var allStatus = coilHelper.GetAllCoilStatus(EnuMcuDeviceType.ChamberA);
foreach (var kvp in allStatus)
{
    Console.WriteLine($"{kvp.Key}: {kvp.Value}");
}

// 检查特定线圈是否存在
var coil = mcuService.GetInputCoilByEnum(deviceType, enumValue);
if (coil == null)
{
    Console.WriteLine("线圈未找到，请检查枚举名称和设备类型");
}
```

## 📚 完整文档

- **详细文档**: `Docs/CoilLookupSolution.md`
- **使用示例**: `Examples/CoilStatusUsageExample.cs`
- **测试代码**: `Tests/CoilLookupTests.cs`

## 🎉 开始使用

1. 在您的状态管理器构造函数中创建`CoilStatusHelper`实例
2. 使用`GetCoilValue()`方法替换硬编码的线圈查找
3. 利用内置的状态计算方法简化复杂逻辑
4. 运行测试验证功能正常工作

```csharp
// 运行测试
var tests = new CoilLookupTests();
tests.RunAllTests();
```

现在您可以在`ShuttleSlotStatusManager.cs`和`ChamberSubsystemStatus.cs`中享受类型安全、易于维护的线圈状态查询了！
