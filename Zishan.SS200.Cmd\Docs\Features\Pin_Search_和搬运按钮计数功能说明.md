# Pin Search测试和搬运按钮计数功能说明

## 功能概述

为Pin Search测试按钮和搬运按钮添加了计数功能，用于在无限循环中显示已执行的次数。这两个按钮不能同时运行，共用一个计数器。

## 功能特点

1. **共用计数器**：Pin Search测试按钮和搬运按钮共用同一个已执行次数计数器
2. **互斥执行**：两个按钮不能同时运行，通过`IsExecutingCommand`属性控制
3. **实时更新**：在每次循环完成后实时更新已执行次数
4. **自动重置**：开始新的测试时自动重置计数器为0

## 实现细节

### 1. ViewModel属性

```csharp
/// <summary>
/// 已执行次数：Pin Search测试和搬运按钮共用的累计计数器
/// </summary>
[ObservableProperty]
private int executedCount = 0;

/// <summary>
/// 是否已经开始执行测试：用于区分"未开始"和"已执行: 0次"状态
/// </summary>
[ObservableProperty]
private bool hasStartedExecution = false;
```

### 2. UI显示

在界面上添加了一个新的显示区域，位于剩余循环次数显示的旁边：

```xml
<!--  已执行次数显示  -->
<Border
    MinWidth="60"
    Margin="5,0"
    Padding="8,3"
    Background="LightGreen"
    BorderBrush="Gray"
    BorderThickness="1"
    CornerRadius="3"
    ToolTip="Pin Search测试和搬运按钮的累计执行次数">
    <TextBlock
        VerticalAlignment="Center"
        d:Text="已执行: 0次"
        FontWeight="Bold">
        <TextBlock.Text>
            <MultiBinding Converter="{StaticResource ExecutedCountDisplayConverter}">
                <Binding Path="ExecutedCount" />
                <Binding Path="HasStartedExecution" />
            </MultiBinding>
        </TextBlock.Text>
    </TextBlock>
</Border>
```

### 3. 转换器

创建了`ExecutedCountDisplayConverter`多值转换器来格式化显示文本：

- 未开始执行时显示为 "未开始"
- 开始执行后显示为 "已执行: X次"（包括刚开始时的"已执行: 0次"）

### 4. 计数逻辑

- **重置计数**：在`ExecuteTestMethodAsync`方法中，开始执行时重置`ExecutedCount`为0，并设置`HasStartedExecution`为true
- **更新计数**：在`ExecuteGenericLoopTestAsync`方法中，每次循环完成后更新`ExecutedCount`为当前循环次数
- **状态重置**：在测试完成时，重置`HasStartedExecution`为false，回到"未开始"状态

## 使用方法

1. **设置循环次数**：在"公共循环次数"文本框中输入循环次数（-1为无限循环）
2. **点击按钮**：点击"Pin Search测试"或"搬运"按钮开始执行
3. **观察计数**：
   - "剩余: X次" 显示剩余的循环次数
   - "已执行: X次" 显示已完成的循环次数
4. **停止循环**：点击"公共停止循环"按钮停止执行

## 界面效果

- **剩余循环次数**：蓝色背景，显示格式为"剩余: X次"、"无限循环"或"已完成"
- **已执行次数**：绿色背景，显示格式为"未开始"或"已执行: X次"
- **工具提示**：鼠标悬停时显示详细说明

## 注意事项

1. Pin Search测试按钮和搬运按钮不能同时运行
2. 计数器在开始新测试时会自动重置
3. 在无限循环模式下，已执行次数会持续累加
4. 停止循环后，已执行次数保持最后的值，直到开始新的测试

## 相关文件

- `ViewModels/BasicCommandTestViewModel.cs`：主要逻辑实现
- `Views/BasicCommandTest.xaml`：UI界面定义
- `Converters/ExecutedCountDisplayConverter.cs`：显示格式转换器
