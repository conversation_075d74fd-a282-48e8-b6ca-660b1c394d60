﻿using System;
using System.Collections.Generic;
using SqlSugar;
using SqlSugar.DbConvert;
using Zishan.SS200.Cmd.Enums;

// using Zishan.Robot.Shared.AttributeExtend;
// using Zishan.Robot.Shared.Enums;

namespace Zishan.SS200.Cmd.Models.History
{
    [SugarTable("RecipeInfoHistory", TableDescription = "运行配方历史记录")]
    public class RecipeInfoHistory
    {
        /// <summary>
        /// 配方ID
        /// </summary>
        [SugarColumn(IsIdentity = true, IsPrimaryKey = true, ColumnDescription = "配方ID")]
        public int Id { get; set; }

        /// <summary>
        /// 花篮左边条码号
        /// </summary>
        [SugarColumn(ColumnDescription = "花篮左边条码号", IsNullable = true)]
        public string LeftBarcode { get; set; }

        /// <summary>
        /// 花篮右边条码号
        /// </summary>
        [SugarColumn(ColumnDescription = "花篮右边条码号", IsNullable = true)]
        public string RightBarcode { get; set; }

        /// <summary>
        /// 左边P1 Cassette Waffer实际数量
        /// </summary>
        [SugarColumn(ColumnDescription = "左边P1 Cassette Waffer实际数量", IsNullable = true)]
        public int? LeftLotActualCount { get; set; }

        /// <summary>
        /// 右边P2 Cassette Waffer实际数量
        /// </summary>
        [SugarColumn(ColumnDescription = "右边P2 Cassette Waffer实际数量", IsNullable = true)]
        public int? RightLotActualCount { get; set; }

        /// <summary>
        /// Sequence配方名称
        /// </summary>
        [SugarColumn(ColumnDescription = "Sequence配方名称")]
        public string RecipeName { get; set; }

        /// <summary>
        /// 是否下次连续运行模式
        /// </summary>
        [SugarColumn(ColumnDescription = "是否下方连续运行模式")]
        public bool IsRunContinuous { get; set; }

        /// <summary>
        /// 是否默认数量
        /// </summary>
        [SugarColumn(ColumnDescription = "是否默认数量")]
        public bool DefalutQty { get; set; }

        /// <summary>
        /// 花篮顶部Slot号，上面是上限，比如：25
        /// </summary>
        [SugarColumn(ColumnDescription = "花篮顶部Slot号")]
        public int Top { get; set; }

        /// <summary>
        /// 花篮低部Slot号，下面是下限，比如：1
        /// </summary>
        [SugarColumn(ColumnDescription = "花篮低部Slot号")]
        public int Bottom { get; set; }

        /// <summary>
        /// 左侧Wafer映射状态
        /// </summary>
        [SugarColumn(ColumnDescription = "左侧Wafer映射状态", IsJson = true, IsNullable = true)]
        public List<EnuWaferMappingStatus> LeftRecipeIds { get; set; }

        /// <summary>
        /// 右侧Wafer映射状态
        /// </summary>
        [SugarColumn(ColumnDescription = "右侧Wafer映射状态", IsJson = true, IsNullable = true)]
        public List<EnuWaferMappingStatus> RightRecipeIds { get; set; }

        /// <summary>
        /// Cassette运行状态： fcRecipeSelect、fcProcessing、fcUnloading、fcWaitRemove
        /// </summary>
        [SugarColumn(ColumnDescription = "Cassette运行状态： fcRecipeSelect、fcProcessing、fcUnloading、fcWaitRemove", ColumnDataType = "varchar(50)", SqlParameterDbType = typeof(EnumToStringConvert))]
        public CassetteRunStatusEnum FcStatus { get; set; }

        #region 扩展字段

        /// <summary>
        /// 是否最新，最新一条设置为‘Y’，其它都为‘N’
        /// </summary>
        [SugarColumn(ColumnDescription = "是否有效")]
        public string Valid { get; set; } = "Y";

        /// <summary>
        /// 本地电脑IP地址
        /// </summary>
        [SugarColumn(ColumnDescription = "本地电脑IP地址", IsNullable = true)]
        public String ClientIp { get; set; } = "127.0.0.1";

        /// <summary>
        /// 客户端厂商自定义设备名，例如：A2301_某某1、A2302_某某2、A2303_某某3
        /// </summary>
        [SugarColumn(ColumnDescription = "客户端厂商自定义设备名，例如：A2301_某某1、A2302_某某2、A2303_某某3", IsNullable = true)]
        public String ClientName { get; set; } = "";

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(ColumnDescription = "排序号", IsNullable = true)]
        public int? SortCode { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [SugarColumn(ColumnDescription = "备注信息", IsNullable = true)]
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", InsertServerTime = true)]
        public DateTime CreateTime { get; set; }

        #endregion 扩展字段
    }
}