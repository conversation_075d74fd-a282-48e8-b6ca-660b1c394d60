﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.Process
{
    /// <summary>
    /// 工作单元类型
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuUnitWorkStatus
    {
        /// <summary>
        /// 单元空闲
        /// </summary>
        [Description("单元空闲")]
        Idle = 0,

        /// <summary>
        /// 单元动作中
        /// </summary>
        [Description("单元动作中")]
        Busy = 1,

        /// <summary>
        /// 单元工艺中
        /// </summary>
        [Description("单元工艺中")]
        Process = 2,

        /// <summary>
        /// 单元故障
        /// </summary>
        [Description("单元故障")]
        Alarm = 10,

        /// <summary>
        /// 单元不存在
        /// </summary>
        [Description("单元不存在")]
        Null = 20
    }
}