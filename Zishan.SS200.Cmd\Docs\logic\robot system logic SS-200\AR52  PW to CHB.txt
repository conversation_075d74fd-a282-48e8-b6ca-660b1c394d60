﻿AR52
PW to CHB
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			CHB exist status review
SS26/SS27
				SS26
CHB enable
					CHB slot status review
LSS11 / LSS12
						LSS11=0 LSS12=0
							paddle slot status review
LSS5~LSS8
								LSS5~LSS8=0
									compare ABS(RTF-RP2) with ABS(RTF-RP6)
										ABS(RTF-RP2)≤ABS(RTF-RP6)
											AR2
T-axis smooth to CHB
												AR22
Move Z-axis height smooth to CHA get
													CHB-ASP1
CHB open slit door
CHB-ASP4
CHB lift pin down
														AR12
R-axis smooth to CHB extend
															CHB-ASP3
CHB lift pin up
																AR62
wafer status smooth paddle to CHB
																	AR19
R-axis zero position
																		CHB-ASP2
CHB close slit door
																			command done
										ABS(RTF-RP2)>ABS(RTF-RP6)
											AR6
T-axis nose to cassette
												AR26
Move Z-axis height nose to CHA get
													CHB-ASP1
CHB open slit door
CHB-ASP4
CHB lift pin down
														AR16
R-axis nose to CHA extend
															CHB-ASP3
CHB lift pin up
																AR73
wafer status nose paddle to CHB
																	AR19
R-axis zero position
																		CHB-ASP2
CHB close slit door
																			command done
								LSS7 XX=1
or/and
LSS8 XX=1
									AR6
T-axis nose to cassette
										AR26
Move Z-axis height nose to CHA get
											CHB-ASP1
CHB open slit door
CHB-ASP4
CHB lift pin down
												AR16
R-axis nose to CHA extend
													CHB-ASP3
CHB lift pin up
														AR73
wafer status nose paddle to CHB
															AR19
R-axis zero position
																CHB-ASP2
CHB close slit door
																	command done
								LSS5 XX=1
or/and
LSS6 XX=1
									AR2
T-axis smooth to CHB
										AR22
Move Z-axis height smooth to CHA get
											CHB-ASP1
CHB open slit door
CHB-ASP4
CHB lift pin down
												AR12
R-axis smooth to CHB extend
													CHB-ASP43CHB lift pin up
														AR62
wafer status smooth paddle to CHB
															AR19
R-axis zero position
																CHB-ASP2
CHB close slit door
																	command done
								RS36=1 RS37=1
									RA17 ALARM
						others status
							RA54 ALARM
				SS27
CHB disable
					RA26 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM