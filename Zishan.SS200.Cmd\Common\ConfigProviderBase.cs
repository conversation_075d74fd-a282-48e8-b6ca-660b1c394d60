using System;
using System.Collections.Generic;
using System.IO;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 配置参数基础类，用于JSON反序列化
    /// </summary>
    public class ConfigParametersBase
    {
        public List<ConfigureSetting> ConfigureSettings { get; set; }
    }

    /// <summary>
    /// 配置提供者基类，提供通用的配置加载、监控和访问功能
    /// </summary>
    /// <typeparam name="TEnum">配置参数枚举类型</typeparam>
    /// <typeparam name="TConfig">配置类型</typeparam>
    public abstract class ConfigProviderBase<TEnum, TConfig> : IDisposable
        where TEnum : Enum
        where TConfig : ConfigParametersBase, new()
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ConfigProviderBase<TEnum, TConfig>));

        /// <summary>
        /// 枚举字典，用于存储配置值
        /// </summary>
        protected readonly EnumDictionary<TEnum> Settings = new EnumDictionary<TEnum>();

        /// <summary>
        /// 文件系统监视器，监控配置文件变化
        /// </summary>
        protected readonly FileSystemWatcher ConfigWatcher;

        /// <summary>
        /// 配置文件路径
        /// </summary>
        protected readonly string ConfigPath;

        /// <summary>
        /// 最后修改时间，用于防抖动处理
        /// </summary>
        protected DateTime LastModifiedTime = DateTime.MinValue;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        protected ConfigProviderBase(string configPath)
        {
            ConfigPath = configPath;

            // 初始化文件系统监视器
            string configDir = Path.GetDirectoryName(GetConfigFilePath());
            string configFileName = Path.GetFileName(ConfigPath);

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            ConfigWatcher = new FileSystemWatcher(configDir, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            // 注册文件变化事件处理
            ConfigWatcher.Changed += OnConfigFileChanged;
            ConfigWatcher.Created += OnConfigFileChanged;

            // 初始化默认值
            InitializeDefaultValues();

            // 尝试加载配置文件
            LoadFromJson();
        }

        /// <summary>
        /// 初始化默认值，子类必须实现
        /// </summary>
        protected abstract void InitializeDefaultValues();

        /// <summary>
        /// 处理配置文件变化
        /// </summary>
        protected virtual void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 防抖动处理
                if ((DateTime.Now - LastModifiedTime).TotalMilliseconds < 100)
                {
                    return;
                }

                _logger.Info($"检测到配置文件变化: {e.FullPath}, 变化类型: {e.ChangeType}");
                LoadFromJson();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理配置文件变化事件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从JSON配置文件加载参数
        /// </summary>
        /// <returns>是否加载成功</returns>
        public virtual bool LoadFromJson()
        {
            try
            {
                string jsonFilePath = GetConfigFilePath();
                if (!File.Exists(jsonFilePath))
                {
                    _logger.Warn($"配置参数文件不存在: {jsonFilePath}，将使用默认值");
                    return false;
                }

                // 获取文件最后修改时间
                DateTime currentModified = File.GetLastWriteTime(jsonFilePath);

                // 如果文件未修改，则不重新加载
                if (currentModified == LastModifiedTime)
                {
                    return true;
                }

                LastModifiedTime = currentModified;

                // 读取配置文件内容
                string jsonContent;
                using (var fileStream = new FileStream(jsonFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    jsonContent = reader.ReadToEnd();
                }

                // 反序列化配置
                var config = JsonConvert.DeserializeObject<TConfig>(jsonContent);

                if (config?.ConfigureSettings == null || config.ConfigureSettings.Count == 0)
                {
                    _logger.Warn("未找到有效的配置参数，将使用默认值");
                    return false;
                }

                // 加载配置到内存
                foreach (var setting in config.ConfigureSettings)
                {
                    if (string.IsNullOrEmpty(setting.Code))
                    {
                        _logger.Warn($"参数ID {setting.Id} 缺少代码标识，已跳过");
                        continue;
                    }

                    if (TryParseEnum(setting.Code, out TEnum enumValue))
                    {
                        Settings.Set(enumValue, setting.Value);
                        _logger.Debug($"加载参数 {setting.Code} = {setting.Value} ({setting.Description})");
                    }
                    else
                    {
                        _logger.Warn($"无法将 {setting.Code} 解析为有效的枚举值，已跳过");
                    }
                }

                OnConfigLoaded();

                _logger.Info($"成功从 {jsonFilePath} 加载 {config.ConfigureSettings.Count} 个配置参数");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载配置参数文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 配置加载完成后的回调，子类可以重写以添加额外逻辑
        /// </summary>
        protected virtual void OnConfigLoaded()
        {
            // 可由子类重写添加额外逻辑
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        protected virtual string GetConfigFilePath()
        {
            try
            {
                return App.ConfigHelper.GetConfigFilePath(ConfigPath);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取配置文件路径失败: {ex.Message}", ex);

                // 回退策略 - 尝试直接拼接路径
                string fallbackPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    ConfigPath);

                _logger.Info($"使用回退路径: {fallbackPath}");
                return fallbackPath;
            }
        }

        /// <summary>
        /// 尝试将字符串解析为枚举值
        /// </summary>
        protected bool TryParseEnum(string value, out TEnum result)
        {
            try
            {
                result = (TEnum)Enum.Parse(typeof(TEnum), value, true);
                return true;
            }
            catch
            {
                result = default;
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public virtual void Dispose()
        {
            ConfigWatcher?.Dispose();
        }

        #region 辅助方法

        /// <summary>
        /// 获取参数值（泛型方法）
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="key">参数枚举键</param>
        /// <returns>参数值</returns>
        public T GetValue<T>(TEnum key)
        {
            return Settings.Get<T>(key);
        }

        /// <summary>
        /// 获取参数值，如果不存在则返回默认值
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="key">参数枚举键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>参数值或默认值</returns>
        public T GetValueOrDefault<T>(TEnum key, T defaultValue)
        {
            return Settings.GetOrDefault(key, defaultValue);
        }

        /// <summary>
        /// 获取整数参数值
        /// </summary>
        /// <param name="key">参数枚举键</param>
        /// <returns>整数参数值</returns>
        public int GetIntValue(TEnum key)
        {
            return GetValue<int>(key);
        }

        /// <summary>
        /// 获取浮点数参数值
        /// </summary>
        /// <param name="key">参数枚举键</param>
        /// <returns>浮点数参数值</returns>
        public double GetDoubleValue(TEnum key)
        {
            return GetValue<double>(key);
        }

        /// <summary>
        /// 获取字符串参数值
        /// </summary>
        /// <param name="key">参数枚举键</param>
        /// <returns>字符串参数值</returns>
        public string GetStringValue(TEnum key)
        {
            return GetValue<string>(key);
        }

        /// <summary>
        /// 获取布尔参数值
        /// </summary>
        /// <param name="key">参数枚举键</param>
        /// <returns>布尔参数值</returns>
        public bool GetBoolValue(TEnum key)
        {
            return GetValue<bool>(key);
        }

        #endregion
    }
}