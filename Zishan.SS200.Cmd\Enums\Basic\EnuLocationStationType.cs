namespace Zishan.SS200.Cmd.Enums.Basic
{
    /// <summary>
    /// 晶圆位置站点类型枚举
    /// 支持不同精度级别的位置定义：
    /// - 区域级别：CoolingChamber（用于T轴、R轴）
    /// - 精确级别：CoolingTop、CoolingBottom（用于Z轴）
    /// </summary>
    public enum EnuLocationStationType
    {
        /// <summary>
        /// 无/未定义位置
        /// </summary>
        None = -1,

        /// <summary>
        /// 晶圆盒
        /// </summary>
        Cassette = 0,

        /// <summary>
        /// 工艺腔室A (原CHA)
        /// </summary>
        ChamberA = 1,

        /// <summary>
        /// 工艺腔室B (原CHB)
        /// </summary>
        ChamberB = 2,

        /// <summary>
        /// 冷却腔区域（用于T轴旋转、R轴伸缩）
        /// T轴和R轴只需要知道"冷却腔方向"，不区分上下层
        /// </summary>
        CoolingChamber = 3,

        /// <summary>
        /// 上层单独冷却腔（用于Z轴精确高度控制）
        /// </summary>
        CoolingTop = 4,

        /// <summary>
        /// 下层单独冷却腔（用于Z轴精确高度控制）
        /// </summary>
        CoolingBottom = 5
    }
}