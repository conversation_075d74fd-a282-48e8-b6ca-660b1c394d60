{"configureSettings": [{"id": 1, "code": "PPS1", "description": "slit door motion min time", "value": 0.3, "unit": "S"}, {"id": 2, "code": "PPS2", "description": "slit door motion max time", "value": 3, "unit": "S"}, {"id": 3, "code": "PPS3", "description": "lift pin motion min time", "value": 0.3, "unit": "S"}, {"id": 4, "code": "PPS4", "description": "lift pin motion max time", "value": 3, "unit": "S"}, {"id": 5, "code": "PPS5", "description": "max delta pressure for open slit door", "value": 4, "unit": "<PERSON><PERSON>"}, {"id": 6, "code": "PPS6", "description": "chamber A process vacuum pressure", "value": 9, "unit": "<PERSON><PERSON>"}, {"id": 7, "code": "PPS7", "description": "loadlock process vacuum pressure (PAI7 read)", "value": 100, "unit": "<PERSON><PERSON>"}, {"id": 8, "code": "PPS8", "description": "MFC1 flow range", "value": 10000, "unit": "sccm"}, {"id": 9, "code": "PPS9", "description": "MFC2 flow range", "value": 1000, "unit": "sccm"}, {"id": 10, "code": "PPS10", "description": "MFC3 flow range", "value": 1000, "unit": "sccm"}, {"id": 11, "code": "PPS11", "description": "MFC4 flow range", "value": 200, "unit": "sccm"}, {"id": 12, "code": "PPS12", "description": "fault for MFC1 flow deviation", "value": 50, "unit": "sccm"}, {"id": 13, "code": "PPS13", "description": "fault for MFC2 flow deviation", "value": 20, "unit": "sccm"}, {"id": 14, "code": "PPS14", "description": "fault for MFC3 flow deviation", "value": 20, "unit": "sccm"}, {"id": 15, "code": "PPS15", "description": "fault for MFC4 flow deviation", "value": 10, "unit": "sccm"}, {"id": 16, "code": "PPS16", "description": "max time for gas flow stable", "value": 5, "unit": "S"}, {"id": 17, "code": "PPS17", "description": "control line for MFC1 flow", "value": 20, "unit": "sccm"}, {"id": 18, "code": "PPS18", "description": "control line for MFC2 flow", "value": 10, "unit": "sccm"}, {"id": 19, "code": "PPS19", "description": "control line for MFC3 flow", "value": 10, "unit": "sccm"}, {"id": 20, "code": "PPS20", "description": "control line for MFC4 flow", "value": 6, "unit": "sccm"}, {"id": 21, "code": "PPS21", "description": "point for MFC flow out of control line/10point (2points/s)", "value": 3, "unit": "point"}, {"id": 22, "code": "PPS22", "description": "chamber A pressure gauge range", "value": 10, "unit": "<PERSON><PERSON>"}, {"id": 23, "code": "PPS23", "description": "loadlock pressure gauge range", "value": 100, "unit": "<PERSON><PERSON>"}, {"id": 24, "code": "PPS24", "description": "fault for pressure flow deviation (setpoint)", "value": 10, "unit": "percent"}, {"id": 25, "code": "PPS25", "description": "max time for pressure control", "value": 10, "unit": "S"}, {"id": 26, "code": "PPS26", "description": "control line for pressure control (setpoint)", "value": 8, "unit": "percent"}, {"id": 27, "code": "PPS27", "description": "point for pressure control out of control line/10point", "value": 3, "unit": "point"}, {"id": 28, "code": "PPS28", "description": "max tine for temperature servo", "value": 600, "unit": "S"}, {"id": 29, "code": "PPS29", "description": "low temperature deviation", "value": 10, "unit": "℃"}, {"id": 30, "code": "PPS30", "description": "high temperature deviation", "value": 20, "unit": "℃"}, {"id": 31, "code": "PPS31", "description": "chamber temperature deviation for wafer receive", "value": 5, "unit": "℃"}, {"id": 32, "code": "PPS32", "description": "control line for chamber temperature", "value": 300, "unit": "S"}, {"id": 33, "code": "PPS33", "description": "point for temperature out of control line/10point", "value": 3, "unit": "S"}, {"id": 34, "code": "PPS34", "description": "throttle valve motion max time", "value": 2, "unit": "S"}, {"id": 35, "code": "PPS35", "description": "RF forward power deviation fault", "value": 15, "unit": "W"}, {"id": 36, "code": "PPS36", "description": "RF reflector out of limit", "value": 10, "unit": "W"}, {"id": 37, "code": "PPS37", "description": "point for RF forward power out of control line/10point", "value": 3, "unit": "point"}, {"id": 38, "code": "PPS38", "description": "point for RF reflector power out of control line/10point", "value": 3, "unit": "point"}, {"id": 39, "code": "PPS39", "description": "RF forward power control line", "value": 10, "unit": "W"}, {"id": 40, "code": "PPS40", "description": "RF reflector power control line", "value": 10, "unit": "W"}, {"id": 41, "code": "PPS41", "description": "plasma on check", "value": "check", "unit": "logic"}, {"id": 42, "code": "PPS42", "description": "max time for RF stable", "value": 3, "unit": "S"}, {"id": 43, "code": "PPS43", "description": "point for plasma on sensor tripped of control line/10point", "value": 3, "unit": "point"}, {"id": 44, "code": "PPS44", "description": "max time for process chamber pump down", "value": 10, "unit": "<PERSON><PERSON>"}, {"id": 45, "code": "PPS45", "description": "active chamber", "value": "CHA", "unit": "N/A"}, {"id": 46, "code": "PPS46", "description": "process chamber pumping down pressure deviation", "value": 0.5, "unit": "<PERSON><PERSON>"}]}