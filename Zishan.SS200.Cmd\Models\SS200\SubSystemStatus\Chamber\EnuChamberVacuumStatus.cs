using System.ComponentModel;

namespace Zish<PERSON>.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// 腔体真空状态枚举
    /// </summary>
    public enum EnuChamberVacuumStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 腔体工艺真空状态 (SP11: PDI5=1 PDI6=1 PAI1≤PPS6)
        /// </summary>
        [Description("工艺真空")]
        ProcessVacuum = 1,

        /// <summary>
        /// 腔体非工艺真空状态 (SP12: PDI5=0 or PDI6=0 or PAI1>PPS6)
        /// </summary>
        [Description("非工艺真空")]
        NoProcessVacuum = 2
    }
}
