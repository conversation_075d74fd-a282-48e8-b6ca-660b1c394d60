using System;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Models
{
    /// <summary>
    /// Modbus线圈（布尔量转换为INT）
    /// </summary>
    public partial class ModbusCoil : BaseModbusRegister, ICloneable, IEquatable<ModbusCoil>
    {
        private bool isManualUpdate;

        /// <summary>
        /// 设备类型
        /// </summary>
        [ObservableProperty]
        private EnuMcuDeviceType deviceType;

        /// <summary>
        /// Modbus寄存器值 转换为Modbus线圈布尔量
        /// </summary>
        [ObservableProperty]
        private bool? coilvalue;

        /// <summary>
        /// 设备是否已连接
        /// </summary>
        [ObservableProperty]
        private bool isDeviceConnected;

        /// <summary>
        /// IO代码 (如: RDI1, SDI1, PDI1等)
        /// </summary>
        [ObservableProperty]
        private string ioCode = "";

        /// <summary>
        /// MCU IO映射 (如: SDI_26/RDI_26, PDI_9等)
        /// </summary>
        [ObservableProperty]
        private string mcuIo = "";

        /// <summary>
        /// IO类型 (DI, DO)
        /// </summary>
        [ObservableProperty]
        private string ioType = "";

        /// <summary>
        /// 传感器类型 (如: optic sensor NPN, switch等)
        /// </summary>
        [ObservableProperty]
        private string sensorType = "";

        /// <summary>
        /// 控制类型 (如: EV, position control等)
        /// </summary>
        [ObservableProperty]
        private string controlType = "";

        /// <summary>
        /// 详细备注信息 (如: no wafer:0 wafer:1, enable:1等)
        /// </summary>
        [ObservableProperty]
        private string detailedRemark = "";

        /// <summary>
        /// 更新设备连接状态
        /// </summary>
        public void UpdateConnectionStatus()
        {
            var service = S200McuCmdService.Instance;
            switch (DeviceType)
            {
                case EnuMcuDeviceType.Shuttle:
                    IsDeviceConnected = service.Shuttle?.IsConnected ?? false;
                    break;

                case EnuMcuDeviceType.Robot:
                    IsDeviceConnected = service.Robot?.IsConnected ?? false;
                    break;

                case EnuMcuDeviceType.ChamberA:
                    IsDeviceConnected = service.ChamberA?.IsConnected ?? false;
                    break;

                case EnuMcuDeviceType.ChamberB:
                    IsDeviceConnected = service.ChamberB?.IsConnected ?? false;
                    break;
            }
        }

        /// <summary>
        /// 手动写入值到设备
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanWriteValue))]
        public async Task WriteValueManually(bool value)
        {
            try
            {
                // 写入前更新连接状态
                UpdateConnectionStatus();

                if (!IsDeviceConnected)
                {
                    MessageBox.Show("设备未连接，无法写入数据。", "写入失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                isManualUpdate = true;
                await S200McuCmdService.Instance.WriteCoilValueAsync(this, value);
                Coilvalue = value; // 写入成功后更新UI
            }
            catch (Exception ex)
            {
                MessageBox.Show($"写入失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                // 恢复UI状态
                Coilvalue = !value;
            }
            finally
            {
                isManualUpdate = false;
            }
        }

        /// <summary>
        /// 检查是否可以写入值
        /// </summary>
        private bool CanWriteValue()
        {
            UpdateConnectionStatus();
            return IsWriteable && IsDeviceConnected;
        }

        /// <summary>
        /// 当Coilvalue属性变更时调用
        /// </summary>
        partial void OnCoilvalueChanged(bool? oldValue, bool? newValue)
        {
            // 只在手动更新时写入设备
            if (isManualUpdate || !IsDeviceConnected)
            {
                return;
            }

            // 自动更新时不触发写入
            if (newValue != oldValue && newValue.HasValue)
            {
                // 这里只更新UI显示，不写入设备
                return;
            }
        }

        public object Clone()
        {
            return new ModbusCoil
            {
                Address = this.Address,
                Name = this.Name,
                Title = this.Title,
                Description = this.Description,
                Remark = this.Remark,
                Value = this.Value,
                IsWriteable = this.IsWriteable,
                Coilvalue = this.Coilvalue,
                IsDeviceConnected = this.IsDeviceConnected,
                DeviceType = this.DeviceType,
                IoCode = this.IoCode,
                McuIo = this.McuIo,
                IoType = this.IoType,
                SensorType = this.SensorType,
                ControlType = this.ControlType,
                DetailedRemark = this.DetailedRemark
            };
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as ModbusCoil);
        }

        public bool Equals(ModbusCoil other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;

            return Address == other.Address &&
                   string.Equals(Name, other.Name, StringComparison.OrdinalIgnoreCase);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + Address.GetHashCode();
                hash = hash * 23 + (Name?.GetHashCode() ?? 0);
                return hash;
            }
        }

        public static bool operator ==(ModbusCoil left, ModbusCoil right)
        {
            if (left is null) return right is null;
            return left.Equals(right);
        }

        public static bool operator !=(ModbusCoil left, ModbusCoil right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            var strInfo = $"IOCode:{IoCode},mcuIo:{McuIo},Content:{Title},Remark:{Remark}";
            return strInfo;
        }
    }
}