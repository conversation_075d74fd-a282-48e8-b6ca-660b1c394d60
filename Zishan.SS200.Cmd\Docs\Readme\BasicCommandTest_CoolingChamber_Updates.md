# BasicCommandTest UI 和 ViewModel 冷却腔规则更新

## 更新概述

根据 `EnuLocationStationType.cs` 定义的分层精度规则，对 BasicCommandTest 的 UI 界面和 ViewModel 逻辑进行了全面更新，确保不同轴向使用正确的位置类型。

## 核心规则

### EnuLocationStationType 分层精度设计
- **区域级别**：`CoolingChamber` - 用于 T 轴旋转和 R 轴伸缩
- **精确级别**：`CoolingTop`、`CoolingBottom` - 用于 Z 轴精确高度控制

### 轴向使用规则
- **T 轴（旋转轴）**：使用 `CoolingChamber`，不区分上下层
- **R 轴（伸缩轴）**：使用 `CoolingChamber`，不区分上下层  
- **Z 轴（升降轴）**：使用 `CoolingTop` 或 `CoolingBottom`，精确控制高度

## 主要修改内容

### 1. UI 界面更新 (BasicCommandTest.xaml)

#### T 轴部分修改
- **Nose 端**：将两个按钮（CoolingTop/CoolingBottom）合并为一个 CoolingChamber 按钮
- **Smooth 端**：同样合并为一个 CoolingChamber 按钮
- **按钮文本**：更新为"移动到冷却腔区域"

#### R 轴部分修改
- **Nose 端**：将两个按钮（CoolingTop/CoolingBottom）合并为一个 CoolingChamber 按钮
- **Smooth 端**：同样合并为一个 CoolingChamber 按钮
- **按钮文本**：更新为"移动到冷却腔区域"

#### Z 轴部分增强
- **Nose 端**：新增 CoolingTop 和 CoolingBottom 按钮
- **Smooth 端**：新增 CoolingTop 和 CoolingBottom 按钮
- **按钮文本**：分别为"移动到冷却腔上层"和"移动到冷却腔下层"

### 2. ViewModel 逻辑更新 (BasicCommandTestViewModel.cs)

#### 新增命令
- 添加 `MoveZAxisToLocationCommand` 命令，支持 Z 轴移动到指定位置类型

#### 初始化方法更新
- `InitializeFromChamberOptions()` 和 `InitializeToChamberOptions()` 方法中添加 CoolingChamber 选项
- 同时保留 CoolingTop 和 CoolingBottom 选项以支持 Z 轴操作

#### 配置方法更新
- `GetSlotCountByStationType()` 方法增加对 CoolingChamber 的支持

#### Robot 命令更新
- T 轴相关命令（AR3, AR7）：从 CoolingTop 改为 CoolingChamber
- R 轴相关命令（AR13, AR17）：从 CoolingTop 改为 CoolingChamber

#### 测试方法更新
- 所有 T 轴测试调用：从 CoolingTop/CoolingBottom 改为 CoolingChamber
- 所有 R 轴测试调用：从 CoolingTop/CoolingBottom 改为 CoolingChamber
- Z 轴测试调用：保持使用 CoolingTop/CoolingBottom

## 修改详情

### UI 按钮参数更新
```xml
<!-- T轴 - 修改前 -->
<Button CommandParameter="Nose,CoolingTop" Content="【Cooling相同】移动到CoolingTop" />
<Button CommandParameter="Nose,CoolingBottom" Content="【Cooling相同】移动到CoolingBottom" />

<!-- T轴 - 修改后 -->
<Button CommandParameter="Nose,CoolingChamber" Content="移动到冷却腔区域" />

<!-- Z轴 - 新增 -->
<Button CommandParameter="Nose,CoolingTop" Content="移动到冷却腔上层" />
<Button CommandParameter="Nose,CoolingBottom" Content="移动到冷却腔下层" />
```

### ViewModel 枚举使用更新
```csharp
// T轴 - 修改前
await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingTop);

// T轴 - 修改后
await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingChamber);

// Z轴 - 保持不变
await _mcuCmdService.MoveZAxisToGetPositionAsync(endType, EnuLocationStationType.CoolingTop, slot);
```

## 向后兼容性

- 现有的 CoolingTop/CoolingBottom 枚举值保持不变
- Z 轴相关的所有操作继续使用精确位置类型
- 新增的 CoolingChamber 不影响现有的 Z 轴逻辑

## 测试建议

1. **T 轴测试**：验证使用 CoolingChamber 参数的按钮能正确执行旋转操作
2. **R 轴测试**：验证使用 CoolingChamber 参数的按钮能正确执行伸缩操作
3. **Z 轴测试**：验证新增的 CoolingTop/CoolingBottom 按钮能正确执行升降操作
4. **集成测试**：验证不同轴向的组合操作能正确协调工作

## 相关文件

- `Zishan.SS200.Cmd/Views/BasicCommandTest.xaml` - UI 界面
- `Zishan.SS200.Cmd/ViewModels/BasicCommandTestViewModel.cs` - ViewModel 逻辑
- `Zishan.SS200.Cmd/Enums/Basic/EnuLocationStationType.cs` - 位置类型枚举定义

## 注意事项

1. 确保 MCU 命令服务能正确处理 CoolingChamber 参数
2. 验证位置参数配置中 CoolingChamber 有对应的 T 轴和 R 轴参数
3. 检查状态更新逻辑是否需要相应调整以支持新的位置类型
