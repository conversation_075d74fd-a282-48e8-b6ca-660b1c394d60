using System;
using System.Collections.Generic;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberA;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.ChamberA;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// 枚举字典配置系统使用示例
    /// </summary>
    public class EnumDictionaryConfigExample
    {
        /// <summary>
        /// 使用新配置系统的示例
        /// </summary>
        public static void UseNewConfigSystem()
        {
            // 使用新的配置提供者
            var provider = ChaConfigParametersProviderNew.Instance;

            // 使用枚举作为键访问配置
            int doorMinTime = provider.GetValue<int>(EnuChaConfigParameterCodes.PPS1);
            int doorMaxTime = provider.GetIntValue(EnuChaConfigParameterCodes.PPS2);
            double rfFrequency = provider.GetDoubleValue(EnuChaConfigParameterCodes.PPS17);

            Console.WriteLine($"门最小操作时间: {doorMinTime}秒");
            Console.WriteLine($"门最大操作时间: {doorMaxTime}秒");
            Console.WriteLine($"RF频率最小值: {rfFrequency}MHz");

            // 也可以使用特定的方法访问配置
            int minTime = provider.GetChamberDoorMinTime();
            int maxTime = provider.GetChamberDoorMaxTime();

            Console.WriteLine($"特定方法获取门最小操作时间: {minTime}秒");
            Console.WriteLine($"特定方法获取门最大操作时间: {maxTime}秒");
        }

        /// <summary>
        /// 使用代码生成器生成配置提供者示例
        /// </summary>
        public static void GenerateConfigProvider()
        {
            // 定义默认值
            var defaultValues = new Dictionary<Enum, object>
            {
                { EnuChaConfigParameterCodes.PPS1, 3 },
                { EnuChaConfigParameterCodes.PPS2, 5 },
                { EnuChaConfigParameterCodes.PPS3, 2 },
                { EnuChaConfigParameterCodes.PPS4, 4 }
            };

            // 定义特定方法
            var specificMethods = new List<(Enum key, string methodName, Type returnType, string description)>
            {
                (EnuChaConfigParameterCodes.PPS1, "GetChamberDoorMinTime", typeof(int), "获取处理室门最小操作时间(秒)"),
                (EnuChaConfigParameterCodes.PPS2, "GetChamberDoorMaxTime", typeof(int), "获取处理室门最大操作时间(秒)")
            };

            // 生成代码
            string code = ConfigProviderGenerator.GenerateProviderCode(
                typeof(EnuChaConfigParameterCodes),
                "ChaConfigParametersProvider",
                "Configs/SS200/SubsystemConfigure/ChamberA/ChaConfigParameters.json",
                defaultValues,
                specificMethods,
                "Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberA"
            );

            // 输出生成的代码
            Console.WriteLine(code);

            // 也可以保存到文件
            // ConfigProviderGenerator.SaveToFile(code, "GeneratedProvider.cs");
        }

        /// <summary>
        /// 枚举字典基本使用示例
        /// </summary>
        public static void UseEnumDictionary()
        {
            // 创建枚举字典
            var settings = new EnumDictionary<EnuChaConfigParameterCodes>();

            // 设置默认值
            settings.SetDefault(EnuChaConfigParameterCodes.PPS1, 3);
            settings.SetDefault(EnuChaConfigParameterCodes.PPS2, 5);

            // 设置值
            settings.Set(EnuChaConfigParameterCodes.PPS1, 4);

            // 获取值
            int value1 = settings.Get<int>(EnuChaConfigParameterCodes.PPS1); // 返回4（已设置值）
            int value2 = settings.Get<int>(EnuChaConfigParameterCodes.PPS2); // 返回5（默认值）

            // 获取值或默认值
            int value3 = settings.GetOrDefault(EnuChaConfigParameterCodes.PPS3, 10); // 返回10（键不存在，使用提供的默认值）

            Console.WriteLine($"PPS1: {value1}");
            Console.WriteLine($"PPS2: {value2}");
            Console.WriteLine($"PPS3: {value3}");
        }
    }
}