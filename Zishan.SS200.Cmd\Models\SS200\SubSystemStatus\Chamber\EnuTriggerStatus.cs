using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// 触发状态枚举
    /// </summary>
    public enum EnuTriggerStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 无报警状态 (MPS1)
        /// </summary>
        [Description("无报警")]
        NoAlarm = 1,

        /// <summary>
        /// 报警状态 (MPS2)
        /// </summary>
        [Description("报警")]
        Alarm = 2
    }
}
