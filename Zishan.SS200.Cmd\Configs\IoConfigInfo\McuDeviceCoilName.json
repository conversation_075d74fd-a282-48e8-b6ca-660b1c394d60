{"Shuttle": {"DiNames": {"1": {"name": "cassette door up sensor", "ioCode": "SDI1", "mcuIo": "SDI_1", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "到位:0"}, "2": {"name": "cassette door down sensor", "ioCode": "SDI2", "mcuIo": "SDI_2", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "到位:0"}, "3": {"name": "cassette nest extend sensor", "ioCode": "SDI3", "mcuIo": "SDI_3", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "到位:0"}, "4": {"name": "cassette nest retract sensor", "ioCode": "SDI4", "mcuIo": "SDI_4", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "到位:0"}, "5": {"name": "cassette nest home switch", "ioCode": "SDI5", "mcuIo": "SDI_5", "ioType": "DI", "sensorType": "switch", "remark": "到位:0"}, "6": {"name": "present sensor cassette 1", "ioCode": "SDI6", "mcuIo": "SDI_6", "ioType": "DI", "sensorType": "switch", "remark": "触发为低电平0，未触发为高电平1"}, "7": {"name": "present sensor cassette 2", "ioCode": "SDI7", "mcuIo": "SDI_7", "ioType": "DI", "sensorType": "switch", "remark": "触发为低电平0，未触发为高电平1"}, "8": {"name": "present sensor cassette 3", "ioCode": "SDI8", "mcuIo": "SDI_8", "ioType": "DI", "sensorType": "switch", "remark": "触发为低电平0，未触发为高电平1"}, "9": {"name": "present sensor cassette 4", "ioCode": "SDI9", "mcuIo": "SDI_9", "ioType": "DI", "sensorType": "switch", "remark": "触发为低电平0，未触发为高电平1"}, "10": {"name": "DI water flow switch", "ioCode": "SDI10", "mcuIo": "SDI_25", "ioType": "DI", "sensorType": "switch", "remark": "触发为低电平0，未触发为高电平1"}, "11": {"name": "light curtain 1", "ioCode": "SDI11", "mcuIo": "SDI_10", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "触发为低电平0，未触发为高电平1"}, "12": {"name": "light curtain 2", "ioCode": "SDI12", "mcuIo": "SDI_11", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "spare"}, "13": {"name": "shuttle rotate sensor 1", "ioCode": "SDI13", "mcuIo": "SDI_12", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "接近为高电平1，未接近为低电平0"}, "14": {"name": "shuttle rotate sensor 2", "ioCode": "SDI14", "mcuIo": "SDI_13", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "接近为高电平1，未接近为低电平0"}, "15": {"name": "shuttle up sensor", "ioCode": "SDI15", "mcuIo": "SDI_14", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "接近为高电平1，未接近为低电平0"}, "16": {"name": "shuttle down sensor", "ioCode": "SDI16", "mcuIo": "SDI_15", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "接近为高电平1，未接近为低电平0"}, "17": {"name": "wafer slide out sensor 1 FL", "ioCode": "SDI17", "mcuIo": "SDI_16", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "挡住为高电平1，未挡住为低电平0"}, "18": {"name": "wafer slide out sensor 2 FR", "ioCode": "SDI18", "mcuIo": "SDI_17", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "挡住为高电平1，未挡住为低电平0"}, "19": {"name": "wafer slide out sensor 3 BL", "ioCode": "SDI19", "mcuIo": "SDI_18", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "挡住为高电平1，未挡住为低电平0"}, "20": {"name": "wafer slide out sensor 4 BR", "ioCode": "SDI20", "mcuIo": "SDI_19", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "挡住为高电平1，未挡住为低电平0"}, "21": {"name": "shuttle foreline switch", "ioCode": "SDI21", "mcuIo": "SDI_20", "ioType": "DI", "sensorType": "switch", "remark": ""}, "22": {"name": "shuttle vacuum ISO valve open sensor", "ioCode": "SDI22", "mcuIo": "SDI_21", "ioType": "DI", "sensorType": "接近传感器", "remark": "到位:0"}, "23": {"name": "shuttle vacuum ISO valve close sensor", "ioCode": "SDI23", "mcuIo": "SDI_22", "ioType": "DI", "sensorType": "接近传感器", "remark": "到位:0"}, "24": {"name": "XV cross valve open sensor", "ioCode": "SDI24", "mcuIo": "SDI_23", "ioType": "DI", "sensorType": "接近传感器", "remark": "到位:0"}, "25": {"name": "XV cross valve close sensor", "ioCode": "SDI25", "mcuIo": "SDI_24", "ioType": "DI", "sensorType": "接近传感器", "remark": "到位:0"}, "26": {"name": "cooling chamber leak sensor", "ioCode": "SDI26", "mcuIo": "SDI_30", "ioType": "DI", "sensorType": "", "remark": ""}, "27": {"name": "spare", "ioCode": "SDI27", "mcuIo": "", "ioType": "DI", "sensorType": "", "remark": "spare"}, "28": {"name": "spare", "ioCode": "SDI28", "mcuIo": "", "ioType": "DI", "sensorType": "", "remark": "spare"}}, "DONames": {"1": {"name": "cassette door cylinder up", "ioCode": "SDO1", "mcuIo": "SEVDO_1", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "2": {"name": "cassette door cylinder down", "ioCode": "SDO2", "mcuIo": "SEVDO_2", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "3": {"name": "cassette door motion enable", "ioCode": "SDO3", "mcuIo": "SEVDO_3", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "4": {"name": "cassette nest cylinder extend", "ioCode": "SDO4", "mcuIo": "SEVDO_4", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "5": {"name": "cassette nest cylinder retract", "ioCode": "SDO5", "mcuIo": "SEVDO_5", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "6": {"name": "shuttle vacuum ISO valve", "ioCode": "SDO6", "mcuIo": "SEVDO_6", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "7": {"name": "shuttle backfill valve", "ioCode": "SDO7", "mcuIo": "SEVDO_7", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "8": {"name": "XV cross valve", "ioCode": "SDO8", "mcuIo": "SEVDO_8", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "9": {"name": "loadlock bleed valve", "ioCode": "SDO9", "mcuIo": "SEVDO_9", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "10": {"name": "loadlock backfill valve", "ioCode": "SDO10", "mcuIo": "SEVDO_10", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "11": {"name": "shuttle assy rotation CW", "ioCode": "SDO11", "mcuIo": "SDO_1", "ioType": "DO", "controlType": "", "remark": ""}, "12": {"name": "shuttle assy rotation CCW", "ioCode": "SDO12", "mcuIo": "SDO_2", "ioType": "DO", "controlType": "", "remark": ""}, "13": {"name": "shuttle motor up", "ioCode": "SDO13", "mcuIo": "SDO_3", "ioType": "DO", "controlType": "", "remark": ""}, "14": {"name": "shuttle motor down", "ioCode": "SDO14", "mcuIo": "SDO_4", "ioType": "DO", "controlType": "", "remark": ""}, "15": {"name": "shuttle braker", "ioCode": "SDO15", "mcuIo": "SDO_5", "ioType": "DO", "controlType": "", "remark": ""}, "16": {"name": "PCWS switch", "ioCode": "SDO16", "mcuIo": "SDO_15", "ioType": "DO", "controlType": "", "remark": ""}, "17": {"name": "LOAD_READY1", "ioCode": "SDO17", "mcuIo": "SDO_6", "ioType": "DO", "controlType": "", "remark": ""}, "18": {"name": "UNLOAD_READY1", "ioCode": "SDO18", "mcuIo": "SDO_7", "ioType": "DO", "controlType": "", "remark": ""}, "19": {"name": "LOAD_READY2", "ioCode": "SDO19", "mcuIo": "SDO_8", "ioType": "DO", "controlType": "", "remark": ""}, "20": {"name": "UNLOAD_READY2", "ioCode": "SDO20", "mcuIo": "SDO_9", "ioType": "DO", "controlType": "", "remark": ""}, "21": {"name": "紫色，蜂鸣器", "ioCode": "SDO21", "mcuIo": "SDO_10", "ioType": "DO", "controlType": "", "remark": ""}, "22": {"name": "蓝色，LED蓝", "ioCode": "SDO22", "mcuIo": "SDO_11", "ioType": "DO", "controlType": "", "remark": ""}, "23": {"name": "绿色，LED绿", "ioCode": "SDO23", "mcuIo": "SDO_12", "ioType": "DO", "controlType": "", "remark": ""}, "24": {"name": "橙色，LED橙", "ioCode": "SDO24", "mcuIo": "SDO_13", "ioType": "DO", "controlType": "", "remark": ""}, "25": {"name": "红色，LED红", "ioCode": "SDO25", "mcuIo": "SDO_14", "ioType": "DO", "controlType": "", "remark": ""}}}, "Robot": {"DiNames": {"1": {"name": "Paddle sensor 1 left", "ioCode": "RDI1", "mcuIo": "SDI_26/RDI_26", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "no wafer:0 wafer:1"}, "2": {"name": "Paddle sensor 2 right", "ioCode": "RDI2", "mcuIo": "SDI_27/RDI_25", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "no wafer:0 wafer:1"}, "3": {"name": "pin search 1", "ioCode": "RDI3", "mcuIo": "RDI_31", "ioType": "DI", "sensorType": "switch", "remark": "detect:0 no detect:1"}, "4": {"name": "pin search 2", "ioCode": "RDI4", "mcuIo": "RDI_32", "ioType": "DI", "sensorType": "switch", "remark": "detect:0 no detect:1"}}, "DONames": {}}, "ChamberA": {"DiNames": {"1": {"name": "CV open sensor", "ioCode": "PDI1", "mcuIo": "PDI_9", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "2": {"name": "CV close sensor", "ioCode": "PDI2", "mcuIo": "PDI_8", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "3": {"name": "T/V open sensor", "ioCode": "PDI3", "mcuIo": "PDI_12", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "到位:0"}, "4": {"name": "T/V close sensor", "ioCode": "PDI4", "mcuIo": "PDI_13", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "到位:0"}, "5": {"name": "foreline switch", "ioCode": "PDI5", "mcuIo": "PDI_11", "ioType": "DI", "sensorType": "switch", "remark": "vacuum:1 no vacuum:0"}, "6": {"name": "Torr switch", "ioCode": "PDI6", "mcuIo": "PDI_10", "ioType": "DI", "sensorType": "switch", "remark": "vacuum:1 no vacuum:0"}, "7": {"name": "plasma sensor 1", "ioCode": "PDI7", "mcuIo": "PDI_1", "ioType": "DI", "sensorType": "NPN", "remark": "plasma on:0 plasma off:1"}, "8": {"name": "plasma sensor 2", "ioCode": "PDI8", "mcuIo": "PDI_2", "ioType": "DI", "sensorType": "NPN", "remark": "plasma on:0 plasma off:1"}, "9": {"name": "matching interlock switch 1", "ioCode": "PDI9", "mcuIo": "PDI_15", "ioType": "DI", "sensorType": "switch", "remark": "spare"}, "10": {"name": "matching interlock switch 2", "ioCode": "PDI10", "mcuIo": "PDI_16", "ioType": "DI", "sensorType": "switch", "remark": "spare"}, "11": {"name": "thermostate", "ioCode": "PDI11", "mcuIo": "PDI_3", "ioType": "DI", "sensorType": "switch", "remark": "over temp:0 temp normal:1"}, "12": {"name": "slit door open sensor", "ioCode": "PDI12", "mcuIo": "PDI_5", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "13": {"name": "slit door close sensor", "ioCode": "PDI13", "mcuIo": "PDI_4", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "14": {"name": "lift pin up sensor", "ioCode": "PDI14", "mcuIo": "PDI_6", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "15": {"name": "lift pin down sensor", "ioCode": "PDI15", "mcuIo": "PDI_7", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}}, "DONames": {"1": {"name": "T/V open", "ioCode": "PDO1", "mcuIo": "PDO_1", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "2": {"name": "T/V close", "ioCode": "PDO2", "mcuIo": "PDO_2", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "3": {"name": "T/V FRZ", "ioCode": "PDO3", "mcuIo": "PDO_3", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "4": {"name": "CV control", "ioCode": "PDO4", "mcuIo": "PEVDO_1", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "5": {"name": "GAS C1 control", "ioCode": "PDO5", "mcuIo": "PEVDO_2", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "6": {"name": "GAS C2 control", "ioCode": "PDO6", "mcuIo": "PEVDO_3", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "7": {"name": "GAS C3 control", "ioCode": "PDO7", "mcuIo": "PEVDO_4", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "8": {"name": "GAS C4 control", "ioCode": "PDO8", "mcuIo": "PEVDO_5", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "9": {"name": "GAS CM valve control", "ioCode": "PDO9", "mcuIo": "PEVDO_6", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "10": {"name": "slit door open", "ioCode": "PDO10", "mcuIo": "PEVDO_7", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "11": {"name": "slit door close", "ioCode": "PDO11", "mcuIo": "PEVDO_8", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "12": {"name": "lift pin up", "ioCode": "PDO12", "mcuIo": "PEVDO_9", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "13": {"name": "lift pin down", "ioCode": "PDO13", "mcuIo": "PEVDO_10", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "14": {"name": "RF1 enable", "ioCode": "PDO14", "mcuIo": "PDO_14", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "15": {"name": "RF2 enable", "ioCode": "PDO15", "mcuIo": "PDO_15", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "16": {"name": "heater enable", "ioCode": "PDO16", "mcuIo": "PDO_4", "ioType": "DO", "controlType": "", "remark": "enable:0"}}}, "ChamberB": {"DiNames": {"1": {"name": "CV open sensor", "ioCode": "PDI1", "mcuIo": "PDI_9", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "2": {"name": "CV close sensor", "ioCode": "PDI2", "mcuIo": "PDI_8", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "3": {"name": "T/V open sensor", "ioCode": "PDI3", "mcuIo": "PDI_12", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "到位:0"}, "4": {"name": "T/V close sensor", "ioCode": "PDI4", "mcuIo": "PDI_13", "ioType": "DI", "sensorType": "optic sensor NPN", "remark": "到位:0"}, "5": {"name": "foreline switch", "ioCode": "PDI5", "mcuIo": "PDI_11", "ioType": "DI", "sensorType": "switch", "remark": "vacuum:1 no vacuum:0"}, "6": {"name": "Torr switch", "ioCode": "PDI6", "mcuIo": "PDI_10", "ioType": "DI", "sensorType": "switch", "remark": "vacuum:1 no vacuum:0"}, "7": {"name": "plasma sensor 1", "ioCode": "PDI7", "mcuIo": "PDI_1", "ioType": "DI", "sensorType": "NPN", "remark": "plasma on:0 plasma off:1"}, "8": {"name": "plasma sensor 2", "ioCode": "PDI8", "mcuIo": "PDI_2", "ioType": "DI", "sensorType": "NPN", "remark": "plasma on:0 plasma off:1"}, "9": {"name": "matching interlock switch 1", "ioCode": "PDI9", "mcuIo": "PDI_15", "ioType": "DI", "sensorType": "switch", "remark": "spare"}, "10": {"name": "matching interlock switch 2", "ioCode": "PDI10", "mcuIo": "PDI_16", "ioType": "DI", "sensorType": "switch", "remark": "spare"}, "11": {"name": "thermostate", "ioCode": "PDI11", "mcuIo": "PDI_3", "ioType": "DI", "sensorType": "switch", "remark": "over temp:0 temp normal:1"}, "12": {"name": "slit door open sensor", "ioCode": "PDI12", "mcuIo": "PDI_5", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "13": {"name": "slit door close sensor", "ioCode": "PDI13", "mcuIo": "PDI_4", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "14": {"name": "lift pin up sensor", "ioCode": "PDI14", "mcuIo": "PDI_6", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}, "15": {"name": "lift pin down sensor", "ioCode": "PDI15", "mcuIo": "PDI_7", "ioType": "DI", "sensorType": "接近传感器NPN", "remark": "到位:0"}}, "DONames": {"1": {"name": "T/V open", "ioCode": "PDO1", "mcuIo": "PDO_1", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "2": {"name": "T/V close", "ioCode": "PDO2", "mcuIo": "PDO_2", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "3": {"name": "T/V FRZ", "ioCode": "PDO3", "mcuIo": "PDO_3", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "4": {"name": "CV control", "ioCode": "PDO4", "mcuIo": "PEVDO_1", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "5": {"name": "GAS C1 control", "ioCode": "PDO5", "mcuIo": "PEVDO_2", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "6": {"name": "GAS C2 control", "ioCode": "PDO6", "mcuIo": "PEVDO_3", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "7": {"name": "GAS C3 control", "ioCode": "PDO7", "mcuIo": "PEVDO_4", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "8": {"name": "GAS C4 control", "ioCode": "PDO8", "mcuIo": "PEVDO_5", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "9": {"name": "GAS CM valve control", "ioCode": "PDO9", "mcuIo": "PEVDO_6", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "10": {"name": "slit door open", "ioCode": "PDO10", "mcuIo": "PEVDO_7", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "11": {"name": "slit door close", "ioCode": "PDO11", "mcuIo": "PEVDO_8", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "12": {"name": "lift pin up", "ioCode": "PDO12", "mcuIo": "PEVDO_9", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "13": {"name": "lift pin down", "ioCode": "PDO13", "mcuIo": "PEVDO_10", "ioType": "DO", "controlType": "EV", "remark": "enable:0"}, "14": {"name": "RF1 enable", "ioCode": "PDO14", "mcuIo": "PDO_14", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "15": {"name": "RF2 enable", "ioCode": "PDO15", "mcuIo": "PDO_15", "ioType": "DO", "controlType": "", "remark": "enable:0"}, "16": {"name": "heater enable", "ioCode": "PDO16", "mcuIo": "PDO_4", "ioType": "DO", "controlType": "", "remark": "enable:0"}}}}