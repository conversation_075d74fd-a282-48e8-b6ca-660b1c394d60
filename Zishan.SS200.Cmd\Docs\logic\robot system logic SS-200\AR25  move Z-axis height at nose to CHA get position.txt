﻿AR25
Move Z-axis height at nose to CHA get
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			slide out sensor installation review
SPS11
				SPS11=Y
					slide out sensor status reiew
						DI19=0 DI20=0
							T-axis position status review
(RS5 or others position)
								RS5
									chamber pressure review
RPS29
										Y
											loadlock pressure review
SP13 or SP14
												SP13
loadlock vacuum
													AR39-(RP23-RPS16)
														slide out sensor status reiew
															DI19=0 DI20=0
																command done
															DI19=0 DI20=1
																RA20 ALARM
															DI19=1 DI20=0
																RA19 ALARM
															DI19=1 DI20=1
																RA21 ALARM
												SP14
loadlock no vacuum
													AR39-RP23
														slide out sensor status reiew
															DI19=0 DI20=0
																command done
															DI19=0 DI20=1
																RA20 ALARM
															DI19=1 DI20=0
																RA19 ALARM
															DI19=1 DI20=1
																RA21 ALARM
										N
											AR39-RP23
												slide out sensor status reiew
													DI19=0 DI20=0
														command done
													DI19=0 DI20=1
														RA20 ALARM
													DI19=1 DI20=0
														RA19 ALARM
													DI19=1 DI20=1
														RA21 ALARM
								others position
									RA12 ALARM
						DI19=0 DI20=1
							RA20 ALARM
						DI19=1 DI20=0
							RA19 ALARM
						DI19=1 DI20=1
							RA19 ALARM
				SPS11=N
					T-axis position status review
(RS5 or others position)
						RS5
							chamber pressure review
RPS29
								Y
									loadlock pressure review
SP13 or SP14
										SP13
loadlock vacuum
											AR39-(RP23-RPS16)
												command done
										SP14
loadlock no vacuum
											AR39-RP23
												command done
								N
									AR39-RP23
										command done
						others position
							RA12 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM