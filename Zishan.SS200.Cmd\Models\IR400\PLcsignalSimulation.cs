﻿using System;
using System.Collections.Generic;
using Prism.Mvvm;
using System.ComponentModel;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Config;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// PLC信号模拟类
    /// </summary>
    public class PLcsignalSimulation : BindableBase
    {
        #region 字段

        /// <summary>
        /// 循环检查自动点击
        /// </summary>
        public bool IsLoopCheckAutoClick;

        #endregion 字段

        #region 属性

        /// <summary>
        /// 搬运状态信号CheckBox是否可以修改，默认：False
        /// </summary>
        public bool CarryStatusCheckBoxEnable { get => _CarryStatusCheckBoxEnable; set => SetProperty(ref _CarryStatusCheckBoxEnable, value); }
        private bool _CarryStatusCheckBoxEnable;

        /// <summary>
        /// North面是否有晶圆
        /// </summary>
        [Description("North面是否有晶圆")]
        public bool RobotNorthHaseWafer { get => _RobotNorthHaseWafer; set => SetProperty(ref _RobotNorthHaseWafer, value); }
        private bool _RobotNorthHaseWafer;

        /// <summary>
        /// Smooth面是否有晶圆
        /// </summary>
        [Description("Smooth面是否有晶圆")]
        public bool RobotSmothHaseWafer { get => _RobotSmothHaseWafer; set => SetProperty(ref _RobotSmothHaseWafer, value); }
        private bool _RobotSmothHaseWafer;

        /// <summary>
        /// CHA是否有晶圆
        /// </summary>
        [Description("CHA是否有晶圆")]
        public bool ChaHasWafer { get => _ChaHasWafer; set => SetProperty(ref _ChaHasWafer, value); }
        private bool _ChaHasWafer;

        /// <summary>
        /// CHB是否有晶圆
        /// </summary>
        [Description("CHB是否有晶圆")]
        public bool ChbHasWafer { get => _ChbHasWafer; set => SetProperty(ref _ChbHasWafer, value); }
        private bool _ChbHasWafer;

        /// <summary>
        /// CHC是否有晶圆
        /// </summary>
        [Description("CHC是否有晶圆")]
        public bool ChcHasWafer { get => _ChcHasWafer; set => SetProperty(ref _ChcHasWafer, value); }
        private bool _ChcHasWafer;

        /// <summary>
        /// Cooling是否有晶圆
        /// </summary>
        [Description("Cooling是否有晶圆")]
        public bool CoolingHasWafer { get => _CoolingHasWafer; set => SetProperty(ref _CoolingHasWafer, value); }
        private bool _CoolingHasWafer;

        /// <summary>
        /// CHA 处理完成
        /// </summary>
        [Description("CHA 处理完成")]
        public bool? ChaProcessFinished
        {
            get => _ChaProcessFinished;
            set
            {
                if (SetProperty(ref _ChaProcessFinished, value) && value == true)
                {
                    ChaProcessFinishedTrigger = true;
                    ChaDateTimeProcessFinished = DateTime.Now;
                }
            }
        }
        private bool? _ChaProcessFinished;

        /// <summary>
        /// CHA 处理完成 触发信号，用完重置False
        /// </summary>
        public bool ChaProcessFinishedTrigger { get; set; }

        /// <summary>
        /// CHA 处理完成 时间点
        /// </summary>
        public DateTime ChaDateTimeProcessFinished { get => _ChaDateTimeProcessFinished; set => SetProperty(ref _ChaDateTimeProcessFinished, value); }
        private DateTime _ChaDateTimeProcessFinished;

        /// <summary>
        /// CHA 是否允许触发处理完成信号
        /// </summary>
        public bool EnableChaProcessFinished
        {
            get { return _EnableChaProcessFinished; }
            set { SetProperty(ref _EnableChaProcessFinished, value); }
        }
        private bool _EnableChaProcessFinished;

        /// <summary>
        /// CHB 处理完成
        /// </summary>
        [Description("CHB 处理完成")]
        public bool? ChbProcessFinished
        {
            get => _ChbProcessFinished;
            set
            {
                if (SetProperty(ref _ChbProcessFinished, value) && value == true)
                {
                    ChbProcessFinishedTrigger = true;
                    ChbDateTimeProcessFinished = DateTime.Now;
                }
            }
        }
        private bool? _ChbProcessFinished;

        /// <summary>
        /// CHB 处理完成 触发信号，用完重置False
        /// </summary>
        public bool ChbProcessFinishedTrigger { get; set; }

        /// <summary>
        /// CHB 处理完成 时间点
        /// </summary>
        public DateTime ChbDateTimeProcessFinished { get => _ChbDateTimeProcessFinished; set => SetProperty(ref _ChbDateTimeProcessFinished, value); }
        private DateTime _ChbDateTimeProcessFinished;

        /// <summary>
        /// CHB 是否允许触发处理完成信号
        /// </summary>
        public bool EnableChbProcessFinished
        {
            get { return _EnableChbProcessFinished; }
            set { SetProperty(ref _EnableChbProcessFinished, value); }
        }
        private bool _EnableChbProcessFinished;

        /// <summary>
        /// CHC 处理完成
        /// </summary>
        [Description("CHC 处理完成")]
        public bool? ChcProcessFinished
        {
            get => _ChcProcessFinished;
            set
            {
                if (SetProperty(ref _ChcProcessFinished, value) && value == true)
                {
                    ChcProcessFinishedTrigger = true;
                    ChcDateTimeProcessFinished = DateTime.Now;
                }
            }
        }
        private bool? _ChcProcessFinished;

        /// <summary>
        /// CHC 处理完成 触发信号，用完重置False
        /// </summary>
        public bool ChcProcessFinishedTrigger { get; set; }

        /// <summary>
        /// CHC 处理完成 时间点
        /// </summary>
        public DateTime ChcDateTimeProcessFinished { get => _ChcDateTimeProcessFinished; set => SetProperty(ref _ChcDateTimeProcessFinished, value); }
        private DateTime _ChcDateTimeProcessFinished;

        /// <summary>
        /// CHC 是否允许触发处理完成信号
        /// </summary>
        public bool EnableChcProcessFinished
        {
            get { return _EnableChcProcessFinished; }
            set { SetProperty(ref _EnableChcProcessFinished, value); }
        }
        private bool _EnableChcProcessFinished;

        /// <summary>
        /// Cooling 处理完成
        /// </summary>
        [Description("Cooling 处理完成")]
        public bool CoolingProcessFinished
        {
            get => _CoolingProcessFinished;
            set
            {
                if (SetProperty(ref _CoolingProcessFinished, value) && value)
                {
                    CoolingProcessFinishedTrigger = true;
                }
            }
        }

        private bool _CoolingProcessFinished;

        /// <summary>
        /// Cooling 处理完成 触发信号，用完重置False
        /// </summary>
        public bool CoolingProcessFinishedTrigger { get; set; }

        /// North面晶圆槽位
        /// </summary>
        public int SlotNorth { get => _SlotNorth; set => SetProperty(ref _SlotNorth, value); }
        private int _SlotNorth;

        /// <summary>
        /// Smooth面晶圆槽位
        /// </summary>
        public int SlotSmooth { get => _SlotSmooth; set => SetProperty(ref _SlotSmooth, value); }
        private int _SlotSmooth;

        /// <summary>
        /// CHA晶圆槽位
        /// </summary>
        public int SlotCha { get => _SlotCha; set => SetProperty(ref _SlotCha, value); }
        private int _SlotCha;

        /// <summary>
        /// CHB晶圆槽位
        /// </summary>
        public int SlotChb { get => _SlotChb; set => SetProperty(ref _SlotChb, value); }
        private int _SlotChb;

        /// <summary>
        /// CHC晶圆槽位
        /// </summary>
        public int SlotChc { get => _SlotChc; set => SetProperty(ref _SlotChc, value); }
        private int _SlotChc;

        /// <summary>
        /// Cooling晶圆槽位
        /// </summary>
        public int SlotCooling { get => _SlotCooling; set => SetProperty(ref _SlotCooling, value); }
        private int _SlotCooling;

        /// <summary>
        /// 是否启用自动点击
        /// </summary>
        [Description("是否启用自动点击")]
        public bool IsAutoClick { get => _IsAutoClick; set => SetProperty(ref _IsAutoClick, value); }
        private bool _IsAutoClick = true;

        #endregion 属性

        #region 构造函数

        public PLcsignalSimulation()
        {
            ChaProcessFinished = null;
            ChbProcessFinished = null;
            ChcProcessFinished = null;
        }

        #endregion 构造函数

        #region 方法

        /// <summary>
        /// 获取当前有晶圆且已完成处理的腔体名称（优先返回有晶圆的腔体）
        /// </summary>
        /// <param name="recipeFileName">配方文件名</param>
        /// <returns>有晶圆且已完成处理的腔体名称</returns>
        public EnuChamberName GetFinishedChamberWithWafer(string recipeFileName)
        {
            // 如果是ABC配方，判断哪些腔体启用
            bool isEnableCha = recipeFileName.Contains("A");
            bool isEnableChb = recipeFileName.Contains("B");
            bool isEnableChc = recipeFileName.Contains("C");

            // 优先检查有晶圆且已完成的腔体
            if (isEnableCha && ChaHasWafer && (ChaProcessFinished ?? false))
            {
                return EnuChamberName.CHA;
            }

            if (isEnableChb && ChbHasWafer && (ChbProcessFinished ?? false))
            {
                return EnuChamberName.CHB;
            }

            if (isEnableChc && ChcHasWafer && (ChcProcessFinished ?? false))
            {
                return EnuChamberName.CHC;
            }

            // 如果没有找到有晶圆且已完成的腔体，返回默认值
            return EnuChamberName.LoadLock;
        }

        /// <summary>
        /// 设置当前各腔体界面上是否有未处理的Wafer
        /// </summary>
        /// <returns></returns>
        public void SetCurrentUIWaferProcess(bool chaHasWafer, int slotCha, bool chbHasWafer, int slotChb, bool chcHasWafer, int slotChc, bool coolingHasWafer, int slotcooling, bool robotNorthHaseWafer, int slotNose, bool robotSmothHaseWafer, int slotSmooth)
        {
            ChaHasWafer = chaHasWafer;
            SlotCha = slotCha;
            ChbHasWafer = chbHasWafer;
            SlotChb = slotChb;
            ChcHasWafer = chcHasWafer;
            SlotChc = slotChc;
            CoolingHasWafer = coolingHasWafer;
            SlotCooling = slotcooling;
            RobotNorthHaseWafer = robotNorthHaseWafer;
            SlotNorth = slotNose;
            RobotSmothHaseWafer = robotSmothHaseWafer;
            SlotSmooth = slotSmooth;
        }

        /// <summary>
        /// 根据配方名判断设备是否还有处理的Wafer
        /// </summary>
        /// <param name="recipeName"></param>
        /// <returns></returns>
        public bool CheckIsRemainWaferProcess(string recipeName)
        {
            bool blResult = false;

            var chkCha = recipeName.Contains("A");
            var chkChb = recipeName.Contains("B");
            var chkChc = recipeName.Contains("C");

            blResult = (chkCha && ChaHasWafer) || (chkChb && ChbHasWafer) || (chkChc && ChcHasWafer) || RobotNorthHaseWafer || RobotSmothHaseWafer;

            return blResult;
        }

        /// <summary>
        /// 是否允许手动触发处理完成信号
        /// </summary>
        public void CheckCanProcessFinished(bool IsIdle)
        {
            if (IsIdle)
            {
                if (ChaHasWafer)
                {
                    EnableChaProcessFinished = true;
                }
                if (ChbHasWafer)
                {
                    EnableChbProcessFinished = true;
                }
                if (ChcHasWafer)
                {
                    EnableChcProcessFinished = true;
                }
            }
            else
            {
                EnableChaProcessFinished = false;
                EnableChbProcessFinished = false;
                EnableChcProcessFinished = false;
            }
        }

        //private string nextCh = "CHA";

        ///// <summary>
        ///// 自动点击腔体工艺做完
        ///// </summary>
        ///// <param name="isCheck"></param>
        ///// <returns></returns>
        //public async Task AutoClickProcessFinished_old(bool isLoopCheckAutoClick = true)
        //{
        //    IsLoopCheckAutoClick = isLoopCheckAutoClick;
        //    while (IsLoopCheckAutoClick)
        //    {
        //        while (IsAutoClick)
        //        {
        //            await Task.Delay(1000);
        //            if (nextCh == "CHA")
        //            {
        //                if (EnableChaProcessFinished)
        //                {
        //                    ChaProcessFinished = true;
        //                    nextCh = "CHB";
        //                }
        //            }
        //            else if (nextCh == "CHB")
        //            {
        //                if (EnableChbProcessFinished)
        //                {
        //                    ChbProcessFinished = true;
        //                    nextCh = "CHC";
        //                }
        //            }
        //            else if (nextCh == "CHC")
        //            {
        //                if (EnableChcProcessFinished)
        //                {
        //                    ChcProcessFinished = true;
        //                    nextCh = "CHA";
        //                }
        //            }
        //        }
        //        await Task.Delay(3000);
        //    }
        //}

        /// <summary>
        /// 自动点击腔体工艺做完,使用循环队列
        /// </summary>
        /// <param name="isLoopCheckAutoClick"></param>
        /// <returns></returns>
        public async Task AutoClickProcessFinished(Queue<string> processQueue, bool isLoopCheckAutoClick = true)
        {
            IsLoopCheckAutoClick = isLoopCheckAutoClick;
            //Queue<string> processQueue = new Queue<string>(new[] { "CHA", "CHB", "CHC" });

            while (IsLoopCheckAutoClick)
            {
                int retryCount = 5;
                while (IsLoopCheckAutoClick && IsAutoClick)
                {
                    await Task.Delay(DelayConfig.PLCAutoClickDelay);
                    string currentProcess = processQueue.Peek();

                    bool processFinished = false;

                    switch (currentProcess)
                    {
                        case "CHA":
                            if (EnableChaProcessFinished)
                            {
                                ChaProcessFinished = true;
                                processFinished = true;
                            }
                            break;

                        case "CHB":
                            if (EnableChbProcessFinished)
                            {
                                ChbProcessFinished = true;
                                processFinished = true;
                            }
                            break;

                        case "CHC":
                            if (EnableChcProcessFinished)
                            {
                                ChcProcessFinished = true;
                                processFinished = true;
                            }
                            break;
                    }

                    //如果处理完成，将当前处理的腔体移动到队列末尾
                    if (processFinished)
                    {
                        processQueue.Dequeue();
                        processQueue.Enqueue(currentProcess);
                        retryCount = 5;
                    }
                    else
                    {
                        if (retryCount-- < 0)
                        {
                            var trueCount = 0;
                            if (EnableChaProcessFinished)
                            {
                                trueCount++;
                            }
                            if (EnableChbProcessFinished)
                            {
                                trueCount++;
                            }
                            if (EnableChcProcessFinished)
                            {
                                trueCount++;
                            }

                            if (trueCount > 0)
                            {
                                processQueue.Dequeue();
                                processQueue.Enqueue(currentProcess);
                            }
                            else
                            {
                                retryCount = 0;
                            }
                        }
                    }
                }
                await Task.Delay(DelayConfig.StatusCheckDelay);
            }
        }

        /// <summary>
        ///  PLC信号模拟 重置
        /// </summary>
        public void Reset()
        {
            RobotSmothHaseWafer = false;
            RobotNorthHaseWafer = false;
            ChaHasWafer = false;
            ChbHasWafer = false;
            ChcHasWafer = false;
            CoolingHasWafer = false;
            ChaProcessFinished = null;
            ChbProcessFinished = null;
            ChcProcessFinished = true;

            EnableChaProcessFinished = false;
            EnableChbProcessFinished = false;
            EnableChcProcessFinished = false;
        }

        #endregion 方法
    }
}