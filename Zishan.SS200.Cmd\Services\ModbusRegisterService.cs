using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models;
using System.Linq;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Services
{
    public class ModbusRegisterService
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ModbusRegisterService));
        private readonly Dictionary<int, ModbusRegister> _registers;
        private readonly Dictionary<int, ModbusCoil> _coils;

        public ModbusRegisterService()
        {
            _coils = new Dictionary<int, ModbusCoil>();
            _registers = new Dictionary<int, ModbusRegister>();

            InitializeRegisters();
            _logger.Info("Modbus寄存器服务已初始化");
        }

        private void InitializeRegisters()
        {
            // 初始化线圈
            InitializeCoils();

            // 初始化输入寄存器
            InitializeInputRegisters();

            // 初始化保持寄存器
            InitializeHoldingRegisters();
        }

        private void InitializeCoils()
        {
            // 系统状态
            AddCoil(2000, "ResponseSend", "信息回复标志", "读取_信息回复标志", "GVL_ModbusTest.ResponseSend", false);
            AddCoil(2001, "D_CMD_Executed", "命令已执行", "显示_命令_已执行（Busy Or Done）。用于告知客户端：“服务端已收到CMD_Enable，并且在执行中或者已完成，请客户端复位CMD_Enable”", "GVL_ModbusTest.D_CMD_Executed", false);
            AddCoil(2002, "D_CMD_CheckCode", "命令校验码状态", "显示_命令_校验码。用于对齐通讯服务端和客户端的信号，当服务端和客户端的校验码一致时才继续发送命令和命令触发。", "GVL_ModbusTest.D_CMD_CheckCode", false);
            AddCoil(2003, "CMD_Enable", "发送命令触发", "写入_发送命令触发", "GVL_ModbusTest.CMD_Enable", true);
            AddCoil(2004, "CMD_CheckCode", "发送命令校验码", "写入_命令_校验码。用于对齐通讯服务端和客户端的信号，当服务端和客户端的校验码一致时才继续发送命令和命令触发。", "GVL_ModbusTest.CMD_CheckCode", false);

            // 设定按钮
            AddCoil(2010, "SetCoordinate_SetButton", "设定单元坐标", "写入_设定单元坐标参数_设定按钮", "GVL_ModbusTest.SetCoordinate_SetButton", true);
            AddCoil(2011, "Dynamic_SetButton", "动态参数设定", "写入_动态参数_设定按钮", "GVL_ModbusTest.Dynamic_SetButton", true);
            AddCoil(2012, "Base_SetButton", "基本参数设定", "写入_基本参数_设定按钮", "GVL_ModbusTest.Base_SetButton", true);
            AddCoil(2013, "SetCoordinate_CassetteUnit_SetButton", "花篮单元设定", "写入按钮_花篮单元（大气环境下的坐标）_设定按钮", "GVL_ModbusTest.SetCoordinate_CassetteUnit_SetButton", true);

            // 状态信息
            AddCoil(2040, "D_TeachConnected", "示教器已连接", "读取_TeachConnected 示教器已连接", "GVL_ModbusTest.D_TeachConnected", false);
            AddCoil(2041, "D_RobotPowerStatus", "电机使能状态", "读取_RobotPowerStatus Power使能状态", "GVL_ModbusTest.D_RobotPowerStatus", false);
            AddCoil(2042, "D_OperationReleased", "机器人运行允许", "读取_OperationReleased Robot允许操作（运行）", "GVL_ModbusTest.D_OperationReleased", false);
            AddCoil(2043, "D_RobotOPMode", "机器人操作模式", "读取_RobotOPMode Robot操作模式。0:关闭 1：自动 2：手动慢速 3：手动全速 4:自动外部控制 5：特殊模式1，多用于安全集成 6：特殊模式2，多用于安全集成", "GVL_ModbusTest.D_RobotOPMode", false);

            // 控制命令
            AddCoil(2050, "RobotPowerEnable", "电机使能控制", "写入_RobotPower使能（电机使能）", "GVL_ModbusTest.RobotPowerEnable", true);
            AddCoil(2051, "RobotOperationEnable", "操作使能控制", "写入_Robot操作使能(允许运行)", "GVL_ModbusTest.RobotOperationEnable", true);
            AddCoil(2052, "RobotResetExecute", "故障复位", "写入_Robot复位（确认并复位故障信息）", "GVL_ModbusTest.RobotResetExecute", true);
            AddCoil(2053, "RobotInterruptExecute", "程序中断", "写入_Robot中断程序运行", "GVL_ModbusTest.RobotInterruptExecute", true);
            AddCoil(2054, "RobotContinueExecute", "程序继续", "写入_Robot继续程序运行", "GVL_ModbusTest.RobotContinueExecute", true);

            // 大气真空切换
            AddCoil(2055, "Switch_Air_Vacuum", "切换大气真空", "写入_切换开关_大气/真空", "GVL_ModbusTest.Switch_Air_Vacuum", true);
        }

        private void InitializeInputRegisters()
        {
            // 当前坐标
            AddRegisterPair(1, "D_rPos_A1", "升降轴当前坐标", "显示_当前坐标_轴_A1（升降轴）",
                "GVL_ModbusTest.D_rPos_A1.Union_WORD[0], GVL_ModbusTest.D_rPos_A1.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(3, "D_rPos_A2", "旋转轴当前坐标", "显示_当前坐标_轴_A2（旋转轴）",
                "GVL_ModbusTest.D_rPos_A2.Union_WORD[0], GVL_ModbusTest.D_rPos_A2.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(5, "D_rPos_A3", "伸缩轴当前坐标", "显示_当前坐标_轴_A3（伸缩轴）",
                "GVL_ModbusTest.D_rPos_A3.Union_WORD[0], GVL_ModbusTest.D_rPos_A3.Union_WORD[1]",
                ModbusRegisterType.Input);

            // 单元位置
            AddRegisterPair(10, "D_rUnitPos_A1", "升降轴单元位置", "显示_（当前选择的）单元位置_A1（升降轴）",
                "GVL_ModbusTest.D_rUnitPos_A1.Union_WORD[0], GVL_ModbusTest.D_rUnitPos_A1.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(12, "D_rUnitPos_A2", "旋转轴单元位置", "显示_（当前选择的）单元位置_A2（旋转轴）",
                "GVL_ModbusTest.D_rUnitPos_A2.Union_WORD[0], GVL_ModbusTest.D_rUnitPos_A2.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(14, "D_rUnitPos_A3", "伸缩轴单元位置", "显示_（当前选择的）单元位置_A3（伸缩轴）",
                "GVL_ModbusTest.D_rUnitPos_A3.Union_WORD[0], GVL_ModbusTest.D_rUnitPos_A3.Union_WORD[1]",
                ModbusRegisterType.Input);

            // 速度和动态参数
            AddRegisterPair(20, "D_Override", "速度比率", "Robot速度比率设定。时间覆盖因子的新设定值（0.1 .. 100.0%）",
                "GVL_ModbusTest.D_Override.Union_WORD[0], GVL_ModbusTest.D_Override.Union_WORD[1]",
                ModbusRegisterType.Input);

            // 动态参数_Z轴
            AddRegisterPairInt(22, "D_Dynamic_Velocity", "动态参数_Z轴_速度", "动态参数_显示_Z轴_速度(0~100%)_Z轴",
                "GVL_ModbusTest.D_Dynamic_Velocity.Union_WORD[0], GVL_ModbusTest.D_Dynamic_Velocity.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(24, "D_Dynamic_Acc", "动态参数_Z轴_加速度", "动态参数_显示_Z轴_加速度(0~100%)_Z轴",
                "GVL_ModbusTest.D_Dynamic_Acc.Union_WORD[0], GVL_ModbusTest.D_Dynamic_Acc.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(26, "D_Dynamic_Dec", "动态参数_Z轴_减速度", "动态参数_显示_Z轴_减速度(0~100%)_Z轴",
                "GVL_ModbusTest.D_Dynamic_Dec.Union_WORD[0], GVL_ModbusTest.D_Dynamic_Dec.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(28, "D_Dynamic_Jerk", "动态参数_Z轴_加加速度", "动态参数_显示_Z轴_加加速度(0~100%)_Z轴",
                "GVL_ModbusTest.D_Dynamic_Jerk.Union_WORD[0], GVL_ModbusTest.D_Dynamic_Jerk.Union_WORD[1]",
                ModbusRegisterType.Input);

            // 基本参数
            AddRegisterPair(30, "D_Base_StartPos_R", "R轴复位起点位置", "基本参数_(复位)起点位置_R轴",
                "GVL_ModbusTest.D_Base_StartPos_R.Union_WORD[0], GVL_ModbusTest.D_Base_StartPos_R.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(32, "D_Base_StartPos_T", "T轴复位起点位置", "基本参数_(复位)起点位置_T轴",
                "GVL_ModbusTest.D_Base_StartPos_T.Union_WORD[0], GVL_ModbusTest.D_Base_StartPos_T.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(34, "D_Base_StartPos_Z", "Z轴复位起点位置", "基本参数_(复位)起点位置_Z轴",
                "GVL_ModbusTest.D_Base_StartPos_Z.Union_WORD[0], GVL_ModbusTest.D_Base_StartPos_Z.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(36, "D_Base_SpecificPos_R", "R轴特定运动位置", "基本参数_特定位置_R轴",
                "GVL_ModbusTest.D_Base_SpecificPos_R.Union_WORD[0], GVL_ModbusTest.D_Base_SpecificPos_R.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(38, "D_Base_SpecificPos_T", "T轴特定运动位置", "基本参数_特定位置_T轴",
                "GVL_ModbusTest.D_Base_SpecificPos_T.Union_WORD[0], GVL_ModbusTest.D_Base_SpecificPos_T.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPair(40, "D_Base_SpecificPos_Z", "Z轴特定运动位置", "基本参数_特定位置_Z轴（T轴运行需要Z轴先定位到指定位置）",
                "GVL_ModbusTest.D_Base_SpecificPos_Z.Union_WORD[0], GVL_ModbusTest.D_Base_SpecificPos_Z.Union_WORD[1]",
                ModbusRegisterType.Input);

            // 动态参数_T轴
            AddRegisterPairInt(50, "D_Dynamic_2_Velocity", "动态参数_T轴_速度", "动态参数_显示_T轴_速度(0~100%)_T轴",
                "GVL_ModbusTest.D_Dynamic_2_Velocity.Union_WORD[0], GVL_ModbusTest.D_Dynamic_2_Velocity.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(52, "D_Dynamic_2_Acc", "动态参数_T轴_加速度", "动态参数_显示_T轴_加速度(0~100%)_T轴",
                "GVL_ModbusTest.D_Dynamic_2_Acc.Union_WORD[0], GVL_ModbusTest.D_Dynamic_2_Acc.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(54, "D_Dynamic_2_Dec", "动态参数_T轴_减速度", "动态参数_显示_T轴_减速度(0~100%)_T轴",
                "GVL_ModbusTest.D_Dynamic_2_Dec.Union_WORD[0], GVL_ModbusTest.D_Dynamic_2_Dec.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(56, "D_Dynamic_2_Jerk", "动态参数_T轴_加加速度", "动态参数_显示_T轴_加加速度(0~100%)_T轴",
                "GVL_ModbusTest.D_Dynamic_2_Jerk.Union_WORD[0], GVL_ModbusTest.D_Dynamic_2_Jerk.Union_WORD[1]",
                ModbusRegisterType.Input);

            // 动态参数_R轴
            AddRegisterPairInt(58, "D_Dynamic_3_Velocity", "动态参数_R轴_速度", "动态参数_显示_R轴_速度(0~100%)_R轴",
                "GVL_ModbusTest.D_Dynamic_3_Velocity.Union_WORD[0], GVL_ModbusTest.D_Dynamic_3_Velocity.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(60, "D_Dynamic_3_Acc", "动态参数_R轴_加速度", "动态参数_显示_R轴_加速度(0~100%)_R轴",
                "GVL_ModbusTest.D_Dynamic_3_Acc.Union_WORD[0], GVL_ModbusTest.D_Dynamic_3_Acc.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(62, "D_Dynamic_3_Dec", "动态参数_R轴_减速度", "动态参数_显示_R轴_减速度(0~100%)_R轴",
                "GVL_ModbusTest.D_Dynamic_3_Dec.Union_WORD[0], GVL_ModbusTest.D_Dynamic_3_Dec.Union_WORD[1]",
                ModbusRegisterType.Input);
            AddRegisterPairInt(64, "D_Dynamic_3_Jerk", "动态参数_R轴_加加速度", "动态参数_显示_R轴_加加速度(0~100%)_R轴",
                "GVL_ModbusTest.D_Dynamic_3_Jerk.Union_WORD[0], GVL_ModbusTest.D_Dynamic_3_Jerk.Union_WORD[1]",
                ModbusRegisterType.Input);

            // 命令移动到读写地址：1300
            // AddRegisterString(100, count: 128, "GVL_ModbusTest.ResponseCache.Union_Word", "命令回复", "命令回复缓存区（String 255,128Word）",
            //     "GVL_ModbusTest.ResponseCache.Union_Word", ModbusRegisterType.Input);

            // 错误信息
            AddRegisterString(300, count: 128, "GVL_ModbusTest.D_LastErrorMassage", "报警信息", "报警信息（String 255,128Word）",
                 "D_LastErrorMassage", ModbusRegisterType.Input);
        }

        private void InitializeHoldingRegisters()
        {
            // 单元位置相关寄存器
            AddRegisterPair(1000, "rUnitPos_A1", "升降轴单元位置", "写入_（当前选择的）单元位置_A1（升降轴）",
                "GVL_ModbusTest.rUnitPos_A1.Union_WORD[0], GVL_ModbusTest.rUnitPos_A1.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1002, "rUnitPos_A2", "旋转轴单元位置", "写入_（当前选择的）单元位置_A2（旋转轴）",
                "GVL_ModbusTest.rUnitPos_A2.Union_WORD[0], GVL_ModbusTest.rUnitPos_A2.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1004, "rUnitPos_A3", "伸缩轴单元位置", "写入_（当前选择的）单元位置_A3（伸缩轴）",
                "GVL_ModbusTest.rUnitPos_A3.Union_WORD[0], GVL_ModbusTest.rUnitPos_A3.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 设定单元坐标参数
            AddRegister(1010, "SetCoordinate_Select_Arm", "手臂选择编号", "设定单元坐标参数_选择_手臂编号。0：A手 1：B手",
                "GVL.SetCoordinate_Select_Arm", ModbusRegisterType.Holding);
            AddRegister(1011, "SetCoordinate_Select_Unit", "单元选择编号", "设定单元坐标参数_选择_单元编号。0~99单元",
                "GVL.SetCoordinate_Select_Unit", ModbusRegisterType.Holding);
            AddRegister(1012, "SetCoordinate_Select_SubUnit", "子单元选择编号", "设定单元坐标参数_选择_子单元编号。0~30子单元",
                "GVL.SetCoordinate_Select_SubUnit", ModbusRegisterType.Holding);
            AddRegister(1013, "SetCoordinate_Select_MultiplePos", "多重位置选择编号",
                "设定单元坐标参数_选择_多重位置编号。[0]:基本位置 [1]:基本位置下方(取片起始位置,放片结束位置) [2]:基本位置上方(放片起始位置,取片结束位置)",
                "GVL.SetCoordinate_Select_MultiplePos", ModbusRegisterType.Holding);

            // 机器人速度设置
            AddRegisterPair(1020, "Override", "机器人速度比率", "Robot速度比率设定。时间覆盖因子的新设定值（0.1 .. 100.0%）",
                "GVL_ModbusTest.Override.Union_WORD[0], GVL_ModbusTest.Override.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 动态参数
            AddRegisterPairInt(1022, "Dynamic_Velocity", "动态参数_Z轴_速度", "动态参数_速度(0~100%)_Z轴",
                "GVL_ModbusTest.Dynamic_Velocity.Union_WORD[0], GVL_ModbusTest.Dynamic_Velocity.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1024, "Dynamic_Acc", "动态参数_Z轴_加速度", "动态参数_加速度(0~100%)_Z轴",
                "GVL_ModbusTest.Dynamic_Acc.Union_WORD[0], GVL_ModbusTest.Dynamic_Acc.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1026, "Dynamic_Dec", "动态参数_Z轴_减速度", "动态参数_减速度(0~100%)_Z轴",
                "GVL_ModbusTest.Dynamic_Dec.Union_WORD[0], GVL_ModbusTest.Dynamic_Dec.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1028, "Dynamic_Jerk", "动态参数_Z轴_加加速度", "动态参数_加加速度(0~100%)_Z轴",
                "GVL_ModbusTest.Dynamic_Jerk.Union_WORD[0], GVL_ModbusTest.Dynamic_Jerk.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 基本参数_(复位)起点位置
            AddRegisterPair(1030, "Base_StartPos_R", "R轴复位起点位置", "基本参数_(复位)起点位置_R轴",
                "GVL_ModbusTest.Base_StartPos_R.Union_WORD[0], GVL_ModbusTest.Base_StartPos_R.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1032, "Base_StartPos_T", "T轴复位起点位置", "基本参数_(复位)起点位置_T轴",
                "GVL_ModbusTest.Base_StartPos_T.Union_WORD[0], GVL_ModbusTest.Base_StartPos_T.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1034, "Base_StartPos_Z", "Z轴复位起点位置", "基本参数_(复位)起点位置_Z轴",
                "GVL_ModbusTest.Base_StartPos_Z.Union_WORD[0], GVL_ModbusTest.Base_StartPos_Z.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 基本参数_特定位置
            AddRegisterPair(1036, "Base_SpecificPos_R", "R轴特定运动位置", "基本参数_特定位置_R轴",
                "GVL_ModbusTest.Base_SpecificPos_R.Union_WORD[0], GVL_ModbusTest.Base_SpecificPos_R.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1038, "Base_SpecificPos_T", "T轴特定运动位置", "基本参数_特定位置_T轴",
                "GVL_ModbusTest.Base_SpecificPos_T.Union_WORD[0], GVL_ModbusTest.Base_SpecificPos_T.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1040, "Base_SpecificPos_Z", "Z轴特定运动位置", "基本参数_特定位置_Z轴（T轴运行需要Z轴先定位到指定位置）",
                "GVL_ModbusTest.Base_SpecificPos_Z.Union_WORD[0], GVL_ModbusTest.Base_SpecificPos_Z.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 真空位置偏差
            AddRegisterPair(1042, "PosOffSet", "真空大气位置偏差", "大气和真空之间的位置偏差。真空参数 := 大气参数 - 高度偏差",
                "GVL_ModbusTest.PosOffSet.Union_WORD[0], GVL_ModbusTest.PosOffSet.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 来自上位机的指令
            AddRegisterString(1100, count: 128, "sPcControlWord", "上位机控制指令", "(来自上位机的)指令（String 255,128Word）",
                "GVL_ModbusTest.sPcControlWord.Union_Word", ModbusRegisterType.Holding);

            // 动态参数_T轴
            AddRegisterPairInt(1050, "Dynamic_2_Velocity", "动态参数_T轴_速度", "动态参数_2_速度(0~100%)_T轴",
                "GVL_ModbusTest.Dynamic_2_Velocity.Union_WORD[0], GVL_ModbusTest.Dynamic_2_Velocity.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1052, "Dynamic_2_Acc", "动态参数_T轴_加速度", "动态参数_2_加速度(0~100%)_T轴",
                "GVL_ModbusTest.Dynamic_2_Acc.Union_WORD[0], GVL_ModbusTest.Dynamic_2_Acc.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1054, "Dynamic_2_Dec", "动态参数_T轴_减速度", "动态参数_2_减速度(0~100%)_T轴",
                "GVL_ModbusTest.Dynamic_2_Dec.Union_WORD[0], GVL_ModbusTest.Dynamic_2_Dec.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1056, "Dynamic_2_Jerk", "动态参数_T轴_加加速度", "动态参数_2_加加速度(0~100%)_T轴",
                "GVL_ModbusTest.Dynamic_2_Jerk.Union_WORD[0], GVL_ModbusTest.Dynamic_2_Jerk.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 动态参数_R轴
            AddRegisterPairInt(1058, "Dynamic_3_Velocity", "动态参数_R轴_速度", "动态参数_3_速度(0~100%)_R轴",
                "GVL_ModbusTest.Dynamic_3_Velocity.Union_WORD[0], GVL_ModbusTest.Dynamic_3_Velocity.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1060, "Dynamic_3_Acc", "动态参数_R轴_加速度", "动态参数_3_加速度(0~100%)_R轴",
                "GVL_ModbusTest.Dynamic_3_Acc.Union_WORD[0], GVL_ModbusTest.Dynamic_3_Acc.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1062, "Dynamic_3_Dec", "动态参数_R轴_减速度", "动态参数_3_减速度(0~100%)_R轴",
                "GVL_ModbusTest.Dynamic_3_Dec.Union_WORD[0], GVL_ModbusTest.Dynamic_3_Dec.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPairInt(1064, "Dynamic_3_Jerk", "动态参数_R轴_加加速度", "动态参数_3_加加速度(0~100%)_R轴",
                "GVL_ModbusTest.Dynamic_3_Jerk.Union_WORD[0], GVL_ModbusTest.Dynamic_3_Jerk.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 花篮单元坐标参数
            AddRegisterPair(1066, "SetCoordinate_CassetteUnit_Pos_R", "花篮单元(大气)_伸缩位置", "设定_花篮单元（大气环境下的坐标）_伸缩位置(R轴)",
                "GVL_ModbusTest.SetCoordinate_CassetteUnit_Pos_R.Union_WORD[0], GVL_ModbusTest.SetCoordinate_CassetteUnit_Pos_R.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1068, "SetCoordinate_CassetteUnit_Pos_T", "花篮单元(大气)_旋转位置", "设定_花篮单元（大气环境下的坐标）_旋转位置(T轴)",
                "GVL_ModbusTest.SetCoordinate_CassetteUnit_Pos_T.Union_WORD[0], GVL_ModbusTest.SetCoordinate_CassetteUnit_Pos_T.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1070, "SetCoordinate_CassetteUnit_Pos_Z_Slot1_Pos1", "花篮单元(大气)_升降_下方位置",
                "设定_花篮单元（大气环境下的坐标）_升降位置(Z轴)_首片(基本位置)下方位置的坐标。对于槽间距6.35mm的晶舟盒，建议数值：上方位置坐标 - 下方位置坐标 = 3.25mm",
                "GVL_ModbusTest.SetCoordinate_CassetteUnit_Pos_Z_Slot1_Pos1.Union_WORD[0], GVL_ModbusTest.SetCoordinate_CassetteUnit_Pos_Z_Slot1_Pos1.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1072, "SetCoordinate_CassetteUnit_Pos_Z_Slot1_Pos2", "花篮单元(大气)_升降_上方位置",
                "设定_花篮单元（大气环境下的坐标）_升降位置(Z轴)_首片(基本位置)上方位置的坐标。对于槽间距6.35mm的晶舟盒，建议数值：上方位置坐标 - 下方位置坐标 = 3.25mm",
                "GVL_ModbusTest.SetCoordinate_CassetteUnit_Pos_Z_Slot1_Pos2.Union_WORD[0], GVL_ModbusTest.SetCoordinate_CassetteUnit_Pos_Z_Slot1_Pos2.Union_WORD[1]",
                ModbusRegisterType.Holding);
            AddRegisterPair(1074, "SetCoordinate_CassetteUnit_SlotGap", "花篮单元(大气)_槽间距", "设定_花篮单元（大气环境下的坐标）_槽间距",
                "GVL_ModbusTest.SetCoordinate_CassetteUnit_SlotGap.Union_WORD[0], GVL_ModbusTest.SetCoordinate_CassetteUnit_SlotGap.Union_WORD[1]",
                ModbusRegisterType.Holding);

            // 命令回复缓存区
            AddRegisterString(100, count: 128, "ResponseCache", "命令执行回复", "命令回复缓存区（String 255,128Word）",
                "GVL_ModbusTest.ResponseCache.Union_Word", ModbusRegisterType.Holding);

            if (Golbal.IsDevDebug)
            {
                // 开发调试用 命令回复缓存区
                AddRegisterString(1300, count: 128, "ResponseCacheDebug", "命令执行回复(Debug)", "命令回复缓存区（String 255,128Word）",
        "GVL_ModbusTest.ResponseCache.Union_Word", ModbusRegisterType.Holding);
            }
        }

        private void AddCoil(int address, string name, string title, string description, string remark, bool isWriteable)
        {
            _coils[address] = new ModbusCoil
            {
                Address = (ushort)address,
                Name = name,
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = isWriteable
            };
        }

        /// <summary>
        /// 获取所有线圈
        /// </summary>
        public IEnumerable<ModbusCoil> GetAllCoils()
        {
            return _coils.Values;
        }

        private void AddRegister(int address, string name, string title, string description, string remark,
            ModbusRegisterType type, bool isWriteable = true)
        {
            _registers[address] = new ModbusRegister
            {
                Address = (ushort)address,
                Name = name,
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = isWriteable,
                RegisterType = type
            };
        }

        private void AddRegisterString(int startAddress, int count, string name, string title, string description, string remark,
            ModbusRegisterType type, bool isWriteable = true)
        {
            for (int i = startAddress; i < startAddress + count; i++)
            {
                _registers[i] = new ModbusRegister
                {
                    Address = (ushort)i,
                    Name = name,
                    Title = title,
                    Description = description,
                    Remark = remark,
                    IsWriteable = isWriteable,
                    RegisterType = type,
                    CombinevalueValueType = ModbusCombineValueType.String
                };
            }
        }

        /// <summary>
        /// 用于转换为32位CDAB Float型
        /// </summary>
        /// <param name="lowAddress"></param>
        /// <param name="name"></param>
        /// <param name="title"></param>
        /// <param name="description"></param>
        /// <param name="remark"></param>
        /// <param name="type"></param>
        private void AddRegisterPair(int lowAddress, string name, string title, string description, string remark,
            ModbusRegisterType type)
        {
            // 高字（存放在低地址）
            _registers[lowAddress] = new ModbusRegister
            {
                Address = (ushort)lowAddress,
                Name = $"{name}_High",
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = type == ModbusRegisterType.Holding,
                IsHighWord = true,
                LowRegisterAddress = lowAddress,
                HighRegisterAddress = lowAddress + 1,

                RegisterType = type,
                CombinevalueValueType = ModbusCombineValueType.Register32
            };

            // 低字（存放在高地址）
            _registers[lowAddress + 1] = new ModbusRegister
            {
                Address = (ushort)(lowAddress + 1),
                Name = $"{name}_Low",
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = type == ModbusRegisterType.Holding,
                IsHighWord = false,
                LowRegisterAddress = lowAddress,
                HighRegisterAddress = lowAddress + 1,

                RegisterType = type,
                CombinevalueValueType = ModbusCombineValueType.Register32
            };
        }

        /// <summary>
        /// 用于转换为32位CDAB Int型
        /// </summary>
        /// <param name="lowAddress"></param>
        /// <param name="name"></param>
        /// <param name="title"></param>
        /// <param name="description"></param>
        /// <param name="remark"></param>
        /// <param name="type"></param>
        private void AddRegisterPairInt(int lowAddress, string name, string title, string description, string remark,
            ModbusRegisterType type)
        {
            // 高字（存放在低地址）
            _registers[lowAddress] = new ModbusRegister
            {
                Address = (ushort)lowAddress,
                Name = $"{name}_High",
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = type == ModbusRegisterType.Holding,
                IsHighWord = true,
                LowRegisterAddress = lowAddress,
                HighRegisterAddress = lowAddress + 1,

                RegisterType = type,
                CombinevalueValueType = ModbusCombineValueType.Register32_Int
            };

            // 低字（存放在高地址）
            _registers[lowAddress + 1] = new ModbusRegister
            {
                Address = (ushort)(lowAddress + 1),
                Name = $"{name}_Low",
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = type == ModbusRegisterType.Holding,
                IsHighWord = false,
                LowRegisterAddress = lowAddress,
                HighRegisterAddress = lowAddress + 1,

                RegisterType = type,
                CombinevalueValueType = ModbusCombineValueType.Register32_Int
            };
        }

        /// <summary>
        /// 获取所有寄存器
        /// </summary>
        public IEnumerable<ModbusRegister> GetAllRegisters()
        {
            return _registers.Values;
        }

        /// <summary>
        /// 获取指定地址的寄存器
        /// </summary>
        public ModbusRegister GetRegister(int address)
        {
            return _registers.GetValueOrDefault(address);
        }

        /// <summary>
        /// 获取所有输入寄存器
        /// </summary>
        public IEnumerable<ModbusRegister> GetInputRegisters()
        {
            return _registers.Values.Where(r => r.RegisterType == ModbusRegisterType.Input);
        }

        /// <summary>
        /// 获取所有保持寄存器
        /// </summary>
        public IEnumerable<ModbusRegister> GetHoldingRegisters()
        {
            return _registers.Values.Where(r => r.RegisterType == ModbusRegisterType.Holding);
        }

        public Task<(double? value, string error)> ReadDoubleAsync(int lowAddress)
        {
            if (!_registers.TryGetValue(lowAddress, out var lowRegister) ||
                !_registers.TryGetValue(lowAddress + 1, out var highRegister))
            {
                _logger.Error($"地址 {lowAddress} 读取双精度值失败，未找到寄存器");
                return Task.FromResult<(double? value, string error)>((null, "无效地址"));
            }

            try
            {
                long combined = ((long)highRegister.Value << 16) | lowRegister.Value;
                var value = BitConverter.Int64BitsToDouble(combined);
                _logger.Debug($"从地址 {lowAddress} 读取双精度值 {value}");
                return Task.FromResult<(double? value, string error)>((value, null));
            }
            catch (Exception ex)
            {
                _logger.Error($"转换双精度值失败: {ex.Message}", ex);
                return Task.FromResult<(double? value, string error)>((null, ex.Message));
            }
        }

        public Task<bool> WriteDoubleAsync(int lowAddress, double value)
        {
            if (!_registers.TryGetValue(lowAddress, out var lowRegister) ||
                !_registers.TryGetValue(lowAddress + 1, out var highRegister) ||
                !lowRegister.IsWriteable || !highRegister.IsWriteable)
            {
                _logger.Error($"写入双精度值 {value} 到地址 {lowAddress} 失败，未找到寄存器或寄存器不可写");
                return Task.FromResult(false);
            }

            try
            {
                long bits = BitConverter.DoubleToInt64Bits(value);
                lowRegister.Value = (ushort)(bits & 0xFFFF);
                highRegister.Value = (ushort)((bits >> 16) & 0xFFFF);

                _logger.Debug($"已将双精度值 {value} 写入地址 {lowAddress}");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.Error($"写入双精度值失败: {ex.Message}", ex);
                return Task.FromResult(false);
            }
        }
    }
}