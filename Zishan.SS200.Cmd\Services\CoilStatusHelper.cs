using System;
using System.Collections.Generic;
using System.Linq;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using <PERSON><PERSON>an.SS200.Cmd.Enums.SS200.IOInterface.Chamber;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using <PERSON>ishan.SS200.Cmd.Enums.SS200.IOInterface.Shuttle;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Models;

namespace Zishan.SS200.Cmd.Services
{
    /// <summary>
    /// 线圈状态辅助类，提供便捷的状态查询和计算方法
    /// 用于简化SubSystemStatus中的状态计算逻辑
    ///
    /// 重要说明：NPN传感器逻辑
    /// - 位置传感器（如Slit Door、Lift Pin、晶圆盒门/巢等）：到位时为0（false），未到位时为1（true）
    /// - 存在检测传感器（如晶圆盒存在传感器）：触发时为0（false），未触发时为1（true）
    /// - Paddle传感器：特殊逻辑，有晶圆时为1（true），无晶圆时为0（false）
    /// - Pin搜索传感器：检测到时为0（false），未检测到时为1（true）
    /// - 旋转/位置传感器（SDI13-SDI16）：接近时为1（true），未接近时为0（false）
    /// </summary>
    public class CoilStatusHelper
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(CoilStatusHelper));
        private readonly S200McuCmdService _mcuService;

        public CoilStatusHelper(S200McuCmdService mcuService)
        {
            _mcuService = mcuService ?? throw new ArgumentNullException(nameof(mcuService));
        }

        /// <summary>
        /// 构造函数重载，接受接口类型
        /// </summary>
        /// <param name="mcuService">MCU命令服务接口</param>
        public CoilStatusHelper(Services.Interfaces.IS200McuCmdService mcuService)
        {
            if (mcuService is S200McuCmdService concreteService)
            {
                _mcuService = concreteService;
            }
            else
            {
                throw new ArgumentException("MCU服务必须是S200McuCmdService的实例", nameof(mcuService));
            }
        }

        #region 单个线圈状态查询

        /// <summary>
        /// 获取指定枚举对应线圈的布尔值
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValue">枚举值</param>
        /// <returns>线圈的布尔值，未找到线圈或值为null时返回false</returns>
        public bool GetCoilValue<T>(EnuMcuDeviceType deviceType, T enumValue) where T : Enum
        {
            var coil = _mcuService.GetCoilByEnum(deviceType, enumValue);
            return coil?.Coilvalue ?? false;
        }

        /// <summary>
        /// 获取指定枚举对应线圈的可空布尔值
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValue">枚举值</param>
        /// <returns>线圈的可空布尔值</returns>
        public bool? GetCoilValueNullable<T>(EnuMcuDeviceType deviceType, T enumValue) where T : Enum
        {
            var coil = _mcuService.GetCoilByEnum(deviceType, enumValue);
            return coil?.Coilvalue;
        }

        /// <summary>
        /// 检查指定枚举对应的线圈是否为true
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValue">枚举值</param>
        /// <returns>线圈值是否为true</returns>
        public bool IsCoilActive<T>(EnuMcuDeviceType deviceType, T enumValue) where T : Enum
        {
            return GetCoilValue(deviceType, enumValue);
        }

        #endregion 单个线圈状态查询

        #region 多个线圈状态组合查询

        /// <summary>
        /// 检查多个线圈是否都为true（AND逻辑）
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValues">枚举值数组</param>
        /// <returns>所有线圈都为true时返回true</returns>
        public bool AreAllCoilsActive<T>(EnuMcuDeviceType deviceType, params T[] enumValues) where T : Enum
        {
            return enumValues.All(enumValue => GetCoilValue(deviceType, enumValue));
        }

        /// <summary>
        /// 检查多个线圈中是否有任意一个为true（OR逻辑）
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValues">枚举值数组</param>
        /// <returns>任意一个线圈为true时返回true</returns>
        public bool IsAnyCoilActive<T>(EnuMcuDeviceType deviceType, params T[] enumValues) where T : Enum
        {
            return enumValues.Any(enumValue => GetCoilValue(deviceType, enumValue));
        }

        /// <summary>
        /// 获取多个线圈的状态组合（用于复杂状态判断）
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValues">枚举值数组</param>
        /// <returns>线圈状态的布尔数组</returns>
        public bool[] GetCoilValues<T>(EnuMcuDeviceType deviceType, params T[] enumValues) where T : Enum
        {
            return enumValues.Select(enumValue => GetCoilValue(deviceType, enumValue)).ToArray();
        }

        #endregion 多个线圈状态组合查询

        #region Chamber特定的状态计算方法

        /// <summary>
        /// 计算Chamber的Slit Door状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>Slit Door状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus CalculateSlitDoorStatus(EnuMcuDeviceType deviceType)
        {
            var openSensor = GetCoilValue(deviceType, EnuChamberDICodes.PDI12_SlitDoorOpenSensor);
            var closeSensor = GetCoilValue(deviceType, EnuChamberDICodes.PDI13_SlitDoorCloseSensor);

            // 根据传感器状态计算Slit Door位置
            // 注意：传感器为NPN类型，到位时为0（false），未到位时为1（true）
            if (openSensor && !closeSensor)  // PDI12=0 PDI13=1
                return Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus.Close;
            else if (!openSensor && closeSensor)  // PDI12=1 PDI13=0
                return Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus.Open;
            else if (!openSensor && !closeSensor)  // PDI12=0 PDI13=0
                return Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus.BetweenOpenClose;
            else
                return Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus.None;  // 异常状态
        }

        /// <summary>
        /// 计算Chamber的Lift Pin状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>Lift Pin状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus CalculateLiftPinStatus(EnuMcuDeviceType deviceType)
        {
            var upSensor = GetCoilValue(deviceType, EnuChamberDICodes.PDI14_LiftPinUpSensor);
            var downSensor = GetCoilValue(deviceType, EnuChamberDICodes.PDI15_LiftPinDownSensor);

            // 根据传感器状态计算Lift Pin位置
            // 注意：传感器为NPN类型，到位时为0（false），未到位时为1（true）
            if (!upSensor && downSensor)  // 上传感器到位，下传感器未到位
                return Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus.Up;
            else if (upSensor && !downSensor)  // 上传感器未到位，下传感器到位
                return Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus.Down;
            else if (!upSensor && !downSensor)  // 两个传感器都到位（中间位置）
                return Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus.BetweenUpDown;
            else
                return Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus.None;  // 异常状态
        }

        #endregion Chamber特定的状态计算方法

        #region Shuttle特定的状态计算方法

        /// <summary>
        /// 检查Shuttle的晶圆盒门是否在指定位置
        /// </summary>
        /// <param name="isUp">true检查上升位置，false检查下降位置</param>
        /// <returns>是否在指定位置</returns>
        public bool IsShuttleCassetteDoorAtPosition(bool isUp)
        {
            // 注意：传感器为NPN类型，到位时为0（false），未到位时为1（true）
            if (isUp)
                return !GetCoilValue(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1_CassetteDoorUpSensor);
            else
                return !GetCoilValue(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI2_CassetteDoorDownSensor);
        }

        /// <summary>
        /// 检查Shuttle的晶圆盒巢是否在指定位置
        /// </summary>
        /// <param name="isExtended">true检查伸出位置，false检查收回位置</param>
        /// <returns>是否在指定位置</returns>
        public bool IsShuttleCassetteNestAtPosition(bool isExtended)
        {
            // 注意：传感器为NPN类型，到位时为0（false），未到位时为1（true）
            if (isExtended)
                return !GetCoilValue(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI3_CassetteNestExtendSensor);
            else
                return !GetCoilValue(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI4_CassetteNestRetractSensor);
        }

        #endregion Shuttle特定的状态计算方法

        #region Robot特定的状态计算方法

        /// <summary>
        /// 检查Robot的Paddle传感器状态
        /// </summary>
        /// <param name="paddleNumber">Paddle编号（1或2）</param>
        /// <returns>是否检测到晶圆</returns>
        public bool IsWaferDetectedOnPaddle(int paddleNumber)
        {
            return paddleNumber switch
            {
                1 => GetCoilValue(EnuMcuDeviceType.Robot, EnuRobotDICodes.RDI1_PaddleSensor1Left),
                2 => GetCoilValue(EnuMcuDeviceType.Robot, EnuRobotDICodes.RDI2_PaddleSensor2Right),
                _ => false
            };
        }

        /// <summary>
        /// 检查Robot的Pin搜索传感器状态
        /// </summary>
        /// <param name="pinNumber">Pin编号（1或2）</param>
        /// <returns>是否检测到Pin</returns>
        public bool IsPinDetected(int pinNumber)
        {
            return pinNumber switch
            {
                1 => !GetCoilValue(EnuMcuDeviceType.Robot, EnuRobotDICodes.RDI3_PinSearch1), // 注意：检测到时为0
                2 => !GetCoilValue(EnuMcuDeviceType.Robot, EnuRobotDICodes.RDI4_PinSearch2), // 注意：检测到时为0
                _ => false
            };
        }

        #endregion Robot特定的状态计算方法

        #region Chamber状态计算方法

        /// <summary>
        /// 计算Chamber的Wafer Ready状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>Wafer Ready状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuWaferReadyStatus CalculateWaferReadyStatus(EnuMcuDeviceType deviceType)
        {
            var slitDoorStatus = CalculateSlitDoorStatus(deviceType);
            var liftPinStatus = CalculateLiftPinStatus(deviceType);

            // SP7: wafer ready receive (SP1 SP5) - slit door open + lift pin down
            if (slitDoorStatus == Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus.Open &&
                liftPinStatus == Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus.Down)
            {
                return Models.SS200.SubSystemStatus.Chamber.EnuWaferReadyStatus.ReadyReceive;
            }

            // SP8: wafer ready out (SP1 SP4) - slit door open + lift pin up
            if (slitDoorStatus == Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus.Open &&
                liftPinStatus == Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus.Up)
            {
                return Models.SS200.SubSystemStatus.Chamber.EnuWaferReadyStatus.ReadyOut;
            }

            // SP9: wafer received (SP2 SP4) - slit door close + lift pin up
            if (slitDoorStatus == Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus.Close &&
                liftPinStatus == Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus.Up)
            {
                return Models.SS200.SubSystemStatus.Chamber.EnuWaferReadyStatus.Received;
            }

            // SP10: wafer out (SP2 SP5) - slit door close + lift pin down
            if (slitDoorStatus == Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus.Close &&
                liftPinStatus == Models.SS200.SubSystemStatus.Chamber.EnuLiftPinStatus.Down)
            {
                return Models.SS200.SubSystemStatus.Chamber.EnuWaferReadyStatus.Out;
            }

            return Models.SS200.SubSystemStatus.Chamber.EnuWaferReadyStatus.None;
        }

        /// <summary>
        /// 计算Chamber的ISO阀门状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>ISO阀门状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuValveStatus CalculateIsoValveStatus(EnuMcuDeviceType deviceType)
        {
            var openSensor = GetCoilValue(deviceType, EnuChamberDICodes.PDI1_CvOpenSensor);
            var closeSensor = GetCoilValue(deviceType, EnuChamberDICodes.PDI2_CvCloseSensor);

            // 根据传感器状态计算ISO阀门位置
            // 注意：传感器为NPN类型，到位时为0（false），未到位时为1（true）
            // SP15: ISO valve open (PDI1=0 PDI2=1)
            if (!openSensor && closeSensor)
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.Open;
            // SP16: ISO valve close (PDI1=1 PDI2=0)
            else if (openSensor && !closeSensor)
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.Close;
            // SP17: ISO valve between open close (PDI1=1 PDI2=1)
            else if (openSensor && closeSensor)
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.BetweenOpenClose;
            else
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.None;  // 异常状态
        }

        /// <summary>
        /// 计算Chamber的节流阀状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>节流阀状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuValveStatus CalculateThrottleValveStatus(EnuMcuDeviceType deviceType)
        {
            // 根据I/O分布表：SP18-SP20使用PDI3和PDI4
            var tvOpenSensor = GetCoilValue(deviceType, EnuChamberDICodes.PDI3_TvOpenSensor);
            var tvCloseSensor = GetCoilValue(deviceType, EnuChamberDICodes.PDI4_TvCloseSensor);

            // 根据传感器状态计算节流阀位置
            // 注意：传感器为NPN类型，到位时为0（false），未到位时为1（true）
            // SP18: throttle valve open (PDI3=0 PDI4=1)
            if (!tvOpenSensor && tvCloseSensor)
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.Open;
            // SP19: throttle valve close (PDI3=1 PDI4=0)
            else if (tvOpenSensor && !tvCloseSensor)
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.Close;
            // SP20: throttle valve between open close (PDI3=1 PDI4=1)
            else if (tvOpenSensor && tvCloseSensor)
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.BetweenOpenClose;
            else
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.None;  // 异常状态
        }

        /// <summary>
        /// 计算Chamber的前级真空状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>前级真空状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuForlineVacuumStatus CalculateForlineVacuumStatus(EnuMcuDeviceType deviceType)
        {
            var forelineSwitch = GetCoilValue(deviceType, EnuChamberDICodes.PDI5_ForelineSwitch);

            // 根据前级开关状态计算前级真空状态
            // SP21: sensor show foreline no vacuum (PDI5=0)
            if (!forelineSwitch)
                return Models.SS200.SubSystemStatus.Chamber.EnuForlineVacuumStatus.NoVacuum;
            // SP22: sensor show foreline vacuum (PDI5=1)
            else
                return Models.SS200.SubSystemStatus.Chamber.EnuForlineVacuumStatus.HasVacuum;
        }

        /// <summary>
        /// 计算Chamber的腔体真空状态（基于数字输入，不包含模拟量）
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>腔体真空状态枚举</returns>
        /// <remarks>
        /// 完整的腔体真空状态需要模拟量PAI1和参数PPS6，此方法仅基于数字输入PDI5和PDI6
        /// SP11: chamber process vacuum (PDI5=1 PDI6=1 PAI1≤PPS6)
        /// SP12: chamber no process vacuum (PDI5=0 or PDI6=0 or PAI1>PPS6)
        /// </remarks>
        public Models.SS200.SubSystemStatus.Chamber.EnuChamberVacuumStatus CalculateChamberVacuumStatusBasic(EnuMcuDeviceType deviceType)
        {
            var forelineSwitch = GetCoilValue(deviceType, EnuChamberDICodes.PDI5_ForelineSwitch);
            var torrSwitch = GetCoilValue(deviceType, EnuChamberDICodes.PDI6_TorrSwitch);

            // 基于数字输入的基础判断（不包含模拟量PAI1和PPS6的比较）
            // 如果PDI5=0 or PDI6=0，则为非工艺真空
            if (!forelineSwitch || !torrSwitch)
                return Models.SS200.SubSystemStatus.Chamber.EnuChamberVacuumStatus.NoProcessVacuum;

            // 如果PDI5=1 and PDI6=1，需要进一步检查模拟量，这里暂时返回工艺真空
            // TODO: 需要添加模拟量PAI1和参数PPS6的比较逻辑
            return Models.SS200.SubSystemStatus.Chamber.EnuChamberVacuumStatus.ProcessVacuum;
        }

        /// <summary>
        /// 计算Chamber的气体阀门状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <param name="valveType">阀门类型（CM, C1, C2, C3, C4）</param>
        /// <returns>气体阀门状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuValveStatus CalculateGasValveStatus(EnuMcuDeviceType deviceType, string valveType)
        {
            EnuChamberDOCodes doCode = valveType.ToUpper() switch
            {
                "CM" => EnuChamberDOCodes.PDO9_GasCmValveControl,
                "C1" => EnuChamberDOCodes.PDO5_GasC1Control,
                "C2" => EnuChamberDOCodes.PDO6_GasC2Control,
                "C3" => EnuChamberDOCodes.PDO7_GasC3Control,
                "C4" => EnuChamberDOCodes.PDO8_GasC4Control,
                _ => throw new ArgumentException($"不支持的阀门类型: {valveType}")
            };

            var outputValue = GetOutputCoilValue(deviceType, doCode);

            // 根据I/O分布表：
            // SP23: CM open (PDO9=0), SP24: CM close (PDO9=1)
            // SP25: C1 open (PDO5=0), SP26: C1 close (PDO5=1)
            // 等等...
            // 由于enable:0为有效，当输出为0时阀门打开，为1时阀门关闭
            if (!outputValue)
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.Open;
            else
                return Models.SS200.SubSystemStatus.Chamber.EnuValveStatus.Close;
        }

        /// <summary>
        /// 计算Chamber的CM阀门状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>CM阀门状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuValveStatus CalculateCmValveStatus(EnuMcuDeviceType deviceType)
        {
            return CalculateGasValveStatus(deviceType, "CM");
        }

        /// <summary>
        /// 计算Chamber的C1阀门状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>C1阀门状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuValveStatus CalculateC1ValveStatus(EnuMcuDeviceType deviceType)
        {
            return CalculateGasValveStatus(deviceType, "C1");
        }

        /// <summary>
        /// 计算Chamber的C2阀门状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>C2阀门状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuValveStatus CalculateC2ValveStatus(EnuMcuDeviceType deviceType)
        {
            return CalculateGasValveStatus(deviceType, "C2");
        }

        /// <summary>
        /// 计算Chamber的C3阀门状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>C3阀门状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuValveStatus CalculateC3ValveStatus(EnuMcuDeviceType deviceType)
        {
            return CalculateGasValveStatus(deviceType, "C3");
        }

        /// <summary>
        /// 计算Chamber的C4阀门状态
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>C4阀门状态枚举</returns>
        public Models.SS200.SubSystemStatus.Chamber.EnuValveStatus CalculateC4ValveStatus(EnuMcuDeviceType deviceType)
        {
            return CalculateGasValveStatus(deviceType, "C4");
        }

        /// <summary>
        /// 获取输出线圈的值
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="doCode">数字输出代码</param>
        /// <returns>线圈值，如果未找到则返回false</returns>
        private bool GetOutputCoilValue(EnuMcuDeviceType deviceType, EnuChamberDOCodes doCode)
        {
            try
            {
                var outputCoil = _mcuService.GetOutputCoilByEnum(deviceType, doCode);
                return outputCoil?.Coilvalue ?? false;
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，返回默认值
                System.Diagnostics.Debug.WriteLine($"获取输出线圈值失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 计算Chamber的负载锁真空状态（预留接口）
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>负载锁真空状态枚举</returns>
        /// <remarks>
        /// 完整的负载锁真空状态需要模拟量PAI6和参数PPS7
        /// SP13: loadlock pressure vacuum (PAI6≤PPS7)
        /// SP14: loadlock pressure no vacuum (PAI6>PPS7)
        /// 此方法为预留接口，需要后续实现模拟量读取功能
        /// </remarks>
        public Models.SS200.SubSystemStatus.Chamber.EnuLoadlockVacuumStatus CalculateLoadlockVacuumStatus(EnuMcuDeviceType deviceType)
        {
            // TODO: 需要实现模拟量PAI6和参数PPS7的读取和比较逻辑
            return Models.SS200.SubSystemStatus.Chamber.EnuLoadlockVacuumStatus.None;
        }

        /// <summary>
        /// 计算Chamber的触发状态（预留接口）
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>触发状态枚举</returns>
        /// <remarks>
        /// 触发状态的计算可能需要更复杂的逻辑，包括报警状态检查等
        /// MPS1: no alarm → EnuTriggerStatus.NoAlarm
        /// MPS2: alarm → EnuTriggerStatus.Alarm
        /// 此方法为预留接口，需要后续实现具体的报警检查逻辑
        /// </remarks>
        public Models.SS200.SubSystemStatus.Chamber.EnuTriggerStatus CalculateTriggerStatus(EnuMcuDeviceType deviceType)
        {
            // TODO: 需要实现报警状态检查逻辑
            return Models.SS200.SubSystemStatus.Chamber.EnuTriggerStatus.NoAlarm;
        }

        /// <summary>
        /// 计算Chamber的运行状态（预留接口）
        /// </summary>
        /// <param name="deviceType">Chamber设备类型（ChamberA或ChamberB）</param>
        /// <returns>运行状态枚举</returns>
        /// <remarks>
        /// 运行状态的计算需要检查设备运行状态、RF状态等
        /// MPS3A: busy A (当前chamber slit door动作中，或者robot于chamber交互中)
        /// MPS3B: busy B (lift pin动作中)
        /// MPS4: idle (slit door/lift pin不在动作中，或者不在与robot交互中)
        /// MPS5: processing (recipe running，RF on)
        /// 此方法为预留接口，需要后续实现具体的运行状态检查逻辑
        /// </remarks>
        public Models.SS200.SubSystemStatus.Chamber.EnuRunStatus CalculateRunStatus(EnuMcuDeviceType deviceType)
        {
            // TODO: 需要实现运行状态检查逻辑，包括设备运行状态、RF状态等
            return Models.SS200.SubSystemStatus.Chamber.EnuRunStatus.Idle;
        }

        #endregion Chamber状态计算方法

        #region 工具方法

        /// <summary>
        /// 获取所有可用的线圈信息（用于调试）
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>线圈信息字典</returns>
        public Dictionary<string, bool?> GetAllCoilStatus(EnuMcuDeviceType deviceType)
        {
            var result = new Dictionary<string, bool?>();

            _mcuService.GetModbusCoil(deviceType, out var inputCoils, out var controlCoils);

            if (inputCoils != null)
            {
                foreach (var coil in inputCoils)
                {
                    result[$"DI_{coil.IoCode}"] = coil.Coilvalue;
                }
            }

            if (controlCoils != null)
            {
                foreach (var coil in controlCoils)
                {
                    result[$"DO_{coil.IoCode}"] = coil.Coilvalue;
                }
            }

            return result;
        }

        #endregion 工具方法

        #region Shuttle状态计算方法

        /// <summary>
        /// 计算Shuttle基本状态 (MSD1-MSD3)
        /// </summary>
        /// <returns>Shuttle状态</returns>
        public Models.SS200.SubSystemStatus.Shuttle.EnuShuttleStatus CalculateShuttleStatus()
        {
            // 这里需要根据实际的状态逻辑来实现
            // 目前返回默认值，实际实现需要根据具体的状态条件
            return Models.SS200.SubSystemStatus.Shuttle.EnuShuttleStatus.Idle;
        }

        /// <summary>
        /// 计算Shuttle位置状态 (SSD1-SSD7)
        /// </summary>
        /// <param name="ssc6Config">SSC6配置模式</param>
        /// <returns>Shuttle位置状态</returns>
        public Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus CalculateShuttlePositionStatus(Models.SS200.SubSystemStatus.Shuttle.EnuSSC6Config ssc6Config)
        {
            var deviceType = EnuMcuDeviceType.Shuttle;

            // 获取位置传感器状态
            var rotate1 = GetCoilValue(deviceType, EnuShuttleDICodes.SDI13_ShuttleRotateSensor1);
            var rotate2 = GetCoilValue(deviceType, EnuShuttleDICodes.SDI14_ShuttleRotateSensor2);
            var up = GetCoilValue(deviceType, EnuShuttleDICodes.SDI15_ShuttleUpSensor);
            var down = GetCoilValue(deviceType, EnuShuttleDICodes.SDI16_ShuttleDownSensor);

            // 获取巢传感器状态（仅SMIF模式需要）- 注意：SDI4为NPN类型，到位时为0
            var nestRetract = GetCoilValue(deviceType, EnuShuttleDICodes.SDI4_CassetteNestRetractSensor);
            var nestHome = GetCoilValue(deviceType, EnuShuttleDICodes.SDI5_CassetteNestHomeSwitch);
            // var nestRetract = !nestRetractRaw; // 转换NPN逻辑：到位时为true
            // var nestHome = !nestHomeRaw;       // 转换NPN逻辑：到位时为true

            // SSD1: DI13=1 DI14=0 DI15=0 DI16=0 (Shuttle上升/Shuttle1外部)
            if (rotate1 && !rotate2 && !up && !down)
                return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleUpShuttle1Outer;

            // SSD2: DI13=0 DI14=1 DI15=0 DI16=0 (Shuttle上升/Shuttle2外部)
            if (!rotate1 && rotate2 && !up && !down)
                return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleUpShuttle2Outer;

            // SSD3: Shuttle下降/Shuttle1外部位置
            if (rotate1 && !rotate2 && !up && down)
            {
                if (ssc6Config == Models.SS200.SubSystemStatus.Shuttle.EnuSSC6Config.SMIF)
                {
                    // SMIF模式需要检查巢传感器: 巢收回未到位 && 巢Home未到位
                    if (!nestRetract && !nestHome)
                        return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleDownShuttle1Outer;
                }
                else
                {
                    // FIXED模式不需要检查巢传感器
                    return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleDownShuttle1Outer;
                }
            }

            // SSD4: Shuttle下降/Shuttle2外部位置
            if (!rotate1 && rotate2 && !up && down)
            {
                if (ssc6Config == Models.SS200.SubSystemStatus.Shuttle.EnuSSC6Config.SMIF)
                {
                    // SMIF模式需要检查巢传感器: DI4=0 DI5=0
                    if (!nestRetract && !nestHome)
                        return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleDownShuttle2Outer;
                }
                else
                {
                    // FIXED模式不需要检查巢传感器
                    return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleDownShuttle2Outer;
                }
            }

            // SSD5: Shuttle上下之间/Shuttle1外部位置
            if (rotate1 && !rotate2 && up && !down)
            {
                if (ssc6Config == Models.SS200.SubSystemStatus.Shuttle.EnuSSC6Config.SMIF)
                {
                    // SMIF模式需要检查巢传感器: DI4=0 DI5=0
                    if (!nestRetract && !nestHome)
                        return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleUpDownBetweenShuttle1Outer;
                }
                else
                {
                    // FIXED模式不需要检查巢传感器
                    return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleUpDownBetweenShuttle1Outer;
                }
            }

            // SSD6: Shuttle上下之间/Shuttle2外部位置
            if (!rotate1 && rotate2 && up && !down)
            {
                if (ssc6Config == Models.SS200.SubSystemStatus.Shuttle.EnuSSC6Config.SMIF)
                {
                    // SMIF模式需要检查巢传感器: DI4=0 DI5=0
                    if (!nestRetract && !nestHome)
                        return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleUpDownBetweenShuttle2Outer;
                }
                else
                {
                    // FIXED模式不需要检查巢传感器
                    return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleUpDownBetweenShuttle2Outer;
                }
            }

            // SSD7: DI13=0 DI14=0 DI15=0 DI16=1 (Shuttle下降且旋转之间)
            if (!rotate1 && !rotate2 && !up && down)
                return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.ShuttleDownRotationBetween;

            return Models.SS200.SubSystemStatus.Shuttle.EnuShuttlePositionStatus.None;
        }

        /// <summary>
        /// 计算晶圆盒门和巢状态 (SSD8-SSD13)
        /// </summary>
        /// <param name="ssc6Config">SSC6配置模式</param>
        /// <returns>晶圆盒门和巢状态</returns>
        public Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus CalculateCassetteDoorNestStatus(Models.SS200.SubSystemStatus.Shuttle.EnuSSC6Config ssc6Config)
        {
            var deviceType = EnuMcuDeviceType.Shuttle;

            var doorUp = GetCoilValue(deviceType, EnuShuttleDICodes.SDI1_CassetteDoorUpSensor);
            var doorDown = GetCoilValue(deviceType, EnuShuttleDICodes.SDI2_CassetteDoorDownSensor);
            // var doorUp = !doorUpRaw;    // 转换NPN逻辑：到位时为true
            // var doorDown = !doorDownRaw; // 转换NPN逻辑：到位时为true

            if (ssc6Config == Models.SS200.SubSystemStatus.Shuttle.EnuSSC6Config.SMIF)
            {
                // SMIF模式需要检查所有传感器
                var rotate1 = GetCoilValue(deviceType, EnuShuttleDICodes.SDI3_CassetteNestExtendSensor);
                var nestRetract = GetCoilValue(deviceType, EnuShuttleDICodes.SDI4_CassetteNestRetractSensor);
                var nestHome = GetCoilValue(deviceType, EnuShuttleDICodes.SDI5_CassetteNestHomeSwitch);
                // var nestRetract = !nestRetractRaw; // 转换NPN逻辑：到位时为true
                // var nestHome = !nestHomeRaw;       // 转换NPN逻辑：到位时为true

                // SSD8: DI1=1 DI2=0 DI13=0 DI4=1 DI5=1
                if (doorUp && !doorDown && !rotate1 && nestRetract && nestHome)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteDoorOpen;

                // SSD9: DI1=0 DI2=1 DI13=1 DI4=0 DI5=0
                if (!doorUp && doorDown && rotate1 && !nestRetract && !nestHome)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteDoorClose;

                // SSD10: DI1=0 DI2=0 DI13=1 DI4=0 DI5=0
                if (!doorUp && !doorDown && rotate1 && !nestRetract && !nestHome)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteDoorBetween;

                // SSD11: DI1=1 DI2=0 DI13=1 DI4=0 DI5=0
                if (doorUp && !doorDown && rotate1 && !nestRetract && !nestHome)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteDoorOpenNestRetract;

                // SSD12: DI1=1 DI2=0 DI13=1 DI4=1 DI5=1
                if (doorUp && !doorDown && rotate1 && nestRetract && nestHome)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteNestBetween1;

                // SSD13: DI1=1 DI2=0 DI13=1 DI4=1 DI5=0
                if (doorUp && !doorDown && rotate1 && nestRetract && !nestHome)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteNestBetween2;
            }
            else
            {
                // FIXED模式只检查门传感器
                // SSD8: DI1=1 DI2=0
                if (doorUp && !doorDown)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteDoorOpen;

                // SSD9: DI1=0 DI2=1
                if (!doorUp && doorDown)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteDoorClose;

                // SSD10: DI1=0 DI2=0
                if (!doorUp && !doorDown)
                    return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.CassetteDoorBetween;

                // SSD11-SSD13在FIXED模式下为N/A
            }

            return Models.SS200.SubSystemStatus.Shuttle.EnuCassetteDoorNestStatus.None;
        }

        /// <summary>
        /// 计算Shuttle阀门状态
        /// </summary>
        /// <param name="valveType">阀门类型</param>
        /// <returns>阀门状态</returns>
        public Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus CalculateShuttleValveStatus(Enums.SS200.IOInterface.Shuttle.ShuttleValveType valveType)
        {
            var deviceType = EnuMcuDeviceType.Shuttle;

            switch (valveType)
            {
                case Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleIso:
                    // SSD14-SSD15: Shuttle ISO阀门
                    var isoOpen = GetCoilValue(deviceType, EnuShuttleDICodes.SDI22_ShuttleVacuumIsoValveOpenSensor);
                    var isoClose = GetCoilValue(deviceType, EnuShuttleDICodes.SDI23_ShuttleVacuumIsoValveCloseSensor);

                    if (!isoOpen && isoClose)  // 注意：传感器为NPN类型，到位时为0
                        return Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Open;
                    else if (isoOpen && !isoClose)
                        return Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Close;
                    break;

                case Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleXv:
                    // SSD16-SSD17: Shuttle XV阀门
                    var xvOpen = GetCoilValue(deviceType, EnuShuttleDICodes.SDI24_XvCrossValveOpenSensor);
                    var xvClose = GetCoilValue(deviceType, EnuShuttleDICodes.SDI25_XvCrossValveCloseSensor);

                    if (!xvOpen && xvClose)  // 注意：传感器为NPN类型，到位时为0
                        return Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Open;
                    else if (xvOpen && !xvClose)
                        return Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Close;
                    break;

                case Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleBackfill:
                    // SSD18-SSD19: Shuttle回填阀门 (基于DO状态)
                    // 由于enable:0为有效，当输出为0时阀门打开，为1时阀门关闭
                    var backfillDO = GetOutputCoilValue(deviceType, EnuShuttleDOCodes.SDO7_ShuttleBackfillValve);
                    return !backfillDO ? Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Open : Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Close;

                case Enums.SS200.IOInterface.Shuttle.ShuttleValveType.LoadlockBleed:
                    // SSD20-SSD21: 负载锁排气阀门 (基于DO状态)
                    // 由于enable:0为有效，当输出为0时阀门打开，为1时阀门关闭
                    var bleedDO = GetOutputCoilValue(deviceType, EnuShuttleDOCodes.SDO9_LoadlockBleedValve);
                    return !bleedDO ? Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Open : Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Close;

                case Enums.SS200.IOInterface.Shuttle.ShuttleValveType.LoadlockBackfill:
                    // SSD22-SSD23: 负载锁回填阀门 (基于DO状态)
                    // 由于enable:0为有效，当输出为0时阀门打开，为1时阀门关闭
                    var loadlockBackfillDO = GetOutputCoilValue(deviceType, EnuShuttleDOCodes.SDO10_LoadlockBackfillValve);
                    return !loadlockBackfillDO ? Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Open : Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.Close;
            }

            return Models.SS200.SubSystemStatus.Shuttle.EnuShuttleValveStatus.None;
        }

        /// <summary>
        /// 获取输出线圈值
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="doCode">DO代码</param>
        /// <returns>线圈值</returns>
        private bool GetOutputCoilValue(EnuMcuDeviceType deviceType, EnuShuttleDOCodes doCode)
        {
            var coil = _mcuService.GetOutputCoilByEnum(deviceType, doCode);
            return coil?.Coilvalue ?? false;
        }

        /// <summary>
        /// 计算特定晶圆盒位置的批次状态 (LSD1-LSD4)
        /// </summary>
        /// <param name="shuttleNumber">Shuttle编号 (1或2)</param>
        /// <param name="cassetteNumber">晶圆盒编号 (1或2)</param>
        /// <returns>批次状态枚举</returns>
        public Models.SS200.SubSystemStatus.Shuttle.EnuLotStatus CalculateLotStatus(int shuttleNumber, int cassetteNumber)
        {
            try
            {
                bool hasLot = IsCassettePresent(shuttleNumber, cassetteNumber);
                return hasLot ? Models.SS200.SubSystemStatus.Shuttle.EnuLotStatus.HasLot :
                               Models.SS200.SubSystemStatus.Shuttle.EnuLotStatus.NoLot;
            }
            catch (Exception ex)
            {
                _logger.Error($"计算批次状态时发生错误 (Shuttle{shuttleNumber}, Cassette{cassetteNumber}): {ex.Message}", ex);
                return Models.SS200.SubSystemStatus.Shuttle.EnuLotStatus.NoLot;
            }
        }

        /// <summary>
        /// 计算所有Shuttle批次状态 (LSD1-LSD4)
        /// </summary>
        /// <returns>包含所有批次状态的字典</returns>
        public Dictionary<string, Models.SS200.SubSystemStatus.Shuttle.EnuLotStatus> CalculateAllLotStatus()
        {
            var lotStatus = new Dictionary<string, Models.SS200.SubSystemStatus.Shuttle.EnuLotStatus>();

            try
            {
                lotStatus["LSD1"] = CalculateLotStatus(1, 1); // Shuttle1 Cassette1
                lotStatus["LSD2"] = CalculateLotStatus(1, 2); // Shuttle1 Cassette2
                lotStatus["LSD3"] = CalculateLotStatus(2, 1); // Shuttle2 Cassette1
                lotStatus["LSD4"] = CalculateLotStatus(2, 2); // Shuttle2 Cassette2
            }
            catch (Exception ex)
            {
                _logger.Error($"计算所有批次状态时发生错误: {ex.Message}", ex);
            }

            return lotStatus;
        }

        /// <summary>
        /// 获取特定晶圆盒位置的存在状态
        /// </summary>
        /// <param name="shuttleNumber">Shuttle编号 (1或2)</param>
        /// <param name="cassetteNumber">晶圆盒编号 (1或2)</param>
        /// <returns>晶圆盒是否存在</returns>
        public bool IsCassettePresent(int shuttleNumber, int cassetteNumber)
        {
            try
            {
                EnuShuttleDICodes sensorCode;

                // 根据Shuttle和晶圆盒编号选择对应的传感器
                switch (shuttleNumber)
                {
                    case 1:
                        sensorCode = cassetteNumber == 1 ?
                            EnuShuttleDICodes.SDI6_PresentSensorCassette1 :
                            EnuShuttleDICodes.SDI7_PresentSensorCassette2;
                        break;

                    case 2:
                        sensorCode = cassetteNumber == 1 ?
                            EnuShuttleDICodes.SDI8_PresentSensorCassette3 :
                            EnuShuttleDICodes.SDI9_PresentSensorCassette4;
                        break;

                    default:
                        _logger.Warn($"无效的Shuttle编号: {shuttleNumber}");
                        return false;
                }

                // 注意：存在传感器为开关类型，触发为低电平0，未触发为高电平1
                // 当晶圆盒存在时，传感器被触发，值为0，所以需要取反
                var sensorValue = GetCoilValue(EnuMcuDeviceType.Shuttle, sensorCode);
                return sensorValue; // 传感器值为1时表示晶圆盒存在
            }
            catch (Exception ex)
            {
                _logger.Error($"检查晶圆盒存在状态时发生错误 (Shuttle{shuttleNumber}, Cassette{cassetteNumber}): {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取所有晶圆盒存在状态的详细信息
        /// </summary>
        /// <returns>包含所有晶圆盒状态的字典</returns>
        public Dictionary<string, bool> GetAllCassettePresenceStatus()
        {
            var status = new Dictionary<string, bool>();

            try
            {
                status["LSD1_Shuttle1_Cassette1"] = IsCassettePresent(1, 1);
                status["LSD2_Shuttle1_Cassette2"] = IsCassettePresent(1, 2);
                status["LSD3_Shuttle2_Cassette1"] = IsCassettePresent(2, 1);
                status["LSD4_Shuttle2_Cassette2"] = IsCassettePresent(2, 2);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取晶圆盒存在状态时发生错误: {ex.Message}", ex);
            }

            return status;
        }

        #endregion Shuttle状态计算方法
    }
}