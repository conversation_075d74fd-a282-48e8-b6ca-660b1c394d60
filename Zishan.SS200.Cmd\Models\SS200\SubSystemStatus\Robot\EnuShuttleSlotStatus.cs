using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot
{
    /// <summary>
    /// Shuttle槽位状态枚举
    /// </summary>
    public enum EnuShuttleSlotStatus
    {
        /// <summary>
        /// 无状态
        /// </summary>
        [Description("无状态")]
        None = 0,

        /// <summary>
        /// Smoth端到Shuttle1槽位get
        /// </summary>
        [Description("Smoth端到Shuttle1槽位get")]
        SmoothToShuttle1SlotGet = 1,

        /// <summary>
        /// Smoth端到Shuttle1槽位put
        /// </summary>
        [Description("Smoth端到Shuttle1槽位put")]
        SmoothToShuttle1SlotPut = 2,

        /// <summary>
        /// Nose端到Shuttle1槽位get
        /// </summary>
        [Description("Nose端到Shuttle1槽位get")]
        NoseToShuttle1SlotGet = 3,

        /// <summary>
        /// Nose端到Shuttle1槽位put
        /// </summary>
        [Description("Nose端到Shuttle1槽位put")]
        NoseToShuttle1SlotPut = 4,

        /// <summary>
        /// Smoth端到Shuttle2槽位get
        /// </summary>
        [Description("Smoth端到Shuttle2槽位get")]
        SmoothToShuttle2SlotGet = 5,

        /// <summary>
        /// Smoth端到Shuttle2槽位put
        /// </summary>
        [Description("Smoth端到Shuttle2槽位put")]
        SmoothToShuttle2SlotPut = 6,

        /// <summary>
        /// Nose端到Shuttle2槽位get
        /// </summary>
        [Description("Nose端到Shuttle2槽位get")]
        NoseToShuttle2SlotGet = 7,

        /// <summary>
        /// Nose端到Shuttle2槽位put
        /// </summary>
        [Description("Nose端到Shuttle2槽位put")]
        NoseToShuttle2SlotPut = 8
    }
}