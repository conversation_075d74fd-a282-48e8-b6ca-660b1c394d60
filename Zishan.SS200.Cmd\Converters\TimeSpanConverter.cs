using System;
using System.Globalization;
using System.Windows.Data;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// TimeSpan转换器，使用Utility.GetHourMinutesInfo方法提供直观的时间显示
    /// </summary>
    public class TimeSpanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is TimeSpan timeSpan)
            {
                // 使用 Utility.GetHourMinutesInfo 方法，用户更加直观查看
                var readableTime = Utility.GetHourMinutesInfo(timeSpan);
                return $"剩余{readableTime}";
            }
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}