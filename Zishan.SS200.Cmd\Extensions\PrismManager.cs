﻿namespace Zishan.SS200.Cmd.Extensions
{
    public static class PrismManager
    {
        /// <summary>
        /// 首页区域
        /// </summary>
        public static readonly string MainViewRegionName = "MainViewRegion";

        /// <summary>
        /// 配方或者Sequence管理区域
        /// </summary>
        public static readonly string RecipeOrSequenceViewRegionName = "RecipeOrSequenceViewRegion";

        /// <summary>
        /// 配方（CH、Cooling）公共区域
        /// </summary>
        public static readonly string RecipeViewRegionName = "RecipeViewRegion";

        /// <summary>
        /// 传输校准管理区域
        /// </summary>
        public static readonly string CalibrationViewRegionName = "CalibrationViewRegion";

        /// <summary>
        /// 参数设定管理区域
        /// </summary>
        public static readonly string ParameterSettingViewRegionName = "ParameterSettingViewRegion";

        /// <summary>
        /// LogRecordViewRegion管理区域
        /// </summary>
        public static readonly string LogRecordViewRegionName = "LogRecordViewRegion";

        /// <summary>
        /// 设置页区域
        /// </summary>
        //public static readonly string SettingsViewRegionName = "SettingsViewRegion";
    }
}