# T轴归零InterLock实现总结

## 概述

根据AR09 T轴归零逻辑文档，完成了T轴归零的InterLock使用实现。该实现充分利用了现有的InterLock系统，通过统一的访问接口获取状态、配置和IO信息，避免了重复实现。

## 实现特点

### 1. 充分利用InterLock系统
- **状态获取**: 通过 `_interLock.SubsystemStatus.Robot.Status` 获取机器人状态
- **配置访问**: 通过 `_interLock.SubsystemConfigure.Robot` 获取位置参数
- **IO访问**: 通过 `_interLock.IOInterface.Shuttle` 获取传感器状态
- **RTZ轴位置**: 通过 `_interLock.RTZAxisPosition` 获取实时位置信息

### 2. 遵循AR09逻辑流程
严格按照AR09文档实现T轴归零的完整流程：

```
AR9 T-axis zero
├── Robot status review (MRS1~MRS3)
│   ├── MRS1 IDLE → 允许执行
│   ├── MRS2 BUSY → RA1 ALARM
│   └── MRS3 ALARM → RA2 ALARM
├── T-axis position status review (RS9 or others)
│   ├── RS9 → 已在零位，直接完成
│   └── others → 执行归零流程
├── R-axis zero (AR19)
├── Z-axis to rotation position (AR39-RPS27)
├── T-axis to zero position (AR10-RP9)
└── Shuttle sensor check (SPS11 & DI19~DI20)
```

## 核心实现

### 1. 主要方法

<augment_code_snippet path="Extensions/RobotWaferOperationsExtensions.cs" mode="EXCERPT">
````csharp
/// <summary>
/// T轴归零 - 根据AR09逻辑实现完整的T轴归零流程
/// 主要利用InterLock系统获取状态和配置信息
/// </summary>
public static async Task<(bool Success, string Message)> ZeroTAxisAsync(
    this IS200McuCmdService cmdService)
{
    // 1. Robot状态检查 (MRS1~MRS3)
    if (!CheckRobotStatusForOperation("T轴归零"))
        return (false, "机器人状态不允许执行T轴归零操作");

    // 2. 获取当前RTZ轴位置
    var currentPosition = _interLock.RTZAxisPosition.GetCurrentRTZSteps();
    
    // 3. 检查是否已在零位 (RS9)
    int tAxisZeroPosition = _interLock.SubsystemConfigure.Robot.RP9_TAxisZero.Value;
    if (Math.Abs(currentPosition.TAxisStep - tAxisZeroPosition) <= 100)
        return (true, "T轴已在零位，归零完成");

    // 4. 执行归零流程
    // 4.1 R轴归零 (AR19)
    // 4.2 Z轴移动到旋转位置 (AR39-RPS27)
    // 4.3 移动T轴到零位 (AR10-RP9)
    // 4.4 Shuttle传感器检查 (SPS11检查)
}
````
</augment_code_snippet>

### 2. 辅助方法

#### 机器人状态检查
<augment_code_snippet path="Extensions/RobotWaferOperationsExtensions.cs" mode="EXCERPT">
````csharp
/// <summary>
/// 检查机器人状态是否允许执行操作 - 使用InterLock系统
/// </summary>
private static bool CheckRobotStatusForOperation(string operationName)
{
    var robotStatus = _interLock.SubsystemStatus.Robot.Status;
    
    if (robotStatus.EnuRobotStatus == EnuRobotStatus.Idle)
        return true; // MRS1 IDLE - 允许执行
    
    if (robotStatus.EnuRobotStatus == EnuRobotStatus.Busy)
        return false; // MRS2 BUSY - RA1 ALARM
    
    if (robotStatus.EnuRobotStatus == EnuRobotStatus.Alarm)
        return false; // MRS3 ALARM - RA2 ALARM
}
````
</augment_code_snippet>

#### Shuttle传感器检查
<augment_code_snippet path="Extensions/RobotWaferOperationsExtensions.cs" mode="EXCERPT">
````csharp
/// <summary>
/// 检查Shuttle滑出传感器状态 - 使用InterLock系统获取传感器状态
/// </summary>
private static (bool Success, string Message) CheckShuttleSlideOutSensors()
{
    // 检查SPS11配置
    string sps11Value = _interLock.SubsystemConfigure.Shuttle.SPS11_SlideOutBackSensorEnable.StringValue;
    
    if (sps11Value == "Y")
    {
        // 获取DI19和DI20状态
        bool di19Status = _interLock.IOInterface.Shuttle.SDI19_WaferSlideOutSensor3BL.Value;
        bool di20Status = _interLock.IOInterface.Shuttle.SDI20_WaferSlideOutSensor4BR.Value;
        
        // 根据AR09逻辑检查传感器状态组合
        if (!di19Status && !di20Status) // DI19=0 and DI20=0
            return (true, "Shuttle传感器状态正常");
        // 其他组合返回相应的报警代码
    }
}
````
</augment_code_snippet>

## InterLock系统使用优势

### 1. 统一访问接口
- **一致性**: 所有状态、配置、IO访问都通过统一的InterLock接口
- **可维护性**: 避免直接访问底层数据，便于后续维护和扩展
- **类型安全**: 利用强类型访问器，减少运行时错误

### 2. 实时数据获取
- **RTZ轴位置**: 通过 `RTZAxisPosition` 获取实时的T、R、Z轴位置
- **机器人状态**: 通过 `SubsystemStatus.Robot` 获取实时状态
- **传感器状态**: 通过 `IOInterface.Shuttle` 获取实时IO状态

### 3. 配置参数访问
- **位置参数**: 通过 `SubsystemConfigure.Robot.RP9_TAxisZero` 等获取位置配置
- **系统参数**: 通过 `SubsystemConfigure.Robot.RPS27_ZAxisHeightForRobotRotation` 等获取系统配置
- **传感器配置**: 通过 `SubsystemConfigure.Shuttle.SPS11_SlideOutBackSensorEnable` 等获取传感器配置

## 使用示例

### 基本调用
```csharp
// 执行T轴归零
var result = await cmdService.ZeroTAxisAsync();
if (result.Success)
{
    UILogService.AddSuccessLog("T轴归零成功");
}
else
{
    UILogService.AddErrorLog($"T轴归零失败: {result.Message}");
}
```

### 在机器人初始化中使用
```csharp
public static async Task<(bool Success, string Message)> InitializeRobotAsync(
    this IS200McuCmdService cmdService)
{
    // 1. T轴归零 - 使用InterLock实现
    var tResult = await ZeroTAxisAsync(cmdService);
    if (!tResult.Success)
        return tResult;

    // 2. R轴归零
    var rResult = await ZeroRAxisAsync(cmdService);
    if (!rResult.Success)
        return rResult;

    // 3. Z轴归零
    var zResult = await ZeroZAxisAsync(cmdService);
    if (!zResult.Success)
        return zResult;

    return (true, "机器人初始化完成");
}
```

## 技术特点

### 1. 错误处理
- **状态检查**: 在每个步骤前检查机器人状态
- **异常捕获**: 完整的try-catch异常处理
- **详细日志**: 每个步骤都有详细的日志记录

### 2. 参数验证
- **位置容差**: 允许100步的位置误差
- **传感器组合**: 严格按照AR09逻辑检查传感器状态组合
- **配置验证**: 检查SPS11配置决定是否进行传感器检查

### 3. 性能优化
- **避免重复**: 充分利用现有InterLock系统，避免重复实现
- **实时访问**: 直接访问实时数据，无需额外的数据同步
- **简化流程**: 如果已在零位，直接返回成功，避免不必要的操作

## 总结

本实现成功地将AR09 T轴归零逻辑与InterLock系统结合，实现了：

1. **完整的AR09逻辑流程**: 严格按照文档实现每个步骤
2. **充分利用InterLock**: 通过统一接口访问所有必要的状态和配置
3. **良好的错误处理**: 完整的状态检查和异常处理机制
4. **详细的日志记录**: 便于调试和问题追踪
5. **高可维护性**: 代码结构清晰，易于理解和维护

这种实现方式体现了"好多只需要InterLock中能获取"的设计理念，最大化地利用了现有的系统架构，避免了重复开发，提高了代码的一致性和可维护性。
