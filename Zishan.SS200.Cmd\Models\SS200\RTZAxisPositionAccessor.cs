using System;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Models.SS200
{
    /// <summary>
    /// RTZ轴位置访问器
    /// 提供统一的RTZ轴位置访问接口，包括：
    /// - 基本位置访问（步进值）
    /// - 物理单位转换（角度、长度、高度）
    /// - 组合访问和格式化显示
    /// - 数据有效性检查和安全范围验证
    /// </summary>
    public class RTZAxisPositionAccessor
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RTZAxisPositionAccessor));
        private readonly IS200McuCmdService _mcuCmdService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mcuCmdService">MCU命令服务实例</param>
        public RTZAxisPositionAccessor(IS200McuCmdService mcuCmdService)
        {
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
        }

        #region 基本位置访问（步进值）

        /// <summary>
        /// T轴当前步进位置值 - 实时数据
        /// </summary>
        public int CurrentTAxisStep => _mcuCmdService.CurrentTAxisStep;

        /// <summary>
        /// R轴当前步进位置值 - 实时数据
        /// </summary>
        public int CurrentRAxisStep => _mcuCmdService.CurrentRAxisStep;

        /// <summary>
        /// Z轴当前步进位置值 - 实时数据
        /// </summary>
        public int CurrentZAxisStep => _mcuCmdService.CurrentZAxisStep;

        #endregion

        #region 物理单位转换

        /// <summary>
        /// T旋转轴角度值（度） - 实时计算
        /// 转换公式：步进值/100000*360
        /// </summary>
        public double CurrentTAxisDegree => _mcuCmdService.CurrentTAxisDegree;

        /// <summary>
        /// R伸缩轴长度值（mm） - 实时计算
        /// 转换公式：L = Sin(步进值/50000*360)*2*208.96
        /// </summary>
        public double CurrentRAxisLength => _mcuCmdService.CurrentRAxisLength;

        /// <summary>
        /// Z上下轴高度值（mm） - 实时计算
        /// 转换公式：步进值/1000*5
        /// </summary>
        public double CurrentZAxisHeight => _mcuCmdService.CurrentZAxisHeight;

        #endregion

        #region 数据有效性检查

        /// <summary>
        /// RTZ轴位置数据是否有效
        /// </summary>
        public bool IsRTZPositionDataValid => _mcuCmdService.IsRTZPositionDataValid;

        #endregion

        #region 安全范围验证

        /// <summary>
        /// 所有轴是否都在安全范围内
        /// </summary>
        public bool AreAllAxesInSafeRange
        {
            get
            {
                try
                {
                    if (!IsRTZPositionDataValid)
                        return false;

                    return IsAxisPositionInSafeRange("T") &&
                           IsAxisPositionInSafeRange("R") &&
                           IsAxisPositionInSafeRange("Z");
                }
                catch (Exception ex)
                {
                    _logger.Error($"检查轴安全范围时发生错误: {ex.Message}", ex);
                    return false;
                }
            }
        }

        /// <summary>
        /// 检查指定轴的位置是否在安全范围内
        /// </summary>
        /// <param name="axisName">轴名称（T、R、Z）</param>
        /// <returns>是否在安全范围内</returns>
        public bool IsAxisPositionInSafeRange(string axisName)
        {
            try
            {
                if (!IsRTZPositionDataValid)
                    return false;

                switch (axisName?.ToUpper())
                {
                    case "T":
                        // T轴安全范围：-********* 到 ********* steps
                        return CurrentTAxisStep >= -********* && CurrentTAxisStep <= *********;
                    case "R":
                        // R轴安全范围：-100000 到 100000 steps
                        return CurrentRAxisStep >= -100000 && CurrentRAxisStep <= 100000;
                    case "Z":
                        // Z轴安全范围：-50000 到 50000 steps
                        return CurrentZAxisStep >= -50000 && CurrentZAxisStep <= 50000;
                    default:
                        _logger.Warn($"未知的轴名称: {axisName}");
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"检查轴 {axisName} 安全范围时发生错误: {ex.Message}", ex);
                return false;
            }
        }

        #endregion

        #region 组合访问方法

        /// <summary>
        /// 获取RTZ轴位置的组合信息（步进值）
        /// </summary>
        /// <returns>T轴、R轴、Z轴的步进值</returns>
        public async Task<(int TAxisStep, int RAxisStep, int ZAxisStep)> GetCurrentRTZSteps()
        {
            return await _mcuCmdService.GetCurrentRTZSteps();
        }

        /// <summary>
        /// 获取RTZ轴位置的组合信息（物理值）
        /// </summary>
        /// <returns>T轴角度、R轴长度、Z轴高度</returns>
        public async Task<(double TAxisDegree, double RAxisLength, double ZAxisHeight)> GetCurrentRTZPhysicalValues()
        {
            return await _mcuCmdService.GetCurrentRTZPhysicalValues();
        }

        #endregion

        #region 格式化显示方法

        /// <summary>
        /// 获取RTZ轴位置的详细显示文本
        /// </summary>
        /// <returns>格式化的位置信息文本</returns>
        public async Task<string> GetRTZPositionDisplayText()
        {
            try
            {
                if (!IsRTZPositionDataValid)
                    return "RTZ轴位置数据无效";

                var (tStep, rStep, zStep) = await GetCurrentRTZSteps();
                var (tDeg, rLen, zHeight) = await GetCurrentRTZPhysicalValues();

                return $"RTZ轴位置信息:\n" +
                       $"T轴: {tStep} steps ({tDeg:F2}°)\n" +
                       $"R轴: {rStep} steps ({rLen:F2}mm)\n" +
                       $"Z轴: {zStep} steps ({zHeight:F2}mm)\n" +
                       $"安全状态: {(AreAllAxesInSafeRange ? "安全" : "警告")}";
            }
            catch (Exception ex)
            {
                _logger.Error($"生成RTZ位置显示文本时发生错误: {ex.Message}", ex);
                return "获取RTZ轴位置信息失败";
            }
        }

        /// <summary>
        /// 获取RTZ轴位置的简化显示文本
        /// </summary>
        /// <returns>简化的位置信息文本</returns>
        public async Task<string> GetRTZPositionSimpleText()
        {
            try
            {
                if (!IsRTZPositionDataValid)
                    return "数据无效";

                var (tStep, rStep, zStep) = await GetCurrentRTZSteps();
                return $"T:{tStep}, R:{rStep}, Z:{zStep}";
            }
            catch (Exception ex)
            {
                _logger.Error($"生成RTZ位置简化文本时发生错误: {ex.Message}", ex);
                return "获取失败";
            }
        }

        /// <summary>
        /// 获取RTZ轴位置的JSON格式文本
        /// </summary>
        /// <returns>JSON格式的位置信息</returns>
        public async Task<string> GetRTZPositionJsonText()
        {
            try
            {
                if (!IsRTZPositionDataValid)
                    return "{\"valid\": false, \"message\": \"数据无效\"}";

                var (tStep, rStep, zStep) = await GetCurrentRTZSteps();
                var (tDeg, rLen, zHeight) = await GetCurrentRTZPhysicalValues();

                return $"{{" +
                       $"\"valid\": true, " +
                       $"\"tAxis\": {{\"step\": {tStep}, \"degree\": {tDeg:F2}}}, " +
                       $"\"rAxis\": {{\"step\": {rStep}, \"length\": {rLen:F2}}}, " +
                       $"\"zAxis\": {{\"step\": {zStep}, \"height\": {zHeight:F2}}}, " +
                       $"\"allSafe\": {AreAllAxesInSafeRange.ToString().ToLower()}" +
                       $"}}";
            }
            catch (Exception ex)
            {
                _logger.Error($"生成RTZ位置JSON文本时发生错误: {ex.Message}", ex);
                return "{\"valid\": false, \"message\": \"生成失败\"}";
            }
        }

        #endregion

        #region 详细信息获取

        /// <summary>
        /// 获取详细的RTZ轴位置信息
        /// </summary>
        /// <returns>详细位置信息对象</returns>
        public async Task<RTZAxisPositionInfo> GetDetailedPositionInfo()
        {
            try
            {
                if (!IsRTZPositionDataValid)
                {
                    return new RTZAxisPositionInfo
                    {
                        IsValid = false,
                        ErrorMessage = "RTZ轴位置数据无效"
                    };
                }

                var (tStep, rStep, zStep) = await GetCurrentRTZSteps();
                var (tDeg, rLen, zHeight) = await GetCurrentRTZPhysicalValues();

                return new RTZAxisPositionInfo
                {
                    IsValid = true,
                    TAxisStep = tStep,
                    RAxisStep = rStep,
                    ZAxisStep = zStep,
                    TAxisDegree = tDeg,
                    RAxisLength = rLen,
                    ZAxisHeight = zHeight,
                    IsTAxisSafe = IsAxisPositionInSafeRange("T"),
                    IsRAxisSafe = IsAxisPositionInSafeRange("R"),
                    IsZAxisSafe = IsAxisPositionInSafeRange("Z"),
                    IsAllAxesSafe = AreAllAxesInSafeRange,
                    Timestamp = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"获取详细RTZ位置信息时发生错误: {ex.Message}", ex);
                return new RTZAxisPositionInfo
                {
                    IsValid = false,
                    ErrorMessage = $"获取详细信息失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取诊断信息
        /// </summary>
        /// <returns>诊断信息文本</returns>
        public async Task<string> GetDiagnosticInfo()
        {
            try
            {
                var info = await GetDetailedPositionInfo();
                if (!info.IsValid)
                    return $"诊断信息: {info.ErrorMessage}";

                return $"RTZ轴诊断信息 [{info.Timestamp:yyyy-MM-dd HH:mm:ss}]:\n" +
                       $"数据有效性: {info.IsValid}\n" +
                       $"T轴: {info.TAxisStep} steps ({info.TAxisDegree:F2}°) - {(info.IsTAxisSafe ? "安全" : "警告")}\n" +
                       $"R轴: {info.RAxisStep} steps ({info.RAxisLength:F2}mm) - {(info.IsRAxisSafe ? "安全" : "警告")}\n" +
                       $"Z轴: {info.ZAxisStep} steps ({info.ZAxisHeight:F2}mm) - {(info.IsZAxisSafe ? "安全" : "警告")}\n" +
                       $"整体安全状态: {(info.IsAllAxesSafe ? "安全" : "警告")}";
            }
            catch (Exception ex)
            {
                _logger.Error($"生成诊断信息时发生错误: {ex.Message}", ex);
                return $"诊断信息生成失败: {ex.Message}";
            }
        }

        #endregion
    }
}
