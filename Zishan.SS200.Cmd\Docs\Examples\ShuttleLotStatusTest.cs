using System;
using System.Collections.Generic;
using Zishan.SS200.Cmd.Common;
using <PERSON>ishan.SS200.Cmd.Enums;
using <PERSON>ishan.SS200.Cmd.Enums.SS200.IOInterface.Shuttle;
using <PERSON>ishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// Shuttle批次状态测试示例
    /// 演示如何测试和验证批次状态功能
    /// </summary>
    public class ShuttleLotStatusTest
    {
        private readonly CoilStatusHelper _coilHelper;

        public ShuttleLotStatusTest()
        {
            // 注意：在实际使用中，CoilStatusHelper需要通过依赖注入获取
            // 这里仅作为示例展示
            _coilHelper = new CoilStatusHelper(null); // 需要传入实际的S200McuCmdService实例
        }

        /// <summary>
        /// 测试单个批次状态计算
        /// </summary>
        public void TestSingleLotStatusCalculation()
        {
            Console.WriteLine("=== 测试单个批次状态计算 ===");

            try
            {
                // 测试所有4个位置的批次状态
                var testCases = new[]
                {
                    new { Shuttle = 1, Cassette = 1, Description = "Shuttle1 Cassette1 (LSD1)" },
                    new { Shuttle = 1, Cassette = 2, Description = "Shuttle1 Cassette2 (LSD2)" },
                    new { Shuttle = 2, Cassette = 1, Description = "Shuttle2 Cassette1 (LSD3)" },
                    new { Shuttle = 2, Cassette = 2, Description = "Shuttle2 Cassette2 (LSD4)" }
                };

                foreach (var testCase in testCases)
                {
                    var lotStatus = _coilHelper.CalculateLotStatus(testCase.Shuttle, testCase.Cassette);
                    var isPresent = _coilHelper.IsCassettePresent(testCase.Shuttle, testCase.Cassette);
                    
                    Console.WriteLine($"{testCase.Description}:");
                    Console.WriteLine($"  批次状态: {lotStatus}");
                    Console.WriteLine($"  晶圆盒存在: {(isPresent ? "是" : "否")}");
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                AppLog.Error($"测试单个批次状态计算时发生错误: {ex.Message}");
                Console.WriteLine($"错误: {ex.Message}");
            }

            Console.WriteLine("=====================================");
        }

        /// <summary>
        /// 测试批量批次状态计算
        /// </summary>
        public void TestBatchLotStatusCalculation()
        {
            Console.WriteLine("=== 测试批量批次状态计算 ===");

            try
            {
                // 获取所有批次状态
                var allLotStatus = _coilHelper.CalculateAllLotStatus();
                Console.WriteLine("所有批次状态:");
                foreach (var kvp in allLotStatus)
                {
                    Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
                }

                Console.WriteLine();

                // 获取所有晶圆盒存在状态
                var allPresenceStatus = _coilHelper.GetAllCassettePresenceStatus();
                Console.WriteLine("所有晶圆盒存在状态:");
                foreach (var kvp in allPresenceStatus)
                {
                    Console.WriteLine($"  {kvp.Key}: {(kvp.Value ? "存在" : "不存在")}");
                }
            }
            catch (Exception ex)
            {
                AppLog.Error($"测试批量批次状态计算时发生错误: {ex.Message}");
                Console.WriteLine($"错误: {ex.Message}");
            }

            Console.WriteLine("=====================================");
        }

        /// <summary>
        /// 测试完整的Shuttle状态更新（包含批次状态）
        /// </summary>
        public void TestCompleteShuttleStatusUpdate()
        {
            Console.WriteLine("=== 测试完整Shuttle状态更新 ===");

            try
            {
                var shuttleStatus = new ShuttleSubsystemStatus();

                // 1. 基本状态
                shuttleStatus.ShuttleStatus = _coilHelper.CalculateShuttleStatus();

                // 2. 位置状态
                shuttleStatus.ShuttlePositionStatus = _coilHelper.CalculateShuttlePositionStatus(
                    shuttleStatus.Ssc6Config);

                // 3. 门巢状态
                shuttleStatus.CassetteDoorNestStatus = _coilHelper.CalculateCassetteDoorNestStatus(
                    shuttleStatus.Ssc6Config);

                // 4. 批次状态（重点测试）
                shuttleStatus.Shuttle1Cassette1LotStatus = _coilHelper.CalculateLotStatus(1, 1);
                shuttleStatus.Shuttle1Cassette2LotStatus = _coilHelper.CalculateLotStatus(1, 2);
                shuttleStatus.Shuttle2Cassette1LotStatus = _coilHelper.CalculateLotStatus(2, 1);
                shuttleStatus.Shuttle2Cassette2LotStatus = _coilHelper.CalculateLotStatus(2, 2);

                // 输出结果
                Console.WriteLine("完整Shuttle状态:");
                Console.WriteLine($"  基本状态: {shuttleStatus.ShuttleStatus}");
                Console.WriteLine($"  位置状态: {shuttleStatus.ShuttlePositionStatus}");
                Console.WriteLine($"  门巢状态: {shuttleStatus.CassetteDoorNestStatus}");
                Console.WriteLine($"  批次状态:");
                Console.WriteLine($"    LSD1 (Shuttle1 Cassette1): {shuttleStatus.Shuttle1Cassette1LotStatus}");
                Console.WriteLine($"    LSD2 (Shuttle1 Cassette2): {shuttleStatus.Shuttle1Cassette2LotStatus}");
                Console.WriteLine($"    LSD3 (Shuttle2 Cassette1): {shuttleStatus.Shuttle2Cassette1LotStatus}");
                Console.WriteLine($"    LSD4 (Shuttle2 Cassette2): {shuttleStatus.Shuttle2Cassette2LotStatus}");
            }
            catch (Exception ex)
            {
                AppLog.Error($"测试完整Shuttle状态更新时发生错误: {ex.Message}");
                Console.WriteLine($"错误: {ex.Message}");
            }

            Console.WriteLine("=====================================");
        }

        /// <summary>
        /// 测试边界条件和错误处理
        /// </summary>
        public void TestEdgeCasesAndErrorHandling()
        {
            Console.WriteLine("=== 测试边界条件和错误处理 ===");

            try
            {
                // 测试无效的Shuttle编号
                Console.WriteLine("测试无效Shuttle编号:");
                var invalidShuttleResult = _coilHelper.CalculateLotStatus(0, 1);
                Console.WriteLine($"  Shuttle0 Cassette1: {invalidShuttleResult}");

                var invalidShuttleResult2 = _coilHelper.CalculateLotStatus(3, 1);
                Console.WriteLine($"  Shuttle3 Cassette1: {invalidShuttleResult2}");

                // 测试无效的晶圆盒编号
                Console.WriteLine("测试无效晶圆盒编号:");
                var invalidCassetteResult = _coilHelper.CalculateLotStatus(1, 0);
                Console.WriteLine($"  Shuttle1 Cassette0: {invalidCassetteResult}");

                var invalidCassetteResult2 = _coilHelper.CalculateLotStatus(1, 3);
                Console.WriteLine($"  Shuttle1 Cassette3: {invalidCassetteResult2}");

                // 测试晶圆盒存在状态的边界条件
                Console.WriteLine("测试晶圆盒存在状态边界条件:");
                bool invalidPresence1 = _coilHelper.IsCassettePresent(0, 1);
                bool invalidPresence2 = _coilHelper.IsCassettePresent(1, 0);
                Console.WriteLine($"  IsCassettePresent(0, 1): {invalidPresence1}");
                Console.WriteLine($"  IsCassettePresent(1, 0): {invalidPresence2}");
            }
            catch (Exception ex)
            {
                AppLog.Error($"测试边界条件时发生错误: {ex.Message}");
                Console.WriteLine($"错误: {ex.Message}");
            }

            Console.WriteLine("=====================================");
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public void RunAllTests()
        {
            Console.WriteLine("开始运行Shuttle批次状态测试...");
            Console.WriteLine();

            TestSingleLotStatusCalculation();
            TestBatchLotStatusCalculation();
            TestCompleteShuttleStatusUpdate();
            TestEdgeCasesAndErrorHandling();

            Console.WriteLine("所有测试完成！");
        }
    }

    /// <summary>
    /// 测试程序入口点示例
    /// </summary>
    public static class ShuttleLotStatusTestRunner
    {
        /// <summary>
        /// 运行测试的示例方法
        /// </summary>
        public static void RunTests()
        {
            try
            {
                var tester = new ShuttleLotStatusTest();
                tester.RunAllTests();
            }
            catch (Exception ex)
            {
                AppLog.Error($"运行Shuttle批次状态测试时发生错误: {ex.Message}");
                Console.WriteLine($"测试运行失败: {ex.Message}");
            }
        }
    }
}
