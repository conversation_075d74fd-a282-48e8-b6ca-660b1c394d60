﻿<UserControl
    x:Class="Zishan.SS200.Cmd.Views.Dialogs.WaferInfoDisplay"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    d:DesignHeight="1290"
    d:DesignWidth="2540"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="25*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="auto" />
        </Grid.ColumnDefinitions>

        <!--  标题  -->
        <TextBlock
            Grid.ColumnSpan="2"
            HorizontalAlignment="Center"
            FontSize="24"
            FontWeight="Bold"
            Text="{Binding Title}" />

        <!--  Cassette Wafer状态  -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="999*" />
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="566*" />
                <ColumnDefinition Width="432*" />
            </Grid.ColumnDefinitions>
            <Grid Grid.Column="0" Margin="10,10,10,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <TextBlock
                    FontSize="18"
                    FontWeight="Bold"
                    Text="Left Cassette WaferInfo" />

                <ListView Grid.Row="1" ItemsSource="{Binding PlcCurRunStatus.CassetteOldInfos}">
                    <ListView.View>
                        <GridView>
                            <GridViewColumn DisplayMemberBinding="{Binding LeftWaferInfo.SlotNo}" Header="槽号" />
                            <GridViewColumn DisplayMemberBinding="{Binding LeftWaferInfo.Name}" Header="名字" />
                            <GridViewColumn Header="Wafer真实状态">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="Maping状态">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LeftWaferInfo.MapingStatus}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding LeftWaferInfo.MapingStatus}" Value="Exist">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn DisplayMemberBinding="{Binding LeftWaferInfo.ProcessStatus}" Header="Wafer状态" />
                            <GridViewColumn Header="晶圆存在标识">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LeftWaferInfo.PosWaferAtCS_Exist}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding LeftWaferInfo.PosWaferAtCS_Exist}" Value="True">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="叠片">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LeftWaferInfo.PosWaferAtCS_Lamination}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding LeftWaferInfo.PosWaferAtCS_Lamination}" Value="False">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="太薄">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LeftWaferInfo.PosWaferAtCS_Thin}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding LeftWaferInfo.PosWaferAtCS_Thin}" Value="False">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="斜片">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LeftWaferInfo.PosWaferAtCS_Askew}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding LeftWaferInfo.PosWaferAtCS_Askew}" Value="False">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="扫片异常">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LeftWaferInfo.PosWaferAtCS_ScanError}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding LeftWaferInfo.PosWaferAtCS_ScanError}" Value="False">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                        </GridView>
                    </ListView.View>
                </ListView>
            </Grid>

            <!--  左右分割栏移动  -->
            <GridSplitter
                Grid.RowSpan="1"
                Grid.Column="1"
                Width="5"
                HorizontalAlignment="Center" />

            <Grid
                Grid.Column="2"
                Grid.ColumnSpan="2"
                Margin="10,10,10,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <TextBlock
                    FontSize="18"
                    FontWeight="Bold"
                    Text="Right Cassette WaferInfo" />

                <ListView Grid.Row="1" ItemsSource="{Binding PlcCurRunStatus.CassetteOldInfos}">
                    <ListView.View>
                        <GridView>
                            <GridViewColumn DisplayMemberBinding="{Binding RightWaferInfo.SlotNo}" Header="槽号" />
                            <GridViewColumn DisplayMemberBinding="{Binding RightWaferInfo.Name}" Header="名字" />
                            <GridViewColumn Header="真实状态">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="Maping状态">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding RightWaferInfo.MapingStatus}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RightWaferInfo.MapingStatus}" Value="Exist">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn DisplayMemberBinding="{Binding RightWaferInfo.ProcessStatus}" Header="Wafer状态" />
                            <GridViewColumn Header="晶圆存在标识">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding RightWaferInfo.PosWaferAtCS_Exist}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RightWaferInfo.PosWaferAtCS_Exist}" Value="True">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="叠片">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding RightWaferInfo.PosWaferAtCS_Lamination}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RightWaferInfo.PosWaferAtCS_Lamination}" Value="False">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="太薄">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding RightWaferInfo.PosWaferAtCS_Thin}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RightWaferInfo.PosWaferAtCS_Thin}" Value="False">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="斜片">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding RightWaferInfo.PosWaferAtCS_Askew}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RightWaferInfo.PosWaferAtCS_Askew}" Value="False">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="扫片异常">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding RightWaferInfo.PosWaferAtCS_ScanError}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RightWaferInfo.PosWaferAtCS_ScanError}" Value="False">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                        </GridView>
                    </ListView.View>
                </ListView>
            </Grid>
        </Grid>

        <!--  CHA Wafer状态  -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <GroupBox Header="CHA Left WaferInfo" Style="{StaticResource GroupBoxTab}">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.Cha}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding LeftWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding LeftWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>

            <GroupBox Grid.Column="1" Header="CHA Right WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.Cha}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding RightWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding RightWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>
        </Grid>

        <!--  CHB Wafer状态  -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <GroupBox Header="CHB Left WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.Chb}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding LeftWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding LeftWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>

            <GroupBox Grid.Column="1" Header="CHB Right WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.Chb}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding RightWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding RightWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>
        </Grid>

        <!--  CHC Wafer状态  -->
        <Grid Grid.Row="4">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <GroupBox Header="CHC Left WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.Chc}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding LeftWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding LeftWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>

            <GroupBox Grid.Column="1" Header="CHC Right WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.Chc}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding RightWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding RightWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>
        </Grid>

        <!--  Cooling Wafer状态  -->
        <Grid Grid.Row="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <GroupBox Header="Cooling Left WaferInfo" Style="{StaticResource GroupBoxTab}">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.Cooling}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding LeftWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding LeftWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>

            <GroupBox Grid.Column="1" Header="Cooling Right WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.Cooling}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding RightWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding RightWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>
        </Grid>

        <!--  RobotNose Wafer状态  -->
        <Grid Grid.Row="6">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <GroupBox Header="RobotNose Left WaferInfo" Style="{StaticResource GroupBoxTab}">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.RobotNose}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding LeftWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding LeftWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>

            <GroupBox Grid.Column="1" Header="RobotNose Right WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.RobotNose}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding RightWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding RightWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>
        </Grid>

        <!--  RobotSmooth Wafer状态  -->
        <Grid Grid.Row="7">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <GroupBox Header="RobotSmooth Left WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.RobotSmooth}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding LeftWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding LeftWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>

            <GroupBox Grid.Column="1" Header="RobotSmooth Right WaferInfo">
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="4">
                    <Grid Margin="10" DataContext="{Binding PlcCurRunStatus.RobotSmooth}">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="SlotNo" />
                        <TextBlock
                            Grid.Column="1"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Name" />
                        <TextBlock
                            Grid.Column="2"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="Wafer_ExistStatus" />

                        <TextBlock Grid.Row="1" Text="{Binding RightWaferInfo.SlotNo}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="{Binding RightWaferInfo.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="{Binding LeftWaferInfo.Wafer_ExistStatus}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="Red" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LeftWaferInfo.Wafer_ExistStatus}" Value="Have">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Border>
            </GroupBox>
        </Grid>

        <Grid Grid.Row="8" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <!--  按钮操作  -->
            <!--<StackPanel Margin="10" Orientation="Horizontal">
                <Button
                    Width="100"
                    Height="40"
                    Margin="10,0"
                    Background="#0D3E60"
                    Command="{Binding RunCmd}"
                    CommandParameter="BeforeAdd"
                    Content="在前新增"
                    Cursor="Hand"
                    IsEnabled="{Binding BtnEnableStatus.BeforeAdd, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    Style="{StaticResource ButtonPrimary.Small}" />
                <Button
                    Width="100"
                    Height="40"
                    Margin="10,0"
                    Background="#0D3E60"
                    Command="{Binding RunCmd}"
                    CommandParameter="AfterAdd"
                    Content="在后新增"
                    Cursor="Hand"
                    IsEnabled="{Binding BtnEnableStatus.AfterAdd, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    Style="{StaticResource ButtonPrimary.Small}" />

                <Button
                    Width="100"
                    Height="40"
                    Margin="10,0"
                    Background="#0D3E60"
                    Command="{Binding RunCmd}"
                    CommandParameter="DeleteCurItem"
                    Content="删除当前项"
                    Cursor="Hand"
                    IsEnabled="{Binding BtnEnableStatus.DeleteCurItem, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    Style="{StaticResource ButtonWarning.Small}"
                    ToolTip="快捷键CTRL+D" />
            </StackPanel>-->

            <!--  按钮操作  -->
            <StackPanel
                Grid.Column="1"
                Margin="10"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Button
                    Width="100"
                    Height="40"
                    Margin="10,0"
                    Command="{Binding RunCmd}"
                    CommandParameter="Update"
                    Content="Update"
                    Cursor="Hand"
                    IsEnabled="{Binding BtnEnableStatus.Save, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    Style="{StaticResource ButtonSuccess}" />

                <!--<Button
                    Width="100"
                    Height="40"
                    Margin="10,0"
                    Background="#007272"
                    Command="{Binding RunCmd}"
                    CommandParameter="Clear"
                    Content="清空"
                    Cursor="Hand"
                    IsEnabled="{Binding BtnEnableStatus.Clear, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    Style="{StaticResource ButtonWarning}" />-->
            </StackPanel>
        </Grid>

        <Border
            Grid.Row="1"
            Grid.RowSpan="99"
            Grid.Column="1">
            <!--  Cassette Wafer状态  -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0" Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        FontSize="18"
                        FontWeight="Bold"
                        Text="Real Left Cassette WaferInfo" />

                    <ListView Grid.Row="1" ItemsSource="{Binding PlcCurRunStatus.CassetteInfos}">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn DisplayMemberBinding="{Binding LeftWaferInfo.SlotNo}" Header="槽号" />
                                <GridViewColumn DisplayMemberBinding="{Binding LeftWaferInfo.PosName}" Header="槽号" />
                                <GridViewColumn DisplayMemberBinding="{Binding LeftWaferInfo.PosLeftOrRight}" Header="名字" />
                                <GridViewColumn DisplayMemberBinding="{Binding LeftWaferInfo.PosSlot}" Header="名字" />
                            </GridView>
                        </ListView.View>
                    </ListView>
                </Grid>

                <!--  左右分割栏移动  -->
                <GridSplitter
                    Grid.RowSpan="1"
                    Grid.Column="1"
                    Width="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Stretch" />

                <Grid Grid.Column="2" Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        FontSize="18"
                        FontWeight="Bold"
                        Text="Real Left Cassette WaferInfo" />

                    <ListView Grid.Row="1" ItemsSource="{Binding PlcCurRunStatus.CassetteInfos}">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn DisplayMemberBinding="{Binding RightWaferInfo.SlotNo}" Header="SlotNo" />
                                <GridViewColumn DisplayMemberBinding="{Binding RightWaferInfo.PosName}" Header="PosName" />
                                <GridViewColumn DisplayMemberBinding="{Binding RightWaferInfo.PosLeftOrRight}" Header="PosLeftOrRight" />
                                <GridViewColumn DisplayMemberBinding="{Binding RightWaferInfo.PosSlot}" Header="PosSlot" />
                            </GridView>
                        </ListView.View>
                    </ListView>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>