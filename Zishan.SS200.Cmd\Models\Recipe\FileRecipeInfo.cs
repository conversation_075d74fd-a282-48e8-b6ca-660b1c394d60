﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models.Recipe;

namespace Zishan.SS200.Cmd.Models.Recipe
{
    public class FileRecipeInfo
    {
        public string FileName { get; set; }

        public AllFlow RecipeAllFlow { get; set; }

        public FileRecipeInfo(string fileName, AllFlow recipeAllFlow)
        {
            FileName = fileName;
            RecipeAllFlow = recipeAllFlow;
        }

        public override string ToString()
        {
            return $"FileName:{FileName},RecipeAllFlow:{RecipeAllFlow}";
        }
    }
}