using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Markup;
using System.IO;

namespace Zishan.SS200.Cmd.Views
{
    /// <summary>
    /// IntegerConversionView.xaml 的交互逻辑
    /// </summary>
    public partial class IntegerConversionView : UserControl
    {
        public IntegerConversionView()
        {
            try
            {
                InitializeComponent();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化IntegerConversionView失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
} 