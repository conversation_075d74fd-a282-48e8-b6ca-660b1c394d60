using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot
{
    /// <summary>
    /// Robot状态枚举
    /// </summary>
    public enum EnuRobotStatus
    {
        /// <summary>
        /// 空闲状态
        /// </summary>
        [Description("空闲")]
        Idle = 0,

        /// <summary>
        /// 忙碌状态
        /// </summary>
        [Description("忙碌")]
        Busy = 1,

        /// <summary>
        /// 报警状态
        /// </summary>
        [Description("报警")]
        Alarm = 2,

        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0
    }
}