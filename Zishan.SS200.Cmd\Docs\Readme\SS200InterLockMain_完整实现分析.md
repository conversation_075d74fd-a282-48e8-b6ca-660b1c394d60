# SS200InterLockMain 完整实现分析与工作原理详解

## 概述

`SS200InterLockMain` 是SS200半导体设备控制系统的核心统一访问入口，采用单例模式设计，为整个系统提供对设备IO、报警信息、配置参数、状态信息的类型安全、高性能访问接口。

## 核心设计架构

### 1. 设计模式组合

#### 单例模式 (Singleton Pattern)
```csharp
private static readonly Lazy<SS200InterLockMain> _instance =
    new Lazy<SS200InterLockMain>(() => new SS200InterLockMain(), true);

public static SS200InterLockMain Instance => _instance.Value;
```

**特点**：
- 使用 `Lazy<T>` 实现线程安全的延迟初始化
- 确保全局唯一实例，避免重复创建
- 自动处理多线程并发访问的同步问题

#### 访问器模式 (Accessor Pattern)
```csharp
public class IOPropertyAccessor<TEnum> where TEnum : Enum
{
    public bool Value => _coilHelper.GetCoilValue(_deviceType, _enumValue);
    public string Content => _description;
    public bool? ValueNullable => _coilHelper.GetCoilValueNullable(_deviceType, _enumValue);
    public bool IsActive => _coilHelper.IsCoilActive(_deviceType, _enumValue);
}
```

**优势**：
- 提供统一的属性访问接口
- 动态计算属性实时获取设备状态
- 类型安全，支持泛型约束

#### 工厂模式 (Factory Pattern)
```csharp
private IOPropertyAccessor<EnuRobotDICodes> GetOrCreateAccessor(EnuRobotDICodes enumValue)
{
    string key = enumValue.ToString();
    return (IOPropertyAccessor<EnuRobotDICodes>)_cache.GetOrAdd(key,
        _ => new IOPropertyAccessor<EnuRobotDICodes>(_coilHelper, EnuMcuDeviceType.Robot, enumValue));
}
```

**特点**：
- 使用 `ConcurrentDictionary` 实现线程安全缓存
- 避免重复创建相同的访问器实例
- 支持不同设备类型的访问器创建

### 2. 系统架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    SS200InterLockMain                      │
│                     (单例入口)                              │
├─────────────────────────────────────────────────────────────┤
│  IOInterface    │  AlarmCode     │  SubsystemConfigure      │
│  报警代码访问    │  子系统配置     │  子系统状态              │
├─────────────────────────────────────────────────────────────┤
│  Robot访问器     │  Chamber访问器  │  Shuttle访问器           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   RDI1-4    │  │   PDI1-15   │  │   SDI1-28   │         │
│  │   RDO1-16   │  │   PDO1-16   │  │   SDO1-25   │         │
│  │   RA1-32    │  │   CA1-32    │  │   SA1-32    │         │
│  │   RS1-32    │  │   CS1-32    │  │   SS1-32    │         │
│  │   RP1-28    │  │   CP1-32    │  │   SP1-32    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件详解

### 1. 属性访问器基类

#### IOPropertyAccessor<TEnum>
- **功能**：提供IO设备的统一访问接口
- **核心属性**：
  - `Value`: 动态计算属性，实时从设备获取当前值
  - `Content`: 枚举的描述信息，直接从DescriptionAttribute获取
  - `ValueNullable`: 可空的线圈值
  - `IsActive`: 检查线圈是否激活

#### AlarmPropertyAccessor
- **功能**：提供报警信息的统一访问接口
- **核心属性**：
  - `Content`: 报警内容（英文）
  - `ChsContent`: 报警内容（中文描述）
  - `Cause`: 报警原因
  - `Code`: 报警代码
  - `Item`: 报警项ID

#### ConfigPropertyAccessor
- **功能**：提供配置参数的统一访问接口
- **核心属性**：
  - `Value`: 配置值
  - `Content`: 配置描述
  - `Unit`: 配置单位
  - `Code`: 配置代码
  - `AxisType`: 轴类型

#### StatusPropertyAccessor<TStatus>
- **功能**：提供状态信息的统一访问接口
- **核心属性**：
  - `Value`: 动态计算的状态值
  - `Content`: 状态描述

### 2. 设备IO访问器

#### RobotIOAccessor
```csharp
public IOPropertyAccessor<EnuRobotDICodes> RDI1_PaddleSensor1Left =>
    GetOrCreateAccessor(EnuRobotDICodes.RDI1_PaddleSensor1Left, "Paddle传感器1左侧");
```

**支持的IO类型**：
- RDI1-4: Robot数字输入
- RDO1-16: Robot数字输出
- 传感器状态监控
- 执行器控制

#### ChamberIOAccessor
```csharp
public IOPropertyAccessor<EnuChamberDICodes> PDI12_SlitDoorOpenSensor =>
    GetOrCreateAccessor(EnuChamberDICodes.PDI12_SlitDoorOpenSensor, "Slit Door Open传感器");
```

**支持的设备**：
- ChamberA: 腔体A的IO控制
- ChamberB: 腔体B的IO控制
- Slit Door控制
- Lift Pin控制

#### ShuttleIOAccessor
```csharp
public IOPropertyAccessor<EnuShuttleDICodes> SDI6_PresentSensorCassette1 =>
    GetOrCreateAccessor(EnuShuttleDICodes.SDI6_PresentSensorCassette1, "存在传感器晶圆盒1");
```

**功能**：
- 晶圆盒存在检测
- Shuttle位置监控
- 传输状态控制

### 3. 报警访问器

#### RobotAlarmAccessor
```csharp
public AlarmPropertyAccessor RA1_SystemBusyReject =>
    GetOrCreateAccessor(EnuRobotAlarmCodes.RA1);
```

**报警类型**：
- RA1: 系统忙拒绝
- RA2: 系统报警拒绝
- RA3: R轴不在原点位置错误

#### ChamberAlarmAccessor
```csharp
public AlarmPropertyAccessor PAC1_SystemAbnormalReject =>
    GetOrCreateAccessor(EnuChaAlarmCodes.PAC1);
```

**报警类型**：
- PAC1: 腔体系统异常拒绝
- PAC2: 腔体或机器人忙拒绝

#### ShuttleAlarmAccessor
```csharp
public AlarmPropertyAccessor SA1_SystemBusyReject =>
    GetOrCreateAccessor(EnuShuttleAlarmCodes.SA1);
```

**报警类型**：
- SA1: Shuttle系统忙碌拒绝
- SA2: Shuttle系统报警拒绝
- SA3: 卡匣巢移动超时
- SA4: 卡匣巢移动速度过快
- SA5: 卡匣巢位置条件失败
- SA6: Shuttle移动超时
- SA7: Shuttle移动过快
- SA8: Shuttle上下位置条件失败
- SA9: Shuttle旋转超时

### 4. 配置访问器

#### PositionValueAccessor
```csharp
public ConfigPropertyAccessor RP1_TAxisPosition =>
    GetOrCreateAccessor("RP1");
```

**配置参数**：
- RP1-RP9: T轴位置参数
- RP10-RP18: R轴位置参数
- RP19-RP28: Z轴位置参数

### 5. 状态访问器

#### RobotStatusAccessor
```csharp
public StatusPropertyAccessor<EnuRobotStatus> RS1_RobotStatus =>
    GetOrCreateStatusAccessor("RS1", () => _statusGetter()?.EnuRobotStatus ?? EnuRobotStatus.Idle, "Robot主状态");
```

#### ChamberStatusAccessor
```csharp
public StatusPropertyAccessor<ChamberEnuSlitDoorStatus> SlitDoorStatus =>
    GetOrCreateStatusAccessor("SlitDoor", () => _statusGetter()?.SlitDoorStatus ?? ChamberEnuSlitDoorStatus.None, "Slit Door状态");
```

## 初始化流程

### 1. 单例创建流程
1. 首次访问 `SS200InterLockMain.Instance`
2. `Lazy<T>` 检查是否已初始化
3. 调用私有构造函数创建实例
4. 初始化各个访问器
5. 加载配置数据
6. 返回完整初始化的实例

### 2. 配置加载流程
```csharp
private void LoadRobotPositionConfiguration()
{
    var provider = RobotPositionParametersProvider.Instance;
    
    for (int i = 1; i <= 28; i++)
    {
        string code = $"RP{i}";
        if (Enum.TryParse<EnuRobotPositionParameterCodes>(code, out var enumCode))
        {
            int value = provider.GetParameterValue(enumCode);
            var setting = new ConfigureSetting
            {
                Id = i,
                Code = code,
                Description = GetRobotPositionDescription(code),
                Value = value,
                Unit = "step",
                AxisType = GetAxisTypeForRP(code)
            };
            RobotPositionValue.Add(setting);
        }
    }
}
```

## 使用示例

### 1. 基本IO访问
```csharp
// 获取Robot Paddle传感器状态
bool paddleStatus = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
string paddleDesc = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Content;

// 获取Chamber Slit Door状态
bool slitDoorOpen = SS200InterLockMain.Instance.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
```

### 2. 报警信息访问
```csharp
// 获取Robot报警信息
var robotAlarm = SS200InterLockMain.Instance.AlarmCode.Robot.RA1_SystemBusyReject;
string alarmContent = robotAlarm.Content;      // 英文描述
string alarmChsContent = robotAlarm.ChsContent; // 中文描述
string alarmCode = robotAlarm.Code;            // 报警代码
```

### 3. 配置参数访问
```csharp
// 获取T轴位置配置
var tAxisConfig = SS200InterLockMain.Instance.SubsystemConfigure.PositionValue.RP1_TAxisPosition;
int tAxisValue = tAxisConfig.Value;        // 配置值
string tAxisDesc = tAxisConfig.Content;    // 配置描述
string tAxisUnit = tAxisConfig.Unit;       // 配置单位
```

### 4. 状态信息访问
```csharp
// 获取Robot状态
var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.RS1_RobotStatus.Value;

// 获取Chamber状态
var slitDoorStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberA.SlitDoorStatus.Value;
var liftPinStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberA.LiftPinStatus.Value;
```

## 性能优化特性

### 1. 缓存机制
- 使用 `ConcurrentDictionary` 缓存访问器实例
- 避免重复创建相同的访问器对象
- 提高访问性能，减少内存占用

### 2. 延迟初始化
- 使用 `Lazy<T>` 实现延迟初始化
- 只在首次访问时创建实例
- 减少应用程序启动时间

### 3. 线程安全
- 单例模式使用线程安全的 `Lazy<T>`
- 缓存使用线程安全的 `ConcurrentDictionary`
- 支持多线程并发访问

## 扩展性设计

### 1. 添加新设备类型
1. 定义新的枚举类型
2. 创建对应的访问器类
3. 在主访问器中集成
4. 更新配置提供者

### 2. 添加新数据类型
1. 创建新的属性访问器
2. 实现数据获取逻辑
3. 集成到主类中
4. 编写单元测试

## 总结

`SS200InterLockMain` 通过精心设计的架构模式，为SS200半导体设备控制系统提供了：

1. **统一访问接口**：所有设备信息通过一个入口访问
2. **类型安全**：强类型访问，编译时检查
3. **高性能**：缓存机制和延迟初始化
4. **线程安全**：支持多线程环境
5. **易于扩展**：支持新设备和数据类型
6. **易于维护**：清晰的架构和职责分离

这种设计模式为复杂的半导体设备控制系统提供了一个强大、灵活且易于使用的统一访问框架。
