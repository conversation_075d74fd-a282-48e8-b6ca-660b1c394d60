﻿using System.ComponentModel;

using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 去腔体取放动作
    /// </summary>
    [TypeConverter(typeof(EnuChamberGoAction))]
    public enum EnuChamberGoAction
    {
        /// <summary>
        /// 去腔体取
        /// </summary>
        [Description("Get")]
        Get = 0,

        /// <summary>
        /// 去腔体放
        /// </summary>
        [Description("Put")]
        Put = 1,

        /// <summary>
        /// 回原点
        /// </summary>
        [Description("Home")]
        Home = 2,

        /// <summary>
        /// 去腔体无动作
        /// </summary>
        [Description("None")]
        None = 3
    }
}