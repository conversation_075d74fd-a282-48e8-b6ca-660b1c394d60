﻿using Prism.Services.Dialogs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.ViewModels.DialogViewModels.DesignViewModels
{
    internal class WaferInfoDisplayViewDesignViewModel : WaferInfoDisplayViewModel
    {
        private static WaferInfoDisplayViewDesignViewModel _Instance;
        public static WaferInfoDisplayViewDesignViewModel Instance => _Instance ??= new();

        private readonly IDialogService _dialogService;

        public WaferInfoDisplayViewDesignViewModel(IDialogService dialogService = null)
        {
            _dialogService = dialogService;
        }
    }
}