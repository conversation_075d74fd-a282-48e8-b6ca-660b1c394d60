using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// SS200ConfigurationValidator测试类
    /// 用于验证配置验证器的功能
    /// </summary>
    public class SS200ConfigurationValidatorTest
    {
        /// <summary>
        /// 测试完整的配置验证功能
        /// </summary>
        public static async Task TestCompleteValidation()
        {
            try
            {
                Console.WriteLine("=== SS200配置验证器完整测试 ===");
                Console.WriteLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine();

                // 创建验证器实例
                var validator = new SS200ConfigurationValidator();

                Console.WriteLine("1. 测试启动时验证...");
                var startupResult = validator.ValidateOnStartup(blAlwaysShowResult: true);
                Console.WriteLine($"启动验证结果: {(startupResult.IsValid ? "✅ 通过" : "❌ 失败")}");
                Console.WriteLine($"错误数量: {startupResult.Errors.Count}");
                Console.WriteLine($"警告数量: {startupResult.Warnings.Count}");
                Console.WriteLine($"信息数量: {startupResult.InfoMessages.Count}");
                Console.WriteLine();

                Console.WriteLine("2. 测试完整验证...");
                var completeResult = validator.ValidateAll();
                Console.WriteLine($"完整验证结果: {(completeResult.IsValid ? "✅ 通过" : "❌ 失败")}");
                Console.WriteLine();

                Console.WriteLine("3. 测试报警代码验证...");
                var alarmResult = validator.ValidateAlarmCodes();
                Console.WriteLine($"报警代码验证结果: {(alarmResult.IsValid ? "✅ 通过" : "❌ 失败")}");
                DisplayValidationDetails(alarmResult, "报警代码验证");
                Console.WriteLine();

                Console.WriteLine("4. 测试配置参数验证...");
                var configResult = validator.ValidateConfigurationParameters();
                Console.WriteLine($"配置参数验证结果: {(configResult.IsValid ? "✅ 通过" : "❌ 失败")}");
                DisplayValidationDetails(configResult, "配置参数验证");
                Console.WriteLine();

                Console.WriteLine("5. 测试数据导出...");
                validator.ExportCurrentDataToJson();
                Console.WriteLine("✅ 数据导出完成");
                Console.WriteLine();

                Console.WriteLine("=== 测试完成 ===");
                Console.WriteLine($"总体结果: {(startupResult.IsValid && completeResult.IsValid ? "✅ 所有测试通过" : "⚠️ 部分测试有警告")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试Robot报警代码验证
        /// </summary>
        public static void TestRobotAlarmValidation()
        {
            try
            {
                Console.WriteLine("=== Robot报警代码验证测试 ===");

                var validator = new SS200ConfigurationValidator();
                var result = validator.ValidateAlarmCodes();

                Console.WriteLine($"验证结果: {(result.IsValid ? "✅ 通过" : "❌ 失败")}");
                DisplayValidationDetails(result, "Robot报警代码");

                // 显示前5个信息消息作为示例
                if (result.InfoMessages.Count > 0)
                {
                    Console.WriteLine("\n前5个验证信息示例:");
                    for (int i = 0; i < Math.Min(5, result.InfoMessages.Count); i++)
                    {
                        Console.WriteLine($"  {i + 1}. {result.InfoMessages[i]}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Robot报警代码验证测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试Robot位置参数验证
        /// </summary>
        public static void TestRobotPositionValidation()
        {
            try
            {
                Console.WriteLine("=== Robot位置参数验证测试 ===");

                var validator = new SS200ConfigurationValidator();
                var result = validator.ValidateConfigurationParameters();

                Console.WriteLine($"验证结果: {(result.IsValid ? "✅ 通过" : "❌ 失败")}");
                DisplayValidationDetails(result, "Robot位置参数");

                // 显示前5个信息消息作为示例
                if (result.InfoMessages.Count > 0)
                {
                    Console.WriteLine("\n前5个验证信息示例:");
                    for (int i = 0; i < Math.Min(5, result.InfoMessages.Count); i++)
                    {
                        Console.WriteLine($"  {i + 1}. {result.InfoMessages[i]}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Robot位置参数验证测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示验证结果详情
        /// </summary>
        private static void DisplayValidationDetails(SS200ConfigurationValidator.ValidationResult result, string testName)
        {
            if (result.Errors.Count > 0)
            {
                Console.WriteLine($"{testName} - 错误 ({result.Errors.Count}):");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"  ❌ {error}");
                }
            }

            if (result.Warnings.Count > 0)
            {
                Console.WriteLine($"{testName} - 警告 ({result.Warnings.Count}):");
                foreach (var warning in result.Warnings)
                {
                    Console.WriteLine($"  ⚠️ {warning}");
                }
            }

            if (result.InfoMessages.Count > 0)
            {
                Console.WriteLine($"{testName} - 信息 ({result.InfoMessages.Count}):");
                // 只显示前3个信息消息，避免输出过长
                for (int i = 0; i < Math.Min(3, result.InfoMessages.Count); i++)
                {
                    Console.WriteLine($"  ℹ️ {result.InfoMessages[i]}");
                }
                if (result.InfoMessages.Count > 3)
                {
                    Console.WriteLine($"  ... 还有 {result.InfoMessages.Count - 3} 条信息");
                }
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("开始运行SS200ConfigurationValidator所有测试...");
            Console.WriteLine();

            await TestCompleteValidation();
            Console.WriteLine();

            TestRobotAlarmValidation();
            Console.WriteLine();

            TestRobotPositionValidation();
            Console.WriteLine();

            Console.WriteLine("所有测试运行完成！");
        }
    }
}