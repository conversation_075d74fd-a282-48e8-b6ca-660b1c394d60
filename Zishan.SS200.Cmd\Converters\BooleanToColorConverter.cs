using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 将布尔值转换为颜色的转换器
    /// </summary>
    public class BooleanToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                string colorParam = parameter as string;
                if (!string.IsNullOrEmpty(colorParam) && colorParam.Contains(","))
                {
                    string[] colors = colorParam.Split(',');
                    if (colors.Length >= 2)
                    {
                        string trueColor = colors[0].Trim();
                        string falseColor = colors[1].Trim();
                        
                        try
                        {
                            if (boolValue)
                            {
                                return new BrushConverter().ConvertFromString(trueColor);
                            }
                            else
                            {
                                return new BrushConverter().ConvertFromString(falseColor);
                            }
                        }
                        catch
                        {
                            // 转换失败时使用默认颜色
                            return boolValue ? Brushes.Green : Brushes.Red;
                        }
                    }
                }
                
                // 默认颜色
                return boolValue ? Brushes.Green : Brushes.Red;
            }
            
            return Brushes.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 