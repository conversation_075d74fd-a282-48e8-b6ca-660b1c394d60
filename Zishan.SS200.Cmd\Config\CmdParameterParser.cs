using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Zishan.SS200.Cmd.Config
{
    /// <summary>
    /// 配置参数项
    /// </summary>
    public class ConfigParameter
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("value")]
        public int Value { get; set; }

        public override string ToString()
        {
            return $"Name={Name},Value={Value}";
        }
    }

    /// <summary>
    /// 命令配置
    /// </summary>
    public class CommandConfig
    {
        [JsonPropertyName("CMDIndex")]
        public int CMDIndex { get; set; }

        [JsonPropertyName("Prompt")]
        public string Prompt { get; set; }

        [JsonPropertyName("ConfigPara")]
        public List<ConfigParameter> ConfigPara { get; set; }

        [JsonPropertyName("RunPara")]
        public List<ConfigParameter> RunPara { get; set; }

        [JsonPropertyName("TimeOut")]
        public int TimeOut { get; set; }
    }

    /// <summary>
    /// 命令配置解析器
    /// </summary>
    public class CmdParameterParser
    {
        private readonly Dictionary<string, CommandConfig> _commands;
        private readonly string _configFilePath;

        public CmdParameterParser(string configFilePath)
        {
            _configFilePath = configFilePath;
            _commands = new Dictionary<string, CommandConfig>();
            LoadConfig();
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    throw new FileNotFoundException($"Configuration file not found: {_configFilePath}");
                }

                var jsonString = File.ReadAllText(_configFilePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new CmdParameterParserConverter() }
                };

                var configs = JsonSerializer.Deserialize<Dictionary<string, CommandConfig>>(jsonString, options);
                if (configs != null)
                {
                    foreach (var config in configs)
                    {
                        _commands[config.Key] = config.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to load configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取命令配置
        /// </summary>
        /// <param name="commandName">命令名称</param>
        /// <returns>命令配置</returns>
        public CommandConfig GetCommand(string commandName)
        {
            if (_commands.TryGetValue(commandName, out var config))
            {
                return config;
            }
            throw new KeyNotFoundException($"Command '{commandName}' not found");
        }

        /// <summary>
        /// 获取所有命令名称
        /// </summary>
        public IEnumerable<string> GetCommandNames()
        {
            return _commands.Keys;
        }

        /// <summary>
        /// 根据命令索引获取命令配置
        /// </summary>
        public CommandConfig GetCommandByIndex(int cmdIndex)
        {
            var command = _commands.FirstOrDefault(x => x.Value.CMDIndex == cmdIndex);
            if (command.Value != null)
            {
                return command.Value;
            }
            throw new KeyNotFoundException($"Command with index '{cmdIndex}' not found");
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfig()
        {
            _commands.Clear();
            LoadConfig();
        }
    }

    /// <summary>
    /// 配置参数转换器
    /// </summary>
    public class CmdParameterParserConverter : JsonConverter<ConfigParameter>
    {
        public override ConfigParameter Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException();
            }

            var parameter = new ConfigParameter();
            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    return parameter;
                }

                if (reader.TokenType == JsonTokenType.PropertyName)
                {
                    var propertyName = reader.GetString();
                    reader.Read();
                    if (propertyName != null)
                    {
                        parameter.Name = propertyName;
                        parameter.Value = reader.GetInt32();
                    }
                }
            }

            throw new JsonException();
        }

        public override void Write(Utf8JsonWriter writer, ConfigParameter value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            writer.WriteNumber(value.Name, value.Value);
            writer.WriteEndObject();
        }
    }
}