# UiViewModel RTZ轴位置访问优化说明

## 概述

对`UiViewModel.cs`进行了优化，使其使用S200McuCmdService中新实现的RTZ轴位置访问接口，提高了代码的一致性、可维护性和性能。

## 优化内容

### 1. RTZ轴位置属性优化

**优化前**：
```csharp
// 直接访问RobotAlarmRegisters数组
public int TAxisStep => RobotAlarmRegisters.Count > 3 ? RobotAlarmRegisters[3].Combinevalue : 0;
public int RAxisStep => RobotAlarmRegisters.Count > 5 ? RobotAlarmRegisters[5].Combinevalue : 0;
public int ZAxisStep => RobotAlarmRegisters.Count > 7 ? RobotAlarmRegisters[7].Combinevalue : 0;

// 重复的转换公式计算
public double TAxisDegree => RobotAlarmRegisters.Count > 3 ? (RobotAlarmRegisters[3].Combinevalue / 100000.0) * 360.0 : 0.0;
public double RAxisLength => RobotAlarmRegisters.Count > 5 ? Math.Sin((RobotAlarmRegisters[5].Combinevalue / 50000.0) * (Math.PI / 180) * 360) * 2 * 208.96 : 0.0;
public double ZAxisHeight => RobotAlarmRegisters.Count > 7 ? (RobotAlarmRegisters[7].Combinevalue / 1000.0) * 5.0 : 0.0;
```

**优化后**：
```csharp
// 使用S200McuCmdService的统一接口
public int TAxisStep => McuCmdService.CurrentTAxisStep;
public int RAxisStep => McuCmdService.CurrentRAxisStep;
public int ZAxisStep => McuCmdService.CurrentZAxisStep;

public double TAxisDegree => McuCmdService.CurrentTAxisDegree;
public double RAxisLength => McuCmdService.CurrentRAxisLength;
public double ZAxisHeight => McuCmdService.CurrentZAxisHeight;
```

### 2. 新增便捷方法

```csharp
/// <summary>
/// 获取RTZ轴位置的组合信息（步进值）
/// </summary>
public (int TAxisStep, int RAxisStep, int ZAxisStep) GetRTZSteps() => McuCmdService.GetCurrentRTZSteps();

/// <summary>
/// 获取RTZ轴物理位置的组合信息
/// </summary>
public (double TAxisDegree, double RAxisLength, double ZAxisHeight) GetRTZPhysicalValues() => McuCmdService.GetCurrentRTZPhysicalValues();

/// <summary>
/// 检查RTZ轴位置数据是否有效
/// </summary>
public bool IsRTZPositionDataValid => McuCmdService.IsRTZPositionDataValid;

/// <summary>
/// 获取RTZ轴位置的格式化字符串（用于显示和复制）
/// </summary>
public string GetRTZPositionDisplayText()

/// <summary>
/// 获取RTZ轴位置的简化字符串（仅步进值）
/// </summary>
public string GetRTZPositionSimpleText()
```

### 3. CopyAllAxisPositions方法优化

**优化前**：
```csharp
private void CopyAllAxisPositions()
{
    // 直接访问RobotAlarmRegisters数组
    var tAxisValue = RobotAlarmRegisters[3].Combinevalue.ToString();
    var rAxisValue = RobotAlarmRegisters[5].Combinevalue.ToString();
    var zAxisValue = RobotAlarmRegisters[7].Combinevalue.ToString();
    
    var allValues = $"T轴: {tAxisValue}, R轴: {rAxisValue}, Z轴: {zAxisValue}";
    // ...
}
```

**优化后**：
```csharp
private void CopyAllAxisPositions()
{
    // 使用优化的便捷方法
    var allValues = GetRTZPositionSimpleText();
    // ...
}
```

### 4. ObservableProperty使用修复

修复了所有`_mcuCmdService`私有字段的直接访问，改为使用生成的`McuCmdService`属性：

```csharp
// 修复前
public bool IsRobotCoilReadingSupported => _mcuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.Robot);

// 修复后  
public bool IsRobotCoilReadingSupported => McuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.Robot);
```

## 优化优势

### 1. 代码一致性
- 统一使用S200McuCmdService的接口访问RTZ轴位置
- 避免了直接访问底层数组的不一致性
- 与其他模块保持相同的访问模式

### 2. 可维护性提升
- 消除了重复的转换公式代码
- 集中化的数据访问逻辑
- 更容易进行单元测试和调试

### 3. 性能优化
- 减少了重复的数组边界检查
- 统一的数据有效性验证
- 更高效的属性访问

### 4. 功能增强
- 新增了数据有效性检查
- 提供了格式化字符串方法
- 支持组合信息的一次性获取

### 5. 错误处理改进
- 统一的错误处理逻辑
- 更好的空值和边界情况处理
- 一致的默认值返回

## 使用示例

### 基本用法
```csharp
// 获取单独的轴位置
int tAxisStep = viewModel.TAxisStep;
double tAxisDegree = viewModel.TAxisDegree;

// 检查数据有效性
if (viewModel.IsRTZPositionDataValid)
{
    // 使用位置数据
}
```

### 组合信息获取
```csharp
// 获取所有轴的步进值
var (t, r, z) = viewModel.GetRTZSteps();

// 获取所有轴的物理值
var (tDeg, rLen, zHeight) = viewModel.GetRTZPhysicalValues();
```

### 格式化显示
```csharp
// 获取详细的显示文本
string detailText = viewModel.GetRTZPositionDisplayText();
// 输出: "T轴: 1000 steps (3.60°), R轴: 2000 steps (15.23mm), Z轴: 500 steps (2.50mm)"

// 获取简化的显示文本
string simpleText = viewModel.GetRTZPositionSimpleText();
// 输出: "T轴: 1000, R轴: 2000, Z轴: 500"
```

## 向后兼容性

- 保持了所有原有的公共属性和方法
- UI绑定无需修改
- 现有的调用代码继续有效
- 仅内部实现进行了优化

## 相关文件

- **优化文件**: `ViewModels/Dock/UiViewModel.cs`
- **依赖服务**: `Services/S200McuCmdService.cs`
- **接口定义**: `Services/Interfaces/IS200McuCmdService.cs`
- **功能说明**: `Docs/Readme/RTZ轴位置访问功能说明.md`

## 后续建议

1. **其他ViewModel优化**: 可以将类似的优化应用到其他使用RTZ轴位置的ViewModel
2. **单元测试**: 为新增的便捷方法添加单元测试
3. **UI绑定优化**: 考虑在UI中使用新的格式化方法
4. **性能监控**: 监控优化后的性能表现
5. **文档更新**: 更新相关的API文档和使用指南
