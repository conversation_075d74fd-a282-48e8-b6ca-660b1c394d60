﻿using System.Collections.Generic;
using Prism.Mvvm;
using System.Text;

namespace Zishan.SS200.Cmd.Models.Shared
{
    public class PlcCurRunStatusXXX : BindableBase
    {
        /// <summary>
        /// Cassette剩余未做的Slot号
        /// </summary>
        public Queue<int> QueueWaferSlot { get; set; } = new();

        /// <summary>
        /// definity
        /// </summary>
        public bool ChaHasWafer { get => _ChaHasWafer; set => SetProperty(ref _ChaHasWafer, value); }
        private bool _ChaHasWafer;
        /// <summary>
        /// definity
        /// </summary>
        public int SlotCha { get => _SlotCha; set => SetProperty(ref _SlotCha, value); }
        private int _SlotCha;

        /// <summary>
        /// definity
        /// </summary>
        public bool ChbHasWafer { get => _ChbHasWafer; set => SetProperty(ref _ChbHasWafer, value); }
        private bool _ChbHasWafer;
        /// <summary>
        /// definity
        /// </summary>
        public int SlotChb { get => _SlotChb; set => SetProperty(ref _SlotChb, value); }
        private int _SlotChb;

        /// <summary>
        /// definity
        /// </summary>
        public bool ChcHasWafer { get => _ChcHasWafer; set => SetProperty(ref _ChcHasWafer, value); }
        private bool _ChcHasWafer;
        /// <summary>
        /// definity
        /// </summary>
        public int SlotChc { get => _SlotChc; set => SetProperty(ref _SlotChc, value); }
        private int _SlotChc;

        /// <summary>
        /// definity
        /// </summary>
        public bool CoolingHasWafer { get => _CoolingHasWafer; set => SetProperty(ref _CoolingHasWafer, value); }
        private bool _CoolingHasWafer;
        /// <summary>
        /// definity
        /// </summary>
        public int SlotCooling { get => _SlotCooling; set => SetProperty(ref _SlotCooling, value); }
        private int _SlotCooling;

        /// <summary>
        /// definity
        /// </summary>
        public bool RobotNoseHasWafer { get => _robotNoseHasWafer; set => SetProperty(ref _robotNoseHasWafer, value); }
        private bool _robotNoseHasWafer;
        /// <summary>
        /// definity
        /// </summary>
        public int SlotrobotNose { get => _SlotrobotNose; set => SetProperty(ref _SlotrobotNose, value); }
        private int _SlotrobotNose;

        /// <summary>
        /// definity
        /// </summary>
        public bool RobotSmoothHasWafer { get => _RobotSmoothHasWafer; set => SetProperty(ref _RobotSmoothHasWafer, value); }
        private bool _RobotSmoothHasWafer;
        /// <summary>
        /// definity
        /// </summary>
        public int SlotRobotSmooth { get => _SlotRobotSmooth; set => SetProperty(ref _SlotRobotSmooth, value); }
        private int _SlotRobotSmooth;

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append($"CHA         是否有Wafer：{ChaHasWafer}，编号：{SlotCha}\r\n");
            sb.Append($"CHB         是否有Wafer：{ChbHasWafer}，编号：{SlotChb}\r\n");
            sb.Append($"CHC         是否有Wafer：{ChcHasWafer}，编号：{SlotChc}\r\n");
            sb.Append($"Cooling     是否有Wafer：{CoolingHasWafer}，编号：{SlotCooling}\r\n");
            sb.Append($"RobotNose   是否有Wafer：{RobotNoseHasWafer}，编号：{SlotrobotNose}\r\n");
            sb.Append($"RobotSmooth 是否有Wafer：{RobotSmoothHasWafer}，编号：{SlotRobotSmooth}\r\n");
            return sb.ToString();
        }
    }
}