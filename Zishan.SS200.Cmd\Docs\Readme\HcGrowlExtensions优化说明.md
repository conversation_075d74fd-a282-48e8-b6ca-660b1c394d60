# HcGrowlExtensions 优化说明

## 优化概述

对 `HcGrowlExtensions` 类进行了全面优化，提升了功能性、易用性和可维护性。

## 主要优化内容

### 1. 🎯 智能时间管理
- **智能等待时间**：`waitTime = -1` 时自动根据调试模式选择合适的显示时间
- **分类时间常量**：为不同场景定义了专门的时间常量
- **向后兼容**：保留原有的 `WaitTime` 常量，标记为过时但不影响现有代码

```csharp
// 新的时间常量
public const int DefaultWaitTime = 5;      // 默认停留时间
public const int DebugWaitTime = 10;       // 开发模式停留时间
public const int ProductionWaitTime = 2;   // 生产模式快速时间
public const int ErrorWaitTime = 8;        // 错误消息停留时间

// 智能时间使用
HcGrowlExtensions.Success("操作成功");  // 自动选择合适时间
HcGrowlExtensions.Success("操作成功", waitTime: -1);  // 显式使用智能时间
```

### 2. 🏷️ Token 分组管理
- **预定义 Token 常量**：避免硬编码字符串，提高代码可维护性
- **设备分组**：不同设备类型使用不同的 Token 进行消息分组

```csharp
// Token 常量类
public static class Tokens
{
    public const string System = "system";
    public const string Device = "device";
    public const string Command = "command";
    public const string Chamber = "chamber";
    public const string Robot = "robot";
    public const string Shuttle = "shuttle";
    public const string Connection = "connection";
    public const string Validation = "validation";
    public const string Debug = "debug";
}

// 使用示例
HcGrowlExtensions.Info("系统启动", HcGrowlExtensions.Tokens.System);
HcGrowlExtensions.Clear(HcGrowlExtensions.Tokens.Robot);  // 清除Robot相关消息
```

### 3. 🔧 设备相关便捷方法
- **设备专用方法**：为设备操作提供专门的方法，自动添加设备前缀和分组
- **连接状态方法**：专门处理设备连接状态的显示

```csharp
// 设备相关方法
HcGrowlExtensions.DeviceInfo(EnuMcuDeviceType.Robot, "状态更新");
// 显示: [Robot] 状态更新

HcGrowlExtensions.DeviceError(EnuMcuDeviceType.ChamberA, "通信超时");
// 显示: [ChamberA] 通信超时

HcGrowlExtensions.ConnectionStatus(EnuMcuDeviceType.Shuttle, true);
// 显示: Shuttle 连接成功

// 命令执行结果
HcGrowlExtensions.CommandResult("PinSearch", true, "P1: 100, P2: 200", EnuMcuDeviceType.Robot);
// 显示: [Robot] 命令 PinSearch 执行成功: P1: 100, P2: 200
```

### 4. 🐛 调试和开发支持
- **调试消息**：仅在开发模式下显示，包含调用位置信息
- **性能统计**：显示操作耗时信息
- **进度显示**：显示批量操作的进度

```csharp
// 调试消息（仅开发模式显示）
HcGrowlExtensions.Debug("开始执行Robot命令");
// 显示: [DEBUG] S200McuCmdPanelViewModel.ExecuteCommandAsync:123 - 开始执行Robot命令

// 性能统计
HcGrowlExtensions.Performance("数据库查询", 150.5);
// 显示: 性能统计: 数据库查询 耗时 150.50 毫秒

// 进度显示
HcGrowlExtensions.Progress("批量处理", 5, 10);
// 显示: 批量处理: 5/10 (50.0%)
```

### 5. 🧵 线程安全
- **UI线程检查**：自动检查当前线程，确保在UI线程上执行
- **异步安全**：可以在任何线程中安全调用

```csharp
// 在后台线程中安全调用
Task.Run(() =>
{
    // 后台处理...
    HcGrowlExtensions.Success("后台任务完成");  // 自动切换到UI线程
});
```

### 6. 🧹 清理功能增强
- **分组清理**：可以清除特定分组的消息
- **设备清理**：可以清除特定设备的所有消息

```csharp
// 清理功能
HcGrowlExtensions.Clear(HcGrowlExtensions.Tokens.Robot);  // 清除Robot相关消息
HcGrowlExtensions.ClearDevice(EnuMcuDeviceType.ChamberA); // 清除ChamberA相关消息
HcGrowlExtensions.ClearAll();  // 清除所有消息
```

### 7. 📝 完整的方法支持
- **Ask 方法**：支持询问对话框
- **Fatal 方法**：支持致命错误显示
- **参数验证**：自动过滤空消息

```csharp
// 询问对话框
HcGrowlExtensions.Ask("确定要删除配置吗？", isConfirmed =>
{
    if (isConfirmed)
    {
        DeleteConfiguration();
        HcGrowlExtensions.Success("配置已删除");
    }
    return true;
});

// 致命错误
HcGrowlExtensions.Fatal("系统发生致命错误，程序即将退出", staysOpen: true);
```

## 向后兼容性

✅ **完全向后兼容**：现有代码无需修改即可正常工作
✅ **渐进式升级**：可以逐步采用新的方法和功能
✅ **废弃标记**：旧的常量标记为过时但仍可使用

## 使用建议

### 新项目推荐用法
```csharp
// 基础消息
HcGrowlExtensions.Info("操作完成");
HcGrowlExtensions.Success("保存成功");
HcGrowlExtensions.Warning("参数异常");
HcGrowlExtensions.Error("操作失败");

// 设备相关
HcGrowlExtensions.DeviceSuccess(EnuMcuDeviceType.Robot, "命令执行成功");
HcGrowlExtensions.ConnectionStatus(EnuMcuDeviceType.Shuttle, true);

// 命令结果
HcGrowlExtensions.CommandResult("PinSearch", true, "执行成功", EnuMcuDeviceType.Robot);

// 调试信息
HcGrowlExtensions.Debug("调试信息");
HcGrowlExtensions.Performance("操作名称", elapsedMs);
```

### 现有代码迁移
```csharp
// 旧代码（仍然有效）
HcGrowlExtensions.Success("操作成功", waitTime: HcGrowlExtensions.WaitTime);

// 推荐的新写法
HcGrowlExtensions.Success("操作成功");  // 使用智能时间
// 或
HcGrowlExtensions.Success("操作成功", waitTime: HcGrowlExtensions.DefaultWaitTime);
```

## 性能优化

1. **消息过滤**：自动过滤空消息，避免无效调用
2. **智能线程切换**：只在必要时切换到UI线程
3. **常量预定义**：避免字符串重复创建

## 总结

优化后的 `HcGrowlExtensions` 提供了：
- 🎯 更智能的时间管理
- 🏷️ 更好的消息分组
- 🔧 更便捷的设备操作
- 🐛 更强的调试支持
- 🧵 更安全的线程处理
- 📝 更完整的功能覆盖
- ✅ 完全的向后兼容

这些优化将显著提升开发效率和用户体验，同时保持代码的简洁性和可维护性。
