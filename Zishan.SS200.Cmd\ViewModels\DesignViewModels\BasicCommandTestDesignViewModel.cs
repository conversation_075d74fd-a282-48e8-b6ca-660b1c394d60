﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

//using Prism.Navigation.Regions;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.ViewModels;

namespace Zishan.SS200.Cmd.ViewModels.DesignViewModels
{
    /// <summary>
    /// 设计时视图模型，用于XAML设计器中显示模拟数据
    /// </summary>
    public class BasicCommandTestDesignViewModel : BasicCommandTestViewModel
    {
        private static BasicCommandTestDesignViewModel _Instance;
        public static BasicCommandTestDesignViewModel Instance => _Instance ??= new();

        public BasicCommandTestDesignViewModel() : base()
        {
            // 设计时数据，覆盖基类的设置
            this.Title = "设计时 - Wafer 搬运";
        }
    }
}