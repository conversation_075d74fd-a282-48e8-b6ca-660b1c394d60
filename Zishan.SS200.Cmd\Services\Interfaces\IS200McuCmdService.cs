using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using Zishan.SS200.Cmd.Models;

namespace Zishan.SS200.Cmd.Services.Interfaces;

/// <summary>
/// S200McuCmdService接口，用于依赖注入和测试
/// </summary>
public interface IS200McuCmdService : IDisposable
{
    string Name { get; set; }

    McuDevice Shuttle { get; }
    McuDevice Robot { get; }
    McuDevice ChamberA { get; }
    McuDevice ChamberB { get; }

    /// <summary>
    /// Shuttle设备输入线圈集合
    /// </summary>
    ObservableCollection<ModbusCoil> ShuttleInputCoils { get; }

    /// <summary>
    /// Robot设备输入线圈集合
    /// </summary>
    ObservableCollection<ModbusCoil> RobotInputCoils { get; }

    /// <summary>
    /// Cha设备输入线圈集合
    /// </summary>
    ObservableCollection<ModbusCoil> ChaInputCoils { get; }

    /// <summary>
    /// Chb设备输入线圈集合
    /// </summary>
    ObservableCollection<ModbusCoil> ChbInputCoils { get; }

    /// <summary>
    /// Shuttle设备控制线圈集合，用于读写状态展示和控制
    /// </summary>
    ObservableCollection<ModbusCoil> ShuttleCoils { get; }

    /// <summary>
    /// Robot设备控制线圈集合，用于读写状态展示和控制
    /// </summary>
    ObservableCollection<ModbusCoil> RobotCoils { get; }

    /// <summary>
    /// Cha设备控制线圈集合，用于读写状态展示和控制
    /// </summary>
    ObservableCollection<ModbusCoil> ChaCoils { get; }

    /// <summary>
    /// Chb设备控制线圈集合，用于读写状态展示和控制
    /// </summary>
    ObservableCollection<ModbusCoil> ChbCoils { get; }

    /// <summary>
    /// Robot电机寄存器映射区 报警、RTZ位置信息，使用ModbusRegister类型
    /// </summary>
    public ObservableCollection<ModbusRegister> RobotAlarmRegisters { get; }

    /// <summary>
    /// Robot Nose 做Pin Search 获取到的值，已经计算中心值
    /// </summary>
    public int NoseBasePinSearchValue { get; set; }

    /// <summary>
    /// Robot Smooth 做Pin Search 获取到的值，已经计算中心值
    /// </summary>
    public int SmoothBasePinSearchValue { get; set; }

    /// <summary>
    /// 启动线圈状态监控
    /// </summary>
    void StartCoilsMonitoring();

    /// <summary>
    /// 停止线圈状态监控
    /// </summary>
    void StopCoilsMonitoring();

    Task ConnectAllAsync(string shuttleIp, int shuttlePort, string robotIp, int robotPort, string chaIp, int chaPort, string chbIp, int chbPort);

    Task DisconnectAllAsync();

    /// <summary>
    /// 检查设备是否支持线圈读取
    /// </summary>
    /// <param name="deviceType">设备类型</param>
    /// <returns>是否支持</returns>
    bool IsCoilReadingSupported(EnuMcuDeviceType deviceType);

    /// <summary>
    /// 写入线圈值到设备
    /// </summary>
    /// <param name="coil">要写入的线圈</param>
    /// <param name="value">要写入的值</param>
    Task WriteCoilValueAsync(ModbusCoil coil, bool value);

    /// <summary>
    /// 获取所有设备的状态
    /// </summary>
    /// <returns>设备状态字典，键为设备类型，值为设备状态枚举</returns>
    Dictionary<EnuMcuDeviceType, DeviceStatus> GetAllDeviceStatus();

    void GetRobotSpecilModbusCoil(EnuRobotDICodes enuRobotDICodes, out ModbusCoil inputCoil);

    #region RTZ轴位置访问接口

    /// <summary>
    /// T轴当前步进位置值 - 实时数据
    /// </summary>
    int CurrentTAxisStep { get; }

    /// <summary>
    /// R轴当前步进位置值 - 实时数据
    /// </summary>
    int CurrentRAxisStep { get; }

    /// <summary>
    /// Z轴当前步进位置值 - 实时数据
    /// </summary>
    int CurrentZAxisStep { get; }

    /// <summary>
    /// T旋转轴角度值（度） - 实时计算
    /// 转换公式：步进值/100000*360
    /// </summary>
    double CurrentTAxisDegree { get; }

    /// <summary>
    /// R伸缩轴长度值（mm） - 实时计算
    /// 转换公式：L = Sin(步进值/50000*360)*2*208.96
    /// </summary>
    double CurrentRAxisLength { get; }

    /// <summary>
    /// Z上下轴高度值（mm） - 实时计算
    /// 转换公式：步进值/1000*5
    /// </summary>
    double CurrentZAxisHeight { get; }

    /// <summary>
    /// 获取RTZ轴位置的组合信息（步进值） - 实时数据
    /// </summary>
    /// <returns>T轴、R轴、Z轴的步进值</returns>
    Task<(int TAxisStep, int RAxisStep, int ZAxisStep)> GetCurrentRTZSteps();

    /// <summary>
    /// 获取RTZ轴物理位置的组合信息 - 实时计算
    /// </summary>
    /// <returns>T轴角度(度)、R轴长度(mm)、Z轴高度(mm)</returns>
    Task<(double TAxisDegree, double RAxisLength, double ZAxisHeight)> GetCurrentRTZPhysicalValues();

    /// <summary>
    /// 检查RTZ轴位置数据是否有效
    /// </summary>
    bool IsRTZPositionDataValid { get; }

    #endregion RTZ轴位置访问接口

    /// <summary>
    /// 手动更新报警寄存器状态（用于获取最新的Pin Search值等）
    /// </summary>
    Task RefreshAlarmRegistersAsync();
}