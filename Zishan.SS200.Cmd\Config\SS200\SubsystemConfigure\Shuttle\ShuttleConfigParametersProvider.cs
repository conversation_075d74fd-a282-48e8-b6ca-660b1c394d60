using System;
using System.Collections.Generic;
using System.IO;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Shuttle;
using Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Shuttle
{
    /// <summary>
    /// 穿梭室配置参数根配置
    /// </summary>
    public class ShuttleConfigParametersConfig
    {
        public List<ConfigureSetting> ConfigureSettings { get; set; }
    }

    /// <summary>
    /// 穿梭室配置参数提供者 - 从JSON配置文件加载参数
    /// </summary>
    public class ShuttleConfigParametersProvider : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ShuttleConfigParametersProvider));
        private readonly Dictionary<string, object> _settings = new Dictionary<string, object>();
        private readonly FileSystemWatcher _configWatcher;

        private static readonly Lazy<ShuttleConfigParametersProvider> _instance =
            new Lazy<ShuttleConfigParametersProvider>(() => new ShuttleConfigParametersProvider());

        // 配置文件路径
        private const string CONFIG_PATH = "Configs/SS200/SubsystemConfigure/Shuttle/ShuttleConfigParameters.json";

        // 最后修改时间，用于监测配置文件变化
        private DateTime _lastModifiedTime = DateTime.MinValue;

        public static ShuttleConfigParametersProvider Instance => _instance.Value;

        // 私有构造函数
        private ShuttleConfigParametersProvider()
        {
            // 初始化文件系统监视器
            string configDir = Path.GetDirectoryName(GetConfigFilePath());
            string configFileName = Path.GetFileName(CONFIG_PATH);

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            _configWatcher = new FileSystemWatcher(configDir, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            // 注册文件变化事件处理
            _configWatcher.Changed += OnConfigFileChanged;
            _configWatcher.Created += OnConfigFileChanged;

            // 初始化默认值
            InitializeDefaultValues();

            // 尝试加载配置文件
            LoadFromJson();
        }

        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 由于文件系统事件可能会触发多次，这里添加简单的防抖动处理
                if ((DateTime.Now - _lastModifiedTime).TotalMilliseconds < 100)
                {
                    return;
                }

                _logger.Info($"检测到配置文件变化: {e.FullPath}, 变化类型: {e.ChangeType}");
                LoadFromJson();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理配置文件变化事件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaultValues()
        {
            // 卡匣巢和门控制参数
            _settings["SPS1"] = 3;    // 卡匣巢伸展/缩回最小时间
            _settings["SPS2"] = 5;    // 卡匣巢伸展/缩回最大时间
            _settings["SPS6"] = 10;   // 卡匣门运动最小时间
            _settings["SPS7"] = 20;   // 卡匣门运动最大时间

            // 穿梭室运动控制参数
            _settings["SPS3"] = 10;   // 穿梭室上升/下降最小时间
            _settings["SPS4"] = 20;   // 穿梭室上升/下降最大时间
            _settings["SPS5"] = 15;   // 穿梭室旋转最大时间

            // 阀门控制参数
            _settings["SPS9"] = 3;    // ISO阀运动最大时间

            // 传感器使能参数
            _settings["SPS10"] = "Y"; // 前滑出传感器使能
            _settings["SPS11"] = "Y"; // 后滑出传感器使能

            // 真空系统参数
            _settings["SPS12"] = 2;    // 穿梭室传输压力
            _settings["SPS13"] = 50;   // 穿梭室上升/下降压差
            _settings["SPS14"] = 730;  // 装载锁大气压力最小值
            _settings["SPS15"] = 730;  // 穿梭室大气压力最小值
            _settings["SPS16"] = 2;    // 传输压力
            _settings["SPS17"] = 10;   // 穿梭室抽真空最大时间
            _settings["SPS18"] = 760;  // 装载锁大气压力设定点
            _settings["SPS19"] = 760;  // 穿梭室大气压力最小设定点
            _settings["SPS20"] = 0;    // 穿梭室压力偏移
            _settings["SPS21"] = 0;    // 装载锁压力偏移
            _settings["SPS22"] = 10;   // 穿梭室回填最大时间
            _settings["SPS23"] = 10;   // 装载锁回填最大时间

            // 备用参数
            _settings["SPS8"] = "N/A"; // 备用
        }

        /// <summary>
        /// 从JSON配置文件加载参数
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool LoadFromJson()
        {
            try
            {
                string jsonFilePath = GetConfigFilePath();
                if (!File.Exists(jsonFilePath))
                {
                    _logger.Warn($"穿梭室配置参数文件不存在: {jsonFilePath}，将使用默认值");
                    return false;
                }

                // 获取文件最后修改时间
                DateTime currentModified = File.GetLastWriteTime(jsonFilePath);

                // 如果文件未修改，则不重新加载
                if (currentModified == _lastModifiedTime)
                {
                    return true;
                }

                _lastModifiedTime = currentModified;

                string jsonContent;
                using (var fileStream = new FileStream(jsonFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    jsonContent = reader.ReadToEnd();
                }
                var config = JsonConvert.DeserializeObject<ShuttleConfigParametersConfig>(jsonContent);

                if (config?.ConfigureSettings == null || config.ConfigureSettings.Count == 0)
                {
                    _logger.Warn("未找到有效的配置参数，将使用默认值");
                    return false;
                }

                // 临时字典，验证成功后再替换
                var tempSettings = new Dictionary<string, object>();
                foreach (var setting in config.ConfigureSettings)
                {
                    if (string.IsNullOrEmpty(setting.Code))
                    {
                        _logger.Warn($"参数ID {setting.Id} 缺少代码标识，已跳过");
                        continue;
                    }

                    tempSettings[setting.Code] = setting.Value;
                    _logger.Debug($"加载参数 {setting.Code} = {setting.Value} ({setting.Description})");
                }

                // 验证所有必要参数都存在
                for (int i = 1; i <= 23; i++)
                {
                    string code = $"SPS{i}";
                    if (!tempSettings.ContainsKey(code))
                    {
                        _logger.Warn($"配置文件中缺少必要参数 {code}，将使用默认值");
                        tempSettings[code] = _settings[code]; // 使用默认值
                    }
                }

                // 更新参数字典
                foreach (var kvp in tempSettings)
                {
                    _settings[kvp.Key] = kvp.Value;
                }

                _logger.Info($"成功从 {jsonFilePath} 加载 {tempSettings.Count} 个穿梭室配置参数");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载穿梭室配置参数文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        private string GetConfigFilePath()
        {
            try
            {
                return App.ConfigHelper.GetConfigFilePath(CONFIG_PATH);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取配置文件路径失败: {ex.Message}", ex);

                // 回退策略 - 尝试直接拼接路径
                string fallbackPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    CONFIG_PATH);

                _logger.Info($"使用回退路径: {fallbackPath}");
                return fallbackPath;
            }
        }

        /// <summary>
        /// 获取参数值（泛型方法）
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="code">参数代码 (如 "SPS1")</param>
        /// <returns>参数值</returns>
        public T GetSettingValue<T>(string code)
        {
            if (_settings.TryGetValue(code, out object value))
            {
                if (value is T typedValue)
                {
                    return typedValue;
                }

                try
                {
                    // 尝试转换类型
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch (Exception ex)
                {
                    _logger.Error($"参数类型转换失败: {code}, 期望类型: {typeof(T).Name}, 实际值: {value}", ex);
                    throw new InvalidCastException($"参数类型不匹配: {code}");
                }
            }

            throw new KeyNotFoundException($"找不到穿梭室配置参数: {code}");
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        /// <param name="enuShuttleConfigParameterCodes">参数代码枚举</param>
        /// <returns>参数值</returns>
        public T GetSettingValue<T>(EnuShuttleConfigParameterCodes enuShuttleConfigParameterCodes)
        {
            return GetSettingValue<T>(enuShuttleConfigParameterCodes.ToString());
        }

        /// <summary>
        /// 获取int类型参数值
        /// </summary>
        /// <param name="enuShuttleConfigParameterCodes">参数代码枚举</param>
        /// <returns>int类型参数值</returns>
        public int GetIntSettingValue(EnuShuttleConfigParameterCodes enuShuttleConfigParameterCodes)
        {
            return GetSettingValue<int>(enuShuttleConfigParameterCodes);
        }

        /// <summary>
        /// 获取string类型参数值
        /// </summary>
        /// <param name="enuShuttleConfigParameterCodes">参数代码枚举</param>
        /// <returns>string类型参数值</returns>
        public string GetStringSettingValue(EnuShuttleConfigParameterCodes enuShuttleConfigParameterCodes)
        {
            return GetSettingValue<string>(enuShuttleConfigParameterCodes);
        }

        /// <summary>
        /// 获取bool类型参数值
        /// </summary>
        /// <param name="enuShuttleConfigParameterCodes">参数代码枚举</param>
        /// <returns>bool类型参数值</returns>
        public bool GetBoolSettingValue(EnuShuttleConfigParameterCodes enuShuttleConfigParameterCodes)
        {
            // 对于Y/N类型的参数，转换为bool
            string value = GetSettingValue<string>(enuShuttleConfigParameterCodes);
            return value == "Y";
        }

        #region 辅助方法 - 获取特定配置参数

        /// <summary>
        /// 获取卡匣巢伸展/缩回最小时间
        /// </summary>
        /// <returns>卡匣巢伸展/缩回最小时间(秒)</returns>
        public int GetCassetteNestExtendRetractMinTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS1);
        }

        /// <summary>
        /// 获取卡匣巢伸展/缩回最大时间
        /// </summary>
        /// <returns>卡匣巢伸展/缩回最大时间(秒)</returns>
        public int GetCassetteNestExtendRetractMaxTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS2);
        }

        /// <summary>
        /// 获取穿梭室上升/下降最小时间
        /// </summary>
        /// <returns>穿梭室上升/下降最小时间(秒)</returns>
        public int GetShuttleUpDownMinTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS3);
        }

        /// <summary>
        /// 获取穿梭室上升/下降最大时间
        /// </summary>
        /// <returns>穿梭室上升/下降最大时间(秒)</returns>
        public int GetShuttleUpDownMaxTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS4);
        }

        /// <summary>
        /// 获取穿梭室旋转最大时间
        /// </summary>
        /// <returns>穿梭室旋转最大时间(秒)</returns>
        public int GetShuttleRotateMaxTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS5);
        }

        /// <summary>
        /// 获取卡匣门运动最小时间
        /// </summary>
        /// <returns>卡匣门运动最小时间(秒)</returns>
        public int GetCassetteDoorMotionMinTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS6);
        }

        /// <summary>
        /// 获取卡匣门运动最大时间
        /// </summary>
        /// <returns>卡匣门运动最大时间(秒)</returns>
        public int GetCassetteDoorMotionMaxTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS7);
        }

        /// <summary>
        /// 获取ISO阀运动最大时间
        /// </summary>
        /// <returns>ISO阀运动最大时间(秒)</returns>
        public int GetISOValveMotionMaxTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS9);
        }

        /// <summary>
        /// 获取前滑出传感器使能状态
        /// </summary>
        /// <returns>前滑出传感器是否启用</returns>
        public bool GetFrontSlideOutSensorEnable()
        {
            return GetBoolSettingValue(EnuShuttleConfigParameterCodes.SPS10);
        }

        /// <summary>
        /// 获取后滑出传感器使能状态
        /// </summary>
        /// <returns>后滑出传感器是否启用</returns>
        public bool GetBackSlideOutSensorEnable()
        {
            return GetBoolSettingValue(EnuShuttleConfigParameterCodes.SPS11);
        }

        /// <summary>
        /// 获取穿梭室传输压力
        /// </summary>
        /// <returns>穿梭室传输压力(Torr)</returns>
        public int GetShuttleTransferPressure()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS12);
        }

        /// <summary>
        /// 获取穿梭室上升/下降压差
        /// </summary>
        /// <returns>穿梭室上升/下降压差(Torr)</returns>
        public int GetDeltaPressureForShuttleUpDown()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS13);
        }

        /// <summary>
        /// 获取装载锁大气压力最小值
        /// </summary>
        /// <returns>装载锁大气压力最小值(Torr)</returns>
        public int GetLoadlockATMPressureMinimum()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS14);
        }

        /// <summary>
        /// 获取穿梭室大气压力最小值
        /// </summary>
        /// <returns>穿梭室大气压力最小值(Torr)</returns>
        public int GetShuttleATMPressureMinimum()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS15);
        }

        /// <summary>
        /// 获取传输压力
        /// </summary>
        /// <returns>传输压力(Torr)</returns>
        public int GetTransferPressure()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS16);
        }

        /// <summary>
        /// 获取穿梭室抽真空最大时间
        /// </summary>
        /// <returns>穿梭室抽真空最大时间(分钟)</returns>
        public int GetPumpDownShuttleMaxTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS17);
        }

        /// <summary>
        /// 获取装载锁大气压力设定点
        /// </summary>
        /// <returns>装载锁大气压力设定点(Torr)</returns>
        public int GetLoadlockATMPressureSetpoint()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS18);
        }

        /// <summary>
        /// 获取穿梭室大气压力最小设定点
        /// </summary>
        /// <returns>穿梭室大气压力最小设定点(Torr)</returns>
        public int GetShuttleATMPressureMinimumSetpoint()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS19);
        }

        /// <summary>
        /// 获取穿梭室压力偏移
        /// </summary>
        /// <returns>穿梭室压力偏移(Torr)</returns>
        public int GetShuttlePressureOffset()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS20);
        }

        /// <summary>
        /// 获取装载锁压力偏移
        /// </summary>
        /// <returns>装载锁压力偏移(Torr)</returns>
        public int GetLoadlockPressureOffset()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS21);
        }

        /// <summary>
        /// 获取穿梭室回填最大时间
        /// </summary>
        /// <returns>穿梭室回填最大时间(分钟)</returns>
        public int GetShuttleBackfillMaxTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS22);
        }

        /// <summary>
        /// 获取装载锁回填最大时间
        /// </summary>
        /// <returns>装载锁回填最大时间(分钟)</returns>
        public int GetLoadlockBackfillMaxTime()
        {
            return GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS23);
        }

        #endregion 辅助方法 - 获取特定配置参数

        public void Dispose()
        {
            _configWatcher?.Dispose();
        }
    }
}