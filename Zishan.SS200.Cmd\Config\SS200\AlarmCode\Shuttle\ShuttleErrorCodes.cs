using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using Zishan.SS200.Cmd.Models.SS200.AlarmCode;

namespace Zishan.SS200.Cmd.Config.SS200.AlarmCode.Shuttle;

/// <summary>
/// 传送室报警代码
/// </summary>
public class ShuttleErrorCodes
{
    /// <summary>
    /// 报警项列表
    /// </summary>
    public List<AlarmItem> AlarmItems { get; set; } = new List<AlarmItem>();

    /// <summary>
    /// 从JSON文件加载报警代码
    /// </summary>
    /// <param name="filePath">JSON文件路径</param>
    /// <returns>ShuttleErrorCodes实例</returns>
    public static ShuttleErrorCodes LoadFromJson(string filePath)
    {
        if (!File.Exists(filePath))
        {
            return new ShuttleErrorCodes();
        }

        string jsonContent;
        using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
        using (var reader = new StreamReader(fileStream))
        {
            jsonContent = reader.ReadToEnd();
        }

        var alarmItems = JsonSerializer.Deserialize<List<AlarmItem>>(jsonContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        return new ShuttleErrorCodes
        {
            AlarmItems = alarmItems ?? new List<AlarmItem>()
        };
    }

    /// <summary>
    /// 保存到JSON文件
    /// </summary>
    /// <param name="filePath">JSON文件路径</param>
    public void SaveToJson(string filePath)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true
        };
        string jsonContent = JsonSerializer.Serialize(AlarmItems, options);
        File.WriteAllText(filePath, jsonContent);
    }
}