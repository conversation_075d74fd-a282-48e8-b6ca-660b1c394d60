using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Enums.Basic;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// 层次化日志使用示例
    /// 展示如何使用新的UILogService缩进功能来创建有层次感的日志
    /// </summary>
    public class HierarchicalLoggingExample
    {
        /// <summary>
        /// 示例1：使用using语句自动管理缩进层级
        /// </summary>
        public async Task Example1_UsingStatementApproach()
        {
            UILogService.AddLog("开始执行晶圆传输操作");
            
            using (UILogService.CreateIndentScope())
            {
                UILogService.AddLog("搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2) 机械臂端口: Nose");
                
                // 取晶圆操作
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("取Wafer: 从ChamberA(SLOT:1)获取Wafer");
                    
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("开始取Wafer...");
                        await Task.Delay(1000); // 模拟操作时间
                        UILogService.AddLog("检查机械臂状态...");
                        UILogService.AddLog("移动到取片位置...");
                        UILogService.AddSuccessLog("取Wafer操作完成");
                    }
                }
                
                // 放晶圆操作
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("放Wafer: 将Wafer放置到ChamberB(SLOT:2)");
                    
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("开始放Wafer...");
                        await Task.Delay(1000); // 模拟操作时间
                        UILogService.AddLog("移动到放片位置...");
                        UILogService.AddLog("确认放片完成...");
                        UILogService.AddSuccessLog("放Wafer操作完成");
                    }
                }
            }
            
            UILogService.AddSuccessLog("搬运Wafer成功: Wafer已成功从ChamberA(SLOT:1)搬运到ChamberB(SLOT:2)");
        }

        /// <summary>
        /// 示例2：手动管理缩进层级
        /// </summary>
        public async Task Example2_ManualIndentManagement()
        {
            UILogService.AddLog("执行搬运，Robot连接状态: True，服务实例: McuCmdService");
            
            UILogService.IncreaseIndent();
            UILogService.AddLog("搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2) 机械臂端口: Nose");
            
            UILogService.IncreaseIndent();
            UILogService.AddLog("取Wafer: 从ChamberA(SLOT:1)获取Wafer");
            
            UILogService.IncreaseIndent();
            UILogService.AddLog("开始取Wafer...");
            await Task.Delay(500);
            UILogService.AddLog("结束取Wafer...");
            UILogService.DecreaseIndent();
            
            UILogService.AddLog("放Wafer: 将Wafer放置到ChamberB(SLOT:2)");
            
            UILogService.IncreaseIndent();
            UILogService.AddLog("开始放Wafer...");
            await Task.Delay(500);
            UILogService.AddLog("结束放Wafer...");
            UILogService.DecreaseIndent();
            
            UILogService.DecreaseIndent();
            UILogService.DecreaseIndent();
            
            UILogService.AddSuccessLog("搬运Wafer成功: Wafer已成功从ChamberA(SLOT:1)搬运到ChamberB(SLOT:2)");
        }

        /// <summary>
        /// 示例3：使用便捷方法
        /// </summary>
        public async Task Example3_ConvenienceMethods()
        {
            UILogService.AddLogAndIncreaseIndent("开始复杂的晶圆处理流程");
            
            UILogService.AddLogAndIncreaseIndent("第一阶段：初始化检查");
            UILogService.AddLog("检查设备状态...");
            UILogService.AddLog("检查安全联锁...");
            UILogService.DecreaseIndentAndAddSuccessLog("初始化检查完成");
            
            UILogService.AddLogAndIncreaseIndent("第二阶段：晶圆传输");
            UILogService.AddLog("准备传输路径...");
            UILogService.AddLog("执行传输动作...");
            UILogService.DecreaseIndentAndAddSuccessLog("晶圆传输完成");
            
            UILogService.AddLogAndIncreaseIndent("第三阶段：后处理");
            UILogService.AddLog("清理传输路径...");
            UILogService.AddLog("更新状态信息...");
            UILogService.DecreaseIndentAndAddSuccessLog("后处理完成");
            
            UILogService.DecreaseIndentAndAddSuccessLog("复杂的晶圆处理流程全部完成");
        }

        /// <summary>
        /// 示例4：错误处理中的层次化日志
        /// </summary>
        public async Task Example4_ErrorHandlingWithHierarchy()
        {
            UILogService.AddLogAndIncreaseIndent("开始执行可能失败的操作");
            
            try
            {
                UILogService.AddLogAndIncreaseIndent("尝试连接设备");
                
                // 模拟可能失败的操作
                var random = new Random();
                if (random.Next(2) == 0)
                {
                    throw new Exception("设备连接失败");
                }
                
                UILogService.DecreaseIndentAndAddSuccessLog("设备连接成功");
                
                UILogService.AddLogAndIncreaseIndent("执行设备操作");
                await Task.Delay(1000);
                UILogService.DecreaseIndentAndAddSuccessLog("设备操作完成");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"操作失败: {ex.Message}");
                
                UILogService.AddLogAndIncreaseIndent("开始错误恢复流程");
                UILogService.AddLog("重置设备状态...");
                UILogService.AddLog("清理资源...");
                UILogService.DecreaseIndentAndAddLog("错误恢复完成");
            }
            finally
            {
                UILogService.DecreaseIndentAndAddLog("操作结束，清理完成");
            }
        }

        /// <summary>
        /// 示例5：配置缩进选项
        /// </summary>
        public void Example5_ConfigureIndentOptions()
        {
            // 保存原始设置
            var originalLevel = UILogService.GetCurrentIndentLevel();
            
            UILogService.AddLog("使用默认缩进设置（4个空格）");
            UILogService.IncreaseIndent();
            UILogService.AddLog("这是缩进的日志");
            UILogService.DecreaseIndent();
            
            // 更改缩进字符串为2个空格
            UILogService.SetIndentString("  ");
            UILogService.AddLog("更改为2个空格缩进");
            UILogService.IncreaseIndent();
            UILogService.AddLog("这是使用2个空格的缩进");
            UILogService.DecreaseIndent();
            
            // 更改缩进字符串为Tab
            UILogService.SetIndentString("\t");
            UILogService.AddLog("更改为Tab缩进");
            UILogService.IncreaseIndent();
            UILogService.AddLog("这是使用Tab的缩进");
            UILogService.DecreaseIndent();
            
            // 恢复默认设置
            UILogService.SetIndentString("    ");
            UILogService.AddLog("恢复默认缩进设置");
            
            // 测试最大缩进层级限制
            UILogService.SetMaxIndentLevel(3);
            UILogService.AddLog("设置最大缩进层级为3");
            
            for (int i = 0; i < 5; i++)
            {
                UILogService.IncreaseIndent();
                UILogService.AddLog($"缩进层级 {UILogService.GetCurrentIndentLevel()}");
            }
            
            // 重置缩进
            UILogService.ResetIndent();
            UILogService.AddLog("重置缩进层级");
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public async Task RunAllExamples()
        {
            UILogService.AddLog("=== 层次化日志示例开始 ===");
            
            UILogService.AddLogAndIncreaseIndent("示例1：using语句自动管理");
            await Example1_UsingStatementApproach();
            UILogService.DecreaseIndent();
            
            UILogService.AddLogAndIncreaseIndent("示例2：手动管理缩进");
            await Example2_ManualIndentManagement();
            UILogService.DecreaseIndent();
            
            UILogService.AddLogAndIncreaseIndent("示例3：便捷方法");
            await Example3_ConvenienceMethods();
            UILogService.DecreaseIndent();
            
            UILogService.AddLogAndIncreaseIndent("示例4：错误处理");
            await Example4_ErrorHandlingWithHierarchy();
            UILogService.DecreaseIndent();
            
            UILogService.AddLogAndIncreaseIndent("示例5：配置选项");
            Example5_ConfigureIndentOptions();
            UILogService.DecreaseIndent();
            
            UILogService.AddSuccessLog("=== 层次化日志示例完成 ===");
        }
    }
}
