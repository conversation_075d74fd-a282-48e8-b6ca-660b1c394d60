﻿<Window
    x:Class="Zishan.SS200.Cmd.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Extensions="clr-namespace:Zishan.SS200.Cmd.Extensions"
    xmlns:behavior="clr-namespace:Zishan.SS200.Cmd.Behaviors"
    xmlns:common="clr-namespace:Zishan.SS200.Cmd.Common"
    xmlns:conv="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:converters="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dock="clr-namespace:Zishan.SS200.Cmd.Views.Dock"
    xmlns:dvm="clr-namespace:Zishan.SS200.Cmd.ViewModels.DesignViewModels"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:services="clr-namespace:Zishan.SS200.Cmd.Services"
    xmlns:vRules="clr-namespace:Zishan.SS200.Cmd.ValidationRules"
    xmlns:viewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    xmlns:views="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:wucvt="clr-namespace:Wu.Wpf.Converters;assembly=Wu.Wpf"
    xmlns:wuext="clr-namespace:Wu.Wpf.Extensions;assembly=Wu.Wpf"
    Title="{Binding Title}"
    MinWidth="1920"
    MinHeight="1000"
    d:DataContext="{x:Static dvm:MainViewDesignViewModel.Instance}"
    d:DesignHeight="1290"
    d:DesignWidth="2146"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="Transparent"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <conv:DescriptionConverter x:Key="DescriptionConverter" />
            <conv:AndMultiValueConverter x:Key="AndMultiValueConverter" />
            <conv:InvertBooleanConverter x:Key="InvertBooleanConverter" />
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        </ResourceDictionary>
    </Window.Resources>
    <materialDesign:DialogHost
        x:Name="DialogHost"
        DialogTheme="Inherit"
        Identifier="RootDialog">
        <Grid Background="White">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Border>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <!--  主内容区域  -->
                    <Border>
                        <TabControl
                            Grid.Row="0"
                            Margin="5"
                            Style="{StaticResource TabControlCapsuleSolid}"
                            TabStripPlacement="Bottom">

                            <TabItem
                                Cursor="Hand"
                                Header="SS-200：Modbus通信"
                                IsEnabled="True"
                                IsSelected="{Binding IsTabItemtMainSelected}">
                                <Grid Background="White">
                                    <Border>
                                        <!--  Robot 搬运基础命令测试页  -->
                                        <views:S200McuCmdPanel />
                                    </Border>
                                </Grid>
                            </TabItem>

                            <TabItem
                                Header="基础命令测试"
                                HeaderStringFormat="{}{0}"
                                IsEnabled="True">
                                <Border>
                                    <!--  Robot 搬运基础命令测试页  -->
                                    <views:BasicCommandTest />
                                </Border>
                            </TabItem>

                            <TabItem
                                Header="Wafer 搬运测试"
                                HeaderStringFormat="{}{0}"
                                IsEnabled="True"
                                IsSelected="True">
                                <Border>
                                    <!--  Wafer 搬运测试页  -->
                                    <views:TransferWafer />
                                </Border>
                            </TabItem>
                        </TabControl>
                    </Border>

                    <!--  分隔线  -->
                    <GridSplitter
                        Grid.Column="1"
                        Width="5"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Stretch" />

                    <!--  左侧导航栏  -->
                    <Border Grid.Column="2">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="5*" MinHeight="200" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="4*" MinHeight="200" />
                            </Grid.RowDefinitions>
                            <!--  右上侧：Robot状态信息  -->
                            <dock:RobotStatusPanel />

                            <!--  上下分隔线：往下拖动，上面的RobotStatusPanel中的表格没有变大  -->
                            <GridSplitter
                                Grid.Row="1"
                                Height="5"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                Background="LightGray"
                                ResizeBehavior="PreviousAndNext"
                                ResizeDirection="Rows"
                                ShowsPreview="False" />
                            <!--  右下侧：日志显示  -->
                            <dock:LogView Grid.Row="2" />
                        </Grid>
                    </Border>
                </Grid>
            </Border>
            <!--  状态栏  -->
            <Border Grid.Row="1">
                <StatusBar
                    Height="25"
                    Background="#F5F5F5"
                    DockPanel.Dock="Bottom">
                    <StatusBar.ItemsPanel>
                        <ItemsPanelTemplate>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                            </Grid>
                        </ItemsPanelTemplate>
                    </StatusBar.ItemsPanel>

                    <!--  Shuttle状态  -->
                    <StatusBarItem Grid.Column="0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse
                                Width="8"
                                Height="8"
                                Margin="0,0,5,0"
                                VerticalAlignment="Center">
                                <Ellipse.Style>
                                    <Style TargetType="Ellipse">
                                        <Setter Property="Fill" Value="#FF4444" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding DeviceStatuses[Shuttle]}" Value="{x:Static services:DeviceStatus.Connected}">
                                                <Setter Property="Fill" Value="#4CAF50" />
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                Storyboard.TargetProperty="Width"
                                                                From="8"
                                                                To="10"
                                                                Duration="0:0:0.2" />
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                Storyboard.TargetProperty="Height"
                                                                From="8"
                                                                To="10"
                                                                Duration="0:0:0.2" />
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </DataTrigger.EnterActions>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding DeviceStatuses[Shuttle]}" Value="{x:Static services:DeviceStatus.Busy}">
                                                <Setter Property="Fill" Value="#FFC107" />
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                Storyboard.TargetProperty="Width"
                                                                From="8"
                                                                To="10"
                                                                Duration="0:0:0.2" />
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                Storyboard.TargetProperty="Height"
                                                                From="8"
                                                                To="10"
                                                                Duration="0:0:0.2" />
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </DataTrigger.EnterActions>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding DeviceStatuses[Shuttle]}" Value="{x:Static services:DeviceStatus.Disconnected}">
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                RepeatBehavior="1x"
                                                                Storyboard.TargetProperty="Opacity"
                                                                From="0.7"
                                                                To="1"
                                                                Duration="0:0:0.5" />
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </DataTrigger.EnterActions>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Ellipse.Style>
                            </Ellipse>
                            <TextBlock>
                                <Run Text="Shuttle: " />
                                <Run Text="{Binding DeviceStatuses[Shuttle]}" />
                                <Run Text=" (" />
                                <Run Text="{Binding Source={x:Static local:App.AppIniConfig}, Path=ShuttleIp}" />
                                <Run Text=":" />
                                <Run Text="{Binding Source={x:Static local:App.AppIniConfig}, Path=ShuttlePort}" />
                                <Run Text=")" />
                            </TextBlock>
                        </StackPanel>
                    </StatusBarItem>

                    <!--  Robot状态  -->
                    <StatusBarItem Grid.Column="1" Margin="10,0,0,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse
                                Width="8"
                                Height="8"
                                Margin="0,0,5,0"
                                VerticalAlignment="Center">
                                <Ellipse.Style>
                                    <Style TargetType="Ellipse">
                                        <Setter Property="Fill" Value="#FF4444" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding DeviceStatuses[Robot]}" Value="{x:Static services:DeviceStatus.Connected}">
                                                <Setter Property="Fill" Value="#4CAF50" />
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                Storyboard.TargetProperty="Width"
                                                                From="8"
                                                                To="10"
                                                                Duration="0:0:0.2" />
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                Storyboard.TargetProperty="Height"
                                                                From="8"
                                                                To="10"
                                                                Duration="0:0:0.2" />
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </DataTrigger.EnterActions>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding DeviceStatuses[Robot]}" Value="{x:Static services:DeviceStatus.Busy}">
                                                <Setter Property="Fill" Value="#FFC107" />
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                Storyboard.TargetProperty="Width"
                                                                From="8"
                                                                To="10"
                                                                Duration="0:0:0.2" />
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                Storyboard.TargetProperty="Height"
                                                                From="8"
                                                                To="10"
                                                                Duration="0:0:0.2" />
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </DataTrigger.EnterActions>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding DeviceStatuses[Robot]}" Value="{x:Static services:DeviceStatus.Disconnected}">
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation
                                                                AutoReverse="True"
                                                                RepeatBehavior="1x"
                                                                Storyboard.TargetProperty="Opacity"
                                                                From="0.7"
                                                                To="1"
                                                                Duration="0:0:0.5" />
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </DataTrigger.EnterActions>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Ellipse.Style>
                            </Ellipse>
                            <TextBlock>
                                <Run Text="Robot: " />
                                <Run Text="{Binding DeviceStatuses[Robot]}" />
                                <Run Text=" (" />
                                <Run Text="{Binding Source={x:Static local:App.AppIniConfig}, Path=RobotIp}" />
                                <Run Text=":" />
                                <Run Text="{Binding Source={x:Static local:App.AppIniConfig}, Path=RobotPort}" />
                                <Run Text=")" />
                            </TextBlock>
                        </StackPanel>
                    </StatusBarItem>

                    <!--  ChamberA状态  -->
                    <StatusBarItem Grid.Column="2" Margin="10,0,0,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse
                                Width="8"
                                Height="8"
                                Margin="0,0,5,0"
                                VerticalAlignment="Center">
                                <Ellipse.Style>
                                    <Style TargetType="Ellipse">
                                        <Setter Property="Fill" Value="#FF4444" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding DeviceStatuses[ChamberA]}" Value="{x:Static services:DeviceStatus.Connected}">
                                                <Setter Property="Fill" Value="#4CAF50" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding DeviceStatuses[ChamberA]}" Value="{x:Static services:DeviceStatus.Busy}">
                                                <Setter Property="Fill" Value="#4CAF50" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Ellipse.Style>
                            </Ellipse>
                            <TextBlock>
                                <Run Text="ChamberA: " />
                                <Run Text="{Binding DeviceStatuses[ChamberA]}" />
                                <Run Text=" (" />
                                <Run Text="{Binding Source={x:Static local:App.AppIniConfig}, Path=ChaIp}" />
                                <Run Text=":" />
                                <Run Text="{Binding Source={x:Static local:App.AppIniConfig}, Path=ChaPort}" />
                                <Run Text=")" />
                            </TextBlock>
                        </StackPanel>
                    </StatusBarItem>

                    <!--  CHB状态  -->
                    <StatusBarItem Grid.Column="3" Margin="10,0,0,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse
                                Width="8"
                                Height="8"
                                Margin="0,0,5,0"
                                VerticalAlignment="Center">
                                <Ellipse.Style>
                                    <Style TargetType="Ellipse">
                                        <Setter Property="Fill" Value="#FF4444" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding DeviceStatuses[ChamberB]}" Value="{x:Static services:DeviceStatus.Connected}">
                                                <Setter Property="Fill" Value="#4CAF50" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding DeviceStatuses[ChamberB]}" Value="{x:Static services:DeviceStatus.Busy}">
                                                <Setter Property="Fill" Value="#4CAF50" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Ellipse.Style>
                            </Ellipse>
                            <TextBlock>
                                <Run Text="ChamberB: " />
                                <Run Text="{Binding DeviceStatuses[ChamberB]}" />
                                <Run Text=" (" />
                                <Run Text="{Binding Source={x:Static local:App.AppIniConfig}, Path=ChbIp}" />
                                <Run Text=":" />
                                <Run Text="{Binding Source={x:Static local:App.AppIniConfig}, Path=ChbPort}" />
                                <Run Text=")" />
                            </TextBlock>
                        </StackPanel>
                    </StatusBarItem>

                    <!--  版本信息  -->
                    <StatusBarItem Grid.Column="5">
                        <TextBlock
                            Margin="10,0,5,0"
                            Foreground="#666666"
                            Text="{Binding Title}" />
                    </StatusBarItem>
                </StatusBar>
            </Border>
        </Grid>
    </materialDesign:DialogHost>
</Window>