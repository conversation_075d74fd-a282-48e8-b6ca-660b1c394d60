using System;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// SS200InterLockMain 配置测试示例
    /// 用于验证机器人位置配置是否正确加载
    /// </summary>
    public class SS200InterLockMainConfigTest
    {
        /// <summary>
        /// 测试机器人位置配置加载
        /// </summary>
        public static void TestRobotPositionConfiguration()
        {
            try
            {
                Console.WriteLine("=== SS200InterLockMain 配置测试 ===");
                
                // 获取单例实例
                var instance = SS200InterLockMain.Instance;
                Console.WriteLine("✓ SS200InterLockMain 单例实例获取成功");
                
                // 测试 RP1 配置访问
                try
                {
                    var rp1Value = instance.SubsystemConfigure.Robot.RP1_TAxisSmoothToCHA?.Value;
                    Console.WriteLine($"✓ RP1_TAxisPosition 配置值: {rp1Value}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ RP1_TAxisPosition 访问失败: {ex.Message}");
                }
                
                // 测试 RP2 配置访问
                try
                {
                    var rp2Value = instance.SubsystemConfigure.Robot.RP2_TAxisSmoothToCHB?.Value;
                    Console.WriteLine($"✓ RP2_RAxisPosition 配置值: {rp2Value}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ RP2_RAxisPosition 访问失败: {ex.Message}");
                }
                
                // 测试其他几个配置
                string[] testConfigs = { "RP3", "RP4", "RP5", "RP10", "RP27", "RP28" };
                foreach (var config in testConfigs)
                {
                    try
                    {
                        // 通过反射获取属性值（简化测试）
                        var property = instance.SubsystemConfigure.Robot.GetType().GetProperty($"{config}_*");
                        if (property != null)
                        {
                            var accessor = property.GetValue(instance.SubsystemConfigure.Robot);
                            if (accessor != null)
                            {
                                var valueProperty = accessor.GetType().GetProperty("Value");
                                if (valueProperty != null)
                                {
                                    var value = valueProperty.GetValue(accessor);
                                    Console.WriteLine($"✓ {config} 配置值: {value}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"✗ {config} 访问失败: {ex.Message}");
                    }
                }
                
                Console.WriteLine("=== 配置测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }
        
        /// <summary>
        /// 测试IO接口访问
        /// </summary>
        public static void TestIOInterfaceAccess()
        {
            try
            {
                Console.WriteLine("\n=== IO接口访问测试 ===");
                
                var instance = SS200InterLockMain.Instance;
                
                // 测试机器人IO访问
                try
                {
                    var robotDI1 = instance.IOInterface.Robot.RDI1_PaddleSensor1Left;
                    Console.WriteLine($"✓ Robot DI1 访问成功: {robotDI1.Content}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Robot DI1 访问失败: {ex.Message}");
                }
                
                // 测试腔室IO访问
                try
                {
                    var chamberADI13 = instance.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor;
                    Console.WriteLine($"✓ Chamber A PDI13 访问成功: {chamberADI13.Content}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Chamber A PDI13 访问失败: {ex.Message}");
                }
                
                Console.WriteLine("=== IO接口测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ IO接口测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试报警代码访问
        /// </summary>
        public static void TestAlarmCodeAccess()
        {
            try
            {
                Console.WriteLine("\n=== 报警代码访问测试 ===");
                
                var instance = SS200InterLockMain.Instance;
                
                // 测试机器人报警访问
                try
                {
                    var robotAlarm = instance.AlarmCode.Robot.RA1_SystemBusyReject;
                    Console.WriteLine($"✓ Robot Alarm RA1 访问成功: {robotAlarm.Content}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Robot Alarm RA1 访问失败: {ex.Message}");
                }
                
                Console.WriteLine("=== 报警代码测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 报警代码测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始运行 SS200InterLockMain 综合测试...\n");
            
            TestRobotPositionConfiguration();
            TestIOInterfaceAccess();
            TestAlarmCodeAccess();
            
            Console.WriteLine("\n所有测试完成！");
        }
    }
}
