﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models
{
    /// <summary>
    /// 状态属性
    /// </summary>
    public class StatusProperty : INotifyPropertyChanged
    {
        private string _value;
        private object _editValue;
        private bool _isModified;

        /// <summary>
        /// 属性名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 属性值（显示用）
        /// </summary>
        public string Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    _value = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 编辑值（用户手动编辑的值）
        /// </summary>
        public object EditValue
        {
            get => _editValue ?? GetTypedValue(_value);
            set
            {
                if (!Equals(_editValue, value))
                {
                    _editValue = value;
                    IsModified = !IsValueEqualToOriginal(_editValue);
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 设备类型
        /// </summary>
        public EnuMcuDeviceType DeviceType { get; set; }

        /// <summary>
        /// 子系统类型（用于更细粒度的分类），预留，暂时不使用
        /// </summary>
        public string SubsystemType { get; set; }

        /// <summary>
        /// 是否可编辑
        /// </summary>
        public bool IsEditable { get; set; }

        /// <summary>
        /// 属性类型
        /// </summary>
        public Type PropertyType { get; set; }

        /// <summary>
        /// 原始值（用于恢复功能）
        /// </summary>
        public string OriginalValue { get; set; }

        /// <summary>
        /// 枚举选项（如果是枚举类型）
        /// </summary>
        public List<object> EnumOptions { get; set; }

        /// <summary>
        /// 是否被手动修改过
        /// </summary>
        public bool IsModified
        {
            get => _isModified;
            set
            {
                if (_isModified != value)
                {
                    _isModified = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 属性路径（用于反射设置值）
        /// </summary>
        public string PropertyPath { get; set; }

        /// <summary>
        /// 目标对象引用
        /// </summary>
        public object TargetObject { get; set; }

        /// <summary>
        /// 是否为空值
        /// </summary>
        public bool IsNullValue => string.IsNullOrEmpty(Value) || Value == "null";

        /// <summary>
        /// 属性类型分类
        /// </summary>
        public PropertyTypeCategory TypeCategory
        {
            get
            {
                if (PropertyType == null) return PropertyTypeCategory.String;

                if (PropertyType == typeof(bool) || PropertyType == typeof(bool?))
                    return PropertyTypeCategory.Boolean;

                if (PropertyType.IsEnum)
                    return PropertyTypeCategory.Enum;

                if (PropertyType == typeof(int) || PropertyType == typeof(int?) ||
                    PropertyType == typeof(double) || PropertyType == typeof(double?) ||
                    PropertyType == typeof(float) || PropertyType == typeof(float?) ||
                    PropertyType == typeof(decimal) || PropertyType == typeof(decimal?) ||
                    PropertyType == typeof(ushort) || PropertyType == typeof(ushort?) ||
                    PropertyType == typeof(short) || PropertyType == typeof(short?) ||
                    PropertyType == typeof(uint) || PropertyType == typeof(uint?) ||
                    PropertyType == typeof(long) || PropertyType == typeof(long?) ||
                    PropertyType == typeof(ulong) || PropertyType == typeof(ulong?))
                    return PropertyTypeCategory.Numeric;

                return PropertyTypeCategory.String;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 保存当前值作为原始值
        /// </summary>
        public void SaveAsOriginal()
        {
            OriginalValue = Value;
            EditValue = GetTypedValue(Value);
            IsModified = false;
        }

        /// <summary>
        /// 恢复到原始值
        /// </summary>
        public void RestoreToOriginal()
        {
            EditValue = GetTypedValue(OriginalValue);
            IsModified = false;
        }

        /// <summary>
        /// 将字符串值转换为对应的类型值
        /// </summary>
        private object GetTypedValue(string stringValue)
        {
            if (string.IsNullOrEmpty(stringValue) || PropertyType == null)
                return stringValue;

            try
            {
                if (PropertyType == typeof(bool) || PropertyType == typeof(bool?))
                {
                    return bool.Parse(stringValue);
                }
                else if (PropertyType.IsEnum)
                {
                    return Enum.Parse(PropertyType, stringValue);
                }
                else
                {
                    return stringValue;
                }
            }
            catch
            {
                return stringValue;
            }
        }

        /// <summary>
        /// 检查编辑值是否与原始值相等
        /// </summary>
        private bool IsValueEqualToOriginal(object editValue)
        {
            if (string.IsNullOrEmpty(OriginalValue))
                return editValue == null || editValue.ToString() == "";

            if (editValue == null)
                return string.IsNullOrEmpty(OriginalValue);

            // 对于布尔值，进行特殊处理
            if (PropertyType == typeof(bool) || PropertyType == typeof(bool?))
            {
                if (editValue is bool boolValue)
                {
                    return string.Equals(boolValue.ToString(), OriginalValue, StringComparison.OrdinalIgnoreCase) ||
                           string.Equals(boolValue.ToString().ToLower(), OriginalValue, StringComparison.OrdinalIgnoreCase);
                }
            }

            // 对于枚举值，比较名称
            if (PropertyType?.IsEnum == true)
            {
                return string.Equals(editValue.ToString(), OriginalValue, StringComparison.OrdinalIgnoreCase);
            }

            // 对于其他类型，比较字符串表示
            return string.Equals(editValue.ToString(), OriginalValue, StringComparison.Ordinal);
        }
    }

    /// <summary>
    /// 属性类型分类
    /// </summary>
    public enum PropertyTypeCategory
    {
        String,
        Boolean,
        Enum,
        Numeric
    }
}