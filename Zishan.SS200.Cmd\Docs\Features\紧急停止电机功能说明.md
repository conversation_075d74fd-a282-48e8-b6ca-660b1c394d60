# 紧急停止电机功能说明

## 概述

为RobotParameter.json配置对应的UI界面添加了紧急停止电机按钮功能，该功能直接调用底层Modbus命令来停止机器人的T、R、Z三轴电机。

## 功能特性

### 1. 紧急停止按钮
- **位置**: BasicCommandTest.xaml界面中
- **样式**: 红色危险按钮样式 (`ButtonDanger`)
- **提示**: "紧急停止按钮直接发出3轴停止电机命令"

### 2. 实现原理
- 直接调用底层Modbus命令
- 使用`EnuRobotCmdIndex.MotorStop`命令索引
- 分别对T轴(1)、R轴(2)、Z轴(3)发送停止命令
- 遵循RobotParameter.json中的MotorStop配置

## 技术实现

### 1. 命令配置
根据RobotParameter.json中的MotorStop配置：
```json
"MotorStop": {
  "CMDIndex": 2,
  "Prompt": "停止电机",
  "ConfigPara": [],
  "RunPara": [
    { "MoveAxis": 1 }
  ],
  "TimeOut": 1000
}
```

### 2. 轴参数说明
- **MoveAxis = 1**: T轴（旋转轴）
- **MoveAxis = 2**: R轴（伸缩轴）  
- **MoveAxis = 3**: Z轴（升降轴）

### 3. 代码实现
在`BasicCommandTestViewModel.cs`中添加了`EmergencyStopCommand`：

```csharp
[RelayCommand]
private async Task EmergencyStop()
{
    // 检查Robot连接状态
    if (!_mcuCmdService.Robot.IsConnected)
    {
        UILogService.AddErrorLog("Robot未连接，无法执行紧急停止命令");
        return;
    }

    // 依次停止T轴、R轴、Z轴电机
    for (int axis = 1; axis <= 3; axis++)
    {
        var result = await _mcuCmdService.Robot.Run(
            EnuRobotCmdIndex.MotorStop, 
            new List<ushort> { (ushort)axis }
        );
        // 处理结果...
    }
}
```

## 使用方法

### 1. 界面操作
1. 打开BasicCommandTest界面
2. 确保Robot设备已连接
3. 点击红色"紧急停止"按钮
4. 系统将依次停止T、R、Z三轴电机

### 2. 执行流程
1. **连接检查**: 验证Robot设备连接状态
2. **状态设置**: 设置执行状态防止重复操作
3. **依次停止**: 按T→R→Z顺序停止各轴电机
4. **结果反馈**: 显示每个轴的停止结果
5. **状态恢复**: 恢复原始执行状态

### 3. 日志记录
系统会记录详细的操作日志：
- 🚨 执行紧急停止命令开始
- 正在停止X轴电机...
- ✅ X轴电机停止成功 / ❌ X轴电机停止失败
- 🚨 紧急停止命令执行完成

## 安全特性

### 1. 连接检查
- 执行前检查Robot设备连接状态
- 未连接时显示错误提示并终止操作

### 2. 状态管理
- 保存和恢复原始执行状态
- 防止执行过程中的状态冲突

### 3. 异常处理
- 完整的try-catch异常处理
- 详细的错误日志记录
- 用户友好的错误提示

### 4. 执行反馈
- 实时显示执行进度
- 每个轴的停止结果反馈
- 成功/失败状态提示

## 注意事项

### 1. 配置文件限制
- **重要**: RobotParameter.json配置文件不能修改
- 必须遵循现有的MotorStop命令配置
- 超时时间固定为1000ms

### 2. 执行顺序
- 按T轴→R轴→Z轴的固定顺序执行
- 每个轴独立执行，不会因单个轴失败而中断整个流程

### 3. 设备状态
- 仅在Robot设备连接时可用
- 执行过程中会临时设置执行状态

## 相关文件

### 1. 界面文件
- `Views/BasicCommandTest.xaml` - UI界面定义
- `ViewModels/BasicCommandTestViewModel.cs` - 业务逻辑实现

### 2. 配置文件
- `Configs/CmdParameter/RobotParameter.json` - 命令参数配置

### 3. 枚举定义
- `Enums/McuCmdIndex/EnuRobotCmdIndex.cs` - Robot命令索引枚举

### 4. 服务接口
- `Services/S200McuCmdService.cs` - Modbus命令服务
- `Services/Interfaces/IS200McuCmdService.cs` - 服务接口定义

## 测试建议

### 1. 功能测试
- 验证按钮点击响应
- 检查三轴停止命令执行
- 确认日志记录完整性

### 2. 异常测试
- 设备未连接时的错误处理
- 网络中断时的异常处理
- 命令执行失败时的错误反馈

### 3. 状态测试
- 执行状态的正确设置和恢复
- 重复点击的防护机制
- 与其他命令的状态冲突处理

## 版本信息

- **实现日期**: 2025-01-23
- **实现版本**: v1.0
- **负责模块**: Robot控制模块
- **依赖组件**: Modbus通讯服务、Robot设备服务
