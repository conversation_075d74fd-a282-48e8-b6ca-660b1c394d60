using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Enums.Basic;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// 层次化日志测试类
    /// 用于测试和演示新的层次化日志功能
    /// </summary>
    public class HierarchicalLoggingTest
    {
        /// <summary>
        /// 测试基本的层次化日志功能
        /// </summary>
        public static async Task TestBasicHierarchicalLogging()
        {
            UILogService.AddLog("=== 开始测试层次化日志功能 ===");

            // 模拟原始的平铺日志输出
            UILogService.AddLog("--- 原始平铺日志输出 ---");
            UILogService.AddLog("执行搬运，Robot连接状态: True，服务实例: McuCmdService");
            UILogService.AddLog("搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2) 机械臂端口: Nose");
            UILogService.AddLog("取Wafer: 从ChamberA(SLOT:1)获取Wafer");
            UILogService.AddLog("开始取Wafer...");
            UILogService.AddLog("结束取Wafer...");
            UILogService.AddLog("放Wafer: 将Wafer放置到ChamberB(SLOT:2)");
            UILogService.AddLog("开始放Wafer...");
            UILogService.AddLog("结束放Wafer...");
            UILogService.AddSuccessLog("搬运Wafer成功: Wafer已成功从ChamberA(SLOT:1)搬运到ChamberB(SLOT:2)");

            await Task.Delay(1000);

            // 使用新的层次化日志
            UILogService.AddLog("--- 新的层次化日志输出 ---");
            await TestHierarchicalWaferTransfer();

            UILogService.AddSuccessLog("=== 层次化日志功能测试完成 ===\r\n");
        }

        /// <summary>
        /// 测试层次化的晶圆传输日志
        /// </summary>
        private static async Task TestHierarchicalWaferTransfer()
        {
            UILogService.AddLogAndIncreaseIndent("执行搬运，Robot连接状态: True，服务实例: McuCmdService");

            using (UILogService.CreateIndentScope())
            {
                UILogService.AddLog("搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2) 机械臂端口: Nose");

                // 取Wafer操作
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("取Wafer: 从ChamberA(SLOT:1)获取Wafer");

                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("开始取Wafer...");
                        await Task.Delay(200);

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("检查InterLock安全状态");
                            UILogService.AddLog("确认ChamberA位置有晶圆");
                            UILogService.AddLog("确认Robot机械臂上无晶圆");
                        }

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("移动T轴到ChamberA位置");
                            UILogService.AddLog("移动Z轴到取片位置");
                            UILogService.AddLog("伸展R轴到ChamberA位置");
                        }

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("执行ChamberA顶针升降取片操作");
                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog("等待ChamberA顶针上升，将晶圆顶起到取片高度");
                                UILogService.AddLog("顶针上升完成");
                            }
                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog("等待ChamberA顶针下降，将晶圆放置到Robot机械臂上");
                                UILogService.AddLog("顶针下降完成");
                            }
                            UILogService.AddSuccessLog("ChamberA顶针升降取片操作完成");
                        }

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("R轴归零");
                            UILogService.AddSuccessLog("R轴归零成功");
                        }

                        UILogService.AddSuccessLog("结束取Wafer...");
                    }
                }

                // 放Wafer操作
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("放Wafer: 将Wafer放置到ChamberB(SLOT:2)");

                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("开始放Wafer...");
                        await Task.Delay(200);

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("检查ChamberB位置是否无晶圆");
                            UILogService.AddLog("确认Robot机械臂上有晶圆");
                        }

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("移动T轴到ChamberB位置");
                            UILogService.AddLog("移动Z轴到放片位置");
                            UILogService.AddLog("伸展R轴到ChamberB位置");
                        }

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("执行ChamberB顶针升降放片操作");
                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog("等待ChamberB顶针下降放片");
                                UILogService.AddLog("顶针下降完成");
                            }
                            UILogService.AddSuccessLog("ChamberB顶针升降放片操作完成");
                        }

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("R轴归零");
                            UILogService.AddSuccessLog("R轴归零成功");
                        }

                        UILogService.AddSuccessLog("结束放Wafer...");
                    }
                }
            }

            UILogService.DecreaseIndentAndAddSuccessLog("搬运Wafer成功: Wafer已成功从ChamberA(SLOT:1)搬运到ChamberB(SLOT:2)\r\n");
        }

        /// <summary>
        /// 测试不同的缩进配置
        /// </summary>
        public static void TestIndentConfigurations()
        {
            UILogService.AddLog("=== 测试不同的缩进配置 ===");

            // 测试默认缩进（4个空格）
            UILogService.AddLog("测试默认缩进（4个空格）:");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第一层缩进");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第二层缩进");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第三层缩进");
            UILogService.ResetIndent();

            // 测试2个空格缩进
            UILogService.SetIndentString("  ");
            UILogService.AddLog("测试2个空格缩进:");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第一层缩进");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第二层缩进");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第三层缩进");
            UILogService.ResetIndent();

            // 测试Tab缩进
            UILogService.SetIndentString("\t");
            UILogService.AddLog("测试Tab缩进:");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第一层缩进");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第二层缩进");
            UILogService.IncreaseIndent();
            UILogService.AddLog("第三层缩进");
            UILogService.ResetIndent();

            // 恢复默认设置
            UILogService.SetIndentString("    ");
            UILogService.AddSuccessLog("缩进配置测试完成，已恢复默认设置\r\n");
        }

        /// <summary>
        /// 测试最大缩进层级限制
        /// </summary>
        public static void TestMaxIndentLevel()
        {
            UILogService.AddLog("=== 测试最大缩进层级限制 ===");

            // 设置最大缩进层级为3
            UILogService.SetMaxIndentLevel(3);
            UILogService.AddLog("设置最大缩进层级为3，测试超出限制的情况:");

            for (int i = 1; i <= 6; i++)
            {
                UILogService.IncreaseIndent();
                UILogService.AddLog($"尝试增加到第{i}层缩进，当前实际层级: {UILogService.GetCurrentIndentLevel()}");
            }

            UILogService.ResetIndent();
            UILogService.SetMaxIndentLevel(10); // 恢复默认值
            UILogService.AddSuccessLog("最大缩进层级测试完成\r\n");
        }

        /// <summary>
        /// 测试错误处理中的层次化日志
        /// </summary>
        public static async Task TestErrorHandlingWithHierarchy()
        {
            UILogService.AddLog("=== 测试错误处理中的层次化日志 ===");

            UILogService.AddLogAndIncreaseIndent("开始执行可能失败的操作");

            try
            {
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("尝试连接设备");

                    // 模拟随机失败
                    var random = new Random();
                    if (random.Next(2) == 0)
                    {
                        throw new Exception("模拟的设备连接失败");
                    }

                    UILogService.AddSuccessLog("设备连接成功");

                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("执行设备操作");
                        await Task.Delay(500);
                        UILogService.AddSuccessLog("设备操作完成");
                    }
                }

                UILogService.DecreaseIndentAndAddSuccessLog("操作成功完成\r\n");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"操作失败: {ex.Message}");

                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("开始错误恢复流程");
                    UILogService.AddLog("重置设备状态...");
                    UILogService.AddLog("清理资源...");
                    UILogService.AddLog("错误恢复完成");
                }

                UILogService.DecreaseIndentAndAddLog("操作结束，已处理错误\r\n");
            }
        }

        /// <summary>
        /// 测试UI日志调用信息显示功能
        /// </summary>
        public static async Task TestCallerInfoDisplay()
        {
            UILogService.AddLog("=== 测试UI日志调用信息显示功能 ===");

            // 测试不同类型的日志方法
            UILogService.AddLog("这是普通日志消息");
            UILogService.AddSuccessLog("这是成功日志消息");
            UILogService.AddErrorLog("这是错误日志消息");
            UILogService.AddWarningLog("这是警告日志消息");
            UILogService.AddInfoLog("这是信息日志消息");

            // 测试层次化日志中的调用信息
            UILogService.AddLogAndIncreaseIndent("开始层次化日志测试");

            using (UILogService.CreateIndentScope())
            {
                UILogService.AddLog("第一层缩进日志");

                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("第二层缩进日志");
                    UILogService.AddSuccessLog("第二层成功日志");
                }

                UILogService.AddLog("返回第一层");
            }

            UILogService.DecreaseIndentAndAddSuccessLog("层次化日志测试完成");

            UILogService.AddLog("提示：调用信息的显示由Config.ini中的ShowCallerInfoInUILog配置项控制");
            UILogService.AddLog("当前配置状态可在日志消息中查看是否包含(Line: xxx, Method: xxx)信息");

            await Task.Delay(100); // 短暂延时以便观察

            UILogService.AddSuccessLog("=== UI日志调用信息显示功能测试完成 ===\r\n");
        }

        /// <summary>
        /// 测试机器人初始化的层次化日志
        /// </summary>
        public static async Task TestRobotInitializationWithHierarchy()
        {
            UILogService.AddLog("=== 测试机器人初始化层次化日志 ===");

            // 模拟机器人初始化过程
            UILogService.AddLogAndIncreaseIndent("开始初始化机器人（三轴归零）");

            try
            {
                // 1. T轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行T轴归零");
                    await Task.Delay(300);

                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("检查T轴当前位置");
                        UILogService.AddLog("发送T轴归零命令");
                        UILogService.AddLog("等待T轴归零完成");
                        UILogService.AddLog("确认T轴已到达零位");
                    }

                    UILogService.AddSuccessLog("T轴归零成功");
                }

                // 2. R轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行R轴归零");
                    await Task.Delay(300);

                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("检查R轴当前位置");
                        UILogService.AddLog("发送R轴归零命令");
                        UILogService.AddLog("等待R轴归零完成");
                        UILogService.AddLog("确认R轴已到达零位");
                    }

                    UILogService.AddSuccessLog("R轴归零成功");
                }

                // 3. Z轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行Z轴归零");
                    await Task.Delay(300);

                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("检查Z轴当前位置");
                        UILogService.AddLog("发送Z轴归零命令");
                        UILogService.AddLog("等待Z轴归零完成");
                        UILogService.AddLog("确认Z轴已到达零位");
                    }

                    UILogService.AddSuccessLog("Z轴归零成功");
                }

                UILogService.DecreaseIndentAndAddSuccessLog("机器人三轴归零成功");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化异常: {ex.Message}");
            }

            UILogService.AddSuccessLog("机器人初始化层次化日志测试完成");
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            UILogService.AddLog("🚀 开始运行层次化日志测试套件");

            await TestBasicHierarchicalLogging();
            await Task.Delay(500);

            await TestCallerInfoDisplay();
            await Task.Delay(500);

            await TestRobotInitializationWithHierarchy();
            await Task.Delay(500);

            TestIndentConfigurations();
            await Task.Delay(500);

            TestMaxIndentLevel();
            await Task.Delay(500);

            await TestErrorHandlingWithHierarchy();

            UILogService.AddSuccessLog("🎉 层次化日志测试套件运行完成");
        }
    }
}