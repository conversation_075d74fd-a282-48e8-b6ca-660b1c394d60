ASP1 
OD SD
	chamber trigger status review
MPS1~MPS2
		MPS1 IDLE
no alarm
			chamber run status review
MPS3~MPS5
				MPS4 / MPS3B
					chamber slit door status review
SP1~SP3
						SP1
slit door open
							command done
						others status
							PAC6 ALARM
						SP2 or SP3
							chamber pressure review
RPS29
								Y
									chamber pressure review
PAI1
										PAI1≤PPS6
											compare with PAI1 and PAI6
												ABS(PAI6-PAI1)≤PPS5
													robot run status review
MRS1~3
														MRS1 or MRS3
robot run status is busy or alarm
															PAC18 ALARM
														MRS2
robot run status is idle
															robot R-axis status review
RS10~18 or others status
																RS18
robot R-axis at zero position
																	robot T-axis status review
RS1~9 or others status
																		one position of (RS1~9)
																			PDO10=1
PDO11=2
																				SP2---SP3---SP1
slit door status change logic
																					PPS1≤time≤PPS2
																						command done
																					time>PPS2
																						PAC8 ALARM
																					time<PPS1
																						PAC9 ALARM
																				others status change logic
																					PAC5 ALARM
																		others status
																			PAC17 ALARM
																others status
																	PAC17 ALARM
												ABS(PAI6-PAI1)>PPS5
													PAC4 ALARM
										PAI1>PPS6
											PAC7 ALARM
								N
									robot run status review
MRS1~3
										MRS1 or MRS3
robot run status is busy or alarm
											PAC18 ALARM
										MRS2
robot run status is idle
											robot R-axis status review
RS10~18 or others status
												RS18
robot R-axis at zero position
													robot T-axis status review
RS1~9 or others status
														one position of (RS1~9)
															PDO10=1
PDO11=2
																SP2---SP3---SP1
slit door status change logic
																	PPS1≤time≤PPS2
																		command done
																	time>PPS2
																		PAC8 ALARM
																	time<PPS1
																		PAC9 ALARM
																others status change logic
																	PAC5 ALARM
														others status
															PAC17 ALARM
												others status
													PAC17 ALARM
				MPS3A
					PAC2 ALARM
				MPS5
					PAC3 ALARM
		MPS2
alarm
			PAC1 ALARM