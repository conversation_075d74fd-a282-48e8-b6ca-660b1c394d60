﻿using System.Collections.Generic;
using Prism.Mvvm;
using System.Windows.Controls;
using CommunityToolkit.Mvvm.ComponentModel;

// using Zishan.Robot.Core.Mvvm;
// using Zishan.Robot.Shared.AttributeExtend;
// using Zishan.Robot.Shared.Enums;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Mvvm;

namespace Zishan.SS200.Cmd.DTO
{
    public partial class DtoRunRecipeInfo : ViewModel
    {
        public int RecipeId { get; set; }

        /// <summary>
        /// 花篮Slot最大数为:51，实际使用1~25
        /// </summary>
        public const int SlotMaxCount = 51;

        public bool IsRemote { get; set; }

        /// <summary>
        /// 花篮左边ID写到这里
        /// </summary>

        public string LeftBarcode { get => _LeftBarcode; set => SetProperty(ref _LeftBarcode, value); }
        private string _LeftBarcode;

        /// <summary>
        /// 花篮右边ID写到这里
        /// </summary>

        public string RightBarcode { get => _RightBarcode; set => SetProperty(ref _RightBarcode, value); }
        private string _RightBarcode;

        /// <summary>
        /// Sequence配方名称写到这里
        /// </summary>

        public string RecipeName { get => _RecipeName; set => SetProperty(ref _RecipeName, value); }
        private string _RecipeName;

        [ObservableProperty]
        private bool runContinuous;

        /// <summary>
        /// 花篮顶部Slot号，上面是上限，比如：25
        /// </summary>
        public int Top
        {
            get => _Top;
            set
            {
                if (SetProperty(ref _Top, value))
                {
                    CalculateRecipeIds();
                }
            }
        }
        private int _Top;

        /// <summary>
        /// 花篮低部Slot号，下面是下限，比如：1
        /// </summary>
        public int Bottom
        {
            get => _Bottom;
            set
            {
                if (SetProperty(ref _Bottom, value))
                {
                    CalculateRecipeIds();
                }
            }
        }
        private int _Bottom;

        /// <summary>
        /// 根据花篮Top和Bottom计算出来的RecipeId
        /// </summary>
        //[AdsVariable("GVL_SV.Stage_L_SV.MapingStatus_Target", CustomReadAccess = AccessMode.WriteOnly, CustomWriteAccess = AccessMode.WriteOnly)]
        public List<EnuWaferMappingStatus> LeftRecipeIds { get; set; }

        /// <summary>
        /// 根据花篮Top和Bottom计算出来的RecipeId
        /// </summary>
        //[AdsVariable("GVL_SV.Stage_R_SV.MapingStatus_Target", CustomReadAccess = AccessMode.WriteOnly, CustomWriteAccess = AccessMode.WriteOnly)]
        public List<EnuWaferMappingStatus> RightRecipeIds { get; set; }

        public DtoRunRecipeInfo()
        {
            LeftRecipeIds = new List<EnuWaferMappingStatus>(new EnuWaferMappingStatus[SlotMaxCount]);
            RightRecipeIds = new List<EnuWaferMappingStatus>(new EnuWaferMappingStatus[SlotMaxCount]);
        }

        public DtoRunRecipeInfo(bool isRemote = false) : this()
        {
            IsRemote = isRemote;
        }

        /// <summary>
        /// 根据花篮Top和Bottom范围区间、花篮Slot最大数计算出来的RecipeId
        /// </summary>
        /// <returns></returns>
        public void CalculateRecipeIds()
        {
            //for (int i = 0; i < SlotMaxCount; i++)
            //{
            //    var WafertExistFlag = EnuWaferMappingStatus.NotExist;
            //    if (i >= Bottom && i <= Top)
            //    {
            //        WafertExistFlag = EnuWaferMappingStatus.Exist;
            //    }
            //    LeftRecipeIds[i] = WafertExistFlag;
            //    RightRecipeIds[i] = WafertExistFlag;
            //}
        }

        /// <summary>
        ///根据 Bottom到Top之间LeftRecipeIds、RightRecipeIds都为空的Index取出来
        /// </summary>
        /// <returns></returns>
        public List<int> GetFullEmptySlots()
        {
            List<int> lstFullEmptySlots = new List<int>();

            /*
			// 暂时注释掉
            for (int i = Bottom; i <= Top; i++)
            {
                if (LeftRecipeIds[i] == EnuWaferMappingStatus.NotExist && RightRecipeIds[i] == EnuWaferMappingStatus.NotExist)
                {
                    lstFullEmptySlots.Add(i);
                }
            }
            */
            return lstFullEmptySlots;
        }

        public override string ToString()
        {
            var strRet = $"LeftBarcode:{LeftBarcode},RightBarcode:{RightBarcode},RecipeName:{RecipeName},Top:{Top},Bottom:{Bottom}";
            return strRet;
        }
    }
}