using System;
using System.Globalization;
using System.Linq;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    public class MultiCommandParameterConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            // 假设所有参数都是字符串
            return values.Select(v => v?.ToString()).ToArray();
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}