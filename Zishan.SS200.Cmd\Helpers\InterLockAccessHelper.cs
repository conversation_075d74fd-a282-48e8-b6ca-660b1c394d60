using System;
using <PERSON>ishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Helpers
{
    /// <summary>
    /// InterLock 访问优化助手类
    /// 提供缓存的 InterLock 访问，减少重复的单例调用，提高性能
    /// </summary>
    public static class InterLockAccessHelper
    {
        /// <summary>
        /// 执行带有缓存 InterLock 实例的操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void WithInterLock(Action<SS200InterLockMain> action)
        {
            if (action == null)
                throw new ArgumentNullException(nameof(action));

            var interLock = SS200InterLockMain.Instance;
            action(interLock);
        }

        /// <summary>
        /// 执行带有缓存 InterLock 实例的操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <returns>函数执行结果</returns>
        public static T WithInterLock<T>(Func<SS200InterLockMain, T> func)
        {
            if (func == null)
                throw new ArgumentNullException(nameof(func));

            var interLock = SS200InterLockMain.Instance;
            return func(interLock);
        }

        /// <summary>
        /// 执行带有缓存 IO 接口的操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void WithIOInterface(Action<IOInterfaceAccessor> action)
        {
            WithInterLock(interLock => action(interLock.IOInterface));
        }

        /// <summary>
        /// 执行带有缓存 IO 接口的操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <returns>函数执行结果</returns>
        public static T WithIOInterface<T>(Func<IOInterfaceAccessor, T> func)
        {
            return WithInterLock(interLock => func(interLock.IOInterface));
        }

        /// <summary>
        /// 执行带有缓存报警代码的操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void WithAlarmCode(Action<AlarmCodeAccessor> action)
        {
            WithInterLock(interLock => action(interLock.AlarmCode));
        }

        /// <summary>
        /// 执行带有缓存报警代码的操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <returns>函数执行结果</returns>
        public static T WithAlarmCode<T>(Func<AlarmCodeAccessor, T> func)
        {
            return WithInterLock(interLock => func(interLock.AlarmCode));
        }

        /// <summary>
        /// 执行带有缓存子系统配置的操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void WithSubsystemConfigure(Action<SubsystemConfigureAccessor> action)
        {
            WithInterLock(interLock => action(interLock.SubsystemConfigure));
        }

        /// <summary>
        /// 执行带有缓存子系统配置的操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <returns>函数执行结果</returns>
        public static T WithSubsystemConfigure<T>(Func<SubsystemConfigureAccessor, T> func)
        {
            return WithInterLock(interLock => func(interLock.SubsystemConfigure));
        }

        /// <summary>
        /// 执行带有缓存子系统状态的操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void WithSubsystemStatus(Action<SubsystemStatusAccessor> action)
        {
            WithInterLock(interLock => action(interLock.SubsystemStatus));
        }

        /// <summary>
        /// 执行带有缓存子系统状态的操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <returns>函数执行结果</returns>
        public static T WithSubsystemStatus<T>(Func<SubsystemStatusAccessor, T> func)
        {
            return WithInterLock(interLock => func(interLock.SubsystemStatus));
        }

        /// <summary>
        /// 获取 Robot 状态
        /// </summary>
        /// <returns>Robot 状态对象</returns>
        public static Models.SS200.SubSystemStatus.Robot.RobotSubsystemStatus GetRobotStatus()
        {
            return WithSubsystemStatus(status => status.Robot.Status);
        }

        /// <summary>
        /// 获取 Chamber A 状态
        /// </summary>
        /// <returns>Chamber A 状态对象</returns>
        public static Models.SS200.SubSystemStatus.Chamber.ChamberSubsystemStatus GetChamberAStatus()
        {
            return WithSubsystemStatus(status => status.ChamberA.Status);
        }

        /// <summary>
        /// 获取 Chamber B 状态
        /// </summary>
        /// <returns>Chamber B 状态对象</returns>
        public static Models.SS200.SubSystemStatus.Chamber.ChamberSubsystemStatus GetChamberBStatus()
        {
            return WithSubsystemStatus(status => status.ChamberB.Status);
        }

        /// <summary>
        /// 获取 Shuttle 状态
        /// </summary>
        /// <returns>Shuttle 状态对象</returns>
        public static Models.SS200.SubSystemStatus.Shuttle.ShuttleSubsystemStatus GetShuttleStatus()
        {
            return WithSubsystemStatus(status => status.Shuttle.Status);
        }

        /// <summary>
        /// 检查 Robot 是否在指定位置
        /// </summary>
        /// <param name="checkFunc">位置检查函数</param>
        /// <returns>是否在指定位置</returns>
        public static bool CheckRobotPosition(Func<Models.SS200.SubSystemStatus.Robot.RobotSubsystemStatus, bool> checkFunc)
        {
            var robotStatus = GetRobotStatus();
            return robotStatus != null && checkFunc(robotStatus);
        }

        /// <summary>
        /// 批量获取传感器状态
        /// </summary>
        /// <param name="sensorAccessors">传感器访问器数组</param>
        /// <returns>传感器状态数组</returns>
        public static bool[] GetSensorValues(params Func<IOInterfaceAccessor, bool>[] sensorAccessors)
        {
            return WithIOInterface(io =>
            {
                var results = new bool[sensorAccessors.Length];
                for (int i = 0; i < sensorAccessors.Length; i++)
                {
                    results[i] = sensorAccessors[i](io);
                }
                return results;
            });
        }

        /// <summary>
        /// 批量获取配置参数值
        /// </summary>
        /// <param name="configAccessors">配置访问器数组</param>
        /// <returns>配置值数组</returns>
        public static int[] GetConfigValues(params Func<SubsystemConfigureAccessor, int>[] configAccessors)
        {
            return WithSubsystemConfigure(config =>
            {
                var results = new int[configAccessors.Length];
                for (int i = 0; i < configAccessors.Length; i++)
                {
                    results[i] = configAccessors[i](config);
                }
                return results;
            });
        }
    }
}
