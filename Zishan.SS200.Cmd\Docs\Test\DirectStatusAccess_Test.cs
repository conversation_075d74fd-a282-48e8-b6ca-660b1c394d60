using System;
using <PERSON>ishan.SS200.Cmd.Models.SS200;
using <PERSON>ishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using <PERSON>ishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// 直接访问状态实体对象的测试示例
    /// </summary>
    public class DirectStatusAccessTest
    {
        /// <summary>
        /// 测试Robot状态直接访问
        /// </summary>
        public void TestRobotStatusDirectAccess()
        {
            try
            {
                // 直接获取完整的Robot状态实体对象
                var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
                
                if (robotStatus != null)
                {
                    Console.WriteLine("=== Robot状态信息 ===");
                    Console.WriteLine($"Robot主状态: {robotStatus.EnuRobotStatus}");
                    Console.WriteLine($"T轴Smooth目的地: {robotStatus.EnuTAxisSmoothDestination}");
                    Console.WriteLine($"T轴Nose目的地: {robotStatus.EnuTAxisNoseDestination}");
                    Console.WriteLine($"T轴零位: {robotStatus.TAxisIsZeroPosition}");
                    
                    Console.WriteLine($"R轴Smooth扩展: {robotStatus.EnuTAndRAxisSmoothExtendDestination}");
                    Console.WriteLine($"R轴Nose扩展: {robotStatus.EnuTAndRAxisNoseExtendDestination}");
                    Console.WriteLine($"R轴零位: {robotStatus.RAxisIsZeroPosition}");
                    
                    Console.WriteLine($"Z轴高度状态: {robotStatus.EnuTAndZAxisHeightStatus}");
                    Console.WriteLine($"Z轴零位: {robotStatus.ZAxisIsZeroPosition}");
                    
                    Console.WriteLine($"Pin Search状态: {robotStatus.PinSearchStatus}");
                    Console.WriteLine($"Pin Search数据有效: {robotStatus.EnuPinSearchDataEffective}");
                    
                    Console.WriteLine($"Smooth Paddle P1: {robotStatus.SmoothPaddleP1Status}");
                    Console.WriteLine($"Smooth Paddle P2: {robotStatus.SmoothPaddleP2Status}");
                    Console.WriteLine($"Nose Paddle P3: {robotStatus.NosePaddleP3Status}");
                    Console.WriteLine($"Nose Paddle P4: {robotStatus.NosePaddleP4Status}");
                }
                else
                {
                    Console.WriteLine("Robot状态未初始化");
                }
                
                // 检查初始化状态
                bool isInitialized = SS200InterLockMain.Instance.SubsystemStatus.Robot.IsInitialized;
                Console.WriteLine($"Robot状态已初始化: {isInitialized}");
                
                // 获取状态字符串（用于调试）
                string statusString = SS200InterLockMain.Instance.SubsystemStatus.Robot.StatusString;
                Console.WriteLine($"Robot状态字符串: {statusString}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试Robot状态访问时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试Chamber状态直接访问
        /// </summary>
        public void TestChamberStatusDirectAccess()
        {
            try
            {
                // 测试ChamberA状态
                var chamberAStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberA.Status;
                if (chamberAStatus != null)
                {
                    Console.WriteLine("=== ChamberA状态信息 ===");
                    Console.WriteLine($"Slit Door状态: {chamberAStatus.SlitDoorStatus}");
                    Console.WriteLine($"Lift Pin状态: {chamberAStatus.LiftPinStatus}");
                }
                
                // 测试ChamberB状态
                var chamberBStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberB.Status;
                if (chamberBStatus != null)
                {
                    Console.WriteLine("=== ChamberB状态信息 ===");
                    Console.WriteLine($"Slit Door状态: {chamberBStatus.SlitDoorStatus}");
                    Console.WriteLine($"Lift Pin状态: {chamberBStatus.LiftPinStatus}");
                }
                
                // 检查初始化状态
                bool chamberAInitialized = SS200InterLockMain.Instance.SubsystemStatus.ChamberA.IsInitialized;
                bool chamberBInitialized = SS200InterLockMain.Instance.SubsystemStatus.ChamberB.IsInitialized;
                Console.WriteLine($"ChamberA已初始化: {chamberAInitialized}");
                Console.WriteLine($"ChamberB已初始化: {chamberBInitialized}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试Chamber状态访问时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试Shuttle状态直接访问
        /// </summary>
        public void TestShuttleStatusDirectAccess()
        {
            try
            {
                // 直接获取Shuttle状态
                var shuttleStatus = SS200InterLockMain.Instance.SubsystemStatus.Shuttle.Status;
                if (shuttleStatus != null)
                {
                    Console.WriteLine("=== Shuttle状态信息 ===");
                    // 注意：具体属性取决于ShuttleSubsystemStatus类的实际定义
                    Console.WriteLine($"Shuttle状态: {shuttleStatus.ToString()}");
                }
                
                // 检查初始化状态
                bool isInitialized = SS200InterLockMain.Instance.SubsystemStatus.Shuttle.IsInitialized;
                Console.WriteLine($"Shuttle状态已初始化: {isInitialized}");
                
                // 获取状态字符串
                string statusString = SS200InterLockMain.Instance.SubsystemStatus.Shuttle.StatusString;
                Console.WriteLine($"Shuttle状态字符串: {statusString}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试Shuttle状态访问时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 性能对比测试：直接访问 vs 原有访问器方式
        /// </summary>
        public void TestPerformanceComparison()
        {
            const int iterations = 10000;
            
            try
            {
                // 测试直接访问性能
                var startTime = DateTime.Now;
                for (int i = 0; i < iterations; i++)
                {
                    var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
                    var mainStatus = robotStatus?.EnuRobotStatus;
                    var tAxisDestination = robotStatus?.EnuTAxisSmoothDestination;
                }
                var directAccessTime = DateTime.Now - startTime;
                
                Console.WriteLine($"直接访问方式 - {iterations}次访问耗时: {directAccessTime.TotalMilliseconds}ms");
                Console.WriteLine($"平均每次访问耗时: {directAccessTime.TotalMilliseconds / iterations:F4}ms");
                
                // 注意：原有的访问器方式已经被简化，这里只是展示概念
                Console.WriteLine("新的直接访问方式提供了更好的性能和更简洁的代码");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"性能测试时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public void RunAllTests()
        {
            Console.WriteLine("开始直接状态访问测试...\n");
            
            TestRobotStatusDirectAccess();
            Console.WriteLine();
            
            TestChamberStatusDirectAccess();
            Console.WriteLine();
            
            TestShuttleStatusDirectAccess();
            Console.WriteLine();
            
            TestPerformanceComparison();
            Console.WriteLine();
            
            Console.WriteLine("所有测试完成。");
        }
    }
}
