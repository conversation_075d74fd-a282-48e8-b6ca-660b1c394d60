using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.IOInterface.Chamber
{
    /// <summary>
    /// Process Chamber数字输出(DO)代码枚举
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuChamberDOCodes
    {
        /// <summary>
        /// T/V打开 (PDO1: PDO_1)
        /// enable:0
        /// </summary>
        [Description("T/V打开")]
        PDO1_TvOpen = 1,

        /// <summary>
        /// T/V关闭 (PDO2: PDO_2)
        /// enable:0
        /// </summary>
        [Description("T/V关闭")]
        PDO2_TvClose = 2,

        /// <summary>
        /// T/V FRZ (PDO3: PDO_3)
        /// enable:0
        /// </summary>
        [Description("T/V FRZ")]
        PDO3_TvFrz = 3,

        /// <summary>
        /// CV控制 (PDO4: PEVDO_1)
        /// EV - enable:0
        /// </summary>
        [Description("CV控制")]
        PDO4_CvControl = 4,

        /// <summary>
        /// 气体C1控制 (PDO5: PEVDO_2)
        /// EV - enable:0
        /// </summary>
        [Description("气体C1控制")]
        PDO5_GasC1Control = 5,

        /// <summary>
        /// 气体C2控制 (PDO6: PEVDO_3)
        /// EV - enable:0
        /// </summary>
        [Description("气体C2控制")]
        PDO6_GasC2Control = 6,

        /// <summary>
        /// 气体C3控制 (PDO7: PEVDO_4)
        /// EV - enable:0
        /// </summary>
        [Description("气体C3控制")]
        PDO7_GasC3Control = 7,

        /// <summary>
        /// 气体C4控制 (PDO8: PEVDO_5)
        /// EV - enable:0
        /// </summary>
        [Description("气体C4控制")]
        PDO8_GasC4Control = 8,

        /// <summary>
        /// 气体CM阀门控制 (PDO9: PEVDO_6)
        /// EV - enable:0
        /// </summary>
        [Description("气体CM阀门控制")]
        PDO9_GasCmValveControl = 9,

        /// <summary>
        /// Slit Door打开 (PDO10: PEVDO_7)
        /// EV - enable:0
        /// </summary>
        [Description("Slit Door打开")]
        PDO10_SlitDoorOpen = 10,

        /// <summary>
        /// Slit Door关闭 (PDO11: PEVDO_8)
        /// EV - enable:0
        /// </summary>
        [Description("Slit Door关闭")]
        PDO11_SlitDoorClose = 11,

        /// <summary>
        /// Lift Pin上升 (PDO12: PEVDO_9)
        /// EV - enable:0
        /// </summary>
        [Description("Lift Pin上升")]
        PDO12_LiftPinUp = 12,

        /// <summary>
        /// Lift Pin下降 (PDO13: PEVDO_10)
        /// EV - enable:0
        /// </summary>
        [Description("Lift Pin下降")]
        PDO13_LiftPinDown = 13,

        /// <summary>
        /// RF1使能 (PDO14: PDO_14)
        /// enable:0
        /// </summary>
        [Description("RF1使能")]
        PDO14_Rf1Enable = 14,

        /// <summary>
        /// RF2使能 (PDO15: PDO_15)
        /// enable:0
        /// </summary>
        [Description("RF2使能")]
        PDO15_Rf2Enable = 15,

        /// <summary>
        /// 加热器使能 (PDO16: PDO_4)
        /// enable:0
        /// </summary>
        [Description("加热器使能")]
        PDO16_HeaterEnable = 16
    }
}