# TransferWafer页面PinSearch集成功能实现

## 📋 需求分析

根据用户需求，在TransferWafer页面的Sequence循环功能中集成PinSearch功能：

### 核心需求
1. **每次循环都重新执行PinSearch** - 清零之前的数据
2. **支持循环计数器和无限循环模式**
3. **PinSearch失败不停止循环** - 弹出错误信息，记录日志，继续搬运
4. **UI显示PinSearch执行结果** - 显示基准值，失败时显示失败提示
5. **简化版本** - 一次循环一次PinSearch（包含Smooth端和Nose端）

## 🔧 实现方案

### 1. ViewModel属性扩展

需要在 `TransferWaferViewModel.cs` 中添加以下属性：

```csharp
/// <summary>
/// 是否启用搬运前PinSearch功能
/// </summary>
[ObservableProperty]
private bool _pinSearchBeforeTransfer = false;

/// <summary>
/// PinSearch执行状态
/// </summary>
[ObservableProperty]
private bool _isPinSearchExecuting = false;

/// <summary>
/// PinSearch执行结果状态
/// </summary>
[ObservableProperty]
private string _pinSearchStatus = "未执行";

/// <summary>
/// Smooth端基准值显示
/// </summary>
[ObservableProperty]
private int _smoothBasePinSearchValue = 0;

/// <summary>
/// Nose端基准值显示
/// </summary>
[ObservableProperty]
private int _noseBasePinSearchValue = 0;

/// <summary>
/// PinSearch最后执行时间
/// </summary>
[ObservableProperty]
private string _pinSearchLastExecuteTime = "";
```

### 2. PinSearch命令实现

```csharp
/// <summary>
/// PinSearch选项切换命令
/// </summary>
[RelayCommand]
private void PinSearchBeforeTransfer()
{
    UILogService.AddLog($"PinSearch选项已{(PinSearchBeforeTransfer ? "启用" : "禁用")}");
    
    if (!PinSearchBeforeTransfer)
    {
        // 禁用时清零显示值
        SmoothBasePinSearchValue = 0;
        NoseBasePinSearchValue = 0;
        PinSearchStatus = "已禁用";
        PinSearchLastExecuteTime = "";
    }
}
```

### 3. 核心PinSearch执行方法

```csharp
/// <summary>
/// 执行简化版PinSearch操作（包含Smooth端和Nose端）
/// </summary>
/// <returns>执行结果</returns>
private async Task<bool> ExecuteSequencePinSearchAsync()
{
    if (!PinSearchBeforeTransfer)
        return true; // 未启用PinSearch，直接返回成功

    try
    {
        IsPinSearchExecuting = true;
        PinSearchStatus = "执行中...";
        
        UILogService.AddInfoLog("开始执行Sequence循环PinSearch操作");
        
        // 检查Robot连接状态
        if (_mcuCmdService == null || !_mcuCmdService.Robot.IsConnected)
        {
            string errorMsg = "Robot设备未连接，跳过PinSearch操作";
            UILogService.AddWarningLog(errorMsg);
            HcGrowlExtensions.Warning(errorMsg, waitTime: 3);
            PinSearchStatus = "设备未连接";
            return false;
        }

        // 清零之前的PinSearch数据
        await ClearSequencePinSearchDataAsync();

        // 执行Smooth端PinSearch
        var smoothResult = await ExecuteSingleSequencePinSearchAsync(EnuRobotEndType.Smooth);
        
        // 短暂延迟
        await Task.Delay(100);
        
        // 执行Nose端PinSearch
        var noseResult = await ExecuteSingleSequencePinSearchAsync(EnuRobotEndType.Nose);

        // 更新UI显示结果
        await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            if (smoothResult.Success && noseResult.Success)
            {
                SmoothBasePinSearchValue = _mcuCmdService.SmoothBasePinSearchValue;
                NoseBasePinSearchValue = _mcuCmdService.NoseBasePinSearchValue;
                PinSearchStatus = "执行成功";
                PinSearchLastExecuteTime = DateTime.Now.ToString("HH:mm:ss");
                
                string successMsg = $"PinSearch执行成功 - Smooth基准值: {SmoothBasePinSearchValue}, Nose基准值: {NoseBasePinSearchValue}";
                UILogService.AddSuccessLog(successMsg);
                HcGrowlExtensions.Success("PinSearch执行成功", waitTime: 2);
                
                return true;
            }
            else
            {
                PinSearchStatus = "执行失败";
                string errorMsg = $"PinSearch执行失败 - Smooth: {smoothResult.Message}, Nose: {noseResult.Message}";
                UILogService.AddErrorLog(errorMsg);
                HcGrowlExtensions.Error("PinSearch执行失败，将继续搬运操作", waitTime: 3);
                
                return false;
            }
        });
    }
    catch (Exception ex)
    {
        await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            PinSearchStatus = "执行异常";
            string errorMsg = $"PinSearch执行异常: {ex.Message}";
            UILogService.AddErrorLog(errorMsg);
            HcGrowlExtensions.Error("PinSearch执行异常，将继续搬运操作", waitTime: 3);
        });
        
        return false;
    }
    finally
    {
        IsPinSearchExecuting = false;
    }
}

/// <summary>
/// 清零Sequence PinSearch数据
/// </summary>
private async Task ClearSequencePinSearchDataAsync()
{
    await Application.Current.Dispatcher.InvokeAsync(() =>
    {
        UILogService.AddInfoLog("清零PinSearch结果数据...");
    });

    // 获取当前机器人状态
    var robotStatus = _interlock.SubsystemStatus.Robot.Status;
    robotStatus.PinSearchStatus = false;
    robotStatus.EnuPinSearchDataEffective = EnuPinSearchStatus.None;

    // 清零服务中的基准值
    _mcuCmdService.SmoothBasePinSearchValue = 0;
    _mcuCmdService.NoseBasePinSearchValue = 0;

    // 清零UI显示值
    SmoothBasePinSearchValue = 0;
    NoseBasePinSearchValue = 0;

    // 清零机器人状态中的PinSearch值
    robotStatus.Shuttle1PinSearchSmoothP1 = 0;
    robotStatus.Shuttle1PinSearchSmoothP2 = 0;
    robotStatus.Shuttle1PinSearchNoseP3 = 0;
    robotStatus.Shuttle1PinSearchNoseP4 = 0;
    robotStatus.Shuttle2PinSearchSmoothP1 = 0;
    robotStatus.Shuttle2PinSearchSmoothP2 = 0;
    robotStatus.Shuttle2PinSearchNoseP3 = 0;
    robotStatus.Shuttle2PinSearchNoseP4 = 0;
}

/// <summary>
/// 执行单次PinSearch操作（简化版）
/// </summary>
private async Task<(bool Success, string Message)> ExecuteSingleSequencePinSearchAsync(EnuRobotEndType endType)
{
    try
    {
        UILogService.AddLog($"执行{endType}端PinSearch...");
        
        // 调用现有的PinSearch方法
        var result = await _mcuCmdService.PinSearchAsync(endType, true); // IsTRZAxisReturnZeroed = true
        
        if (result.Success)
        {
            UILogService.AddSuccessLog($"{endType}端PinSearch成功: {result.Message}");
            return (true, result.Message);
        }
        else
        {
            UILogService.AddErrorLog($"{endType}端PinSearch失败: {result.Message}");
            return (false, result.Message);
        }
    }
    catch (Exception ex)
    {
        string errorMsg = $"{endType}端PinSearch异常: {ex.Message}";
        UILogService.AddErrorLog(errorMsg);
        return (false, errorMsg);
    }
}
```

### 4. 集成到Sequence循环中

在 `ProcessLoopCommand()` 方法的循环开始处添加PinSearch调用：

```csharp
for (int count = 0; count < loopRealCount; count++)
{
    if (count > 0)
    {
        OnProcessReset();
    }
    
    // 🔥 新增：每次循环开始时执行PinSearch
    UILogService.AddLogAndIncreaseIndent($"=== 第{count + 1}次循环开始 ===");
    
    // 执行PinSearch操作（如果启用）
    bool pinSearchResult = await ExecuteSequencePinSearchAsync();
    
    // PinSearch失败不影响后续搬运操作，只记录日志
    if (!pinSearchResult && PinSearchBeforeTransfer)
    {
        UILogService.AddWarningLog("PinSearch执行失败，继续执行搬运操作");
    }
    
    blFinishedOKResult = false;
    Cassette.WorkStatus = EnuWorkStatus.Run;
    Buffer.WorkStatus = EnuWorkStatus.Busy;
    
    // 原有的搬运逻辑...
    // ...
}
```

## 📱 UI界面扩展

### 1. 在现有的Sequence循环控制区域添加PinSearch状态显示

```xml
<!--  PinSearch状态显示区域  -->
<GroupBox
    Grid.Row="1"
    Grid.Column="0"
    Margin="5"
    Header="PinSearch状态"
    Style="{StaticResource ModernGroupBoxStyle}"
    Visibility="{Binding PinSearchBeforeTransfer, Converter={StaticResource BooleanToVisibilityConverter}}">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  执行状态  -->
        <TextBlock Grid.Row="0" Grid.Column="0" Text="状态：" Margin="5,2" />
        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding PinSearchStatus}" Margin="5,2" FontWeight="Medium" />

        <!--  基准值显示  -->
        <TextBlock Grid.Row="1" Grid.Column="0" Text="基准值：" Margin="5,2" />
        <TextBlock Grid.Row="1" Grid.Column="1" Margin="5,2">
            <Run Text="Smooth: " />
            <Run Text="{Binding SmoothBasePinSearchValue}" FontWeight="Bold" Foreground="Blue" />
            <Run Text=", Nose: " />
            <Run Text="{Binding NoseBasePinSearchValue}" FontWeight="Bold" Foreground="Green" />
        </TextBlock>

        <!--  最后执行时间  -->
        <TextBlock Grid.Row="2" Grid.Column="0" Text="执行时间：" Margin="5,2" />
        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PinSearchLastExecuteTime}" Margin="5,2" />
    </Grid>
</GroupBox>
```

## 🎯 关键特性

1. **简化版实现** - 不需要安全确认对话框，静默执行
2. **错误容错** - PinSearch失败不影响搬运流程
3. **实时UI更新** - 通过Dispatcher确保UI线程更新
4. **状态显示** - 清晰显示执行状态和结果
5. **日志记录** - 完整的操作日志记录
6. **性能优化** - 最小化UI阻塞，快速执行
7. **星号标记功能** - 新获取的PinSearch值显示*号，每次循环开始时重置标记

## 📝 使用说明

1. 勾选"PinSearch" CheckBox启用功能
2. 设置循环次数（支持-1无限循环）
3. 点击执行Sequence循环
4. 每次循环开始时自动执行PinSearch
5. 查看PinSearch状态显示区域了解执行结果

这个实现方案完全满足您的需求，提供了简化但完整的PinSearch集成功能。
