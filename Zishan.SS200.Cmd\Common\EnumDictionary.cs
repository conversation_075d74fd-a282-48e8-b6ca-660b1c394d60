using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using log4net;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 提供使用枚举作为键的字典实现，具有类型安全访问、默认值和转换功能
    /// </summary>
    /// <typeparam name="TEnum">枚举类型</typeparam>
    public class EnumDictionary<TEnum> where TEnum : Enum
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(EnumDictionary<TEnum>));
        private readonly Dictionary<TEnum, object> _values = new Dictionary<TEnum, object>();
        private readonly Dictionary<TEnum, object> _defaultValues = new Dictionary<TEnum, object>();

        /// <summary>
        /// 获取已存储的值数量
        /// </summary>
        public int Count => _values.Count;

        /// <summary>
        /// 获取所有已存储的键
        /// </summary>
        public IEnumerable<TEnum> Keys => _values.Keys;

        /// <summary>
        /// 设置值
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">枚举键</param>
        /// <param name="value">要存储的值</param>
        public void Set<T>(TEnum key, T value)
        {
            _values[key] = value;
        }

        /// <summary>
        /// 设置默认值
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">枚举键</param>
        /// <param name="defaultValue">默认值</param>
        public void SetDefault<T>(TEnum key, T defaultValue)
        {
            _defaultValues[key] = defaultValue;
        }

        /// <summary>
        /// 批量设置默认值
        /// </summary>
        /// <param name="defaultValues">默认值字典</param>
        public void SetDefaults(Dictionary<TEnum, object> defaultValues)
        {
            foreach (var kvp in defaultValues)
            {
                _defaultValues[kvp.Key] = kvp.Value;
            }
        }

        /// <summary>
        /// 获取类型安全的值
        /// </summary>
        /// <typeparam name="T">期望的返回类型</typeparam>
        /// <param name="key">枚举键</param>
        /// <returns>转换为请求类型的值</returns>
        public T Get<T>(TEnum key)
        {
            // 如果存在值，尝试获取并转换
            if (_values.TryGetValue(key, out object value))
            {
                return ConvertToType<T>(value, key);
            }

            // 如果存在默认值，尝试获取并转换
            if (_defaultValues.TryGetValue(key, out object defaultValue))
            {
                return ConvertToType<T>(defaultValue, key);
            }

            // 没有找到值或默认值，抛出异常
            string keyName = key.ToString();
            string description = GetEnumDescription(key);
            _logger.Warn($"未找到键 {keyName} ({description}) 的值或默认值");

            throw new KeyNotFoundException($"未找到键 {keyName} ({description}) 的值或默认值");
        }

        /// <summary>
        /// 尝试获取值，如果不存在则返回默认值
        /// </summary>
        /// <typeparam name="T">期望的返回类型</typeparam>
        /// <param name="key">枚举键</param>
        /// <param name="defaultValue">如果键不存在时返回的默认值</param>
        /// <returns>值或默认值</returns>
        public T GetOrDefault<T>(TEnum key, T defaultValue)
        {
            try
            {
                return Get<T>(key);
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">要检查的键</param>
        /// <returns>如果键存在于值或默认值字典中则返回true</returns>
        public bool ContainsKey(TEnum key)
        {
            return _values.ContainsKey(key) || _defaultValues.ContainsKey(key);
        }

        /// <summary>
        /// 移除键
        /// </summary>
        /// <param name="key">要移除的键</param>
        /// <returns>如果成功移除则返回true</returns>
        public bool Remove(TEnum key)
        {
            return _values.Remove(key);
        }

        /// <summary>
        /// 清除所有值（不清除默认值）
        /// </summary>
        public void Clear()
        {
            _values.Clear();
        }

        /// <summary>
        /// 将值转换为请求的类型
        /// </summary>
        private T ConvertToType<T>(object value, TEnum key)
        {
            try
            {
                if (value is T typedValue)
                {
                    return typedValue;
                }

                // 特殊处理布尔值转换
                if (typeof(T) == typeof(bool) && (value is int || value is string))
                {
                    if (value is int intValue)
                    {
                        return (T)(object)(intValue != 0);
                    }

                    if (value is string strValue)
                    {
                        if (bool.TryParse(strValue, out bool result))
                        {
                            return (T)(object)result;
                        }

                        return (T)(object)(strValue.ToLower() == "true" || strValue == "1");
                    }
                }

                // 尝试使用Convert进行转换
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                string keyName = key.ToString();
                string description = GetEnumDescription(key);
                _logger.Error($"无法将键 {keyName} ({description}) 的值 {value} 转换为类型 {typeof(T).Name}: {ex.Message}");

                throw new InvalidCastException(
                    $"无法将键 {keyName} ({description}) 的值 {value} ({value.GetType().Name}) 转换为类型 {typeof(T).Name}", ex);
            }
        }

        /// <summary>
        /// 获取枚举值的描述
        /// </summary>
        private string GetEnumDescription(TEnum value)
        {
            var fi = value.GetType().GetField(value.ToString());
            var attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }
    }
}