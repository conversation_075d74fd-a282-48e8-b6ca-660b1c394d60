using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Services;
using log4net;

namespace Zishan.SS200.Cmd.Models.SS200.Factories
{
    /// <summary>
    /// 访问器工厂接口
    /// </summary>
    public interface IAccessorFactory
    {
        /// <summary>
        /// 创建IO属性访问器
        /// </summary>
        /// <typeparam name="TEnum">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValue">枚举值</param>
        /// <returns>IO属性访问器</returns>
        IOPropertyAccessor<TEnum> CreateIOAccessor<TEnum>(EnuMcuDeviceType deviceType, TEnum enumValue) where TEnum : Enum;

        /// <summary>
        /// 创建设备IO访问器
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>设备IO访问器</returns>
        object CreateDeviceIOAccessor(EnuMcuDeviceType deviceType);

        /// <summary>
        /// 获取支持的设备类型
        /// </summary>
        /// <returns>支持的设备类型列表</returns>
        IEnumerable<EnuMcuDeviceType> GetSupportedDeviceTypes();
    }

    /// <summary>
    /// 访问器工厂实现
    /// 使用工厂模式创建各种访问器实例
    /// </summary>
    public class AccessorFactory : IAccessorFactory
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(AccessorFactory));
        private readonly CoilStatusHelper _coilHelper;
        private readonly ConcurrentDictionary<string, object> _accessorCache = new();

        public AccessorFactory(CoilStatusHelper coilHelper)
        {
            _coilHelper = coilHelper ?? throw new ArgumentNullException(nameof(coilHelper));
        }

        /// <summary>
        /// 创建IO属性访问器
        /// </summary>
        public IOPropertyAccessor<TEnum> CreateIOAccessor<TEnum>(EnuMcuDeviceType deviceType, TEnum enumValue) where TEnum : Enum
        {
            string cacheKey = $"IO_{deviceType}_{enumValue}";

            return (IOPropertyAccessor<TEnum>)_accessorCache.GetOrAdd(cacheKey, _ =>
            {
                _logger.Debug($"创建IO访问器: {deviceType}.{enumValue}");
                return new IOPropertyAccessor<TEnum>(_coilHelper, deviceType, enumValue);
            });
        }

        /// <summary>
        /// 创建设备IO访问器
        /// </summary>
        public object CreateDeviceIOAccessor(EnuMcuDeviceType deviceType)
        {
            string cacheKey = $"Device_{deviceType}";

            return _accessorCache.GetOrAdd(cacheKey, _ =>
            {
                _logger.Debug($"创建设备IO访问器: {deviceType}");
                
                return deviceType switch
                {
                    EnuMcuDeviceType.Robot => new RobotIOAccessor(_coilHelper),
                    EnuMcuDeviceType.ChamberA => new ChamberIOAccessor(_coilHelper, EnuMcuDeviceType.ChamberA),
                    EnuMcuDeviceType.ChamberB => new ChamberIOAccessor(_coilHelper, EnuMcuDeviceType.ChamberB),
                    EnuMcuDeviceType.Shuttle => new ShuttleIOAccessor(_coilHelper),
                    _ => throw new NotSupportedException($"不支持的设备类型: {deviceType}")
                };
            });
        }

        /// <summary>
        /// 获取支持的设备类型
        /// </summary>
        public IEnumerable<EnuMcuDeviceType> GetSupportedDeviceTypes()
        {
            return new[]
            {
                EnuMcuDeviceType.Robot,
                EnuMcuDeviceType.ChamberA,
                EnuMcuDeviceType.ChamberB,
                EnuMcuDeviceType.Shuttle
            };
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        public void ClearCache()
        {
            _accessorCache.Clear();
            _logger.Info("访问器缓存已清除");
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        public CacheStatistics GetCacheStatistics()
        {
            return new CacheStatistics
            {
                TotalCachedItems = _accessorCache.Count,
                CacheKeys = new List<string>(_accessorCache.Keys)
            };
        }
    }

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// 缓存项总数
        /// </summary>
        public int TotalCachedItems { get; set; }

        /// <summary>
        /// 缓存键列表
        /// </summary>
        public List<string> CacheKeys { get; set; } = new();

        public override string ToString()
        {
            return $"缓存项数量: {TotalCachedItems}, 缓存键: [{string.Join(", ", CacheKeys)}]";
        }
    }

    /// <summary>
    /// 访问器构建器
    /// 提供流畅的API来构建访问器
    /// </summary>
    public class AccessorBuilder
    {
        private readonly IAccessorFactory _factory;
        private EnuMcuDeviceType _deviceType;
        private string _description;

        public AccessorBuilder(IAccessorFactory factory)
        {
            _factory = factory ?? throw new ArgumentNullException(nameof(factory));
        }

        /// <summary>
        /// 设置设备类型
        /// </summary>
        public AccessorBuilder ForDevice(EnuMcuDeviceType deviceType)
        {
            _deviceType = deviceType;
            return this;
        }

        /// <summary>
        /// 设置描述
        /// </summary>
        public AccessorBuilder WithDescription(string description)
        {
            _description = description;
            return this;
        }

        /// <summary>
        /// 构建IO访问器
        /// </summary>
        public IOPropertyAccessor<TEnum> BuildIOAccessor<TEnum>(TEnum enumValue) where TEnum : Enum
        {
            if (_deviceType == default)
                throw new InvalidOperationException("必须先设置设备类型");

            return _factory.CreateIOAccessor(_deviceType, enumValue);
        }

        /// <summary>
        /// 构建设备访问器
        /// </summary>
        public object BuildDeviceAccessor()
        {
            if (_deviceType == default)
                throw new InvalidOperationException("必须先设置设备类型");

            return _factory.CreateDeviceIOAccessor(_deviceType);
        }
    }

    /// <summary>
    /// 访问器工厂管理器
    /// 管理多个工厂实例
    /// </summary>
    public class AccessorFactoryManager
    {
        private static readonly Lazy<AccessorFactoryManager> _instance = 
            new Lazy<AccessorFactoryManager>(() => new AccessorFactoryManager());

        public static AccessorFactoryManager Instance => _instance.Value;

        private readonly Dictionary<string, IAccessorFactory> _factories = new();
        private readonly object _lock = new object();

        private AccessorFactoryManager() { }

        /// <summary>
        /// 注册工厂
        /// </summary>
        public void RegisterFactory(string name, IAccessorFactory factory)
        {
            lock (_lock)
            {
                _factories[name] = factory ?? throw new ArgumentNullException(nameof(factory));
            }
        }

        /// <summary>
        /// 获取工厂
        /// </summary>
        public IAccessorFactory GetFactory(string name)
        {
            lock (_lock)
            {
                return _factories.TryGetValue(name, out var factory) ? factory : null;
            }
        }

        /// <summary>
        /// 获取默认工厂
        /// </summary>
        public IAccessorFactory GetDefaultFactory()
        {
            return GetFactory("default");
        }

        /// <summary>
        /// 创建构建器
        /// </summary>
        public AccessorBuilder CreateBuilder(string factoryName = "default")
        {
            var factory = GetFactory(factoryName);
            if (factory == null)
                throw new InvalidOperationException($"工厂 '{factoryName}' 未注册");

            return new AccessorBuilder(factory);
        }

        /// <summary>
        /// 获取所有注册的工厂名称
        /// </summary>
        public IEnumerable<string> GetRegisteredFactoryNames()
        {
            lock (_lock)
            {
                return new List<string>(_factories.Keys);
            }
        }
    }

    /// <summary>
    /// 工厂初始化器
    /// 负责初始化和配置工厂
    /// </summary>
    public static class FactoryInitializer
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(FactoryInitializer));

        /// <summary>
        /// 初始化默认工厂
        /// </summary>
        public static void InitializeDefaultFactory(CoilStatusHelper coilHelper)
        {
            try
            {
                var factory = new AccessorFactory(coilHelper);
                AccessorFactoryManager.Instance.RegisterFactory("default", factory);
                
                _logger.Info("默认访问器工厂初始化成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"初始化默认工厂失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 初始化所有工厂
        /// </summary>
        public static void InitializeAllFactories(CoilStatusHelper coilHelper)
        {
            InitializeDefaultFactory(coilHelper);
            
            // 可以在这里添加其他特殊用途的工厂
            // 例如：测试工厂、模拟工厂等
            
            _logger.Info("所有访问器工厂初始化完成");
        }
    }
}
