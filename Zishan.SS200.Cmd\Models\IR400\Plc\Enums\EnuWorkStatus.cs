﻿using System.ComponentModel;

using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Models.IR400.Plc.Enums
{
    /// <summary>
    /// 工作状态
    /// </summary>
    [TypeConverter(typeof(EnuWorkStatus))]
    public enum EnuWorkStatus
    {
        /// <summary>
        /// 空闲状态【公共】
        /// </summary>
        [Description("Idle")]
        Idle = 0,

        /// <summary>
        /// 报警状态【公共】
        /// </summary>
        [Description("Alarm")]
        Alarm = 1,

        /// <summary>
        /// 运行状态【整机】
        /// </summary>
        [Description("Run")]
        Run = 2,

        /// <summary>
        //  繁忙状态【腔体、Cooling、Loadlock、Buffer】
        /// </summary>
        [Description("Busy")]
        Busy = 3,

        /// <summary>
        //  处理状态【腔体、Cooling】
        /// </summary>
        [Description("Process")]
        Process = 4,

        /// <summary>
        //  扫片【Loadlock】
        /// </summary>
        [Description("Mapping")]
        Mapping = 4
    }
}