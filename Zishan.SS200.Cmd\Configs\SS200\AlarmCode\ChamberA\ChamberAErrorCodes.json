[{"Item": "1", "Code": "PAC1", "Content": "chamber system status abnormal, command reject", "ChineseDescription": "腔室系统状态异常，指令被拒绝"}, {"Item": "2", "Code": "PAC2", "Content": "CHX run status or robot run status is busy, command reject", "ChineseDescription": "CHX运行状态或机器人运行状态忙碌，指令被拒绝"}, {"Item": "3", "Code": "PAC3", "Content": "CHX run status is processing, command reject", "ChineseDescription": "CHX运行状态处理中，指令被拒绝"}, {"Item": "4", "Code": "PAC4", "Content": "delta pressure about CHX and loadlock out of setpoint", "ChineseDescription": "CHX与装载锁压差超出设定点"}, {"Item": "5", "Code": "PAC5", "Content": "slit door sensor position status abnormal", "ChineseDescription": "狭缝门传感器位置状态异常"}, {"Item": "6", "Code": "PAC6", "Content": "slit door sensor failure or system error", "ChineseDescription": "狭缝门传感器故障或系统错误"}, {"Item": "7", "Code": "PAC7", "Content": "chamber pressure not at vacuum status, can not open slit door", "ChineseDescription": "腔室压力未达真空状态，无法开启狭缝门"}, {"Item": "8", "Code": "PAC8", "Content": "slit door open time out", "ChineseDescription": "狭缝门开启超时"}, {"Item": "9", "Code": "PAC9", "Content": "slit door open too fast", "ChineseDescription": "狭缝门开启过快"}, {"Item": "10", "Code": "PAC10", "Content": "lift pin sensor position status abnormal", "ChineseDescription": "升降针传感器位置状态异常"}, {"Item": "11", "Code": "PAC11", "Content": "lift pin sensor show cylinder is between, lift pin motion failure", "ChineseDescription": "升降针传感器显示气缸在中间位置，升降针运动失败"}, {"Item": "12", "Code": "PAC12", "Content": "lift pin open time out", "ChineseDescription": "升降针开启超时"}, {"Item": "13", "Code": "PAC13", "Content": "lift pin open too fast", "ChineseDescription": "升降针开启过快"}, {"Item": "14", "Code": "PAC14", "Content": "lift pin sensor failure or system error", "ChineseDescription": "升降针传感器故障或系统错误"}, {"Item": "15", "Code": "PAC15", "Content": "robot R-axis not at right position, lift pin motion failure", "ChineseDescription": "机器人R轴位置不正确，升降针运动失败"}, {"Item": "16", "Code": "PAC16", "Content": "robot run status is not idle, lift pin motion failure", "ChineseDescription": "机器人运行状态非空闲，升降针运动失败"}, {"Item": "17", "Code": "PAC17", "Content": "robot R-axis not at right position, slit door motion failure", "ChineseDescription": "机器人R轴位置不正确，狭缝门运动失败"}, {"Item": "18", "Code": "PAC18", "Content": "robot run status is not idle, slit door motion failure", "ChineseDescription": "机器人运行状态非空闲，狭缝门运动失败"}, {"Item": "19", "Code": "PAC19", "Content": "throttle valve position sensor failure or flag position error", "ChineseDescription": "节流阀位置传感器故障或标志位置错误"}, {"Item": "20", "Code": "PAC20", "Content": "throttle valve motion time out", "ChineseDescription": "节流阀运动超时"}, {"Item": "21", "Code": "PAC21", "Content": "foreline not at vacuum status, can not open ISO valve", "ChineseDescription": "前级未达真空状态，无法开启ISO阀"}, {"Item": "22", "Code": "PAC22", "Content": "chamber ISO valve sensor failure or position error", "ChineseDescription": "腔室ISO阀传感器故障或位置错误"}, {"Item": "23", "Code": "PAC23", "Content": "CV valve open time out", "ChineseDescription": "CV阀开启超时"}, {"Item": "24", "Code": "PAC24", "Content": "ISO valve not at open position, can not open CM valve", "ChineseDescription": "ISO阀未在开启位置，无法开启CM阀"}, {"Item": "25", "Code": "PAC25", "Content": "chamber no process vacuum, can not open CM valve", "ChineseDescription": "腔室无工艺真空，无法开启CM阀"}, {"Item": "26", "Code": "PAC26", "Content": "CM valve not at open position, can not open gas valve", "ChineseDescription": "CM阀未在开启位置，无法开启气体阀"}, {"Item": "27", "Code": "PAC27", "Content": "gas 1 servo time out", "ChineseDescription": "气体1伺服超时"}, {"Item": "28", "Code": "PAC28", "Content": "gas 2 servo time out", "ChineseDescription": "气体2伺服超时"}, {"Item": "29", "Code": "PAC29", "Content": "gas 3 servo time out", "ChineseDescription": "气体3伺服超时"}, {"Item": "30", "Code": "PAC30", "Content": "gas 4 servo time out", "ChineseDescription": "气体4伺服超时"}, {"Item": "31", "Code": "PAC31", "Content": "gas1 flow fault", "ChineseDescription": "气体1流量故障"}, {"Item": "32", "Code": "PAC32", "Content": "gas2 flow fault", "ChineseDescription": "气体2流量故障"}, {"Item": "33", "Code": "PAC33", "Content": "gas3 flow fault", "ChineseDescription": "气体3流量故障"}, {"Item": "34", "Code": "PAC34", "Content": "gas4 flow fault", "ChineseDescription": "气体4流量故障"}, {"Item": "35", "Code": "PAC35", "Content": "gas1 flow unstable", "ChineseDescription": "气体1流量不稳定"}, {"Item": "36", "Code": "PAC36", "Content": "gas2 flow unstable", "ChineseDescription": "气体2流量不稳定"}, {"Item": "37", "Code": "PAC37", "Content": "gas3 flow unstable", "ChineseDescription": "气体3流量不稳定"}, {"Item": "38", "Code": "PAC38", "Content": "gas4 flow unstable", "ChineseDescription": "气体4流量不稳定"}, {"Item": "39", "Code": "PAC39", "Content": "CV valve status is no opened, pressure control failure", "ChineseDescription": "CV阀状态未开启，压力控制失败"}, {"Item": "40", "Code": "PAC40", "Content": "CM valve not at open position, pressure control failure", "ChineseDescription": "CM阀未在开启位置，压力控制失败"}, {"Item": "41", "Code": "PAC41", "Content": "no open gas valve, can not control pressure", "ChineseDescription": "无开启的气体阀，无法控制压力"}, {"Item": "42", "Code": "PAC42", "Content": "pressure servo time out", "ChineseDescription": "压力伺服超时"}, {"Item": "43", "Code": "PAC43", "Content": "pressure flow fault", "ChineseDescription": "压力流量故障"}, {"Item": "44", "Code": "PAC44", "Content": "pressure unstable", "ChineseDescription": "压力不稳定"}, {"Item": "45", "Code": "PAC45", "Content": "temperature servo time out", "ChineseDescription": "温度伺服超时"}, {"Item": "46", "Code": "PAC46", "Content": "temperature out of setpoint", "ChineseDescription": "温度超出设定点"}, {"Item": "47", "Code": "PAC47", "Content": "temperature unstable", "ChineseDescription": "温度不稳定"}, {"Item": "48", "Code": "PAC48", "Content": "slit door not close, can not turn on RF", "ChineseDescription": "狭缝门未关闭，无法开启RF"}, {"Item": "49", "Code": "PAC49", "Content": "chamber no vacuum, RF off", "ChineseDescription": "腔室无真空，RF关闭"}, {"Item": "50", "Code": "PAC50", "Content": "ISO valve not open, can not turn on RF", "ChineseDescription": "ISO阀未开启，无法开启RF"}, {"Item": "51", "Code": "PAC51", "Content": "CM valve not at open position, can not turn on RF", "ChineseDescription": "CM阀未在开启位置，无法开启RF"}, {"Item": "52", "Code": "PAC52", "Content": "no plasma was detected on CHX left head", "ChineseDescription": "CHX左头未检测到等离子体"}, {"Item": "53", "Code": "PAC53", "Content": "no plasma was detected on CHX right head", "ChineseDescription": "CHX右头未检测到等离子体"}, {"Item": "54", "Code": "PAC54", "Content": "RF1 power forward to setpoint time out", "ChineseDescription": "RF1前向功率达到设定点超时"}, {"Item": "55", "Code": "PAC55", "Content": "RF1 reflector power out of limit", "ChineseDescription": "RF1反射功率超出限制"}, {"Item": "56", "Code": "PAC56", "Content": "RF2 power forward to setpoint time out", "ChineseDescription": "RF2前向功率达到设定点超时"}, {"Item": "57", "Code": "PAC57", "Content": "RF2 reflector power out of limit", "ChineseDescription": "RF2反射功率超出限制"}, {"Item": "58", "Code": "PAC58", "Content": "RF1 forward power out of setpoint", "ChineseDescription": "RF1前向功率超出设定点"}, {"Item": "59", "Code": "PAC59", "Content": "RF2 forward power out of setpoint", "ChineseDescription": "RF2前向功率超出设定点"}, {"Item": "60", "Code": "PAC60", "Content": "RF1 forward power output unstable", "ChineseDescription": "RF1前向功率输出不稳定"}, {"Item": "61", "Code": "PAC61", "Content": "RF2 forward power output unstable", "ChineseDescription": "RF2前向功率输出不稳定"}, {"Item": "62", "Code": "PAC62", "Content": "RF off failure", "ChineseDescription": "RF关闭失败"}, {"Item": "63", "Code": "PAC63", "Content": "RF1 reflector power output unstable", "ChineseDescription": "RF1反射功率输出不稳定"}, {"Item": "64", "Code": "PAC64", "Content": "RF2 reflector power output unstable", "ChineseDescription": "RF2反射功率输出不稳定"}, {"Item": "65", "Code": "PAC65", "Content": "Chamber pump down time out", "ChineseDescription": "腔室抽真空超时"}]