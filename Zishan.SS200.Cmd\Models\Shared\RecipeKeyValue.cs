﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Models.Shared
{
    /// <summary>
    /// 配方键值对数据
    /// </summary>
    public class RecipeKeyValue : BindableBase
    {
        /// <summary>
        /// 键
        /// </summary>
        public int KeyId { get => _KeyId; set => SetProperty(ref _KeyId, value); }
        private int _KeyId;

        /// <summary>
        /// 值
        /// </summary>
        public string KeyValue { get => _KeyValue; set => SetProperty(ref _KeyValue, value); }
        private string _KeyValue;

        public RecipeKeyValue(int keyName, string keyValue)
        {
            KeyId = keyName;
            KeyValue = keyValue;
        }

        public override string ToString()
        {
            return $"KeyId={KeyId},KeyValue={KeyValue}";
        }
    }
}