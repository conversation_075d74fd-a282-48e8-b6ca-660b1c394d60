using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Zishan.SS200.Cmd.Interface;
using Zishan.SS200.Cmd.Models.IR400;
using Zishan.SS200.Cmd.ViewModels;

namespace Zishan.SS200.Cmd.UserControls
{
    /// <summary>
    /// Arm.xaml 的交互逻辑
    /// </summary>
    public partial class Arm : UserControl, IWaferDragDropInfo
    {
        #region 依赖属性

        //[Category("Property")]
        //[Description("注释说明")]
        //public bool NorthLeftShowWafer
        //{
        //    get { return (bool)GetValue(NorthLeftShowWaferProperty); }
        //    set { SetValue(NorthLeftShowWaferProperty, value); }
        //}

        //public static readonly DependencyProperty NorthLeftShowWaferProperty =
        //            DependencyProperty.Register("NorthLeftShowWafer", typeof(bool), typeof(Arm),
        //                new FrameworkPropertyMetadata(default(bool), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        //[Category("Property")]
        //[Description("注释说明")]
        //public bool NorthRightShowWafer
        //{
        //    get { return (bool)GetValue(NorthRightShowWaferProperty); }
        //    set { SetValue(NorthRightShowWaferProperty, value); }
        //}

        //public static readonly DependencyProperty NorthRightShowWaferProperty =
        //            DependencyProperty.Register("NorthRightShowWafer", typeof(bool), typeof(Arm),
        //                new FrameworkPropertyMetadata(default(bool), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        //[Category("Property")]
        //[Description("注释说明")]
        //public bool SmoothLeftShowWafer
        //{
        //    get { return (bool)GetValue(SmoothLeftShowWaferProperty); }
        //    set { SetValue(SmoothLeftShowWaferProperty, value); }
        //}

        //public static readonly DependencyProperty SmoothLeftShowWaferProperty =
        //            DependencyProperty.Register("SmoothLeftShowWafer", typeof(bool), typeof(Arm),
        //                new FrameworkPropertyMetadata(default(bool), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        //[Category("Property")]
        //[Description("注释说明")]
        //public bool SmoothRightShowWafer
        //{
        //    get { return (bool)GetValue(SmoothRightShowWaferProperty); }
        //    set { SetValue(SmoothRightShowWaferProperty, value); }
        //}

        //public static readonly DependencyProperty SmoothRightShowWaferProperty =
        //            DependencyProperty.Register("SmoothRightShowWafer", typeof(bool), typeof(Arm),
        //                new FrameworkPropertyMetadata(default(bool), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        /// <summary>
        /// 固定位置：SmoothRobot Left Slot
        /// </summary>
        [Description("固定位置：SlotNoseRobot Left Slot")]
        [Category("Robot_IR400")]
        public int SlotNoseRobotL
        {
            get { return (int)GetValue(SlotNoseRobotLProperty); }
            set { SetValue(SlotNoseRobotLProperty, value); }
        }

        public static readonly DependencyProperty SlotNoseRobotLProperty =
            DependencyProperty.Register(nameof(SlotNoseRobotL), typeof(int), typeof(Arm), new FrameworkPropertyMetadata(0, FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender) { BindsTwoWayByDefault = true });

        /// <summary>
        /// 固定位置：SlotNoseRobot Right Slot
        /// </summary>
        [Description("固定位置：SmoothRobot Right Slot")]
        [Category("Robot_IR400")]
        public int SlotNoseRobotR
        {
            get { return (int)GetValue(SlotNoseRobotRProperty); }
            set { SetValue(SlotNoseRobotRProperty, value); }
        }

        public static readonly DependencyProperty SlotNoseRobotRProperty =
            DependencyProperty.Register(nameof(SlotNoseRobotR), typeof(int), typeof(Arm), new FrameworkPropertyMetadata(0, FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender) { BindsTwoWayByDefault = true });

        /// <summary>
        /// 固定位置：SmoothRobot Left Slot
        /// </summary>
        [Description("固定位置：SmoothRobot Left Slot")]
        [Category("Robot_IR400")]
        public int SlotSmoothRobotL
        {
            get { return (int)GetValue(SlotSmoothRobotLProperty); }
            set { SetValue(SlotSmoothRobotLProperty, value); }
        }

        public static readonly DependencyProperty SlotSmoothRobotLProperty =
            DependencyProperty.Register(nameof(SlotSmoothRobotL), typeof(int), typeof(Arm), new FrameworkPropertyMetadata(0, FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender) { BindsTwoWayByDefault = true });

        /// <summary>
        /// 固定位置：SmoothRobot Right Slot
        /// </summary>
        [Description("固定位置：SmoothRobot Right Slot")]
        [Category("Robot_IR400")]
        public int SlotSmoothRobotR
        {
            get { return (int)GetValue(SlotSmoothRobotRProperty); }
            set { SetValue(SlotSmoothRobotRProperty, value); }
        }

        public static readonly DependencyProperty SlotSmoothRobotRProperty =
            DependencyProperty.Register(nameof(SlotSmoothRobotR), typeof(int), typeof(Arm), new FrameworkPropertyMetadata(0, FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender) { BindsTwoWayByDefault = true });

        //[Category("Property")]
        //[Description("当前腔体")]
        //public BContainer CurCharber
        //{
        //    get { return (BContainer)GetValue(CurCharberProperty); }
        //    set { SetValue(CurCharberProperty, value); }
        //}

        //public static readonly DependencyProperty CurCharberProperty =
        //            DependencyProperty.Register("CurCharber", typeof(BContainer), typeof(Arm),
        //                new FrameworkPropertyMetadata(default(BContainer), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("NoseRobotIRArm")]
        public BContainer NoseRobotIRArm
        {
            get { return (BContainer)GetValue(NoseRobotIRArmProperty); }
            set { SetValue(NoseRobotIRArmProperty, value); }
        }

        public static readonly DependencyProperty NoseRobotIRArmProperty =
                    DependencyProperty.Register("NoseRobotIRArm", typeof(BContainer), typeof(Arm),
                        new FrameworkPropertyMetadata(default(BContainer), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("SmoothRobotIRArm")]
        public BContainer SmoothRobotIRArm
        {
            get { return (BContainer)GetValue(SmoothRobotIRArmProperty); }
            set { SetValue(SmoothRobotIRArmProperty, value); }
        }

        public static readonly DependencyProperty SmoothRobotIRArmProperty =
                    DependencyProperty.Register("SmoothRobotIRArm", typeof(BContainer), typeof(Arm),
                        new FrameworkPropertyMetadata(default(BContainer), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("Robot 旋转角度")]
        public double RotateAngle
        {
            get { return (double)GetValue(RotateAngleProperty); }
            set { SetValue(RotateAngleProperty, value); }
        }

        public static readonly DependencyProperty RotateAngleProperty =
                    DependencyProperty.Register("RotateAngle", typeof(double), typeof(Arm),
                        new FrameworkPropertyMetadata(default(double), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("直角三角形计算出来的X轴距离")]
        public double TranslateX
        {
            get { return (double)GetValue(TranslateXProperty); }
            set { SetValue(TranslateXProperty, value); }
        }

        public static readonly DependencyProperty TranslateXProperty =
                    DependencyProperty.Register("TranslateX", typeof(double), typeof(Arm),
                        new FrameworkPropertyMetadata(default(double), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("R直角三角形计算出来的Y轴距离")]
        public double TranslateY
        {
            get { return (double)GetValue(TranslateYProperty); }
            set { SetValue(TranslateYProperty, value); }
        }

        public static readonly DependencyProperty TranslateYProperty =
                    DependencyProperty.Register("TranslateY", typeof(double), typeof(Arm),
                        new FrameworkPropertyMetadata(default(double), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("Wafer编号")]
        public int WaferNo
        {
            get { return (int)GetValue(WaferNoProperty); }
            set { SetValue(WaferNoProperty, value); }
        }

        public static readonly DependencyProperty WaferNoProperty =
                    DependencyProperty.Register("WaferNo", typeof(int), typeof(Arm),
                        new FrameworkPropertyMetadata(default(int), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("当前腔体")]
        public BContainer CurCharber
        {
            get { return (BContainer)GetValue(CurCharberProperty); }
            set { SetValue(CurCharberProperty, value); }
        }

        public static readonly DependencyProperty CurCharberProperty =
                    DependencyProperty.Register("CurCharber", typeof(BContainer), typeof(Arm),
                        new FrameworkPropertyMetadata(default(BContainer), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("最大Wafer数量")]
        public int MaxWafers
        {
            get { return (int)GetValue(MaxWafersProperty); }
            set { SetValue(MaxWafersProperty, value); }
        }

        public static readonly DependencyProperty MaxWafersProperty =
                    DependencyProperty.Register("MaxWafers", typeof(int), typeof(Arm),
                        new FrameworkPropertyMetadata(25, FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        #endregion 依赖属性

        public Arm()
        {
            InitializeComponent();

            //设置初始状态
            //SetCurrentValue(SmoothLeftShowWaferProperty, false);
            //SetCurrentValue(SmoothRightShowWaferProperty, false);
            //SetCurrentValue(NorthLeftShowWaferProperty, false);
            //SetCurrentValue(NorthRightShowWaferProperty, false);

            Storyboard sbdArm = Resources["sbWafer"] as Storyboard;
            //sbdArm?.Begin();

            this.Loaded += UContainer_Loaded;

            //我这里需要绑定两个TextBlock,因此将这两个TextBlock进行绑定,绑定的是用于中转的变量,后续看的到.
            Binding bindCurCharber = new Binding() { Source = this };
            this.mainGrid.SetBinding(Grid.DataContextProperty, bindCurCharber);

            //this.DataContext = CurCharber;//不能绑定上去？
        }

        private void UContainer_Loaded(object sender, RoutedEventArgs e)
        {
            // 获取父窗口的数据上下文
            var parentDataContext = this.Parent as FrameworkElement;
            while (parentDataContext != null && !(parentDataContext.DataContext is TransferWaferViewModel))
            {
                parentDataContext = parentDataContext.Parent as FrameworkElement;
            }

            // 如果找到了IR400ViewModel，那么设置为当前用户控件的数据上下文
            if (parentDataContext != null)
            {
                this.DataContext = parentDataContext.DataContext;
            }
        }

        public void Dispose()
        {
        }
    }
}