﻿using System.Windows.Controls;

using log4net;
using Newtonsoft.Json;
using System;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using HandyControl.Controls;
using MaterialDesignThemes.Wpf;
using Zishan.SS200.Cmd.ViewModels;

namespace Zishan.SS200.Cmd.Views
{
    /// <summary>
    /// Interaction logic for RunRecipe
    /// </summary>
    public partial class RunRecipe : UserControl
    {
        public const string ViewName = nameof(RunRecipe);

        private System.Windows.Window parentWindow;

        public RunRecipe()
        {
            InitializeComponent();

            Loaded += (s, e) =>
            {
                parentWindow = System.Windows.Window.GetWindow(this);
            };

            ColorZone.MouseMove += (s, e) =>
            {
                if (e.LeftButton == MouseButtonState.Pressed && parentWindow != null)
                {
                    parentWindow.DragMove();
                }
            };
        }

        private void CheckComboBox_SelectionChanged_Left(object sender, SelectionChangedEventArgs e)
        {
            var viewModel = DataContext as RunRecipeViewModel;
            if (viewModel != null)
            {
                var checkComboBox = sender as CheckComboBox;
                if (checkComboBox != null)
                {
                    viewModel.UpdateLeftWaferSlotSelected(checkComboBox.SelectedItems);
                }
            }
        }

        private void CheckComboBox_SelectionChanged_Right(object sender, SelectionChangedEventArgs e)
        {
            var viewModel = DataContext as RunRecipeViewModel;
            if (viewModel != null)
            {
                var checkComboBox = sender as CheckComboBox;
                if (checkComboBox != null)
                {
                    viewModel.UpdateRightWaferSlotSelected(checkComboBox.SelectedItems);
                }
            }
        }

        private void ListBox_SelectionChanged_Left(object sender, SelectionChangedEventArgs e)
        {
            UpdateSelectedItems();
        }

        private void ListBox_SelectionChanged_Right(object sender, SelectionChangedEventArgs e)
        {
            UpdateSelectedItems();
        }

        private void UpdateSelectedItems()
        {
            var viewModel = DataContext as RunRecipeViewModel;
            if (viewModel != null)
            {
                var leftItems = LeftListBox.SelectedItems.Cast<int>().ToList();
                var rightItems = RightListBox.SelectedItems.Cast<int>().ToList();
                viewModel.UpdateSelectedItems(leftItems, rightItems);
            }
        }
    }
}