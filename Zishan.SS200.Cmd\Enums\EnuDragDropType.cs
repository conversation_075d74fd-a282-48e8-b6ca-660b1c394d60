﻿using System.ComponentModel;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 拖拽类型
    /// </summary>
    [TypeConverter(typeof(EnuDragDropType))]
    public enum EnuDragDropType
    {
        /// <summary>
        /// 未知
        /// </summary>
        Unknow = -1,

        /// <summary>
        /// Wafer->CH
        /// </summary>
        WaferToCH = 0,

        /// <summary>
        /// CH->Wafer
        /// </summary>
        CHToWafer = 1,

        /// <summary>
        /// CH->CH
        /// </summary>
        CHToCH = 2,

        /// <summary>
        /// CH->Cooling
        /// </summary>
        CHToCooling = 3,

        /// <summary>
        /// Cooling->CH
        /// </summary>
        CoolingToCH = 4,

        /// <summary>
        /// CH->Cassette
        /// </summary>
        ChToCassette = 5,

        /// <summary>
        /// Cassette->CH
        /// </summary>
        CassetteToCH = 6,
    }
}