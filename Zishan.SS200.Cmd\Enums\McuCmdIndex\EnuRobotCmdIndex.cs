namespace Zishan.SS200.Cmd.Enums.McuCmdIndex;

/// <summary>
/// Robot设备命令索引枚举，注意：枚举值要与CmdParameter命令参数一致
/// </summary>
public enum EnuRobotCmdIndex : ushort
{
    #region 已使用

    MoveMotor = 0,
    AlarmReset = 1,
    MotorStop = 2,
    Move_T_Axis = 3,
    Move_R_Axis = 4,
    Move_Z_Axis = 5,
    PinSearch = 6,

    #endregion 已使用

    #region 未使用

    AR1 = 16,
    AR2 = 17,
    AR3 = 18,
    AR4 = 9,
    AR5 = 14,
    AR6 = 15,
    AR8 = 7,
    AR9 = 8,
    AR11 = 10,
    AR12 = 11,
    AR13 = 12,
    AR14 = 13,

    #endregion 未使用
}