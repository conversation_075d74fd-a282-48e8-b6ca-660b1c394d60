using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 将整数值转换为字符串的转换器
    /// </summary>
    public class IntToStringConverter : IValueConverter
    {
        /// <summary>
        /// 格式化字符串
        /// </summary>
        public string Format { get; set; } = "{0}";

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value == null)
                    return string.Empty;

                // 如果value是整数类型
                if (value is int intValue)
                {
                    // 如果参数不为空，使用参数作为格式化字符串
                    if (parameter != null)
                    {
                        return string.Format(parameter.ToString(), intValue);
                    }
                    
                    // 否则使用默认的格式化字符串
                    return string.Format(Format, intValue);
                }

                // 如果不是整数类型，直接转为字符串
                return value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && int.TryParse(stringValue, out int result))
            {
                return result;
            }
            
            return DependencyProperty.UnsetValue;
        }
    }
} 