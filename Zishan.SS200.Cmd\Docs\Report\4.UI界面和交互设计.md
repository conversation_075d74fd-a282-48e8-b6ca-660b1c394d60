# Zishan.SS200.Cmd UI界面和交互设计

## UI架构概述

Zishan.SS200.Cmd应用程序的用户界面采用WPF框架实现，主要UI定义在`Views/MainWindow.xaml`文件中。应用程序采用了现代化的UI设计，使用了多种控件库提升用户体验：

1. **HandyControl**：提供丰富的现代化UI控件
2. **Microsoft XAML Behaviors**：支持高级UI交互
3. **Wu.Wpf.ControlLibrary**：自定义控件库

## UI组织结构

MainWindow采用了分层组织的结构：

```
MainWindow
└── TabControl (底部标签页)
    └── TabItem "SS-200：Shuttle"
        └── Grid (主布局)
            ├── Expander "RTZ轴位置显示"
            │   └── GroupBox
            │       └── ItemsControl (轴位置信息)
            ├── GridSplitter (分隔条)
            ├── GroupBox "手动模式命令"
            │   └── Grid
            │       ├── ComboBox "命令"
            │       ├── ComboBox "参数1"
            │       ├── ComboBox "参数2"
            │       └── ...
            └── ...
```

## 关键UI组件分析

### 1. 主窗口布局

主窗口使用了ResponsiveGrid布局，提供了灵活的自适应布局能力：

```xml
<Grid Background="White">
    <TabControl
        Margin="5"
        Style="{StaticResource TabControlCapsuleSolid}"
        TabStripPlacement="Bottom">
        <!-- 标签页内容 -->
    </TabControl>
</Grid>
```

### 2. TabControl 标签页控制

应用使用底部标签页结构组织不同功能区：

```xml
<TabItem
    Cursor="Hand"
    Header="SS-200：Shuttle"
    IsEnabled="True"
    IsSelected="{Binding IsTabItemtMainSelected}">
    <!-- 标签页内容 -->
</TabItem>
```

### 3. RTZ轴位置显示

通过Expander控件提供可折叠的信息面板：

```xml
<Expander
    ExpandDirection="Left"
    Header="RTZ轴位置显示"
    IsExpanded="True">
    <GroupBox Margin="5" Header="RTZ轴位置显示">
        <!-- 位置显示内容 -->
    </GroupBox>
</Expander>
```

位置信息通过ItemsControl展示，实现数据绑定：

```xml
<ItemsControl ItemsSource="{Binding UiRTZAxisLocationViewPLCAddressVarInfos}">
    <ItemsControl.ItemTemplate>
        <DataTemplate>
            <Grid>
                <TextBox
                    MinWidth="120"
                    hc:InfoElement.Title="{Binding Title, StringFormat={}{0}}"
                    hc:InfoElement.TitlePlacement="Left"
                    FontSize="16"
                    FontWeight="Medium"
                    IsEnabled="False"
                    IsReadOnly="True"
                    Text="{Binding Combinevalue, StringFormat={}{0:F3}}" />
                <TextBlock
                    Grid.Column="1"
                    Margin="5"
                    Text="mm" />
            </Grid>
        </DataTemplate>
    </ItemsControl.ItemTemplate>
    <ItemsControl.ItemsPanel>
        <ItemsPanelTemplate>
            <UniformGrid Columns="3" />
        </ItemsPanelTemplate>
    </ItemsControl.ItemsPanel>
</ItemsControl>
```

### 4. 手动模式命令区

命令区使用了ComboBox组合，提供命令和参数选择：

```xml
<GroupBox Margin="5" Header="手动模式命令">
    <Grid Margin="10">
        <hc:ComboBox
            Grid.Column="0"
            hc:InfoElement.Title="命令"
            hc:InfoElement.TitlePlacement="Left"
            DisplayMemberPath="CmdFormat"
            ItemsSource="{Binding IR400RobotCmdUsage}"
            SelectedItem="{Binding CmdUsageSelected}" />
        
        <hc:ComboBox
            Grid.Column="1"
            hc:InfoElement.Title="{Binding CmdPrameter1.Name}"
            DisplayMemberPath="CmdDesc"
            IsEnabled="{Binding CmdPrameter1.IsEnable}"
            ItemsSource="{Binding CmdPrameter1.CmdPrameters}"
            SelectedItem="{Binding CmdParmeter1Selected}" />
        
        <!-- 更多ComboBox... -->
    </Grid>
</GroupBox>
```

## 数据绑定

UI组件通过WPF数据绑定机制与ViewModel关联：

1. **主窗口绑定**：
```xml
Title="{Binding Title}"
d:DataContext="{x:Static dvm:MainViewDesignViewModel.Instance}"
prism:ViewModelLocator.AutoWireViewModel="True"
```

2. **数据展示绑定**：
```xml
Text="{Binding Combinevalue, StringFormat={}{0:F3}}"
```

3. **列表数据绑定**：
```xml
ItemsSource="{Binding UiRTZAxisLocationViewPLCAddressVarInfos}"
```

4. **命令绑定**：
```xml
Command="{Binding TextChangedCommand}"
CommandParameter="{Binding Path=Text, RelativeSource={RelativeSource AncestorType={x:Type hc:ComboBox}}}"
```

## 值转换器应用

为了适应不同数据类型和表示形式，应用使用了多个值转换器：

```xml
<ResourceDictionary>
    <conv:DescriptionConverter x:Key="DescriptionConverter" />
    <conv:AndMultiValueConverter x:Key="AndMultiValueConverter" />
    <conv:InvertBooleanConverter x:Key="InvertBooleanConverter" />
</ResourceDictionary>
```

其中，InvertBooleanConverter用于反转布尔值：

```csharp
public class InvertBooleanConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool stepStatus)
        {
            return !stepStatus;
        }
        return Binding.DoNothing;
    }
    
    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
```

## 交互行为

应用使用Microsoft.Xaml.Behaviors实现复杂交互：

```xml
<i:Interaction.Triggers>
    <i:EventTrigger EventName="KeyUp">
        <i:InvokeCommandAction Command="{Binding TextChangedCommand}" CommandParameter="{Binding Path=Text, RelativeSource={RelativeSource AncestorType={x:Type hc:ComboBox}}}" />
    </i:EventTrigger>
</i:Interaction.Triggers>
```

## UI扩展功能

UI使用了多种扩展功能提升用户体验：

1. **ToolTip增强**：提供详细的信息提示
```xml
ToolTip="{Binding CmdUsageSelected.CmdUsage}"
```

2. **输入增强**：通过附加属性改善输入体验
```xml
wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
```

3. **标题增强**：使用InfoElement提供更丰富的标题效果
```xml
hc:InfoElement.Title="命令"
hc:InfoElement.TitlePlacement="Left"
hc:TitleElement.HorizontalAlignment="Right"
hc:TitleElement.TitleWidth="40"
```

## 响应式设计

UI布局采用了网格(Grid)和相对定位，确保在不同屏幕尺寸下能够正常显示：

```xml
<Grid.ColumnDefinitions>
    <ColumnDefinition Width="*" />
    <ColumnDefinition Width="auto" />
    <ColumnDefinition Width="*" />
    <ColumnDefinition Width="auto" />
    <ColumnDefinition Width="*" />
</Grid.ColumnDefinitions>
```

## 可访问性考虑

UI设计考虑了可访问性和易用性：

1. **高对比度**：白色背景上使用深色文本
2. **合理间距**：控件之间有足够空间
3. **一致的导航**：使用标准TabControl导航
4. **提示文本**：通过ToolTip提供额外信息

## 总结

Zishan.SS200.Cmd应用程序的UI设计采用了现代WPF实践，结合了HandyControl等先进UI库，提供了:

1. **清晰的布局结构**：使用分层标签页和面板
2. **丰富的数据展示**：动态绑定和格式化展示
3. **方便的命令输入**：组合ComboBox和命令模式
4. **响应式设计**：适应不同屏幕尺寸
5. **增强的交互体验**：通过行为和触发器

UI和交互设计充分利用了WPF的MVVM架构，提供了直观且专业的工业控制界面。 