using System;
using System.Collections.Generic;
using System.IO;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot
{
    /// <summary>
    /// 机器人配置参数根配置
    /// </summary>
    public class RobotConfigureSettingsConfig
    {
        public List<ConfigureSetting> ConfigureSettings { get; set; }
    }

    /// <summary>
    /// 机器人配置参数提供者 - 从JSON配置文件加载参数
    /// </summary>
    public class RobotConfigureSettingsProvider : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RobotConfigureSettingsProvider));
        private readonly Dictionary<string, int> _settings = new Dictionary<string, int>();
        private readonly FileSystemWatcher _configWatcher;

        private static readonly Lazy<RobotConfigureSettingsProvider> _instance =
            new Lazy<RobotConfigureSettingsProvider>(() => new RobotConfigureSettingsProvider());

        // 配置文件路径
        private const string CONFIG_PATH = "Configs/SS200/SubsystemConfigure/Robot/RobotConfigureSettings.json";

        // 最后修改时间，用于监测配置文件变化
        private DateTime _lastModifiedTime = DateTime.MinValue;

        public static RobotConfigureSettingsProvider Instance => _instance.Value;

        // 私有构造函数
        private RobotConfigureSettingsProvider()
        {
            // 初始化文件系统监视器
            string configDir = Path.GetDirectoryName(GetConfigFilePath());
            string configFileName = Path.GetFileName(CONFIG_PATH);

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            _configWatcher = new FileSystemWatcher(configDir, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            // 注册文件变化事件处理
            _configWatcher.Changed += OnConfigFileChanged;
            _configWatcher.Created += OnConfigFileChanged;

            // 初始化默认值
            InitializeDefaultValues();

            // 尝试加载配置文件
            LoadFromJson();
        }

        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 由于文件系统事件可能会触发多次，这里添加简单的防抖动处理
                if ((DateTime.Now - _lastModifiedTime).TotalMilliseconds < 100)
                {
                    return;
                }

                _logger.Info($"检测到配置文件变化: {e.FullPath}, 变化类型: {e.ChangeType}");
                LoadFromJson();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理配置文件变化事件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaultValues()
        {
            // 速度参数
            _settings["RPS1"] = 1000; // 机器人旋转速度
            _settings["RPS2"] = 800;  // 机器人伸展速度
            _settings["RPS3"] = 500;  // 机器人上下速度
            _settings["RPS4"] = 0;    // 机器人慢速移动
            _settings["RPS5"] = 25;   // T轴慢速移动速度
            _settings["RPS6"] = 25;   // R轴慢速移动速度
            _settings["RPS7"] = 25;   // Z轴慢速移动速度

            // 超时参数
            _settings["RPS8"] = 30;   // 机器人旋转最大时间
            _settings["RPS9"] = 30;   // 机器人伸展最大时间
            _settings["RPS10"] = 30;  // 机器人上下最大时间
            _settings["RPS11"] = 100; // T轴回零偏差步数
            _settings["RPS12"] = 60;  // 机器人T轴回零最大时间
            _settings["RPS13"] = 60;  // 机器人R轴回零最大时间
            _settings["RPS14"] = 60;  // 机器人Z轴回零最大时间

            // 偏差参数
            _settings["RPS15"] = 100; // T轴偏差为R轴零位
            _settings["RPS16"] = 100; // Z轴步进偏差用于反馈真空
            _settings["RPS17"] = 100; // Z轴步进偏差用于穿梭无真空

            // 晶圆参数
            _settings["RPS18"] = 100; // 4/5寸晶圆Z轴位置
            _settings["RPS19"] = 100; // 6寸晶圆Z轴位置
            _settings["RPS20"] = 100; // 8寸晶圆Z轴位置
            _settings["RPS21"] = 100; // 4寸晶圆盒Z轴取放晶圆增量
            _settings["RPS22"] = 100; // 6寸晶圆盒Z轴取放晶圆增量
            _settings["RPS23"] = 100; // 8寸晶圆盒Z轴取放晶圆增量
            _settings["RPS24"] = 100; // 冷却腔Z轴取放晶圆增量
            _settings["RPS25"] = 100; // 插销搜索最大偏差值

            // 状态检查参数
            _settings["RPS26"] = 1;   // 晶圆实际状态检查
            _settings["RPS27"] = 1000; // 机器人旋转时Z轴高度
            _settings["RPS28"] = 500; // 插销搜索最低步进
            _settings["RPS29"] = 1;   // 腔室压力检查
        }

        /// <summary>
        /// 从JSON配置文件加载参数
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool LoadFromJson()
        {
            try
            {
                string jsonFilePath = GetConfigFilePath();
                if (!File.Exists(jsonFilePath))
                {
                    _logger.Warn($"机器人配置参数文件不存在: {jsonFilePath}，将使用默认值");
                    return false;
                }

                // 获取文件最后修改时间
                DateTime currentModified = File.GetLastWriteTime(jsonFilePath);

                // 如果文件未修改，则不重新加载
                if (currentModified == _lastModifiedTime)
                {
                    return true;
                }

                _lastModifiedTime = currentModified;

                string jsonContent;
                using (var fileStream = new FileStream(jsonFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    jsonContent = reader.ReadToEnd();
                }

                var config = JsonConvert.DeserializeObject<RobotConfigureSettingsConfig>(jsonContent);

                if (config?.ConfigureSettings == null || config.ConfigureSettings.Count == 0)
                {
                    _logger.Warn("未找到有效的配置参数，将使用默认值");
                    return false;
                }

                // 临时字典，验证成功后再替换
                var tempSettings = new Dictionary<string, int>();
                foreach (var setting in config.ConfigureSettings)
                {
                    if (string.IsNullOrEmpty(setting.Code))
                    {
                        _logger.Warn($"参数ID {setting.Id} 缺少代码标识，已跳过");
                        continue;
                    }

                    tempSettings[setting.Code] = setting.IntValue;
                    _logger.Debug($"加载参数 {setting.Code} = {setting.Value} ({setting.Description})");
                }

                // 验证所有必要参数都存在
                for (int i = 1; i <= 29; i++)
                {
                    string code = $"RPS{i}";
                    if (!tempSettings.ContainsKey(code))
                    {
                        _logger.Warn($"配置文件中缺少必要参数 {code}，将使用默认值");
                        tempSettings[code] = _settings[code]; // 使用默认值
                    }
                }

                // 更新参数字典
                foreach (var kvp in tempSettings)
                {
                    _settings[kvp.Key] = kvp.Value;
                }

                _logger.Info($"成功从 {jsonFilePath} 加载 {tempSettings.Count} 个机器人配置参数");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载机器人配置参数文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        private string GetConfigFilePath()
        {
            try
            {
                return App.ConfigHelper.GetConfigFilePath(CONFIG_PATH);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取配置文件路径失败: {ex.Message}", ex);

                // 回退策略 - 尝试直接拼接路径
                string fallbackPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    CONFIG_PATH);

                _logger.Info($"使用回退路径: {fallbackPath}");
                return fallbackPath;
            }
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        /// <param name="enuRobotConfigureSettingCodes">参数代码 (如 "RPS1")</param>
        /// <returns>参数值</returns>
        public int GetSettingValue(EnuRobotConfigureSettingCodes enuRobotConfigureSettingCodes)
        {
            if (_settings.TryGetValue(enuRobotConfigureSettingCodes.ToString(), out int value))
            {
                return value;
            }

            throw new KeyNotFoundException($"找不到机器人配置参数: {enuRobotConfigureSettingCodes}");
        }

        #region 辅助方法 - 获取特定配置参数

        /// <summary>
        /// 获取轴移动速度
        /// </summary>
        /// <param name="axisType">轴类型</param>
        /// <returns>对应轴的移动速度</returns>
        public int GetAxisSpeed(EnuRobotAxisType axisType)
        {
            switch (axisType)
            {
                case EnuRobotAxisType.TAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS1);

                case EnuRobotAxisType.RAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS2);

                case EnuRobotAxisType.ZAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS3);

                default:
                    throw new ArgumentException($"不支持的轴类型: {axisType}");
            }
        }

        /// <summary>
        /// 获取是否启用RTZ轴慢速模式
        /// </summary>
        /// <returns>是否启用RTZ轴慢速模式</returns>
        public bool GetIsRobotMoveSlowly()
        {
            bool isSlow = GetSettingValue(EnuRobotConfigureSettingCodes.RPS4) == 1 ? true : false;
            return isSlow;
        }

        /// <summary>
        /// 获取轴慢速移动速度百分比
        /// </summary>
        /// <param name="axisType">轴类型</param>
        /// <returns>对应轴的慢速移动速度百分比</returns>
        public int GetAxisSlowSpeed(EnuRobotAxisType axisType)
        {
            switch (axisType)
            {
                case EnuRobotAxisType.TAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS5);

                case EnuRobotAxisType.RAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS6);

                case EnuRobotAxisType.ZAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS7);

                default:
                    throw new ArgumentException($"不支持的轴类型: {axisType}");
            }
        }

        /// <summary>
        /// 获取轴移动最大时间
        /// </summary>
        /// <param name="axisType">轴类型</param>
        /// <returns>对应轴的移动最大时间（秒）</returns>
        public int GetAxisMaxTime(EnuRobotAxisType axisType)
        {
            switch (axisType)
            {
                case EnuRobotAxisType.TAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS8);

                case EnuRobotAxisType.RAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS9);

                case EnuRobotAxisType.ZAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS10);

                default:
                    throw new ArgumentException($"不支持的轴类型: {axisType}");
            }
        }

        /// <summary>
        /// 获取轴回零最大时间
        /// </summary>
        /// <param name="axisType">轴类型</param>
        /// <returns>对应轴的回零最大时间（秒）</returns>
        public int GetAxisHomeMaxTime(EnuRobotAxisType axisType)
        {
            switch (axisType)
            {
                case EnuRobotAxisType.TAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS12);

                case EnuRobotAxisType.RAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS13);

                case EnuRobotAxisType.ZAxis:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS14);

                default:
                    throw new ArgumentException($"不支持的轴类型: {axisType}");
            }
        }

        /// <summary>
        /// 获取T轴回零偏差步数
        /// </summary>
        /// <returns>T轴回零偏差步数</returns>
        public int GetTAxisHomeDeviation()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS11);
        }

        /// <summary>
        /// 获取R轴零位的T轴偏差
        /// </summary>
        /// <returns>R轴零位的T轴偏差</returns>
        public int GetTAxisDeviationForRZero()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS15);
        }

        /// <summary>
        /// 获取反馈真空的Z轴步进偏差
        /// </summary>
        /// <returns>反馈真空的Z轴步进偏差</returns>
        public int GetZAxisDeviationForVacuum()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS16);
        }

        /// <summary>
        /// 获取穿梭无真空的Z轴步进偏差
        /// </summary>
        /// <returns>穿梭无真空的Z轴步进偏差</returns>
        public int GetZAxisDeviationForNoVacuum()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS17);
        }

        /// <summary>
        /// 获取晶圆Z轴位置
        /// </summary>
        /// <param name="waferSize">晶圆尺寸</param>
        /// <returns>对应晶圆尺寸的Z轴位置</returns>
        public int GetWaferZPosition(EnuWaferSize waferSize)
        {
            switch (waferSize)
            {
                case EnuWaferSize.Inch4:
                    // 包含4/5寸晶圆
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS18);

                case EnuWaferSize.Inch6:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS19);

                case EnuWaferSize.Inch8:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS20);

                default:
                    throw new ArgumentException($"不支持的晶圆尺寸: {waferSize}");
            }
        }

        /// <summary>
        /// 获取晶圆盒Z轴放晶圆增量
        /// </summary>
        /// <param name="waferSize">晶圆尺寸</param>
        /// <returns>对应晶圆尺寸的Z轴取放晶圆增量</returns>
        public int GetCassetteZPutDelta(EnuWaferSize waferSize)
        {
            switch (waferSize)
            {
                case EnuWaferSize.Inch4:
                    // 包含4/5寸晶圆
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS21);

                case EnuWaferSize.Inch6:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS22);

                case EnuWaferSize.Inch8:
                    return GetSettingValue(EnuRobotConfigureSettingCodes.RPS23);

                default:
                    throw new ArgumentException($"不支持的晶圆尺寸: {waferSize}");
            }
        }

        /// <summary>
        /// 获取冷却腔Z轴取放晶圆增量
        /// </summary>
        /// <returns>冷却腔Z轴取放晶圆增量</returns>
        public int GetCoolingChamberZDelta()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS24);
        }

        /// <summary>
        /// 获取Pin Search最大偏差值【指的左右2边Setp最大偏差值不能超过制定值】
        /// </summary>
        /// <returns>Pin Search最大偏差值</returns>
        public int GetPinSearchMaxDelta()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS25);
        }

        /// <summary>
        /// 获取晶圆实际状态检查设置
        /// </summary>
        /// <returns>是否检查晶圆实际状态</returns>
        public bool GetWaferStatusCheck()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS26) > 0;
        }

        /// <summary>
        /// 获取机器人旋转时Z轴高度
        /// </summary>
        /// <returns>机器人旋转时Z轴高度</returns>
        public int GetZHeightForRotation()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS27);
        }

        /// <summary>
        /// 获取可以做完Pin Seach最低距离步数（机限位置）
        /// </summary>
        /// <returns>插销搜索最低步进</returns>
        public int GetPinSearchLowestStep()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS28);
        }

        /// <summary>
        /// 获取腔室压力检查设置
        /// </summary>
        /// <returns>是否检查腔室压力</returns>
        public bool GetChamberPressureReview()
        {
            return GetSettingValue(EnuRobotConfigureSettingCodes.RPS29) > 0;
        }

        #endregion 辅助方法 - 获取特定配置参数

        public void Dispose()
        {
            _configWatcher?.Dispose();
        }
    }
}