using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models;

namespace Zishan.SS200.Cmd.Services.Interfaces
{
    /// <summary>
    /// 设备连接管理服务接口
    /// 负责处理设备的连接、断开和状态管理
    /// </summary>
    public interface IDeviceConnectionService
    {
        /// <summary>
        /// 连接单个设备
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <param name="ipAddress">IP地址</param>
        /// <param name="port">端口号</param>
        /// <param name="slaveId">从站ID</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectDeviceAsync(string deviceName, string ipAddress, int port, byte slaveId);

        /// <summary>
        /// 连接所有设备
        /// </summary>
        /// <param name="deviceConfigs">设备配置字典，键为设备名称，值为IP地址、端口和从站ID的元组</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAllDevicesAsync(Dictionary<string, (string ipAddress, int port, byte slaveId)> deviceConfigs);

        /// <summary>
        /// 断开单个设备连接
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        Task DisconnectDeviceAsync(string deviceName);

        /// <summary>
        /// 断开所有设备连接
        /// </summary>
        Task DisconnectAllDevicesAsync();

        /// <summary>
        /// 检查设备是否已连接
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>设备是否已连接</returns>
        bool IsDeviceConnected(string deviceName);

        /// <summary>
        /// 获取所有设备的状态
        /// </summary>
        /// <returns>设备状态字典，键为设备名称，值为设备状态枚举</returns>
        Dictionary<string, DeviceStatus> GetAllDeviceStatus();

        /// <summary>
        /// 获取设备对象
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>设备对象</returns>
        McuDevice GetDevice(string deviceName);

        /// <summary>
        /// 获取所有设备对象
        /// </summary>
        /// <returns>设备对象字典，键为设备名称，值为设备对象</returns>
        Dictionary<string, McuDevice> GetAllDevices();
    }
} 