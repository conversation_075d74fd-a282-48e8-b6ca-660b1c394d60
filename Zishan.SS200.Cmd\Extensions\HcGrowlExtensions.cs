﻿using System;
using System.Runtime.CompilerServices;
using System.Windows;
using HandyControl.Controls;
using HandyControl.Data;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Extensions
{
    /// <summary>
    /// HandyControl Growl 扩展方法
    /// 提供简化的消息通知功能，支持多种消息类型和智能配置
    /// </summary>
    public static class HcGrowlExtensions
    {
        #region 常量定义

        /// <summary>
        /// 默认停留时间（秒）
        /// </summary>
        public const int DefaultWaitTime = 5;

        /// <summary>
        /// 开发模式下的停留时间（秒）
        /// </summary>
        public const int DebugWaitTime = 10;

        /// <summary>
        /// 生产模式下的快速停留时间（秒）
        /// </summary>
        public const int ProductionWaitTime = 2;

        /// <summary>
        /// 错误消息的默认停留时间（秒）- 错误消息需要更长时间让用户注意
        /// </summary>
        public const int ErrorWaitTime = 8;

        /// <summary>
        /// 向后兼容的等待时间常量
        /// </summary>
        [Obsolete("请使用 DefaultWaitTime 替代", false)]
        public const int WaitTime = DefaultWaitTime;

        #endregion 常量定义

        #region Token 常量

        /// <summary>
        /// 预定义的 Token 常量，用于消息分组管理
        /// </summary>
        public static class Tokens
        {
            public const string System = "system";
            public const string Device = "device";
            public const string Command = "command";
            public const string Chamber = "chamber";
            public const string Robot = "robot";
            public const string Shuttle = "shuttle";
            public const string Connection = "connection";
            public const string Validation = "validation";
            public const string Debug = "debug";
        }

        #endregion Token 常量

        #region 显示模式枚举

        /// <summary>
        /// Growl 显示模式
        /// </summary>
        public enum GrowlDisplayMode
        {
            /// <summary>
            /// 窗口模式 - 在当前窗口内显示
            /// </summary>
            Window,

            /// <summary>
            /// 桌面模式 - 在整个桌面显示（默认）
            /// </summary>
            Desktop
        }

        #endregion 显示模式枚举

        #region 基础消息方法

        /// <summary>
        /// 显示信息消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        /// <param name="displayMode">显示模式</param>
        public static void Info(string message, string token = "", int waitTime = -1, bool showDateTime = false,
            GrowlDisplayMode displayMode = GrowlDisplayMode.Window)
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            ExecuteOnUIThread(() =>
            {
                var growlInfo = new GrowlInfo
                {
                    Message = message,
                    WaitTime = GetSmartWaitTime(waitTime, DefaultWaitTime),
                    Token = token,
                    ShowDateTime = showDateTime
                };

                if (displayMode == GrowlDisplayMode.Desktop)
                    Growl.InfoGlobal(growlInfo);
                else
                    Growl.Info(growlInfo);
            });
        }

        /// <summary>
        /// 显示成功消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        /// <param name="displayMode">显示模式</param>
        public static void Success(string message, string token = "", int waitTime = -1, bool showDateTime = false,
            GrowlDisplayMode displayMode = GrowlDisplayMode.Window)
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            ExecuteOnUIThread(() =>
            {
                var growlInfo = new GrowlInfo
                {
                    Message = message,
                    WaitTime = GetSmartWaitTime(waitTime, DefaultWaitTime),
                    Token = token,
                    ShowDateTime = showDateTime
                };

                if (displayMode == GrowlDisplayMode.Desktop)
                    Growl.SuccessGlobal(growlInfo);
                else
                    Growl.Success(growlInfo);
            });
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        /// <param name="displayMode">显示模式</param>
        public static void Warning(string message, string token = "", int waitTime = -1, bool showDateTime = false,
            GrowlDisplayMode displayMode = GrowlDisplayMode.Window)
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            ExecuteOnUIThread(() =>
            {
                var growlInfo = new GrowlInfo
                {
                    Message = message,
                    WaitTime = GetSmartWaitTime(waitTime, DefaultWaitTime),
                    Token = token,
                    ShowDateTime = showDateTime
                };

                if (displayMode == GrowlDisplayMode.Desktop)
                    Growl.WarningGlobal(growlInfo);
                else
                    Growl.Warning(growlInfo);
            });
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        /// <param name="displayMode">显示模式</param>
        public static void Error(string message, string token = "", int waitTime = -1, bool showDateTime = false,
            GrowlDisplayMode displayMode = GrowlDisplayMode.Window)
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            ExecuteOnUIThread(() =>
            {
                var growlInfo = new GrowlInfo
                {
                    Message = message,
                    WaitTime = GetSmartWaitTime(waitTime, ErrorWaitTime),
                    Token = token,
                    ShowDateTime = showDateTime
                };

                if (displayMode == GrowlDisplayMode.Desktop)
                    Growl.ErrorGlobal(growlInfo);
                else
                    Growl.Error(growlInfo);
            });
        }

        /// <summary>
        /// 显示询问消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="actionBeforeClose">关闭前的回调函数</param>
        /// <param name="token">分组标识</param>
        /// <param name="confirmStr">确认按钮文本</param>
        /// <param name="cancelStr">取消按钮文本</param>
        public static void Ask(string message, Func<bool, bool> actionBeforeClose, string token = "",
            string confirmStr = "确认", string cancelStr = "取消")
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            ExecuteOnUIThread(() =>
            {
                Growl.AskGlobal(new GrowlInfo
                {
                    Message = message,
                    ActionBeforeClose = actionBeforeClose,
                    Token = token,
                    ConfirmStr = confirmStr,
                    CancelStr = cancelStr
                });
            });
        }

        /// <summary>
        /// 显示致命错误消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="staysOpen">是否保持打开直到用户手动关闭</param>
        public static void Fatal(string message, string token = "", bool staysOpen = true)
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            ExecuteOnUIThread(() =>
            {
                Growl.FatalGlobal(new GrowlInfo
                {
                    Message = message,
                    Token = token,
                    ShowDateTime = false,
                    StaysOpen = staysOpen
                });
            });
        }

        #endregion 基础消息方法

        #region 位置控制便捷方法

        /// <summary>
        /// 在窗口内显示信息消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        public static void InfoInWindow(string message, string token = "", int waitTime = -1, bool showDateTime = false)
        {
            Info(message, token, waitTime, showDateTime, GrowlDisplayMode.Window);
        }

        /// <summary>
        /// 在桌面显示信息消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        public static void InfoOnDesktop(string message, string token = "", int waitTime = -1, bool showDateTime = false)
        {
            Info(message, token, waitTime, showDateTime, GrowlDisplayMode.Desktop);
        }

        /// <summary>
        /// 在窗口内显示成功消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        public static void SuccessInWindow(string message, string token = "", int waitTime = -1, bool showDateTime = false)
        {
            Success(message, token, waitTime, showDateTime, GrowlDisplayMode.Window);
        }

        /// <summary>
        /// 在桌面显示成功消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        public static void SuccessOnDesktop(string message, string token = "", int waitTime = -1, bool showDateTime = false)
        {
            Success(message, token, waitTime, showDateTime, GrowlDisplayMode.Desktop);
        }

        /// <summary>
        /// 在窗口内显示警告消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        public static void WarningInWindow(string message, string token = "", int waitTime = -1, bool showDateTime = false)
        {
            Warning(message, token, waitTime, showDateTime, GrowlDisplayMode.Window);
        }

        /// <summary>
        /// 在桌面显示警告消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        public static void WarningOnDesktop(string message, string token = "", int waitTime = -1, bool showDateTime = false)
        {
            Warning(message, token, waitTime, showDateTime, GrowlDisplayMode.Desktop);
        }

        /// <summary>
        /// 在窗口内显示错误消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        public static void ErrorInWindow(string message, string token = "", int waitTime = -1, bool showDateTime = false)
        {
            Error(message, token, waitTime, showDateTime, GrowlDisplayMode.Window);
        }

        /// <summary>
        /// 在桌面显示错误消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="token">分组标识</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="showDateTime">是否显示时间戳</param>
        public static void ErrorOnDesktop(string message, string token = "", int waitTime = -1, bool showDateTime = false)
        {
            Error(message, token, waitTime, showDateTime, GrowlDisplayMode.Desktop);
        }

        #endregion 位置控制便捷方法

        #region 设备相关的便捷方法

        /// <summary>
        /// 显示设备相关的信息消息
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="message">消息内容</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="displayMode">显示模式</param>
        public static void DeviceInfo(EnuMcuDeviceType deviceType, string message, int waitTime = -1,
            GrowlDisplayMode displayMode = GrowlDisplayMode.Window)
        {
            string deviceName = GetDeviceName(deviceType);
            Info($"[{deviceName}] {message}", GetDeviceToken(deviceType), waitTime, false, displayMode);
        }

        /// <summary>
        /// 显示设备相关的成功消息
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="message">消息内容</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="displayMode">显示模式</param>
        public static void DeviceSuccess(EnuMcuDeviceType deviceType, string message, int waitTime = -1,
            GrowlDisplayMode displayMode = GrowlDisplayMode.Window)
        {
            string deviceName = GetDeviceName(deviceType);
            Success($"[{deviceName}] {message}", GetDeviceToken(deviceType), waitTime, false, displayMode);
        }

        /// <summary>
        /// 显示设备相关的警告消息
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="message">消息内容</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="displayMode">显示模式</param>
        public static void DeviceWarning(EnuMcuDeviceType deviceType, string message, int waitTime = -1,
            GrowlDisplayMode displayMode = GrowlDisplayMode.Window)
        {
            string deviceName = GetDeviceName(deviceType);
            Warning($"[{deviceName}] {message}", GetDeviceToken(deviceType), waitTime, false, displayMode);
        }

        /// <summary>
        /// 显示设备相关的错误消息
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="message">消息内容</param>
        /// <param name="waitTime">停留时间（秒），-1表示使用智能时间</param>
        /// <param name="displayMode">显示模式</param>
        public static void DeviceError(EnuMcuDeviceType deviceType, string message, int waitTime = -1,
            GrowlDisplayMode displayMode = GrowlDisplayMode.Window)
        {
            string deviceName = GetDeviceName(deviceType);
            Error($"[{deviceName}] {message}", GetDeviceToken(deviceType), waitTime, false, displayMode);
        }

        /// <summary>
        /// 显示命令执行结果
        /// </summary>
        /// <param name="commandName">命令名称</param>
        /// <param name="success">是否成功</param>
        /// <param name="details">详细信息</param>
        /// <param name="deviceType">设备类型（可选）</param>
        public static void CommandResult(string commandName, bool success, string details = "", EnuMcuDeviceType? deviceType = null)
        {
            string prefix = deviceType.HasValue ? $"[{GetDeviceName(deviceType.Value)}] " : "";
            string token = deviceType.HasValue ? GetDeviceToken(deviceType.Value) : Tokens.Command;

            if (success)
            {
                string message = $"{prefix}命令 {commandName} 执行成功";
                if (!string.IsNullOrEmpty(details))
                    message += $": {details}";
                Success(message, token);
            }
            else
            {
                string message = $"{prefix}命令 {commandName} 执行失败";
                if (!string.IsNullOrEmpty(details))
                    message += $": {details}";
                Error(message, token);
            }
        }

        /// <summary>
        /// 显示连接状态消息
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="connected">是否连接成功</param>
        public static void ConnectionStatus(EnuMcuDeviceType deviceType, bool connected)
        {
            string deviceName = GetDeviceName(deviceType);
            if (connected)
            {
                Success($"{deviceName} 连接成功", Tokens.Connection);
            }
            else
            {
                Error($"{deviceName} 连接失败，请检查设备状态和网络连接", Tokens.Connection);
            }
        }

        #endregion 设备相关的便捷方法

        #region 调试和开发相关方法

        /// <summary>
        /// 显示调试信息（仅在开发模式下显示）
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="memberName">调用方法名</param>
        /// <param name="filePath">调用文件路径</param>
        /// <param name="lineNumber">调用行号</param>
        public static void Debug(string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            if (!Golbal.IsDevDebug) return;

            string fileName = System.IO.Path.GetFileNameWithoutExtension(filePath);
            string debugMessage = $"[DEBUG] {fileName}.{memberName}:{lineNumber} - {message}";
            Info(debugMessage, Tokens.Debug, DebugWaitTime, showDateTime: true);
        }

        /// <summary>
        /// 显示性能信息
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="elapsedMilliseconds">耗时（毫秒）</param>
        public static void Performance(string operation, double elapsedMilliseconds)
        {
            if (!Golbal.IsDevDebug) return;

            string message = $"性能统计: {operation} 耗时 {elapsedMilliseconds:F2} 毫秒";
            Info(message, Tokens.Debug, ProductionWaitTime);
        }

        /// <summary>
        /// 显示进度信息
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="current">当前进度</param>
        /// <param name="total">总数</param>
        public static void Progress(string operation, int current, int total)
        {
            double percentage = (double)current / total * 100;
            string message = $"{operation}: {current}/{total} ({percentage:F1}%)";
            Info(message, Tokens.System, ProductionWaitTime);
        }

        #endregion 调试和开发相关方法

        #region 清理方法

        /// <summary>
        /// 清除指定Token的所有消息
        /// </summary>
        /// <param name="token">Token标识</param>
        public static void Clear(string token)
        {
            if (string.IsNullOrEmpty(token)) return;
            ExecuteOnUIThread(() => Growl.Clear(token));
        }

        /// <summary>
        /// 清除所有消息
        /// </summary>
        public static void ClearAll()
        {
            ExecuteOnUIThread(() => Growl.Clear());
        }

        /// <summary>
        /// 清除设备相关的消息
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        public static void ClearDevice(EnuMcuDeviceType deviceType)
        {
            Clear(GetDeviceToken(deviceType));
        }

        #endregion 清理方法

        #region 私有辅助方法

        /// <summary>
        /// 获取智能等待时间
        /// </summary>
        /// <param name="requestedTime">请求的时间，-1表示使用智能时间</param>
        /// <param name="defaultTime">默认时间</param>
        /// <returns>实际等待时间</returns>
        private static int GetSmartWaitTime(int requestedTime, int defaultTime)
        {
            if (requestedTime >= 0)
                return requestedTime;

            // 根据调试模式智能选择时间
            return Golbal.IsDevDebug ? DebugWaitTime : defaultTime;
        }

        /// <summary>
        /// 获取设备名称
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>设备名称</returns>
        private static string GetDeviceName(EnuMcuDeviceType deviceType)
        {
            return deviceType switch
            {
                EnuMcuDeviceType.Shuttle => "Shuttle",
                EnuMcuDeviceType.Robot => "Robot",
                EnuMcuDeviceType.ChamberA => "ChamberA",
                EnuMcuDeviceType.ChamberB => "ChamberB",
                _ => deviceType.ToString()
            };
        }

        /// <summary>
        /// 获取设备对应的Token
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>Token字符串</returns>
        private static string GetDeviceToken(EnuMcuDeviceType deviceType)
        {
            return deviceType switch
            {
                EnuMcuDeviceType.Shuttle => Tokens.Shuttle,
                EnuMcuDeviceType.Robot => Tokens.Robot,
                EnuMcuDeviceType.ChamberA => Tokens.Chamber,
                EnuMcuDeviceType.ChamberB => Tokens.Chamber,
                _ => Tokens.Device
            };
        }

        /// <summary>
        /// 确保在UI线程上执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        private static void ExecuteOnUIThread(Action action)
        {
            if (Application.Current?.Dispatcher?.CheckAccess() == true)
            {
                action();
            }
            else
            {
                Application.Current?.Dispatcher?.BeginInvoke(action);
            }
        }

        #endregion 私有辅助方法
    }
}