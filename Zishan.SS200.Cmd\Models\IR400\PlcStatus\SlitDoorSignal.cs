﻿using Prism.Mvvm;

namespace Zishan.SS200.Cmd.Models.IR400.PlcStatus
{
    /// <summary>
    /// SlitDoor控制命令
    /// </summary>
    public class SlitDoorSignal : BindableBase
    {
        #region LoadLock 门控制PLC命令

        /// <summary>
        /// LoadLock关门指令
        /// </summary>
        public bool LoadLockDoorClose { get => _LoadLockDoorClose; set => SetProperty(ref _LoadLockDoorClose, value); }
        private bool _LoadLockDoorClose;

        /// <summary>
        /// LoadLock开门指令
        /// </summary>
        public bool LoadLockDoorOpen { get => _LoadLockDoorOpen; set => SetProperty(ref _LoadLockDoorOpen, value); }
        private bool _LoadLockDoorOpen;

        /// <summary>
        /// LoadLock关门状态反馈是否完成
        /// </summary>
        public bool LoadLockDoorCloseState { get => _LoadLockDoorCloseState; set => SetProperty(ref _LoadLockDoorCloseState, value); }
        private bool _LoadLockDoorCloseState;

        /// <summary>
        /// LoadLock开门状态反馈是否完成
        /// </summary>
        public bool LoadLockDoorOpenState { get => _LoadLockDoorOpenState; set => SetProperty(ref _LoadLockDoorOpenState, value); }
        private bool _LoadLockDoorOpenState;

        /// <summary>
        /// LoadLock关门条件是否满足
        /// </summary>
        public bool LoadLockDoorCloseCondition { get => _LoadLockDoorCloseCondition; set => SetProperty(ref _LoadLockDoorCloseCondition, value); }
        private bool _LoadLockDoorCloseCondition;

        /// <summary>
        /// LoadLock开门条件是否满足
        /// </summary>
        public bool LoadLockDoorOpenCondition { get => _LoadLockDoorOpenCondition; set => SetProperty(ref _LoadLockDoorOpenCondition, value); }
        private bool _LoadLockDoorOpenCondition;

        //public const string LoadLockDoorClose = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_B_Manual_Pos_1";
        //public const string LoadLockDoorOpen = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_B_Manual_Pos_2";

        //public const string LoadLockDoorCloseState = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.o_InPlace_Pos_1";
        //public const string LoadLockDoorOpenState = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.o_InPlace_Pos_2";

        //public const string LoadLockDoorCloseCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_StartConditions_Pos_1";
        //public const string LoadLockDoorOpenCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_StartConditions_Pos_2";

        #endregion LoadLock 门控制PLC命令

        #region CHA 门控制PLC命令

        /// <summary>
        /// CHADoor关闭指令
        /// </summary>
        public bool CHADoorClose { get => _CHADoorClose; set => SetProperty(ref _CHADoorClose, value); }
        private bool _CHADoorClose;

        /// <summary>
        /// CHADoor打开指令
        /// </summary>
        public bool CHADoorOpen { get => _CHADoorOpen; set => SetProperty(ref _CHADoorOpen, value); }
        private bool _CHADoorOpen;

        /// <summary>
        /// CHADoor关闭状态反馈是否完成
        /// </summary>
        public bool CHADoorCloseState { get => _CHADoorCloseState; set => SetProperty(ref _CHADoorCloseState, value); }
        private bool _CHADoorCloseState;

        /// <summary>
        /// CHADoor打开状态反馈是否完成
        /// </summary>
        public bool CHADoorOpenState { get => _CHADoorOpenState; set => SetProperty(ref _CHADoorOpenState, value); }
        private bool _CHADoorOpenState;

        /// <summary>
        /// CHADoor关闭条件是否满足
        /// </summary>
        public bool CHADoorCloseCondition { get => _CHADoorCloseCondition; set => SetProperty(ref _CHADoorCloseCondition, value); }
        private bool _CHADoorCloseCondition;

        /// <summary>
        /// CHADoor打开条件是否满足
        /// </summary>
        public bool CHADoorOpenCondition { get => _CHADoorOpenCondition; set => SetProperty(ref _CHADoorOpenCondition, value); }
        private bool _CHADoorOpenCondition;

        //public const string CHADoorClose = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_B_Manual_Pos_1";
        //public const string CHADoorOpen = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_B_Manual_Pos_2";

        //public const string CHADoorCloseState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.o_InPlace_Pos_1";
        //public const string CHADoorOpenState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.o_InPlace_Pos_2";

        //public const string CHADoorCloseCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_StartConditions_Pos_1";
        //public const string CHADoorOpenCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_StartConditions_Pos_2";

        #endregion CHA 门控制PLC命令

        #region CHB 门控制PLC命令

        /// <summary>
        /// CHBDoor关闭指令
        /// </summary>
        public bool CHBDoorClose { get => _CHBDoorClose; set => SetProperty(ref _CHBDoorClose, value); }
        private bool _CHBDoorClose;

        /// <summary>
        /// CHBDoor打开指令
        /// </summary>
        public bool CHBDoorOpen { get => _CHBDoorOpen; set => SetProperty(ref _CHBDoorOpen, value); }
        private bool _CHBDoorOpen;

        /// <summary>
        /// CHBDoor关闭状态反馈是否完成
        /// </summary>
        public bool CHBDoorCloseState { get => _CHBDoorCloseState; set => SetProperty(ref _CHBDoorCloseState, value); }
        private bool _CHBDoorCloseState;

        /// <summary>
        /// CHBDoor打开状态反馈是否完成
        /// </summary>
        public bool CHBDoorOpenState { get => _CHBDoorOpenState; set => SetProperty(ref _CHBDoorOpenState, value); }
        private bool _CHBDoorOpenState;

        /// <summary>
        /// CHBDoor关闭条件是否满足
        /// </summary>
        public bool CHBDoorCloseCondition { get => _CHBDoorCloseCondition; set => SetProperty(ref _CHBDoorCloseCondition, value); }
        private bool _CHBDoorCloseCondition;

        /// <summary>
        /// CHBDoor打开条件是否满足
        /// </summary>
        public bool CHBDoorOpenCondition { get => _CHBDoorOpenCondition; set => SetProperty(ref _CHBDoorOpenCondition, value); }
        private bool _CHBDoorOpenCondition;

        //public const string CHBDoorClose = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_B_Manual_Pos_1";
        //public const string CHBDoorOpen = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_B_Manual_Pos_2";

        //public const string CHBDoorCloseState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.o_InPlace_Pos_1";
        //public const string CHBDoorOpenState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.o_InPlace_Pos_2";

        //public const string CHBDoorCloseCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_StartConditions_Pos_1";
        //public const string CHBDoorOpenCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_StartConditions_Pos_2";

        #endregion CHB 门控制PLC命令

        #region CHC 门控制PLC命令

        /// <summary>
        /// CHCDoor关闭指令
        /// </summary>
        public bool CHCDoorClose { get => _CHCDoorClose; set => SetProperty(ref _CHCDoorClose, value); }
        private bool _CHCDoorClose;

        /// <summary>
        /// CHCDoor打开指令
        /// </summary>
        public bool CHCDoorOpen { get => _CHCDoorOpen; set => SetProperty(ref _CHCDoorOpen, value); }
        private bool _CHCDoorOpen;

        /// <summary>
        /// CHCDoor关闭状态反馈是否完成
        /// </summary>
        public bool CHCDoorCloseState { get => _CHCDoorCloseState; set => SetProperty(ref _CHCDoorCloseState, value); }
        private bool _CHCDoorCloseState;

        /// <summary>
        /// CHCDoor打开状态反馈是否完成
        /// </summary>
        public bool CHCDoorOpenState { get => _CHCDoorOpenState; set => SetProperty(ref _CHCDoorOpenState, value); }
        private bool _CHCDoorOpenState;

        /// <summary>
        /// CHCDoor关闭条件是否满足
        /// </summary>
        public bool CHCDoorCloseCondition { get => _CHCDoorCloseCondition; set => SetProperty(ref _CHCDoorCloseCondition, value); }
        private bool _CHCDoorCloseCondition;

        /// <summary>
        /// CHCDoor打开条件是否满足
        /// </summary>
        public bool CHCDoorOpenCondition { get => _CHCDoorOpenCondition; set => SetProperty(ref _CHCDoorOpenCondition, value); }
        private bool _CHCDoorOpenCondition;

        //public const string CHCDoorClose = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_B_Manual_Pos_1";
        //public const string CHCDoorOpen = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_B_Manual_Pos_2";
        //public const string CHCDoorCloseState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.o_InPlace_Pos_1";
        //public const string CHCDoorOpenState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.o_InPlace_Pos_2";
        //public const string CHCDoorCloseCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_StartConditions_Pos_1";
        //public const string CHCDoorOpenCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_StartConditions_Pos_2";

        #endregion CHC 门控制PLC命令

        /// <summary>
        /// Cooling 处理完成
        /// </summary>
        public bool CoolingProcessFinished { get => _CoolingProcessFinished; set => SetProperty(ref _CoolingProcessFinished, value); }
        private bool _CoolingProcessFinished;

        /// <summary>
        /// CHA 处理完成
        /// </summary>
        public bool CHAProcessFinished { get => _CHAProcessFinished; set => SetProperty(ref _CHAProcessFinished, value); }
        private bool _CHAProcessFinished;

        /// <summary>
        /// CHB 处理完成
        /// </summary>
        public bool CHBProcessFinished { get => _CHBProcessFinished; set => SetProperty(ref _CHBProcessFinished, value); }
        private bool _CHBProcessFinished;

        /// <summary>
        /// CHC 处理完成
        /// </summary>
        public bool CHCProcessFinished { get => _CHCProcessFinished; set => SetProperty(ref _CHCProcessFinished, value); }
        private bool _CHCProcessFinished;
    }
}