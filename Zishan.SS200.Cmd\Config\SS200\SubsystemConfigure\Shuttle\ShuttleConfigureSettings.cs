using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Shuttle;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Shuttle
{
    /// <summary>
    /// 穿梭室配置设置类
    /// 用于统一访问穿梭室配置参数
    /// </summary>
    public class ShuttleConfigureSettings
    {
        private static readonly Lazy<ShuttleConfigureSettings> _instance =
            new Lazy<ShuttleConfigureSettings>(() => new ShuttleConfigureSettings());

        public static ShuttleConfigureSettings Instance => _instance.Value;

        private ShuttleConfigureSettings()
        {
            // 私有构造函数，防止外部实例化
        }

        #region 卡匣巢和门控制参数

        /// <summary>
        /// 卡匣巢伸展/缩回最小时间(秒)
        /// </summary>
        public int CassetteNestExtendRetractMinTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS1);

        /// <summary>
        /// 卡匣巢伸展/缩回最大时间(秒)
        /// </summary>
        public int CassetteNestExtendRetractMaxTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS2);

        /// <summary>
        /// 卡匣门运动最小时间(秒)
        /// </summary>
        public int CassetteDoorMotionMinTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS6);

        /// <summary>
        /// 卡匣门运动最大时间(秒)
        /// </summary>
        public int CassetteDoorMotionMaxTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS7);

        #endregion 卡匣巢和门控制参数

        #region 穿梭室运动控制参数

        /// <summary>
        /// 穿梭室上升/下降最小时间(秒)
        /// </summary>
        public int ShuttleUpDownMinTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS3);

        /// <summary>
        /// 穿梭室上升/下降最大时间(秒)
        /// </summary>
        public int ShuttleUpDownMaxTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS4);

        /// <summary>
        /// 穿梭室旋转最大时间(秒)
        /// </summary>
        public int ShuttleRotateMaxTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS5);

        #endregion 穿梭室运动控制参数

        #region 阀门控制参数

        /// <summary>
        /// ISO阀运动最大时间(秒)
        /// </summary>
        public int ISOValveMotionMaxTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS9);

        #endregion 阀门控制参数

        #region 传感器使能参数

        /// <summary>
        /// 前滑出传感器使能
        /// </summary>
        public bool FrontSlideOutSensorEnable =>
            ShuttleConfigParametersProvider.Instance.GetBoolSettingValue(EnuShuttleConfigParameterCodes.SPS10);

        /// <summary>
        /// 后滑出传感器使能
        /// </summary>
        public bool BackSlideOutSensorEnable =>
            ShuttleConfigParametersProvider.Instance.GetBoolSettingValue(EnuShuttleConfigParameterCodes.SPS11);

        #endregion 传感器使能参数

        #region 真空系统参数

        /// <summary>
        /// 穿梭室传输压力(Torr)
        /// </summary>
        public int ShuttleTransferPressure =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS12);

        /// <summary>
        /// 穿梭室上升/下降压差(Torr)
        /// </summary>
        public int DeltaPressureForShuttleUpDown =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS13);

        /// <summary>
        /// 装载锁大气压力最小值(Torr)
        /// </summary>
        public int LoadlockATMPressureMinimum =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS14);

        /// <summary>
        /// 穿梭室大气压力最小值(Torr)
        /// </summary>
        public int ShuttleATMPressureMinimum =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS15);

        /// <summary>
        /// 传输压力(Torr)
        /// </summary>
        public int TransferPressure =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS16);

        /// <summary>
        /// 穿梭室抽真空最大时间(分钟)
        /// </summary>
        public int PumpDownShuttleMaxTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS17);

        /// <summary>
        /// 装载锁大气压力设定点(Torr)
        /// </summary>
        public int LoadlockATMPressureSetpoint =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS18);

        /// <summary>
        /// 穿梭室大气压力最小设定点(Torr)
        /// </summary>
        public int ShuttleATMPressureMinimumSetpoint =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS19);

        /// <summary>
        /// 穿梭室压力偏移(Torr)
        /// </summary>
        public int ShuttlePressureOffset =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS20);

        /// <summary>
        /// 装载锁压力偏移(Torr)
        /// </summary>
        public int LoadlockPressureOffset =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS21);

        /// <summary>
        /// 穿梭室回填最大时间(分钟)
        /// </summary>
        public int ShuttleBackfillMaxTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS22);

        /// <summary>
        /// 装载锁回填最大时间(分钟)
        /// </summary>
        public int LoadlockBackfillMaxTime =>
            ShuttleConfigParametersProvider.Instance.GetIntSettingValue(EnuShuttleConfigParameterCodes.SPS23);

        #endregion 真空系统参数
    }
}