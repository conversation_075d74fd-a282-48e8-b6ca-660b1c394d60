﻿AR56
PW to CB
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			CB slot status review
LSS15 / LSS16
				LSS13=0 LSS14=0
					paddle slot status review
LSS5~LSS8
						LSS5~LSS8=0
							compare ABS(RTF-RP3) with ABS(RTF-RP7)
								ABS(RTF-RP3)≤ABS(RTF-RP7)
									AR3
T-axis smooth to cooling
										AR30
Move Z-axis height smooth to CB put
											AR13
R-axis smooth to cooling extend
												AR24
Move Z-axis height smooth to CB get
													AR66
wafer status smooth paddle to CB
														AR19
R-axis zero position
															command done
								ABS(RTF-RP3)>ABS(RTF-RP7)
									AR7
T-axis nose to cooling
										AR32
Move Z-axis height nose to CB put
											AR17
R-axis nose to cooling extend
												AR28
Move Z-axis height nose to CB get
													AR77
wafer status nose paddle to CB
														AR19
R-axis zero position
															command done
						LSS7 XX=1
or/and
LSS8 XX=1
							AR7
T-axis nose to cooling
								AR32
Move Z-axis height nose to CB put
									AR17
R-axis nose to cooling extend
										AR28
Move Z-axis height nose to CB get
											AR77
wafer status nose paddle to CB
												AR19
R-axis zero position
													command done
						LSS5 XX=1
or/and
LSS6 XX=1
							AR3
T-axis smooth to cooling
								AR30
Move Z-axis height smooth to CB put
									AR13
R-axis smooth to cooling extend
										AR24
Move Z-axis height smooth to CB get
											AR66
wafer status smooth paddle to CB
												AR19
R-axis zero position
													command done
				others status
					RA54 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM