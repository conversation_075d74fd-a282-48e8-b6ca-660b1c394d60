﻿namespace Zishan.SS200.Cmd.Constants
{
    /// <summary>
    /// SlitDoor控制命令 PLC变量路径
    /// </summary>
    public static class SlitDoorPLCCmdConstants
    {
        //SlitDoor门控制PLC命令

        //LoadLock
        //  close GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_B_Manual_Pos_1              Open GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_B_Manual_Pos_2
        //  condition   GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_StartConditions_Pos_1= FALSE       GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_StartConditions_Pos_2= FALSE

        //close state   GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.o_InPlace_Pos_1 Open state GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.o_InPlace_Pos_2

        //CHA
        //      close GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_B_Manual_Pos_1 Open  GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_B_Manual_Pos_2
        //  condition   GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_StartConditions_Pos_1= FALSE       GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_StartConditions_Pos_2= FALSE

        //close state   GVL.Buffer_IO.Cylinder_SlitDoor_CHA.o_InPlace_Pos_1 Open state GVL.Buffer_IO.Cylinder_SlitDoor_CHA.o_InPlace_Pos_2

        //CHB
        //      close GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_B_Manual_Pos_1 Open  GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_B_Manual_Pos_2
        //  condition   GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_StartConditions_Pos_1= FALSE          GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_StartConditions_Pos_2= FALSE

        //close state   GVL.Buffer_IO.Cylinder_SlitDoor_CHB.o_InPlace_Pos_1 Open state GVL.Buffer_IO.Cylinder_SlitDoor_CHB.o_InPlace_Pos_2

        //CHC
        //      close GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_B_Manual_Pos_1 Open  GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_B_Manual_Pos_2
        //  condition  GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_StartConditions_Pos_1= FALSE        GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_StartConditions_Pos_2= FALSE

        //close state   GVL.Buffer_IO.Cylinder_SlitDoor_CHC.o_InPlace_Pos_1 Open state GVL.Buffer_IO.Cylinder_SlitDoor_CHC.o_InPlace_Pos_2

        #region LoadLock SlitDoor门控制PLC命令

        public const string LoadLockDoorClose = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_B_Manual_Pos_1";
        public const string LoadLockDoorOpen = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_B_Manual_Pos_2";

        public const string LoadLockDoorCloseState = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.o_InPlace_Pos_1";
        public const string LoadLockDoorOpenState = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.o_InPlace_Pos_2";

        public const string LoadLockDoorCloseCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_StartConditions_Pos_1";
        public const string LoadLockDoorOpenCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_LoadLock.i_StartConditions_Pos_2";

        #endregion LoadLock SlitDoor门控制PLC命令

        #region CHA 门控制PLC命令

        public const string CHADoorClose = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_B_Manual_Pos_1";
        public const string CHADoorOpen = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_B_Manual_Pos_2";

        public const string CHADoorCloseState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.o_InPlace_Pos_1";
        public const string CHADoorOpenState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.o_InPlace_Pos_2";

        public const string CHADoorCloseCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_StartConditions_Pos_1";
        public const string CHADoorOpenCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHA.i_StartConditions_Pos_2";

        #endregion CHA 门控制PLC命令

        #region CHB 门控制PLC命令

        public const string CHBDoorClose = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_B_Manual_Pos_1";
        public const string CHBDoorOpen = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_B_Manual_Pos_2";

        public const string CHBDoorCloseState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.o_InPlace_Pos_1";
        public const string CHBDoorOpenState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.o_InPlace_Pos_2";

        public const string CHBDoorCloseCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_StartConditions_Pos_1";
        public const string CHBDoorOpenCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHB.i_StartConditions_Pos_2";

        #endregion CHB 门控制PLC命令

        #region CHC 门控制PLC命令

        public const string CHCDoorClose = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_B_Manual_Pos_1";
        public const string CHCDoorOpen = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_B_Manual_Pos_2";
        public const string CHCDoorCloseState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.o_InPlace_Pos_1";
        public const string CHCDoorOpenState = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.o_InPlace_Pos_2";
        public const string CHCDoorCloseCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_StartConditions_Pos_1";
        public const string CHCDoorOpenCondition = "GVL.Buffer_IO.Cylinder_SlitDoor_CHC.i_StartConditions_Pos_2";

        #endregion CHC 门控制PLC命令

        /// <summary>
        /// 门检查是否关闭，溢出时间，单位：秒，默认30秒
        /// </summary>
        public const double DoorCloseCheckTimeout = 10;
    }
}