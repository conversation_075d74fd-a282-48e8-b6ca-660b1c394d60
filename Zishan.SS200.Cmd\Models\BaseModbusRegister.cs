using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;

namespace Zishan.SS200.Cmd.Models
{
    /// <summary>
    /// Modbus寄存器基类
    /// </summary>
    public partial class BaseModbusRegister : ObservableObject
    {
        /// <summary>
        /// Modbus地址
        /// </summary>
        public ushort Address { get; set; }

        /// <summary>
        /// 变量名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 变量标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 变量描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 备注,PLC中的变量名，例如：GVL_ModbusTest.ResponseSend、GVL_ModbusTest.CMD_Enable
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 当前Modbus寄存器16位原始值
        /// </summary>
        [ObservableProperty]
        private ushort value;

        /// <summary>
        /// 是否可写
        /// </summary>
        public bool IsWriteable { get; set; }
    }
}