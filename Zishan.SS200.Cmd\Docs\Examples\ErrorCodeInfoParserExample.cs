using System;
using Zishan.SS200.Cmd.Config;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// AlarmInfoParser使用示例
    /// </summary>
    public class AlarmInfoParserExample
    {
        /// <summary>
        /// 示例方法
        /// </summary>
        public static void Example()
        {
            // 创建解析器实例
            var errorCodeInfoParser = new ErrorCodeInfoParser();

            // 添加配置文件
            errorCodeInfoParser.AddConfigFilePath("Robot", "Configs/ErrorCodeInfo/RobotAlarmInfo.json");
            errorCodeInfoParser.AddConfigFilePath("SHTL", "Configs/ErrorCodeInfo/SHTLAlarmInfo.json");

            try
            {
                // 获取错误信息示例
                var robotAlarm = errorCodeInfoParser.GetAlarmInfo("Robot", "0001");
                Console.WriteLine($"Robot错误 0001: {robotAlarm.Kind} - {robotAlarm.Cause}");

                var shtlAlarm = errorCodeInfoParser.GetAlarmInfo("SHTL", "0002");
                Console.WriteLine($"SHTL错误 0002: {shtlAlarm.Kind} - {shtlAlarm.Cause}");

                // 尝试获取错误信息
                if (errorCodeInfoParser.TryGetAlarmInfo("Robot", "000F", out var alarmInfo))
                {
                    Console.WriteLine($"找到错误: {alarmInfo.Kind} - {alarmInfo.Cause}");
                }
                else
                {
                    Console.WriteLine("未找到错误信息");
                }

                // 获取所有SHTL错误信息
                Console.WriteLine("\nSHTL所有错误信息:");
                foreach (var alarm in errorCodeInfoParser.GetAlarmInfosBySource("SHTL"))
                {
                    Console.WriteLine($"  {alarm.Code}: {alarm.Kind} - {alarm.Cause}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析命令响应中的错误代码
        /// </summary>
        /// <param name="source">错误源</param>
        /// <param name="responseCode">响应代码</param>
        /// <returns>格式化的错误信息</returns>
        public static string ParseErrorResponse(ErrorCodeInfoParser parser, string source, string responseCode)
        {
            if (parser.TryGetAlarmInfo(source, responseCode, out var alarmInfo))
            {
                return $"错误: [{alarmInfo.Code}] {alarmInfo.Kind} - {alarmInfo.Cause}";
            }
            return $"未知错误: {responseCode}";
        }
    }
}