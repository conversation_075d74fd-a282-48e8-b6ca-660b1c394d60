<UserControl
    x:Class="Zishan.SS200.Cmd.Views.IntegerConversionView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    d:DesignHeight="250"
    d:DesignWidth="600"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        <Style x:Key="CopyButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="3,0,0,0" />
            <Setter Property="Padding" Value="3,0" />
            <Setter Property="ToolTip" Value="复制到剪贴板" />
            <Setter Property="Background" Value="#E8F5E9" />
            <Setter Property="BorderBrush" Value="#81C784" />
            <Setter Property="Foreground" Value="#2E7D32" />
        </Style>
    </UserControl.Resources>

    <UserControl.DataContext>
        <viewModels:IntegerConversionViewModel />
    </UserControl.DataContext>

    <GroupBox Header="32位整数和16位整数转换工具">
        <Grid Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />  <!-- 功能按钮区 -->
                <RowDefinition Height="Auto" />  <!-- 32位整数输入 -->
                <RowDefinition Height="Auto" />  <!-- 16位整数输入 -->
                <RowDefinition Height="Auto" />  <!-- 消息显示区 -->
            </Grid.RowDefinitions>
            
            <!-- 功能按钮区 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,8">
                <CheckBox 
                    Content="十六进制显示" 
                    IsChecked="{Binding UseHexDisplay}" 
                    Command="{Binding ToggleDisplayFormatCommand}"
                    VerticalAlignment="Center"
                    Margin="0,0,10,0" />
                <Button 
                    Content="清空所有" 
                    Command="{Binding ClearAllFieldsCommand}"
                    Padding="5,2"
                    Background="#FBE9E7" 
                    Foreground="#D32F2F"
                    BorderBrush="#FFAB91" />
            </StackPanel>

            <!--  32位整数输入  -->
            <DockPanel Grid.Row="1" Margin="0,5">
                <TextBlock
                    MinWidth="80"
                    VerticalAlignment="Center"
                    DockPanel.Dock="Left"
                    Text="32位整数："
                    TextAlignment="Right" />
                
                <Button 
                    DockPanel.Dock="Right"
                    Content="转换为16位" 
                    Command="{Binding ConvertInt32ToInt16Command}"
                    Margin="10,0,0,0" 
                    Padding="10,2"
                    Background="#E3F2FD"
                    BorderBrush="#90CAF9"
                    Foreground="#1565C0" />
                
                <Button 
                    DockPanel.Dock="Right"
                    Content="复制" 
                    Command="{Binding CopyInt32ValueCommand}"
                    Style="{StaticResource CopyButtonStyle}" />
                
                <TextBox Height="24" Text="{Binding Int32Value, UpdateSourceTrigger=PropertyChanged}">
                    <TextBox.ToolTip>
                        <TextBlock>
                            <Run Text="输入32位整数" />
                            <LineBreak />
                            <Run Text="支持十进制数字或带0x前缀的十六进制" />
                        </TextBlock>
                    </TextBox.ToolTip>
                </TextBox>
            </DockPanel>

            <!--  16位整数输入  -->
            <DockPanel Grid.Row="2" Margin="0,5">
                <TextBlock
                    MinWidth="80"
                    VerticalAlignment="Center"
                    DockPanel.Dock="Left"
                    Text="16位整数："
                    TextAlignment="Right" />
                
                <Button 
                    DockPanel.Dock="Right"
                    Content="转换为32位"
                    Command="{Binding ConvertInt16ToInt32Command}"
                    Margin="10,0,0,0"
                    Padding="10,2"
                    Background="#E3F2FD"
                    BorderBrush="#90CAF9"
                    Foreground="#1565C0" />

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <DockPanel Grid.Column="0">
                        <Button 
                            DockPanel.Dock="Right"
                            Content="复制" 
                            Command="{Binding CopyInt16Value1Command}"
                            Style="{StaticResource CopyButtonStyle}" />
                        <TextBox Height="24" Text="{Binding Int16Value1, UpdateSourceTrigger=PropertyChanged}">
                            <TextBox.ToolTip>
                                <TextBlock>
                                    <Run Text="输入高16位整数" />
                                    <LineBreak />
                                    <Run Text="支持十进制数字或带0x前缀的十六进制" />
                                </TextBlock>
                            </TextBox.ToolTip>
                        </TextBox>
                    </DockPanel>

                    <TextBlock
                        Grid.Column="1"
                        Margin="5,0"
                        VerticalAlignment="Center"
                        Text="," />

                    <DockPanel Grid.Column="2">
                        <Button 
                            DockPanel.Dock="Right"
                            Content="复制" 
                            Command="{Binding CopyInt16Value2Command}"
                            Style="{StaticResource CopyButtonStyle}" />
                        <TextBox Height="24" Text="{Binding Int16Value2, UpdateSourceTrigger=PropertyChanged}">
                            <TextBox.ToolTip>
                                <TextBlock>
                                    <Run Text="输入低16位整数" />
                                    <LineBreak />
                                    <Run Text="支持十进制数字或带0x前缀的十六进制" />
                                </TextBlock>
                            </TextBox.ToolTip>
                        </TextBox>
                    </DockPanel>
                </Grid>
            </DockPanel>

            <!--  消息显示区  -->
            <StackPanel Grid.Row="3" Margin="0,8,0,0">
                <!-- 成功消息 -->
                <TextBlock
                    Margin="0,2"
                    Foreground="#4CAF50"
                    Text="{Binding ConversionSuccess}"
                    TextWrapping="Wrap"
                    Visibility="{Binding ConversionSuccess, Converter={StaticResource StringToVisibilityConverter}}" />
                
                <!-- 错误消息 -->
                <TextBlock
                    Margin="0,2"
                    Foreground="Red"
                    Text="{Binding ConversionError}"
                    TextWrapping="Wrap"
                    Visibility="{Binding ConversionError, Converter={StaticResource StringToVisibilityConverter}}" />
            </StackPanel>
        </Grid>
    </GroupBox>
</UserControl> 