using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using Zishan.SS200.Cmd.Models;
using System.Threading.Tasks;
using System;
using System.Linq;
using System.Collections.Generic;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Config;
using log4net;
using System.IO;
using System.Threading;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using System.Text;

namespace Zishan.SS200.Cmd.ViewModels.Dock
{
    /// <summary>
    /// 主窗口视图模型
    /// </summary>
    public partial class UiViewModel : ObservableObject
    {
        //private readonly IS200McuCmdService _mcuCmdService;
        private readonly ILog _logger = LogManager.GetLogger(typeof(UiViewModel));

        // 创建电机告警信息解析器实例
        private readonly MotorAlarmInfoParser _motorAlarmInfoParser;

        [ObservableProperty]
        private IS200McuCmdService _mcuCmdService;

        /// <summary>
        /// 状态栏信息
        /// </summary>
        [ObservableProperty]
        private string statusBarInfo;

        /// <summary>
        /// Shuttle设备是否支持线圈读取
        /// </summary>
        public bool IsShuttleCoilReadingSupported => McuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.Shuttle);

        /// <summary>
        /// Robot设备是否支持线圈读取
        /// </summary>
        public bool IsRobotCoilReadingSupported => McuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.Robot);

        /// <summary>
        /// Cha设备是否支持线圈读取
        /// </summary>
        public bool IsChaCoilReadingSupported => McuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.ChamberA);

        /// <summary>
        /// Chb设备是否支持线圈读取
        /// </summary>
        public bool IsChbCoilReadingSupported => McuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.ChamberB);

        /// <summary>
        /// Robot电机寄存器映射区 报警、RTZ位置信息，使用ModbusRegister类型
        /// </summary>
        public ObservableCollection<ModbusRegister> RobotAlarmRegisters => McuCmdService.RobotAlarmRegisters;

        //T轴错误代码：RobotAlarmRegisters[0].Value
        //R轴错误代码：RobotAlarmRegisters[1].Value
        //Z轴错误代码：RobotAlarmRegisters[2].Value
        //定义T轴、R轴、Z轴的错误代码对应的报警信，错误信息根据上面的错误代码获取，用于UI ToolTip显示

        /// <summary>
        /// T轴错误信息，用于UI ToolTip显示
        /// </summary>
        public string TAxisErrorInfo => RobotAlarmRegisters.Count > 0 && RobotAlarmRegisters[0].Value != 0
            ? GetMotorErrorTooltip(RobotAlarmRegisters[0].Value.ToString("X4"))
            : null;

        /// <summary>
        /// R轴错误信息，用于UI ToolTip显示
        /// </summary>
        public string RAxisErrorInfo => RobotAlarmRegisters.Count > 1 && RobotAlarmRegisters[1].Value != 0
            ? GetMotorErrorTooltip(RobotAlarmRegisters[1].Value.ToString("X4"))
            : null;

        /// <summary>
        /// Z轴错误信息，用于UI ToolTip显示
        /// </summary>
        public string ZAxisErrorInfo => RobotAlarmRegisters.Count > 2 && RobotAlarmRegisters[2].Value != 0
            ? GetMotorErrorTooltip(RobotAlarmRegisters[2].Value.ToString("X4"))
            : null;

        #region RTZ轴位置访问 - 优化使用S200McuCmdService的新接口

        /// <summary>
        /// T轴当前步进位置值 - 使用优化的服务接口
        /// </summary>
        public int TAxisStep => McuCmdService.CurrentTAxisStep;

        /// <summary>
        /// R轴当前步进位置值 - 使用优化的服务接口
        /// </summary>
        public int RAxisStep => McuCmdService.CurrentRAxisStep;

        /// <summary>
        /// Z轴当前步进位置值 - 使用优化的服务接口
        /// </summary>
        public int ZAxisStep => McuCmdService.CurrentZAxisStep;

        /// <summary>
        /// T旋转轴角度值（度） - 使用优化的服务接口
        /// 转换公式：步进值/100000*360
        /// </summary>
        public double TAxisDegree => McuCmdService.CurrentTAxisDegree;

        /// <summary>
        /// R伸缩轴长度值（mm） - 使用优化的服务接口
        /// 转换公式：L = Sin(步进值/50000*360)*2*208.96
        /// </summary>
        public double RAxisLength => McuCmdService.CurrentRAxisLength;

        /// <summary>
        /// Z上下轴高度值（mm） - 使用优化的服务接口
        /// 转换公式：步进值/1000*5
        /// </summary>
        public double ZAxisHeight => McuCmdService.CurrentZAxisHeight;

        /// <summary>
        /// 获取RTZ轴位置的组合信息（步进值）
        /// </summary>
        public async Task<(int TAxisStep, int RAxisStep, int ZAxisStep)> GetRTZSteps() => await McuCmdService.GetCurrentRTZSteps();

        /// <summary>
        /// 获取RTZ轴物理位置的组合信息
        /// </summary>
        public async Task<(double TAxisDegree, double RAxisLength, double ZAxisHeight)> GetRTZPhysicalValues() => await McuCmdService.GetCurrentRTZPhysicalValues();

        /// <summary>
        /// 检查RTZ轴位置数据是否有效
        /// </summary>
        public bool IsRTZPositionDataValid => McuCmdService.IsRTZPositionDataValid;

        /// <summary>
        /// 获取RTZ轴位置的格式化字符串（用于显示和复制）
        /// </summary>
        public async Task<string> GetRTZPositionDisplayText()
        {
            if (!IsRTZPositionDataValid)
                return "RTZ轴位置数据无效";

            var (tAxisStep, rAxisStep, zAxisStep) = await GetRTZSteps();
            var (tAxisDegree, rAxisLength, zAxisHeight) = await GetRTZPhysicalValues();

            return $"T轴: {tAxisStep} steps ({tAxisDegree:F2}°), " +
                   $"R轴: {rAxisStep} steps ({rAxisLength:F2}mm), " +
                   $"Z轴: {zAxisStep} steps ({zAxisHeight:F2}mm)";
        }

        /// <summary>
        /// 获取RTZ轴位置的简化字符串（仅步进值）
        /// </summary>
        public async Task<string> GetRTZPositionSimpleText()
        {
            if (!IsRTZPositionDataValid)
                return "数据无效";

            var (tAxisStep, rAxisStep, zAxisStep) = await GetRTZSteps();
            return $"T轴: {tAxisStep}, R轴: {rAxisStep}, Z轴: {zAxisStep}";
        }

        #endregion RTZ轴位置访问

        #region PinSearch P1 P2

        public ushort PinSearchP1_H => RobotAlarmRegisters.Count > 9 ? RobotAlarmRegisters[9].Value : (ushort)0;
        public ushort PinSearchP1_L => RobotAlarmRegisters.Count > 10 ? RobotAlarmRegisters[10].Value : (ushort)0;

        public ushort PinSearchP2_H => RobotAlarmRegisters.Count > 11 ? RobotAlarmRegisters[11].Value : (ushort)0;
        public ushort PinSearchP2_L => RobotAlarmRegisters.Count > 12 ? RobotAlarmRegisters[12].Value : (ushort)0;

        /// <summary>
        /// Pin Search P1点位合并值（32位整数）
        /// </summary>
        public int PinSearchP1Value => RobotAlarmRegisters.Count > 9 ? RobotAlarmRegisters[9].Combinevalue : 0;

        /// <summary>
        /// Pin Search P2点位合并值（32位整数）
        /// </summary>
        public int PinSearchP2Value => RobotAlarmRegisters.Count > 11 ? RobotAlarmRegisters[11].Combinevalue : 0;

        #endregion PinSearch P1 P2

        /// <summary>
        /// 监听RobotAlarmRegisters变化，更新轴错误信息
        /// </summary>
        private void InitializeRegisterPropertyChangeHandlers()
        {
            // 监听集合本身的变化，处理可能的延迟加载
            if (McuCmdService.RobotAlarmRegisters is ObservableCollection<ModbusRegister> collection)
            {
                collection.CollectionChanged += (s, e) =>
                {
                    // 当集合有新项添加时，为它们设置监听
                    if (e.NewItems != null)
                    {
                        foreach (ModbusRegister register in e.NewItems)
                        {
                            // 根据寄存器的地址设置不同的属性更新
                            switch (register.Address)
                            {
                                case 0: // T轴错误代码
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value))
                                        {
                                            OnPropertyChanged(nameof(TAxisErrorInfo));
                                        }
                                    };
                                    break;

                                case 1: // R轴错误代码
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value))
                                        {
                                            OnPropertyChanged(nameof(RAxisErrorInfo));
                                        }
                                    };
                                    break;

                                case 2: // Z轴错误代码
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value))
                                        {
                                            OnPropertyChanged(nameof(ZAxisErrorInfo));
                                        }
                                    };
                                    break;

                                case 3: // T轴步进值
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value) || args.PropertyName == nameof(ModbusRegister.Combinevalue))
                                        {
                                            OnPropertyChanged(nameof(TAxisStep));
                                            OnPropertyChanged(nameof(TAxisDegree));
                                        }
                                    };
                                    break;

                                case 5: // R轴步进值
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value) || args.PropertyName == nameof(ModbusRegister.Combinevalue))
                                        {
                                            OnPropertyChanged(nameof(RAxisStep));
                                            OnPropertyChanged(nameof(RAxisLength));
                                        }
                                    };
                                    break;

                                case 7: // Z轴步进值
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value) || args.PropertyName == nameof(ModbusRegister.Combinevalue))
                                        {
                                            OnPropertyChanged(nameof(ZAxisStep));
                                            OnPropertyChanged(nameof(ZAxisHeight));
                                        }
                                    };
                                    break;

                                case 9: // PinSearchP1_H
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value))
                                        {
                                            OnPropertyChanged(nameof(PinSearchP1_H));
                                            OnPropertyChanged(nameof(PinSearchP1Value));
                                        }
                                    };
                                    break;

                                case 10: // PinSearchP1_L
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value))
                                        {
                                            OnPropertyChanged(nameof(PinSearchP1_L));
                                            OnPropertyChanged(nameof(PinSearchP1Value));
                                        }
                                    };
                                    break;

                                case 11: // PinSearchP2_H
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value))
                                        {
                                            OnPropertyChanged(nameof(PinSearchP2_H));
                                            OnPropertyChanged(nameof(PinSearchP2Value));
                                        }
                                    };
                                    break;

                                case 12: // PinSearchP2_L
                                    register.PropertyChanged += (sender, args) =>
                                    {
                                        if (args.PropertyName == nameof(ModbusRegister.Value))
                                        {
                                            OnPropertyChanged(nameof(PinSearchP2_L));
                                            OnPropertyChanged(nameof(PinSearchP2Value));
                                        }
                                    };
                                    break;
                            }
                        }
                    }
                };
            }

            // 为现有的寄存器设置监听
            if (RobotAlarmRegisters.Count > 0)
            {
                RobotAlarmRegisters[0].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value))
                    {
                        OnPropertyChanged(nameof(TAxisErrorInfo));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 1)
            {
                RobotAlarmRegisters[1].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value))
                    {
                        OnPropertyChanged(nameof(RAxisErrorInfo));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 2)
            {
                RobotAlarmRegisters[2].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value))
                    {
                        OnPropertyChanged(nameof(ZAxisErrorInfo));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 3)
            {
                RobotAlarmRegisters[3].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value) || e.PropertyName == nameof(ModbusRegister.Combinevalue))
                    {
                        OnPropertyChanged(nameof(TAxisStep));
                        OnPropertyChanged(nameof(TAxisDegree));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 5)
            {
                RobotAlarmRegisters[5].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value) || e.PropertyName == nameof(ModbusRegister.Combinevalue))
                    {
                        OnPropertyChanged(nameof(RAxisStep));
                        OnPropertyChanged(nameof(RAxisLength));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 7)
            {
                RobotAlarmRegisters[7].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value) || e.PropertyName == nameof(ModbusRegister.Combinevalue))
                    {
                        OnPropertyChanged(nameof(ZAxisStep));
                        OnPropertyChanged(nameof(ZAxisHeight));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 9)
            {
                RobotAlarmRegisters[9].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value))
                    {
                        OnPropertyChanged(nameof(PinSearchP1_H));
                        OnPropertyChanged(nameof(PinSearchP1Value));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 10)
            {
                RobotAlarmRegisters[10].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value))
                    {
                        OnPropertyChanged(nameof(PinSearchP1_L));
                        OnPropertyChanged(nameof(PinSearchP1Value));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 11)
            {
                RobotAlarmRegisters[11].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value))
                    {
                        OnPropertyChanged(nameof(PinSearchP2_H));
                        OnPropertyChanged(nameof(PinSearchP2Value));
                    }
                };
            }

            if (RobotAlarmRegisters.Count > 12)
            {
                RobotAlarmRegisters[12].PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ModbusRegister.Value))
                    {
                        OnPropertyChanged(nameof(PinSearchP2_L));
                        OnPropertyChanged(nameof(PinSearchP2Value));
                    }
                };
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mcuCmdService">MCU命令服务</param>
        public UiViewModel(IS200McuCmdService mcuCmdService)
        {
            McuCmdService = mcuCmdService;

            // 初始化电机告警信息解析器
            try
            {
                string configPath = "Configs/AlarmInfo/MotorAlarmInfo.json";

                // 使用配置辅助类获取配置文件路径
                configPath = App.ConfigHelper.GetConfigFilePath(configPath);

                // 检查文件是否存在
                if (!File.Exists(configPath))
                {
                    _logger?.Error($"电机告警信息配置文件不存在: {configPath}");
                    throw new FileNotFoundException($"找不到电机告警信息配置文件: {configPath}");
                }

                _logger?.Info($"初始化电机告警信息解析器，配置文件路径: {configPath}");
                _motorAlarmInfoParser = new MotorAlarmInfoParser(configPath);

                // 设置错误提示提供器为自身的GetMotorErrorTooltip方法
                _logger?.Info("电机告警信息解析器初始化成功");
            }
            catch (Exception ex)
            {
                _logger?.Error($"初始化电机告警信息解析器失败: {ex.Message}", ex);
            }

            // 初始化寄存器属性变化处理器
            InitializeRegisterPropertyChangeHandlers();
        }

        /// <summary>
        /// 刷新所有设备线圈支持状态
        /// </summary>
        public void RefreshCoilSupportStatus()
        {
            OnPropertyChanged(nameof(IsShuttleCoilReadingSupported));
            OnPropertyChanged(nameof(IsRobotCoilReadingSupported));
            OnPropertyChanged(nameof(IsChaCoilReadingSupported));
            OnPropertyChanged(nameof(IsChbCoilReadingSupported));
        }

        /// <summary>
        /// 启动线圈监控
        /// </summary>
        public void StartCoilsMonitoring()
        {
            McuCmdService.StartCoilsMonitoring();
        }

        /// <summary>
        /// 停止线圈监控
        /// </summary>
        public void StopCoilsMonitoring()
        {
            McuCmdService.StopCoilsMonitoring();
        }

        /// <summary>
        /// 尝试将文本设置到剪贴板，包含重试逻辑
        /// </summary>
        /// <param name="text">要复制到剪贴板的文本</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试间隔（毫秒）</param>
        /// <returns>是否成功</returns>
        private bool TrySetClipboardText(string text, int maxRetries = 5, int retryDelayMs = 100)
        {
            if (string.IsNullOrEmpty(text))
            {
                _logger?.Warn("尝试复制空文本到剪贴板");
                return false;
            }

            Exception lastException = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    if (attempt > 0)
                    {
                        _logger?.Debug($"尝试复制到剪贴板，第 {attempt} 次重试");
                    }

                    // 在UI线程上执行操作
                    if (Application.Current.Dispatcher.CheckAccess())
                    {
                        Clipboard.SetText(text);
                    }
                    else
                    {
                        Application.Current.Dispatcher.Invoke(() => Clipboard.SetText(text));
                    }

                    _logger?.Debug("成功复制到剪贴板");
                    return true;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _logger?.Debug($"复制到剪贴板失败，重试 {attempt}/{maxRetries}: {ex.Message}");

                    // 如果这不是最后一次尝试，则等待一段时间再重试
                    if (attempt < maxRetries)
                    {
                        Thread.Sleep(retryDelayMs);
                    }
                }
            }

            // 所有重试都失败
            _logger?.Error($"多次尝试后复制到剪贴板失败: {lastException?.Message}", lastException);
            return false;
        }

        /// <summary>
        /// 复制当前轴值到剪贴板（异步版本）
        /// ✅ 优化报告修复：使用异步版本避免UI线程阻塞
        /// </summary>
        /// <param name="text">要复制的文本</param>
        [RelayCommand]
        private async Task CopyCurrentAxisValue(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                StatusBarInfo = "无法复制空文本";
                HcGrowlExtensions.Warning(StatusBarInfo);
                _logger?.Warn($"尝试复制空文本");
                return;
            }

            try
            {
                if (await TrySetClipboardTextAsync(text))
                {
                    StatusBarInfo = $"已复制值 {text} 到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info($"成功复制值 {text} 到剪贴板");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Error(StatusBarInfo);
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制到剪贴板失败: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error($"复制错误信息到剪贴板失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 复制所有轴位置到剪贴板（异步版本）
        /// ✅ 优化报告修复：使用异步版本避免UI线程阻塞
        /// </summary>
        [RelayCommand]
        private async Task CopyAllAxisPositions()
        {
            try
            {
                // 使用优化的RTZ轴位置访问接口获取格式化字符串
                var allValues = await GetRTZPositionSimpleText();

                // 复制到剪贴板
                if (await TrySetClipboardTextAsync(allValues))
                {
                    StatusBarInfo = $"已复制所有轴位置 {allValues} 到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info($"成功复制所有轴位置到剪贴板: {allValues}");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Warn("复制所有轴位置到剪贴板失败");
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制所有轴位置到剪贴板时发生错误: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error("复制所有轴位置到剪贴板时发生错误", ex);
            }
        }

        /// <summary>
        /// 复制所有Pin Search位置值到剪贴板（异步版本）
        /// ✅ 优化报告修复：使用异步版本避免UI线程阻塞
        /// </summary>
        [RelayCommand]
        private async Task CopyAllPinSearchValues()
        {
            try
            {
                // 获取Pin Search的位置值，使用整数格式
                var p1Value = PinSearchP1Value.ToString();
                var p2Value = PinSearchP2Value.ToString();

                // 组合成格式化的字符串
                var allValues = $"P1: {p1Value}, P2: {p2Value}";

                // 复制到剪贴板
                if (await TrySetClipboardTextAsync(allValues))
                {
                    StatusBarInfo = $"已复制所有Pin Search值 {allValues} 到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info($"成功复制所有Pin Search值到剪贴板: {allValues}");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Warn("复制所有Pin Search位置值到剪贴板失败");
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制所有Pin Search位置值到剪贴板时发生错误: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error("复制所有Pin Search位置值到剪贴板时发生错误", ex);
            }
        }

        /// <summary>
        /// 复制当前轴错误信息到剪贴板
        /// </summary>
        /// <param name="parameters">包含错误代码和轴名称的参数</param>
        [RelayCommand]
        private void CopyCurrentErrorInfo(object parameters)
        {
            try
            {
                if (parameters is not object[] args || args.Length < 2 ||
                    args[0] is not string errorCode || args[1] is not string axisName)
                {
                    StatusBarInfo = "参数错误，无法复制";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Warn($"复制错误信息参数无效: parameters={parameters}");
                    return;
                }

                if (string.IsNullOrEmpty(errorCode) || errorCode == "0x0000")
                {
                    StatusBarInfo = "当前无错误信息可复制";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Info($"尝试复制空错误信息: axisName={axisName}, errorCode={errorCode}");
                    return;
                }

                string errorInfo = GetMotorErrorTooltip(errorCode.Replace("0x", ""));
                if (string.IsNullOrEmpty(errorInfo))
                {
                    StatusBarInfo = $"未找到 {axisName} 对应的错误信息";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Warn($"未找到错误信息: axisName={axisName}, errorCode={errorCode}");
                    return;
                }

                // 组织格式化的信息
                string formattedInfo = $"{axisName}错误 {errorCode}：\r\n{errorInfo}";

                if (TrySetClipboardText(formattedInfo))
                {
                    StatusBarInfo = $"已复制{axisName}错误信息到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info($"成功复制错误信息到剪贴板: axisName={axisName}, errorCode={errorCode}");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Error(StatusBarInfo);
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制到剪贴板失败: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error($"复制错误信息到剪贴板失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 复制所有轴的错误信息到剪贴板
        /// </summary>
        [RelayCommand]
        private void CopyAllAxisErrorInfo()
        {
            try
            {
                var builder = new System.Text.StringBuilder();
                bool hasError = false;

                // 获取T轴错误信息
                if (RobotAlarmRegisters.Count > 0 && RobotAlarmRegisters[0].Value != 0)
                {
                    string tErrorCode = RobotAlarmRegisters[0].Value.ToString("X4");
                    string tErrorInfo = GetMotorErrorTooltip(tErrorCode);
                    builder.AppendLine($"T轴错误(0x{tErrorCode})：");
                    builder.AppendLine(tErrorInfo);
                    builder.AppendLine();
                    hasError = true;
                    _logger?.Debug($"获取到T轴错误信息: errorCode=0x{tErrorCode}");
                }

                // 获取R轴错误信息
                if (RobotAlarmRegisters.Count > 1 && RobotAlarmRegisters[1].Value != 0)
                {
                    string rErrorCode = RobotAlarmRegisters[1].Value.ToString("X4");
                    string rErrorInfo = GetMotorErrorTooltip(rErrorCode);
                    builder.AppendLine($"R轴错误(0x{rErrorCode})：");
                    builder.AppendLine(rErrorInfo);
                    builder.AppendLine();
                    hasError = true;
                    _logger?.Debug($"获取到R轴错误信息: errorCode=0x{rErrorCode}");
                }

                // 获取Z轴错误信息
                if (RobotAlarmRegisters.Count > 2 && RobotAlarmRegisters[2].Value != 0)
                {
                    string zErrorCode = RobotAlarmRegisters[2].Value.ToString("X4");
                    string zErrorInfo = GetMotorErrorTooltip(zErrorCode);
                    builder.AppendLine($"Z轴错误(0x{zErrorCode})：");
                    builder.AppendLine(zErrorInfo);
                    hasError = true;
                    _logger?.Debug($"获取到Z轴错误信息: errorCode=0x{zErrorCode}");
                }

                if (!hasError)
                {
                    StatusBarInfo = "当前无错误信息可复制";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Info("尝试复制错误信息时发现无错误");
                    return;
                }

                if (TrySetClipboardText(builder.ToString()))
                {
                    StatusBarInfo = "已复制所有轴错误信息到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info("成功复制所有轴错误信息到剪贴板");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Error(StatusBarInfo);
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制到剪贴板失败: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error($"复制所有轴错误信息到剪贴板失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取电机错误信息
        /// </summary>
        /// <param name="code">错误代码</param>
        /// <returns>格式化的错误信息</returns>
        public string GetMotorErrorInfo(string code)
        {
            try
            {
                _logger?.Debug($"开始获取电机错误信息: code={code}");
                if (_motorAlarmInfoParser != null && _motorAlarmInfoParser.TryGetAlarmInfo(code, out var motorAlarmInfo))
                {
                    var causeText = motorAlarmInfo.Cause != null && motorAlarmInfo.Cause.Count > 0
                        ? string.Join("; ", motorAlarmInfo.Cause)
                        : "未知原因";

                    var disposeText = motorAlarmInfo.Dispose != null && motorAlarmInfo.Dispose.Count > 0
                        ? string.Join("; ", motorAlarmInfo.Dispose)
                        : "无处理方法";

                    var resetInfo = motorAlarmInfo.AlarmResetClear ? "可通过复位清除" : "不可通过复位清除";

                    var result = $"错误: [{motorAlarmInfo.Code}] {motorAlarmInfo.Kind}\n原因: {causeText}\n处理: {disposeText}\n{resetInfo}";
                    _logger?.Debug($"成功获取电机错误信息: code={code}, kind={motorAlarmInfo.Kind}");
                    return result;
                }
                _logger?.Warn($"未找到对应的电机错误信息: code={code}");
                return $"未知电机错误: {code}";
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取电机错误信息失败: {ex.Message}", ex);
                return $"错误信息查询失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取电机错误工具提示信息
        /// </summary>
        /// <param name="hexCode">错误代码（十六进制）</param>
        /// <returns>格式化的工具提示内容</returns>
        public string GetMotorErrorTooltip(string hexCode)
        {
            try
            {
                _logger?.Debug($"开始解析错误代码: {hexCode}");

                // 移除可能的前缀
                hexCode = hexCode.Replace("0x", "").Trim();

                // 确保大写
                hexCode = hexCode.ToUpper();

                _logger?.Debug($"格式化后的错误代码: {hexCode}");

                if (_motorAlarmInfoParser != null && _motorAlarmInfoParser.TryGetAlarmInfo(hexCode, out var motorAlarmInfo))
                {
                    _logger?.Debug($"成功解析错误代码: {hexCode}, 错误类型: {motorAlarmInfo.Kind}");

                    var sb = new System.Text.StringBuilder();
                    sb.AppendLine($"错误: [{motorAlarmInfo.Code}] {motorAlarmInfo.Kind}");

                    // 添加原因
                    if (motorAlarmInfo.Cause != null && motorAlarmInfo.Cause.Count > 0)
                    {
                        sb.AppendLine("\n可能原因:");
                        foreach (var cause in motorAlarmInfo.Cause)
                        {
                            sb.AppendLine($"• {cause}");
                        }
                    }

                    // 添加处理方法
                    if (motorAlarmInfo.Dispose != null && motorAlarmInfo.Dispose.Count > 0)
                    {
                        sb.AppendLine("\n处理方法:");
                        foreach (var dispose in motorAlarmInfo.Dispose)
                        {
                            sb.AppendLine($"• {dispose}");
                        }
                    }

                    // 添加是否可复位信息
                    sb.AppendLine($"\n{(motorAlarmInfo.AlarmResetClear ? "✓ 可通过复位清除" : "✗ 不可通过复位清除")}");

                    var result = sb.ToString();
                    _logger?.Debug($"生成的错误提示: {result}");
                    return result;
                }
                else
                {
                    if (_motorAlarmInfoParser == null)
                    {
                        _logger?.Error("电机告警信息解析器未初始化");
                    }
                    else
                    {
                        _logger?.Warn($"未找到匹配的错误信息: {hexCode}");
                    }
                    return $"未找到匹配的错误信息: {hexCode}";
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取电机错误工具提示失败: {ex.Message}", ex);
                return $"错误信息查询失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取可通过复位清除的告警列表
        /// </summary>
        /// <returns>可复位告警列表</returns>
        public IEnumerable<MotorAlarmInfo> GetResetClearableAlarms()
        {
            try
            {
                if (_motorAlarmInfoParser != null)
                {
                    return _motorAlarmInfoParser.GetResetClearableAlarmInfos();
                }
                return Enumerable.Empty<MotorAlarmInfo>();
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取可复位告警列表失败: {ex.Message}", ex);
                return Enumerable.Empty<MotorAlarmInfo>();
            }
        }

        /// <summary>
        /// 尝试将文本设置到剪贴板，包含重试逻辑（异步版本）
        /// ✅ 优化报告修复：使用异步延迟和Dispatcher调用，避免UI线程阻塞
        /// </summary>
        /// <param name="text">要复制到剪贴板的文本</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试间隔（毫秒）</param>
        /// <returns>是否成功</returns>
        private async Task<bool> TrySetClipboardTextAsync(string text, int maxRetries = 5, int retryDelayMs = 100)
        {
            if (string.IsNullOrEmpty(text))
            {
                _logger?.Warn("尝试复制空文本到剪贴板");
                return false;
            }

            Exception lastException = null;

            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    // 使用异步Dispatcher调用，避免死锁
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        Clipboard.SetText(text);
                    });

                    _logger?.Debug($"成功复制文本到剪贴板: {text}");
                    return true;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _logger?.Warn($"复制到剪贴板失败 (尝试 {i + 1}/{maxRetries}): {ex.Message}");

                    if (i < maxRetries - 1)
                    {
                        await Task.Delay(retryDelayMs);  // 异步延迟，不阻塞UI线程
                    }
                }
            }

            // 所有重试都失败
            _logger?.Error($"多次尝试后复制到剪贴板失败: {lastException?.Message}", lastException);
            return false;
        }
    }
}