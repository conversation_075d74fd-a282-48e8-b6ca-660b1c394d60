using System;
using log4net;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// RTZ轴位置访问示例
    /// 演示如何使用S200McuCmdService中新增的RTZ轴位置访问属性
    /// </summary>
    public class RTZAxisPositionAccessExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RTZAxisPositionAccessExample));

        /// <summary>
        /// 演示RTZ轴位置访问的基本用法
        /// </summary>
        public static void DemonstrateBasicUsage()
        {
            try
            {
                _logger.Info("=== RTZ轴位置访问示例 ===");

                // 获取S200McuCmdService实例
                var mcuService = S200McuCmdService.Instance;

                // 检查RTZ轴位置数据是否有效
                if (!mcuService.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，请确保Robot设备已连接并启动了报警监控");
                    return;
                }

                _logger.Info("RTZ轴位置数据有效，开始读取位置信息...");

                // 1. 获取单独的轴位置（步进值）
                int tAxisStep = mcuService.CurrentTAxisStep;
                int rAxisStep = mcuService.CurrentRAxisStep;
                int zAxisStep = mcuService.CurrentZAxisStep;

                _logger.Info($"T轴步进位置: {tAxisStep} steps");
                _logger.Info($"R轴步进位置: {rAxisStep} steps");
                _logger.Info($"Z轴步进位置: {zAxisStep} steps");

                // 2. 获取物理单位值
                double tAxisDegree = mcuService.CurrentTAxisDegree;
                double rAxisLength = mcuService.CurrentRAxisLength;
                double zAxisHeight = mcuService.CurrentZAxisHeight;

                _logger.Info($"T轴角度: {tAxisDegree:F2}°");
                _logger.Info($"R轴长度: {rAxisLength:F2}mm");
                _logger.Info($"Z轴高度: {zAxisHeight:F2}mm");

                // 3. 获取组合信息
                var (t, r, z) = mcuService.GetCurrentRTZSteps();
                _logger.Info($"RTZ轴步进值组合: T={t}, R={r}, Z={z}");

                var (tDeg, rLen, zHeight) = mcuService.GetCurrentRTZPhysicalValues();
                _logger.Info($"RTZ轴物理值组合: T={tDeg:F2}°, R={rLen:F2}mm, Z={zHeight:F2}mm");

                _logger.Info("=== RTZ轴位置访问示例完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"RTZ轴位置访问示例执行失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示实时监控RTZ轴位置变化
        /// </summary>
        public static void DemonstrateRealTimeMonitoring()
        {
            try
            {
                _logger.Info("=== RTZ轴位置实时监控示例 ===");

                var mcuService = S200McuCmdService.Instance;

                if (!mcuService.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，无法进行实时监控");
                    return;
                }

                // 记录初始位置
                var initialSteps = mcuService.GetCurrentRTZSteps();
                _logger.Info($"初始RTZ轴位置: T={initialSteps.TAxisStep}, R={initialSteps.RAxisStep}, Z={initialSteps.ZAxisStep}");

                // 模拟实时监控（实际使用中可以用定时器）
                for (int i = 0; i < 5; i++)
                {
                    System.Threading.Thread.Sleep(1000); // 等待1秒

                    var currentSteps = mcuService.GetCurrentRTZSteps();
                    var currentPhysical = mcuService.GetCurrentRTZPhysicalValues();

                    _logger.Info($"第{i + 1}次读取:");
                    _logger.Info($"  步进值: T={currentSteps.TAxisStep}, R={currentSteps.RAxisStep}, Z={currentSteps.ZAxisStep}");
                    _logger.Info($"  物理值: T={currentPhysical.TAxisDegree:F2}°, R={currentPhysical.RAxisLength:F2}mm, Z={currentPhysical.ZAxisHeight:F2}mm");

                    // 检查位置是否发生变化
                    if (currentSteps.TAxisStep != initialSteps.TAxisStep)
                    {
                        _logger.Info($"T轴位置发生变化: {initialSteps.TAxisStep} -> {currentSteps.TAxisStep}");
                    }
                    if (currentSteps.RAxisStep != initialSteps.RAxisStep)
                    {
                        _logger.Info($"R轴位置发生变化: {initialSteps.RAxisStep} -> {currentSteps.RAxisStep}");
                    }
                    if (currentSteps.ZAxisStep != initialSteps.ZAxisStep)
                    {
                        _logger.Info($"Z轴位置发生变化: {initialSteps.ZAxisStep} -> {currentSteps.ZAxisStep}");
                    }
                }

                _logger.Info("=== RTZ轴位置实时监控示例完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"RTZ轴位置实时监控示例执行失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示RTZ轴位置的转换公式验证
        /// </summary>
        public static void DemonstrateConversionFormulas()
        {
            try
            {
                _logger.Info("=== RTZ轴位置转换公式验证示例 ===");

                var mcuService = S200McuCmdService.Instance;

                if (!mcuService.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，无法进行转换公式验证");
                    return;
                }

                // 获取当前步进值
                int tAxisStep = mcuService.CurrentTAxisStep;
                int rAxisStep = mcuService.CurrentRAxisStep;
                int zAxisStep = mcuService.CurrentZAxisStep;

                _logger.Info("当前步进值:");
                _logger.Info($"  T轴: {tAxisStep} steps");
                _logger.Info($"  R轴: {rAxisStep} steps");
                _logger.Info($"  Z轴: {zAxisStep} steps");

                // 手动计算物理值并与属性值对比
                double manualTAxisDegree = tAxisStep / 100000.0 * 360.0;
                double manualRAxisLength = Math.Sin((rAxisStep / 50000.0) * (Math.PI / 180) * 360) * 2 * 208.96;
                double manualZAxisHeight = zAxisStep / 1000.0 * 5.0;

                double propertyTAxisDegree = mcuService.CurrentTAxisDegree;
                double propertyRAxisLength = mcuService.CurrentRAxisLength;
                double propertyZAxisHeight = mcuService.CurrentZAxisHeight;

                _logger.Info("转换公式验证:");
                _logger.Info($"  T轴角度 - 手动计算: {manualTAxisDegree:F6}°, 属性值: {propertyTAxisDegree:F6}°, 匹配: {Math.Abs(manualTAxisDegree - propertyTAxisDegree) < 0.000001}");
                _logger.Info($"  R轴长度 - 手动计算: {manualRAxisLength:F6}mm, 属性值: {propertyRAxisLength:F6}mm, 匹配: {Math.Abs(manualRAxisLength - propertyRAxisLength) < 0.000001}");
                _logger.Info($"  Z轴高度 - 手动计算: {manualZAxisHeight:F6}mm, 属性值: {propertyZAxisHeight:F6}mm, 匹配: {Math.Abs(manualZAxisHeight - propertyZAxisHeight) < 0.000001}");

                _logger.Info("=== RTZ轴位置转换公式验证示例完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"RTZ轴位置转换公式验证示例执行失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static void RunAllExamples()
        {
            _logger.Info("开始运行RTZ轴位置访问所有示例...");

            DemonstrateBasicUsage();
            Console.WriteLine();

            DemonstrateRealTimeMonitoring();
            Console.WriteLine();

            DemonstrateConversionFormulas();

            _logger.Info("所有RTZ轴位置访问示例运行完成");
        }
    }
}
