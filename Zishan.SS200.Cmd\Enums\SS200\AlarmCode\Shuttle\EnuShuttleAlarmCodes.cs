﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Shuttle
{
    /// <summary>
    /// Shuttle 报警代码枚举类型
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuShuttleAlarmCodes
    {
        /// <summary>
        /// alarm 1 system busy command reject
        /// </summary>
        [Description("alarm 1 system busy command reject")]
        SA1 = 0,

        /// <summary>
        /// alarm 2 system alarm command reject
        /// </summary>
        [Description("alarm 2 system alarm command reject")]
        SA2 = 1,

        /// <summary>
        /// alarm 3 cassette nest move time out
        /// </summary>
        [Description("alarm 3 cassette nest move time out")]
        SA3 = 2,

        /// <summary>
        /// alarm 4 cassette nest move speed too high
        /// </summary>
        [Description("alarm 4 cassette nest move speed too high")]
        SA4 = 3,

        /// <summary>
        /// alarm 5 cassette nest position condition failure
        /// </summary>
        [Description("alarm 5 cassette nest position condition failure")]
        SA5 = 4,

        /// <summary>
        /// alarm 6 shuttle move time out
        /// </summary>
        [Description("alarm 6 shuttle move time out")]
        SA6 = 5,

        /// <summary>
        /// alarm 7 shuttle move too fast
        /// </summary>
        [Description("alarm 7 shuttle move too fast")]
        SA7 = 6,

        /// <summary>
        /// alarm 8 shuttle up down position condition failure
        /// </summary>
        [Description("alarm 8 shuttle up down position condition failure")]
        SA8 = 7,

        /// <summary>
        /// alarm 9 shuttle rotate time out
        /// </summary>
        [Description("alarm 9 shuttle rotate time out")]
        SA9 = 8,

        /// <summary>
        /// alarm 10 shuttle rotate position condition failure
        /// </summary>
        [Description("alarm 10 shuttle rotate position condition failure")]
        SA10 = 9,

        /// <summary>
        /// alarm 11 cassette door move time out
        /// </summary>
        [Description("alarm 11 cassette door move time out")]
        SA11 = 10,

        /// <summary>
        /// alarm 12 cassette door move too fast
        /// </summary>
        [Description("alarm 12 cassette door move too fast")]
        SA12 = 11,

        /// <summary>
        /// alarm 13 cassette door position condition failure
        /// </summary>
        [Description("alarm 13 cassette door position condition failure")]
        SA13 = 12,

        /// <summary>
        /// alarm 14 open CV time out
        /// </summary>
        [Description("alarm 14 open CV time out")]
        SA14 = 13,

        /// <summary>
        /// alarm 15 CV position status failure
        /// </summary>
        [Description("alarm 15 CV position status failure")]
        SA15 = 14,

        /// <summary>
        /// alarm 16 close CV time out
        /// </summary>
        [Description("alarm 16 close CV time out")]
        SA16 = 15,

        /// <summary>
        /// alarm 17 open XV time out
        /// </summary>
        [Description("alarm 17 open XV time out")]
        SA17 = 16,

        /// <summary>
        /// alarm 18 XV position status failure
        /// </summary>
        [Description("alarm 18 XV position status failure")]
        SA18 = 17,

        /// <summary>
        /// alarm 19 close XV time out
        /// </summary>
        [Description("alarm 19 close XV time out")]
        SA19 = 18,

        /// <summary>
        /// shuttle & loadlock pressure delta out of range
        /// </summary>
        [Description("shuttle & loadlock pressure delta out of range")]
        SA20 = 19,

        /// <summary>
        /// chamber pressure review is no, pls confirm to continue
        /// </summary>
        [Description("chamber pressure review is no, pls confirm to continue")]
        SA21 = 20,

        /// <summary>
        /// shuttle position unknown, can not motion cassette door
        /// </summary>
        [Description("shuttle position unknown, can not motion cassette door")]
        SA22 = 21,

        /// <summary>
        /// shuttle position unknown, can not swap cassette
        /// </summary>
        [Description("shuttle position unknown, can not swap cassette")]
        SA23 = 22,

        /// <summary>
        /// cassette nest not at home, shuttle motion failure
        /// </summary>
        [Description("cassette nest not at home, shuttle motion failure")]
        SA24 = 23,

        /// <summary>
        /// chamber pressure review is no, return cassette reject
        /// </summary>
        [Description("chamber pressure review is no, return cassette reject")]
        SA25 = 24,

        /// <summary>
        /// shuttle vacuum switch show foreline is no vacuum,can not open CV
        /// </summary>
        [Description("shuttle vacuum switch show foreline is no vacuum,can not open CV")]
        SA26 = 25,

        /// <summary>
        /// cassette door is not close, can not open shuttle vacuum valve
        /// </summary>
        [Description("cassette door is not close, can not open shuttle vacuum valve")]
        SA27 = 26,

        /// <summary>
        /// shuttle pump down time out
        /// </summary>
        [Description("shuttle pump down time out")]
        SA28 = 27,

        /// <summary>
        /// shuttle backfill time out
        /// </summary>
        [Description("shuttle backfill time out")]
        SA29 = 28,

        /// <summary>
        /// CHA trigger status is alarm, can not pump down loadlock
        /// </summary>
        [Description("CHA trigger status is alarm, can not pump down loadlock")]
        SA30 = 29,

        /// <summary>
        /// CHB trigger status is alarm, can not pump down loadlock
        /// </summary>
        [Description("CHB trigger status is alarm, can not pump down loadlock")]
        SA31 = 30,

        /// <summary>
        /// CHA run status is not at idle status, can not pump down loadlock
        /// </summary>
        [Description("CHA run status is not at idle status, can not pump down loadlock")]
        SA32 = 31,

        /// <summary>
        /// CHB run status is not at idle status, can not pump down loadlock
        /// </summary>
        [Description("CHB run status is not at idle status, can not pump down loadlock")]
        SA33 = 32,

        /// <summary>
        /// CHA/B pressure>setpoint, and slit door is close, can not pump down loadlock
        /// </summary>
        [Description("CHA/B pressure>setpoint, and slit door is close, can not pump down loadlock")]
        SA34 = 33,

        /// <summary>
        /// CHA pressure>setpoint, and slit door is close, can not pump down loadlock
        /// </summary>
        [Description("CHA pressure>setpoint, and slit door is close, can not pump down loadlock")]
        SA35 = 34,

        /// <summary>
        /// CHB pressure>setpoint, and slit door is close, can not pump down loadlock
        /// </summary>
        [Description("CHB pressure>setpoint, and slit door is close, can not pump down loadlock")]
        SA36 = 35,

        /// <summary>
        /// system trigger status is alarm, command reject
        /// </summary>
        [Description("system trigger status is alarm, command reject")]
        SA37 = 36,

        /// <summary>
        /// loadlock run status is not idle, command reject
        /// </summary>
        [Description("loadlock run status is not idle, command reject")]
        SA38 = 37,

        /// <summary>
        /// shuttle pressure is not at ATM status, backfill loadlock failure
        /// </summary>
        [Description("shuttle pressure is not at ATM status, backfill loadlock failure")]
        SA39 = 38
    }
}