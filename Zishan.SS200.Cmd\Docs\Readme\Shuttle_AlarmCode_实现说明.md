# Shuttle AlarmCode 实现说明

## 概述

本文档说明了在 `SS200InterLockMain.cs` 中添加 Shuttle 报警代码支持的实现过程和使用方法。

## 问题描述

原有的 `SS200InterLockMain.Instance.AlarmCode` 只支持 Robot 和 Chamber 的报警代码访问：

```csharp
var alarm_robot = SS200InterLockMain.Instance.AlarmCode.Robot.RA1_SystemBusyReject.Content;
var alarm_CHA = SS200InterLockMain.Instance.AlarmCode.ChamberA.PAC1_SystemAbnormalReject.Content;
var alarm_CHB = SS200InterLockMain.Instance.AlarmCode.ChamberB.PAC1_SystemAbnormalReject.Content;
```

但是缺少 Shuttle 子系统的报警代码支持：
```csharp
// 这行代码会报错，因为没有 Shuttle 属性
var alarm_Shuttle = SS200InterLockMain.Instance.AlarmCode.Shuttle.SA1_SystemBusyReject.Content;
```

## 实现方案

### 1. 添加必要的 using 语句

在 `SS200InterLockMain.cs` 文件顶部添加了 Shuttle 相关的命名空间引用：

```csharp
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Shuttle;
using Zishan.SS200.Cmd.Config.SS200.AlarmCode.Shuttle;
```

### 2. 创建 ShuttleAlarmAccessor 类

参照 `RobotAlarmAccessor` 和 `ChamberAlarmAccessor` 的实现模式，创建了 `ShuttleAlarmAccessor` 类：

```csharp
/// <summary>
/// Shuttle报警访问器
/// </summary>
public class ShuttleAlarmAccessor
{
    private readonly ShuttleErrorCodesProvider _provider;
    private readonly ConcurrentDictionary<string, AlarmPropertyAccessor> _cache = new();

    public ShuttleAlarmAccessor()
    {
        _provider = ShuttleErrorCodesProvider.Instance;
    }

    /// <summary>
    /// SA1 - Shuttle system status is busy, command reject
    /// </summary>
    public AlarmPropertyAccessor SA1_SystemBusyReject =>
        GetOrCreateAccessor(EnuShuttleAlarmCodes.SA1);

    // ... 其他报警代码属性

    private AlarmPropertyAccessor GetOrCreateAccessor(EnuShuttleAlarmCodes alarmCode)
    {
        string key = alarmCode.ToString();
        return _cache.GetOrAdd(key, _ =>
        {
            var alarmItem = _provider.GetAlarmItemByCode(alarmCode);
            return alarmItem != null ? new AlarmPropertyAccessor(alarmItem) : null;
        });
    }
}
```

### 3. 更新 AlarmCodeAccessor 类

在 `AlarmCodeAccessor` 类中添加了 `Shuttle` 属性：

```csharp
public class AlarmCodeAccessor
{
    public RobotAlarmAccessor Robot { get; }
    public ChamberAlarmAccessor ChamberA { get; }
    public ChamberAlarmAccessor ChamberB { get; }
    public ShuttleAlarmAccessor Shuttle { get; }  // 新增

    public AlarmCodeAccessor()
    {
        Robot = new RobotAlarmAccessor();
        ChamberA = new ChamberAlarmAccessor();
        ChamberB = new ChamberAlarmAccessor();
        Shuttle = new ShuttleAlarmAccessor();  // 新增
    }
}
```

## 支持的 Shuttle 报警代码

`ShuttleAlarmAccessor` 类目前支持以下报警代码：

| 代码 | 属性名                           | 描述                        |
| ---- | -------------------------------- | --------------------------- |
| SA1  | SA1_SystemBusyReject             | Shuttle系统忙碌，指令被拒绝 |
| SA2  | SA2_SystemAlarmReject            | Shuttle系统报警，指令被拒绝 |
| SA3  | SA3_CassetteNestMoveTimeout      | 卡匣巢移动超时              |
| SA4  | SA4_CassetteNestSpeedTooHigh     | 卡匣巢移动速度过快          |
| SA5  | SA5_CassetteNestPositionFailure  | 卡匣巢位置条件失败          |
| SA6  | SA6_ShuttleMoveTimeout           | Shuttle移动超时             |
| SA7  | SA7_ShuttleMoveTooFast           | Shuttle移动过快             |
| SA8  | SA8_ShuttleUpDownPositionFailure | Shuttle上下位置条件失败     |
| SA9  | SA9_ShuttleRotateTimeout         | Shuttle旋转超时             |

## 使用方法

### 基本使用

```csharp
// 获取 SS200InterLockMain 实例
var controlCenter = SS200InterLockMain.Instance;

// 现在可以访问 Shuttle 报警代码了
var shuttleAlarm = controlCenter.AlarmCode.Shuttle.SA1_SystemBusyReject;

// 获取报警信息
string alarmCode = shuttleAlarm.Code;           // 报警代码：SA1
string englishDesc = shuttleAlarm.Content;      // 英文描述
string chineseDesc = shuttleAlarm.ChsContent;   // 中文描述
string alarmCause = shuttleAlarm.Cause;         // 报警原因
```

### 与其他子系统报警代码对比

```csharp
var controlCenter = SS200InterLockMain.Instance;

// 现在所有子系统的报警代码都可以统一访问
var alarm_robot = controlCenter.AlarmCode.Robot.RA1_SystemBusyReject.Content;
var alarm_CHA = controlCenter.AlarmCode.ChamberA.PAC1_SystemAbnormalReject.Content;
var alarm_CHB = controlCenter.AlarmCode.ChamberB.PAC1_SystemAbnormalReject.Content;
var alarm_Shuttle = controlCenter.AlarmCode.Shuttle.SA1_SystemBusyReject.Content;  // 新增支持
```

## 技术特点

1. **一致性设计**：Shuttle 报警访问器的实现完全遵循了 Robot 和 Chamber 报警访问器的设计模式
2. **缓存机制**：使用 `ConcurrentDictionary` 缓存报警属性访问器，提高性能
3. **延迟加载**：报警属性访问器采用延迟加载模式，只在首次访问时创建
4. **线程安全**：使用线程安全的集合类型，支持多线程环境
5. **配置驱动**：报警信息从 JSON 配置文件加载，支持动态更新

## 依赖关系

此实现依赖以下组件：

- `EnuShuttleAlarmCodes`：Shuttle 报警代码枚举
- `ShuttleErrorCodesProvider`：Shuttle 报警代码提供者
- `AlarmPropertyAccessor`：报警属性访问器基类
- `ShuttleErrorCodes.json`：Shuttle 报警代码配置文件

## 测试验证

可以使用以下文件来测试和验证 Shuttle 报警代码功能是否正常工作：

- **测试文件**：`Zishan.SS200.Cmd\Docs\Test\TestShuttleAlarmCode.cs` - 用于功能验证和单元测试
- **示例文件**：`Zishan.SS200.Cmd\Docs\Examples\ShuttleAlarmCodeExample.cs` - 演示使用方法和最佳实践

## 总结

通过添加 `ShuttleAlarmAccessor` 类和更新 `AlarmCodeAccessor` 类，成功实现了 Shuttle 子系统报警代码的统一访问支持，使得系统的报警代码访问接口更加完整和一致。
