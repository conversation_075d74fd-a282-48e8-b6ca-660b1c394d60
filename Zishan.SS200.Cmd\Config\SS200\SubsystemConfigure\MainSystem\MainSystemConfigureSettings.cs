using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.MainSystem;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.MainSystem
{
    /// <summary>
    /// 主系统配置设置类
    /// 用于统一访问主系统配置参数
    /// </summary>
    public class MainSystemConfigureSettings
    {
        private static readonly Lazy<MainSystemConfigureSettings> _instance =
            new Lazy<MainSystemConfigureSettings>(() => new MainSystemConfigureSettings());

        public static MainSystemConfigureSettings Instance => _instance.Value;

        private MainSystemConfigureSettings()
        {
            // 私有构造函数，防止外部实例化
        }

        #region 系统配置参数

        /// <summary>
        /// 传送室1晶圆尺寸 (inch)
        /// </summary>
        public int Shuttle1WaferSize =>
            MainSystemConfigParametersProvider.Instance.GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC1);

        /// <summary>
        /// 传送室2晶圆尺寸 (inch)
        /// </summary>
        public int Shuttle2WaferSize =>
            MainSystemConfigParametersProvider.Instance.GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC2);

        /// <summary>
        /// 腔室位置 (CHA / CHB / CHA and CHB)
        /// </summary>
        public string ChamberLocation =>
            MainSystemConfigParametersProvider.Instance.GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC3);

        /// <summary>
        /// 腔室A工艺类型 (ICP / CCP / ICP and CCP)
        /// </summary>
        public string ChamberAProcess =>
            MainSystemConfigParametersProvider.Instance.GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC4);

        /// <summary>
        /// 腔室B工艺类型 (ICP / CCP / ICP and CCP)
        /// </summary>
        public string ChamberBProcess =>
            MainSystemConfigParametersProvider.Instance.GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC5);

        /// <summary>
        /// 卡匣巢类型 (fixed/SMIF)
        /// </summary>
        public string CassetteNestType =>
            MainSystemConfigParametersProvider.Instance.GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC6);

        /// <summary>
        /// 狭缝门类型 (A/B)
        /// </summary>
        public string SlitDoorType =>
            MainSystemConfigParametersProvider.Instance.GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC7);

        /// <summary>
        /// 优先级生效时间 (min)
        /// </summary>
        public int PriorityEffectiveTime =>
            MainSystemConfigParametersProvider.Instance.GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC8);

        /// <summary>
        /// 映射功能是否启用
        /// </summary>
        public bool MappingFunctionEnabled =>
            MainSystemConfigParametersProvider.Instance.GetBoolSettingValue(EnuMainSystemConfigParameterCodes.SSC9);

        /// <summary>
        /// 跳过空槽位是否启用
        /// </summary>
        public bool SkipEmptySlotEnabled =>
            MainSystemConfigParametersProvider.Instance.GetBoolSettingValue(EnuMainSystemConfigParameterCodes.SSC10);

        /// <summary>
        /// 冷却室温度 (℃)
        /// </summary>
        public int CoolingChamberTemperature =>
            MainSystemConfigParametersProvider.Instance.GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC11);

        /// <summary>
        /// 腔室温度偏差 (℃)
        /// </summary>
        public int ChamberTemperatureDeviation =>
            MainSystemConfigParametersProvider.Instance.GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC12);

        /// <summary>
        /// 传输压力类型 (A/B)
        /// </summary>
        public string TransferPressureType =>
            MainSystemConfigParametersProvider.Instance.GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC13);

        /// <summary>
        /// 槽位产品顺序 (A/B)
        /// </summary>
        public string SlotProductOrder =>
            MainSystemConfigParametersProvider.Instance.GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC14);

        #endregion 系统配置参数

        /// <summary>
        /// 获取默认设置列表
        /// </summary>
        /// <returns>默认配置设置列表</returns>
        public static List<Models.SS200.SubsystemConfigure.ConfigureSetting> GetDefaultSettings()
        {
            var settings = new List<Models.SS200.SubsystemConfigure.ConfigureSetting>();

            // 添加默认设置
            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 1,
                Code = EnuMainSystemConfigParameterCodes.SSC1.ToString(),
                Description = "传送室1晶圆尺寸 (inch)",
                Value = 8,
                Unit = "inch"
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 2,
                Code = EnuMainSystemConfigParameterCodes.SSC2.ToString(),
                Description = "传送室2晶圆尺寸 (inch)",
                Value = 8,
                Unit = "inch"
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 3,
                Code = EnuMainSystemConfigParameterCodes.SSC3.ToString(),
                Description = "腔室位置 (CHA / CHB / CHA and CHB)",
                Value = 0,  // 使用整数表示字符串值
                Unit = ""
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 4,
                Code = EnuMainSystemConfigParameterCodes.SSC4.ToString(),
                Description = "腔室A工艺类型 (ICP / CCP / ICP and CCP)",
                Value = 0,  // 使用整数表示字符串值
                Unit = ""
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 5,
                Code = EnuMainSystemConfigParameterCodes.SSC5.ToString(),
                Description = "腔室B工艺类型 (ICP / CCP / ICP and CCP)",
                Value = 0,  // 使用整数表示字符串值
                Unit = ""
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 6,
                Code = EnuMainSystemConfigParameterCodes.SSC6.ToString(),
                Description = "卡匣巢类型 (fixed/SMIF)",
                Value = 0,  // 使用整数表示字符串值
                Unit = ""
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 7,
                Code = EnuMainSystemConfigParameterCodes.SSC7.ToString(),
                Description = "狭缝门类型 (A/B)",
                Value = 0,  // 使用整数表示字符串值
                Unit = ""
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 8,
                Code = EnuMainSystemConfigParameterCodes.SSC8.ToString(),
                Description = "优先级生效时间 (min)",
                Value = 30,
                Unit = "min"
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 9,
                Code = EnuMainSystemConfigParameterCodes.SSC9.ToString(),
                Description = "映射功能是否启用",
                Value = 1,  // 使用整数表示Y/N值，1表示Y，0表示N
                Unit = ""
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 10,
                Code = EnuMainSystemConfigParameterCodes.SSC10.ToString(),
                Description = "跳过空槽位是否启用",
                Value = 1,  // 使用整数表示Y/N值，1表示Y，0表示N
                Unit = ""
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 11,
                Code = EnuMainSystemConfigParameterCodes.SSC11.ToString(),
                Description = "冷却室温度 (℃)",
                Value = 25,
                Unit = "℃"
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 12,
                Code = EnuMainSystemConfigParameterCodes.SSC12.ToString(),
                Description = "腔室温度偏差 (℃)",
                Value = 5,
                Unit = "℃"
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 13,
                Code = EnuMainSystemConfigParameterCodes.SSC13.ToString(),
                Description = "传输压力类型 (A/B)",
                Value = 0,  // 使用整数表示字符串值
                Unit = ""
            });

            settings.Add(new Models.SS200.SubsystemConfigure.ConfigureSetting
            {
                Id = 14,
                Code = EnuMainSystemConfigParameterCodes.SSC14.ToString(),
                Description = "槽位产品顺序 (A/B)",
                Value = 0,  // 使用整数表示字符串值
                Unit = ""
            });

            return settings;
        }
    }
}