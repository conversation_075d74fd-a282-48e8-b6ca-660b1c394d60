# 动态Expander Header功能实现说明

## 功能概述

实现了Robot轴位置信息Expander的动态Header功能，根据展开状态显示不同的内容：

- **折叠状态（IsExpanded=False）**: 显示详细信息 `"Robot轴位置信息:【T轴：11 Step;R轴：22 Step;Z轴：33 PinSearch：Smooth：1000；Nose：1001】"`
- **展开状态（IsExpanded=True）**: 显示简单标题 `"Robot轴位置信息"`

## 实现细节

### 1. ViewModel属性添加

在 `RobotStatusPanelViewModel.cs` 中添加了以下属性：

```csharp
#region Robot轴位置信息Header

/// <summary>
/// 轴位置信息展开状态
/// </summary>
[ObservableProperty]
private bool _isAxisInfoExpanded = false;

/// <summary>
/// 动态轴位置信息Header
/// </summary>
public string AxisInfoHeader
{
    get
    {
        if (IsAxisInfoExpanded)
        {
            return "Robot轴位置信息";
        }
        else
        {
            return $"Robot轴位置信息:【T轴：{TAxisStep} Step;R轴：{RAxisStep} Step;Z轴：{ZAxisStep} PinSearch：Smooth：{PinSearchP1Value}；Nose：{PinSearchP2Value}】";
        }
    }
}

/// <summary>
/// IsAxisInfoExpanded属性变化时的处理方法
/// </summary>
partial void OnIsAxisInfoExpandedChanged(bool value)
{
    OnPropertyChanged(nameof(AxisInfoHeader));
}

#endregion Robot轴位置信息Header
```

### 2. 实时数据更新

在轴位置数据变化时自动更新Header显示：

```csharp
// T轴步进值变化时
OnPropertyChanged(nameof(AxisInfoHeader));

// R轴步进值变化时
OnPropertyChanged(nameof(AxisInfoHeader));

// Z轴步进值变化时
OnPropertyChanged(nameof(AxisInfoHeader));

// PinSearch值变化时
OnPropertyChanged(nameof(AxisInfoHeader));
```

### 3. XAML绑定

修改Expander控件的绑定：

```xml
<Expander 
    Grid.Row="1" 
    Margin="0,10,0,10" 
    d:IsExpanded="True" 
    ExpandDirection="Down" 
    Header="{Binding AxisInfoHeader}" 
    IsExpanded="{Binding IsAxisInfoExpanded, Mode=TwoWay}">
```

### 4. 设计时支持

在 `RobotStatusPanelDesignViewModel.cs` 中添加了相同的属性，确保设计时也能正常显示。

**注意**: DesignViewModel不使用ObservableProperty特性，而是使用传统的属性实现方式，因为DesignViewModel不是partial类。

## 使用效果

### 折叠状态
```
Robot轴位置信息:【T轴：12345 Step;R轴：67890 Step;Z轴：54321 PinSearch：Smooth：1000；Nose：1001】
```

### 展开状态
```
Robot轴位置信息
```

## 技术特点

1. **ObservableProperty**: 使用CommunityToolkit.Mvvm的ObservableProperty特性，简化属性实现
2. **双向绑定**: `IsExpanded` 使用双向绑定，确保UI状态与ViewModel同步
3. **实时更新**: T轴、R轴、Z轴位置数据变化时，Header内容会实时更新
4. **设计时支持**: 提供了DesignViewModel支持，便于UI设计
5. **性能优化**: 只在必要时触发PropertyChanged事件

## 扩展性

这个模式可以应用到其他需要动态Header的Expander控件上，只需要：

1. 在ViewModel中添加对应的展开状态属性
2. 添加动态Header属性
3. 在XAML中进行绑定
4. 在相关数据变化时触发Header更新

## 注意事项

- Header中显示的数据（T轴、R轴、Z轴、Smooth、Nose值）会根据实际的传感器数据实时更新
- 展开状态会在用户操作时自动保存，下次打开时保持上次的状态
- 使用ObservableProperty特性需要确保类继承自ObservableObject
- 如果需要持久化展开状态，可以考虑将其保存到配置文件中
