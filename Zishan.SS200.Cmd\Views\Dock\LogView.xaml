﻿<UserControl
    x:Class="Zishan.SS200.Cmd.Views.Dock.LogView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:behavior="clr-namespace:Zishan.SS200.Cmd.Behaviors"
    xmlns:common="clr-namespace:Zishan.SS200.Cmd.Common"
    xmlns:converters="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dockvm="clr-namespace:Zishan.SS200.Cmd.ViewModels.Dock"
    xmlns:dvm="clr-namespace:Zishan.SS200.Cmd.ViewModels.Dock.DesignViewModels"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:userControls="clr-namespace:Zishan.SS200.Cmd.UserControls"
    xmlns:viewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    xmlns:visualBasic="clr-namespace:Microsoft.VisualBasic;assembly=Microsoft.VisualBasic.Core"
    xmlns:wu="https://github.com/Monika1313/Wu"
    d:Background="LightBlue"
    d:DataContext="{x:Static dvm:LogViewDesignViewModel.Instance}"
    d:DesignHeight="800"
    d:DesignWidth="450"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <!-- <UserControl.DataContext> -->
    <!--     <dockvm:LogViewViewModel /> -->
    <!-- </UserControl.DataContext> -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  UI日志控制面板  -->
        <Border
            Grid.Row="0"
            Margin="2"
            Padding="5"
            Background="LightGray"
            BorderBrush="Gray"
            BorderThickness="1">
            <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                <CheckBox
                    x:Name="UILogEnabledCheckBox"
                    VerticalAlignment="Center"
                    Content="启用UI日志显示"
                    IsChecked="{Binding IsUILogEnabled, Mode=TwoWay}"
                    ToolTip="控制是否在界面上显示日志信息，注意：禁用后日志仍会记录到文件中">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="Checked">
                            <i:InvokeCommandAction Command="{Binding ToggleUILogEnabledCommand}" />
                        </i:EventTrigger>
                        <i:EventTrigger EventName="Unchecked">
                            <i:InvokeCommandAction Command="{Binding ToggleUILogEnabledCommand}" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </CheckBox>
            </StackPanel>
        </Border>

        <!--  日志信息显示区域  -->
        <GroupBox Grid.Row="1" Header="{Binding Title}">
            <ListBox
                x:Name="LogListBox"
                ItemsSource="{Binding LogList}"
                ScrollViewer.VerticalScrollBarVisibility="Auto"
                ToolTip="双击清除">
                <i:Interaction.Behaviors>
                    <behavior:ListBoxScrollToBottomBehavior />
                </i:Interaction.Behaviors>
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Margin="0,0,5,0" Text="{Binding Index}" />
                            <TextBlock Text="{Binding Message}" ToolTip="{Binding Message}" />
                            <StackPanel.ContextMenu>
                                <ContextMenu>
                                    <MenuItem
                                        Command="{Binding DataContext.CopyMessageToClipboardCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                        CommandParameter="{Binding Message}"
                                        Header="复制消息到剪贴板" />
                                </ContextMenu>
                            </StackPanel.ContextMenu>
                        </StackPanel>
                    </DataTemplate>
                </ListBox.ItemTemplate>
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="MouseDoubleClick">
                        <i:InvokeCommandAction Command="{Binding ExecuteLogListDoubleClickCommand}" CommandParameter="{Binding ElementName=LogListBox}" />
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </ListBox>
        </GroupBox>
    </Grid>
</UserControl>