using System;

namespace Zishan.SS200.Cmd.Models.SS200
{
    /// <summary>
    /// RTZ轴位置信息数据模型
    /// 包含RTZ轴的完整位置信息、安全状态和时间戳
    /// </summary>
    public class RTZAxisPositionInfo
    {
        /// <summary>
        /// 数据是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息（当数据无效时）
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        #region 步进值信息

        /// <summary>
        /// T轴步进位置值
        /// </summary>
        public int TAxisStep { get; set; }

        /// <summary>
        /// R轴步进位置值
        /// </summary>
        public int RAxisStep { get; set; }

        /// <summary>
        /// Z轴步进位置值
        /// </summary>
        public int ZAxisStep { get; set; }

        #endregion

        #region 物理单位信息

        /// <summary>
        /// T轴角度值（度）
        /// </summary>
        public double TAxisDegree { get; set; }

        /// <summary>
        /// R轴长度值（mm）
        /// </summary>
        public double RAxisLength { get; set; }

        /// <summary>
        /// Z轴高度值（mm）
        /// </summary>
        public double ZAxisHeight { get; set; }

        #endregion

        #region 安全状态信息

        /// <summary>
        /// T轴位置是否在安全范围内
        /// </summary>
        public bool IsTAxisSafe { get; set; }

        /// <summary>
        /// R轴位置是否在安全范围内
        /// </summary>
        public bool IsRAxisSafe { get; set; }

        /// <summary>
        /// Z轴位置是否在安全范围内
        /// </summary>
        public bool IsZAxisSafe { get; set; }

        /// <summary>
        /// 所有轴是否都在安全范围内
        /// </summary>
        public bool IsAllAxesSafe { get; set; }

        #endregion

        #region 时间戳信息

        /// <summary>
        /// 数据获取时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        #endregion

        #region 便捷属性

        /// <summary>
        /// 获取步进值的元组形式
        /// </summary>
        public (int TAxisStep, int RAxisStep, int ZAxisStep) StepValues =>
            (TAxisStep, RAxisStep, ZAxisStep);

        /// <summary>
        /// 获取物理值的元组形式
        /// </summary>
        public (double TAxisDegree, double RAxisLength, double ZAxisHeight) PhysicalValues =>
            (TAxisDegree, RAxisLength, ZAxisHeight);

        /// <summary>
        /// 获取安全状态的元组形式
        /// </summary>
        public (bool IsTAxisSafe, bool IsRAxisSafe, bool IsZAxisSafe) SafetyStatus =>
            (IsTAxisSafe, IsRAxisSafe, IsZAxisSafe);

        #endregion

        #region 格式化方法

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>格式化的字符串</returns>
        public override string ToString()
        {
            if (!IsValid)
                return $"RTZ轴位置信息无效: {ErrorMessage}";

            return $"RTZ轴位置 [{Timestamp:HH:mm:ss}]: " +
                   $"T={TAxisStep}({TAxisDegree:F1}°), " +
                   $"R={RAxisStep}({RAxisLength:F1}mm), " +
                   $"Z={ZAxisStep}({ZAxisHeight:F1}mm) " +
                   $"[{(IsAllAxesSafe ? "安全" : "警告")}]";
        }

        /// <summary>
        /// 获取详细的字符串表示
        /// </summary>
        /// <returns>详细的格式化字符串</returns>
        public string ToDetailedString()
        {
            if (!IsValid)
                return $"RTZ轴位置信息无效: {ErrorMessage}";

            return $"RTZ轴详细位置信息 [{Timestamp:yyyy-MM-dd HH:mm:ss}]:\n" +
                   $"  T轴: {TAxisStep} steps ({TAxisDegree:F2}°) - {(IsTAxisSafe ? "安全" : "警告")}\n" +
                   $"  R轴: {RAxisStep} steps ({RAxisLength:F2}mm) - {(IsRAxisSafe ? "安全" : "警告")}\n" +
                   $"  Z轴: {ZAxisStep} steps ({ZAxisHeight:F2}mm) - {(IsZAxisSafe ? "安全" : "警告")}\n" +
                   $"  整体状态: {(IsAllAxesSafe ? "安全" : "警告")}";
        }

        /// <summary>
        /// 获取简化的字符串表示
        /// </summary>
        /// <returns>简化的格式化字符串</returns>
        public string ToSimpleString()
        {
            if (!IsValid)
                return "数据无效";

            return $"T:{TAxisStep}, R:{RAxisStep}, Z:{ZAxisStep}";
        }

        /// <summary>
        /// 获取JSON格式的字符串表示
        /// </summary>
        /// <returns>JSON格式的字符串</returns>
        public string ToJsonString()
        {
            if (!IsValid)
                return $"{{\"valid\": false, \"error\": \"{ErrorMessage}\"}}";

            return $"{{" +
                   $"\"valid\": true, " +
                   $"\"timestamp\": \"{Timestamp:yyyy-MM-dd HH:mm:ss}\", " +
                   $"\"tAxis\": {{\"step\": {TAxisStep}, \"degree\": {TAxisDegree:F2}, \"safe\": {IsTAxisSafe.ToString().ToLower()}}}, " +
                   $"\"rAxis\": {{\"step\": {RAxisStep}, \"length\": {RAxisLength:F2}, \"safe\": {IsRAxisSafe.ToString().ToLower()}}}, " +
                   $"\"zAxis\": {{\"step\": {ZAxisStep}, \"height\": {ZAxisHeight:F2}, \"safe\": {IsZAxisSafe.ToString().ToLower()}}}, " +
                   $"\"allSafe\": {IsAllAxesSafe.ToString().ToLower()}" +
                   $"}}";
        }

        #endregion

        #region 比较方法

        /// <summary>
        /// 比较两个RTZ轴位置信息是否相等（仅比较位置值）
        /// </summary>
        /// <param name="other">要比较的另一个位置信息</param>
        /// <returns>位置值是否相等</returns>
        public bool PositionEquals(RTZAxisPositionInfo other)
        {
            if (other == null)
                return false;

            if (!IsValid || !other.IsValid)
                return false;

            return TAxisStep == other.TAxisStep &&
                   RAxisStep == other.RAxisStep &&
                   ZAxisStep == other.ZAxisStep;
        }

        /// <summary>
        /// 检查位置是否发生了变化
        /// </summary>
        /// <param name="other">要比较的另一个位置信息</param>
        /// <returns>位置是否发生了变化</returns>
        public bool HasPositionChanged(RTZAxisPositionInfo other)
        {
            return !PositionEquals(other);
        }

        /// <summary>
        /// 获取与另一个位置信息的差值
        /// </summary>
        /// <param name="other">要比较的另一个位置信息</param>
        /// <returns>位置差值的元组</returns>
        public (int TAxisDiff, int RAxisDiff, int ZAxisDiff) GetPositionDifference(RTZAxisPositionInfo other)
        {
            if (other == null || !IsValid || !other.IsValid)
                return (0, 0, 0);

            return (
                TAxisStep - other.TAxisStep,
                RAxisStep - other.RAxisStep,
                ZAxisStep - other.ZAxisStep
            );
        }

        #endregion

        #region 静态工厂方法

        /// <summary>
        /// 创建一个表示无效数据的RTZ轴位置信息
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>无效的RTZ轴位置信息</returns>
        public static RTZAxisPositionInfo CreateInvalid(string errorMessage = "数据无效")
        {
            return new RTZAxisPositionInfo
            {
                IsValid = false,
                ErrorMessage = errorMessage,
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// 创建一个有效的RTZ轴位置信息
        /// </summary>
        /// <param name="tStep">T轴步进值</param>
        /// <param name="rStep">R轴步进值</param>
        /// <param name="zStep">Z轴步进值</param>
        /// <param name="tDegree">T轴角度值</param>
        /// <param name="rLength">R轴长度值</param>
        /// <param name="zHeight">Z轴高度值</param>
        /// <param name="tSafe">T轴是否安全</param>
        /// <param name="rSafe">R轴是否安全</param>
        /// <param name="zSafe">Z轴是否安全</param>
        /// <returns>有效的RTZ轴位置信息</returns>
        public static RTZAxisPositionInfo CreateValid(
            int tStep, int rStep, int zStep,
            double tDegree, double rLength, double zHeight,
            bool tSafe, bool rSafe, bool zSafe)
        {
            return new RTZAxisPositionInfo
            {
                IsValid = true,
                TAxisStep = tStep,
                RAxisStep = rStep,
                ZAxisStep = zStep,
                TAxisDegree = tDegree,
                RAxisLength = rLength,
                ZAxisHeight = zHeight,
                IsTAxisSafe = tSafe,
                IsRAxisSafe = rSafe,
                IsZAxisSafe = zSafe,
                IsAllAxesSafe = tSafe && rSafe && zSafe,
                Timestamp = DateTime.Now
            };
        }

        #endregion
    }
}
