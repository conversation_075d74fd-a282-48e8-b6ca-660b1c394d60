# SS-200机器人系统R轴移动通用流程图 (AR11-AR20)

## 概述

基于对AR11到AR20文档的深度分析，本流程图整合了所有R轴移动操作的共同模式和逻辑，适用于CHA、CHB、冷却室、卡匣等所有目标位置的R轴移动操作。

## 流程图

```mermaid
flowchart TD
    Start([开始: R轴移动命令]) --> CheckRobotStatus{检查机器人状态<br/>MRS1~MRS3}
    
    CheckRobotStatus -->|MRS1 IDLE| CheckTargetDevice{检查目标设备状态}
    CheckRobotStatus -->|MRS2 BUSY| AlarmRA1[RA1 ALARM<br/>机器人忙碌]
    CheckRobotStatus -->|MRS3 ALARM| AlarmRA2[RA2 ALARM<br/>机器人报警]
    
    CheckTargetDevice -->|CHA| CheckCHA{检查CHA状态<br/>SS17/SS18}
    CheckTargetDevice -->|CHB| CheckCHB{检查CHB状态<br/>SS26/SS27}
    CheckTargetDevice -->|冷却室| CheckCT{检查冷却室状态<br/>MCS1~MCS2}
    CheckTargetDevice -->|卡匣| CheckCassette{检查卡匣状态<br/>SSD1~SSD7}
    
    CheckCHA -->|SS17 Enable| CheckCHATrigger{检查CHA触发<br/>MPS1~MPS2}
    CheckCHA -->|SS18 Disable| AlarmRA26[RA26 ALARM<br/>CHA禁用]
    
    CheckCHB -->|SS26 Enable| CheckCHBTrigger{检查CHB触发<br/>MPS1~MPS2}
    CheckCHB -->|SS27 Disable| AlarmRA27[RA27 ALARM<br/>CHB禁用]
    
    CheckCT -->|MCS1 Normal| CheckCTRun{检查冷却室运行<br/>MCS3~MCS5}
    CheckCT -->|MCS2 Alarm| AlarmRA34[RA34 ALARM<br/>冷却室报警]
    
    CheckCassette -->|正常位置| CheckShuttle{检查穿梭机状态}
    CheckCassette -->|异常位置| AlarmRA25[RA25 ALARM<br/>卡匣位置异常]
    
    CheckCHATrigger -->|MPS1 Alarm| AlarmRA28[RA28 ALARM<br/>CHA触发报警]
    CheckCHATrigger -->|MPS2 Normal| CheckCHARun{检查CHA运行<br/>MPS3~MPS5}
    
    CheckCHBTrigger -->|MPS2 Alarm| AlarmRA29[RA29 ALARM<br/>CHB触发报警]
    CheckCHBTrigger -->|MPS1 Normal| CheckCHBRun{检查CHB运行<br/>MPS3~MPS5}
    
    CheckCHARun -->|MPS3 Busy| AlarmRA30[RA30 ALARM<br/>CHA忙碌]
    CheckCHARun -->|MPS4 Idle| CheckCHADoor{检查CHA门状态<br/>SP1/SP2}
    CheckCHARun -->|MPS5 Processing| AlarmRA32[RA32 ALARM<br/>CHA处理中]
    
    CheckCHBRun -->|MPS3 Busy| AlarmRA31[RA31 ALARM<br/>CHB忙碌]
    CheckCHBRun -->|MPS4 Idle| CheckCHBDoor{检查CHB门状态<br/>SP1/SP2}
    CheckCHBRun -->|MPS5 Processing| AlarmRA33[RA33 ALARM<br/>CHB处理中]
    
    CheckCTRun -->|MCS3 Busy| AlarmRA35[RA35 ALARM<br/>冷却室忙碌]
    CheckCTRun -->|MCS4 Idle| CheckTAxis{检查T轴位置}
    CheckCTRun -->|MCS5 Processing| AlarmRA35B[RA35 ALARM<br/>冷却室处理中]
    
    CheckShuttle -->|穿梭机1启用| CheckTAxis
    CheckShuttle -->|穿梭机2启用| CheckTAxis
    CheckShuttle -->|穿梭机禁用| AlarmRA23[RA23 ALARM<br/>穿梭机1禁用]
    CheckShuttle -->|穿梭机禁用| AlarmRA24[RA24 ALARM<br/>穿梭机2禁用]
    
    CheckCHADoor -->|SP1 Open| CheckTAxis
    CheckCHADoor -->|SP2 Close| AlarmRA8[RA8 ALARM<br/>CHA门关闭]
    
    CheckCHBDoor -->|SP1 Open| CheckTAxis
    CheckCHBDoor -->|SP2 Close| AlarmRA9[RA9 ALARM<br/>CHB门关闭]
    
    CheckTAxis{检查T轴位置<br/>当前 vs 目标} -->|位置正确| CheckZAxis{检查Z轴位置<br/>当前 vs 目标}
    CheckTAxis -->|位置错误| AlarmRA4[RA4 ALARM<br/>T轴位置错误]
    
    AlarmRA4 --> UserConfirm1{用户确认?}
    UserConfirm1 -->|确认| CheckZAxis
    UserConfirm1 -->|取消| CommandCancel[命令取消]
    
    CheckZAxis -->|位置正确| CheckSlideOutSensor{检查滑出传感器<br/>安装状态 SPS11}
    CheckZAxis -->|位置错误| AlarmRA10[RA10 ALARM<br/>Z轴位置错误]
    
    AlarmRA10 --> UserConfirm2{用户确认?}
    UserConfirm2 -->|确认| CheckSlideOutSensor
    UserConfirm2 -->|取消| CommandCancel
    
    CheckSlideOutSensor -->|SPS11=Y<br/>已安装| CheckSensorStatus{检查滑出传感器状态<br/>DI19/DI20}
    CheckSlideOutSensor -->|SPS11=N<br/>未安装| ExecuteRAxisMove[执行R轴移动<br/>AR20-RPxx]
    
    CheckSensorStatus -->|DI19=0 DI20=0<br/>正常| ExecuteRAxisMove
    CheckSensorStatus -->|DI19=0 DI20=1| AlarmRA20[RA20 ALARM<br/>传感器状态异常]
    CheckSensorStatus -->|DI19=1 DI20=0| AlarmRA19[RA19 ALARM<br/>传感器状态异常]
    CheckSensorStatus -->|DI19=1 DI20=1| AlarmRA21[RA21 ALARM<br/>传感器状态异常]
    
    ExecuteRAxisMove --> CheckMoveComplete{检查移动完成<br/>位置反馈}
    
    CheckMoveComplete -->|移动超时| AlarmRA6[RA6 ALARM<br/>移动超时]
    CheckMoveComplete -->|移动完成| FinalSensorCheck{最终传感器检查<br/>DI19/DI20}
    
    FinalSensorCheck -->|DI19=0 DI20=0<br/>正常| CommandDone([命令完成])
    FinalSensorCheck -->|DI19=0 DI20=1| AlarmRA20B[RA20 ALARM<br/>最终传感器异常]
    FinalSensorCheck -->|DI19=1 DI20=0| AlarmRA19B[RA19 ALARM<br/>最终传感器异常]
    FinalSensorCheck -->|DI19=1 DI20=1| AlarmRA21B[RA21 ALARM<br/>最终传感器异常]
    
    %% 报警处理
    AlarmRA1 --> End([流程结束])
    AlarmRA2 --> End
    AlarmRA6 --> End
    AlarmRA8 --> End
    AlarmRA9 --> End
    AlarmRA19 --> End
    AlarmRA19B --> End
    AlarmRA20 --> End
    AlarmRA20B --> End
    AlarmRA21 --> End
    AlarmRA21B --> End
    AlarmRA23 --> End
    AlarmRA24 --> End
    AlarmRA25 --> End
    AlarmRA26 --> End
    AlarmRA27 --> End
    AlarmRA28 --> End
    AlarmRA29 --> End
    AlarmRA30 --> End
    AlarmRA31 --> End
    AlarmRA32 --> End
    AlarmRA33 --> End
    AlarmRA34 --> End
    AlarmRA35 --> End
    AlarmRA35B --> End
    CommandCancel --> End
    CommandDone --> End
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef alarm fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class Start,End,CommandDone,CommandCancel startEnd
    class CheckRobotStatus,CheckTargetDevice,CheckCHA,CheckCHB,CheckCT,CheckCassette,CheckCHATrigger,CheckCHBTrigger,CheckCHARun,CheckCHBRun,CheckCTRun,CheckShuttle,CheckCHADoor,CheckCHBDoor,CheckTAxis,CheckZAxis,CheckSlideOutSensor,CheckSensorStatus,CheckMoveComplete,FinalSensorCheck,UserConfirm1,UserConfirm2 decision
    class ExecuteRAxisMove process
    class AlarmRA1,AlarmRA2,AlarmRA4,AlarmRA6,AlarmRA8,AlarmRA9,AlarmRA10,AlarmRA19,AlarmRA19B,AlarmRA20,AlarmRA20B,AlarmRA21,AlarmRA21B,AlarmRA23,AlarmRA24,AlarmRA25,AlarmRA26,AlarmRA27,AlarmRA28,AlarmRA29,AlarmRA30,AlarmRA31,AlarmRA32,AlarmRA33,AlarmRA34,AlarmRA35,AlarmRA35B alarm
```

## 核心流程结构

### 1. 系统状态检查层
- **机器人状态检查** (MRS1~MRS3)：确保机器人处于可操作状态
- **目标设备状态检查**：根据目标位置检查相应设备状态
  - **CHA/CHB**：检查腔室启用状态、触发状态、运行状态、门状态
  - **冷却室**：检查触发状态和运行状态  
  - **卡匣**：检查穿梭机位置和启用状态

### 2. 位置准备层
- **T轴位置检查**：确认当前T轴位置是否匹配目标要求
- **Z轴位置检查**：确认当前Z轴位置是否匹配目标要求
- **用户交互机制**：位置不匹配时提供确认/取消选项

### 3. 传感器检查层
- **滑出传感器安装检查** (SPS11)：Y=已安装，N=未安装
- **滑出传感器状态检查** (DI19/DI20)：四种组合状态的处理
  - `00`：正常状态，继续执行
  - `01`、`10`、`11`：异常状态，触发相应报警

### 4. 执行层
- **R轴移动执行** (AR20-RPxx)：调用具体的R轴位置移动
- **移动完成检查**：验证移动是否在规定时间内完成
- **最终传感器检查**：移动完成后再次确认传感器状态

## 错误处理机制

流程图包含了完整的错误处理体系，涵盖了文档中提到的所有报警代码：

| 报警代码 | 描述 | 处理方式 |
|---------|------|---------|
| RA1/RA2 | 机器人状态异常 | 直接终止 |
| RA4/RA10 | 轴位置错误 | 支持用户确认继续 |
| RA6 | 移动超时 | 直接终止 |
| RA8/RA9 | 腔室门状态异常 | 直接终止 |
| RA19/RA20/RA21 | 传感器状态异常 | 直接终止 |
| RA23~RA35 | 各种设备状态异常 | 直接终止 |

## 通用性设计

这个流程图采用参数化设计，适用于：
- **所有目标位置**：CHA、CHB、冷却室、卡匣
- **所有移动类型**：smooth paddle、nose paddle  
- **所有R轴位置**：AR20-RP10到RP18的各种位置

## 关键特性

1. **分层检查**：从系统级到设备级到传感器级的逐层验证
2. **容错机制**：关键错误点提供用户交互选项
3. **状态一致性**：移动前后都进行传感器状态检查
4. **完整覆盖**：涵盖了AR11-AR20中所有的逻辑分支和异常处理

## 使用说明

本流程图可以作为SS-200机器人系统R轴移动操作的标准参考，帮助：
- 理解R轴移动的完整流程
- 实现各种R轴移动功能
- 进行系统调试和故障排除
- 制定操作规程和培训材料

## 相关文档

- AR11: move R-axis smooth CHA extend
- AR12: move R-axis smooth CHB extend  
- AR13: move R-axis smooth cooling chamber extend
- AR14: move R-axis smooth cassette extend
- AR15: move R-axis nose CHA extend
- AR16: move R-axis nose CHB extend
- AR17: move R-axis nose cooling chamber extend
- AR18: move R-axis nose cassette extend
- AR19: R-axis zero
- AR20: R-axis position xx

---
*文档生成时间：2025-07-10*  
*基于文档：AR11-AR20 机器人系统逻辑*
