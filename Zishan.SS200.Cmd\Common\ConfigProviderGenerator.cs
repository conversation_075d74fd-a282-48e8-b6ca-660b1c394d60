using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 配置提供者代码生成工具类
    /// 可用于生成基于新枚举字典的配置提供者代码
    /// </summary>
    public static class ConfigProviderGenerator
    {
        /// <summary>
        /// 生成新的配置提供者代码
        /// </summary>
        /// <param name="enumType">配置参数枚举类型</param>
        /// <param name="providerName">提供者类名</param>
        /// <param name="configPath">配置文件路径</param>
        /// <param name="defaultValues">默认值字典</param>
        /// <param name="specificMethods">特定参数访问方法列表</param>
        /// <param name="namespaceName">命名空间</param>
        /// <returns>生成的代码</returns>
        public static string GenerateProviderCode(
            Type enumType,
            string providerName,
            string configPath,
            Dictionary<Enum, object> defaultValues,
            List<(Enum key, string methodName, Type returnType, string description)> specificMethods,
            string namespaceName)
        {
            if (!enumType.IsEnum)
            {
                throw new ArgumentException("enumType必须是枚举类型", nameof(enumType));
            }

            var sb = new StringBuilder();

            // 生成using语句
            sb.AppendLine("using System;");
            sb.AppendLine("using log4net;");
            sb.AppendLine("using Zishan.SS200.Cmd.Common;");
            sb.AppendLine($"using {enumType.Namespace};");
            sb.AppendLine();

            // 生成命名空间和类声明
            sb.AppendLine($"namespace {namespaceName}");
            sb.AppendLine("{");
            sb.AppendLine($"    /// <summary>");
            sb.AppendLine($"    /// {providerName} - 使用枚举字典实现");
            sb.AppendLine($"    /// </summary>");
            sb.AppendLine($"    public class {providerName} : ConfigProviderBase<{enumType.Name}, ConfigParametersBase>");
            sb.AppendLine("    {");
            sb.AppendLine($"        private static readonly ILog _logger = LogManager.GetLogger(typeof({providerName}));");
            sb.AppendLine();

            // 生成单例实现
            sb.AppendLine("        // 单例实现");
            sb.AppendLine($"        private static readonly Lazy<{providerName}> _instance =");
            sb.AppendLine($"            new Lazy<{providerName}>(() => new {providerName}());");
            sb.AppendLine();

            // 生成配置文件路径
            sb.AppendLine("        // 配置文件路径");
            sb.AppendLine($"        private const string CONFIG_PATH = \"{configPath}\";");
            sb.AppendLine();

            // 生成单例属性
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 获取单例实例");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine($"        public static {providerName} Instance => _instance.Value;");
            sb.AppendLine();

            // 生成构造函数
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 私有构造函数");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine($"        private {providerName}() : base(CONFIG_PATH)");
            sb.AppendLine("        {");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 生成初始化默认值方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 初始化默认值");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        protected override void InitializeDefaultValues()");
            sb.AppendLine("        {");

            // 添加默认值设置
            foreach (var kvp in defaultValues)
            {
                string valueString = GetValueString(kvp.Value);
                sb.AppendLine($"            Settings.SetDefault({enumType.Name}.{kvp.Key}, {valueString});");
            }

            sb.AppendLine("        }");
            sb.AppendLine();

            // 生成特定参数访问方法
            if (specificMethods.Count > 0)
            {
                sb.AppendLine("        #region 特定参数访问方法");
                sb.AppendLine();

                foreach (var method in specificMethods)
                {
                    string getterMethod = GetGetterMethodName(method.returnType);

                    sb.AppendLine("        /// <summary>");
                    sb.AppendLine($"        /// {method.description}");
                    sb.AppendLine("        /// </summary>");
                    sb.AppendLine($"        public {GetTypeName(method.returnType)} {method.methodName}()");
                    sb.AppendLine("        {");
                    sb.AppendLine($"            return {getterMethod}({enumType.Name}.{method.key});");
                    sb.AppendLine("        }");
                    sb.AppendLine();
                }

                sb.AppendLine("        #endregion");
            }

            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// 获取值的字符串表示形式
        /// </summary>
        private static string GetValueString(object value)
        {
            if (value == null)
            {
                return "null";
            }

            if (value is string strValue)
            {
                return $"\"{strValue}\"";
            }

            if (value is bool boolValue)
            {
                return boolValue ? "true" : "false";
            }

            if (value is double doubleValue)
            {
                return doubleValue.ToString(System.Globalization.CultureInfo.InvariantCulture);
            }

            return value.ToString();
        }

        /// <summary>
        /// 获取适当的getter方法名
        /// </summary>
        private static string GetGetterMethodName(Type type)
        {
            if (type == typeof(int))
            {
                return "GetIntValue";
            }

            if (type == typeof(double))
            {
                return "GetDoubleValue";
            }

            if (type == typeof(string))
            {
                return "GetStringValue";
            }

            if (type == typeof(bool))
            {
                return "GetBoolValue";
            }

            return $"GetValue<{GetTypeName(type)}>";
        }

        /// <summary>
        /// 获取类型名称的字符串表示
        /// </summary>
        private static string GetTypeName(Type type)
        {
            if (type == typeof(int))
            {
                return "int";
            }

            if (type == typeof(double))
            {
                return "double";
            }

            if (type == typeof(string))
            {
                return "string";
            }

            if (type == typeof(bool))
            {
                return "bool";
            }

            return type.Name;
        }

        /// <summary>
        /// 将生成的代码保存到文件
        /// </summary>
        /// <param name="code">生成的代码</param>
        /// <param name="filePath">文件路径</param>
        public static void SaveToFile(string code, string filePath)
        {
            string directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.WriteAllText(filePath, code);
        }
    }
}