ASP2 
CD SD
	chamber trigger status review
MPS1~MPS2
		MPS1 IDLE
no alarm
			chamber run status review
MPS3~MPS5
				MPS4 / MPS3B
					chamber slit door status review
SP1~SP3
						SP2
slit door close
							command done
						others status
							PAC6 ALARM
						SP1 or SP3
							robot run status review
MRS1~3
								MRS1 or MRS3
robot run status is busy or alarm
									PAC18 ALARM
								MRS2
robot run status is idle
									robot R-axis status review
RS10~18 or others status
										RS18
robot R-axis at zero position
											robot T-axis status review
RS1~9 or others status
												one position of (RS1~9)
													PDO10=0
PDO11=1
														SP1---SP3---SP2
slit door status change logic
															PPS1≤time≤PPS2
																command done
															time>PPS2
																PAC8 ALARM
															time<PPS1
																PAC9 ALARM
														others status change logic
															PAC5 ALARM
												others status
													PAC17 ALARM
										others status
											PAC17 ALARM
				MPS3A
					PAC2 ALARM
				MPS5
					PAC3 ALARM
		MPS2
alarm
			PAC1 ALARM