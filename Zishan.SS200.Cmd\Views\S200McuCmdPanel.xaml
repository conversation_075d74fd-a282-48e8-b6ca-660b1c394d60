﻿<UserControl
    x:Class="Zishan.SS200.Cmd.Views.S200McuCmdPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Extensions="clr-namespace:Zishan.SS200.Cmd.Extensions"
    xmlns:behavior="clr-namespace:Zishan.SS200.Cmd.Behaviors"
    xmlns:common="clr-namespace:Zishan.SS200.Cmd.Common"
    xmlns:conv="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:converters="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dock="clr-namespace:Zishan.SS200.Cmd.Views.Dock"
    xmlns:dvm="clr-namespace:Zishan.SS200.Cmd.ViewModels.DesignViewModels"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:services="clr-namespace:Zishan.SS200.Cmd.Services"
    xmlns:vRules="clr-namespace:Zishan.SS200.Cmd.ValidationRules"
    xmlns:viewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    xmlns:views="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:wucvt="clr-namespace:Wu.Wpf.Converters;assembly=Wu.Wpf"
    xmlns:wuext="clr-namespace:Wu.Wpf.Extensions;assembly=Wu.Wpf"
    MinWidth="800"
    MinHeight="600"
    d:DataContext="{x:Static dvm:MainViewDesignViewModel.Instance}"
    d:DesignHeight="1290"
    d:DesignWidth="2146"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="Transparent"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <conv:DescriptionConverter x:Key="DescriptionConverter" />
            <conv:AndMultiValueConverter x:Key="AndMultiValueConverter" />
            <conv:InvertBooleanConverter x:Key="InvertBooleanConverter" />
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <Grid Background="White">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  顶部工具栏  -->
            <ToolBar Grid.Row="0">
                <Button
                    Command="{Binding ConnectDevicesCommand}"
                    Content="连接设备"
                    Style="{StaticResource ButtonPrimary}" />
                <Button
                    Margin="10,0"
                    Command="{Binding DisconnectDevicesCommand}"
                    Content="断开设备"
                    Style="{StaticResource ButtonWarning}" />
                <Button
                    Margin="10,0"
                    Command="{Binding ResetTaskCommand}"
                    Content="重置任务"
                    Style="{StaticResource ButtonInfo}" />

                <!--
                <Button
                    Margin="10,0"
                    d:Visibility="Visible"
                    Command="{Binding SingleCmdTestCommand}"
                    Content="单命令测试"
                    Style="{StaticResource ButtonPrimary}"
                    Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />
                <Button
                    Margin="10,0"
                    d:Visibility="Visible"
                    Command="{Binding CompositeTestCmdTestCommand}"
                    Content="复合命令测试"
                    Style="{StaticResource ButtonPrimary}"
                    Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />
                -->
            </ToolBar>

            <!--  主要内容区域  -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" MinWidth="200" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="2*" MinWidth="400" />
                </Grid.ColumnDefinitions>

                <!--  左侧IO区域  -->
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="2*" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="1*" />
                    </Grid.RowDefinitions>
                    <GroupBox
                        Grid.Column="0"
                        Margin="5"
                        Header="MCU设备DI状态">
                        <dock:ModbusDICoilsPanel />
                    </GroupBox>

                    <!--  水平分隔线  -->
                    <GridSplitter
                        Grid.Row="1"
                        Grid.ColumnSpan="2"
                        Height="5"
                        Margin="0"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        Background="Transparent"
                        ShowsPreview="True">
                        <GridSplitter.Template>
                            <ControlTemplate TargetType="{x:Type GridSplitter}">
                                <Grid Background="Transparent">
                                    <Rectangle
                                        Height="1"
                                        Margin="0,2"
                                        Fill="{DynamicResource MaterialDesignDivider}" />
                                </Grid>
                            </ControlTemplate>
                        </GridSplitter.Template>
                    </GridSplitter>

                    <GroupBox
                        Grid.Row="2"
                        Margin="5"
                        Header="MCU设备DIO状态">
                        <dock:ModbusDOCoilsPanel />
                    </GroupBox>
                </Grid>

                <!--  垂直分隔线  -->
                <GridSplitter
                    Grid.RowSpan="999"
                    Grid.Column="1"
                    Width="5"
                    Margin="0"
                    HorizontalAlignment="Center"
                    Background="Transparent"
                    ShowsPreview="True">
                    <GridSplitter.Template>
                        <ControlTemplate TargetType="{x:Type GridSplitter}">
                            <Grid Background="Transparent">
                                <Rectangle
                                    Width="1"
                                    Margin="2,0"
                                    Fill="{DynamicResource MaterialDesignDivider}" />
                            </Grid>
                        </ControlTemplate>
                    </GridSplitter.Template>
                </GridSplitter>

                <!--  右侧命令区域  -->
                <GroupBox
                    Grid.Column="2"
                    Margin="5"
                    Header="命令执行">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" MinWidth="300" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <!--  左测：命令输入区域  -->
                        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
                            <StackPanel Margin="5">
                                <TextBlock
                                    Margin="0,0,0,5"
                                    FontWeight="Bold"
                                    Text="命令控制面板" />

                                <!--  设备选择  -->
                                <WrapPanel Margin="0,5">
                                    <TextBlock
                                        MinWidth="60"
                                        Margin="0,0,5,0"
                                        VerticalAlignment="Center"
                                        Text="设备：" />
                                    <RadioButton
                                        Margin="0,0,10,0"
                                        Content="Shuttle"
                                        GroupName="DeviceSelect"
                                        IsChecked="{Binding IsShuttleSelected}"
                                        IsEnabled="{Binding IsSingleCommandEnabled}" />
                                    <RadioButton
                                        Margin="0,0,10,0"
                                        Content="Robot"
                                        GroupName="DeviceSelect"
                                        IsChecked="{Binding IsRobotSelected}"
                                        IsEnabled="{Binding IsSingleCommandEnabled}" />
                                    <RadioButton
                                        Margin="0,0,10,0"
                                        Content="ChamberA"
                                        GroupName="DeviceSelect"
                                        IsChecked="{Binding IsChaSelected}"
                                        IsEnabled="{Binding IsSingleCommandEnabled}" />
                                    <RadioButton
                                        Content="ChamberB"
                                        GroupName="DeviceSelect"
                                        IsChecked="{Binding IsChbSelected}"
                                        IsEnabled="{Binding IsSingleCommandEnabled}" />
                                </WrapPanel>

                                <!--  命令选择  -->
                                <DockPanel Margin="0,5" LastChildFill="True">
                                    <TextBlock
                                        MinWidth="60"
                                        VerticalAlignment="Center"
                                        DockPanel.Dock="Left"
                                        Text="命令：" />
                                    <ComboBox
                                        MinWidth="300"
                                        IsEnabled="{Binding IsSingleCommandEnabled}"
                                        ItemsSource="{Binding AvailableCommands}"
                                        SelectedItem="{Binding SelectedCommand}"
                                        SelectedValuePath="CommandName">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock>
                                                    <TextBlock.Text>
                                                        <MultiBinding StringFormat="{}{0} , {1}[{2}] , {3}, 静态参数数量：{4}">
                                                            <Binding Path="CMDIndex" />
                                                            <Binding Path="DisplayName" />
                                                            <Binding Path="CommandPrompt" />
                                                            <Binding Path="Description" />
                                                            <Binding Path="StaticParameterList.Count" />
                                                        </MultiBinding>
                                                    </TextBlock.Text>
                                                </TextBlock>
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>
                                </DockPanel>
                                <!--  命令提示  -->
                                <DockPanel Margin="0,5" LastChildFill="True">
                                    <TextBlock
                                        MinWidth="60"
                                        VerticalAlignment="Center"
                                        DockPanel.Dock="Left"
                                        Text="提示：" />
                                    <TextBox
                                        MinWidth="300"
                                        IsReadOnly="True"
                                        Text="{Binding CommandPrompt, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </DockPanel>

                                <UniformGrid Columns="2">
                                    <!--  静态参数输入  -->
                                    <DockPanel Margin="0,5" LastChildFill="True">
                                        <TextBlock
                                            MinWidth="60"
                                            VerticalAlignment="Center"
                                            DockPanel.Dock="Left"
                                            Text="静态参数：" />
                                        <TextBox
                                            Height="120"
                                            MinWidth="300"
                                            MaxWidth="800"
                                            AcceptsReturn="True"
                                            HorizontalScrollBarVisibility="Auto"
                                            IsEnabled="{Binding IsSingleCommandEnabled}"
                                            Text="{Binding StaticParameters, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                            TextWrapping="Wrap"
                                            ToolTip="命令配置中的静态参数，从JSON文件中加载"
                                            VerticalScrollBarVisibility="Auto" />
                                    </DockPanel>

                                    <!--  动态参数输入  -->
                                    <DockPanel Margin="20,5,0,5" LastChildFill="True">
                                        <TextBlock
                                            MinWidth="60"
                                            VerticalAlignment="Center"
                                            DockPanel.Dock="Left"
                                            Text="动态参数：" />
                                        <TextBox
                                            Height="120"
                                            MinWidth="300"
                                            MaxWidth="800"
                                            AcceptsReturn="True"
                                            HorizontalScrollBarVisibility="Auto"
                                            IsEnabled="{Binding IsSingleCommandEnabled}"
                                            Text="{Binding DynamicParameters, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                            TextWrapping="Wrap"
                                            ToolTip="每行格式：参数名=值，值支持十进制或0x开头16进制，支持空行和//、#注释"
                                            VerticalScrollBarVisibility="Auto" />
                                    </DockPanel>
                                </UniformGrid>

                                <!--  动态参数错误提示区  -->
                                <ItemsControl ItemsSource="{Binding DynamicParameterErrors}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="Red"
                                                Text="{Binding}" />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <!--  命令说明  -->
                                <TextBlock
                                    Margin="0,5,0,5"
                                    Foreground="#707070"
                                    Text="{Binding CommandDescription}"
                                    TextWrapping="Wrap" />

                                <StackPanel Orientation="Horizontal">
                                    <!--  执行按钮  -->
                                    <GroupBox
                                        Margin="10,10,10,0"
                                        Padding="10"
                                        Header="单命令管理">
                                        <StackPanel>
                                            <Button
                                                Width="120"
                                                Margin="0,5,0,5"
                                                HorizontalAlignment="Left"
                                                Command="{Binding ExecuteCommandCommand}"
                                                Content="执行命令"
                                                IsEnabled="{Binding IsSingleCommandEnabled}"
                                                Style="{StaticResource ButtonPrimary}" />
                                        </StackPanel>
                                    </GroupBox>

                                    <!--  批量命令管理  -->
                                    <GroupBox
                                        Margin="10,10,10,0"
                                        Padding="10"
                                        Header="批量命令管理">
                                        <StackPanel>
                                            <DockPanel Margin="0,5,0,10" LastChildFill="True">
                                                <TextBlock
                                                    MinWidth="80"
                                                    Margin="0,0,10,0"
                                                    VerticalAlignment="Center"
                                                    DockPanel.Dock="Left"
                                                    Text="批量序列："
                                                    TextAlignment="Right" />
                                                <ComboBox
                                                    MinWidth="300"
                                                    MaxWidth="600"
                                                    IsEnabled="{Binding IsBatchCommandEnabled}"
                                                    ItemsSource="{Binding AvailableBatchSequences}"
                                                    SelectedItem="{Binding SelectedBatchSequence}">
                                                    <ComboBox.ItemTemplate>
                                                        <DataTemplate>
                                                            <TextBlock>
                                                                <TextBlock.Text>
                                                                    <MultiBinding StringFormat="{}{0} - {1} (命令数: {2})">
                                                                        <Binding Path="Name" />
                                                                        <Binding Path="Description" />
                                                                        <Binding Path="Commands.Count" />
                                                                    </MultiBinding>
                                                                </TextBlock.Text>
                                                            </TextBlock>
                                                        </DataTemplate>
                                                    </ComboBox.ItemTemplate>
                                                </ComboBox>
                                            </DockPanel>

                                            <TextBlock
                                                Margin="0,0,0,10"
                                                Padding="5"
                                                Background="#F5F5F5"
                                                Foreground="#505050"
                                                Text="{Binding SelectedBatchSequence.Description}"
                                                TextWrapping="Wrap" />

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>

                                                <Button
                                                    Grid.Column="0"
                                                    Width="120"
                                                    Margin="0,0,10,0"
                                                    HorizontalAlignment="Left"
                                                    Command="{Binding ExecuteBatchCommandCommand}"
                                                    Content="执行批量序列"
                                                    IsEnabled="{Binding IsBatchCommandEnabled}"
                                                    Style="{StaticResource ButtonPrimary}" />

                                                <Button
                                                    Grid.Column="1"
                                                    Width="120"
                                                    HorizontalAlignment="Left"
                                                    Command="{Binding StopBatchCommandExecutionCommand}"
                                                    Content="停止执行"
                                                    IsEnabled="{Binding IsExecutingBatchCommand}"
                                                    Style="{StaticResource ButtonDanger}" />
                                            </Grid>

                                            <!--  批量执行状态  -->
                                            <Border
                                                Margin="0,10,0,0"
                                                Padding="8"
                                                Background="#E8F5E9"
                                                BorderBrush="#81C784"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Visibility="{Binding BatchCommandProgressInfo, TargetNullValue=Collapsed, FallbackValue=Collapsed}">
                                                <TextBlock
                                                    FontWeight="{Binding IsExecutingBatchCommand, TargetNullValue=Normal, FallbackValue=Normal}"
                                                    Foreground="#2E7D32"
                                                    Text="{Binding BatchCommandProgressInfo}"
                                                    TextWrapping="Wrap" />
                                            </Border>
                                        </StackPanel>
                                    </GroupBox>
                                </StackPanel>

                                <!--  展示命令执行结果  -->
                                <StackPanel Margin="0,5" Orientation="Vertical">
                                    <TextBlock
                                        Margin="0,5,0,5"
                                        FontWeight="Bold"
                                        Text="命令执行结果：" />
                                    <hc:TextBox
                                        Margin="0,5,0,5"
                                        hc:TitleElement.Title="运行信息"
                                        hc:TitleElement.TitlePlacement="Left"
                                        Foreground="#0066CC"
                                        IsReadOnly="True"
                                        Text="{Binding CommandRunInfo}"
                                        TextWrapping="Wrap" />
                                    <hc:TextBox
                                        Margin="0,5,0,5"
                                        hc:TitleElement.Title="返回信息"
                                        hc:TitleElement.TitlePlacement="Left"
                                        Foreground="#0066CC"
                                        IsReadOnly="True"
                                        Text="{Binding CommandReturnInfo}"
                                        TextWrapping="Wrap" />
                                </StackPanel>
                            </StackPanel>
                        </ScrollViewer>

                        <StackPanel Grid.Column="1">

                            <!--  右侧：Robot状态信息  -->
                            <!-- <views:RobotStatusPanel DataContext="{Binding UiViewModel}" /> -->

                            <!--  使用IntegerConversionView，包装在Expander中  -->
                            <Expander
                                Margin="0,10,0,10"
                                ExpandDirection="Up"
                                Header="32位和16位整数转换工具"
                                IsExpanded="False">
                                <GroupBox Margin="5" Header="整数转换工具">
                                    <Border CornerRadius="4">
                                        <views:IntegerConversionView />
                                    </Border>
                                </GroupBox>
                            </Expander>
                        </StackPanel>
                    </Grid>
                </GroupBox>
            </Grid>
        </Grid>
    </Grid>
</UserControl>