using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Services.Interfaces;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Utilities
{
    /// <summary>
    /// Modbus命令工具类，提供命令执行和参数创建的公共方法
    /// </summary>
    public static class ModbusCommandUtility
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ModbusCommandUtility));

        /// <summary>
        /// 创建包含步进值的参数列表
        /// </summary>
        /// <param name="steps">步进值</param>
        /// <param name="startSloope">起始斜率</param>
        /// <param name="stopSloope">终止斜率</param>
        /// <param name="runCurrent">运行电流</param>
        /// <returns>参数列表</returns>
        public static List<ushort> CreateStepParameters(
            int steps,
            ushort startSloope = 8000,
            ushort stopSloope = 8000,
            ushort runCurrent = 500)
        {
            List<ushort> parameters = new List<ushort>();

            // 无论步进值是否大于0，都需要生成5个参数
            ushort stepsHigh = (ushort)((steps >> 16) & 0xFFFF); // 高16位
            ushort stepsLow = (ushort)(steps & 0xFFFF);          // 低16位

            parameters.Add(stepsHigh);  // 位置高字
            parameters.Add(stepsLow);   // 位置低字
            parameters.Add(startSloope); // 起始斜率
            parameters.Add(stopSloope);  // 终止斜率
            parameters.Add(runCurrent);  // 运行电流

            return parameters;
        }

        /// <summary>
        /// 执行任意设备的任意MODBUS命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="deviceType">设备类型</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteDeviceCommand(
            IS200McuCmdService cmdService,
            EnuMcuDeviceType deviceType,
            string commandCode,
            List<ushort> parameters = null)
        {
            return await ExecuteDeviceCommand(cmdService, deviceType, commandCode, parameters, CancellationToken.None);
        }

        /// <summary>
        /// 执行任意设备的任意MODBUS命令（支持取消令牌）
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="deviceType">设备类型</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="parameters">参数列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteDeviceCommand(
            IS200McuCmdService cmdService,
            EnuMcuDeviceType deviceType,
            string commandCode,
            List<ushort> parameters,
            CancellationToken cancellationToken)
        {
            // 根据设备类型选择对应的枚举类型并执行命令
            switch (deviceType)
            {
                case EnuMcuDeviceType.Robot:
                    return await ExecuteCommandAsync<EnuRobotCmdIndex>(
                        cmdService, deviceType, commandCode, parameters, cancellationToken);

                case EnuMcuDeviceType.Shuttle:
                    return await ExecuteCommandAsync<EnuShuttleCmdIndex>(
                        cmdService, deviceType, commandCode, parameters, cancellationToken);

                case EnuMcuDeviceType.ChamberA:
                    return await ExecuteCommandAsync<EnuChaCmdIndex>(
                        cmdService, deviceType, commandCode, parameters, cancellationToken);

                case EnuMcuDeviceType.ChamberB:
                    return await ExecuteCommandAsync<EnuChbCmdIndex>(
                        cmdService, deviceType, commandCode, parameters, cancellationToken);

                default:
                    throw new ArgumentException($"不支持的设备类型: {deviceType}");
            }
        }

        /// <summary>
        /// 根据命令代码确定设备类型
        /// </summary>
        /// <param name="commandCode">命令代码</param>
        /// <returns>设备类型枚举</returns>
        public static EnuMcuDeviceType DetermineDeviceTypeFromCommand(string commandCode)
        {
            if (commandCode.StartsWith("AR") || commandCode.StartsWith("MoveMotor") ||
                commandCode.StartsWith("AlarmReset") || commandCode.StartsWith("MotorStop"))
            {
                return EnuMcuDeviceType.Robot;
            }
            else if (commandCode.StartsWith("S") && char.IsDigit(commandCode[1]))
            {
                return EnuMcuDeviceType.Shuttle;
            }
            else if (commandCode.StartsWith("OD_") || commandCode.StartsWith("CD_") ||
                     commandCode.StartsWith("PU") || commandCode.StartsWith("PD") ||
                     commandCode.StartsWith("OV_") || commandCode.StartsWith("CV_") ||
                     commandCode.StartsWith("OC") || commandCode.StartsWith("CC"))
            {
                // 这里需要进一步判断是Cha还是Chb
                // 由于命令代码不能明确区分，默认返回Cha
                // 实际使用时应明确指定设备类型
                return EnuMcuDeviceType.ChamberA;
            }

            // 如果无法确定，则抛出异常
            throw new ArgumentException($"无法从命令代码 {commandCode} 确定设备类型");
        }

        /// <summary>
        /// 通用泛型MCU MODBUS命令执行方法
        /// </summary>
        /// <typeparam name="TEnum">命令枚举类型</typeparam>
        /// <param name="cmdService">命令服务</param>
        /// <param name="deviceType">设备类型（Robot、Shuttle、ChamberA、ChamberB）</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数列表</param>
        /// <returns>执行结果</returns>
        private static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteCommandAsync<TEnum>(
            IS200McuCmdService cmdService,
            EnuMcuDeviceType deviceType,
            string commandCode,
            List<ushort> dynamicParameters = null
           ) where TEnum : Enum
        {
            return await ExecuteCommandAsync<TEnum>(cmdService, deviceType, commandCode, dynamicParameters, CancellationToken.None);
        }

        /// <summary>
        /// 通用泛型MCU MODBUS命令执行方法（支持取消令牌）
        /// </summary>
        /// <typeparam name="TEnum">命令枚举类型</typeparam>
        /// <param name="cmdService">命令服务</param>
        /// <param name="deviceType">设备类型（Robot、Shuttle、ChamberA、ChamberB）</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        private static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteCommandAsync<TEnum>(
            IS200McuCmdService cmdService,
            EnuMcuDeviceType deviceType,
            string commandCode,
            List<ushort> dynamicParameters,
            CancellationToken cancellationToken
           ) where TEnum : Enum
        {
            try
            {
                if (cmdService == null)
                {
                    return ("命令服务实例为空", 0, 0xFFFF);
                }

                // 格式化命令代码
                string formattedCommandCode = commandCode.Replace(" ", "_");

                // 获取对应的设备实例
                McuDevice device = GetDeviceFromService(cmdService, deviceType);
                if (device == null)
                {
                    return ($"无法获取设备: {deviceType}", 0, 0xFFFF);
                }

                if (!device.IsConnected)
                {
                    return ($"{deviceType}设备未连接，无法执行命令: {commandCode}", 0, 0xFFFF);
                }

                // 获取命令静态参数
                var commandConfig = device.GetCommandConfigByName(commandCode);

                _logger.Debug($"子命令 {commandCode} 的动态参数: [{string.Join(",", dynamicParameters ?? new List<ushort>())}]");

                // 将ConfigParameter列表转换为ushort列表
                List<ushort> staticParameters = new List<ushort>();
                if (commandConfig?.ConfigPara != null)
                {
                    staticParameters = commandConfig.ConfigPara
                        .Select(p => (ushort)p.Value)
                        .ToList();

                    _logger.Debug($"子命令 {commandCode} 的静态参数: [{string.Join(",", staticParameters)}]");
                }

                // 合并静态参数和动态参数
                List<ushort> combinedParams = new List<ushort>();
                combinedParams.AddRange(staticParameters);
                if (dynamicParameters != null)
                {
                    combinedParams.AddRange(dynamicParameters);
                }

                _logger.Debug($"子命令 {commandCode} 的合并参数: [{string.Join(",", combinedParams)}]");

                // 将命令代码转换为枚举值
                TEnum cmdEnum;
                try
                {
                    // 首先尝试直接解析枚举名称
                    cmdEnum = (TEnum)Enum.Parse(typeof(TEnum), formattedCommandCode);
                    _logger.Debug($"成功将命令 {commandCode} 解析为枚举 {typeof(TEnum).Name}::{cmdEnum}");
                }
                catch
                {
                    _logger.Debug($"无法直接解析命令 {commandCode} 为枚举，尝试使用配置索引");
                    // 如果无法直接解析，尝试通过配置中的索引值创建枚举
                    if (commandConfig != null)
                    {
                        int cmdIndex = commandConfig.CMDIndex;
                        _logger.Debug($"命令 {commandCode} 的配置索引值为: {cmdIndex}");

                        if (Enum.IsDefined(typeof(TEnum), (ushort)cmdIndex))
                        {
                            cmdEnum = (TEnum)Enum.ToObject(typeof(TEnum), (ushort)cmdIndex);
                            _logger.Debug($"成功通过索引 {cmdIndex} 创建枚举 {typeof(TEnum).Name}::{cmdEnum}");
                        }
                        else
                        {
                            _logger.Error($"索引值 {cmdIndex} 在枚举 {typeof(TEnum).Name} 中未定义");
                            return ($"无法将命令代码 {commandCode} 的索引 {cmdIndex} 转换为枚举值", 0, 0xFFFF);
                        }
                    }
                    else
                    {
                        _logger.Error($"命令 {commandCode} 没有配置，且无法解析为枚举名");
                        return ($"无法解析命令代码 {commandCode} (未找到配置且无法匹配枚举名)", 0, 0xFFFF);
                    }
                }
                int timeOut = commandConfig?.TimeOut ?? Golbal.CommandRunTimeout;
                if (Golbal.IsDevDebug)
                {
                    timeOut = Golbal.DebugCommandRunTimeout;
                }
                // 执行命令
                return await device.Run(cmdEnum, combinedParams, timeOut, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.Error($"执行子命令 {commandCode} 时发生错误: {ex.Message}", ex);
                return ($"执行子命令失败: {ex.Message}", 0, 0xFFFF);
            }
        }

        /// <summary>
        /// 根据枚举设备类型获取对应的配置参数
        /// </summary>
        /// <param name="enuMcuDeviceType"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static string GetDeviceConfigFileName(EnuMcuDeviceType enuMcuDeviceType)
        {
            // 获取配置文件路径
            string configFileName = enuMcuDeviceType switch
            {
                EnuMcuDeviceType.Shuttle => "ShuttleParameter.json",
                EnuMcuDeviceType.Robot => "RobotParameter.json",
                EnuMcuDeviceType.ChamberA => "ChamberAParameter.json",
                EnuMcuDeviceType.ChamberB => "ChamberBParameter.json",
                _ => throw new ArgumentException($"未知的设备类型: {enuMcuDeviceType}")
            };
            return configFileName;
        }

        /// <summary>
        /// 根据设备类型获取设备实例
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="deviceType">设备类型</param>
        /// <returns>设备实例</returns>
        private static McuDevice GetDeviceFromService(IS200McuCmdService cmdService, EnuMcuDeviceType deviceType)
        {
            return deviceType switch
            {
                EnuMcuDeviceType.Robot => cmdService.Robot,
                EnuMcuDeviceType.Shuttle => cmdService.Shuttle,
                EnuMcuDeviceType.ChamberA => cmdService.ChamberA,
                EnuMcuDeviceType.ChamberB => cmdService.ChamberB,
                _ => throw new ArgumentException($"未知的设备类型: {deviceType}")
            };
        }
    }
}