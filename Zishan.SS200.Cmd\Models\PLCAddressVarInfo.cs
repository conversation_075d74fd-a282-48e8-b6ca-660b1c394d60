﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Prism.Commands;
using Prism.Ioc;
using Prism.Mvvm;

// using Prism.Regions;
using SqlSugar;

namespace Zishan.SS200.Cmd.Models
{
    /// <summary>
    /// PLC地址变量信息
    /// </summary>
    [SugarTable("PLCAddressVarInfo", TableDescription = "PLC地址变量信息")]
    public class PLCAddressVarInfo : BindableBase
    {
        private int _Id;
        private string _Code = string.Empty;
        private string _Name = string.Empty;
        private string _DescriptionChs = string.Empty;
        private string _DescriptionEn = string.Empty;
        private string _Module = string.Empty;
        private string _ModuleInType = string.Empty;
        private string _PLC_AddressVar = string.Empty;
        private string _PLC_AddressVarUnit = string.Empty;

        //private double _PLC_AddressVarButtonValue;
        //private int _PLC_AddressVarValueInt;
        //private string _PLC_AddressVarValueStr;
        private object _PLC_AddressVarValue = null!;

        private string _PLC_AddressType = string.Empty;
        private string _ControlType = string.Empty;
        private bool _UiIsValidationRequired;
        private double _UiMinimum;
        private double _UiMaximum;

        /// <summary>
        /// ID号
        /// </summary>
        [SugarColumn(IsIdentity = true, IsPrimaryKey = true, ColumnDescription = "ID号")]
        public int Id
        {
            get { return _Id; }
            set { SetProperty(ref _Id, value); }
        }

        /// <summary>
        /// 命令代码
        /// </summary>
        [SugarColumn(ColumnDescription = "命令代码")]
        public string Code
        {
            get { return _Code; }
            set { SetProperty(ref _Code, value); }
        }

        /// <summary>
        /// 命令名称
        /// </summary>
        [SugarColumn(ColumnDescription = "命令名称", IsNullable = true)]
        public string Name
        {
            get { return _Name; }
            set { SetProperty(ref _Name, value); }
        }

        /// <summary>
        /// 命令名称中文描述
        /// </summary>
        [SugarColumn(ColumnDescription = "命令名称中文描述", IsNullable = true)]
        public string DescriptionChs
        {
            get { return _DescriptionChs; }
            set { SetProperty(ref _DescriptionChs, value); }
        }

        /// <summary>
        /// 命令名称英文描述
        /// </summary>
        [SugarColumn(ColumnDescription = "命令名称英文描述", IsNullable = true)]
        public string DescriptionChsEn
        {
            get { return _DescriptionEn; }
            set { SetProperty(ref _DescriptionEn, value); }
        }

        /// <summary>
        /// 命令类型：Display、Operation、Action
        /// </summary>
        [SugarColumn(ColumnDescription = "命令类型：Display、Operation、Action", IsNullable = true)]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 所属模块
        /// </summary>
        [SugarColumn(ColumnDescription = "所属模块", IsNullable = true)]
        public string Module
        {
            get { return _Module; }
            set { SetProperty(ref _Module, value); }
        }

        /// <summary>
        /// 所属模块内部分类
        /// </summary>
        [SugarColumn(ColumnDescription = "所属模块内部分类", IsNullable = true)]
        public string ModuleInType
        {
            get { return _ModuleInType; }
            set { SetProperty(ref _ModuleInType, value); }
        }

        /// <summary>
        /// PLC地址变量
        /// </summary>
        [SugarColumn(ColumnDescription = "PLC地址变量", IsNullable = true)]
        public string PLC_AddressVar
        {
            get { return _PLC_AddressVar; }
            set { SetProperty(ref _PLC_AddressVar, value); }
        }

        /// <summary>
        /// PLC地址变量值单位
        /// </summary>
        [SugarColumn(ColumnDescription = "PLC地址变量值单位", IsNullable = true)]
        public string PLC_AddressVarUnit
        {
            get { return _PLC_AddressVarUnit; }
            set { SetProperty(ref _PLC_AddressVarUnit, value); }
        }

        #region 扩展字段，用于值PLC变量值保存

        ///// <summary>
        ///// PLC地址变量对应的值【double 型】，数据库忽略
        ///// </summary>
        //[SugarColumn(ColumnDescription = "PLC地址变量对应的值，类型不确定", IsIgnore = true)]
        //public double PLC_AddressVarButtonValue
        //{
        //    get { return _PLC_AddressVarButtonValue; }
        //    set { _PLC_AddressVarButtonValue = value; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// PLC地址变量对应的值【int 型】，数据库忽略
        ///// </summary>
        //[SugarColumn(ColumnDescription = "PLC地址变量对应的值，类型不确定", IsIgnore = true)]
        //public int PLC_AddressVarValueInt
        //{
        //    get { return _PLC_AddressVarValueInt; }
        //    set { _PLC_AddressVarValueInt = value; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// PLC地址变量对应的值【string 型】，数据库忽略
        ///// </summary>
        //[SugarColumn(ColumnDescription = "PLC地址变量对应的值，类型不确定", IsIgnore = true)]
        //public string PLC_AddressVarValueStr
        //{
        //    get { return _PLC_AddressVarValueStr; }
        //    set { _PLC_AddressVarValueStr = value; RaisePropertyChanged(); }
        //}

        //表结构设计，保存PLC值：
        //DataType ENUM('int', 'double', 'double[]', 'MyStruct', ...),  -- 列出所有支持的数据类型
        //RawValue BLOB  -- 用于保存变量的原始二进制值，还是推荐使用JSON格式保存结构化数据

        // 并行读取初始值，设置了MaxDegreeOfParallelism为4，这意味着最多有4个并行任务会同时运行。
        //Parallel.ForEach(variables, new ParallelOptions { MaxDegreeOfParallelism = 4 }, variable =>
        //{
        //    // ...
        //});

        /// <summary>
        /// PLC地址变量对应的值【object 型】，数据库忽略
        /// </summary>
        [SugarColumn(ColumnDescription = "PLC地址变量对应的值，类型不确定", IsIgnore = true)]
        public object PLC_AddressVarValue
        {
            get { return _PLC_AddressVarValue; }
            set { SetProperty(ref _PLC_AddressVarValue, value); }
        }

        #region 前端校验

        [SugarColumn(ColumnDescription = "扩展字段，前端校验用", IsIgnore = true)]
        public bool UiIsValidationRequired
        {
            get { return _UiIsValidationRequired; }
            set { SetProperty(ref _UiIsValidationRequired, value); }
        }

        [SugarColumn(ColumnDescription = "扩展字段，前端校验用", IsIgnore = true)]
        public double UiMinimum
        {
            get { return _UiMinimum; }
            set { SetProperty(ref _UiMinimum, value); }
        }

        [SugarColumn(ColumnDescription = "扩展字段，前端校验用", IsIgnore = true)]
        public double UiMaximum
        {
            get { return _UiMaximum; }
            set { SetProperty(ref _UiMaximum, value); }
        }

        #endregion 前端校验

        #endregion 扩展字段，用于值PLC变量值保存

        /// <summary>
        /// PLC地址变量类型
        /// </summary>
        [SugarColumn(ColumnDescription = "PLC地址变量类型", IsNullable = true)]
        public string PLC_AddressType
        {
            get { return _PLC_AddressType; }
            set { SetProperty(ref _PLC_AddressType, value); }
        }

        /// <summary>
        /// 控件类型，界面上所展示的
        /// </summary>
        [SugarColumn(ColumnDescription = "控件类型，界面上所展示的", IsNullable = true)]
        public string ControlType
        {
            get { return _ControlType; }
            set { SetProperty(ref _ControlType, value); }
        }

        /// <summary>
        /// 是否有效，有效为‘Y’，无效为‘N’
        /// </summary>
        [SugarColumn(ColumnDescription = "是否有效")]
        public string Valid { get; set; } = "Y";

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(ColumnDescription = "排序号")]
        public int? SortCode { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [SugarColumn(ColumnDescription = "备注信息")]
        public string? Remark { get; set; }

        ///// <summary>
        ///// 本地电脑IP地址，ASPEN设备没有ID地址，暂时用IP替代
        ///// </summary>
        //[SugarColumn(ColumnDescription = "本地电脑IP地址，ASPEN设备没有ID地址，暂时用IP替代")]
        //public String ClientIp { get; set; }

        ///// <summary>
        ///// 本地电脑IP地址，ASPEN设备没有ID地址，对应的串口、网络Port，例如：COM7、COM8、8001
        ///// </summary>
        //[SugarColumn(ColumnDescription = "本地电脑IP地址，ASPEN设备没有ID地址，暂时用IP替代")]
        //public String ClientComPort { get; set; }

        ///// <summary>
        ///// 客户端ASPEN厂商自定义设备名：本地电脑IP地址+对应的串口、网络Port，例如：A2301_某某1、A2302_某某2、A2303_某某3
        ///// </summary>
        //[SugarColumn(ColumnDescription = "本地电脑IP地址，ASPEN设备没有ID地址，暂时用IP替代")]
        //public String ClientName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", InsertServerTime = true)]
        public DateTime CreateTime { get; set; }
    }
}