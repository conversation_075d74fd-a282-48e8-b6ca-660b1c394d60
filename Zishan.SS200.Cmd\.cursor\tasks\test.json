{"version": "2.0.0", "tasks": [{"label": "test", "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/Zishan.SS200.Cmd.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "test", "isDefault": true}}, {"label": "test-with-coverage", "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/Zishan.SS200.Cmd.sln", "/p:CollectCoverage=true", "/p:CoverletOutputFormat=lcov", "/p:CoverletOutput=./lcov.info"], "problemMatcher": "$msCompile"}, {"label": "test-specific", "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/Zishan.SS200.Cmd.sln", "--filter", "Category=${input:testCategory}"], "problemMatcher": "$msCompile"}], "inputs": [{"id": "testCategory", "type": "pickString", "description": "选择测试类别", "options": ["UnitTest", "IntegrationTest", "UITest", "PerformanceTest"], "default": "UnitTest"}]}