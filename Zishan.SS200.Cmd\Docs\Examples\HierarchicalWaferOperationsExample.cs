using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Enums.Basic;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// 层次化日志在晶圆操作中的应用示例
    /// 展示如何在实际的晶圆传输代码中使用层次化日志
    /// </summary>
    public static class HierarchicalWaferOperationsExample
    {
        /// <summary>
        /// 带有层次化日志的晶圆传输方法示例
        /// 这是对原有TrasferWaferAsync方法的改进版本
        /// </summary>
        public static async Task<(bool Success, string Message)> TrasferWaferWithHierarchicalLoggingAsync(
            EnuRobotEndType endType,
            EnuLocationStationType sourceStationType,
            EnuLocationStationType targetStationType,
            int sourceSlotNumber = 0,
            int targetSlotNumber = 0)
        {
            UILogService.AddLogAndIncreaseIndent($"开始执行晶圆传输: {sourceStationType}(Slot:{sourceSlotNumber}) → {targetStationType}(Slot:{targetSlotNumber})");
            
            try
            {
                // 1. 机器人初始化
                UILogService.AddLogAndIncreaseIndent("第一步：机器人初始化");
                UILogService.AddLog($"端口类型: {endType}");
                var initResult = await InitializeRobotWithHierarchicalLoggingAsync();
                if (!initResult.Success)
                {
                    UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化失败: {initResult.Message}");
                    UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输失败: {initResult.Message}");
                    return (false, $"TrasferWafer失败: {initResult.Message}");
                }
                UILogService.DecreaseIndentAndAddSuccessLog("机器人初始化成功");

                // 2. 执行GetWafer操作
                UILogService.AddLogAndIncreaseIndent($"第二步：从{sourceStationType}(Slot:{sourceSlotNumber})获取晶圆");
                var getResult = await GetWaferWithHierarchicalLoggingAsync(endType, sourceStationType, sourceSlotNumber);
                if (!getResult.Success)
                {
                    UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {getResult.Message}");
                    UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输失败: {getResult.Message}");
                    return (false, $"TrasferWafer失败: {getResult.Message}");
                }
                UILogService.DecreaseIndentAndAddSuccessLog($"从{sourceStationType}(Slot:{sourceSlotNumber})获取晶圆成功");

                // 3. 执行PutWafer操作
                UILogService.AddLogAndIncreaseIndent($"第三步：将晶圆放置到{targetStationType}(Slot:{targetSlotNumber})");
                var putResult = await PutWaferWithHierarchicalLoggingAsync(endType, targetStationType, targetSlotNumber);
                if (!putResult.Success)
                {
                    UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {putResult.Message}");
                    UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输失败: {putResult.Message}");
                    return (false, $"TrasferWafer失败: {putResult.Message}");
                }
                UILogService.DecreaseIndentAndAddSuccessLog($"将晶圆放置到{targetStationType}(Slot:{targetSlotNumber})成功");

                UILogService.DecreaseIndentAndAddSuccessLog($"晶圆传输成功: {sourceStationType}(Slot:{sourceSlotNumber}) → {targetStationType}(Slot:{targetSlotNumber})");
                return (true, "晶圆传输成功");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输异常: {ex.Message}");
                return (false, $"TrasferWafer操作异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 带有层次化日志的获取晶圆方法示例
        /// </summary>
        private static async Task<(bool Success, string Message)> GetWaferWithHierarchicalLoggingAsync(
            EnuRobotEndType endType,
            EnuLocationStationType sourceStationType,
            int slotNumber = 0)
        {
            try
            {
                UILogService.AddLogAndIncreaseIndent($"开始获取晶圆操作 (端口: {endType}, Slot: {slotNumber})");

                // 1. 位置检查
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"检查{sourceStationType}位置是否有晶圆...");
                    await Task.Delay(200);
                    UILogService.AddSuccessLog($"{sourceStationType}位置确认有晶圆");
                }

                // 2. 机械臂检查
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("确认Robot机械臂上无晶圆...");
                    await Task.Delay(200);
                    UILogService.AddSuccessLog("Robot机械臂确认无晶圆");
                }

                // 3. T轴移动
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"移动T轴到{sourceStationType}位置...");
                    await Task.Delay(300);
                    UILogService.AddSuccessLog($"T轴移动到{sourceStationType}位置完成");
                }

                // 4. R轴移动
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"移动R轴到{sourceStationType}位置...");
                    await Task.Delay(300);
                    UILogService.AddSuccessLog($"R轴移动到{sourceStationType}位置完成");
                }

                // 5. Z轴操作（根据不同类型）
                if (sourceStationType == EnuLocationStationType.ChamberA || sourceStationType == EnuLocationStationType.ChamberB)
                {
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog($"{sourceStationType} 顶针上升取片...");
                        await Task.Delay(400);
                        UILogService.AddSuccessLog($"{sourceStationType} 顶针上升取片完成");
                    }
                }
                else
                {
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog($"Z轴下降到{sourceStationType} Slot {slotNumber}位置...");
                        await Task.Delay(400);
                        UILogService.AddLog("Z轴上升取片...");
                        await Task.Delay(300);
                        UILogService.AddSuccessLog($"Z轴取片操作完成");
                    }
                }

                // 6. R轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("R轴归零...");
                    await Task.Delay(300);
                    UILogService.AddSuccessLog("R轴归零完成");
                }

                UILogService.DecreaseIndentAndAddSuccessLog($"从{sourceStationType}获取晶圆成功");
                return (true, "GetWafer操作成功");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"从{sourceStationType}获取晶圆异常: {ex.Message}");
                return (false, $"GetWafer操作异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 带有层次化日志的放置晶圆方法示例
        /// </summary>
        private static async Task<(bool Success, string Message)> PutWaferWithHierarchicalLoggingAsync(
            EnuRobotEndType endType,
            EnuLocationStationType targetStationType,
            int slotNumber = 0)
        {
            try
            {
                UILogService.AddLogAndIncreaseIndent($"开始放置晶圆操作 (端口: {endType}, Slot: {slotNumber})");

                // 1. 位置检查
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"检查{targetStationType}位置是否无晶圆...");
                    await Task.Delay(200);
                    UILogService.AddSuccessLog($"{targetStationType}位置确认无晶圆");
                }

                // 2. 机械臂检查
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("确认Robot机械臂上有晶圆...");
                    await Task.Delay(200);
                    UILogService.AddSuccessLog("Robot机械臂上确认有晶圆");
                }

                // 3. T轴移动
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"移动T轴到{targetStationType}位置...");
                    await Task.Delay(300);
                    UILogService.AddSuccessLog($"T轴移动到{targetStationType}位置完成");
                }

                // 4. R轴移动
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"移动R轴到{targetStationType}位置...");
                    await Task.Delay(300);
                    UILogService.AddSuccessLog($"R轴移动到{targetStationType}位置完成");
                }

                // 5. Z轴操作（根据不同类型）
                if (targetStationType == EnuLocationStationType.ChamberA || targetStationType == EnuLocationStationType.ChamberB)
                {
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog($"{targetStationType} 顶针下降放片...");
                        await Task.Delay(400);
                        UILogService.AddSuccessLog($"{targetStationType} 顶针下降放片完成");
                    }
                }
                else
                {
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog($"Z轴下降到{targetStationType} Slot {slotNumber}位置放片...");
                        await Task.Delay(400);
                        UILogService.AddLog("Z轴上升...");
                        await Task.Delay(300);
                        UILogService.AddSuccessLog($"Z轴放片操作完成");
                    }
                }

                // 6. R轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("R轴归零...");
                    await Task.Delay(300);
                    UILogService.AddSuccessLog("R轴归零完成");
                }

                UILogService.DecreaseIndentAndAddSuccessLog($"将晶圆放置到{targetStationType}成功");
                return (true, "PutWafer操作成功");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"将晶圆放置到{targetStationType}异常: {ex.Message}");
                return (false, $"PutWafer操作异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 演示复杂的多步骤晶圆处理流程
        /// </summary>
        public static async Task<bool> ComplexWaferProcessingWorkflowAsync()
        {
            UILogService.AddLogAndIncreaseIndent("开始复杂晶圆处理工作流程");

            try
            {
                // 第一步：从晶圆盒取片到ChamberA
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("步骤1：从晶圆盒取片到ChamberA进行处理");
                    var result1 = await TrasferWaferWithHierarchicalLoggingAsync(
                        EnuRobotEndType.Nose,
                        EnuLocationStationType.Cassette,
                        EnuLocationStationType.ChamberA,
                        5, 0);
                    
                    if (!result1.Success)
                    {
                        UILogService.AddErrorLog($"步骤1失败: {result1.Message}");
                        return false;
                    }
                    UILogService.AddSuccessLog("步骤1完成");
                }

                // 第二步：处理延时
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("步骤2：ChamberA处理晶圆");
                    UILogService.AddLog("开始工艺处理...");
                    await Task.Delay(2000); // 模拟处理时间
                    UILogService.AddSuccessLog("工艺处理完成");
                }

                // 第三步：从ChamberA转移到ChamberB
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("步骤3：从ChamberA转移到ChamberB");
                    var result2 = await TrasferWaferWithHierarchicalLoggingAsync(
                        EnuRobotEndType.Nose,
                        EnuLocationStationType.ChamberA,
                        EnuLocationStationType.ChamberB,
                        0, 0);
                    
                    if (!result2.Success)
                    {
                        UILogService.AddErrorLog($"步骤3失败: {result2.Message}");
                        return false;
                    }
                    UILogService.AddSuccessLog("步骤3完成");
                }

                // 第四步：从ChamberB到冷却腔
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("步骤4：从ChamberB转移到冷却腔");
                    var result3 = await TrasferWaferWithHierarchicalLoggingAsync(
                        EnuRobotEndType.Smooth,
                        EnuLocationStationType.ChamberB,
                        EnuLocationStationType.CoolingTop,
                        0, 1);
                    
                    if (!result3.Success)
                    {
                        UILogService.AddErrorLog($"步骤4失败: {result3.Message}");
                        return false;
                    }
                    UILogService.AddSuccessLog("步骤4完成");
                }

                UILogService.DecreaseIndentAndAddSuccessLog("复杂晶圆处理工作流程全部完成");
                return true;
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"工作流程异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 带有层次化日志的机器人初始化方法示例
        /// 展示实际InitializeRobotAsync方法的层次化日志效果
        /// </summary>
        private static async Task<(bool Success, string Message)> InitializeRobotWithHierarchicalLoggingAsync()
        {
            // 使用层次化日志记录机器人初始化流程
            UILogService.AddLogAndIncreaseIndent("开始初始化机器人（三轴归零）");

            try
            {
                // 1. T轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行T轴归零");
                    await Task.Delay(300); // 模拟T轴归零时间
                    UILogService.AddSuccessLog("T轴归零成功");
                }

                // 2. R轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行R轴归零");
                    await Task.Delay(300); // 模拟R轴归零时间
                    UILogService.AddSuccessLog("R轴归零成功");
                }

                // 3. Z轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行Z轴归零");
                    await Task.Delay(300); // 模拟Z轴归零时间
                    UILogService.AddSuccessLog("Z轴归零成功");
                }

                UILogService.DecreaseIndentAndAddSuccessLog("机器人三轴归零成功");
                return (true, "机器人三轴归零成功");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化异常: {ex.Message}");
                return (false, $"机器人初始化异常: {ex.Message}");
            }
        }
    }
}
