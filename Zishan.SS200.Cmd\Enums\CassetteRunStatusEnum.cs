﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// Cassette运行状态，确定以后请勿重命名，否则对应的数据库表内容要修改： NONE、WAIT_CST_PLACED、DOUBLE_CHECK、RECIPE_SELECT、PROCESSING、UNLOADING、WAIT_REMOVE
    /// </summary>
    public enum CassetteRunStatusEnum
    {
        /// <summary>
        ///  初始无状态
        /// </summary>
        NONE = 0,

        /// <summary>
        /// 等待放置
        /// </summary>
        WAIT_CST_PLACED = 1,

        /// <summary>
        /// 二次确认
        /// </summary>
        DOUBLE_CHECK = 2,

        /// <summary>
        /// 配方选择
        /// </summary>
        RECIPE_SELECT = 3,

        /// <summary>
        /// 正在处理
        /// </summary>
        PROCESSING = 4,

        /// <summary>
        /// 准备卸载
        /// </summary>
        UNLOADING = 5,

        /// <summary>
        /// 等待移除
        /// </summary>
        WAIT_REMOVE = 6,

        /// <summary>
        /// 用于UI界面查询
        /// </summary>
        ALL = 999
    }
}