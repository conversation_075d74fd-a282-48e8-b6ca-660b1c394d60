using System;
using System.IO;
using log4net;
using log4net.Config;

namespace Zishan.SS200.Cmd.Test
{
    /// <summary>
    /// Log4net异步配置测试类
    /// </summary>
    public class Log4netAsyncConfigTest
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(Log4netAsyncConfigTest));

        /// <summary>
        /// 测试指定配置文件
        /// </summary>
        /// <param name="configFileName">配置文件名</param>
        public static void TestConfigFile(string configFileName)
        {
            Console.WriteLine($"\n=== 测试配置文件: {configFileName} ===");
            
            try
            {
                // 构建配置文件路径
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                    "Configs", "Log4netConfig", configFileName);
                
                if (!File.Exists(configPath))
                {
                    Console.WriteLine($"配置文件不存在: {configPath}");
                    return;
                }
                
                Console.WriteLine($"配置文件路径: {configPath}");
                
                // 加载配置
                XmlConfigurator.Configure(new FileInfo(configPath));
                Console.WriteLine("配置加载成功");
                
                // 测试各级别日志输出
                TestLogOutput(configFileName);
                
                Console.WriteLine($"测试完成: {configFileName}");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试日志输出
        /// </summary>
        /// <param name="configName">配置名称</param>
        private static void TestLogOutput(string configName)
        {
            try
            {
                Console.WriteLine("开始日志输出测试...");
                
                _logger.Debug($"[{configName}] 这是DEBUG级别日志 - 调试信息");
                _logger.Info($"[{configName}] 这是INFO级别日志 - 一般信息");
                _logger.Warn($"[{configName}] 这是WARN级别日志 - 警告信息");
                _logger.Error($"[{configName}] 这是ERROR级别日志 - 错误信息");
                
                // 测试性能日志
                var perfLogger = LogManager.GetLogger("Performance");
                perfLogger.Info($"[{configName}] 性能测试日志");
                
                Console.WriteLine("日志输出测试完成");
                
                // 检查日志文件是否生成
                CheckLogFiles();
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"日志输出测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查日志文件是否生成
        /// </summary>
        private static void CheckLogFiles()
        {
            try
            {
                var logsDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                
                if (!Directory.Exists(logsDir))
                {
                    Console.WriteLine("日志目录不存在");
                    return;
                }
                
                Console.WriteLine("\n检查生成的日志文件:");
                var logFiles = Directory.GetFiles(logsDir, "*.log");
                
                if (logFiles.Length == 0)
                {
                    Console.WriteLine("未找到日志文件");
                    return;
                }
                
                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    Console.WriteLine($"- {Path.GetFileName(logFile)} ({fileInfo.Length} 字节, {fileInfo.LastWriteTime})");
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查日志文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试所有异步配置文件
        /// </summary>
        public static void TestAllAsyncConfigs()
        {
            Console.WriteLine("=== 测试所有异步配置文件 ===");
            
            // 测试开发环境配置
            TestConfigFile("log4net-development.config");
            
            // 等待一下，确保异步日志写入完成
            System.Threading.Thread.Sleep(1000);
            
            // 测试生产环境配置
            TestConfigFile("log4net-production.config");
            
            // 等待一下
            System.Threading.Thread.Sleep(1000);
            
            // 测试优化配置
            TestConfigFile("log4net-optimized.config");
            
            // 等待异步日志完成
            System.Threading.Thread.Sleep(2000);
            
            Console.WriteLine("\n=== 所有测试完成 ===");
        }

        /// <summary>
        /// 对比测试：原始配置 vs 异步配置
        /// </summary>
        public static void CompareConfigs()
        {
            Console.WriteLine("=== 对比测试：原始配置 vs 异步配置 ===");
            
            // 测试原始配置
            Console.WriteLine("\n--- 测试原始配置 ---");
            TestConfigFile("log4net.config");
            System.Threading.Thread.Sleep(1000);
            
            // 测试开发环境异步配置
            Console.WriteLine("\n--- 测试开发环境异步配置 ---");
            TestConfigFile("log4net-development.config");
            System.Threading.Thread.Sleep(2000);
            
            Console.WriteLine("\n=== 对比测试完成 ===");
        }

        /// <summary>
        /// 清理日志文件
        /// </summary>
        public static void CleanupLogFiles()
        {
            try
            {
                var logsDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                
                if (Directory.Exists(logsDir))
                {
                    var logFiles = Directory.GetFiles(logsDir, "*.log");
                    foreach (var logFile in logFiles)
                    {
                        File.Delete(logFile);
                    }
                    Console.WriteLine($"已清理 {logFiles.Length} 个日志文件");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理日志文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 主测试方法
        /// </summary>
        public static void Main(string[] args)
        {
            Console.WriteLine("Log4net异步配置测试工具");
            Console.WriteLine("========================");
            
            if (args.Length > 0)
            {
                // 测试指定的配置文件
                TestConfigFile(args[0]);
            }
            else
            {
                // 测试所有配置
                TestAllAsyncConfigs();
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
