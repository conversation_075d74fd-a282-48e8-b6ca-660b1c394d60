using System;
using <PERSON><PERSON>an.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Enums.Basic;

namespace Zishan.SS200.Cmd.Constants.SubsystemConfigure.Robot
{
    /// <summary>
    /// 机器人位置参数常量
    /// </summary>
    public static class RobotPositionParameters
    {
        /// <summary>
        /// 根据端口类型和站点类型获取T轴位置参数
        /// </summary>
        /// <param name="endType">机器人端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>对应的T轴位置参数</returns>
        public static int GetTAxisPosition(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            return RobotPositionParametersProvider.Instance.GetTAxisPosition(endType, stationType);
        }

        /// <summary>
        /// 获取T轴零位参数
        /// </summary>
        /// <returns>T轴零位参数</returns>
        public static int GetTAxisZeroPosition()
        {
            return RobotPositionParametersProvider.Instance.GetTAxisZeroPosition();
        }

        /// <summary>
        /// 根据端口类型和站点类型获取R轴位置参数
        /// </summary>
        /// <param name="endType">机器人端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>对应的R轴位置参数</returns>
        public static int GetRAxisPosition(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            return RobotPositionParametersProvider.Instance.GetRAxisPosition(endType, stationType);
        }

        /// <summary>
        /// 获取R轴零位参数
        /// </summary>
        /// <returns>R轴零位参数</returns>
        public static int GetRAxisZeroPosition()
        {
            return RobotPositionParametersProvider.Instance.GetRAxisZeroPosition();
        }

        /// <summary>
        /// 根据端口类型和站点类型获取Z轴取片位置参数【只针对非cassette】
        /// </summary>
        /// <param name="endType">机器人端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>对应的Z轴取片位置参数</returns>
        public static int GetZAxisGetPosition(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            return RobotPositionParametersProvider.Instance.GetZAxisGetPosition(endType, stationType);
        }

        /// <summary>
        /// 获取Z轴零位参数
        /// </summary>
        /// <returns>Z轴零位参数</returns>
        public static int GetZAxisZeroPosition()
        {
            return RobotPositionParametersProvider.Instance.GetZAxisZeroPosition();
        }

        /// <summary>
        /// 获取Z轴做Pin Search需要的初始高度
        /// </summary>
        /// <returns>Z轴做Pin Search需要的初始高度</returns>
        public static int GetZAxisPinSearchPosition()
        {
            return RobotPositionParametersProvider.Instance.GetZAxisPinSearchPosition();
        }
    }
}