{"version": "0.2.0", "configurations": [{"name": "启动", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net8.0/Zishan.SS200.Cmd.exe", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart"}, {"name": "附加到进程", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}"}, {"name": "调试单元测试", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "dotnet", "args": ["test", "${workspaceFolder}/Zishan.SS200.Cmd.sln", "--filter", "${input:testFilter}"], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart"}], "inputs": [{"id": "testFilter", "type": "promptString", "description": "输入测试过滤器表达式", "default": "FullyQualifiedName~UnitTest"}]}