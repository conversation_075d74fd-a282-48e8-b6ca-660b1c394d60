﻿namespace Zishan.SS200.Cmd.Models.IR400
{
    public class RobotCmdUsage
    {
        public string Cmd { get; set; }

        public string CmdDesc { get; set; }

        public string CmdFormat { get; set; }

        public string CmdExample { get; set; }
        public string CmdComment { get; set; }

        public string CmdUsage
        {
            get
            {
                return $"命令：{Cmd}\r\n命令描述：{CmdDesc}\r\n命令格式：{CmdFormat}\r\n示例：{CmdExample}\r\n备注：{CmdComment}";
            }
        }

        public override string ToString()
        {
            return $"Cmd={Cmd}\r\nCmdDesc={CmdDesc}\r\nCmdFormat={CmdFormat}\r\nCmdExample={CmdExample}\r\nCmdComment={CmdComment}";
        }
    }
}