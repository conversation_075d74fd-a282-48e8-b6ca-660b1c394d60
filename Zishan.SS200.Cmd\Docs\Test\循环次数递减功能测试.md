# 循环次数递减功能测试

## 测试目的

验证新实现的循环次数递减功能是否按预期工作，确保：
1. 正整数循环次数能够正确递减
2. -1无限循环保持不变
3. UI显示正确更新
4. 循环在剩余次数为0时正确停止

## 测试用例

### 测试用例1：有限循环递减测试
**目标**: 验证正整数循环次数的递减功能

**步骤**:
1. 打开BasicCommandTest界面
2. 在"公共循环次数"输入框中输入：`3`
3. 观察"剩余次数"显示是否为：`剩余: 3次`
4. 点击"Pin Search测试"按钮
5. 确认安全对话框
6. 观察执行过程中的剩余次数变化

**预期结果**:
- 初始显示：`剩余: 3次`
- 第1次执行后：`剩余: 2次`
- 第2次执行后：`剩余: 1次`
- 第3次执行后：`已完成`
- 循环自动停止

### 测试用例2：无限循环测试
**目标**: 验证-1无限循环功能保持不变

**步骤**:
1. 在"公共循环次数"输入框中输入：`-1`
2. 观察"剩余次数"显示是否为：`无限循环`
3. 点击"Pin Search测试"按钮
4. 确认安全对话框
5. 观察执行过程中的显示
6. 点击"公共停止循环"按钮停止

**预期结果**:
- 始终显示：`无限循环`
- 循环持续执行直到手动停止
- 停止后显示取消信息

### 测试用例3：单次执行测试
**目标**: 验证默认单次执行功能

**步骤**:
1. 在"公共循环次数"输入框中输入：`1`
2. 观察"剩余次数"显示是否为：`剩余: 1次`
3. 点击"搬运"按钮
4. 配置搬运参数并确认
5. 观察执行完成后的显示

**预期结果**:
- 初始显示：`剩余: 1次`
- 执行完成后：`已完成`
- 不会进入循环

### 测试用例4：大数值循环测试
**目标**: 验证较大循环次数的处理

**步骤**:
1. 在"公共循环次数"输入框中输入：`10`
2. 观察"剩余次数"显示
3. 点击"Pin Search测试"按钮
4. 执行几次后点击"公共停止循环"
5. 观察停止时的剩余次数

**预期结果**:
- 正确显示递减过程
- 手动停止时显示正确的剩余次数
- 日志记录准确

## 日志验证点

### 1. 循环开始日志
```
开始执行机器人 PinSearch 测试 - 循环模式: 3次
```

### 2. 循环进度日志
```
=== 第1次 (剩余3次) PinSearch 测试开始 ===
=== 第1次 (剩余3次) PinSearch 测试完成 ===
剩余循环次数: 2
```

### 3. 循环完成日志
```
PinSearch 测试全部完成 (共执行3次)
```

### 4. 手动停止日志
```
用户请求停止循环执行
PinSearch 测试已取消 (已完成2次)
```

## UI验证点

### 1. 剩余次数显示
- 正整数：`剩余: X次`
- 无限循环：`无限循环`
- 已完成：`已完成`

### 2. 输入验证
- 接受正整数
- 接受-1
- 拒绝负数（除-1外）
- 拒绝非数字输入

### 3. 状态同步
- 输入框值改变时，剩余次数同步更新
- 执行过程中，剩余次数实时递减
- 停止后，状态正确重置

## 边界条件测试

### 1. 零值测试
- 输入0，观察行为
- 预期：不执行或执行一次

### 2. 负数测试
- 输入-2、-3等负数
- 预期：按-1处理或提示错误

### 3. 非数字输入测试
- 输入字母、特殊字符
- 预期：输入验证或默认值处理

## 性能测试

### 1. 大循环次数测试
- 输入1000等大数值
- 观察UI响应性
- 验证内存使用情况

### 2. 快速操作测试
- 快速修改循环次数
- 快速启动停止操作
- 验证状态一致性

## 回归测试

### 1. 原有功能验证
- 确保搬运功能正常
- 确保Pin Search功能正常
- 确保其他UI功能不受影响

### 2. 兼容性验证
- 确保与现有配置兼容
- 确保与其他模块交互正常

## 测试结果记录

| 测试用例 | 执行日期 | 测试结果 | 备注 |
|---------|---------|---------|------|
| 有限循环递减 | | | |
| 无限循环 | | | |
| 单次执行 | | | |
| 大数值循环 | | | |
| 边界条件 | | | |
| 性能测试 | | | |
| 回归测试 | | | |

## 问题记录

| 问题描述 | 严重程度 | 状态 | 解决方案 |
|---------|---------|------|---------|
| | | | |
