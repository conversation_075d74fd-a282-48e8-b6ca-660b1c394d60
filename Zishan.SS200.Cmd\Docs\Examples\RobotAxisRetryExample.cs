using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Utilities;

namespace Zishan.SS200.Cmd.Examples
{
    /// <summary>
    /// Robot轴位置检查重试机制使用示例（包括T轴和Z轴）
    /// </summary>
    public class RobotAxisRetryExample
    {
        /// <summary>
        /// 演示轴位置检查重试机制的使用
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        public static async Task DemonstrateRetryMechanism(IS200McuCmdService cmdService)
        {
            UILogService.AddLogAndIncreaseIndent("开始演示轴位置检查重试机制");

            try
            {
                // 示例1：正常的晶圆搬运操作
                await DemonstrateNormalWaferTransfer(cmdService);

                // 示例2：模拟T轴重试场景
                await DemonstrateTAxisRetryScenarios();

                // 示例3：模拟Z轴重试场景
                await DemonstrateZAxisRetryScenarios();

                // 示例4：模拟组合重试场景
                await DemonstrateCombinedRetryScenarios();

                UILogService.DecreaseIndentAndAddSuccessLog("轴位置检查重试机制演示完成");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"演示过程异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 演示正常的晶圆搬运操作
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        private static async Task DemonstrateNormalWaferTransfer(IS200McuCmdService cmdService)
        {
            UILogService.AddLogAndIncreaseIndent("示例1：正常的晶圆搬运操作");

            try
            {
                // 执行从ChamberB到CoolingTop的晶圆搬运
                var result = await cmdService.TrasferWaferAsync(
                    EnuRobotEndType.Nose,
                    EnuLocationStationType.ChamberB,
                    EnuLocationStationType.CoolingTop,
                    sourceSlotNumber: 1,
                    targetSlotNumber: 1,
                    isTRZAxisReturnZeroed: false);

                if (result.Success)
                {
                    UILogService.AddSuccessLog($"晶圆搬运成功: {result.Message}");
                }
                else
                {
                    UILogService.AddErrorLog($"晶圆搬运失败: {result.Message}");
                    UILogService.AddLog("在失败的情况下，T轴和Z轴位置检查重试机制会自动启动");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"晶圆搬运操作异常: {ex.Message}");
            }
            finally
            {
                UILogService.DecreaseIndent();
            }
        }

        /// <summary>
        /// 演示T轴重试场景
        /// </summary>
        private static async Task DemonstrateTAxisRetryScenarios()
        {
            UILogService.AddLogAndIncreaseIndent("示例2：模拟T轴重试场景");

            try
            {
                // 场景1：T轴首次检查成功
                UILogService.AddLogAndIncreaseIndent("场景1：T轴首次检查成功");
                UILogService.AddLog("当T轴已经在正确位置时，首次检查就会通过");
                UILogService.AddSuccessLog("✅ T轴位置检查通过 (首次检查)");
                UILogService.DecreaseIndent();

                // 场景2：T轴第2次检查成功
                UILogService.AddLogAndIncreaseIndent("场景2：T轴第2次检查成功");
                UILogService.AddWarningLog("⚠️ T轴位置检查失败 (首次检查)，开始重试流程...");
                UILogService.AddLog("延迟200ms等待状态表更新 (第2次检查)");
                await Task.Delay(200);
                UILogService.AddSuccessLog("✅ T轴位置检查通过 (第2次检查成功)");
                UILogService.DecreaseIndent();

                // 场景3：T轴需要用户确认的场景
                UILogService.AddLogAndIncreaseIndent("场景3：T轴需要用户确认");
                UILogService.AddWarningLog("⚠️ T轴位置检查失败 (首次检查)，开始重试流程...");
                UILogService.AddWarningLog("⚠️ T轴位置检查失败 (第2次检查)");
                UILogService.AddWarningLog("⚠️ T轴位置检查失败 (第3次检查)");
                UILogService.AddWarningLog("⚠️ T轴位置检查失败 (第4次检查)");
                UILogService.AddWarningLog("⚠️ 即将进行最后一次T轴位置检查重试 (第5次)");
                UILogService.AddLog("此时会弹出T轴确认对话框");
                UILogService.DecreaseIndent();

            }
            finally
            {
                UILogService.DecreaseIndent();
            }
        }

        /// <summary>
        /// 演示Z轴重试场景
        /// </summary>
        private static async Task DemonstrateZAxisRetryScenarios()
        {
            UILogService.AddLogAndIncreaseIndent("示例3：模拟Z轴重试场景");

            try
            {
                // 场景1：Z轴首次检查成功
                UILogService.AddLogAndIncreaseIndent("场景1：Z轴首次检查成功");
                UILogService.AddLog("当Z轴已经在正确高度位置时，首次检查就会通过");
                UILogService.AddSuccessLog("✅ Z轴高度位置检查通过 (首次检查)");
                UILogService.DecreaseIndent();

                // 场景2：Z轴第3次检查成功
                UILogService.AddLogAndIncreaseIndent("场景2：Z轴第3次检查成功");
                UILogService.AddWarningLog("⚠️ Z轴高度位置检查失败 (首次检查)，开始重试流程...");
                UILogService.AddWarningLog("⚠️ Z轴高度位置检查失败 (第2次检查)");
                UILogService.AddLog("延迟200ms等待状态表更新 (第3次检查)");
                await Task.Delay(200);
                UILogService.AddSuccessLog("✅ Z轴高度位置检查通过 (第3次检查成功)");
                UILogService.DecreaseIndent();

                // 场景3：Z轴需要用户确认的场景
                UILogService.AddLogAndIncreaseIndent("场景3：Z轴需要用户确认");
                UILogService.AddWarningLog("⚠️ Z轴高度位置检查失败 (首次检查)，开始重试流程...");
                UILogService.AddWarningLog("⚠️ Z轴高度位置检查失败 (第2次检查)");
                UILogService.AddWarningLog("⚠️ Z轴高度位置检查失败 (第3次检查)");
                UILogService.AddWarningLog("⚠️ Z轴高度位置检查失败 (第4次检查)");
                UILogService.AddWarningLog("⚠️ 即将进行最后一次Z轴高度位置检查重试 (第5次)");
                UILogService.AddLog("此时会弹出Z轴确认对话框，内容包括：");
                UILogService.AddLog("- 已重试次数");
                UILogService.AddLog("- 端口类型和站点类型");
                UILogService.AddLog("- 当前T和Z轴高度状态");
                UILogService.AddLog("- 是否继续重试的选择");
                UILogService.DecreaseIndent();

            }
            finally
            {
                UILogService.DecreaseIndent();
            }
        }

        /// <summary>
        /// 演示组合重试场景
        /// </summary>
        private static async Task DemonstrateCombinedRetryScenarios()
        {
            UILogService.AddLogAndIncreaseIndent("示例4：模拟组合重试场景");

            try
            {
                // 场景1：T轴成功，Z轴需要重试
                UILogService.AddLogAndIncreaseIndent("场景1：T轴检查成功，Z轴需要重试");
                UILogService.AddSuccessLog("✅ T轴位置检查通过 (首次检查)");
                UILogService.AddWarningLog("⚠️ Z轴高度位置检查失败，开始重试流程...");
                await Task.Delay(200);
                UILogService.AddSuccessLog("✅ Z轴高度位置检查通过 (第2次检查成功)");
                UILogService.AddSuccessLog("✅ R轴伸展操作继续执行");
                UILogService.DecreaseIndent();

                // 场景2：T轴需要重试，Z轴成功
                UILogService.AddLogAndIncreaseIndent("场景2：T轴需要重试，Z轴检查成功");
                UILogService.AddWarningLog("⚠️ T轴位置检查失败，开始重试流程...");
                await Task.Delay(200);
                UILogService.AddSuccessLog("✅ T轴位置检查通过 (第3次检查成功)");
                UILogService.AddSuccessLog("✅ Z轴高度位置检查通过 (首次检查)");
                UILogService.AddSuccessLog("✅ R轴伸展操作继续执行");
                UILogService.DecreaseIndent();

                // 场景3：两个轴都需要重试
                UILogService.AddLogAndIncreaseIndent("场景3：T轴和Z轴都需要重试");
                UILogService.AddWarningLog("⚠️ T轴位置检查失败，开始重试流程...");
                await Task.Delay(400);
                UILogService.AddSuccessLog("✅ T轴位置检查通过 (第2次检查成功)");
                UILogService.AddWarningLog("⚠️ Z轴高度位置检查失败，开始重试流程...");
                await Task.Delay(600);
                UILogService.AddSuccessLog("✅ Z轴高度位置检查通过 (第3次检查成功)");
                UILogService.AddSuccessLog("✅ R轴伸展操作继续执行");
                UILogService.DecreaseIndent();

            }
            finally
            {
                UILogService.DecreaseIndent();
            }
        }

        /// <summary>
        /// 演示重试机制的配置参数
        /// </summary>
        public static void DemonstrateRetryConfiguration()
        {
            UILogService.AddLogAndIncreaseIndent("重试机制配置参数说明");

            UILogService.AddLog("T轴和Z轴重试机制配置:");
            UILogService.AddLog("- 最大重试次数: 5次");
            UILogService.AddLog("- 每次重试间隔: 200ms");
            UILogService.AddLog("- 用户确认时机: 第4次失败后，第5次重试前");

            UILogService.AddLog("");
            UILogService.AddLog("重试流程:");
            UILogService.AddLog("1. 首次检查");
            UILogService.AddLog("2. 第2次检查 (延迟200ms)");
            UILogService.AddLog("3. 第3次检查 (延迟200ms)");
            UILogService.AddLog("4. 第4次检查 (延迟200ms)");
            UILogService.AddLog("5. [弹出确认对话框]");
            UILogService.AddLog("6. 第5次检查 (延迟200ms，如果用户确认)");

            UILogService.AddLog("");
            UILogService.AddLog("对话框差异:");
            UILogService.AddLog("- T轴对话框: 显示T轴位置检查信息");
            UILogService.AddLog("- Z轴对话框: 显示Z轴高度位置检查信息和当前状态");

            UILogService.DecreaseIndentAndAddSuccessLog("配置参数说明完成");
        }

        /// <summary>
        /// 演示如何处理重试失败的情况
        /// </summary>
        public static void DemonstrateFailureHandling()
        {
            UILogService.AddLogAndIncreaseIndent("重试失败处理建议");

            UILogService.AddLog("当轴位置检查重试失败时，可以考虑以下处理方式:");

            UILogService.AddLog("");
            UILogService.AddLog("1. T轴重试失败处理:");
            UILogService.AddLog("   - 检查T轴电机和传感器");
            UILogService.AddLog("   - 验证T轴位置参数设置");
            UILogService.AddLog("   - 执行T轴归零操作");

            UILogService.AddLog("");
            UILogService.AddLog("2. Z轴重试失败处理:");
            UILogService.AddLog("   - 检查Z轴电机和传感器");
            UILogService.AddLog("   - 验证Z轴高度参数设置");
            UILogService.AddLog("   - 检查T和Z轴状态更新逻辑");
            UILogService.AddLog("   - 验证容差值是否合理");

            UILogService.AddLog("");
            UILogService.AddLog("3. 系统诊断:");
            UILogService.AddLog("   - 检查状态表更新频率");
            UILogService.AddLog("   - 验证EnuTZAxisHeightStatus枚举状态");
            UILogService.AddLog("   - 检查位置计算逻辑");

            UILogService.DecreaseIndentAndAddSuccessLog("失败处理建议完成");
        }
    }
}
