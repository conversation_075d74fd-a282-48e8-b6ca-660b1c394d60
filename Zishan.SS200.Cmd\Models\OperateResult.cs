using System;

namespace Zishan.SS200.Cmd.Models
{
    /// <summary>
    /// 操作结果的类，只带有成功标志和错误信息
    /// </summary>
    public class OperateResult
    {
        #region 构造函数

        /// <summary>
        /// 实例化一个默认的结果对象
        /// </summary>
        public OperateResult()
        {
            IsSuccess = false;
            Message = string.Empty;
            ErrorCode = 0;
        }

        /// <summary>
        /// 使用指定的消息实例化一个默认的结果对象
        /// </summary>
        /// <param name="message">错误消息</param>
        public OperateResult(string message)
        {
            IsSuccess = false;
            Message = message;
            ErrorCode = 0;
        }

        /// <summary>
        /// 使用错误代码，消息文本来实例化对象
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        public OperateResult(int errorCode, string message)
        {
            IsSuccess = false;
            Message = message;
            ErrorCode = errorCode;
        }

        /// <summary>
        /// 使用错误代码，消息文本及异常信息来实例化对象
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常信息</param>
        public OperateResult(int errorCode, string message, Exception exception)
        {
            IsSuccess = false;
            Message = message;
            ErrorCode = errorCode;
            Exception = exception;
        }

        #endregion

        #region 属性

        /// <summary>
        /// 指示本次操作是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 具体的错误描述
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }

        /// <summary>
        /// 异常信息，当操作出现异常时设置
        /// </summary>
        public Exception Exception { get; set; }

        #endregion

        #region 静态方法

        /// <summary>
        /// 创建一个成功的结果对象
        /// </summary>
        /// <returns>成功的结果对象</returns>
        public static OperateResult CreateSuccessResult()
        {
            return new OperateResult()
            {
                IsSuccess = true,
                Message = "操作成功"
            };
        }

        /// <summary>
        /// 创建一个失败的结果对象，具体的失败原因需要在参数中指定
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>失败的结果对象</returns>
        public static OperateResult CreateFailedResult(string message)
        {
            return new OperateResult()
            {
                IsSuccess = false,
                Message = message
            };
        }

        /// <summary>
        /// 创建一个失败的结果对象，具体的失败原因需要在参数中指定
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <returns>失败的结果对象</returns>
        public static OperateResult CreateFailedResult(int errorCode, string message)
        {
            return new OperateResult()
            {
                IsSuccess = false,
                ErrorCode = errorCode,
                Message = message
            };
        }

        /// <summary>
        /// 创建一个失败的结果对象，具体的失败原因需要在参数中指定
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常信息</param>
        /// <returns>失败的结果对象</returns>
        public static OperateResult CreateFailedResult(int errorCode, string message, Exception exception)
        {
            return new OperateResult()
            {
                IsSuccess = false,
                ErrorCode = errorCode,
                Message = message,
                Exception = exception
            };
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 从另一个结果对象复制错误信息
        /// </summary>
        /// <param name="result">结果对象</param>
        public void CopyErrorFromOther(OperateResult result)
        {
            if (result != null)
            {
                IsSuccess = result.IsSuccess;
                Message = result.Message;
                ErrorCode = result.ErrorCode;
                Exception = result.Exception;
            }
        }

        /// <summary>
        /// 返回表示当前对象的字符串
        /// </summary>
        /// <returns>字符串信息</returns>
        public override string ToString()
        {
            return $"IsSuccess:{IsSuccess} Message:{Message} ErrorCode:{ErrorCode}";
        }

        #endregion
    }

    /// <summary>
    /// 操作结果的泛型类，带有成功标志，错误信息和泛型数据
    /// </summary>
    /// <typeparam name="T">泛型参数</typeparam>
    public class OperateResult<T> : OperateResult
    {
        #region 构造函数

        /// <summary>
        /// 实例化一个默认的结果对象
        /// </summary>
        public OperateResult() : base()
        {
            Content = default(T);
        }

        /// <summary>
        /// 使用指定的消息实例化一个默认的结果对象
        /// </summary>
        /// <param name="message">错误消息</param>
        public OperateResult(string message) : base(message)
        {
            Content = default(T);
        }

        /// <summary>
        /// 使用错误代码，消息文本来实例化对象
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        public OperateResult(int errorCode, string message) : base(errorCode, message)
        {
            Content = default(T);
        }

        /// <summary>
        /// 使用错误代码，消息文本及异常信息来实例化对象
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常信息</param>
        public OperateResult(int errorCode, string message, Exception exception) : base(errorCode, message, exception)
        {
            Content = default(T);
        }

        /// <summary>
        /// 使用指定的数据实例化一个成功的结果对象
        /// </summary>
        /// <param name="content">结果数据</param>
        public OperateResult(T content)
        {
            IsSuccess = true;
            Message = "操作成功";
            Content = content;
        }

        #endregion

        #region 属性

        /// <summary>
        /// 结果对象
        /// </summary>
        public T Content { get; set; }

        #endregion

        #region 静态方法

        /// <summary>
        /// 创建一个成功的结果对象，并包含一个结果数据
        /// </summary>
        /// <param name="content">结果数据</param>
        /// <returns>成功的结果对象</returns>
        public static OperateResult<T> CreateSuccessResult(T content)
        {
            return new OperateResult<T>()
            {
                IsSuccess = true,
                Message = "操作成功",
                Content = content
            };
        }

        /// <summary>
        /// 创建一个失败的结果对象，具体的失败原因需要在参数中指定
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>失败的结果对象</returns>
        public new static OperateResult<T> CreateFailedResult(string message)
        {
            return new OperateResult<T>()
            {
                IsSuccess = false,
                Message = message,
                Content = default(T)
            };
        }

        /// <summary>
        /// 创建一个失败的结果对象，具体的失败原因需要在参数中指定
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <returns>失败的结果对象</returns>
        public new static OperateResult<T> CreateFailedResult(int errorCode, string message)
        {
            return new OperateResult<T>()
            {
                IsSuccess = false,
                ErrorCode = errorCode,
                Message = message,
                Content = default(T)
            };
        }

        /// <summary>
        /// 创建一个失败的结果对象，具体的失败原因需要在参数中指定
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常信息</param>
        /// <returns>失败的结果对象</returns>
        public new static OperateResult<T> CreateFailedResult(int errorCode, string message, Exception exception)
        {
            return new OperateResult<T>()
            {
                IsSuccess = false,
                ErrorCode = errorCode,
                Message = message,
                Exception = exception,
                Content = default(T)
            };
        }

        /// <summary>
        /// 从另一个结果对象复制错误信息
        /// </summary>
        /// <param name="result">结果对象</param>
        public void CopyErrorFromOther<TResult>(OperateResult<TResult> result)
        {
            if (result != null)
            {
                IsSuccess = result.IsSuccess;
                Message = result.Message;
                ErrorCode = result.ErrorCode;
                Exception = result.Exception;
            }
        }

        #endregion

        #region 隐式转换

        /// <summary>
        /// 隐式转换为泛型对象
        /// </summary>
        /// <param name="result">转换的结果对象</param>
        public static implicit operator T(OperateResult<T> result)
        {
            return result.Content;
        }

        #endregion
    }
} 