using System;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// 快速测试SS200InterLockMain配置加载
    /// </summary>
    public static class QuickTest
    {
        /// <summary>
        /// 验证配置是否正确加载
        /// </summary>
        public static void VerifyConfiguration()
        {
            try
            {
                Console.WriteLine("=== 验证SS200InterLockMain配置加载 ===");
                
                // 获取单例实例
                var instance = SS200InterLockMain.Instance;
                Console.WriteLine("✓ SS200InterLockMain 单例实例获取成功");
                
                // 测试RP1和RP2配置访问
                try
                {
                    var rp1Value = instance.SubsystemConfigure.Robot.RP1_TAxisSmoothToCHA?.Value;
                    Console.WriteLine($"✓ RP1_TAxisSmoothToCHA 配置值: {rp1Value}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ RP1_TAxisSmoothToCHA 访问失败: {ex.Message}");
                }

                try
                {
                    var rp2Value = instance.SubsystemConfigure.Robot.RP2_TAxisSmoothToCHB?.Value;
                    Console.WriteLine($"✓ RP2_TAxisSmoothToCHB 配置值: {rp2Value}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ RP2_TAxisSmoothToCHB 访问失败: {ex.Message}");
                }
                
                Console.WriteLine("=== 验证完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 验证失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}
