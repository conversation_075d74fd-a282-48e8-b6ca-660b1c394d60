using System;
using System.ComponentModel;
using System.Globalization;
using System.Reflection;

namespace Wu.Wpf.Converters
{
    /// <summary>
    /// 枚举描述类型转换器，用于将枚举的Description特性值显示在ComboBox等控件中
    /// </summary>
    public class EnumDescriptionTypeConverter : EnumConverter
    {
        public EnumDescriptionTypeConverter(Type type) : base(type)
        {
        }

        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
        {
            if (destinationType == typeof(string) && value != null)
            {
                FieldInfo fi = value.GetType().GetField(value.ToString());
                if (fi != null)
                {
                    var attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);
                    return attributes.Length > 0 && !string.IsNullOrEmpty(attributes[0].Description)
                        ? attributes[0].Description
                        : value.ToString();
                }
                return value.ToString();
            }

            return base.ConvertTo(context, culture, value, destinationType);
        }
    }
}