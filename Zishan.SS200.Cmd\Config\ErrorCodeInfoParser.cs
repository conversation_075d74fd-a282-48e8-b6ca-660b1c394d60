using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Zishan.SS200.Cmd.Config
{
    /// <summary>
    /// 告警信息
    /// </summary>
    public class ErrorCodeInfo
    {
        [JsonPropertyName("cause")]
        public string Cause { get; set; }

        [JsonPropertyName("kind")]
        public string Kind { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 告警信息来源
        /// </summary>
        public string Source { get; set; }

        public override string ToString()
        {
            return $"Cause={Cause},Kind={Kind}";
        }
    }

    /// <summary>
    /// 告警信息解析器
    /// </summary>
    public class ErrorCodeInfoParser
    {
        private readonly Dictionary<string, ErrorCodeInfo> _alarmInfos;
        private readonly Dictionary<string, string> _configFilePaths;

        /// <summary>
        /// 初始化告警信息解析器
        /// </summary>
        public ErrorCodeInfoParser()
        {
            _alarmInfos = new Dictionary<string, ErrorCodeInfo>();
            _configFilePaths = new Dictionary<string, string>();
        }

        /// <summary>
        /// 添加配置文件路径
        /// </summary>
        /// <param name="source">告警源标识</param>
        /// <param name="configFilePath">配置文件路径</param>
        public void AddConfigFilePath(string source, string configFilePath)
        {
            _configFilePaths[source] = configFilePath;
            LoadConfig(source, configFilePath);
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        /// <param name="source">告警源标识</param>
        /// <param name="configFilePath">配置文件路径</param>
        private void LoadConfig(string source, string configFilePath)
        {
            try
            {
                if (!File.Exists(configFilePath))
                {
                    throw new FileNotFoundException($"Alarm info configuration file not found: {configFilePath}");
                }

                var jsonString = File.ReadAllText(configFilePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var alarmInfos = JsonSerializer.Deserialize<Dictionary<string, ErrorCodeInfo>>(jsonString, options);
                if (alarmInfos != null)
                {
                    foreach (var alarm in alarmInfos)
                    {
                        var alarmInfo = alarm.Value;
                        alarmInfo.Code = alarm.Key;
                        alarmInfo.Source = source;

                        // 使用源和代码组合作为字典键，以便在不同文件中可能存在相同的错误代码
                        var key = $"{source}_{alarm.Key}";
                        _alarmInfos[key] = alarmInfo;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to load alarm info configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取告警信息
        /// </summary>
        /// <param name="source">告警源标识</param>
        /// <param name="code">错误代码</param>
        /// <returns>告警信息</returns>
        public ErrorCodeInfo GetAlarmInfo(string source, string code)
        {
            var key = $"{source}_{code}";
            if (_alarmInfos.TryGetValue(key, out var alarmInfo))
            {
                return alarmInfo;
            }
            throw new KeyNotFoundException($"Alarm info for source '{source}' with code '{code}' not found");
        }

        /// <summary>
        /// 尝试获取告警信息
        /// </summary>
        /// <param name="source">告警源标识</param>
        /// <param name="code">错误代码</param>
        /// <param name="errorCodeInfo">告警信息</param>
        /// <returns>是否成功获取告警信息</returns>
        public bool TryGetAlarmInfo(string source, string code, out ErrorCodeInfo errorCodeInfo)
        {
            var key = $"{source}_{code}";
            return _alarmInfos.TryGetValue(key, out errorCodeInfo);
        }

        /// <summary>
        /// 获取所有告警信息
        /// </summary>
        /// <returns>所有告警信息</returns>
        public IEnumerable<ErrorCodeInfo> GetAllAlarmInfos()
        {
            return _alarmInfos.Values;
        }

        /// <summary>
        /// 获取指定来源的所有告警信息
        /// </summary>
        /// <param name="source">告警源标识</param>
        /// <returns>指定来源的所有告警信息</returns>
        public IEnumerable<ErrorCodeInfo> GetAlarmInfosBySource(string source)
        {
            var result = new List<ErrorCodeInfo>();
            foreach (var alarm in _alarmInfos.Values)
            {
                if (alarm.Source == source)
                {
                    result.Add(alarm);
                }
            }
            return result;
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfig()
        {
            _alarmInfos.Clear();
            foreach (var source in _configFilePaths.Keys)
            {
                LoadConfig(source, _configFilePaths[source]);
            }
        }

        /// <summary>
        /// 重新加载指定来源的配置
        /// </summary>
        /// <param name="source">告警源标识</param>
        public void ReloadConfig(string source)
        {
            if (_configFilePaths.TryGetValue(source, out var configFilePath))
            {
                // 移除该来源的所有告警信息
                var keysToRemove = new List<string>();
                foreach (var key in _alarmInfos.Keys)
                {
                    if (_alarmInfos[key].Source == source)
                    {
                        keysToRemove.Add(key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    _alarmInfos.Remove(key);
                }

                // 重新加载该来源的配置
                LoadConfig(source, configFilePath);
            }
            else
            {
                throw new KeyNotFoundException($"Config file path for source '{source}' not found");
            }
        }
    }
}