{"configureSettings": [{"id": 1, "code": "SSC1", "description": "shuttle 1 wafer size", "value": 8, "unit": "inch"}, {"id": 2, "code": "SSC2", "description": "shuttle 2 wafer size", "value": 8, "unit": "inch"}, {"id": 3, "code": "SSC3", "description": "chamber location", "value": "CHA and CHB", "unit": "N/A"}, {"id": 4, "code": "SSC4", "description": "chamber A process", "value": "ICP", "unit": "N/A"}, {"id": 5, "code": "SSC5", "description": "chamber B process", "value": "ICP", "unit": "N/A"}, {"id": 6, "code": "SSC6", "description": "cassette nest type", "value": "fixed", "unit": "N/A"}, {"id": 7, "code": "SSC7", "description": "slit door type", "value": "A", "unit": "N/A"}, {"id": 8, "code": "SSC8", "description": "priority effective", "value": 5, "unit": "min"}, {"id": 9, "code": "SSC9", "description": "mapping function", "value": "N", "unit": "N/A"}, {"id": 10, "code": "SSC10", "description": "skip empty slot", "value": "N", "unit": "N/A"}, {"id": 11, "code": "SSC11", "description": "temperature for cooling chamber", "value": 120, "unit": "℃"}, {"id": 12, "code": "SSC12", "description": "deviation for chamber temperature", "value": 5, "unit": "℃"}, {"id": 13, "code": "SSC13", "description": "transfer pressure", "value": "A", "unit": "N/A"}, {"id": 14, "code": "SSC14", "description": "slot product order", "value": "A", "unit": "N/A"}]}