﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models.IR400;

namespace Zishan.SS200.Cmd.Interface
{
    /// <summary>
    /// 拖放Wafer公共信息接口
    /// </summary>
    public interface IWaferDragDropInfo
    {
        int WaferNo { get; set; }

        BContainer CurCharber { get; set; }

        int MaxWafers { get; set; }
    }
}