using System;
using System.Collections.Generic;
using System.IO;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Robot;
using Zishan.SS200.Cmd.Models.SS200.AlarmCode;

namespace Zishan.SS200.Cmd.Config.SS200.AlarmCode.Robot
{
    /// <summary>
    /// 机器人报警代码提供者 - 从JSON配置文件加载报警代码
    /// </summary>
    public class RobotErrorCodesProvider : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RobotErrorCodesProvider));
        private readonly List<AlarmItem> _alarmItems = new List<AlarmItem>();
        private readonly FileSystemWatcher _configWatcher;

        private static readonly Lazy<RobotErrorCodesProvider> _instance =
            new Lazy<RobotErrorCodesProvider>(() => new RobotErrorCodesProvider());

        // 配置文件路径
        private const string CONFIG_PATH = "Configs/SS200/AlarmCode/Robot/RobotErrorCodes.json";

        // 最后修改时间，用于监测配置文件变化
        private DateTime _lastModifiedTime = DateTime.MinValue;

        public static RobotErrorCodesProvider Instance => _instance.Value;

        // 私有构造函数
        private RobotErrorCodesProvider()
        {
            // 初始化文件系统监视器
            string configDir = Path.GetDirectoryName(GetConfigFilePath());
            string configFileName = Path.GetFileName(CONFIG_PATH);

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            _configWatcher = new FileSystemWatcher(configDir, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            // 注册文件变化事件处理
            _configWatcher.Changed += OnConfigFileChanged;
            _configWatcher.Created += OnConfigFileChanged;

            // 尝试加载配置文件
            LoadFromJson();
        }

        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 由于文件系统事件可能会触发多次，这里添加简单的防抖动处理
                if ((DateTime.Now - _lastModifiedTime).TotalMilliseconds < 100)
                {
                    return;
                }

                _logger.Info($"检测到配置文件变化: {e.FullPath}, 变化类型: {e.ChangeType}");
                LoadFromJson();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理配置文件变化事件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从JSON配置文件加载报警代码
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool LoadFromJson()
        {
            try
            {
                string jsonFilePath = GetConfigFilePath();
                if (!File.Exists(jsonFilePath))
                {
                    _logger.Warn($"机器人报警代码文件不存在: {jsonFilePath}");
                    return false;
                }

                // 获取文件最后修改时间
                DateTime currentModified = File.GetLastWriteTime(jsonFilePath);

                // 如果文件未修改，则不重新加载
                if (currentModified == _lastModifiedTime)
                {
                    return true;
                }

                _lastModifiedTime = currentModified;

                string jsonContent;
                using (var fileStream = new FileStream(jsonFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    jsonContent = reader.ReadToEnd();
                }
                var alarmItems = JsonConvert.DeserializeObject<List<AlarmItem>>(jsonContent);

                if (alarmItems == null || alarmItems.Count == 0)
                {
                    _logger.Warn("未找到有效的报警代码，将使用默认值");
                    return false;
                }

                // 更新报警项列表
                _alarmItems.Clear();
                _alarmItems.AddRange(alarmItems);

                _logger.Info($"成功从 {jsonFilePath} 加载 {_alarmItems.Count} 个机器人报警代码");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载机器人报警代码文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        private string GetConfigFilePath()
        {
            try
            {
                return App.ConfigHelper.GetConfigFilePath(CONFIG_PATH);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取配置文件路径失败: {ex.Message}", ex);

                // 回退策略 - 尝试直接拼接路径
                string fallbackPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    CONFIG_PATH);

                _logger.Info($"使用回退路径: {fallbackPath}");
                return fallbackPath;
            }
        }

        /// <summary>
        /// 获取所有报警项
        /// </summary>
        /// <returns>报警项列表</returns>
        public List<AlarmItem> GetAllAlarmItems()
        {
            return new List<AlarmItem>(_alarmItems);
        }

        /// <summary>
        /// 根据报警枚举代码获取报警项
        /// </summary>
        /// <param name="enuRobotAlarmCodes">枚举代码</param>
        /// <returns>报警项，如果找不到则返回null</returns>
        public AlarmItem GetAlarmItemByCode(EnuRobotAlarmCodes enuRobotAlarmCodes)
        {
            return _alarmItems.Find(item => item.Code == enuRobotAlarmCodes.ToString());
        }

        /// <summary>
        /// 根据报警代码获取报警项
        /// </summary>
        /// <param name="code">报警代码</param>
        /// <returns>报警项，如果找不到则返回null</returns>
        public AlarmItem GetAlarmItemByCode(string code)
        {
            return _alarmItems.Find(item => item.Code == code);
        }

        /// <summary>
        /// 根据报警项ID获取报警项
        /// </summary>
        /// <param name="itemId">报警项ID</param>
        /// <returns>报警项，如果找不到则返回null</returns>
        public AlarmItem GetAlarmItemById(int itemId)
        {
            return _alarmItems.Find(item => item.Item == itemId);
        }

        public void Dispose()
        {
            _configWatcher?.Dispose();
        }
    }
}