using System;
using System.Globalization;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 字符串与枚举值相互转换的转换器
    /// </summary>
    public class StringToEnumConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string strValue && !string.IsNullOrEmpty(strValue))
            {
                // 从绑定上下文中获取枚举类型
                if (parameter is Type enumType && enumType.IsEnum)
                {
                    try
                    {
                        return Enum.Parse(enumType, strValue);
                    }
                    catch
                    {
                        // 如果解析失败，返回默认值
                        return Enum.GetValues(enumType).GetValue(0);
                    }
                }
            }
            
            return null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value != null)
            {
                return value.ToString();
            }
            
            return string.Empty;
        }
    }
}
