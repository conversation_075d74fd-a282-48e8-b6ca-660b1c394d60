# SS200InterLockMain 流程图详解

## 1. 系统整体架构流程图

```mermaid
graph TB
    subgraph "应用程序层"
        A[用户代码] --> B[SS200InterLockMain.Instance]
    end
    
    subgraph "单例管理层"
        B --> C[Lazy&lt;T&gt; 延迟初始化]
        C --> D[SS200InterLockMain 构造函数]
    end
    
    subgraph "访问器层"
        D --> E[IOInterface 初始化]
        D --> F[AlarmCode 初始化]
        D --> G[SubsystemConfigure 初始化]
        D --> H[SubsystemStatus 初始化]
    end
    
    subgraph "工厂层"
        E --> I[AccessorFactory]
        F --> I
        G --> I
        H --> I
    end
    
    subgraph "设备层"
        I --> J[Robot 访问器]
        I --> K[Chamber 访问器]
        I --> L[Shuttle 访问器]
    end
    
    subgraph "数据源层"
        J --> M[CoilStatusHelper]
        K --> M
        L --> M
        G --> N[配置提供者]
        F --> O[报警信息提供者]
        H --> P[状态计算服务]
    end
```

## 2. 单例初始化详细流程

```mermaid
sequenceDiagram
    participant U as 用户代码
    participant L as Lazy&lt;SS200InterLockMain&gt;
    participant S as SS200InterLockMain
    participant AF as AccessorFactory
    participant CP as 配置提供者
    
    U->>L: 访问 Instance 属性
    L->>L: 检查是否已初始化
    alt 未初始化
        L->>S: 创建新实例
        S->>S: 调用构造函数
        S->>AF: 初始化 IOInterface
        S->>AF: 初始化 AlarmCode
        S->>AF: 初始化 SubsystemConfigure
        S->>AF: 初始化 SubsystemStatus
        S->>CP: 加载机器人位置配置
        S->>S: LoadRobotPositionConfiguration()
        S-->>L: 返回初始化完成的实例
    else 已初始化
        L-->>U: 返回现有实例
    end
    L-->>U: 返回 SS200InterLockMain 实例
```

## 3. 访问器创建和缓存流程

```mermaid
graph TD
    A[属性访问请求] --> B{检查 AccessorFactory 缓存}
    B -->|缓存命中| C[返回缓存的访问器]
    B -->|缓存未命中| D[创建访问器工厂方法]
    D --> E[根据设备类型创建访问器]
    E --> F[设置访问器属性]
    F --> G[绑定数据源]
    G --> H[添加到 ConcurrentDictionary 缓存]
    H --> I[返回新创建的访问器]
    
    subgraph "访问器类型"
        J[IOAccessor - IO访问器]
        K[AlarmAccessor - 报警访问器]
        L[ConfigAccessor - 配置访问器]
        M[StatusAccessor - 状态访问器]
    end
    
    E --> J
    E --> K
    E --> L
    E --> M
```

## 4. 配置加载详细流程

```mermaid
graph TD
    A[SS200InterLockMain 构造] --> B[LoadRobotPositionConfiguration]
    B --> C[获取 RobotPositionParametersProvider.Instance]
    C --> D[获取所有 EnuRobotPositionParameterCodes 枚举值]
    D --> E[开始遍历枚举]
    E --> F[当前枚举: RP1_TAxisPosition]
    F --> G[调用 provider.GetParameterValue]
    G --> H[创建 ConfigureSetting 对象]
    H --> I[设置 Code = 'RP1_TAxisPosition']
    I --> J[设置 Value = 50100]
    J --> K[调用 GetRobotPositionDescription]
    K --> L[设置 Description = 'T轴位置参数1']
    L --> M[调用 GetAxisTypeForRP]
    M --> N[设置 AxisType = 'T轴']
    N --> O[添加到 RobotPositionValue 列表]
    O --> P{还有更多枚举?}
    P -->|是| Q[下一个枚举: RP2_RAxisPosition]
    P -->|否| R[配置加载完成]
    Q --> G
```

## 5. IO访问流程图

```mermaid
sequenceDiagram
    participant U as 用户代码
    participant S as SS200InterLockMain
    participant IO as IOInterface
    participant R as Robot访问器
    participant A as RDI1访问器
    participant C as CoilStatusHelper
    participant M as S200McuCmdService
    
    U->>S: Instance.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value
    S->>IO: 获取 Robot 访问器
    IO->>R: 返回 Robot IO 访问器
    R->>A: 获取 RDI1_PaddleSmoothSensor 访问器
    A->>C: 调用 GetCoilValue(EnuMcuDeviceType.Robot, EnuRobotDICodes.RDI1)
    C->>M: 获取实时 DI 值
    M-->>C: 返回布尔值
    C-->>A: 返回传感器状态
    A-->>R: 返回值
    R-->>IO: 返回值
    IO-->>S: 返回值
    S-->>U: 返回传感器状态值
```

## 6. 报警信息访问流程

```mermaid
graph LR
    A[用户请求报警信息] --> B[AlarmCode.Robot.RA1_SystemBusyReject.Content]
    B --> C[获取 Robot 报警访问器]
    C --> D[获取 RA1 报警访问器]
    D --> E[从报警信息提供者获取内容]
    E --> F[返回报警描述文本]
    
    subgraph "报警信息结构"
        G[报警代码: RA1]
        H[报警级别: Error]
        I[报警描述: 系统忙拒绝]
        J[解决方案: 等待系统空闲后重试]
    end
    
    E --> G
    E --> H
    E --> I
    E --> J
```

## 7. 状态计算流程

```mermaid
graph TD
    A[状态访问请求] --> B[SubsystemStatus.Robot.RS1_RobotStatus]
    B --> C[获取相关 DI/DO 状态]
    C --> D[读取 T轴位置]
    D --> E[读取 R轴位置]
    E --> F[读取 Z轴位置]
    F --> G[应用状态计算逻辑]
    G --> H{判断机器人状态}
    H -->|T=0,R=0,Z=0| I[RS1: 原点位置]
    H -->|T≠0,R=0,Z=0| J[RS2: T轴移动中]
    H -->|T=目标,R=伸出,Z=0| K[RS3: 伸出到位]
    H -->|其他组合| L[RS4: 其他状态]
    
    I --> M[返回状态枚举值]
    J --> M
    K --> M
    L --> M
```

## 8. 错误处理流程

```mermaid
graph TD
    A[访问请求] --> B{访问器是否存在?}
    B -->|否| C[抛出 ArgumentException]
    B -->|是| D{数据源是否可用?}
    D -->|否| E[返回默认值或抛出异常]
    D -->|是| F{数据是否有效?}
    F -->|否| G[记录警告日志]
    F -->|是| H[返回正确数据]
    G --> I[返回上次有效值或默认值]
    
    subgraph "异常类型"
        J[ArgumentException - 参数错误]
        K[InvalidOperationException - 操作无效]
        L[TimeoutException - 通信超时]
        M[ConfigurationException - 配置错误]
    end
    
    C --> J
    E --> K
    E --> L
    E --> M
```

## 9. 性能优化流程

```mermaid
graph LR
    A[性能优化策略] --> B[延迟初始化]
    A --> C[访问器缓存]
    A --> D[数据缓存]
    A --> E[批量操作]
    
    B --> F[Lazy&lt;T&gt; 单例]
    C --> G[ConcurrentDictionary 缓存]
    D --> H[定时刷新机制]
    E --> I[批量读取 DI/DO]
    
    subgraph "性能指标"
        J[初始化时间: &lt;100ms]
        K[访问器创建: &lt;1ms]
        L[数据读取: &lt;10ms]
        M[内存占用: &lt;50MB]
    end
    
    F --> J
    G --> K
    H --> L
    I --> M
```

## 10. 扩展性设计流程

```mermaid
graph TB
    A[新设备类型扩展] --> B[定义新的枚举类型]
    B --> C[创建对应的访问器类]
    C --> D[在 AccessorFactory 中注册]
    D --> E[更新 SS200InterLockMain 接口]
    E --> F[添加配置提供者]
    F --> G[实现数据源连接]
    G --> H[编写单元测试]
    H --> I[更新文档]
    
    subgraph "扩展示例"
        J[新增 LoadLock 设备]
        K[定义 EnuLoadLockDICodes]
        L[创建 LoadLockIOAccessor]
        M[添加 LoadLock 属性]
    end
    
    A --> J
    B --> K
    C --> L
    E --> M
```

这些流程图详细展示了 SS200InterLockMain 系统的各个方面，从整体架构到具体的实现细节，帮助开发者理解系统的工作原理和扩展方法。
