﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 机械臂抓取面类型：A面 Nose端、B面 Smooth端
    /// </summary>
    public enum EnuArmFetchSide
    {
        /// <summary>
        /// A面 Nose端,主要用来抓Cassettes的Wafer
        /// </summary>
        [Description("Nose")]
        Nose = 0,

        /// <summary>
        /// B面 Smooth端,主要用来抓腔体的Wafer
        /// </summary>
        [Description("Smooth")]
        Smooth = 1,

        /// <summary>
        /// 未知面，针对非机械臂选Unknown
        /// </summary>
        [Description("Unknow")]
        Unknow = 2
    }
}