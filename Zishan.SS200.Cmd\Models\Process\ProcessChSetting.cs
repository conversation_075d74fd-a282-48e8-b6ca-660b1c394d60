﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums.Process;

//using Zishan.Robot.Shared.AttributeExtend;
//using Zishan.Robot.Shared.Common;
//using Zishan.Robot.Shared.Enums;
//using Zishan.SS200.Cmd.Enums.UI.Process;

namespace Zishan.SS200.Cmd.Models.Process
{
    /// <summary>
    /// CH腔体工艺流程参数
    /// </summary>
    public class ProcessChSetting : BindableBase
    {
        #region MFC流量反馈

        /// <summary>
        /// 当前步计时
        /// </summary>
        public TimeSpan CurrentStepTiming { get => _currentStepTiming; set => SetProperty(ref _currentStepTiming, value); }
        private TimeSpan _currentStepTiming;

        /// <summary>
        /// MFC1流量反馈，单位：sccm
        /// </summary>
        public double Mfc1FlowFeedback { get => _mfc1FlowFeedback; set => SetProperty(ref _mfc1FlowFeedback, value); }
        private double _mfc1FlowFeedback;

        /// <summary>
        /// MFC2流量反馈，单位：sccm
        /// </summary>
        public double Mfc2FlowFeedback { get => _mfc2FlowFeedback; set => SetProperty(ref _mfc2FlowFeedback, value); }
        private double _mfc2FlowFeedback;

        /// <summary>
        /// MFC3流量反馈，单位：sccm
        /// </summary>
        public double Mfc3FlowFeedback { get => _mfc3FlowFeedback; set => SetProperty(ref _mfc3FlowFeedback, value); }
        private double _mfc3FlowFeedback;

        /// <summary>
        /// MFC4流量反馈，单位：sccm
        /// </summary>
        public double Mfc4FlowFeedback { get => _mfc4FlowFeedback; set => SetProperty(ref _mfc4FlowFeedback, value); }
        private double _mfc4FlowFeedback;

        #endregion MFC流量反馈

        #region RF功率反馈

        /// <summary>
        /// 正向功率_L，单位：W
        /// </summary>
        public double ForwardPowerL { get => _forwardPowerL; set => SetProperty(ref _forwardPowerL, value); }
        private double _forwardPowerL;

        /// <summary>
        /// 反射功率_L，单位：W
        /// </summary>
        public double ReflectedPowerL { get => _reflectedPowerL; set => SetProperty(ref _reflectedPowerL, value); }
        private double _reflectedPowerL;

        /// <summary>
        /// 输送功率_L，单位：W
        /// </summary>
        public double DeliveredPowerL { get => _deliveredPowerL; set => SetProperty(ref _deliveredPowerL, value); }
        private double _deliveredPowerL;

        /// <summary>
        /// 正向功率_R，单位：W
        /// </summary>
        public double ForwardPowerR { get => _forwardPowerR; set => SetProperty(ref _forwardPowerR, value); }
        private double _forwardPowerR;

        /// <summary>
        /// 反射功率_R，单位：W
        /// </summary>
        public double ReflectedPowerR { get => _reflectedPowerR; set => SetProperty(ref _reflectedPowerR, value); }
        private double _reflectedPowerR;

        /// <summary>
        /// 输送功率_R，单位：W
        /// </summary>

        public double DeliveredPowerR { get => _deliveredPowerR; set => SetProperty(ref _deliveredPowerR, value); }
        private double _deliveredPowerR;

        #endregion RF功率反馈

        #region 腔体压力和温度

        /// <summary>
        /// 腔体压力，单位：torr
        /// </summary>
        public double ChamberPressure { get => _chamberPressure; set => SetProperty(ref _chamberPressure, value); }
        private double _chamberPressure;

        /// <summary>
        /// 温度检查值，单位：℃
        /// </summary>

        public double TemperatureCheckValue { get => _temperatureCheckValue; set => SetProperty(ref _temperatureCheckValue, value); }
        private double _temperatureCheckValue;

        #endregion 腔体压力和温度

        #region 热板温度

        /// <summary>
        /// 热板1温度_CHA，单位：℃
        /// </summary>

        public double HotPlate1Temperature { get => _hotPlate1Temperature; set => SetProperty(ref _hotPlate1Temperature, value); }
        private double _hotPlate1Temperature;

        /// <summary>
        /// 热板2温度_CHA，单位：℃
        /// </summary>

        public double HotPlate2Temperature { get => _hotPlate2Temperature; set => SetProperty(ref _hotPlate2Temperature, value); }
        private double _hotPlate2Temperature;

        #endregion 热板温度

        #region 当前配方输出

        /// <summary>
        /// 当前步号
        /// </summary>

        public int CurrentStepNumber { get => _currentStepNumber; set => SetProperty(ref _currentStepNumber, value); }
        private int _currentStepNumber;

        /// <summary>
        /// GoToNextStep
        /// </summary>

        public double GoToNextStep { get => _goToNextStep; set => SetProperty(ref _goToNextStep, value); }
        private double _goToNextStep;

        /// <summary>
        /// RF等待时间，单位：s
        /// </summary>

        public double RfWaitTime { get => _rfWaitTime; set => SetProperty(ref _rfWaitTime, value); }
        private double _rfWaitTime;

        /// <summary>
        /// RF运行时间，单位：s
        /// </summary>

        public double RfRunTime { get => _rfRunTime; set => SetProperty(ref _rfRunTime, value); }
        private double _rfRunTime;

        /// <summary>
        /// RF Power，单位：w
        /// </summary>

        public double RfPower { get => _rfPower; set => SetProperty(ref _rfPower, value); }
        private double _rfPower;

        /// <summary>
        /// 腔体压力，单位：torr
        /// </summary>

        public double RecipeChamberPressure { get => _recipeChamberPressure; set => SetProperty(ref _recipeChamberPressure, value); }
        private double _recipeChamberPressure;

        /// <summary>
        /// 气体1流量，单位：sccm
        /// </summary>

        public double Gas1Flow { get => _gas1Flow; set => SetProperty(ref _gas1Flow, value); }
        private double _gas1Flow;

        /// <summary>
        /// 气体2流量，单位：sccm
        /// </summary>

        public double Gas2Flow { get => _gas2Flow; set => SetProperty(ref _gas2Flow, value); }
        private double _gas2Flow;

        /// <summary>
        /// 气体3流量，单位：sccm
        /// </summary>

        public double Gas3Flow { get => _gas3Flow; set => SetProperty(ref _gas3Flow, value); }
        private double _gas3Flow;

        /// <summary>
        /// 气体4流量，单位：sccm
        /// </summary>

        public double Gas4Flow { get => _gas4Flow; set => SetProperty(ref _gas4Flow, value); }
        private double _gas4Flow;

        /// <summary>
        /// 温度检查值，单位：℃
        /// </summary>

        public double RecipeTemperatureCheckValue { get => _recipeTemperatureCheckValue; set => SetProperty(ref _recipeTemperatureCheckValue, value); }
        private double _recipeTemperatureCheckValue;

        /// <summary>
        /// PinUp (Y/N)【不能直接是枚举类型，PLC为数字类型，需要后面转为枚举类型】
        /// 'PinUP'
        ///0    降
        ///1    升
        /// </summary>

        public double PinUp { get => _pinUp; set => SetProperty(ref _pinUp, value); }
        private double _pinUp;

        /// <summary>
        /// ProcessControl by time/by endpoint	%s 【不能直接是枚举类型，PLC为数字类型，需要后面转为枚举类型】
        ///'ProcessControl'
        ///0	By Time
        ///1	By EPD
        /// </summary>
        public double ProcessControlByType { get => _ProcessControlByType; set => SetProperty(ref _ProcessControlByType, value); }
        private double _ProcessControlByType;

        /// <summary>
        /// 当前配方名称
        /// </summary>

        public string CurrentRecipeName { get => _currentRecipeName; set => SetProperty(ref _currentRecipeName, value); }
        private string _currentRecipeName;

        /// <summary>
        /// 预备配方名称
        /// </summary>

        public string PreRecipeName { get => _preRecipeName; set => SetProperty(ref _preRecipeName, value); }
        private string _preRecipeName;

        #endregion 当前配方输出

        #region 单元状态

        /// <summary>
        /// 单元状态
        /// 0	Idle	单元空闲
        /// 1	Busy	单元动作中
        /// 2	Process	单元工艺中
        /// 10	Alarm	单元故障
        /// 20	Null	单元不存在
        /// </summary>

        public EnuUnitWorkStatus UnitStatus { get => _unitStatus; set => SetProperty(ref _unitStatus, value); }
        private EnuUnitWorkStatus _unitStatus;

        #endregion 单元状态

        #region 运行控制

        /// <summary>
        /// 运行状态
        /// </summary>

        public bool IsBusy { get => _isBusy; set => SetProperty(ref _isBusy, value); }
        private bool _isBusy;

        /// <summary>
        /// 运行结果
        /// </summary>

        public bool IsDone { get => _isDone; set => SetProperty(ref _isDone, value); }
        private bool _isDone;

        /// <summary>
        /// 运行控制
        /// </summary>

        public bool Start { get => _start; set => SetProperty(ref _start, value); }
        private bool _start;

        /// <summary>
        /// 停止控制
        /// </summary>

        public bool Stop { get => _stop; set => SetProperty(ref _stop, value); }
        private bool _stop;

        #endregion 运行控制
    }
}