using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.ChamberA;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberA
{
    /// <summary>
    /// 处理室配置设置类
    /// 用于统一访问处理室配置参数
    /// </summary>
    public class ChaConfigureSettings
    {
        private static readonly Lazy<ChaConfigureSettings> _instance =
            new Lazy<ChaConfigureSettings>(() => new ChaConfigureSettings());

        public static ChaConfigureSettings Instance => _instance.Value;

        private ChaConfigureSettings()
        {
            // 私有构造函数，防止外部实例化
        }

        #region 处理室门控制参数

        /// <summary>
        /// 处理室门开关最小时间(秒) - 支持小数
        /// </summary>
        public double ChamberDoorMinTime =>
            ChaConfigParametersProvider.Instance.GetDoubleSettingValue(EnuChaConfigParameterCodes.PPS1);

        /// <summary>
        /// 处理室门开关最大时间(秒)
        /// </summary>
        public int ChamberDoorMaxTime =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS2);

        #endregion 处理室门控制参数

        #region 处理室ISO阀控制参数

        /// <summary>
        /// 处理室ISO阀开关最小时间(秒) - 支持小数
        /// </summary>
        public double ChamberISOValveMinTime =>
            ChaConfigParametersProvider.Instance.GetDoubleSettingValue(EnuChaConfigParameterCodes.PPS3);

        /// <summary>
        /// 处理室ISO阀开关最大时间(秒)
        /// </summary>
        public int ChamberISOValveMaxTime =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS4);

        #endregion 处理室ISO阀控制参数

        #region 处理室真空系统参数

        /// <summary>
        /// 处理室抽真空最大时间(分钟)
        /// </summary>
        public int ChamberPumpDownMaxTime =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS5);

        /// <summary>
        /// 处理室回填最大时间(分钟)
        /// </summary>
        public int ChamberBackfillMaxTime =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS6);

        /// <summary>
        /// 处理室传输压力(Torr)
        /// </summary>
        public int ChamberTransferPressure =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS7);

        /// <summary>
        /// 处理室大气压力最小值(Torr)
        /// </summary>
        public int ChamberATMPressureMinimum =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS8);

        /// <summary>
        /// 处理室压力偏移(Torr)
        /// </summary>
        public int ChamberPressureOffset =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS9);

        /// <summary>
        /// 处理室大气压力设定点(Torr)
        /// </summary>
        public int ChamberATMPressureSetpoint =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS10);

        #endregion 处理室真空系统参数

        #region 处理室电源控制参数

        /// <summary>
        /// 处理室电源开关最小时间(秒)
        /// </summary>
        public int ChamberPowerMinTime =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS11);

        /// <summary>
        /// 处理室电源开关最大时间(秒)
        /// </summary>
        public int ChamberPowerMaxTime =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS12);

        #endregion 处理室电源控制参数

        #region 处理室阀门控制参数

        /// <summary>
        /// 处理室阀门开关最小时间(秒)
        /// </summary>
        public int ChamberValveMinTime =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS13);

        /// <summary>
        /// 处理室阀门开关最大时间(秒)
        /// </summary>
        public int ChamberValveMaxTime =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS14);

        #endregion 处理室阀门控制参数

        #region 处理室RF参数

        /// <summary>
        /// 处理室RF功率最小值(W)
        /// </summary>
        public int ChamberRFPowerMin =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS15);

        /// <summary>
        /// 处理室RF功率最大值(W)
        /// </summary>
        public int ChamberRFPowerMax =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS16);

        /// <summary>
        /// 处理室RF频率最小值(MHz)
        /// </summary>
        public double ChamberRFFrequencyMin =>
            ChaConfigParametersProvider.Instance.GetDoubleSettingValue(EnuChaConfigParameterCodes.PPS17);

        /// <summary>
        /// 处理室RF频率最大值(MHz)
        /// </summary>
        public double ChamberRFFrequencyMax =>
            ChaConfigParametersProvider.Instance.GetDoubleSettingValue(EnuChaConfigParameterCodes.PPS18);

        #endregion 处理室RF参数

        #region 处理室温度参数

        /// <summary>
        /// 处理室温度最小值(℃)
        /// </summary>
        public int ChamberTemperatureMin =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS19);

        /// <summary>
        /// 处理室温度最大值(℃)
        /// </summary>
        public int ChamberTemperatureMax =>
            ChaConfigParametersProvider.Instance.GetIntSettingValue(EnuChaConfigParameterCodes.PPS20);

        #endregion 处理室温度参数

        #region 处理室压力控制参数

        /// <summary>
        /// 处理室抽真空压力偏差(Torr) - 支持小数
        /// </summary>
        public double ChamberPumpDownPressureDeviation =>
            ChaConfigParametersProvider.Instance.GetDoubleSettingValue(EnuChaConfigParameterCodes.PPS46);

        #endregion 处理室压力控制参数
    }
}