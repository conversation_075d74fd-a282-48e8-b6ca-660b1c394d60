using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// 负载锁真空状态枚举
    /// </summary>
    public enum EnuLoadlockVacuumStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 负载锁压力真空状态 (SP13: PAI6≤PPS7)
        /// </summary>
        [Description("压力真空")]
        PressureVacuum = 1,

        /// <summary>
        /// 负载锁压力非真空状态 (SP14: PAI6>PPS7)
        /// </summary>
        [Description("压力非真空")]
        PressureNoVacuum = 2
    }
}
