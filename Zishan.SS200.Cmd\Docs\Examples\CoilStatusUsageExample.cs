using System;
using <PERSON>ishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Chamber;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using <PERSON>ishan.SS200.Cmd.Enums.SS200.IOInterface.Shuttle;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Examples
{
    /// <summary>
    /// 线圈状态使用示例
    /// 展示如何在SubSystemStatus中使用新的线圈查找方法
    /// </summary>
    public class CoilStatusUsageExample
    {
        private readonly S200McuCmdService _mcuService;
        private readonly CoilStatusHelper _coilHelper;

        public CoilStatusUsageExample()
        {
            _mcuService = S200McuCmdService.Instance;
            _coilHelper = new CoilStatusHelper(_mcuService);
        }

        #region 基本使用方法

        /// <summary>
        /// 示例1：直接使用S200McuCmdService的新方法
        /// </summary>
        public void Example1_DirectServiceUsage()
        {
            // 获取Chamber A的Slit Door打开传感器线圈
            var slitDoorOpenCoil = _mcuService.GetInputCoilByEnum(
                EnuMcuDeviceType.ChamberA,
                EnuChamberDICodes.PDI12_SlitDoorOpenSensor);

            // 获取Chamber A的Slit Door关闭传感器线圈
            var slitDoorCloseCoil = _mcuService.GetInputCoilByEnum(
                EnuMcuDeviceType.ChamberA,
                EnuChamberDICodes.PDI13_SlitDoorCloseSensor);

            // 检查线圈状态并计算Slit Door位置
            if (slitDoorOpenCoil?.Coilvalue == true && slitDoorCloseCoil?.Coilvalue == false)
            {
                Console.WriteLine("Slit Door 处于打开状态");
            }
            else if (slitDoorOpenCoil?.Coilvalue == false && slitDoorCloseCoil?.Coilvalue == true)
            {
                Console.WriteLine("Slit Door 处于关闭状态");
            }
            else if (slitDoorOpenCoil?.Coilvalue == false && slitDoorCloseCoil?.Coilvalue == false)
            {
                Console.WriteLine("Slit Door 处于中间位置");
            }
            else
            {
                Console.WriteLine("Slit Door 状态异常");
            }
        }

        /// <summary>
        /// 示例2：使用CoilStatusHelper简化状态查询
        /// </summary>
        public void Example2_HelperUsage()
        {
            // 使用辅助类直接获取布尔值
            var slitDoorOpen = _coilHelper.GetCoilValue(
                EnuMcuDeviceType.ChamberA,
                EnuChamberDICodes.PDI12_SlitDoorOpenSensor);

            var slitDoorClose = _coilHelper.GetCoilValue(
                EnuMcuDeviceType.ChamberA,
                EnuChamberDICodes.PDI13_SlitDoorCloseSensor);

            // 或者直接使用计算方法
            var slitDoorStatus = _coilHelper.CalculateSlitDoorStatus(EnuMcuDeviceType.ChamberA);
            Console.WriteLine($"Slit Door 状态: {slitDoorStatus}");
        }

        #endregion

        #region Chamber状态计算示例

        /// <summary>
        /// 示例3：完整的Chamber状态计算
        /// </summary>
        public void Example3_ChamberStatusCalculation()
        {
            var deviceType = EnuMcuDeviceType.ChamberA;

            // 计算各种状态
            var slitDoorStatus = _coilHelper.CalculateSlitDoorStatus(deviceType);
            var liftPinStatus = _coilHelper.CalculateLiftPinStatus(deviceType);

            // 检查阀门状态
            var isoValveOpen = _coilHelper.GetCoilValue(deviceType, EnuChamberDICodes.PDI1_CvOpenSensor);
            var isoValveClose = _coilHelper.GetCoilValue(deviceType, EnuChamberDICodes.PDI2_CvCloseSensor);

            // 检查温控器状态
            var thermostateNormal = _coilHelper.GetCoilValue(deviceType, EnuChamberDICodes.PDI11_Thermostat);

            Console.WriteLine($"Chamber A 状态:");
            Console.WriteLine($"  Slit Door: {slitDoorStatus}");
            Console.WriteLine($"  Lift Pin: {liftPinStatus}");
            Console.WriteLine($"  ISO阀门打开: {isoValveOpen}");
            Console.WriteLine($"  ISO阀门关闭: {isoValveClose}");
            Console.WriteLine($"  温控器正常: {thermostateNormal}");
        }

        #endregion

        #region Shuttle状态计算示例

        /// <summary>
        /// 示例4：Shuttle状态计算
        /// </summary>
        public void Example4_ShuttleStatusCalculation()
        {
            var deviceType = EnuMcuDeviceType.Shuttle;

            // 检查晶圆盒门位置
            var cassetteDoorUp = _coilHelper.IsShuttleCassetteDoorAtPosition(true);
            var cassetteDoorDown = _coilHelper.IsShuttleCassetteDoorAtPosition(false);

            // 检查晶圆盒巢位置
            var cassetteNestExtended = _coilHelper.IsShuttleCassetteNestAtPosition(true);
            var cassetteNestRetracted = _coilHelper.IsShuttleCassetteNestAtPosition(false);

            // 检查Shuttle旋转传感器
            var rotateStatus = _coilHelper.AreAllCoilsActive(deviceType,
                EnuShuttleDICodes.SDI13_ShuttleRotateSensor1,
                EnuShuttleDICodes.SDI14_ShuttleRotateSensor2);

            Console.WriteLine($"Shuttle 状态:");
            Console.WriteLine($"  晶圆盒门上升: {cassetteDoorUp}");
            Console.WriteLine($"  晶圆盒门下降: {cassetteDoorDown}");
            Console.WriteLine($"  晶圆盒巢伸出: {cassetteNestExtended}");
            Console.WriteLine($"  晶圆盒巢收回: {cassetteNestRetracted}");
            Console.WriteLine($"  旋转传感器激活: {rotateStatus}");
        }

        /// <summary>
        /// 示例4B：完整的Shuttle状态更新（类似UpdateShuttleSubsystemStatus）
        /// </summary>
        public void Example4B_CompleteShuttleStatusUpdate()
        {
            var shuttleStatus = new Models.SS200.SubSystemStatus.Shuttle.ShuttleSubsystemStatus();

            // 1. 基本Shuttle状态 (MSD1-MSD3)
            shuttleStatus.ShuttleStatus = _coilHelper.CalculateShuttleStatus();

            // 2. Shuttle位置状态 (SSD1-SSD7) - 需要考虑SSC6配置
            shuttleStatus.ShuttlePositionStatus = _coilHelper.CalculateShuttlePositionStatus(
                shuttleStatus.Ssc6Config);

            // 3. 晶圆盒门和巢状态 (SSD8-SSD13) - 需要考虑SSC6配置
            shuttleStatus.CassetteDoorNestStatus = _coilHelper.CalculateCassetteDoorNestStatus(
                shuttleStatus.Ssc6Config);

            // 4. 阀门状态 (SSD14-SSD23)
            shuttleStatus.ShuttleIsoValveStatus = _coilHelper.CalculateShuttleValveStatus(
                Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleIso);

            shuttleStatus.ShuttleXvValveStatus = _coilHelper.CalculateShuttleValveStatus(
                Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleXv);

            shuttleStatus.ShuttleBackfillValveStatus = _coilHelper.CalculateShuttleValveStatus(
                Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleBackfill);

            shuttleStatus.LoadlockBleedValveStatus = _coilHelper.CalculateShuttleValveStatus(
                Enums.SS200.IOInterface.Shuttle.ShuttleValveType.LoadlockBleed);

            shuttleStatus.LoadlockBackfillValveStatus = _coilHelper.CalculateShuttleValveStatus(
                Enums.SS200.IOInterface.Shuttle.ShuttleValveType.LoadlockBackfill);

            // 5. 批次状态 (LSD1-LSD4) - 基于晶圆盒存在传感器 (SDI6-SDI9)
            shuttleStatus.Shuttle1Cassette1LotStatus = _coilHelper.CalculateLotStatus(1, 1); // LSD1
            shuttleStatus.Shuttle1Cassette2LotStatus = _coilHelper.CalculateLotStatus(1, 2); // LSD2
            shuttleStatus.Shuttle2Cassette1LotStatus = _coilHelper.CalculateLotStatus(2, 1); // LSD3
            shuttleStatus.Shuttle2Cassette2LotStatus = _coilHelper.CalculateLotStatus(2, 2); // LSD4

            Console.WriteLine($"完整Shuttle状态更新:");
            Console.WriteLine($"  基本状态: {shuttleStatus.ShuttleStatus}");
            Console.WriteLine($"  位置状态: {shuttleStatus.ShuttlePositionStatus}");
            Console.WriteLine($"  门巢状态: {shuttleStatus.CassetteDoorNestStatus}");
            Console.WriteLine($"  ISO阀门: {shuttleStatus.ShuttleIsoValveStatus}");
            Console.WriteLine($"  XV阀门: {shuttleStatus.ShuttleXvValveStatus}");
            Console.WriteLine($"  回填阀门: {shuttleStatus.ShuttleBackfillValveStatus}");
            Console.WriteLine($"  负载锁排气阀门: {shuttleStatus.LoadlockBleedValveStatus}");
            Console.WriteLine($"  负载锁回填阀门: {shuttleStatus.LoadlockBackfillValveStatus}");
            Console.WriteLine($"  批次状态: LSD1={shuttleStatus.Shuttle1Cassette1LotStatus}, " +
                            $"LSD2={shuttleStatus.Shuttle1Cassette2LotStatus}, " +
                            $"LSD3={shuttleStatus.Shuttle2Cassette1LotStatus}, " +
                            $"LSD4={shuttleStatus.Shuttle2Cassette2LotStatus}");
        }

        /// <summary>
        /// 示例5: 批次状态计算示例
        /// 演示如何使用CoilStatusHelper计算Shuttle批次状态 (LSD1-LSD4)
        /// </summary>
        public void Example5_ShuttleLotStatusCalculation()
        {
            Console.WriteLine("=== 示例5: Shuttle批次状态计算 ===");

            // 计算单个批次状态
            var lsd1Status = _coilHelper.CalculateLotStatus(1, 1); // Shuttle1 Cassette1
            var lsd2Status = _coilHelper.CalculateLotStatus(1, 2); // Shuttle1 Cassette2
            var lsd3Status = _coilHelper.CalculateLotStatus(2, 1); // Shuttle2 Cassette1
            var lsd4Status = _coilHelper.CalculateLotStatus(2, 2); // Shuttle2 Cassette2

            Console.WriteLine($"单个批次状态:");
            Console.WriteLine($"  LSD1 (Shuttle1 Cassette1): {lsd1Status}");
            Console.WriteLine($"  LSD2 (Shuttle1 Cassette2): {lsd2Status}");
            Console.WriteLine($"  LSD3 (Shuttle2 Cassette1): {lsd3Status}");
            Console.WriteLine($"  LSD4 (Shuttle2 Cassette2): {lsd4Status}");

            // 计算所有批次状态
            var allLotStatus = _coilHelper.CalculateAllLotStatus();
            Console.WriteLine($"所有批次状态:");
            foreach (var kvp in allLotStatus)
            {
                Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
            }

            // 检查晶圆盒存在状态
            var cassettePresence = _coilHelper.GetAllCassettePresenceStatus();
            Console.WriteLine($"晶圆盒存在状态:");
            foreach (var kvp in cassettePresence)
            {
                Console.WriteLine($"  {kvp.Key}: {(kvp.Value ? "存在" : "不存在")}");
            }

            Console.WriteLine("=====================================");
        }

        #endregion Shuttle状态计算示例

        #region Robot状态计算示例

        /// <summary>
        /// 示例5：Robot状态计算
        /// </summary>
        public void Example5_RobotStatusCalculation()
        {
            // 检查Paddle传感器
            var paddle1HasWafer = _coilHelper.IsWaferDetectedOnPaddle(1);
            var paddle2HasWafer = _coilHelper.IsWaferDetectedOnPaddle(2);

            // 检查Pin搜索传感器
            var pin1Detected = _coilHelper.IsPinDetected(1);
            var pin2Detected = _coilHelper.IsPinDetected(2);

            Console.WriteLine($"Robot 状态:");
            Console.WriteLine($"  Paddle 1 有晶圆: {paddle1HasWafer}");
            Console.WriteLine($"  Paddle 2 有晶圆: {paddle2HasWafer}");
            Console.WriteLine($"  Pin 1 检测到: {pin1Detected}");
            Console.WriteLine($"  Pin 2 检测到: {pin2Detected}");
        }

        #endregion

        #region 在ChamberSubsystemStatus中的实际应用示例

        /// <summary>
        /// 示例6：在ChamberSubsystemStatus中的实际应用
        /// </summary>
        public class ChamberStatusUpdater
        {
            private readonly CoilStatusHelper _coilHelper;
            private readonly ChamberSubsystemStatus _chamberStatus;
            private readonly EnuMcuDeviceType _deviceType;

            public ChamberStatusUpdater(EnuMcuDeviceType deviceType)
            {
                _deviceType = deviceType;
                _coilHelper = new CoilStatusHelper(S200McuCmdService.Instance);

                // 修复：ChamberSubsystemStatus是抽象类，需要通过具体子类实例化
                _chamberStatus = deviceType switch
                {
                    EnuMcuDeviceType.ChamberA => new ChamberASubsystemStatus(),
                    EnuMcuDeviceType.ChamberB => new ChamberBSubsystemStatus(),
                    _ => throw new ArgumentException($"不支持的设备类型: {deviceType}")
                };
            }

            /// <summary>
            /// 更新所有Chamber状态
            /// </summary>
            public void UpdateAllStatus()
            {
                // 更新位置状态
                _chamberStatus.SlitDoorStatus = _coilHelper.CalculateSlitDoorStatus(_deviceType);
                _chamberStatus.LiftPinStatus = _coilHelper.CalculateLiftPinStatus(_deviceType);

                // 更新阀门状态
                UpdateValveStatus();

                // 更新真空状态
                UpdateVacuumStatus();

                // 更新气体状态
                UpdateGasStatus();
            }

            private void UpdateValveStatus()
            {
                // ISO阀门状态
                var isoOpen = _coilHelper.GetCoilValue(_deviceType, EnuChamberDICodes.PDI1_CvOpenSensor);
                var isoClose = _coilHelper.GetCoilValue(_deviceType, EnuChamberDICodes.PDI2_CvCloseSensor);

                if (isoOpen && !isoClose)
                    _chamberStatus.IsoValveStatus = EnuValveStatus.Open;
                else if (!isoOpen && isoClose)
                    _chamberStatus.IsoValveStatus = EnuValveStatus.Close;
                else
                    _chamberStatus.IsoValveStatus = EnuValveStatus.None;

                // 节流阀状态
                var throttleOpen = _coilHelper.GetCoilValue(_deviceType, EnuChamberDICodes.PDI3_TvOpenSensor);
                var throttleClose = _coilHelper.GetCoilValue(_deviceType, EnuChamberDICodes.PDI4_TvCloseSensor);

                if (throttleOpen && !throttleClose)
                    _chamberStatus.ThrottleValveStatus = EnuValveStatus.Open;
                else if (!throttleOpen && throttleClose)
                    _chamberStatus.ThrottleValveStatus = EnuValveStatus.Close;
                else
                    _chamberStatus.ThrottleValveStatus = EnuValveStatus.None;
            }

            private void UpdateVacuumStatus()
            {
                // 这里可以根据实际的真空传感器来计算真空状态
                // 示例代码，实际需要根据具体的传感器配置
            }

            private void UpdateGasStatus()
            {
                // 这里可以根据实际的气体阀门状态来计算气体状态
                // 示例代码，实际需要根据具体的阀门配置
            }

            public ChamberSubsystemStatus GetStatus()
            {
                return _chamberStatus;
            }
        }

        #endregion

        #region 调试和诊断方法

        /// <summary>
        /// 示例7：调试和诊断方法
        /// </summary>
        public void Example7_DiagnosticMethods()
        {
            // 获取所有设备的线圈状态
            foreach (EnuMcuDeviceType deviceType in Enum.GetValues<EnuMcuDeviceType>())
            {
                Console.WriteLine($"\n{deviceType} 设备线圈状态:");
                var allStatus = _coilHelper.GetAllCoilStatus(deviceType);

                foreach (var kvp in allStatus)
                {
                    Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
                }
            }
        }

        #endregion
    }
}
