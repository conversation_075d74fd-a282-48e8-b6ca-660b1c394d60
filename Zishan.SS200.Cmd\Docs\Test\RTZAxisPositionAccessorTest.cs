using System;
using System.Diagnostics;
using log4net;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// RTZ轴位置访问器测试类
    /// 验证方案二实现的正确性、性能和一致性
    /// </summary>
    public class RTZAxisPositionAccessorTest
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RTZAxisPositionAccessorTest));

        /// <summary>
        /// 测试基本功能的正确性
        /// </summary>
        public static void TestBasicFunctionality()
        {
            try
            {
                _logger.Info("=== RTZ轴位置访问器基本功能测试 ===");

                var interlock = SS200InterLockMain.Instance;
                var rtzPosition = interlock.RTZAxisPosition;

                // 测试数据有效性检查
                bool isDataValid = rtzPosition.IsRTZPositionDataValid;
                _logger.Info($"数据有效性检查: {isDataValid}");

                if (!isDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过基本功能测试");
                    return;
                }

                // 测试基本位置访问
                _logger.Info("--- 测试基本位置访问 ---");
                int tAxisStep = rtzPosition.CurrentTAxisStep;
                int rAxisStep = rtzPosition.CurrentRAxisStep;
                int zAxisStep = rtzPosition.CurrentZAxisStep;

                _logger.Info($"T轴步进值: {tAxisStep}");
                _logger.Info($"R轴步进值: {rAxisStep}");
                _logger.Info($"Z轴步进值: {zAxisStep}");

                // 验证步进值的合理性（不应该是极端值）
                bool tAxisReasonable = Math.Abs(tAxisStep) < 1000000;
                bool rAxisReasonable = Math.Abs(rAxisStep) < 1000000;
                bool zAxisReasonable = Math.Abs(zAxisStep) < 1000000;

                _logger.Info($"T轴步进值合理性: {tAxisReasonable}");
                _logger.Info($"R轴步进值合理性: {rAxisReasonable}");
                _logger.Info($"Z轴步进值合理性: {zAxisReasonable}");

                // 测试物理单位转换
                _logger.Info("--- 测试物理单位转换 ---");
                double tAxisDegree = rtzPosition.CurrentTAxisDegree;
                double rAxisLength = rtzPosition.CurrentRAxisLength;
                double zAxisHeight = rtzPosition.CurrentZAxisHeight;

                _logger.Info($"T轴角度: {tAxisDegree:F2}°");
                _logger.Info($"R轴长度: {rAxisLength:F2}mm");
                _logger.Info($"Z轴高度: {zAxisHeight:F2}mm");

                // 验证物理值的合理性
                bool tDegreeReasonable = Math.Abs(tAxisDegree) <= 360;
                bool rLengthReasonable = Math.Abs(rAxisLength) < 1000;
                bool zHeightReasonable = Math.Abs(zAxisHeight) < 1000;

                _logger.Info($"T轴角度合理性: {tDegreeReasonable}");
                _logger.Info($"R轴长度合理性: {rLengthReasonable}");
                _logger.Info($"Z轴高度合理性: {zHeightReasonable}");

                _logger.Info("=== 基本功能测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"基本功能测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试与原有实现的一致性
        /// </summary>
        public static void TestConsistencyWithOriginal()
        {
            try
            {
                _logger.Info("=== RTZ轴位置访问器一致性测试 ===");

                var interlock = SS200InterLockMain.Instance;
                var rtzPosition = interlock.RTZAxisPosition;
                var mcuService = S200McuCmdService.Instance;

                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过一致性测试");
                    return;
                }

                // 比较步进值的一致性
                _logger.Info("--- 测试步进值一致性 ---");
                bool tAxisConsistent = rtzPosition.CurrentTAxisStep == mcuService.CurrentTAxisStep;
                bool rAxisConsistent = rtzPosition.CurrentRAxisStep == mcuService.CurrentRAxisStep;
                bool zAxisConsistent = rtzPosition.CurrentZAxisStep == mcuService.CurrentZAxisStep;

                _logger.Info($"T轴步进值一致性: {tAxisConsistent} (RTZ: {rtzPosition.CurrentTAxisStep}, MCU: {mcuService.CurrentTAxisStep})");
                _logger.Info($"R轴步进值一致性: {rAxisConsistent} (RTZ: {rtzPosition.CurrentRAxisStep}, MCU: {mcuService.CurrentRAxisStep})");
                _logger.Info($"Z轴步进值一致性: {zAxisConsistent} (RTZ: {rtzPosition.CurrentZAxisStep}, MCU: {mcuService.CurrentZAxisStep})");

                // 比较物理值的一致性（允许微小的浮点误差）
                _logger.Info("--- 测试物理值一致性 ---");
                double tDegreeDiff = Math.Abs(rtzPosition.CurrentTAxisDegree - mcuService.CurrentTAxisDegree);
                double rLengthDiff = Math.Abs(rtzPosition.CurrentRAxisLength - mcuService.CurrentRAxisLength);
                double zHeightDiff = Math.Abs(rtzPosition.CurrentZAxisHeight - mcuService.CurrentZAxisHeight);

                bool tDegreeConsistent = tDegreeDiff < 0.001;
                bool rLengthConsistent = rLengthDiff < 0.001;
                bool zHeightConsistent = zHeightDiff < 0.001;

                _logger.Info($"T轴角度一致性: {tDegreeConsistent} (差值: {tDegreeDiff:F6})");
                _logger.Info($"R轴长度一致性: {rLengthConsistent} (差值: {rLengthDiff:F6})");
                _logger.Info($"Z轴高度一致性: {zHeightConsistent} (差值: {zHeightDiff:F6})");

                // 比较组合方法的一致性
                _logger.Info("--- 测试组合方法一致性 ---");
                var rtzSteps = rtzPosition.GetCurrentRTZSteps();
                var mcuSteps = mcuService.GetCurrentRTZSteps();
                bool stepsConsistent = rtzSteps.Equals(mcuSteps);

                var rtzPhysical = rtzPosition.GetCurrentRTZPhysicalValues();
                var mcuPhysical = mcuService.GetCurrentRTZPhysicalValues();
                bool physicalConsistent = 
                    Math.Abs(rtzPhysical.TAxisDegree - mcuPhysical.TAxisDegree) < 0.001 &&
                    Math.Abs(rtzPhysical.RAxisLength - mcuPhysical.RAxisLength) < 0.001 &&
                    Math.Abs(rtzPhysical.ZAxisHeight - mcuPhysical.ZAxisHeight) < 0.001;

                _logger.Info($"步进值组合一致性: {stepsConsistent}");
                _logger.Info($"物理值组合一致性: {physicalConsistent}");

                // 整体一致性评估
                bool overallConsistent = tAxisConsistent && rAxisConsistent && zAxisConsistent &&
                                       tDegreeConsistent && rLengthConsistent && zHeightConsistent &&
                                       stepsConsistent && physicalConsistent;

                if (overallConsistent)
                {
                    _logger.Info("✓ 所有测试项都与原有实现保持一致");
                }
                else
                {
                    _logger.Warn("✗ 发现与原有实现不一致的地方");
                }

                _logger.Info("=== 一致性测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"一致性测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试性能表现
        /// </summary>
        public static void TestPerformance()
        {
            try
            {
                _logger.Info("=== RTZ轴位置访问器性能测试 ===");

                var interlock = SS200InterLockMain.Instance;
                var rtzPosition = interlock.RTZAxisPosition;
                var mcuService = S200McuCmdService.Instance;

                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过性能测试");
                    return;
                }

                const int iterations = 10000;
                var sw = Stopwatch.StartNew();

                // 测试RTZ访问器性能
                _logger.Info($"--- 测试RTZ访问器性能 ({iterations}次迭代) ---");
                sw.Restart();
                for (int i = 0; i < iterations; i++)
                {
                    var _ = rtzPosition.CurrentTAxisStep;
                    var __ = rtzPosition.CurrentRAxisStep;
                    var ___ = rtzPosition.CurrentZAxisStep;
                }
                sw.Stop();
                long rtzTime = sw.ElapsedMilliseconds;

                // 测试直接MCU服务性能
                _logger.Info($"--- 测试MCU服务性能 ({iterations}次迭代) ---");
                sw.Restart();
                for (int i = 0; i < iterations; i++)
                {
                    var _ = mcuService.CurrentTAxisStep;
                    var __ = mcuService.CurrentRAxisStep;
                    var ___ = mcuService.CurrentZAxisStep;
                }
                sw.Stop();
                long mcuTime = sw.ElapsedMilliseconds;

                // 测试组合访问性能
                _logger.Info($"--- 测试组合访问性能 ({iterations}次迭代) ---");
                sw.Restart();
                for (int i = 0; i < iterations; i++)
                {
                    var _ = rtzPosition.GetCurrentRTZSteps();
                }
                sw.Stop();
                long combinedTime = sw.ElapsedMilliseconds;

                // 性能分析
                _logger.Info($"RTZ访问器性能: {rtzTime}ms");
                _logger.Info($"MCU服务性能: {mcuTime}ms");
                _logger.Info($"组合访问性能: {combinedTime}ms");
                _logger.Info($"RTZ访问器平均单次时间: {(double)rtzTime / iterations:F6}ms");
                _logger.Info($"MCU服务平均单次时间: {(double)mcuTime / iterations:F6}ms");

                double performanceRatio = (double)rtzTime / mcuTime;
                _logger.Info($"性能比率 (RTZ/MCU): {performanceRatio:F2}");

                if (performanceRatio <= 1.2)
                {
                    _logger.Info("✓ RTZ访问器性能表现良好（开销 ≤ 20%）");
                }
                else if (performanceRatio <= 2.0)
                {
                    _logger.Warn("⚠ RTZ访问器性能可接受（开销 ≤ 100%）");
                }
                else
                {
                    _logger.Warn("✗ RTZ访问器性能需要优化（开销 > 100%）");
                }

                _logger.Info("=== 性能测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"性能测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试安全检查功能
        /// </summary>
        public static void TestSafetyFeatures()
        {
            try
            {
                _logger.Info("=== RTZ轴位置访问器安全功能测试 ===");

                var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;

                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过安全功能测试");
                    return;
                }

                // 测试单轴安全检查
                _logger.Info("--- 测试单轴安全检查 ---");
                bool tAxisSafe = rtzPosition.IsAxisPositionInSafeRange("T");
                bool rAxisSafe = rtzPosition.IsAxisPositionInSafeRange("R");
                bool zAxisSafe = rtzPosition.IsAxisPositionInSafeRange("Z");

                _logger.Info($"T轴安全状态: {tAxisSafe}");
                _logger.Info($"R轴安全状态: {rAxisSafe}");
                _logger.Info($"Z轴安全状态: {zAxisSafe}");

                // 测试整体安全检查
                bool allSafe = rtzPosition.AreAllAxesInSafeRange;
                bool expectedAllSafe = tAxisSafe && rAxisSafe && zAxisSafe;
                bool allSafeConsistent = allSafe == expectedAllSafe;

                _logger.Info($"整体安全状态: {allSafe}");
                _logger.Info($"预期整体安全状态: {expectedAllSafe}");
                _logger.Info($"整体安全检查一致性: {allSafeConsistent}");

                // 测试无效轴名称处理
                bool invalidAxisResult = rtzPosition.IsAxisPositionInSafeRange("INVALID");
                _logger.Info($"无效轴名称处理: {invalidAxisResult} (应该为false)");

                if (allSafeConsistent && !invalidAxisResult)
                {
                    _logger.Info("✓ 安全检查功能工作正常");
                }
                else
                {
                    _logger.Warn("✗ 安全检查功能存在问题");
                }

                _logger.Info("=== 安全功能测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"安全功能测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试格式化功能
        /// </summary>
        public static void TestFormattingFeatures()
        {
            try
            {
                _logger.Info("=== RTZ轴位置访问器格式化功能测试 ===");

                var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;

                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过格式化功能测试");
                    return;
                }

                // 测试各种格式化方法
                string displayText = rtzPosition.GetRTZPositionDisplayText();
                string simpleText = rtzPosition.GetRTZPositionSimpleText();
                string jsonText = rtzPosition.GetRTZPositionJsonText();
                string diagnosticInfo = rtzPosition.GetDiagnosticInfo();

                _logger.Info($"详细显示文本长度: {displayText?.Length ?? 0}");
                _logger.Info($"简化显示文本长度: {simpleText?.Length ?? 0}");
                _logger.Info($"JSON文本长度: {jsonText?.Length ?? 0}");
                _logger.Info($"诊断信息长度: {diagnosticInfo?.Length ?? 0}");

                // 验证格式化结果不为空
                bool displayTextValid = !string.IsNullOrEmpty(displayText);
                bool simpleTextValid = !string.IsNullOrEmpty(simpleText);
                bool jsonTextValid = !string.IsNullOrEmpty(jsonText);
                bool diagnosticInfoValid = !string.IsNullOrEmpty(diagnosticInfo);

                _logger.Info($"详细显示文本有效性: {displayTextValid}");
                _logger.Info($"简化显示文本有效性: {simpleTextValid}");
                _logger.Info($"JSON文本有效性: {jsonTextValid}");
                _logger.Info($"诊断信息有效性: {diagnosticInfoValid}");

                if (displayTextValid && simpleTextValid && jsonTextValid && diagnosticInfoValid)
                {
                    _logger.Info("✓ 格式化功能工作正常");
                }
                else
                {
                    _logger.Warn("✗ 格式化功能存在问题");
                }

                _logger.Info("=== 格式化功能测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"格式化功能测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            _logger.Info("开始运行RTZ轴位置访问器所有测试...");

            TestBasicFunctionality();
            Console.WriteLine();

            TestConsistencyWithOriginal();
            Console.WriteLine();

            TestPerformance();
            Console.WriteLine();

            TestSafetyFeatures();
            Console.WriteLine();

            TestFormattingFeatures();

            _logger.Info("所有RTZ轴位置访问器测试运行完成");
        }
    }
}
