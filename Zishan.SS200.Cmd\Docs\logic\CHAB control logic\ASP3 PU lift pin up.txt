ASP3 
PU lift pin up
	chamber trigger status review
MPS1~MPS2
		MPS1 IDLE
no alarm
			chamber run status review
MPS3~MPS5
				MPS4 / MPS3A
					chamber lift pin status review
SP4~SP6
						SP4
lift pin up
							command done
						SP6
lit pin up/down between
							PAC11 ALARM
						others status
							PAC14 ALARM
						SP5
							chamber wafer slot status review
CHA:(LSS9 LSS10) 
CHB:(LSS11 LSS12)
								CHA: LSS9 xx=1 or/and LSS10 xx=1
CHB: LSS11 xx=1 or/and LSS12 xx=1
									slit door status review
SP1 / SP2
										SP1
slit door open
											robot run status review
MRS1~3
												MRS1 or MRS3
robot run status is busy or alarm
													PAC16 ALARM
												MRS2
robot run status is idle
													robot R-axis status review
RS18 or others status
														RS18
robot R-axis zero position
															PDO12=1
PDO13=0
																SP5---SP6---SP4
slit door status change logic
																	PPS3≤time≤PPS4
																		command done
																	time>PPS4
																		PAC12 ALARM
																	time<PPS3
																		PAC13 ALARM
																others status change logic
																	PAC10 ALARM
														others status
															PAC15 ALARM
										SP2
slit door close
											PDO12=1
PDO13=0
												SP5---SP6---SP4
slit door status change logic
													PPS3≤time≤PPS4
														command done
													time>PPS4
														PAC12 ALARM
													time<PPS3
														PAC13 ALARM
												others status change logic
													PAC10 ALARM
								CHA: LSS9=0 LSS10=0
CHB: LSS11=0 LSS12=0
									slit door status review
SP1 / SP2
										SP1
slit door open
											robot run status review
MRS1~3
												MRS1 or MRS3
robot run status is busy or alarm
													PAC16 ALARM
												MRS2
robot run status is idle
													robot R-axis status review
RS10~18 or others status
														one position of (RS10~17)
															robot T-axis status review
RS1~9 or others status
																one position of (RS1~9)
																	PDO12=1
PDO13=0
																		SP5---SP6---SP4
slit door status change logic
																			PPS3≤time≤PPS4
																				command done
																			time>PPS4
																				PAC12 ALARM
																			time<PPS3
																				PAC13 ALARM
																		others status change logic
																			PAC10 ALARM
																others status
																	PAC15 ALARM
														RS18
robot R-axis at zero position
															PDO12=1
PDO13=0
																SP5---SP6---SP4
slit door status change logic
																	PPS3≤time≤PPS4
																		command done
																	time>PPS4
																		PAC12 ALARM
																	time<PPS3
																		PAC13 ALARM
																others status change logic
																	PAC10 ALARM
														others status
															PAC15 ALARM
										SP2
slit door close
											PDO12=1
PDO13=0
												SP5---SP6---SP4
slit door status change logic
													PPS3≤time≤PPS4
														command done
													time>PPS4
														PAC12 ALARM
													time<PPS3
														PAC13 ALARM
												others status change logic
													PAC10 ALARM
				MPS3B
					PAC2 ALARM
				MPS5
					PAC3 ALARM
		MPS2
alarm
			PAC1 ALARM