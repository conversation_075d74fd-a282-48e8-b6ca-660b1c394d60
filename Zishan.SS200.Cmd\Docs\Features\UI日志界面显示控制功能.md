# UI日志界面显示控制功能

## 功能概述

UI日志服务现在支持通过配置文件控制是否在UI界面上展示日志信息。此功能可以帮助用户根据需要启用或禁用UI界面的日志显示，减少界面干扰，同时保持日志文件记录功能。

## 配置方式

### 1. 配置文件设置

在 `Config.ini` 文件的 `[General]` 节中添加以下配置项：

```ini
[General]
;是否在UI界面上展示日志，默认：True
ShowUILog = True
```

**配置值说明：**
- `True`: 启用UI界面日志显示，日志会在界面上实时展示（默认）
- `False`: 禁用UI界面日志显示，只记录到日志文件

### 2. 代码中的配置读取

配置项会在应用启动时自动读取到 `App.AppIniConfig.ShowUILog` 属性中。

## 使用效果

### 启用UI界面日志显示时 (ShowUILog = True)

- 日志会实时显示在UI界面的日志面板中
- 用户可以在界面上查看实时日志信息
- 同时也会记录到日志文件中

### 禁用UI界面日志显示时 (ShowUILog = False)

- UI界面的日志面板不会显示新的日志信息
- 日志仍然会正常记录到日志文件中
- 减少UI界面的信息干扰，提升界面清洁度

## 技术实现

### 1. 配置属性定义

在 `IniConfig` 类中添加了新的配置属性：

```csharp
/// <summary>
/// 是否在UI界面上展示日志，默认：true
/// </summary>
public bool ShowUILog { get; set; } = true;
```

### 2. 配置读取

在 `PubHelper.GetAppIniConfig` 方法中添加了配置读取逻辑：

```csharp
// 读取UI界面日志显示配置
if (parsedData["General"]["ShowUILog"] != null)
{
    iniConfig.ShowUILog = Convert.ToBoolean(parsedData["General"]["ShowUILog"]);
}
```

### 3. 日志显示控制

在 `UILogService.AddLog` 方法中根据配置决定是否在UI界面显示：

```csharp
// 检查配置是否在UI界面上展示日志
bool showUILog = false;
try
{
    showUILog = App.AppIniConfig?.ShowUILog ?? true;
}
catch (Exception ex)
{
    // 如果获取配置失败，默认显示UI日志
    _logger?.Debug($"获取UI日志显示配置失败，使用默认值: {ex.Message}");
    showUILog = true;
}

// 根据配置决定是否在UI界面上添加日志
if (showUILog)
{
    // 使用 Dispatcher.Invoke 确保在 UI 线程上操作集合
    Application.Current?.Dispatcher?.Invoke(() =>
    {
        // 调用主视图模型的AddLog方法
        _LogViewModel.AddLog(logModel.Message);
        blUiAdd = true;
    });
}

// 无论是否在UI显示，都记录到日志文件
if (!blUiAdd)
{
    AppLog.Info(logModel.Message);
}
```

## 配置文件示例

完整的配置文件示例：

```ini
[General] 
;Modbus 服务端IP地址
Ip = 127.0.0.1

;Modbus 服务端端口
Port = 502

;是否自动启动，默认：True
AutoStart = True

;是否在UI日志中显示调用方法名和行号，默认：False
ShowCallerInfoInUILog = True

;是否在UI界面上展示日志，默认：True
ShowUILog = True

[Devices]
;设备配置...
```

## 使用建议

1. **开发阶段**：建议设置 `ShowUILog = True`，便于实时查看日志信息进行调试
2. **生产环境**：可以根据需要设置 `ShowUILog = False`，减少界面干扰，提升用户体验
3. **性能考虑**：禁用UI日志显示可以减少UI线程的负担，提升应用性能
4. **日志保留**：无论UI显示是否启用，日志文件记录功能始终保持，确保问题追踪能力

## 注意事项

1. 配置更改仅在当前会话中生效，重启应用程序后会恢复到配置文件中的默认设置
2. 如果配置文件中没有此项，默认值为 `True`（显示UI日志）
3. 如果配置读取失败，系统会使用默认值并记录调试信息
4. 此功能与 `ShowCallerInfoInUILog` 配置独立，可以单独控制
5. UI界面的配置变更不会修改Config.ini配置文件

## 相关文件

- 配置文件：`Configs/Config.ini`
- 配置类：`Config/IniConfig.cs`
- 配置读取：`Common/PubHelper.cs`
- 日志服务：`Services/UILogService.cs`
- 日志视图模型：`ViewModels/Dock/LogViewModel.cs`
