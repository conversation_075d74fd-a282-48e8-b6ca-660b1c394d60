# TransferWaferViewModel.cs AppLog批量替换脚本
# 用于将所有AppLog调用替换为_logger调用

$filePath = "Zishan.SS200.Cmd\ViewModels\TransferWaferViewModel.cs"

# 读取文件内容
$content = Get-Content $filePath -Raw

# 定义替换规则
$replacements = @{
    'AppLog\.Info\(' = '_logger.Info('
    'AppLog\.Debug\(' = '_logger.Debug('
    'AppLog\.Warn\(' = '_logger.Warn('
    'AppLog\.Error\(' = '_logger.Error('
    'AppLog\.InfoFormatted\(' = '_logger.InfoFormat('
    'AppLog\.DebugFormatted\(' = '_logger.DebugFormat('
    'AppLog\.WarnFormatted\(' = '_logger.WarnFormat('
}

# 执行替换
foreach ($pattern in $replacements.Keys) {
    $replacement = $replacements[$pattern]
    $content = $content -replace $pattern, $replacement
    Write-Host "已替换: $pattern -> $replacement"
}

# 写回文件
Set-Content $filePath -Value $content -Encoding UTF8

Write-Host "TransferWaferViewModel.cs AppLog替换完成！"

# 显示替换统计
$remainingAppLog = (Select-String -Path $filePath -Pattern "AppLog\." | Measure-Object).Count
Write-Host "剩余AppLog调用数量: $remainingAppLog"

if ($remainingAppLog -eq 0) {
    Write-Host "✅ 所有AppLog调用已成功替换为_logger调用"
} else {
    Write-Host "⚠️ 仍有AppLog调用需要手动检查"
    Select-String -Path $filePath -Pattern "AppLog\." | ForEach-Object {
        Write-Host "  行 $($_.LineNumber): $($_.Line.Trim())"
    }
}
