---
description: 
globs: 
alwaysApply: false
---
# WPF和MVVM开发规范

## XAML编码规范

### 命名约定
- 控件名称使用驼峰命名法，以控件类型为后缀
  - 例如：`userNameTextBox`, `submitButton`
- x:Name属性仅在需要在代码后置文件中访问控件时使用
- 资源键使用Pascal命名法
  - 例如：`<SolidColorBrush x:Key="PrimaryBackgroundBrush" />`

### 布局规则
- 优先使用Grid和StackPanel进行布局
- 避免硬编码宽度和高度，优先使用自适应布局
- 使用Margin和Padding控制元素间距，保持一致性
- 复杂UI应拆分为用户控件或自定义控件
- 使用Grid.RowDefinitions和Grid.ColumnDefinitions定义网格结构

### 样式和资源
- 通用样式定义在App.xaml或专用资源字典中
- 避免在控件上直接设置样式属性，优先使用样式资源
- 颜色和画笔定义为资源，便于主题切换
- 使用StaticResource而非DynamicResource，除非确实需要动态更新

### 数据绑定
- 所有数据绑定应指定Mode和UpdateSourceTrigger
- 对于文本框，使用`UpdateSourceTrigger=PropertyChanged`实现实时更新
- 使用StringFormat或IValueConverter处理显示格式
- 避免在XAML中使用硬编码字符串，优先使用资源或绑定

### 命令绑定
- 使用命令绑定代替事件处理器
- 命令参数应通过CommandParameter传递
- 使用复合命令处理需要多个接收者的命令

## MVVM实现规范

### ViewModel基础规则
- 所有ViewModel类应实现INotifyPropertyChanged接口
- 使用CommunityToolkit.Mvvm的ObservableObject作为基类
- 使用[ObservableProperty]特性自动实现属性通知
- 使用[RelayCommand]特性实现命令

### 属性设计
- 公开属性应具有合适的数据类型和初始值
- 集合属性应使用ObservableCollection<T>
- 复杂对象属性应实现INotifyPropertyChanged
- 使用计算属性表示派生值，而非存储冗余数据

### 命令实现
- 命令应遵循单一职责原则
- 长时间运行的操作应实现为异步命令
- 命令方法应包含参数验证和错误处理
- 命令可执行条件应明确定义

### 导航和对话框
- 使用导航服务处理视图间导航
- 使用对话框服务显示消息和获取用户输入
- 避免在ViewModel中直接引用View
- 使用消息中介者模式实现ViewModel间通信

### 依赖注入
- ViewModel通过构造函数注入依赖服务
- 避免使用服务定位器模式
- 在应用启动时配置依赖注入容器
- 使用接口定义服务契约，便于测试和替换实现

### 异步操作
- 使用async/await进行异步操作
- 避免使用.Result或.Wait()，防止死锁
- 异步操作应提供用户反馈（如加载指示器）
- 异步方法应使用CancellationToken支持取消操作

## 测试规范
- ViewModel应设计为可测试
- 使用依赖注入和接口使组件可以被模拟
- 单元测试应覆盖所有公开方法和属性
- 使用UI自动化测试验证视图集成

## 性能优化

### 数据虚拟化
- 对大型集合使用UI虚拟化（VirtualizingStackPanel）
- 使用延迟加载技术加载大量数据
- 实现分页机制处理大型数据集
- 使用数据缓存减少重复加载

### 绑定优化
- 减少绑定链的长度和复杂性
- 避免在ItemsControl中使用复杂的数据模板
- 使用ObservableCollection而非手动实现集合变更通知
- 考虑使用批量更新减少UI更新频率

### 资源管理
- 及时释放不再使用的资源
- 使用弱引用避免内存泄漏
- 优化图像资源，使用适当的分辨率和格式
- 延迟加载不立即可见的UI元素

### 渲染优化
- 避免过度使用透明度和复杂效果
- 使用BitmapCache提高静态内容的渲染性能
- 减少视觉树的深度和复杂性
- 使用硬件加速，启用RenderOptions.ProcessRenderMode
