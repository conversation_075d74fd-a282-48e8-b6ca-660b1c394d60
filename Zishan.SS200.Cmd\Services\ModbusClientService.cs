using log4net;
using NModbus;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Net.Sockets;
using System.Threading;
using Zishan.SS200.Cmd.Extensions;

namespace Zishan.SS200.Cmd.Services
{
    public class ModbusClientService : IModbusClientService
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ModbusClientService));
        private IModbusMaster _master;
        private TcpClient _tcpClient;
        private bool _isConnected;
        private string _strMsg;
        private DateTime _lastErrorTime = DateTime.MinValue;
        private static readonly TimeSpan ErrorThrottleInterval = TimeSpan.FromSeconds(3);

        private void ShowThrottledError(string message)
        {
            var now = DateTime.Now;
            if (now - _lastErrorTime >= ErrorThrottleInterval)
            {
                HcGrowlExtensions.Error(message);
                _lastErrorTime = now;
            }
            // 始终记录日志，不受节流影响
            _logger.Error(message);
        }

        public bool IsConnected => _isConnected;

        public IModbusMaster Master => _master;

        public ModbusClientService()
        {
            _logger.Info("Modbus客户端服务已初始化");
        }

        public async Task<bool> ConnectAsync(string ipAddress, int port, int timeout = 3000)
        {
            try
            {
                _logger.Info($"正在连接到Modbus服务器 {ipAddress}:{port}");

                _tcpClient = new TcpClient();
                using var cts = new CancellationTokenSource(timeout);
                await _tcpClient.ConnectAsync(ipAddress, port).WaitAsync(cts.Token);

                _master = new ModbusFactory().CreateMaster(_tcpClient);
                _isConnected = true;

                _logger.Info("Modbus服务器连接成功");
                return true;
            }
            catch (Exception ex)
            {
                _strMsg = $"连接Modbus服务器{ipAddress}:{port},失败: {ex.Message}";
                ShowThrottledError(_strMsg);
                _isConnected = false;
                return false;
            }
        }

        public Task DisconnectAsync()
        {
            try
            {
                _logger.Info("正在断开Modbus连接");
                _master?.Dispose();
                _tcpClient?.Close();
                _isConnected = false;
                _logger.Info("Modbus连接已断开");
            }
            catch (Exception ex)
            {
                _strMsg = $"断开Modbus连接时发生错误: {ex.Message}";
                ShowThrottledError(_strMsg);
            }

            return Task.CompletedTask;
        }

        public async Task<ushort[]> ReadHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints)
        {
            try
            {
                _logger.Debug($"正在读取保持寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}");
                var result = await _master.ReadHoldingRegistersAsync(slaveAddress, startAddress, numberOfPoints);
                _logger.Debug($"读取保持寄存器成功 - 16进制值:{string.Join(",", result.Select(v => $"{v:X4}"))}");
                return result;
            }
            catch (Exception ex)
            {
                _strMsg = $"读取保持寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}，错误信息：{ex.Message}";
                ShowThrottledError(_strMsg);
                throw;
            }
        }

        public async Task WriteHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort[] values)
        {
            try
            {
                _logger.Debug($"正在写入保持寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 值:{string.Join(",", values)}");
                await _master.WriteMultipleRegistersAsync(slaveAddress, startAddress, values);
                _logger.Debug("写入保持寄存器成功");
            }
            catch (Exception ex)
            {
                _strMsg = $"写入保持寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 值:{string.Join(",", values)}，错误信息：{ex.Message}";
                ShowThrottledError(_strMsg);
                throw;
            }
        }

        public async Task WriteSingleRegisterAsync(byte slaveAddress, ushort address, ushort value)
        {
            try
            {
                _logger.Debug($"正在写入单个寄存器 - 从站地址:{slaveAddress}, 地址:{address}, 值:{value}");
                await _master.WriteSingleRegisterAsync(slaveAddress, address, value);
                _logger.Debug("写入单个寄存器成功");
            }
            catch (Exception ex)
            {
                _strMsg = $"正在写入单个寄存器 - 从站地址:{slaveAddress}, 地址:{address}, 值:{value}，错误信息: {ex.Message}";
                ShowThrottledError(_strMsg);
                throw;
            }
        }

        public async Task<ushort[]> ReadInputRegistersAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints)
        {
            try
            {
                _logger.Debug($"正在读取输入寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}");
                var result = await _master.ReadInputRegistersAsync(slaveAddress, startAddress, numberOfPoints);
                _logger.Debug($"读取输入寄存器成功 - 值:{string.Join(",", result)}");
                return result;
            }
            catch (Exception ex)
            {
                _strMsg = $"读取输入寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}，错误信息: {ex.Message}";
                ShowThrottledError(_strMsg);
                throw;
            }
        }

        // 新增线圈读写方法实现
        public async Task<bool[]> ReadCoilsAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints)
        {
            try
            {
                _logger.Debug($"正在读取线圈 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}");
                var result = await _master.ReadCoilsAsync(slaveAddress, startAddress, numberOfPoints);
                _logger.Debug($"读取线圈成功 - 值:{string.Join(",", result)}");
                return result;
            }
            catch (Exception ex)
            {
                _strMsg = $"读取线圈 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}，错误信息: {ex.Message}";
                ShowThrottledError(_strMsg);
                throw;
            }
        }

        public async Task<bool[]> ReadDiscreteInputsAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints)
        {
            try
            {
                _logger.Debug($"正在读取离散输入 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}");
                var result = await _master.ReadInputsAsync(slaveAddress, startAddress, numberOfPoints);
                _logger.Debug($"读取离散输入成功 - 值:{string.Join(",", result)}");
                return result;
            }
            catch (Exception ex)
            {
                _strMsg = $"读取离散输入 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}，错误信息: {ex.Message}";
                ShowThrottledError(_strMsg);
                throw;
            }
        }

        public async Task WriteCoilsAsync(byte slaveAddress, ushort startAddress, bool[] values)
        {
            try
            {
                _logger.Debug($"正在写入线圈 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 值:{string.Join(",", values)}");
                await _master.WriteMultipleCoilsAsync(slaveAddress, startAddress, values);
                _logger.Debug("写入线圈成功");
            }
            catch (Exception ex)
            {
                _strMsg = $"写入线圈 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 值:{string.Join(",", values)}，错误信息: {ex.Message}";
                ShowThrottledError(_strMsg);
                throw;
            }
        }

        public async Task WriteSingleCoilAsync(byte slaveAddress, ushort address, bool value)
        {
            try
            {
                _logger.Debug($"正在写入单个线圈 - 从站地址:{slaveAddress}, 地址:{address}, 值:{value}");
                await _master.WriteSingleCoilAsync(slaveAddress, address, value);
                _logger.Debug("写入单个线圈成功");
            }
            catch (Exception ex)
            {
                _strMsg = $"写入单个线圈 - 从站地址:{slaveAddress}, 地址:{address}, 值:{value}，错误信息: {ex.Message}";
                ShowThrottledError(_strMsg);
                throw;
            }
        }

        public void Dispose()
        {
            _master?.Dispose();
            _tcpClient?.Dispose();
        }
    }
}