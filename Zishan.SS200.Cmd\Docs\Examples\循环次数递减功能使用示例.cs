using System;
using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// 循环次数递减功能使用示例
    /// 展示如何实现和使用循环次数递减功能
    /// </summary>
    public partial class LoopCountDecrementExample : ObservableObject
    {
        /// <summary>
        /// 循环执行次数：-1代表无限循环，默认1执行一次
        /// </summary>
        [ObservableProperty]
        private int loopCount = 1;

        /// <summary>
        /// 剩余循环次数：-1代表无限循环，0表示循环结束
        /// </summary>
        [ObservableProperty]
        private int remainingLoopCount = 1;

        /// <summary>
        /// 是否正在执行命令
        /// </summary>
        [ObservableProperty]
        private bool isExecutingCommand = false;

        /// <summary>
        /// 取消令牌源
        /// </summary>
        private CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 当LoopCount属性变化时，同步更新RemainingLoopCount
        /// </summary>
        partial void OnLoopCountChanged(int value)
        {
            // 只有在非执行状态下才同步更新剩余次数
            if (!IsExecutingCommand)
            {
                RemainingLoopCount = value;
            }
        }

        /// <summary>
        /// 执行循环任务的示例方法
        /// </summary>
        public async Task ExecuteLoopTaskAsync()
        {
            if (IsExecutingCommand)
                return;

            try
            {
                // 初始化循环控制变量
                int currentLoop = 0;
                bool isInfiniteLoop = LoopCount == -1;
                
                // 初始化剩余循环次数
                RemainingLoopCount = LoopCount;

                // 设置执行状态
                IsExecutingCommand = true;

                // 创建取消令牌源
                _cancellationTokenSource = new CancellationTokenSource();

                Console.WriteLine($"开始执行循环任务 - 循环模式: {(isInfiniteLoop ? "无限循环" : $"{LoopCount}次")}");

                // 循环执行任务 - 使用剩余循环次数控制
                while (RemainingLoopCount != 0 && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    currentLoop++;

                    // 显示当前循环信息
                    string currentLoopInfo = isInfiniteLoop ? $"第{currentLoop}次" : $"第{currentLoop}次 (剩余{RemainingLoopCount}次)";
                    Console.WriteLine($"=== {currentLoopInfo} 任务开始 ===");

                    // 模拟任务执行
                    await SimulateTaskExecutionAsync();

                    Console.WriteLine($"=== {currentLoopInfo} 任务完成 ===");

                    // 递减剩余循环次数（无限循环时保持-1）
                    if (!isInfiniteLoop && RemainingLoopCount > 0)
                    {
                        RemainingLoopCount--;
                        Console.WriteLine($"剩余循环次数: {RemainingLoopCount}");
                    }

                    // 如果还有剩余循环，等待一段时间
                    if (RemainingLoopCount != 0 && !_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        Console.WriteLine("等待2秒后开始下一次循环...");
                        await Task.Delay(2000, _cancellationTokenSource.Token);
                    }
                }

                // 循环结束
                string completionMessage = _cancellationTokenSource.Token.IsCancellationRequested
                    ? $"循环任务已取消 (已完成{currentLoop - 1}次)"
                    : $"循环任务全部完成 (共执行{currentLoop}次)";
                Console.WriteLine(completionMessage);
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("循环任务已被取消");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"循环任务执行异常: {ex.Message}");
            }
            finally
            {
                IsExecutingCommand = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 停止循环执行
        /// </summary>
        public void StopLoop()
        {
            try
            {
                if (_cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    _cancellationTokenSource.Cancel();
                    Console.WriteLine("用户请求停止循环执行");
                }
                else
                {
                    Console.WriteLine("当前没有正在执行的循环操作");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"停止循环执行异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 模拟任务执行
        /// </summary>
        private async Task SimulateTaskExecutionAsync()
        {
            // 模拟任务执行时间
            await Task.Delay(1000);
            Console.WriteLine("任务执行完成");
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static async Task RunAllExamples()
        {
            var example = new LoopCountDecrementExample();

            Console.WriteLine("=== 循环次数递减功能示例 ===\n");

            // 示例1：执行3次循环
            Console.WriteLine("示例1：执行3次循环");
            example.LoopCount = 3;
            await example.ExecuteLoopTaskAsync();
            Console.WriteLine();

            // 等待一段时间
            await Task.Delay(1000);

            // 示例2：执行1次
            Console.WriteLine("示例2：执行1次");
            example.LoopCount = 1;
            await example.ExecuteLoopTaskAsync();
            Console.WriteLine();

            // 示例3：无限循环（演示停止功能）
            Console.WriteLine("示例3：无限循环（3秒后自动停止）");
            example.LoopCount = -1;
            
            // 启动无限循环任务
            var infiniteTask = example.ExecuteLoopTaskAsync();
            
            // 3秒后停止
            await Task.Delay(3000);
            example.StopLoop();
            
            // 等待任务完成
            await infiniteTask;
            Console.WriteLine();

            Console.WriteLine("=== 所有示例执行完成 ===");
        }
    }

    /// <summary>
    /// 循环次数显示转换器示例
    /// </summary>
    public static class LoopCountDisplayExample
    {
        /// <summary>
        /// 演示循环次数显示转换
        /// </summary>
        public static void DemonstrateLoopCountDisplay()
        {
            Console.WriteLine("=== 循环次数显示转换示例 ===");

            var converter = new Converters.LoopCountDisplayConverter();

            // 测试不同的循环次数值
            int[] testValues = { -1, 0, 1, 5, 10 };

            foreach (int value in testValues)
            {
                var displayText = converter.Convert(value, typeof(string), null, null);
                Console.WriteLine($"循环次数: {value} -> 显示: {displayText}");
            }

            Console.WriteLine();
        }
    }
}
