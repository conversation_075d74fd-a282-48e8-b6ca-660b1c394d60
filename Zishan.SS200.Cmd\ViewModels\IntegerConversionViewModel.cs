using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Windows;

namespace Zishan.SS200.Cmd.ViewModels
{
    public partial class IntegerConversionViewModel : ObservableObject
    {
        /// <summary>
        /// 32位整数输入值
        /// </summary>
        [ObservableProperty]
        private string int32Value = string.Empty;

        /// <summary>
        /// 第一个16位整数输入值
        /// </summary>
        [ObservableProperty]
        private string int16Value1 = string.Empty;

        /// <summary>
        /// 第二个16位整数输入值
        /// </summary>
        [ObservableProperty]
        private string int16Value2 = string.Empty;

        /// <summary>
        /// 是否使用十六进制显示
        /// </summary>
        [ObservableProperty]
        private bool useHexDisplay = true;

        /// <summary>
        /// 转换成功提示
        /// </summary>
        [ObservableProperty]
        private string conversionSuccess = string.Empty;

        /// <summary>
        /// 转换错误信息
        /// </summary>
        [ObservableProperty]
        private string conversionError = string.Empty;

        public IntegerConversionViewModel()
        {
            // 构造函数
        }

        /// <summary>
        /// 32位转16位命令
        /// </summary>
        [RelayCommand]
        private void ConvertInt32ToInt16()
        {
            try
            {
                ConversionError = string.Empty;
                ConversionSuccess = string.Empty;

                if (string.IsNullOrWhiteSpace(Int32Value))
                {
                    ConversionError = "请输入32位整数";
                    return;
                }

                int int32;
                // 支持十六进制和十进制输入
                if (Int32Value.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                {
                    try
                    {
                        int32 = Convert.ToInt32(Int32Value.Substring(2), 16);
                    }
                    catch
                    {
                        ConversionError = "无效的十六进制整数格式";
                        return;
                    }
                }
                else if (!int.TryParse(Int32Value, out int32))
                {
                    ConversionError = "无效的32位整数格式";
                    return;
                }

                // 转换为两个16位整数
                ushort high = (ushort)((int32 >> 16) & 0xFFFF);
                ushort low = (ushort)(int32 & 0xFFFF);

                // 根据显示格式设置输出
                if (UseHexDisplay)
                {
                    Int16Value1 = $"0x{high:X4}";
                    Int16Value2 = $"0x{low:X4}";
                }
                else
                {
                    Int16Value1 = high.ToString();
                    Int16Value2 = low.ToString();
                }

                ConversionSuccess = "转换成功";
            }
            catch (Exception ex)
            {
                ConversionError = $"转换失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 16位转32位命令
        /// </summary>
        [RelayCommand]
        private void ConvertInt16ToInt32()
        {
            try
            {
                ConversionError = string.Empty;
                ConversionSuccess = string.Empty;

                if (string.IsNullOrWhiteSpace(Int16Value1) || string.IsNullOrWhiteSpace(Int16Value2))
                {
                    ConversionError = "请输入两个16位整数";
                    return;
                }

                // 解析第一个16位整数
                ushort high;
                string value1 = Int16Value1.Trim();
                if (value1.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                {
                    try
                    {
                        high = Convert.ToUInt16(value1.Substring(2), 16);
                    }
                    catch
                    {
                        ConversionError = "无效的第一个16位十六进制整数格式";
                        return;
                    }
                }
                else if (!ushort.TryParse(value1, out high))
                {
                    ConversionError = "无效的第一个16位整数格式";
                    return;
                }

                // 解析第二个16位整数
                ushort low;
                string value2 = Int16Value2.Trim();
                if (value2.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                {
                    try
                    {
                        low = Convert.ToUInt16(value2.Substring(2), 16);
                    }
                    catch
                    {
                        ConversionError = "无效的第二个16位十六进制整数格式";
                        return;
                    }
                }
                else if (!ushort.TryParse(value2, out low))
                {
                    ConversionError = "无效的第二个16位整数格式";
                    return;
                }

                // 组合为32位整数
                int int32 = (high << 16) | low;

                // 根据显示格式设置输出
                if (UseHexDisplay)
                {
                    Int32Value = $"0x{int32:X8}";
                }
                else
                {
                    Int32Value = int32.ToString();
                }

                ConversionSuccess = "转换成功";
            }
            catch (Exception ex)
            {
                ConversionError = $"转换失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 复制32位整数到剪贴板
        /// </summary>
        [RelayCommand]
        private void CopyInt32Value()
        {
            if (!string.IsNullOrWhiteSpace(Int32Value))
            {
                Clipboard.SetText(Int32Value);
                ConversionSuccess = "已复制32位整数到剪贴板";
                ConversionError = string.Empty;
            }
        }

        /// <summary>
        /// 复制第一个16位整数到剪贴板
        /// </summary>
        [RelayCommand]
        private void CopyInt16Value1()
        {
            if (!string.IsNullOrWhiteSpace(Int16Value1))
            {
                Clipboard.SetText(Int16Value1);
                ConversionSuccess = "已复制第一个16位整数到剪贴板";
                ConversionError = string.Empty;
            }
        }

        /// <summary>
        /// 复制第二个16位整数到剪贴板
        /// </summary>
        [RelayCommand]
        private void CopyInt16Value2()
        {
            if (!string.IsNullOrWhiteSpace(Int16Value2))
            {
                Clipboard.SetText(Int16Value2);
                ConversionSuccess = "已复制第二个16位整数到剪贴板";
                ConversionError = string.Empty;
            }
        }

        /// <summary>
        /// 切换显示格式（十六进制/十进制）
        /// </summary>
        [RelayCommand]
        private void ToggleDisplayFormat()
        {
            // 转换现有的值到新格式
            ConvertExistingValuesToCurrentFormat();
        }

        /// <summary>
        /// 将当前显示的值转换为当前选择的格式
        /// </summary>
        private void ConvertExistingValuesToCurrentFormat()
        {
            try
            {
                // 转换32位整数
                if (!string.IsNullOrWhiteSpace(Int32Value))
                {
                    int value;
                    if (Int32Value.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                    {
                        value = Convert.ToInt32(Int32Value.Substring(2), 16);
                    }
                    else if (int.TryParse(Int32Value, out value))
                    {
                        // 值已成功解析
                    }
                    else
                    {
                        // 无法解析，保持原值
                        return;
                    }

                    Int32Value = UseHexDisplay ? $"0x{value:X8}" : value.ToString();
                }

                // 转换第一个16位整数
                if (!string.IsNullOrWhiteSpace(Int16Value1))
                {
                    ushort value;
                    if (Int16Value1.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                    {
                        value = Convert.ToUInt16(Int16Value1.Substring(2), 16);
                    }
                    else if (ushort.TryParse(Int16Value1, out value))
                    {
                        // 值已成功解析
                    }
                    else
                    {
                        // 无法解析，保持原值
                        return;
                    }

                    Int16Value1 = UseHexDisplay ? $"0x{value:X4}" : value.ToString();
                }

                // 转换第二个16位整数
                if (!string.IsNullOrWhiteSpace(Int16Value2))
                {
                    ushort value;
                    if (Int16Value2.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                    {
                        value = Convert.ToUInt16(Int16Value2.Substring(2), 16);
                    }
                    else if (ushort.TryParse(Int16Value2, out value))
                    {
                        // 值已成功解析
                    }
                    else
                    {
                        // 无法解析，保持原值
                        return;
                    }

                    Int16Value2 = UseHexDisplay ? $"0x{value:X4}" : value.ToString();
                }
            }
            catch (Exception ex)
            {
                ConversionError = $"格式转换失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 清除所有字段
        /// </summary>
        [RelayCommand]
        private void ClearAllFields()
        {
            Int32Value = string.Empty;
            Int16Value1 = string.Empty;
            Int16Value2 = string.Empty;
            ConversionError = string.Empty;
            ConversionSuccess = string.Empty;
        }
    }
}