﻿AR55
GW from CB
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			paddle status review
RS53~RS54
				RS53=0 RS54=0
					compare ABS(RTF-RP3) with ABS(RTF-RP7)
						ABS(RTF-RP3)≤ABS(RTF-RP7)
							AR3
T-axis smooth to cooling
								AR24
Move Z-axis height smooth to CT get
									AR13
R-axis smooth to cooling extend
										AR67
wafer status CB to smooth paddle
											AR30
Move Z-axis height smooth to CB put
												AR19
R-axis zero position
													command done
						ABS(RTF-RP4)>ABS(RTF-RP8)
							AR7
T-axis nose to cooling
								AR28
Move Z-axis height nose to CT get
									AR17
R-axis nose to cooling extend
										AR78
wafer status CB to nose paddle
											AR32
Move Z-axis height smooth to CB put
												AR19
R-axis zero position
													command done
				RS53=1 RS54=0
					AR7
T-axis nose to cooling
						AR28
Move Z-axis height nose to CT get
							AR17
R-axis nose to cooling extend
								AR78
wafer status CB to nose paddle
									AR32
Move Z-axis height smooth to CB put
										AR19
R-axis zero position
											command done
				RS53=0 RS54=1
					AR3
T-axis smooth to cooling
						AR24
Move Z-axis height smooth to CT get
							AR13
R-axis smooth to cooling extend
								AR67
wafer status CB to smooth paddle
									AR30
Move Z-axis height smooth to CB put
										AR19
R-axis zero position
											command done
				RS53=1 RS54=1
					RA17 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM