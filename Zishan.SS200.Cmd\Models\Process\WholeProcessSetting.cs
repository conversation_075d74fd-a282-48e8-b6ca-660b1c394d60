using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

//using Zishan.Robot.Shared.AttributeExtend;
//using Zishan.Robot.Shared.Common;
//using Zishan.Robot.Shared.Enums;

namespace Zishan.SS200.Cmd.Models.Process
{
    public class WholeProcessSetting : BindableBase
    {
        /// <summary>
        /// 命令执行中
        /// </summary>
        public bool CommandBusy { get => _commandBusy; set => SetProperty(ref _commandBusy, value); }
        private bool _commandBusy;

        /// <summary>
        /// 回复
        /// </summary>
        public string PlcResponse { get => _plcResponse; set => SetProperty(ref _plcResponse, value); }
        private string _plcResponse;

        /// <summary>
        /// 命令
        /// </summary>
        public string PcControlWord { get => _pcControlWord; set => SetProperty(ref _pcControlWord, value); }
        private string _pcControlWord;

        /// <summary>
        /// 保存参数
        /// </summary>
        public bool SaveParameter { get => _saveParameter; set => SetProperty(ref _saveParameter, value); }
        private bool _saveParameter;

        /// <summary>
        /// 执行指令
        /// </summary>
        public bool CommandEnable { get => _commandEnable; set => SetProperty(ref _commandEnable, value); }
        private bool _commandEnable;

        /// <summary>
        /// 报警确认
        /// </summary>
        public bool AlarmConfirmed { get => _alarmConfirmed; set => SetProperty(ref _alarmConfirmed, value); }
        private bool _alarmConfirmed;

        /// <summary>
        /// 子流程步号
        /// </summary>
        public int SubStepNumber { get => _subStepNumber; set => SetProperty(ref _subStepNumber, value); }
        private int _subStepNumber;

        #region 整机复位

        /// <summary>
        /// 整机复位 开启条件
        /// </summary>
        public bool ResetStartCondition { get => _resetStartCondition; set => SetProperty(ref _resetStartCondition, value); }
        private bool _resetStartCondition;

        /// <summary>
        /// 整机复位 运行条件
        /// </summary>
        public bool ResetRunCondition { get => _resetRunCondition; set => SetProperty(ref _resetRunCondition, value); }
        private bool _resetRunCondition;

        /// <summary>
        /// 整机复位 颜色
        /// </summary>
        public bool ResetRunBusy { get => _resetRunBusy; set => SetProperty(ref _resetRunBusy, value); }
        private bool _resetRunBusy;

        /// <summary>
        /// 整机复位
        /// </summary>
        public bool ResetStart { get => _resetStart; set => SetProperty(ref _resetStart, value); }
        private bool _resetStart;

        #endregion 整机复位

        /// <summary>
        /// 主流程步号
        /// </summary>
        public int MainStepNumber { get => _mainStepNumber; set => SetProperty(ref _mainStepNumber, value); }
        private int _mainStepNumber;

        #region 整机运行

        /// <summary>
        /// 整机运行 开启条件
        /// </summary>
        public bool AutoRunStartCondition { get => _autoRunStartCondition; set => SetProperty(ref _autoRunStartCondition, value); }
        private bool _autoRunStartCondition;

        /// <summary>
        /// 整机运行  运行条件
        /// </summary>
        public bool AutoRunRunCondition { get => _autoRunRunCondition; set => SetProperty(ref _autoRunRunCondition, value); }
        private bool _autoRunRunCondition;

        /// <summary>
        /// 整机运行 颜色
        /// </summary>
        public bool AutoRunBusy { get => _autoRunBusy; set => SetProperty(ref _autoRunBusy, value); }
        private bool _autoRunBusy;

        /// <summary>
        /// 整机运行
        /// </summary>
        public bool AutoRunStart { get => _autoRunStart; set => SetProperty(ref _autoRunStart, value); }
        private bool _autoRunStart;

        #endregion 整机运行

        /// <summary>
        /// 装载花篮
        /// </summary>
        public bool LoadCS { get => _loadCS; set => SetProperty(ref _loadCS, value); }
        private bool _loadCS;

        /// <summary>
        /// 卸载花篮
        /// </summary>
        public bool UnLoadCS { get => _unLoadCS; set => SetProperty(ref _unLoadCS, value); }
        private bool _unLoadCS;

        /// <summary>
        /// 中止整机运行
        /// </summary>
        public bool ProcessAutoRunPause { get => _ProcessAutoRunPause; set => SetProperty(ref _ProcessAutoRunPause, value); }
        private bool _ProcessAutoRunPause;

        /// <summary>
        /// 结束整机运行
        /// </summary>
        public bool WholeAutoRunEnd { get => _WholeAutoRunEnd; set => SetProperty(ref _WholeAutoRunEnd, value); }
        private bool _WholeAutoRunEnd;

        /// <summary>
        /// 停止/工作完成，请取走晶舟盒并按键确认【提示用】
        /// </summary>
        public bool WorkDone { get => _workDone; set => SetProperty(ref _workDone, value); }
        private bool _workDone;

        #region 整机未运行时卸载花篮

        /// <summary>
        /// 整机未运行时卸载花篮 开启条件
        /// </summary>
        public bool UnLoadCSAutoRunStartCondition { get => _unLoadCSAutoRunStartCondition; set => SetProperty(ref _unLoadCSAutoRunStartCondition, value); }
        private bool _unLoadCSAutoRunStartCondition;

        /// <summary>
        /// 整机未运行时卸载花篮 运行条件
        /// </summary>
        public bool UnLoadCSAutoRunRunCondition { get => _unLoadCSAutoRunRunCondition; set => SetProperty(ref _unLoadCSAutoRunRunCondition, value); }
        private bool _unLoadCSAutoRunRunCondition;

        /// <summary>
        /// 整机未运行时卸载花篮 颜色
        /// </summary>
        public bool UnLoadCSAutoRunBusy { get => _unLoadCSAutoRunBusy; set => SetProperty(ref _unLoadCSAutoRunBusy, value); }
        private bool _unLoadCSAutoRunBusy;

        /// <summary>
        /// 整机未运行时卸载花篮
        /// </summary>
        public bool UnLoadCSAutoRunStart { get => _unLoadCSAutoRunStart; set => SetProperty(ref _unLoadCSAutoRunStart, value); }
        private bool _unLoadCSAutoRunStart;

        #endregion 整机未运行时卸载花篮

        #region 一键收片

        /// <summary>
        /// 一键收片 开启条件
        /// </summary>
        public bool RecoverStartCondition { get => _recoverStartCondition; set => SetProperty(ref _recoverStartCondition, value); }
        private bool _recoverStartCondition;

        /// <summary>
        /// 一键收片 运行条件
        /// </summary>
        public bool RecoverRunCondition { get => _recoverRunCondition; set => SetProperty(ref _recoverRunCondition, value); }
        private bool _recoverRunCondition;

        /// <summary>
        ///  一键收片 颜色
        /// </summary>
        public bool RecoverBusy { get => _recoverBusy; set => SetProperty(ref _recoverBusy, value); }
        private bool _recoverBusy;

        /// <summary>
        /// 一键收片
        /// </summary>
        public bool RecoverStart { get => _recoverStart; set => SetProperty(ref _recoverStart, value); }
        private bool _recoverStart;

        #endregion 一键收片

        /// <summary>
        /// 腔体、机械臂上 Wafer状态
        /// </summary>
        public string ChamberALeftWafer { get => _chamberALeftWafer; set => SetProperty(ref _chamberALeftWafer, value); }
        private string _chamberALeftWafer;

        public string ChamberARightWafer { get => _chamberARightWafer; set => SetProperty(ref _chamberARightWafer, value); }
        private string _chamberARightWafer;

        public string ChamberBLeftWafer { get => _chamberBLeftWafer; set => SetProperty(ref _chamberBLeftWafer, value); }
        private string _chamberBLeftWafer;

        public string ChamberBRightWafer { get => _chamberBRightWafer; set => SetProperty(ref _chamberBRightWafer, value); }
        private string _chamberBRightWafer;

        public string ChamberCLeftWafer { get => _chamberCLeftWafer; set => SetProperty(ref _chamberCLeftWafer, value); }
        private string _chamberCLeftWafer;

        public string ChamberCRightWafer { get => _chamberCRightWafer; set => SetProperty(ref _chamberCRightWafer, value); }
        private string _chamberCRightWafer;

        public string CpLeftWafer { get => _cpLeftWafer; set => SetProperty(ref _cpLeftWafer, value); }
        private string _cpLeftWafer;

        public string CpRightWafer { get => _cpRightWafer; set => SetProperty(ref _cpRightWafer, value); }
        private string _cpRightWafer;

        public string PaddleNoseLeftWafer { get => _paddleNoseLeftWafer; set => SetProperty(ref _paddleNoseLeftWafer, value); }
        private string _paddleNoseLeftWafer;

        public string PaddleNoseRightWafer { get => _paddleNoseRightWafer; set => SetProperty(ref _paddleNoseRightWafer, value); }
        private string _paddleNoseRightWafer;

        public string PaddleSmoothLeftWafer { get => _paddleSmoothLeftWafer; set => SetProperty(ref _paddleSmoothLeftWafer, value); }
        private string _paddleSmoothLeftWafer;

        public string PaddleSmoothRightWafer { get => _paddleSmoothRightWafer; set => SetProperty(ref _paddleSmoothRightWafer, value); }
        private string _paddleSmoothRightWafer;
    }
}