using System;
using log4net;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// Shuttle AlarmCode 使用示例
    /// 演示如何访问和使用 Shuttle 子系统的报警代码
    /// </summary>
    public class ShuttleAlarmCodeExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ShuttleAlarmCodeExample));

        /// <summary>
        /// 演示 Shuttle 报警代码的基本使用方法
        /// </summary>
        public static void DemonstrateShuttleAlarmUsage()
        {
            try
            {
                _logger.Info("=== Shuttle AlarmCode 使用示例 ===");

                // 获取 SS200InterLockMain 实例
                var controlCenter = SS200InterLockMain.Instance;

                // 现在可以像访问 Robot 和 Chamber 报警一样访问 Shuttle 报警
                var alarm_robot = controlCenter.AlarmCode.Robot.RA1_SystemBusyReject.Content;
                var alarm_CHA = controlCenter.AlarmCode.ChamberA.PAC1_SystemAbnormalReject.Content;
                var alarm_CHB = controlCenter.AlarmCode.ChamberB.PAC1_SystemAbnormalReject.Content;

                // 新增的 Shuttle 报警代码访问
                var alarm_Shuttle = controlCenter.AlarmCode.Shuttle.SA1_SystemBusyReject.Content;

                _logger.Info($"Robot 报警 (RA1): {alarm_robot}");
                _logger.Info($"Chamber A 报警 (PAC1): {alarm_CHA}");
                _logger.Info($"Chamber B 报警 (PAC1): {alarm_CHB}");
                _logger.Info($"Shuttle 报警 (SA1): {alarm_Shuttle}");

                // 演示更多 Shuttle 报警代码
                _logger.Info("\n=== 更多 Shuttle 报警代码示例 ===");

                var shuttleAlarms = new[]
                {
                    controlCenter.AlarmCode.Shuttle.SA1_SystemBusyReject,
                    controlCenter.AlarmCode.Shuttle.SA2_SystemAlarmReject,
                    controlCenter.AlarmCode.Shuttle.SA3_CassetteNestMoveTimeout,
                    controlCenter.AlarmCode.Shuttle.SA4_CassetteNestSpeedTooHigh,
                    controlCenter.AlarmCode.Shuttle.SA5_CassetteNestPositionFailure,
                    controlCenter.AlarmCode.Shuttle.SA6_ShuttleMoveTimeout,
                    controlCenter.AlarmCode.Shuttle.SA7_ShuttleMoveTooFast,
                    controlCenter.AlarmCode.Shuttle.SA8_ShuttleUpDownPositionFailure,
                    controlCenter.AlarmCode.Shuttle.SA9_ShuttleRotateTimeout
                };

                foreach (var alarm in shuttleAlarms)
                {
                    if (alarm != null)
                    {
                        _logger.Info($"代码: {alarm.Code} | 英文: {alarm.Content} | 中文: {alarm.ChsContent}");
                    }
                }

                _logger.Info("Shuttle AlarmCode 示例演示完成！");
            }
            catch (Exception ex)
            {
                _logger.Error($"演示 Shuttle AlarmCode 时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示如何在实际应用中检查 Shuttle 报警状态
        /// </summary>
        public static void CheckShuttleAlarmStatus()
        {
            try
            {
                _logger.Info("=== 检查 Shuttle 报警状态示例 ===");

                var controlCenter = SS200InterLockMain.Instance;

                // 检查系统忙碌报警
                var busyAlarm = controlCenter.AlarmCode.Shuttle.SA1_SystemBusyReject;
                if (busyAlarm != null)
                {
                    _logger.Info($"Shuttle 系统忙碌报警: {busyAlarm.ChsContent}");
                    // 在实际应用中，这里可以添加相应的处理逻辑
                }

                // 检查系统报警拒绝
                var systemAlarm = controlCenter.AlarmCode.Shuttle.SA2_SystemAlarmReject;
                if (systemAlarm != null)
                {
                    _logger.Info($"Shuttle 系统报警拒绝: {systemAlarm.ChsContent}");
                    // 在实际应用中，这里可以添加相应的处理逻辑
                }

                // 检查移动超时报警
                var timeoutAlarm = controlCenter.AlarmCode.Shuttle.SA6_ShuttleMoveTimeout;
                if (timeoutAlarm != null)
                {
                    _logger.Info($"Shuttle 移动超时报警: {timeoutAlarm.ChsContent}");
                    // 在实际应用中，这里可以添加相应的处理逻辑
                }

                _logger.Info("Shuttle 报警状态检查完成！");
            }
            catch (Exception ex)
            {
                _logger.Error($"检查 Shuttle 报警状态时发生错误: {ex.Message}", ex);
            }
        }
    }
}