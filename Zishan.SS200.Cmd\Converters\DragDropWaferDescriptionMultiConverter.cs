using System;
using System.Globalization;
using System.Windows.Data;
using Zishan.SS200.Cmd.Models.IR400;

namespace Zishan.SS200.Cmd.Converters
{
    public class DragDropWaferDescriptionMultiConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            //Console.WriteLine($"values length: {values.Length}");
            //for (int i = 0; i < values.Length; i++)
            //{
            //    Console.WriteLine($"values[{i}]: {values[i]}");
            //}

            var value = values[0];
            //var cassette = values[1] as Cassette;
            // 使用dataContext和cassette...

            if (value is Wafer wafer)
            {
                return wafer.ToShortString();
            }

            // if (value is IWaferDragDropInfo wafer2)
            // {
            //     //if (cassette is null)
            //     //{
            //     //    MessageBox.Show("cassette is NUll");//获取不到cassette
            //     //}
            //
            //     var strWaferDragUiMsg = $"Wafer_{wafer2.WaferNo}";
            //     return strWaferDragUiMsg;
            // }

            return value;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}