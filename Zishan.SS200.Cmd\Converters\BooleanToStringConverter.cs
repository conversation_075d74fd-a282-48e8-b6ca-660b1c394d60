using System;
using System.Globalization;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 将布尔值转换为字符串的转换器
    /// </summary>
    public class BooleanToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                string stringParam = parameter as string;
                if (!string.IsNullOrEmpty(stringParam) && stringParam.Contains(","))
                {
                    string[] strings = stringParam.Split(',');
                    if (strings.Length >= 2)
                    {
                        string trueString = strings[0].Trim();
                        string falseString = strings[1].Trim();
                        
                        return boolValue ? trueString : falseString;
                    }
                }
                
                // 默认字符串
                return boolValue ? "是" : "否";
            }
            
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 