{"version": 1, "rules": [{"pattern": "**/*.cs", "formatter": {"indentStyle": "space", "indentWidth": 4, "useTabs": false}, "linter": {"enabled": true, "rules": {"no-unused-vars": "warn", "no-undef": "error"}}}, {"pattern": "**/*.xaml", "formatter": {"indentStyle": "space", "indentWidth": 4, "useTabs": false}, "linter": {"enabled": true}}, {"pattern": "**/*.json", "formatter": {"indentStyle": "space", "indentWidth": 2, "useTabs": false}}, {"pattern": "**/*.md", "formatter": {"indentStyle": "space", "indentWidth": 2, "useTabs": false}}, {"pattern": "**/Services/**/*.cs", "formatter": {"organizeImports": true}, "linter": {"enabled": true, "rules": {"no-unused-vars": "warn", "no-undef": "error", "require-await": "warn"}}}, {"pattern": "**/ViewModels/**/*.cs", "formatter": {"organizeImports": true}, "linter": {"enabled": true, "rules": {"no-unused-vars": "warn", "no-undef": "error", "require-await": "warn"}}}, {"pattern": "**/Models/**/*.cs", "formatter": {"organizeImports": true}, "linter": {"enabled": true, "rules": {"no-unused-vars": "warn", "no-undef": "error"}}}, {"pattern": "**/Configs/**/*.json", "formatter": {"indentStyle": "space", "indentWidth": 2, "useTabs": false}}, {"pattern": "**/bin/**", "formatter": {"enabled": false}, "linter": {"enabled": false}}, {"pattern": "**/obj/**", "formatter": {"enabled": false}, "linter": {"enabled": false}}, {"pattern": "**/.vs/**", "formatter": {"enabled": false}, "linter": {"enabled": false}}, {"pattern": "**/*.c<PERSON><PERSON>j", "formatter": {"indentStyle": "space", "indentWidth": 2, "useTabs": false}}, {"pattern": "**/*.sln", "formatter": {"enabled": false}}, {"pattern": "**/Commands/**/*.cs", "formatter": {"organizeImports": true}, "linter": {"enabled": true, "rules": {"no-unused-vars": "warn", "no-undef": "error"}}}, {"pattern": "**/Extensions/**/*.cs", "formatter": {"organizeImports": true}, "linter": {"enabled": true, "rules": {"no-unused-vars": "warn", "no-undef": "error"}}}, {"pattern": "**/Tests/**/*.cs", "formatter": {"organizeImports": true}, "linter": {"enabled": true, "rules": {"no-unused-vars": "warn", "no-undef": "error"}}}], "settings": {"editor": {"formatOnSave": true, "tabSize": 4, "insertSpaces": true, "detectIndentation": true, "trimTrailingWhitespace": true, "insertFinalNewline": true}, "files": {"exclude": ["**/bin/**", "**/obj/**", "**/.vs/**", "**/packages/**"]}, "search": {"exclude": ["**/bin/**", "**/obj/**", "**/.vs/**", "**/packages/**"]}, "csharp": {"format": {"enable": true, "newLine": {"beforeOpenBrace": {"method": "sameLine", "constructor": "sameLine", "anonymousMethod": "sameLine", "control": "sameLine", "block": "sameLine", "switch": "sameLine", "property": "sameLine", "indexer": "sameLine", "event": "sameLine", "accessor": "sameLine", "anonymousTypeInitializer": "sameLine", "lambdaExpressionBody": "sameLine"}}, "indentation": {"useTabs": false, "tabSize": 4, "indentationSize": 4}}, "diagnostics": {"enable": true}}, "rules": {"common": {"general": {"enabled": true, "description": "项目通用开发规范和技术栈"}, "document": {"enabled": true, "description": "项目文档编写规范和标准"}, "git": {"enabled": true, "description": "辅助生成 git 提交信息"}, "gitflow": {"enabled": true, "description": "Git工作流规则"}, "automation": {"enabled": true, "description": "自动化测试规范"}, "logging": {"enabled": true, "description": "日志记录规范"}, "error-handling": {"enabled": true, "description": "错误处理规范"}, "security": {"enabled": true, "description": "安全性规范"}, "performance": {"enabled": true, "description": "性能优化规范"}}, "language": {"c-sharpcn": {"enabled": true, "description": "C#语言开发规范"}, "wpf-mvvm": {"enabled": true, "description": "WPF和MVVM开发规范"}, "modbus-communication": {"enabled": true, "description": "Modbus通信协议和实现规范"}}}}}