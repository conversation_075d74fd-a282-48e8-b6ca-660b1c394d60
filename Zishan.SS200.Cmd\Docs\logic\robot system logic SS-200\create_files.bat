@echo off
setlocal enabledelayedexpansion

echo Creating txt files from xmind files...
set "count_new=0"
set "count_exist=0"

for %%f in (*.xmind) do (
    set "filename=%%~nf.txt"
    if exist "!filename!" (
        echo File already exists: !filename!
        set /a count_exist+=1
    ) else (
        type nul > "!filename!"
        if exist "!filename!" (
            echo Created new file: !filename!
            set /a count_new+=1
        ) else (
            echo Error creating file: !filename!
        )
    )
)

echo.
echo Summary:
echo - Files already exist: %count_exist%
echo - New files created: %count_new%
echo - Total files processed: %count_exist% + %count_new%

if %count_new%==0 (
    echo No new files were created.
) else (
    echo Successfully created %count_new% new files.
)

echo All files have been processed successfully!
pause 