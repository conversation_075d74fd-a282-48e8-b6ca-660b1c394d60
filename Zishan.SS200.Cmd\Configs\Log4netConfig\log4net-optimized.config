<?xml version="1.0" encoding="utf-8" ?>
<log4net>
	<!-- 异步错误日志Appender -->
	<appender name="asyncErrorAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="512" />
		<lossy value="false" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="ERROR" />
		</evaluator>
		<appender-ref ref="errorAppender" />
	</appender>

	<!-- 异步警告日志Appender -->
	<appender name="asyncWarnAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="256" />
		<lossy value="false" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="WARN" />
		</evaluator>
		<appender-ref ref="warnAppender" />
	</appender>

	<!-- 异步信息日志Appender -->
	<appender name="asyncInfoAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="256" />
		<lossy value="true" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="INFO" />
		</evaluator>
		<appender-ref ref="infoAppender" />
	</appender>

	<!-- 异步调试日志Appender (仅开发环境使用) -->
	<appender name="asyncDebugAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="128" />
		<lossy value="true" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="DEBUG" />
		</evaluator>
		<appender-ref ref="debugAppender" />
	</appender>

	<!-- 基础错误日志Appender -->
	<appender name="errorAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="ERROR" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\err.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<!-- 优化的文件大小和数量设置 -->
		<param name="MaxSizeRollBackups" value="30" />
		<param name="MaximumFileSize" value="5MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level  %message%newline" />
		</layout>
		<!-- 使用标准的最小锁定，避免频繁文件读取 -->
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 基础警告日志Appender -->
	<appender name="warnAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="WARN" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\warn.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="20" />
		<param name="MaximumFileSize" value="5MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level  %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 基础信息日志Appender -->
	<appender name="infoAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="INFO" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\info.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="15" />
		<param name="MaximumFileSize" value="5MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level  %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 基础调试日志Appender -->
	<appender name="debugAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="DEBUG" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\debug.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="10" />
		<param name="MaximumFileSize" value="3MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level  %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 性能日志Appender -->
	<appender name="perfAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="INFO" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\perf.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="10" />
		<param name="MaximumFileSize" value="2MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date  %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 根日志配置 - 生产环境建议使用INFO级别 -->
	<root>
		<level value="INFO" />
		<appender-ref ref="asyncErrorAppender" />
		<appender-ref ref="asyncWarnAppender" />
		<appender-ref ref="asyncInfoAppender" />
		<!-- 调试日志仅在开发环境启用 -->
		<!--<appender-ref ref="asyncDebugAppender" />-->
	</root>

	<!-- 性能日志记录器 -->
	<logger name="Performance" additivity="false">
		<level value="INFO" />
		<appender-ref ref="perfAppender" />
	</logger>
</log4net>
