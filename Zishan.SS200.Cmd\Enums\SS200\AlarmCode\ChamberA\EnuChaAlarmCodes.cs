﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.AlarmCode.ChamberA
{
    /// <summary>
    /// Chamber A 报警代码枚举类型
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuChaAlarmCodes
    {
        /// <summary>
        /// chamber system status abnormal, command reject
        /// </summary>
        [Description("chamber system status abnormal, command reject")]
        PAC1 = 0,

        /// <summary>
        /// CHX run status or robot run status is busy, command reject
        /// </summary>
        [Description("CHX run status or robot run status is busy, command reject")]
        PAC2 = 1,

        /// <summary>
        /// CHX run status is processing, command reject
        /// </summary>
        [Description("CHX run status is processing, command reject")]
        PAC3 = 2,

        /// <summary>
        /// delta pressure about CHX and loadlock out of setpoint
        /// </summary>
        [Description("delta pressure about CHX and loadlock out of setpoint")]
        PAC4 = 3,

        /// <summary>
        /// slit door sensor poition status abnormal
        /// </summary>
        [Description("slit door sensor poition status abnormal")]
        PAC5 = 4,

        /// <summary>
        /// slit door sensor failure or system error
        /// </summary>
        [Description("slit door sensor failure or system error")]
        PAC6 = 5,

        /// <summary>
        /// chamber pressure not at vacuum status, can not open slit door
        /// </summary>
        [Description("chamber pressure not at vacuum status, can not open slit door")]
        PAC7 = 6,

        /// <summary>
        /// slit door open time out
        /// </summary>
        [Description("slit door open time out")]
        PAC8 = 7,

        /// <summary>
        /// slit door open too fast
        /// </summary>
        [Description("slit door open too fast")]
        PAC9 = 8,

        /// <summary>
        /// lift pin sensor position status abnormal
        /// </summary>
        [Description("lift pin sensor position status abnormal")]
        PAC10 = 9,

        /// <summary>
        /// lift pin sensor show cylinder is between, lift pin motion failure
        /// </summary>
        [Description("lift pin sensor show cylinder is between, lift pin motion failure")]
        PAC11 = 10,

        /// <summary>
        /// lift pin open time out
        /// </summary>
        [Description("lift pin open time out")]
        PAC12 = 11,

        /// <summary>
        /// lift pin open too fast
        /// </summary>
        [Description("lift pin open too fast")]
        PAC13 = 12,

        /// <summary>
        /// lift pin sensor failure or system error
        /// </summary>
        [Description("lift pin sensor failure or system error")]
        PAC14 = 13,

        /// <summary>
        /// robot R-axis not at right position, lift pin motion failure
        /// </summary>
        [Description("robot R-axis not at right position, lift pin motion failure")]
        PAC15 = 14,

        /// <summary>
        /// robot run status is not idle, lift pin motion failure
        /// </summary>
        [Description("robot run status is not idle, lift pin motion failure")]
        PAC16 = 15,

        /// <summary>
        /// robot R-axis not at right position, slit door motion failure
        /// </summary>
        [Description("robot R-axis not at right position, slit door motion failure")]
        PAC17 = 16,

        /// <summary>
        /// robot run status is not idle, slit door motion failure
        /// </summary>
        [Description("robot run status is not idle, slit door motion failure")]
        PAC18 = 17,

        /// <summary>
        /// throttle valve position sensor failure or flag position error
        /// </summary>
        [Description("throttle valve position sensor failure or flag position error")]
        PAC19 = 18,

        /// <summary>
        /// throttle valve motion time out
        /// </summary>
        [Description("throttle valve motion time out")]
        PAC20 = 19,

        /// <summary>
        /// foreline not at vacuum status, can not open ISO valve
        /// </summary>
        [Description("foreline not at vacuum status, can not open ISO valve")]
        PAC21 = 20,

        /// <summary>
        /// chamber ISO valve sensor failure or position error
        /// </summary>
        [Description("chamber ISO valve sensor failure or position error")]
        PAC22 = 21,

        /// <summary>
        /// CV valve open time out
        /// </summary>
        [Description("CV valve open time out")]
        PAC23 = 22,

        /// <summary>
        /// ISO valve not at open position, can not open CM valve
        /// </summary>
        [Description("ISO valve not at open position, can not open CM valve")]
        PAC24 = 23,

        /// <summary>
        /// chamber no process vacuum, can not open CM valve
        /// </summary>
        [Description("chamber no process vacuum, can not open CM valve")]
        PAC25 = 24,

        /// <summary>
        /// CM valve not at open position, can not open gas valve
        /// </summary>
        [Description("CM valve not at open position, can not open gas valve")]
        PAC26 = 25,

        /// <summary>
        /// gas 1 servo time out
        /// </summary>
        [Description("gas 1 servo time out")]
        PAC27 = 26,

        /// <summary>
        /// gas 2 servo time out
        /// </summary>
        [Description("gas 2 servo time out")]
        PAC28 = 27,

        /// <summary>
        /// gas 3 servo time out
        /// </summary>
        [Description("gas 3 servo time out")]
        PAC29 = 28,

        /// <summary>
        /// gas 4 servo time out
        /// </summary>
        [Description("gas 4 servo time out")]
        PAC30 = 29,

        /// <summary>
        /// gas1 flow fault
        /// </summary>
        [Description("gas1 flow fault")]
        PAC31 = 30,

        /// <summary>
        /// gas2 flow fault
        /// </summary>
        [Description("gas2 flow fault")]
        PAC32 = 31,

        /// <summary>
        /// gas3 flow fault
        /// </summary>
        [Description("gas3 flow fault")]
        PAC33 = 32,

        /// <summary>
        /// gas4 flow fault
        /// </summary>
        [Description("gas4 flow fault")]
        PAC34 = 33,

        /// <summary>
        /// gas1 flow unstable
        /// </summary>
        [Description("gas1 flow unstable")]
        PAC35 = 34,

        /// <summary>
        /// gas2 flow unstable
        /// </summary>
        [Description("gas2 flow unstable")]
        PAC36 = 35,

        /// <summary>
        /// gas3 flow unstable
        /// </summary>
        [Description("gas3 flow unstable")]
        PAC37 = 36,

        /// <summary>
        /// gas4 flow unstable
        /// </summary>
        [Description("gas4 flow unstable")]
        PAC38 = 37,

        /// <summary>
        /// CV valve status is no opened, pressure control failure
        /// </summary>
        [Description("CV valve status is no opened, pressure control failure")]
        PAC39 = 38,

        /// <summary>
        /// CM valve not at open position, pressure control failure
        /// </summary>
        [Description("CM valve not at open position, pressure control failure")]
        PAC40 = 39,

        /// <summary>
        /// no open gas valve, can not control pressure
        /// </summary>
        [Description("no open gas valve, can not control pressure")]
        PAC41 = 40,

        /// <summary>
        /// pressure servo time out
        /// </summary>
        [Description("pressure servo time out")]
        PAC42 = 41,

        /// <summary>
        /// pressure flow fault
        /// </summary>
        [Description("pressure flow fault")]
        PAC43 = 42,

        /// <summary>
        /// pressure unstable
        /// </summary>
        [Description("pressure unstable")]
        PAC44 = 43,

        /// <summary>
        /// temperature servo time out
        /// </summary>
        [Description("temperature servo time out")]
        PAC45 = 44,

        /// <summary>
        /// temperature out of setpoint
        /// </summary>
        [Description("temperature out of setpoint")]
        PAC46 = 45,

        /// <summary>
        /// temperature unstable
        /// </summary>
        [Description("temperature unstable")]
        PAC47 = 46,

        /// <summary>
        /// slit door not close, can not turn on RF
        /// </summary>
        [Description("slit door not close, can not turn on RF")]
        PAC48 = 47,

        /// <summary>
        /// chamber no vacuum, RF off
        /// </summary>
        [Description("chamber no vacuum, RF off")]
        PAC49 = 48,

        /// <summary>
        /// ISO valve not open, can not turn on RF
        /// </summary>
        [Description("ISO valve not open, can not turn on RF")]
        PAC50 = 49,

        /// <summary>
        /// CM valve not at open position, can not turn on RF
        /// </summary>
        [Description("CM valve not at open position, can not turn on RF")]
        PAC51 = 50,

        /// <summary>
        /// no plasma was detected on CHX left head
        /// </summary>
        [Description("no plasma was detected on CHX left head")]
        PAC52 = 51,

        /// <summary>
        /// no plasma was detected on CHX right head
        /// </summary>
        [Description("no plasma was detected on CHX right head")]
        PAC53 = 52,

        /// <summary>
        /// RF1 power forward to setpoint time out
        /// </summary>
        [Description("RF1 power forward to setpoint time out")]
        PAC54 = 53,

        /// <summary>
        /// RF1 reflector power out of limit
        /// </summary>
        [Description("RF1 reflector power out of limit")]
        PAC55 = 54,

        /// <summary>
        /// RF2 power forward to setpoint time out
        /// </summary>
        [Description("RF2 power forward to setpoint time out")]
        PAC56 = 55,

        /// <summary>
        /// RF2 reflector power out of limit
        /// </summary>
        [Description("RF2 reflector power out of limit")]
        PAC57 = 56,

        /// <summary>
        /// RF1 forward power out of setpoint
        /// </summary>
        [Description("RF1 forward power out of setpoint")]
        PAC58 = 57,

        /// <summary>
        /// RF2 forward power out of setpoint
        /// </summary>
        [Description("RF2 forward power out of setpoint")]
        PAC59 = 58,

        /// <summary>
        /// RF1 forward power ouput unstable
        /// </summary>
        [Description("RF1 forward power ouput unstable")]
        PAC60 = 59,

        /// <summary>
        /// RF2 forward power ouput unstable
        /// </summary>
        [Description("RF2 forward power ouput unstable")]
        PAC61 = 60,

        /// <summary>
        /// RF off failure
        /// </summary>
        [Description("RF off failure")]
        PAC62 = 61,

        /// <summary>
        /// RF1 reflector power ouput unstable
        /// </summary>
        [Description("RF1 reflector power ouput unstable")]
        PAC63 = 62,

        /// <summary>
        /// RF2 reflector power ouput unstable
        /// </summary>
        [Description("RF2 reflector power ouput unstable")]
        PAC64 = 63,

        /// <summary>
        /// Chamber pump down time out
        /// </summary>
        [Description("Chamber pump down time out")]
        PAC65 = 64
    }
}