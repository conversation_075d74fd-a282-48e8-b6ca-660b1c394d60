﻿using System.Text.Json.Serialization;
using System.ComponentModel;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 日志类型
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum EnuLogDataType
    {
        /// <summary>
        /// 普通腔体所有
        /// </summary>
        [Description("工作日志")]
        Job = 1,

        /// <summary>
        /// 报警日志
        /// </summary>
        [Description("报警日志")]
        Alarm = 2,

        /// <summary>
        /// 操作日志【上位机提取】
        /// </summary>
        [Description("操作日志")]
        Operation = 3,

        /// <summary>
        /// 监控数据日志【上位机读PLC变量提取】
        /// </summary>
        [Description("监控数据日志")]
        Data = 4,

        /// <summary>
        /// 其它日志
        /// </summary>
        [Description("运行配方日志")]
        RunRecipe = 5,

        /// <summary>
        /// 其它日志
        /// </summary>
        [Description("监控数据日志")]
        Others = 6
    }
}