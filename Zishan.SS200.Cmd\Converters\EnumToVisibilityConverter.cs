using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using Zishan.SS200.Cmd.Models;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 枚举值转可见性转换器
    /// </summary>
    public class EnumToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PropertyTypeCategory category && parameter is string paramStr)
            {
                if (Enum.TryParse<PropertyTypeCategory>(paramStr, out PropertyTypeCategory targetCategory))
                {
                    return category == targetCategory ? Visibility.Visible : Visibility.Collapsed;
                }
            }
            
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
