using System;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.ChamberA;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// Chamber报警访问器测试类
    /// 用于验证所有Chamber报警代码访问器是否正常工作
    /// </summary>
    public class ChamberAlarmAccessorTest
    {
        /// <summary>
        /// 简单测试Chamber报警代码访问器
        /// </summary>
        public static void SimpleTest()
        {
            try
            {
                Console.WriteLine("=== Chamber报警代码访问器简单测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var chamberAlarm = interLock.AlarmCode.ChamberA;

                // 测试前10个报警代码
                Console.WriteLine("\n测试前10个Chamber报警代码:");
                TestAccessor("PAC1", chamberAlarm.PAC1_SystemAbnormalReject);
                TestAccessor("PAC2", chamberAlarm.PAC2_SystemBusyReject);
                TestAccessor("PAC3", chamberAlarm.PAC3_RunProcessingReject);
                TestAccessor("PAC4", chamberAlarm.PAC4_DeltaPressureOutOfSetpoint);
                TestAccessor("PAC5", chamberAlarm.PAC5_SlitDoorSensorAbnormal);
                TestAccessor("PAC6", chamberAlarm.PAC6_SlitDoorSensorFailure);
                TestAccessor("PAC7", chamberAlarm.PAC7_PressureNotVacuumSlitDoorError);
                TestAccessor("PAC8", chamberAlarm.PAC8_SlitDoorOpenTimeout);
                TestAccessor("PAC9", chamberAlarm.PAC9_SlitDoorOpenTooFast);
                TestAccessor("PAC10", chamberAlarm.PAC10_LiftPinSensorAbnormal);

                // 测试中间的一些报警代码
                Console.WriteLine("\n测试中间的一些Chamber报警代码:");
                TestAccessor("PAC20", chamberAlarm.PAC20_ThrottleValveTimeout);
                TestAccessor("PAC30", chamberAlarm.PAC30_Gas4ServoTimeout);
                TestAccessor("PAC40", chamberAlarm.PAC40_CMValveNotOpenPressureError);
                TestAccessor("PAC50", chamberAlarm.PAC50_ISONotOpenRFError);

                // 测试最后几个报警代码
                Console.WriteLine("\n测试最后几个Chamber报警代码:");
                TestAccessor("PAC61", chamberAlarm.PAC61_RF2ForwardPowerUnstable);
                TestAccessor("PAC62", chamberAlarm.PAC62_RFOffFailure);
                TestAccessor("PAC63", chamberAlarm.PAC63_RF1ReflectorPowerUnstable);
                TestAccessor("PAC64", chamberAlarm.PAC64_RF2ReflectorPowerUnstable);
                TestAccessor("PAC65", chamberAlarm.PAC65_ChamberPumpDownTimeout);

                Console.WriteLine("\n=== Chamber报警代码访问器简单测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 统计测试 - 验证所有65个访问器都已定义
        /// </summary>
        public static void CountTest()
        {
            try
            {
                Console.WriteLine("=== Chamber报警代码访问器统计测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var chamberAlarm = interLock.AlarmCode.ChamberA;
                var chamberAlarmType = chamberAlarm.GetType();
                
                // 获取所有以PAC开头的属性
                var properties = chamberAlarmType.GetProperties();
                int pacPropertyCount = 0;
                
                foreach (var property in properties)
                {
                    if (property.Name.StartsWith("PAC") && property.Name.Contains("_"))
                    {
                        pacPropertyCount++;
                        Console.WriteLine($"  发现访问器: {property.Name}");
                    }
                }
                
                Console.WriteLine($"\n总计发现 {pacPropertyCount} 个Chamber报警代码访问器");
                
                if (pacPropertyCount == 65)
                {
                    Console.WriteLine("✅ 所有65个Chamber报警代码访问器都已正确定义！");
                }
                else
                {
                    Console.WriteLine($"❌ 预期65个访问器，实际发现{pacPropertyCount}个");
                }
                
                Console.WriteLine("=== Chamber报警代码访问器统计测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"统计测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 完整测试所有Chamber报警代码访问器
        /// </summary>
        public static void TestAllChamberAlarmAccessors()
        {
            try
            {
                Console.WriteLine("开始测试Chamber报警代码访问器...");
                
                var interLock = SS200InterLockMain.Instance;
                var chamberAlarm = interLock.AlarmCode.ChamberA;

                // 测试PAC1-PAC10
                TestAccessor("PAC1", chamberAlarm.PAC1_SystemAbnormalReject);
                TestAccessor("PAC2", chamberAlarm.PAC2_SystemBusyReject);
                TestAccessor("PAC3", chamberAlarm.PAC3_RunProcessingReject);
                TestAccessor("PAC4", chamberAlarm.PAC4_DeltaPressureOutOfSetpoint);
                TestAccessor("PAC5", chamberAlarm.PAC5_SlitDoorSensorAbnormal);
                TestAccessor("PAC6", chamberAlarm.PAC6_SlitDoorSensorFailure);
                TestAccessor("PAC7", chamberAlarm.PAC7_PressureNotVacuumSlitDoorError);
                TestAccessor("PAC8", chamberAlarm.PAC8_SlitDoorOpenTimeout);
                TestAccessor("PAC9", chamberAlarm.PAC9_SlitDoorOpenTooFast);
                TestAccessor("PAC10", chamberAlarm.PAC10_LiftPinSensorAbnormal);

                // 测试PAC11-PAC20
                TestAccessor("PAC11", chamberAlarm.PAC11_LiftPinCylinderBetween);
                TestAccessor("PAC12", chamberAlarm.PAC12_LiftPinOpenTimeout);
                TestAccessor("PAC13", chamberAlarm.PAC13_LiftPinOpenTooFast);
                TestAccessor("PAC14", chamberAlarm.PAC14_LiftPinSensorFailure);
                TestAccessor("PAC15", chamberAlarm.PAC15_RobotRAxisLiftPinError);
                TestAccessor("PAC16", chamberAlarm.PAC16_RobotNotIdleLiftPinError);
                TestAccessor("PAC17", chamberAlarm.PAC17_RobotRAxisSlitDoorError);
                TestAccessor("PAC18", chamberAlarm.PAC18_RobotNotIdleSlitDoorError);
                TestAccessor("PAC19", chamberAlarm.PAC19_ThrottleValveSensorFailure);
                TestAccessor("PAC20", chamberAlarm.PAC20_ThrottleValveTimeout);

                // 测试PAC21-PAC30
                TestAccessor("PAC21", chamberAlarm.PAC21_ForelineNotVacuumISOError);
                TestAccessor("PAC22", chamberAlarm.PAC22_ISOValveSensorFailure);
                TestAccessor("PAC23", chamberAlarm.PAC23_CVValveOpenTimeout);
                TestAccessor("PAC24", chamberAlarm.PAC24_ISONotOpenCMError);
                TestAccessor("PAC25", chamberAlarm.PAC25_NoProcessVacuumCMError);
                TestAccessor("PAC26", chamberAlarm.PAC26_CMNotOpenGasError);
                TestAccessor("PAC27", chamberAlarm.PAC27_Gas1ServoTimeout);
                TestAccessor("PAC28", chamberAlarm.PAC28_Gas2ServoTimeout);
                TestAccessor("PAC29", chamberAlarm.PAC29_Gas3ServoTimeout);
                TestAccessor("PAC30", chamberAlarm.PAC30_Gas4ServoTimeout);

                // 测试PAC31-PAC40
                TestAccessor("PAC31", chamberAlarm.PAC31_Gas1FlowFault);
                TestAccessor("PAC32", chamberAlarm.PAC32_Gas2FlowFault);
                TestAccessor("PAC33", chamberAlarm.PAC33_Gas3FlowFault);
                TestAccessor("PAC34", chamberAlarm.PAC34_Gas4FlowFault);
                TestAccessor("PAC35", chamberAlarm.PAC35_Gas1FlowUnstable);
                TestAccessor("PAC36", chamberAlarm.PAC36_Gas2FlowUnstable);
                TestAccessor("PAC37", chamberAlarm.PAC37_Gas3FlowUnstable);
                TestAccessor("PAC38", chamberAlarm.PAC38_Gas4FlowUnstable);
                TestAccessor("PAC39", chamberAlarm.PAC39_CVValveNotOpenPressureError);
                TestAccessor("PAC40", chamberAlarm.PAC40_CMValveNotOpenPressureError);

                // 测试PAC41-PAC50
                TestAccessor("PAC41", chamberAlarm.PAC41_NoGasValveOpenPressureError);
                TestAccessor("PAC42", chamberAlarm.PAC42_PressureServoTimeout);
                TestAccessor("PAC43", chamberAlarm.PAC43_PressureFlowFault);
                TestAccessor("PAC44", chamberAlarm.PAC44_PressureUnstable);
                TestAccessor("PAC45", chamberAlarm.PAC45_TemperatureServoTimeout);
                TestAccessor("PAC46", chamberAlarm.PAC46_TemperatureOutOfSetpoint);
                TestAccessor("PAC47", chamberAlarm.PAC47_TemperatureUnstable);
                TestAccessor("PAC48", chamberAlarm.PAC48_SlitDoorNotCloseRFError);
                TestAccessor("PAC49", chamberAlarm.PAC49_NoVacuumRFOff);
                TestAccessor("PAC50", chamberAlarm.PAC50_ISONotOpenRFError);

                // 测试PAC51-PAC60
                TestAccessor("PAC51", chamberAlarm.PAC51_CMNotOpenRFError);
                TestAccessor("PAC52", chamberAlarm.PAC52_NoPlasmaLeftHead);
                TestAccessor("PAC53", chamberAlarm.PAC53_NoPlasmaRightHead);
                TestAccessor("PAC54", chamberAlarm.PAC54_RF1PowerForwardTimeout);
                TestAccessor("PAC55", chamberAlarm.PAC55_RF1ReflectorPowerOutOfLimit);
                TestAccessor("PAC56", chamberAlarm.PAC56_RF2PowerForwardTimeout);
                TestAccessor("PAC57", chamberAlarm.PAC57_RF2ReflectorPowerOutOfLimit);
                TestAccessor("PAC58", chamberAlarm.PAC58_RF1ForwardPowerOutOfSetpoint);
                TestAccessor("PAC59", chamberAlarm.PAC59_RF2ForwardPowerOutOfSetpoint);
                TestAccessor("PAC60", chamberAlarm.PAC60_RF1ForwardPowerUnstable);

                // 测试PAC61-PAC65
                TestAccessor("PAC61", chamberAlarm.PAC61_RF2ForwardPowerUnstable);
                TestAccessor("PAC62", chamberAlarm.PAC62_RFOffFailure);
                TestAccessor("PAC63", chamberAlarm.PAC63_RF1ReflectorPowerUnstable);
                TestAccessor("PAC64", chamberAlarm.PAC64_RF2ReflectorPowerUnstable);
                TestAccessor("PAC65", chamberAlarm.PAC65_ChamberPumpDownTimeout);

                Console.WriteLine("所有Chamber报警代码访问器测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试单个访问器
        /// </summary>
        /// <param name="code">报警代码</param>
        /// <param name="accessor">访问器</param>
        private static void TestAccessor(string code, AlarmPropertyAccessor accessor)
        {
            try
            {
                if (accessor != null)
                {
                    string content = accessor.Content ?? "未定义";
                    string chsContent = accessor.ChsContent ?? "未定义";
                    Console.WriteLine($"  {code}: {content}");
                    Console.WriteLine($"       中文: {chsContent}");
                }
                else
                {
                    Console.WriteLine($"  {code}: 访问器为空");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  {code}: 访问器测试失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            SimpleTest();
            Console.WriteLine();
            CountTest();
        }
    }
}
