using System;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using <PERSON>ishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle;
using Zishan.SS200.Cmd.Enums.Basic;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// SS200InterLockMain 直接访问状态实体对象示例
    /// 演示如何通过统一入口直接获取完整的状态实体对象
    /// </summary>
    public class SS200InterLockMain_DirectStatusAccess_Example
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(SS200InterLockMain_DirectStatusAccess_Example));

        /// <summary>
        /// 演示直接访问状态实体对象的基本用法
        /// </summary>
        public static void BasicDirectStatusAccessExample()
        {
            _logger.Info("=== 开始演示直接访问状态实体对象 ===");

            try
            {
                // 获取SS200InterLockMain单例实例
                var interlock = SS200InterLockMain.Instance;

                // ========================================
                // 1. 直接访问完整的Robot状态实体对象
                // ========================================
                RobotSubsystemStatus robotStatus = interlock.SubsystemStatus.Robot.Status;

                _logger.Info("=== Robot状态信息 ===");
                _logger.Info($"Robot主状态: {robotStatus.EnuRobotStatus}");
                _logger.Info($"T轴Smooth目的地: {robotStatus.EnuTAxisSmoothDestination}");
                _logger.Info($"T轴Nose目的地: {robotStatus.EnuTAxisNoseDestination}");
                _logger.Info($"T和R轴Smooth扩展目的地: {robotStatus.EnuTAndRAxisSmoothExtendDestination}");
                _logger.Info($"T和R轴Nose扩展目的地: {robotStatus.EnuTAndRAxisNoseExtendDestination}");
                _logger.Info($"T和Z轴高度状态: {robotStatus.EnuTAndZAxisHeightStatus}");
                _logger.Info($"Smooth Paddle状态: {robotStatus.SmoothPaddleStatus}");
                _logger.Info($"Nose Paddle状态: {robotStatus.NosePaddleStatus}");

                // ========================================
                // 2. 直接访问完整的Chamber状态实体对象
                // ========================================
                ChamberSubsystemStatus chamberAStatus = interlock.SubsystemStatus.ChamberA.Status;

                _logger.Info("=== Chamber A状态信息 ===");
                _logger.Info($"触发状态: {chamberAStatus.TriggerStatus}");
                _logger.Info($"运行状态: {chamberAStatus.RunStatus}");
                _logger.Info($"Slit Door状态: {chamberAStatus.SlitDoorStatus}");
                _logger.Info($"Lift Pin状态: {chamberAStatus.LiftPinStatus}");
                _logger.Info($"晶圆准备状态: {chamberAStatus.WaferReadyStatus}");
                _logger.Info($"腔体真空状态: {chamberAStatus.ChamberVacuumStatus}");

                // ========================================
                // 3. 直接访问完整的Shuttle状态实体对象
                // ========================================
                ShuttleSubsystemStatus shuttleStatus = interlock.SubsystemStatus.Shuttle.Status;

                _logger.Info("=== Shuttle状态信息 ===");
                _logger.Info($"Shuttle状态: {shuttleStatus.ShuttleStatus}");
                _logger.Info($"SSC6配置: {shuttleStatus.Ssc6Config}");
                _logger.Info($"Shuttle位置状态: {shuttleStatus.ShuttlePositionStatus}");
                _logger.Info($"晶圆盒门巢状态: {shuttleStatus.CassetteDoorNestStatus}");
                _logger.Info($"Shuttle1 Cassette1 Lot状态: {shuttleStatus.Shuttle1Cassette1LotStatus}");

                _logger.Info("=== 直接访问状态实体对象演示完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"直接访问状态实体对象示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示批量状态检查和条件判断
        /// </summary>
        public static void BatchStatusCheckExample()
        {
            _logger.Info("=== 开始批量状态检查和条件判断 ===");

            try
            {
                var interlock = SS200InterLockMain.Instance;

                // 获取所有子系统状态
                var robotStatus = interlock.SubsystemStatus.Robot.Status;
                var chamberAStatus = interlock.SubsystemStatus.ChamberA.Status;
                var chamberBStatus = interlock.SubsystemStatus.ChamberB.Status;
                var shuttleStatus = interlock.SubsystemStatus.Shuttle.Status;

                _logger.Info("=== 系统状态检查 ===");

                // 检查Robot是否准备就绪
                bool robotReady = CheckRobotReady(robotStatus);
                _logger.Info($"Robot准备就绪: {robotReady}");

                // 检查Chamber是否可以接收晶圆
                bool chamberAReady = CheckChamberReady(chamberAStatus);
                bool chamberBReady = CheckChamberReady(chamberBStatus);
                _logger.Info($"Chamber A准备就绪: {chamberAReady}");
                _logger.Info($"Chamber B准备就绪: {chamberBReady}");

                // 检查Shuttle是否有晶圆可传输
                bool shuttleHasWafer = CheckShuttleHasWafer(shuttleStatus);
                _logger.Info($"Shuttle有晶圆: {shuttleHasWafer}");

                // 综合判断是否可以开始传输
                bool canStartTransfer = robotReady && (chamberAReady || chamberBReady) && shuttleHasWafer;
                _logger.Info($"可以开始传输: {canStartTransfer}");

                _logger.Info("=== 批量状态检查完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"批量状态检查示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 演示状态更新操作【注意：从IOC容器获取设备状态实例与interLock获取的设备状态实例是同一个】
        /// </summary>
        public static void StatusUpdateExample()
        {
            _logger.Info("=== 开始演示状态更新操作 ===");

            try
            {
                var interlock = SS200InterLockMain.Instance;

                // 从IOC容器获取Robot状态实例并更新属性
                var robotStatus = App.GetInstance<RobotSubsystemStatus>();
                robotStatus.EnuRobotStatus = EnuRobotStatus.Busy;
                robotStatus.EnuTAxisSmoothDestination = EnuLocationStationType.ChamberA;
                robotStatus.EnuTAndRAxisSmoothExtendDestination = EnuLocationStationType.ChamberA;
                robotStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.SmoothToCHA;

                // 更新Robot状态
                interlock.UpdateRobotStatus(robotStatus);
                _logger.Info("Robot状态已更新");

                // 从IOC容器获取Chamber状态实例并更新属性
                var chamberStatus = App.GetInstance<ChamberSubsystemStatus>();
                chamberStatus.TriggerStatus = EnuTriggerStatus.NoAlarm;
                chamberStatus.RunStatus = EnuRunStatus.Idle;
                chamberStatus.SlitDoorStatus = EnuSlitDoorStatus.Open;
                chamberStatus.LiftPinStatus = EnuLiftPinStatus.Down;

                // 更新Chamber A状态
                interlock.UpdateChamberAStatus(chamberStatus);
                _logger.Info("Chamber A状态已更新");

                // 从IOC容器获取Shuttle状态实例并更新属性
                var shuttleStatus = App.GetInstance<ShuttleSubsystemStatus>();
                shuttleStatus.ShuttleStatus = EnuShuttleStatus.Idle;
                shuttleStatus.Ssc6Config = EnuSSC6Config.SMIF;
                shuttleStatus.ShuttlePositionStatus = EnuShuttlePositionStatus.ShuttleUpShuttle1Outer;
                shuttleStatus.Shuttle1Cassette1LotStatus = EnuLotStatus.HasLot;

                // 更新Shuttle状态
                interlock.UpdateShuttleStatus(shuttleStatus);
                _logger.Info("Shuttle状态已更新");

                // 验证更新后的状态
                var updatedRobotStatus = interlock.SubsystemStatus.Robot.Status;
                var updatedChamberStatus = interlock.SubsystemStatus.ChamberA.Status;
                var updatedShuttleStatus = interlock.SubsystemStatus.Shuttle.Status;

                _logger.Info($"更新后Robot状态: {updatedRobotStatus.EnuRobotStatus}");
                _logger.Info($"更新后Chamber状态: {updatedChamberStatus.RunStatus}");
                _logger.Info($"更新后Shuttle状态: {updatedShuttleStatus.ShuttleStatus}");

                _logger.Info("=== 状态更新操作完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"状态更新示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查Robot是否准备就绪
        /// </summary>
        private static bool CheckRobotReady(RobotSubsystemStatus robotStatus)
        {
            return robotStatus.EnuRobotStatus == EnuRobotStatus.Idle &&
                   robotStatus.TAxisIsZeroPosition &&
                   robotStatus.RAxisIsZeroPosition &&
                   robotStatus.ZAxisIsZeroPosition;
        }

        /// <summary>
        /// 检查Chamber是否准备就绪
        /// </summary>
        private static bool CheckChamberReady(ChamberSubsystemStatus chamberStatus)
        {
            return chamberStatus.TriggerStatus == EnuTriggerStatus.NoAlarm &&
                   chamberStatus.RunStatus == EnuRunStatus.Idle &&
                   chamberStatus.SlitDoorStatus == EnuSlitDoorStatus.Close &&
                   chamberStatus.LiftPinStatus == EnuLiftPinStatus.Down;
        }

        /// <summary>
        /// 检查Shuttle是否有晶圆
        /// </summary>
        private static bool CheckShuttleHasWafer(ShuttleSubsystemStatus shuttleStatus)
        {
            return shuttleStatus.ShuttleStatus == EnuShuttleStatus.Idle &&
                   shuttleStatus.Shuttle1Cassette1LotStatus == EnuLotStatus.HasLot;
        }

        /// <summary>
        /// 演示与原有单个属性访问方式的对比
        /// </summary>
        public static void ComparisonWithOriginalAccessExample()
        {
            _logger.Info("=== 开始演示访问方式对比 ===");

            try
            {
                var interlock = SS200InterLockMain.Instance;

                _logger.Info("=== 访问方式对比 ===");

                // 新方式：直接访问完整状态实体对象
                _logger.Info("--- 直接实体对象访问方式 ---");
                var robotStatus = interlock.SubsystemStatus.Robot.Status;
                var chamberStatus = interlock.SubsystemStatus.ChamberA.Status;
                _logger.Info($"Robot主状态: {robotStatus?.EnuRobotStatus}");
                _logger.Info($"Slit Door状态: {chamberStatus?.SlitDoorStatus}");

                // 详细状态信息访问
                _logger.Info("--- 详细状态信息访问 ---");

                _logger.Info("Robot完整状态:");
                _logger.Info($"  主状态: {robotStatus.EnuRobotStatus}");
                _logger.Info($"  T轴Smooth目的地: {robotStatus.EnuTAxisSmoothDestination}");
                _logger.Info($"  T和R轴Smooth扩展目的地: {robotStatus.EnuTAndRAxisSmoothExtendDestination}");
                _logger.Info($"  T和Z轴高度状态: {robotStatus.EnuTAndZAxisHeightStatus}");

                _logger.Info("Chamber完整状态:");
                _logger.Info($"  触发状态: {chamberStatus.TriggerStatus}");
                _logger.Info($"  运行状态: {chamberStatus.RunStatus}");
                _logger.Info($"  Slit Door状态: {chamberStatus.SlitDoorStatus}");
                _logger.Info($"  Lift Pin状态: {chamberStatus.LiftPinStatus}");

                _logger.Info("=== 访问方式对比演示完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"访问方式对比示例失败: {ex.Message}", ex);
            }
        }

        #region 综合示例

        /// <summary>
        /// 运行所有直接状态访问示例
        /// </summary>
        public static async Task RunAllDirectStatusAccessExamples()
        {
            _logger.Info("开始运行SS200InterLockMain直接状态访问示例...");

            try
            {
                // 1. 基本直接状态访问示例
                BasicDirectStatusAccessExample();
                await Task.Delay(1000);

                // 2. 批量状态检查示例
                BatchStatusCheckExample();
                await Task.Delay(1000);

                // 3. 状态更新示例
                StatusUpdateExample();
                await Task.Delay(1000);

                // 4. 访问方式对比示例
                ComparisonWithOriginalAccessExample();
                await Task.Delay(1000);

                _logger.Info("所有直接状态访问示例运行完成！");
            }
            catch (Exception ex)
            {
                _logger.Error($"运行直接状态访问示例失败: {ex.Message}", ex);
            }
        }

        #endregion 综合示例
    }
}