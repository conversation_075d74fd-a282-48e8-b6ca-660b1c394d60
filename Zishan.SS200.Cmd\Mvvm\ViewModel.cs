// This Source Code Form is subject to the terms of the MIT License.
// If a copy of the MIT was not distributed with this file, You can obtain one at https://opensource.org/licenses/MIT.
// Copyright (C) <PERSON><PERSON><PERSON> and WPF UI Contributors.
// All Rights Reserved.

using CommunityToolkit.Mvvm.ComponentModel;
using Prism.Navigation;

// using Prism.Regions;

namespace Zishan.SS200.Cmd.Mvvm;

public abstract class ViewModel : ObservableObject, IDestructible
{
    protected ViewModel()
    {
    }

    public virtual void Destroy()
    {
    }
}