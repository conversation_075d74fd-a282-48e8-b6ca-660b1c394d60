﻿using Prism.Mvvm;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// 矢量机械臂位置信息
    /// </summary>
    public class RobotArmLocationInfo : BindableBase
    {
        //public EnuChamberGo CurEnuChamberAngle { get; set; }

        public double X { get => _x; set => SetProperty(ref _x, value); }
        private double _x;

        public double Y { get => _y; set => SetProperty(ref _y, value); }
        private double _y;

        public double Angle { get => _Angle; set => SetProperty(ref _Angle, value); }
        private double _Angle;

        /// <summary>
        /// 是否完成变化到指定的腔体位置
        /// </summary>
        public bool isFinisedTransformToPosition { get; set; } = false;

        public EnuChamberName lastEnuChamberNameGo { get; set; } = EnuChamberName.Home;

        public EnuChamberGoAction lastChamberGoAction { get; set; } = EnuChamberGoAction.None;
    }
}