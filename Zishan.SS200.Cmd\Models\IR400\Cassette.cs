﻿using System.Collections.Generic;
using System.Windows;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// Cassette 类表示 Wafer 的存放 cassette，存放未加工和完成加工的 Wafer。
    /// </summary>
    public class Cassette : BContainer
    {
        public Cassette(EnuChamberName name, int capacity = 25) : base(name, 0, capacity)
        {
        }

        public void ClearWafers()
        {
            LeftWaferAction.ClearWafers();
            RightWaferAction.ClearWafers();
        }

        /// <summary>
        /// 加载全局Wafer到Cassettes中
        /// </summary>
        /// <param name="errMsgList"></param>
        /// <returns></returns>
        public bool LoadWafers(out List<string> errMsgList)
        {
            List<Wafer> leftWafers = new List<Wafer>();
            List<Wafer> rigtWafers = new List<Wafer>();
            errMsgList = new List<string>();

            foreach (var wafer in Golbal.CurLeftAvailableWafers)
            {
                leftWafers.Add(wafer);
            }
            foreach (var wafer in Golbal.CurRightAvailableWafers)
            {
                rigtWafers.Add(wafer);
            }

            var blResult = LeftWaferAction.AddWafers(leftWafers, out string message);
            if (!blResult)
            {
                errMsgList.Add(message);
            }
            Application.Current.Dispatcher.Invoke(() =>
           {
               Golbal.CurLeftAvailableWafers.Clear();
           });

            blResult = RightWaferAction.AddWafers(rigtWafers, out message);
            if (!blResult)
            {
                errMsgList.Add(message);
            }
            Application.Current.Dispatcher.Invoke(() =>
            {
                Golbal.CurRightAvailableWafers.Clear();
            });

            IsProcessed = true;

            return errMsgList.Count == 0;
        }
    }
}