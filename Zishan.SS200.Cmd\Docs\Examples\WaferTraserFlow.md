# Wafer搬运功能模块分析

## 文件概述

1. **S200McuCmdServiceExtensions.cs**
   - 基础命令服务扩展类
   - 提供最底层的设备命令执行功能
   - 包括通用MODBUS命令和特定设备命令(Robot, Shuttle, Cha, Chb)

2. **RobotWaferOperationsExtensions.cs**
   - 机器人晶圆搬运操作扩展类
   - 提供晶圆搬运的高级操作(取片、放片、传输)和RTZ轴(旋转、伸缩、升降)位置移动功能
   - 整合了原RobotPositionExtensions的所有功能
   - 依赖S200McuCmdServiceExtensions执行底层命令

## 调用层次结构

```mermaid
graph TD
    A[RobotWaferOperationsExtensions] -->|调用| B[S200McuCmdServiceExtensions]
    B -->|调用| C[ModbusCommandUtility]
```

## 详细调用流程图

```mermaid
flowchart TD
    %% 主要组件
    Client([客户端代码]) --> WaferOps[RobotWaferOperationsExtensions]
    WaferOps --> CmdExt[S200McuCmdServiceExtensions]
    CmdExt --> ModbusUtil[ModbusCommandUtility]
    
    %% 一级命令 - 晶圆传输流程
    subgraph "一级命令 - 晶圆传输流程"
        TrasferWafer["TrasferWaferAsync\n(完整晶圆传输)"]
        PinSearch["PinSearchAsync\n(Pin Search测试)"]
    end
    
    %% 二级命令 - 晶圆取放操作
    subgraph "二级命令 - 晶圆取放操作"
        subgraph "取晶圆操作"
            GetWafer["GetWaferAsync\n(获取晶圆)"]
        end
        
        subgraph "放晶圆操作"
            PutWafer["PutWaferAsync\n(放置晶圆)"]
        end
        
        subgraph "状态交换操作"
            ExecStatus["ExecuteStatusExchangeAsync\n(状态交换)"]
            ConvertIndex["ConvertStationTypeToIndex\n(站点类型转换)"]
        end
    end
    
    %% 三级命令 - 基础轴控制操作
    subgraph "三级命令 - 基础轴控制操作"
        subgraph "机器人初始化"
            InitRobot["InitializeRobotAsync\n(初始化机器人)"]
        end
        
        subgraph "T轴操作"
            subgraph "T轴基础移动"
                MoveTPos["MoveTAxisToPositionAsync\n(T轴移动到指定位置)"]
            end
            
            subgraph "T轴位置移动"
                MoveTLoc["MoveTAxisToLocationAsync\n(T轴移动到站点)"]
            end
            
            subgraph "T轴归零"
                ZeroT["ZeroTAxisAsync\n(T轴归零)"]
            end
        end
        
        subgraph "R轴操作"
            subgraph "R轴基础移动"
                MoveRPos["MoveRAxisToPositionAsync\n(R轴移动到指定位置)"]
            end
            
            subgraph "R轴位置移动"
                MoveRLoc["MoveRAxisToLocationAsync\n(R轴移动到站点)"]
                RetractR["MoveRAxisToHomeAsync\n(R轴归零)"]
            end
            
            subgraph "R轴归零"
                ZeroR["ZeroRAxisAsync\n(R轴归零)"]
            end
        end
        
        subgraph "Z轴操作"
            subgraph "Z轴基础移动"
                MoveZPos["MoveZAxisToPositionAsync\n(Z轴移动到指定位置)"]
            end
            
            subgraph "Z轴取放片位置移动"
                MoveZGet["MoveZAxisToGetPositionAsync\n(Z轴到取片位置 - 简化版)"]
                MoveZPut["MoveZAxisToPutPositionAsync\n(Z轴到放片位置 - 简化版)"]
                MoveZGetFull["MoveZAxisToGetPositionAsync\n(Z轴到取片位置 - 带slot参数)"]
                MoveZPutFull["MoveZAxisToPutPositionAsync\n(Z轴到放片位置 - 带slot参数)"]
            end
            
            subgraph "Z轴归零"
                ZeroZ["ZeroZAxisAsync\n(Z轴归零)"]
            end
        end
    end
    
    %% 参数获取方法
    subgraph "参数获取方法"
        GetZPut["GetZAxisPutPosition\n(获取Z轴放片位置步数)"]
    end
    
    %% 底层命令执行
    subgraph "底层命令执行"
        ExecRobot["ExecuteRobotCommandAsync\n(执行机器人命令)"]
        ExecRAxis["ExecuteRobotRAxisCommandAsync\n(R轴命令)"]
        ExecTAxis["ExecuteRobotTAxisCommandAsync\n(T轴命令)"]
        ExecZAxis["ExecuteRobotZAxisCommandAsync\n(Z轴命令)"]
        ExecDevice["ExecuteDeviceCommandAsync\n(执行设备命令)"]
    end
    
    %% 调用关系 - 一级到二级
    TrasferWafer --> InitRobot
    TrasferWafer --> GetWafer
    TrasferWafer --> PutWafer
    PinSearch --> InitRobot
    PinSearch --> MoveRLoc
    PinSearch --> ExecZAxis
    PinSearch --> ExecDevice
    
    %% 调用关系 - 二级到三级
    GetWafer --> MoveTLoc
    GetWafer --> MoveRLoc
    GetWafer --> MoveZGet
    GetWafer --> ExecStatus
    GetWafer --> RetractR
    
    PutWafer --> MoveTLoc
    PutWafer --> MoveRLoc
    PutWafer --> MoveZPut
    PutWafer --> ExecStatus
    PutWafer --> RetractR
    
    ExecStatus --> ExecRobot
    ExecStatus --> ConvertIndex
    
    %% 调用关系 - 三级到底层
    InitRobot --> ZeroT
    InitRobot --> ZeroR
    InitRobot --> ZeroZ
    
    MoveTLoc --> MoveTPos
    MoveRLoc --> MoveRPos
    RetractR --> ZeroR
    
    MoveZGet --> MoveZGetFull
    MoveZPut --> MoveZPutFull
    MoveZGetFull --> MoveZPos
    MoveZPutFull --> MoveZPos
    
    ZeroT --> MoveTPos
    ZeroR --> MoveRPos
    ZeroZ --> MoveZPos
    
    %% 底层命令关系
    MoveTPos --> ExecTAxis
    MoveRPos --> ExecRAxis
    MoveZPos --> ExecZAxis
    
    ExecTAxis --> ExecRobot
    ExecRAxis --> ExecRobot
    ExecZAxis --> ExecRobot
    ExecRobot --> ExecDevice
    ExecDevice --> ModbusUtil
```

## 功能说明

### S200McuCmdServiceExtensions
- **底层命令执行层**：提供与硬件设备通信的基础功能
- **ExecuteDeviceCommandAsync**：通用设备命令执行方法
- **ExecuteRobotCommandAsync**：机器人命令执行方法
- **ExecuteRobotTAxisCommandAsync**：T轴(旋转轴)命令
- **ExecuteRobotRAxisCommandAsync**：R轴(伸缩轴)命令
- **ExecuteRobotZAxisCommandAsync**：Z轴(升降轴)命令
- 还包含其他设备(Shuttle, Cha, Chb)的命令执行方法

### RobotWaferOperationsExtensions
- **高级操作层**：提供晶圆搬运的完整流程和轴控制功能
- **一级命令 - 晶圆传输流程**：
  - **TrasferWaferAsync**：完整晶圆传输流程
  - **PinSearchAsync**：Pin Search测试流程
- **二级命令 - 晶圆取放操作**：
  - **取晶圆操作**：
    - **GetWaferAsync**：获取晶圆操作
  - **放晶圆操作**：
    - **PutWaferAsync**：放置晶圆操作
  - **状态交换操作**：
    - **ExecuteStatusExchangeAsync**：执行状态交换
    - **ConvertStationTypeToIndex**：站点类型转换为索引
- **三级命令 - 基础轴控制操作**：
  - **机器人初始化**：
    - **InitializeRobotAsync**：机器人初始化(三轴归零)
  - **T轴操作**：
    - **T轴基础移动**：
      - **MoveTAxisToPositionAsync**：T轴移动到指定步数位置
    - **T轴位置移动**：
      - **MoveTAxisToLocationAsync**：根据站点类型移动T轴
    - **T轴归零**：
      - **ZeroTAxisAsync**：T轴归零
  - **R轴操作**：
    - **R轴基础移动**：
      - **MoveRAxisToPositionAsync**：R轴移动到指定步数位置
    - **R轴位置移动**：
      - **MoveRAxisToLocationAsync**：根据站点类型移动R轴
      - **MoveRAxisToHomeAsync**：R轴归零(收回)
    - **R轴归零**：
      - **ZeroRAxisAsync**：R轴归零
  - **Z轴操作**：
    - **Z轴基础移动**：
      - **MoveZAxisToPositionAsync**：Z轴移动到指定步数位置
    - **Z轴取放片位置移动**：
      - **MoveZAxisToGetPositionAsync**：Z轴移动到取片位置(两个重载)
      - **MoveZAxisToPutPositionAsync**：Z轴移动到放片位置(两个重载)
    - **Z轴归零**：
      - **ZeroZAxisAsync**：Z轴归零
- **参数获取方法**：
  - **GetZAxisPutPosition**：获取Z轴放片位置的步数

这两个文件组成了一个分层的机器人控制系统，从底层的MODBUS命令到高级的晶圆搬运操作，提供了完整的功能封装。文件结构通过嵌套region的方式组织，使代码更加清晰和易于维护。 