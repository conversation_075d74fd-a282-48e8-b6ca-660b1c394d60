using System;
using System.Collections.Generic;
using System.IO;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.MainSystem;
using Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.MainSystem
{
    /// <summary>
    /// 主系统配置参数根配置
    /// </summary>
    public class MainSystemConfigParametersConfig
    {
        public List<ConfigureSetting> ConfigureSettings { get; set; }
    }

    /// <summary>
    /// 主系统配置参数提供者 - 从JSON配置文件加载参数
    /// </summary>
    public class MainSystemConfigParametersProvider : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(MainSystemConfigParametersProvider));
        private readonly Dictionary<string, object> _settings = new Dictionary<string, object>();
        private readonly FileSystemWatcher _configWatcher;

        private static readonly Lazy<MainSystemConfigParametersProvider> _instance =
            new Lazy<MainSystemConfigParametersProvider>(() => new MainSystemConfigParametersProvider());

        // 配置文件路径
        private const string CONFIG_PATH = "Configs/SS200/SubsystemConfigure/MainSystem/MainSystemConfigParameters.json";

        // 最后修改时间，用于监测配置文件变化
        private DateTime _lastModifiedTime = DateTime.MinValue;

        public static MainSystemConfigParametersProvider Instance => _instance.Value;

        // 私有构造函数
        private MainSystemConfigParametersProvider()
        {
            // 初始化文件系统监视器
            string configDir = Path.GetDirectoryName(GetConfigFilePath());
            string configFileName = Path.GetFileName(CONFIG_PATH);

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            _configWatcher = new FileSystemWatcher(configDir, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            // 注册文件变化事件处理
            _configWatcher.Changed += OnConfigFileChanged;
            _configWatcher.Created += OnConfigFileChanged;

            // 初始化默认值
            InitializeDefaultValues();

            // 尝试加载配置文件
            LoadFromJson();
        }

        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 由于文件系统事件可能会触发多次，这里添加简单的防抖动处理
                if ((DateTime.Now - _lastModifiedTime).TotalMilliseconds < 100)
                {
                    return;
                }

                _logger.Info($"检测到配置文件变化: {e.FullPath}, 变化类型: {e.ChangeType}");
                LoadFromJson();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理配置文件变化事件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaultValues()
        {
            // 基本系统配置参数
            _settings["SSC1"] = 8;    // 传送室1晶圆尺寸 (inch)
            _settings["SSC2"] = 8;    // 传送室2晶圆尺寸 (inch)
            _settings["SSC3"] = "CHA and CHB";  // 腔室位置
            _settings["SSC4"] = "ICP and CCP";  // 腔室A工艺类型
            _settings["SSC5"] = "ICP and CCP";  // 腔室B工艺类型
            _settings["SSC6"] = "fixed";        // 卡匣巢类型
            _settings["SSC7"] = "A";            // 狭缝门类型
            _settings["SSC8"] = 30;   // 优先级生效时间 (min)
            _settings["SSC9"] = "Y";  // 映射功能启用
            _settings["SSC10"] = "Y"; // 跳过空槽位启用
            _settings["SSC11"] = 25;  // 冷却室温度 (℃)
            _settings["SSC12"] = 5;   // 腔室温度偏差 (℃)
            _settings["SSC13"] = "A"; // 传输压力类型
            _settings["SSC14"] = "A"; // 槽位产品顺序

            // 使用MainSystemConfigureSettings获取默认设置
            try
            {
                var defaultSettings = MainSystemConfigureSettings.GetDefaultSettings();

                // 将默认设置添加到设置字典中
                foreach (var setting in defaultSettings)
                {
                    if (!string.IsNullOrEmpty(setting.Code))
                    {
                        // 对于字符串类型的参数，需要特殊处理
                        if (setting.Code == EnuMainSystemConfigParameterCodes.SSC3.ToString() ||
                            setting.Code == EnuMainSystemConfigParameterCodes.SSC4.ToString() ||
                            setting.Code == EnuMainSystemConfigParameterCodes.SSC5.ToString() ||
                            setting.Code == EnuMainSystemConfigParameterCodes.SSC6.ToString() ||
                            setting.Code == EnuMainSystemConfigParameterCodes.SSC7.ToString() ||
                            setting.Code == EnuMainSystemConfigParameterCodes.SSC13.ToString() ||
                            setting.Code == EnuMainSystemConfigParameterCodes.SSC14.ToString())
                        {
                            // 这些参数已经在上面初始化了，不需要再次设置
                            continue;
                        }

                        // 对于布尔类型的参数，需要特殊处理
                        if (setting.Code == EnuMainSystemConfigParameterCodes.SSC9.ToString() ||
                            setting.Code == EnuMainSystemConfigParameterCodes.SSC10.ToString())
                        {
                            _settings[setting.Code] = setting.IntValue == 1 ? "Y" : "N";
                        }
                        else
                        {
                            _settings[setting.Code] = setting.Value;
                        }

                        _logger.Debug($"初始化默认参数 {setting.Code} = {setting.Value} ({setting.Description})");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"初始化默认参数时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从JSON配置文件加载参数
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool LoadFromJson()
        {
            try
            {
                string jsonFilePath = GetConfigFilePath();
                if (!File.Exists(jsonFilePath))
                {
                    _logger.Warn($"主系统配置参数文件不存在: {jsonFilePath}，将使用默认值");
                    return false;
                }

                // 获取文件最后修改时间
                DateTime currentModified = File.GetLastWriteTime(jsonFilePath);

                // 如果文件未修改，则不重新加载
                if (currentModified == _lastModifiedTime)
                {
                    return true;
                }

                _lastModifiedTime = currentModified;

                string jsonContent;
                using (var fileStream = new FileStream(jsonFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    jsonContent = reader.ReadToEnd();
                }
                var config = JsonConvert.DeserializeObject<MainSystemConfigParametersConfig>(jsonContent);

                if (config?.ConfigureSettings == null || config.ConfigureSettings.Count == 0)
                {
                    _logger.Warn("未找到有效的配置参数，将使用默认值");
                    return false;
                }

                // 临时字典，验证成功后再替换
                var tempSettings = new Dictionary<string, object>();
                foreach (var setting in config.ConfigureSettings)
                {
                    if (string.IsNullOrEmpty(setting.Code))
                    {
                        _logger.Warn($"参数ID {setting.Id} 缺少代码标识，已跳过");
                        continue;
                    }

                    tempSettings[setting.Code] = setting.Value;
                    _logger.Debug($"加载参数 {setting.Code} = {setting.Value} ({setting.Description})");
                }

                // 验证所有必要参数都存在
                ValidateRequiredParameters(tempSettings);

                // 更新参数字典
                foreach (var kvp in tempSettings)
                {
                    _settings[kvp.Key] = kvp.Value;
                }

                _logger.Info($"成功从 {jsonFilePath} 加载 {tempSettings.Count} 个主系统配置参数");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载主系统配置参数文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 验证所有必要参数都存在
        /// </summary>
        private void ValidateRequiredParameters(Dictionary<string, object> tempSettings)
        {
            // 获取所有必要参数的代码
            var requiredCodes = new List<string>();

            // 添加所有SSC参数代码
            foreach (EnuMainSystemConfigParameterCodes code in Enum.GetValues(typeof(EnuMainSystemConfigParameterCodes)))
            {
                requiredCodes.Add(code.ToString());
            }

            // 验证必要参数是否存在
            foreach (var code in requiredCodes)
            {
                if (!tempSettings.ContainsKey(code))
                {
                    _logger.Warn($"配置文件中缺少必要参数 {code}，将使用默认值");
                    if (_settings.ContainsKey(code))
                    {
                        tempSettings[code] = _settings[code]; // 使用默认值
                    }
                }
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        private string GetConfigFilePath()
        {
            try
            {
                return App.ConfigHelper.GetConfigFilePath(CONFIG_PATH);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取配置文件路径失败: {ex.Message}", ex);

                // 回退策略 - 尝试直接拼接路径
                string fallbackPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    CONFIG_PATH);

                _logger.Info($"使用回退路径: {fallbackPath}");
                return fallbackPath;
            }
        }

        /// <summary>
        /// 获取参数值（泛型方法）
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="code">参数代码 (如 "MPS1")</param>
        /// <returns>参数值</returns>
        public T GetSettingValue<T>(string code)
        {
            if (_settings.TryGetValue(code, out object value))
            {
                if (value is T typedValue)
                {
                    return typedValue;
                }

                try
                {
                    // 尝试转换类型
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch (Exception ex)
                {
                    _logger.Error($"参数类型转换失败: {code}, 期望类型: {typeof(T).Name}, 实际值: {value}", ex);
                    throw new InvalidCastException($"参数类型不匹配: {code}");
                }
            }

            throw new KeyNotFoundException($"找不到主系统配置参数: {code}");
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        /// <param name="enuMainSystemConfigParameterCodes">参数代码枚举</param>
        /// <returns>参数值</returns>
        public T GetSettingValue<T>(EnuMainSystemConfigParameterCodes enuMainSystemConfigParameterCodes)
        {
            return GetSettingValue<T>(enuMainSystemConfigParameterCodes.ToString());
        }

        /// <summary>
        /// 获取int类型参数值
        /// </summary>
        /// <param name="enuMainSystemConfigParameterCodes">参数代码枚举</param>
        /// <returns>int类型参数值</returns>
        public int GetIntSettingValue(EnuMainSystemConfigParameterCodes enuMainSystemConfigParameterCodes)
        {
            return GetSettingValue<int>(enuMainSystemConfigParameterCodes.ToString());
        }

        /// <summary>
        /// 获取string类型参数值
        /// </summary>
        /// <param name="enuMainSystemConfigParameterCodes">参数代码枚举</param>
        /// <returns>string类型参数值</returns>
        public string GetStringSettingValue(EnuMainSystemConfigParameterCodes enuMainSystemConfigParameterCodes)
        {
            return GetSettingValue<string>(enuMainSystemConfigParameterCodes.ToString());
        }

        /// <summary>
        /// 获取bool类型参数值
        /// </summary>
        /// <param name="enuMainSystemConfigParameterCodes">参数代码枚举</param>
        /// <returns>bool类型参数值</returns>
        public bool GetBoolSettingValue(EnuMainSystemConfigParameterCodes enuMainSystemConfigParameterCodes)
        {
            // 对于Y/N类型的参数，转换为bool
            string value = GetSettingValue<string>(enuMainSystemConfigParameterCodes.ToString());
            return value == "Y";
        }

        #region 辅助方法 - 获取系统配置参数

        /// <summary>
        /// 获取传送室1晶圆尺寸
        /// </summary>
        /// <returns>传送室1晶圆尺寸 (inch)</returns>
        public int GetShuttle1WaferSize()
        {
            return GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC1);
        }

        /// <summary>
        /// 获取传送室2晶圆尺寸
        /// </summary>
        /// <returns>传送室2晶圆尺寸 (inch)</returns>
        public int GetShuttle2WaferSize()
        {
            return GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC2);
        }

        /// <summary>
        /// 获取腔室位置
        /// </summary>
        /// <returns>腔室位置 (CHA / CHB / CHA and CHB)</returns>
        public string GetChamberLocation()
        {
            return GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC3);
        }

        /// <summary>
        /// 获取腔室A工艺类型
        /// </summary>
        /// <returns>腔室A工艺类型 (ICP / CCP / ICP and CCP)</returns>
        public string GetChamberAProcess()
        {
            return GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC4);
        }

        /// <summary>
        /// 获取腔室B工艺类型
        /// </summary>
        /// <returns>腔室B工艺类型 (ICP / CCP / ICP and CCP)</returns>
        public string GetChamberBProcess()
        {
            return GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC5);
        }

        /// <summary>
        /// 获取卡匣巢类型
        /// </summary>
        /// <returns>卡匣巢类型 (fixed/SMIF)</returns>
        public string GetCassetteNestType()
        {
            return GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC6);
        }

        /// <summary>
        /// 获取狭缝门类型
        /// </summary>
        /// <returns>狭缝门类型 (A/B)</returns>
        public string GetSlitDoorType()
        {
            return GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC7);
        }

        /// <summary>
        /// 获取优先级生效时间
        /// </summary>
        /// <returns>优先级生效时间 (min)</returns>
        public int GetPriorityEffectiveTime()
        {
            return GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC8);
        }

        /// <summary>
        /// 获取映射功能启用状态
        /// </summary>
        /// <returns>映射功能是否启用</returns>
        public bool GetMappingFunctionEnabled()
        {
            return GetBoolSettingValue(EnuMainSystemConfigParameterCodes.SSC9);
        }

        /// <summary>
        /// 获取跳过空槽位启用状态
        /// </summary>
        /// <returns>跳过空槽位是否启用</returns>
        public bool GetSkipEmptySlotEnabled()
        {
            return GetBoolSettingValue(EnuMainSystemConfigParameterCodes.SSC10);
        }

        /// <summary>
        /// 获取冷却室温度
        /// </summary>
        /// <returns>冷却室温度 (℃)</returns>
        public int GetCoolingChamberTemperature()
        {
            return GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC11);
        }

        /// <summary>
        /// 获取腔室温度偏差
        /// </summary>
        /// <returns>腔室温度偏差 (℃)</returns>
        public int GetChamberTemperatureDeviation()
        {
            return GetIntSettingValue(EnuMainSystemConfigParameterCodes.SSC12);
        }

        /// <summary>
        /// 获取传输压力类型
        /// </summary>
        /// <returns>传输压力类型 (A/B)</returns>
        public string GetTransferPressureType()
        {
            return GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC13);
        }

        /// <summary>
        /// 获取槽位产品顺序
        /// </summary>
        /// <returns>槽位产品顺序 (A/B)</returns>
        public string GetSlotProductOrder()
        {
            return GetStringSettingValue(EnuMainSystemConfigParameterCodes.SSC14);
        }

        #endregion 辅助方法 - 获取系统配置参数

        public void Dispose()
        {
            _configWatcher?.Dispose();
        }
    }
}