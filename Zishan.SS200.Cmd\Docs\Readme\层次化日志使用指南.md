# 层次化日志使用指南

## 概述

为了解决日志输出缺乏层次感、查看不友好的问题，我们对 `UILogService` 进行了增强，添加了层次化日志功能。通过缩进来显示调用的层级关系，让日志更加清晰易读。

## 功能特性

### 1. 自动缩进管理
- 支持自动增加/减少缩进层级
- 使用 `using` 语句自动管理作用域
- 线程安全的缩进层级管理

### 2. 灵活的配置选项
- 可配置缩进字符串（空格数量、Tab等）
- 可设置最大缩进层级限制
- 可启用/禁用缩进功能

### 3. 便捷的使用方法
- 提供多种日志记录方法
- 支持手动和自动缩进管理
- 向后兼容现有代码

## 使用方法

### 方法1：使用 using 语句（推荐）

```csharp
UILogService.AddLog("开始执行晶圆传输操作");

using (UILogService.CreateIndentScope())
{
    UILogService.AddLog("搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2)");
    
    using (UILogService.CreateIndentScope())
    {
        UILogService.AddLog("取Wafer: 从ChamberA(SLOT:1)获取Wafer");
        
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog("开始取Wafer...");
            UILogService.AddLog("结束取Wafer...");
        }
    }
}

UILogService.AddSuccessLog("搬运Wafer成功");
```

**输出效果：**
```
开始执行晶圆传输操作
    搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2)
        取Wafer: 从ChamberA(SLOT:1)获取Wafer
            开始取Wafer...
            结束取Wafer...
✅ 搬运Wafer成功
```

### 方法2：手动管理缩进

```csharp
UILogService.AddLog("执行搬运，Robot连接状态: True");

UILogService.IncreaseIndent();
UILogService.AddLog("搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2)");

UILogService.IncreaseIndent();
UILogService.AddLog("取Wafer: 从ChamberA(SLOT:1)获取Wafer");

UILogService.IncreaseIndent();
UILogService.AddLog("开始取Wafer...");
UILogService.AddLog("结束取Wafer...");
UILogService.DecreaseIndent();

UILogService.DecreaseIndent();
UILogService.DecreaseIndent();

UILogService.AddSuccessLog("搬运Wafer成功");
```

### 方法3：使用便捷方法

```csharp
UILogService.AddLogAndIncreaseIndent("开始复杂操作");

UILogService.AddLogAndIncreaseIndent("第一阶段：初始化");
UILogService.AddLog("检查设备状态...");
UILogService.DecreaseIndentAndAddSuccessLog("初始化完成");

UILogService.AddLogAndIncreaseIndent("第二阶段：执行");
UILogService.AddLog("执行主要操作...");
UILogService.DecreaseIndentAndAddSuccessLog("执行完成");

UILogService.DecreaseIndentAndAddSuccessLog("复杂操作全部完成");
```

## API 参考

### 基础日志方法
- `AddLog(string log)` - 添加普通日志
- `AddSuccessLog(string log)` - 添加成功日志（✅）
- `AddErrorLog(string log)` - 添加错误日志（❌）
- `AddWarningLog(string log)` - 添加警告日志（⚠️）
- `AddInfoLog(string log)` - 添加信息日志（ℹ️）

### 缩进控制方法
- `IncreaseIndent()` - 增加缩进层级
- `DecreaseIndent()` - 减少缩进层级
- `ResetIndent()` - 重置缩进层级为0
- `CreateIndentScope()` - 创建缩进作用域
- `GetCurrentIndentLevel()` - 获取当前缩进层级

### 便捷方法
- `AddLogAndIncreaseIndent(string log)` - 添加日志并增加缩进
- `DecreaseIndentAndAddLog(string log)` - 减少缩进并添加日志
- `DecreaseIndentAndAddSuccessLog(string log)` - 减少缩进并添加成功日志
- `DecreaseIndentAndAddErrorLog(string log)` - 减少缩进并添加错误日志

### 配置方法
- `SetIndentString(string indentString)` - 设置缩进字符串
- `SetMaxIndentLevel(int maxLevel)` - 设置最大缩进层级
- `SetIndentEnabled(bool enabled)` - 启用/禁用缩进功能

## 配置选项

### 缩进字符串配置

```csharp
// 使用4个空格（默认）
UILogService.SetIndentString("    ");

// 使用2个空格
UILogService.SetIndentString("  ");

// 使用Tab
UILogService.SetIndentString("\t");
```

### 最大缩进层级

```csharp
// 设置最大缩进层级为5
UILogService.SetMaxIndentLevel(5);
```

### 启用/禁用缩进

```csharp
// 禁用缩进功能
UILogService.SetIndentEnabled(false);

// 启用缩进功能
UILogService.SetIndentEnabled(true);
```

## 在现有代码中的应用

### 晶圆传输操作示例

```csharp
public async Task<(bool Success, string Message)> TrasferWaferAsync(...)
{
    UILogService.AddLogAndIncreaseIndent("开始执行晶圆传输");
    
    try
    {
        // 初始化
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog("机器人初始化");
            // ... 初始化代码
            UILogService.AddSuccessLog("初始化完成");
        }
        
        // 获取晶圆
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog($"从{sourceStation}获取晶圆");
            var result = await GetWaferAsync(...);
            if (!result.Success)
            {
                UILogService.AddErrorLog($"获取晶圆失败: {result.Message}");
                return result;
            }
            UILogService.AddSuccessLog("获取晶圆成功");
        }
        
        // 放置晶圆
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog($"将晶圆放置到{targetStation}");
            var result = await PutWaferAsync(...);
            if (!result.Success)
            {
                UILogService.AddErrorLog($"放置晶圆失败: {result.Message}");
                return result;
            }
            UILogService.AddSuccessLog("放置晶圆成功");
        }
        
        UILogService.DecreaseIndentAndAddSuccessLog("晶圆传输成功");
        return (true, "传输成功");
    }
    catch (Exception ex)
    {
        UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输异常: {ex.Message}");
        return (false, ex.Message);
    }
}
```

## 最佳实践

### 1. 使用 using 语句
推荐使用 `using (UILogService.CreateIndentScope())` 来自动管理缩进，避免忘记减少缩进层级。

### 2. 在方法开始和结束记录日志
```csharp
public async Task SomeOperationAsync()
{
    UILogService.AddLogAndIncreaseIndent("开始某个操作");
    
    try
    {
        // 操作代码
        UILogService.DecreaseIndentAndAddSuccessLog("操作成功完成");
    }
    catch (Exception ex)
    {
        UILogService.DecreaseIndentAndAddErrorLog($"操作失败: {ex.Message}");
        throw;
    }
}
```

### 3. 合理控制缩进层级
避免过深的嵌套，建议不超过5层缩进。

### 4. 错误处理中的缩进管理
确保在异常情况下也能正确管理缩进层级。

## 线程安全

缩进层级使用 `ThreadStatic` 属性，确保每个线程有独立的缩进状态，多线程环境下安全使用。

## 向后兼容

现有的日志调用代码无需修改，只是没有缩进效果。可以逐步迁移到新的层次化日志方式。
