using System;
using System.Globalization;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    public class AndMultiValueConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            return true;
            // if (values.Length != 3 || !(values[0] is bool) || !(values[1] is bool) || !(values[2] is EnuMode))
            // {
            //     return false;
            // }
            //
            // bool isRunning = (bool)values[0];
            // bool hasWafer = (bool)values[1];
            // EnuMode mode = (EnuMode)values[2];
            //
            // if (Golbal.IsDevDebug)//DevDebug模式下，启用手动模式
            // {
            //     mode = EnuMode.Manual;
            // }
            //
            // return !isRunning && hasWafer && mode == EnuMode.Manual;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}