# 手动搬运命令布局说明

## 布局结构

手动搬运命令区域采用了优化的Grid布局，分为三行：

### 第一行：循环控制和Pin Search测试
使用StackPanel水平排列：
- **循环次数文本框**: 设置循环执行次数（-1=无限循环，默认1）
- **Pin Search测试按钮**: 执行Pin Search测试，支持循环
- **停止循环按钮**: 停止当前正在执行的循环操作（红色危险样式）

### 第二行：搬运参数设置
使用WrapPanel水平排列，支持自动换行：
- **From单元位置**: 选择源位置（ChamberA、ChamberB、Cassette等）
- **From SLOT**: 选择源SLOT号
- **To单元位置**: 选择目标位置
- **To SLOT**: 选择目标SLOT号
- **机械臂位置**: 选择Nose端或Smooth端
- **搬运按钮**: 执行晶圆搬运操作，支持循环

### 第三行：其他功能按钮
使用StackPanel水平排列：
- **搬运测试按钮**: 开发调试模式下可见
- **获取当前RTZ位置按钮**: 获取机器人当前位置信息

## 布局特点

### 1. 响应式设计
- 使用WrapPanel确保在窗口较小时控件能自动换行
- 设置合适的控件宽度，保证显示效果

### 2. 逻辑分组
- 循环控制功能集中在第一行
- 搬运参数设置集中在第二行
- 辅助功能按钮集中在第三行

### 3. 视觉层次
- 使用间距和分组提高可读性
- 重要操作按钮使用醒目的样式
- 停止循环按钮使用危险样式突出显示

## 控件说明

### 循环次数文本框
```xml
<TextBox
    Width="80"
    hc:InfoElement.Placeholder="-1=无限循环"
    hc:InfoElement.Title="循环次数："
    Text="{Binding LoopCount}"
    ToolTip="设置循环执行次数：-1代表无限循环，默认1执行一次" />
```

### 停止循环按钮
```xml
<Button
    Width="80"
    Command="{Binding StopLoopCommand}"
    Content="停止循环"
    Style="{StaticResource ButtonDanger}"
    ToolTip="停止当前正在执行的循环操作" />
```

### 搬运按钮
```xml
<Button
    Command="{Binding TrasferWaferCommand}"
    Content="搬运"
    Style="{StaticResource ButtonPrimary}"
    ToolTip="执行晶圆搬运操作，循环次数由上方循环次数文本框控制" />
```

## 使用流程

1. **设置循环次数**: 在循环次数文本框中输入期望的执行次数
2. **配置搬运参数**: 选择From/To位置、SLOT号和机械臂类型
3. **执行操作**: 
   - 点击"Pin Search测试"执行Pin Search循环测试
   - 点击"搬运"执行晶圆搬运循环操作
4. **停止循环**: 如需中断，点击"停止循环"按钮

## 布局优化

### 修复前的问题
- Grid列定义过多，导致布局混乱
- 控件位置冲突，部分控件无法正常显示
- 缺乏逻辑分组，用户体验不佳

### 修复后的改进
- 简化Grid结构，使用3行4列的清晰布局
- 采用StackPanel和WrapPanel提高布局灵活性
- 按功能逻辑分组，提升用户体验
- 添加适当的间距和工具提示

## 技术实现

### Grid定义
```xml
<Grid.RowDefinitions>
    <RowDefinition Height="Auto" />
    <RowDefinition Height="10" />
    <RowDefinition Height="Auto" />
</Grid.RowDefinitions>
<Grid.ColumnDefinitions>
    <ColumnDefinition Width="Auto" />
    <ColumnDefinition Width="Auto" />
    <ColumnDefinition Width="Auto" />
    <ColumnDefinition Width="*" />
</Grid.ColumnDefinitions>
```

### 容器选择
- **StackPanel**: 用于简单的水平或垂直排列
- **WrapPanel**: 用于需要自动换行的控件组
- **Grid**: 用于整体布局结构

这种布局设计确保了界面的清晰性、功能性和可维护性。
