using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.Process
{
    /// <summary>
    /// PinUp 状态
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuPinUP
    {
        /// <summary>
        /// 降
        /// </summary>
        [Description("降")]
        Down = 0,

        /// <summary>
        /// 升
        /// </summary>
        [Description("升")]
        Up = 1,

        /// <summary>
        /// 无，不升不降【开发测试额外添加】
        /// </summary>
        [Description("无升降")]
        None = 26
    }
}