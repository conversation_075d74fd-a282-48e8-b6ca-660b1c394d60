using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle
{
    /// <summary>
    /// 槽位晶圆状态枚举 (LSS1-LSS16, LSSW1-LSSW4)
    /// </summary>
    public enum EnuSlotWaferStatus
    {
        /// <summary>
        /// 无晶圆 (LSS XX 0, LSSW XX 0)
        /// </summary>
        [Description("无晶圆")]
        NoWafer = 0,

        /// <summary>
        /// 有晶圆 (LSS XX 1, LSSW XX 1)
        /// </summary>
        [Description("有晶圆")]
        HasWafer = 1,

        /// <summary>
        /// 晶圆未知 (LSS XX 2) - 仅适用于LSS1-LSS8
        /// </summary>
        [Description("晶圆未知")]
        WaferUnknown = 2
    }
}
