# Zishan.SS200.Cmd 数据流和通信机制

## Modbus通信概述

Zish<PERSON>.SS200.Cmd应用程序通过Modbus TCP协议与PLC设备通信。Modbus是一种工业自动化领域广泛使用的通信协议，该应用使用NModbus库实现Modbus客户端功能。

## 通信架构

应用程序的通信架构采用分层设计：

```
┌────────────────────────┐
│      ViewModel         │
└──────────┬─────────────┘
           │
┌──────────▼─────────────┐
│   S200McuCmdService    │
└──────────┬─────────────┘
           │
┌──────────▼─────────────┐
│   ModbusClientService  │
└──────────┬─────────────┘
           │
┌──────────▼─────────────┐
│     NModbus Library    │
└──────────┬─────────────┘
           │
┌──────────▼─────────────┐
│    TCP/IP Network      │
└──────────┬─────────────┘
           │
┌──────────▼─────────────┐
│      PLC Device        │
└────────────────────────┘
```

## 关键组件分析

### 1. ModbusClientService

ModbusClientService是应用程序通信的核心组件，负责建立TCP连接并处理Modbus请求：

```csharp
public class ModbusClientService : IModbusClientService
{
    private IModbusMaster _master;
    private TcpClient _tcpClient;
    private bool _isConnected;
    
    // 连接到Modbus服务器
    public async Task<bool> ConnectAsync(string ipAddress, int port, int timeout = 3000)
    {
        try
        {
            _tcpClient = new TcpClient();
            using var cts = new CancellationTokenSource(timeout);
            await _tcpClient.ConnectAsync(ipAddress, port).WaitAsync(cts.Token);

            _master = new ModbusFactory().CreateMaster(_tcpClient);
            _isConnected = true;
            
            return true;
        }
        catch (Exception ex)
        {
            // 错误处理...
            return false;
        }
    }
    
    // 读取保持寄存器
    public async Task<ushort[]> ReadHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints)
    {
        try
        {
            var result = await _master.ReadHoldingRegistersAsync(slaveAddress, startAddress, numberOfPoints);
            return result;
        }
        catch (Exception ex)
        {
            // 错误处理...
            throw;
        }
    }
    
    // 写入保持寄存器
    public async Task WriteHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort[] values)
    {
        try
        {
            await _master.WriteMultipleRegistersAsync(slaveAddress, startAddress, values);
        }
        catch (Exception ex)
        {
            // 错误处理...
            throw;
        }
    }
    
    // 写入单个寄存器
    public async Task WriteSingleRegisterAsync(byte slaveAddress, ushort address, ushort value)
    {
        try
        {
            await _master.WriteSingleRegisterAsync(slaveAddress, address, value);
        }
        catch (Exception ex)
        {
            // 错误处理...
            throw;
        }
    }
}
```

### 2. S200McuCmdService

S200McuCmdService是更高级别的通信抽象，基于ModbusClientService提供特定设备的命令服务：

```csharp
public class S200McuCmdService
{
    private readonly IModbusClientService _modbusClient;
    
    public S200McuCmdService(IModbusClientService modbusClient)
    {
        _modbusClient = modbusClient;
    }
    
    // 设备命令功能
    public async Task<TaskHandleResult> ExecuteDeviceCommand(EnuShuttleCmdName cmdName, List<ushort> parameters)
    {
        // 命令执行逻辑...
    }
}
```

### 3. CmdTaskHandlel

CmdTaskHandlel封装了Modbus命令执行的通用机制，实现了命令状态管理和监控：

```csharp
public class CmdTaskHandlel
{
    // 状态标志和寄存器
    public ModbusRegister StatusFlag { get; set; }
    public ModbusRegister RunFlag { get; set; }
    public ModbusRegister RunInfo { get; set; }
    public ModbusRegister ReturnInfo { get; set; }
    public ModbusRegister CmdStartAddr { get; set; }
    public ModbusRegister CmdLen { get; set; }
    
    // 执行命令
    public async Task<TaskHandleResult> ExecuteCommandAsync(IModbusMaster master, byte slaveId, EnuCmdIndexInfo enuCmdIndexInfo, List<ushort> cmdPars, int timeout = 5000)
    {
        // 命令执行流程...
    }
}
```

## 数据流程分析

### 1. 连接建立

连接过程首先从应用程序配置文件读取Modbus服务器IP和端口：

```csharp
private static readonly string ModbusHost = App.AppIniConfig.Ip;
private static readonly int ModbusPort = App.AppIniConfig.Port;
```

然后，在MainWindowViewModel中使用ModbusClientService建立连接：

```csharp
// 连接到Modbus服务器
await _modbusClient.ConnectAsync(ModbusHost, ModbusPort);
```

### 2. 命令执行流程

命令执行遵循以下流程：

1. **命令准备**：在ViewModel中构建命令参数
   ```csharp
   var cmdParameters = new List<ushort>() { 0x01, 0x02, 0x03 };
   ```

2. **命令发送**：通过S200McuCmdService发送命令
   ```csharp
   var result = await _shuttleMcuCmdService.Shuttle.Run(EnuShuttleCmdName.S1_SD, cmdParameters, ref RunInf);
   ```

3. **底层执行**：命令经过S200McuCmdService转换，通过CmdTaskHandlel执行
   ```csharp
   var taskHandle = new CmdTaskHandlel(...);
   var result = await taskHandle.ExecuteCommandAsync(master, slaveId, cmdIndex, parameters);
   ```

4. **结果处理**：命令执行结果返回给ViewModel处理
   ```csharp
   if (result == TaskHandleResult.Success)
   {
       // 成功处理...
   }
   else
   {
       // 失败处理...
   }
   ```

### 3. 数据轮询机制

应用程序使用后台线程定期轮询关键寄存器状态：

```csharp
private async Task StartPollingAsync(CancellationToken cancellationToken)
{
    while (!cancellationToken.IsCancellationRequested)
    {
        try
        {
            if (_modbusClient.IsConnected)
            {
                // 读取状态寄存器
                var registers = await _modbusClient.ReadHoldingRegistersAsync(1, 0, 10);
                // 处理读取结果...
            }
        }
        catch (Exception ex)
        {
            // 异常处理...
        }
        
        // 等待下一轮轮询
        await Task.Delay(100, cancellationToken);
    }
}
```

## 数据类型和转换

### 1. 基本数据类型

Modbus协议使用16位寄存器作为基本数据单元，应用程序中定义了多种数据类型：

```csharp
// 基本Modbus寄存器
public class BaseModbusRegister
{
    public ushort Address { get; set; }
    public ushort Value { get; set; }
}

// Modbus寄存器（含扩展信息）
public class ModbusRegister : BaseModbusRegister
{
    public string Title { get; set; }
    public string Description { get; set; }
    public string Remark { get; set; }
}
```

### 2. 32位浮点数处理

工业应用中常用IEEE 754浮点数表示物理量，需要特殊处理：

```csharp
// 处理32位浮点数（占用两个寄存器）
public class Float32Register
{
    public ushort HighWordAddress { get; set; }
    public ushort LowWordAddress { get; set; }
    public ushort HighWordValue { get; set; }
    public ushort LowWordValue { get; set; }
    
    public float Combinevalue
    {
        get
        {
            // 合并两个16位寄存器为32位浮点数
            byte[] bytes = new byte[4];
            BitConverter.GetBytes(HighWordValue).CopyTo(bytes, 0);
            BitConverter.GetBytes(LowWordValue).CopyTo(bytes, 2);
            return BitConverter.ToSingle(bytes, 0);
        }
    }
}
```

## 错误处理机制

通信层实现了全面的错误处理机制：

### 1. 连接异常处理

```csharp
try
{
    await _tcpClient.ConnectAsync(ipAddress, port).WaitAsync(cts.Token);
}
catch (OperationCanceledException)
{
    _strMsg = $"连接Modbus服务器超时";
    ShowThrottledError(_strMsg);
    return false;
}
catch (Exception ex)
{
    _strMsg = $"连接Modbus服务器失败: {ex.Message}";
    ShowThrottledError(_strMsg);
    return false;
}
```

### 2. 命令超时处理

```csharp
public async Task<TaskHandleResult> WaitForCompletionAsync(IModbusMaster master, byte slaveId, int timeout = 5000)
{
    var stopwatch = Stopwatch.StartNew();
    
    while (stopwatch.ElapsedMilliseconds < timeout)
    {
        var status = await MonitorStatusAsync(master, slaveId);
        
        if (status.Status == TaskHandleStatus.Complete)
        {
            return TaskHandleResult.Success;
        }
        
        await Task.Delay(50);
    }
    
    return TaskHandleResult.Timeout;
}
```

### 3. 节流机制

为避免大量错误消息导致UI卡顿，实现了错误消息节流：

```csharp
private void ShowThrottledError(string message)
{
    var now = DateTime.Now;
    if (now - _lastErrorTime >= ErrorThrottleInterval)
    {
        HcGrowlExtensions.Error(message);
        _lastErrorTime = now;
    }
    // 始终记录日志，不受节流影响
    _logger.Error(message);
}
```

## 通信配置

通信参数通过INI配置文件设置：

```ini
[General] 
;Modbus 服务端IP地址
Ip = 127.0.0.1

;Modbus 服务端端口
Port = 502

;是否自动启动，默认：True
AutoStart = True
```

配置加载过程：

```csharp
//读取Modbus配置文件
AppIniConfig = PubHelper.GetAppIniConfig(iniFilePath);
```

## 诊断与日志

通信过程中的各种事件和异常都会记录到日志：

```csharp
_logger.Debug($"正在读取保持寄存器 - 从站地址:{slaveAddress}, 起始地址:{startAddress}, 数量:{numberOfPoints}");
var result = await _master.ReadHoldingRegistersAsync(slaveAddress, startAddress, numberOfPoints);
_logger.Debug($"读取保持寄存器成功 - 16进制值:{string.Join(",", result.Select(v => $"{v:X4}"))}");
```

## 总结

Zishan.SS200.Cmd应用程序的通信机制采用了分层设计，通过NModbus库实现了稳定、可靠的Modbus TCP通信。关键特点包括：

1. **分层架构**：从底层TCP通信到高级命令抽象的清晰分层
2. **错误处理**：全面的错误捕获和恢复机制
3. **命令抽象**：将复杂的Modbus命令封装为简单API
4. **数据转换**：处理不同数据类型之间的转换
5. **状态监控**：通过轮询机制实时监控设备状态

这种设计使应用程序能够可靠地与工业设备通信，并提供清晰的错误反馈和状态更新。 