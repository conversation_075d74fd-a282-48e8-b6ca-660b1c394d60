<UserControl x:Class="$NAMESPACE$.$CLASS_NAME$"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:$NAMESPACE$"
             xmlns:vm="clr-namespace:$NAMESPACE$.ViewModels"
             xmlns:converters="clr-namespace:$NAMESPACE$.Converters"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800">

  <UserControl.Resources>
    <!-- 资源字典 -->
    <ResourceDictionary>
      <ResourceDictionary.MergedDictionaries>
        <!-- 合并应用资源 -->
      </ResourceDictionary.MergedDictionaries>

      <!-- 转换器 -->
      <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

      <!-- 颜色资源 -->
      <SolidColorBrush x:Key="BrushPrimary"
          Color="#1E88E5"/>
      <SolidColorBrush x:Key="BackgroundSecondary"
          Color="#F5F5F5"/>
      <SolidColorBrush x:Key="BorderBrush"
          Color="#DDDDDD"/>

      <!-- 局部样式 -->
      <Style x:Key="HeaderTextBlockStyle"
             TargetType="TextBlock">
        <Setter Property="FontSize"
                Value="20"/>
        <Setter Property="FontWeight"
                Value="SemiBold"/>
        <Setter Property="Margin"
                Value="0,0,0,10"/>
      </Style>

      <Style x:Key="SubHeaderTextBlockStyle"
             TargetType="TextBlock">
        <Setter Property="FontSize"
                Value="16"/>
        <Setter Property="FontWeight"
                Value="SemiBold"/>
        <Setter Property="Margin"
                Value="0,10,0,5"/>
      </Style>

      <Style x:Key="StandardButtonStyle"
             TargetType="Button">
        <Setter Property="Padding"
                Value="15,5"/>
        <Setter Property="Margin"
                Value="0,0,10,0"/>
        <Setter Property="MinWidth"
                Value="80"/>
      </Style>
    </ResourceDictionary>
  </UserControl.Resources>

  <Grid>
    <Grid.RowDefinitions>
      <RowDefinition Height="Auto"/>
      <RowDefinition Height="*"/>
      <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>

    <!-- 标题区域 -->
    <Border Grid.Row="0"
            Background="{StaticResource ResourceKey=BrushPrimary}"
            Padding="15">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <TextBlock Text="{Binding Title}"
                   Style="{StaticResource HeaderTextBlockStyle}"
                   Foreground="White"/>

        <StackPanel Grid.Column="1"
                    Orientation="Horizontal">
          <Button Content="刷新"
                  Command="{Binding LoadDataCommand}"
                  Style="{StaticResource StandardButtonStyle}"/>
        </StackPanel>
      </Grid>
    </Border>

    <!-- 内容区域 -->
    <Grid Grid.Row="1"
          Margin="15">
      <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
      </Grid.RowDefinitions>

      <!-- 筛选条件区域 -->
      <Expander Grid.Row="0"
                Header="筛选条件"
                IsExpanded="True"
                Margin="0,0,0,10">
        <Grid Margin="0,10,0,0">
          <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
          </Grid.ColumnDefinitions>
          <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
          </Grid.RowDefinitions>

          <TextBlock Grid.Row="0"
                     Grid.Column="0"
                     Text="名称:"
                     Margin="0,0,10,0"
                     VerticalAlignment="Center"/>
          <TextBox Grid.Row="0"
                   Grid.Column="1"
                   Margin="0,0,20,0"/>

          <TextBlock Grid.Row="0"
                     Grid.Column="2"
                     Text="类型:"
                     Margin="0,0,10,0"
                     VerticalAlignment="Center"/>
          <ComboBox Grid.Row="0"
                    Grid.Column="3"/>

          <TextBlock Grid.Row="1"
                     Grid.Column="0"
                     Text="状态:"
                     Margin="0,10,10,0"
                     VerticalAlignment="Center"/>
          <ComboBox Grid.Row="1"
                    Grid.Column="1"
                    Margin="0,10,20,0"/>

          <Button Grid.Row="1"
                  Grid.Column="3"
                  Content="查询"
                  HorizontalAlignment="Right"
                  Margin="0,10,0,0"
                  Style="{StaticResource StandardButtonStyle}"/>
        </Grid>
      </Expander>

      <!-- 数据列表区域 -->
      <Grid Grid.Row="1">
        <Grid.RowDefinitions>
          <RowDefinition Height="Auto"/>
          <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                   Text="数据列表"
                   Style="{StaticResource SubHeaderTextBlockStyle}"/>

        <ListView Grid.Row="1"
                  ItemsSource="{Binding Items}"
                  BorderThickness="1"
                  BorderBrush="{StaticResource BorderBrush}"
                  Margin="0,5,0,0">
          <ListView.View>
            <GridView>
              <GridViewColumn Header="ID"
                              DisplayMemberBinding="{Binding Id}"
                              Width="60"/>
              <GridViewColumn Header="名称"
                              DisplayMemberBinding="{Binding Name}"
                              Width="200"/>
              <GridViewColumn Header="类型"
                              DisplayMemberBinding="{Binding ItemType}"
                              Width="100"/>
              <GridViewColumn Header="创建时间"
                              DisplayMemberBinding="{Binding CreatedAt, StringFormat={}{0:yyyy-MM-dd HH:mm}}"
                              Width="150"/>
              <GridViewColumn Header="启用"
                              Width="60">
                <GridViewColumn.CellTemplate>
                  <DataTemplate>
                    <CheckBox IsChecked="{Binding IsEnabled}"
                              IsEnabled="False"/>
                  </DataTemplate>
                </GridViewColumn.CellTemplate>
              </GridViewColumn>
              <GridViewColumn Header="操作"
                              Width="120">
                <GridViewColumn.CellTemplate>
                  <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                      <Button Content="编辑"
                              Margin="0,0,5,0"
                              Padding="5,2"/>
                      <Button Content="删除"
                              Padding="5,2"/>
                    </StackPanel>
                  </DataTemplate>
                </GridViewColumn.CellTemplate>
              </GridViewColumn>
            </GridView>
          </ListView.View>
        </ListView>

        <!-- 加载指示器 -->
        <Grid Grid.Row="1"
              Background="#80FFFFFF"
              Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
          <StackPanel VerticalAlignment="Center"
                      HorizontalAlignment="Center">
            <ProgressBar IsIndeterminate="True"
                         Width="200"
                         Height="10"/>
            <TextBlock Text="正在加载数据..."
                       Margin="0,10,0,0"
                       HorizontalAlignment="Center"/>
          </StackPanel>
        </Grid>

        <!-- 空数据提示 -->
        <TextBlock Grid.Row="1"
                   Text="暂无数据"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   Visibility="{Binding HasNoData, Converter={StaticResource BooleanToVisibilityConverter}}"/>
      </Grid>
    </Grid>

    <!-- 底部区域 -->
    <Border Grid.Row="2"
            Background="{StaticResource ResourceKey=BackgroundSecondary}"
            Padding="15">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <TextBlock Text="{Binding StatusMessage}"
                   VerticalAlignment="Center"/>

        <StackPanel Grid.Column="1"
                    Orientation="Horizontal">
          <Button Content="新建"
                  Command="{Binding CreateCommand}"
                  Style="{StaticResource StandardButtonStyle}"/>
          <Button Content="保存"
                  Command="{Binding SaveCommand}"
                  Style="{StaticResource StandardButtonStyle}"/>
        </StackPanel>
      </Grid>
    </Border>
  </Grid>
</UserControl> 