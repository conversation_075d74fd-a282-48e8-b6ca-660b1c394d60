using System;
using System.IO;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Common;

/// <summary>
/// 配置路径管理器 - 集中管理所有配置相关路径
/// </summary>
public static class ConfigPaths
{
    #region 模板

    /// <summary>
    /// 应用程序配置目录路径【模板】
    /// </summary>
    public static readonly string AppConfigDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs");

    /// <summary>
    /// Config.ini配置文件原始路径【模板】
    /// </summary>
    public static readonly string ConfigIniSource = Path.Combine(AppConfigDir, "Config.ini");

    #endregion 模板

    /// <summary>
    /// 工作目录中的Configs目录
    /// </summary>
    public static string WorkingConfigDir => Path.Combine(Golbal.WorkRootPath, "Configs");

    /// <summary>
    /// 工作目录中的Config.ini路径
    /// </summary>
    public static string WorkingConfigIni => Path.Combine(WorkingConfigDir, "Config.ini");

    /// <summary>
    /// 工作目录中的IR400配方路径
    /// </summary>
    public static string WorkingIR400RecipeNames => Path.Combine(WorkingConfigDir, "Recipe", "IR400RecipeNames.json");

    /// <summary>
    /// Log4net配置文件在工作目录中的路径
    /// </summary>
    public static string WorkingLog4netConfig => Path.Combine(WorkingConfigDir, "Log4netConfig", "log4net.config");

    /// <summary>
    /// SS200 InterLock配置工作路径
    /// </summary>
    public static string WorkingSS200InterLockConfig => Path.Combine(WorkingConfigDir, "SS200");

    /// <summary>
    /// Log4net配置文件在应用程序目录中的路径
    /// </summary>
    public static string AppLog4netConfig => Path.Combine(AppConfigDir, "Log4netConfig", "log4net.config");

    /// <summary>
    /// 日志目录路径
    /// </summary>
    public static string LogsDir => Path.Combine(Golbal.WorkRootPath, "Logs");

    /// <summary>
    /// 获取工作目录中的配置文件路径
    /// </summary>
    /// <param name="relativePath">相对于Configs目录的路径，例如"AlarmInfo/MotorAlarmInfo.json"</param>
    /// <returns>工作目录中的完整配置文件路径</returns>
    public static string GetWorkingConfigFilePath(string relativePath)
    {
        // 处理前导的"Configs/"，如果有的话
        if (relativePath.StartsWith("Configs/", StringComparison.OrdinalIgnoreCase))
        {
            relativePath = relativePath.Substring(8);
        }

        // 返回相对于工作目录的路径
        return Path.Combine(WorkingConfigDir, relativePath);
    }
}