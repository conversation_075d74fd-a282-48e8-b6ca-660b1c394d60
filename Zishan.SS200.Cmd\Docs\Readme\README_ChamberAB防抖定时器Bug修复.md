# ChamberA和ChamberB防抖定时器Bug修复说明

## 🐛 Bug描述

**问题现象**：
- 添加了ChamberB状态更新后，ChamberA对应的MCU IO修改不起作用了
- ChamberB对应的MCU IO修改却都起作用了
- 应该一一对应，但实际上存在相互干扰

## 🔍 根本原因分析

经过深度分析，发现问题的根本原因是**ChamberA和ChamberB使用了共享的防抖定时器**，导致状态更新相互干扰。

### 问题代码分析

#### 1. 共享防抖定时器
```csharp
/// <summary>
/// Chamber状态更新防抖定时器，避免频繁更新
/// </summary>
private readonly DispatcherTimer _chamberStatusUpdateTimer; // ← 问题：共享定时器
```

#### 2. 事件处理逻辑缺陷
```csharp
private void OnChamberCoilPropertyChanged(object sender, PropertyChangedEventArgs e)
{
    if (e.PropertyName == nameof(ModbusCoil.Coilvalue))
    {
        var coil = sender as ModbusCoil;
        if (coil != null && (coil.DeviceType == EnuMcuDeviceType.ChamberA || coil.DeviceType == EnuMcuDeviceType.ChamberB))
        {
            // 问题：无论是ChamberA还是ChamberB的变化，都使用同一个定时器
            _chamberStatusUpdateTimer.Stop();  // ← 会覆盖之前的更新请求
            _chamberStatusUpdateTimer.Start(); // ← 重新开始计时
        }
    }
}
```

#### 3. 更新方法问题
```csharp
_chamberStatusUpdateTimer.Tick += (sender, e) =>
{
    _chamberStatusUpdateTimer.Stop();
    UpdateChamberSubsystemStatus(true); // ← 总是同时更新ChamberA和ChamberB
};
```

### 问题发生机制

1. **ChamberA IO变化** → 启动共享定时器 → 500ms后准备更新
2. **ChamberB IO在500ms内变化** → 重新启动共享定时器（覆盖ChamberA的更新请求）
3. **最终结果** → 只有ChamberB的变化触发更新，ChamberA的变化被忽略

## 🛠️ 修复方案

### 1. 独立防抖定时器

**修复前**：
```csharp
private readonly DispatcherTimer _chamberStatusUpdateTimer;
```

**修复后**：
```csharp
/// <summary>
/// ChamberA状态更新防抖定时器，避免频繁更新
/// </summary>
private readonly DispatcherTimer _chamberAStatusUpdateTimer;

/// <summary>
/// ChamberB状态更新防抖定时器，避免频繁更新
/// </summary>
private readonly DispatcherTimer _chamberBStatusUpdateTimer;
```

### 2. 独立初始化逻辑

**修复前**：
```csharp
_chamberStatusUpdateTimer = new DispatcherTimer();
_chamberStatusUpdateTimer.Interval = TimeSpan.FromMilliseconds(500);
_chamberStatusUpdateTimer.Tick += (sender, e) =>
{
    _chamberStatusUpdateTimer.Stop();
    UpdateChamberSubsystemStatus(true); // 同时更新两个Chamber
};
```

**修复后**：
```csharp
// 初始化ChamberA状态更新防抖定时器
_chamberAStatusUpdateTimer = new DispatcherTimer();
_chamberAStatusUpdateTimer.Interval = TimeSpan.FromMilliseconds(500);
_chamberAStatusUpdateTimer.Tick += (sender, e) =>
{
    _chamberAStatusUpdateTimer.Stop();
    UpdateChamberASubsystemStatus(); // 只更新ChamberA
};

// 初始化ChamberB状态更新防抖定时器
_chamberBStatusUpdateTimer = new DispatcherTimer();
_chamberBStatusUpdateTimer.Interval = TimeSpan.FromMilliseconds(500);
_chamberBStatusUpdateTimer.Tick += (sender, e) =>
{
    _chamberBStatusUpdateTimer.Stop();
    UpdateChamberBSubsystemStatus(); // 只更新ChamberB
};
```

### 3. 独立更新方法

**新增方法**：
```csharp
/// <summary>
/// 更新ChamberA子系统状态（独立更新，避免与ChamberB相互干扰）
/// </summary>
private void UpdateChamberASubsystemStatus()
{
    try
    {
        _logger?.Debug("开始更新ChamberA子系统状态（由ChamberA DI/DO值变化自动触发）");
        
        // 只更新ChamberA状态
        UpdateSingleChamberStatus(EnuMcuDeviceType.ChamberA, ChamberASubsystemStatus);
        
        _logger?.Debug("ChamberA子系统状态已更新");
    }
    catch (Exception ex)
    {
        _logger?.Error($"更新ChamberA子系统状态时发生错误: {ex.Message}", ex);
    }
    
    UpdateStatusProperties();
}

/// <summary>
/// 更新ChamberB子系统状态（独立更新，避免与ChamberA相互干扰）
/// </summary>
private void UpdateChamberBSubsystemStatus()
{
    try
    {
        _logger?.Debug("开始更新ChamberB子系统状态（由ChamberB DI/DO值变化自动触发）");
        
        // 只更新ChamberB状态
        UpdateSingleChamberStatus(EnuMcuDeviceType.ChamberB, ChamberBSubsystemStatus);
        
        _logger?.Debug("ChamberB子系统状态已更新");
    }
    catch (Exception ex)
    {
        _logger?.Error($"更新ChamberB子系统状态时发生错误: {ex.Message}", ex);
    }
    
    UpdateStatusProperties();
}
```

### 4. 智能事件处理

**修复前**：
```csharp
if (coil != null && (coil.DeviceType == EnuMcuDeviceType.ChamberA || coil.DeviceType == EnuMcuDeviceType.ChamberB))
{
    // 使用共享定时器
    _chamberStatusUpdateTimer.Stop();
    _chamberStatusUpdateTimer.Start();
}
```

**修复后**：
```csharp
if (coil != null)
{
    // 根据设备类型触发对应的防抖定时器，确保ChamberA和ChamberB独立更新
    if (coil.DeviceType == EnuMcuDeviceType.ChamberA)
    {
        _chamberAStatusUpdateTimer.Stop();
        _chamberAStatusUpdateTimer.Start();
        _logger?.Debug($"检测到ChamberA线圈值变化: {coil.IoCode} = {coil.Coilvalue}");
    }
    else if (coil.DeviceType == EnuMcuDeviceType.ChamberB)
    {
        _chamberBStatusUpdateTimer.Stop();
        _chamberBStatusUpdateTimer.Start();
        _logger?.Debug($"检测到ChamberB线圈值变化: {coil.IoCode} = {coil.Coilvalue}");
    }
}
```

## ✅ 修复效果

### 修复前的问题流程
```
ChamberA IO变化 → 启动共享定时器 → 等待500ms
    ↓ (在500ms内)
ChamberB IO变化 → 重启共享定时器 → 覆盖ChamberA的更新
    ↓ (500ms后)
只更新ChamberB，ChamberA的变化被忽略 ❌
```

### 修复后的正确流程
```
ChamberA IO变化 → 启动ChamberA专用定时器 → 500ms后更新ChamberA ✅
    ↓ (同时)
ChamberB IO变化 → 启动ChamberB专用定时器 → 500ms后更新ChamberB ✅
```

## 🎯 验证方法

### 1. 日志验证
修复后，您应该能在日志中看到：
```
[DEBUG] 检测到ChamberA线圈值变化: PDI12 = True
[DEBUG] 开始更新ChamberA子系统状态（由ChamberA DI/DO值变化自动触发）
[DEBUG] ChamberA子系统状态已更新

[DEBUG] 检测到ChamberB线圈值变化: PDI12 = False  
[DEBUG] 开始更新ChamberB子系统状态（由ChamberB DI/DO值变化自动触发）
[DEBUG] ChamberB子系统状态已更新
```

### 2. 功能验证
- 修改ChamberA的IO值 → 只有ChamberA状态更新
- 修改ChamberB的IO值 → 只有ChamberB状态更新
- 同时修改两者 → 两者都独立更新，互不干扰

## 📋 修改文件清单

1. **`ViewModels\Dock\RobotStatusPanelViewModel.cs`**
   - 修改防抖定时器字段定义
   - 修改构造函数中的定时器初始化
   - 新增独立的更新方法
   - 修改事件处理逻辑

## 🎉 总结

这个Bug的修复解决了ChamberA和ChamberB状态更新相互干扰的问题，确保了：

1. **独立性**：ChamberA和ChamberB的IO变化独立处理
2. **准确性**：每个设备的IO变化都能正确触发对应的状态更新
3. **性能**：保持了防抖机制，避免频繁更新
4. **可维护性**：代码结构更清晰，逻辑更明确

现在ChamberA和ChamberB的MCU IO修改应该能够一一对应地正确工作了！
