﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Models.History
{
    /// <summary>
    /// Wafer被机械手抓取、做工艺历史记录
    /// </summary>
    [SugarTable("ArmFetchProcesHistory", TableDescription = "Wafer被机械手抓取历史记录")]
    public class ArmFetchProcesHistory
    {
        public ArmFetchProcesHistory()
        { }

        public ArmFetchProcesHistory(int recipeId)
        {
            RecipeId = recipeId;
        }

        /// <summary>
        /// ID号
        /// </summary>
        [SugarColumn(IsIdentity = true, IsPrimaryKey = true, ColumnDescription = "ID号")]
        public int Id { get; set; }

        /// <summary>
        /// 配方ID号，包含配方基本信息：SN条码等，Top、Bottom及Slot区间等
        /// </summary>
        [SugarColumn(ColumnDescription = "配方ID号，包含配方基本信息：SN条码等，Top、Bottom及Slot区间等")]
        public int RecipeId { get; set; }

        /// <summary>
        /// 左边Slot号，对应Cassette中的Slot号
        /// </summary>
        [SugarColumn(ColumnDescription = "左边Slot号，对应Cassette中的Slot号", IsNullable = true)]
        public int SlotLeft { get; set; }

        /// <summary>
        /// 右边Slot号，对应Cassette中的Slot号
        /// </summary>
        [SugarColumn(ColumnDescription = "右边Slot号，对应Cassette中的Slot号", IsNullable = true)]
        public int SlotRight { get; set; }

        /// <summary>
        /// 机械手抓取的手臂
        /// </summary>
        [SugarColumn(ColumnDescription = "机械手抓取的手臂")]
        public string ArmFetchSide { get; set; }

        /// <summary>
        /// 从哪个腔室抓取
        /// </summary>
        [SugarColumn(ColumnDescription = "从哪个腔室抓取")]
        public string FromChamberName { get; set; }

        /// <summary>
        /// 到哪个腔室放置
        /// </summary>
        [SugarColumn(ColumnDescription = "到哪个腔室放置")]
        public string ToChamberName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [SugarColumn(ColumnDescription = "开始时间", IsNullable = true)]
        public DateTime? FetchStartDateTime { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [SugarColumn(ColumnDescription = "结束时间", IsNullable = true)]
        public DateTime? FetchEndDateTime { get; set; }

        /// <summary>
        /// 取放时间差
        /// </summary>
        [SugarColumn(ColumnDescription = "取放时间差", IsNullable = true)]
        public TimeSpan? FetchDuration
        {
            get { return FetchStartDateTime.HasValue && FetchEndDateTime.HasValue ? FetchEndDateTime.Value.Subtract(FetchStartDateTime.Value) : null; }
            set { _FetchDuration = value; }
        }
        private TimeSpan? _FetchDuration;

        /// <summary>
        /// 工艺腔体类型：CH普通腔体，CP冷却腔体
        /// </summary>
        [SugarColumn(ColumnDescription = "工艺腔体类型：CH普通腔体，CP冷却腔体", IsNullable = true)]
        public string ProcessChamberType { get; set; }

        /// <summary>
        /// 工艺步骤
        /// </summary>
        [SugarColumn(ColumnDescription = "工艺步骤", IsNullable = true)]
        public string ProcessStep { get; set; }

        /// <summary>
        /// 工艺开始时间
        /// </summary>
        [SugarColumn(ColumnDescription = "工艺开始时间", IsNullable = true)]
        public DateTime? ProcessStartTime { get; set; }

        /// <summary>
        /// 工艺结束时间
        /// </summary>
        [SugarColumn(ColumnDescription = "工艺结束时间", IsNullable = true)]
        public DateTime? ProcessEndTime { get; set; }

        /// <summary>
        /// 工艺处理时间差
        /// </summary>
        [SugarColumn(ColumnDescription = "工艺处理时间差", IsNullable = true)]
        public TimeSpan? ProcessDuration
        {
            get { return ProcessStartTime.HasValue && ProcessEndTime.HasValue ? ProcessEndTime.Value.Subtract(ProcessStartTime.Value) : null; }
            set { _ProcessDuration = value; }
        }
        private TimeSpan? _ProcessDuration;

        /// <summary>
        /// 工艺结果
        /// </summary>
        [SugarColumn(ColumnDescription = "工艺结果", IsNullable = true)]
        public string ProcessResult { get; set; }

        #region 扩展字段

        /// <summary>
        /// 是否最新，最新一条设置为‘Y’，其它都为‘N’
        /// </summary>
        [SugarColumn(ColumnDescription = "是否有效")]
        public string Valid { get; set; } = "Y";

        /// <summary>
        /// 本地电脑IP地址
        /// </summary>
        [SugarColumn(ColumnDescription = "本地电脑IP地址", IsNullable = true)]
        public String ClientIp { get; set; } = "127.0.0.1";

        /// <summary>
        /// 客户端厂商自定义设备名，例如：A2301_某某1、A2302_某某2、A2303_某某3
        /// </summary>
        [SugarColumn(ColumnDescription = "客户端厂商自定义设备名，例如：A2301_某某1、A2302_某某2、A2303_某某3", IsNullable = true)]
        public String ClientName { get; set; } = "";

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(ColumnDescription = "排序号", IsNullable = true)]
        public int? SortCode { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [SugarColumn(ColumnDescription = "备注信息", IsNullable = true)]
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", InsertServerTime = true)]
        public DateTime CreateTime { get; set; }

        #endregion 扩展字段

        public void ClearFetchInfor()
        {
            ArmFetchSide = string.Empty;
            FromChamberName = string.Empty;
            ToChamberName = string.Empty;
            FetchStartDateTime = null;
            FetchEndDateTime = null;
            FetchDuration = null;
        }

        public void ClearProcessInfor()
        {
            ProcessChamberType = string.Empty;
            ProcessStep = string.Empty;
            ProcessStartTime = null;
            ProcessEndTime = null;
            ProcessDuration = null;
        }
    }
}