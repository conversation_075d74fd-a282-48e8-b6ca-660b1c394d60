# 循环执行功能说明

## 概述

为Pin Search测试和晶圆搬运操作添加了循环执行功能，支持设定执行次数或无限循环，方便进行批量测试和长时间运行验证。

## 功能特性

### 1. 循环次数设置
- **默认值**: 1（执行一次）
- **无限循环**: -1
- **有限循环**: 任何正整数（如：5表示执行5次）

### 2. 支持的操作
- **Pin Search测试**: 支持循环执行Smooth端和Nose端的Pin Search操作
- **晶圆搬运**: 支持循环执行指定路径的晶圆搬运操作

### 3. 循环控制
- **停止循环**: 提供停止按钮，可随时中断正在执行的循环
- **失败处理**: 搬运失败时询问是否继续执行剩余循环
- **状态显示**: 实时显示当前循环进度

## 使用方法

### 1. 设置循环次数
在"循环次数"文本框中输入：
- `1`: 执行一次（默认）
- `5`: 执行5次
- `-1`: 无限循环

### 2. 执行Pin Search测试
1. 设置循环次数
2. 点击"Pin Search测试"按钮
3. 确认安全提示对话框
4. 系统将按设定次数循环执行测试

### 3. 执行晶圆搬运
1. 设置循环次数
2. 配置搬运参数（From/To位置、SLOT等）
3. 点击"搬运"按钮
4. 系统将按设定次数循环执行搬运

### 4. 停止循环
- 点击"停止循环"按钮可随时中断正在执行的循环操作
- 系统会安全地停止当前循环并显示已完成的次数

## 日志记录

### 循环信息显示
```
开始执行机器人 PinSearch 测试 - 循环模式: 5次
=== 第1/5次 PinSearch 测试开始 ===
  PinSearch结果数据已清零
  执行Smooth端 PinSearch命令
  ...
=== 第1/5次 PinSearch 测试完成 ===
等待2秒后开始下一次循环...
=== 第2/5次 PinSearch 测试开始 ===
  ...
```

### 完成信息
```
PinSearch 测试全部完成 (共执行5次)
```

### 取消信息
```
用户请求停止循环执行
PinSearch 测试已取消 (已完成2次)
```

## 安全特性

### 1. 确认对话框
- 执行前显示安全确认对话框
- 包含循环次数信息
- 用户可选择取消操作

### 2. 失败处理
- 搬运失败时询问是否继续
- 用户可选择停止或继续剩余循环
- 避免因单次失败导致整个循环中断

### 3. 资源管理
- 自动清理取消令牌源
- 正确处理异步操作的取消
- 确保UI状态正确更新

## 技术实现

### 1. 循环控制逻辑
```csharp
// 初始化循环控制变量
int currentLoop = 0;
bool isInfiniteLoop = LoopCount == -1;
int totalLoops = isInfiniteLoop ? int.MaxValue : LoopCount;

// 创建取消令牌源
_cancellationTokenSource = new CancellationTokenSource();

// 循环执行
while (currentLoop < totalLoops && !_cancellationTokenSource.Token.IsCancellationRequested)
{
    currentLoop++;
    // 执行具体操作...
    
    // 等待间隔
    if (currentLoop < totalLoops)
    {
        await Task.Delay(2000, _cancellationTokenSource.Token);
    }
}
```

### 2. 停止循环命令
```csharp
[RelayCommand]
private void StopLoop()
{
    if (_cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested)
    {
        _cancellationTokenSource.Cancel();
        UILogService.AddWarningLog("用户请求停止循环执行");
    }
}
```

## 注意事项

1. **无限循环**: 使用-1设置无限循环时，请确保有明确的停止条件或手动停止
2. **资源占用**: 长时间循环可能占用较多系统资源，建议合理设置循环间隔
3. **设备状态**: 循环执行前请确保设备连接正常且处于可操作状态
4. **安全操作**: 执行循环操作前请确认机械臂周围安全，避免碰撞风险

## 更新历史

- **v1.0**: 初始版本，支持Pin Search和搬运的循环执行
- 添加循环次数设置文本框
- 添加停止循环按钮
- 实现循环控制逻辑和安全确认机制
