<UserControl
    x:Class="Zishan.SS200.Cmd.Views.Dock.ModbusDICoilsPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:designViewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels.Dock.DesignViewModels"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd.Views.Dock"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    d:DesignHeight="800"
    d:DesignWidth="1000"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="White"
    mc:Ignorable="d">

    <d:UserControl.DataContext>
        <designViewModels:ModbusDICoilsPanelDesignViewModel />
    </d:UserControl.DataContext>

    <UserControl.Resources>
        <ResourceDictionary>

            <!--  现有的资源定义  -->
            <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
            <converters:BoolToStateConverter x:Key="BoolToStateConverter" />

            <!--  现代化的面板样式  -->
            <Style x:Key="ModernExpanderStyle" TargetType="Expander">
                <Setter Property="Margin" Value="0,0,0,10" />
                <Setter Property="Background" Value="White" />
                <Setter Property="BorderBrush" Value="#E0E0E0" />
                <Setter Property="BorderThickness" Value="1" />
            </Style>

            <!--  状态文本样式  -->
            <Style x:Key="StatusTextStyle" TargetType="TextBlock">
                <Setter Property="Margin" Value="5,5,5,10" />
                <Setter Property="TextWrapping" Value="Wrap" />
                <Setter Property="FontSize" Value="12" />
            </Style>

            <!--  现代化的线圈指示器样式  -->
            <Style x:Key="ModernCoilIndicatorStyle" TargetType="Ellipse">
                <Setter Property="Width" Value="16" />
                <Setter Property="Height" Value="16" />
                <Setter Property="Margin" Value="3" />
                <Setter Property="Stroke" Value="#BDBDBD" />
                <Setter Property="StrokeThickness" Value="1.5" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Coilvalue}" Value="True">
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect
                                    BlurRadius="8"
                                    ShadowDepth="0"
                                    Color="#4CAF50" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <!--  现代化的边框样式  -->
            <Style x:Key="ModernBorderStyle" TargetType="Border">
                <Setter Property="Width" Value="160" />
                <Setter Property="Height" Value="80" />
                <Setter Property="Margin" Value="4" />
                <Setter Property="Padding" Value="8" />
                <Setter Property="Background" Value="White" />
                <Setter Property="BorderBrush" Value="#E0E0E0" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="CornerRadius" Value="4" />
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect
                            BlurRadius="4"
                            ShadowDepth="1"
                            Color="#20000000" />
                    </Setter.Value>
                </Setter>
            </Style>

            <!--  地址文本样式  -->
            <Style x:Key="AddressTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="11" />
                <Setter Property="Foreground" Value="#757575" />
                <Setter Property="Margin" Value="0,0,0,2" />
            </Style>

            <!--  标题文本样式  -->
            <Style x:Key="TitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12" />
                <Setter Property="TextTrimming" Value="CharacterEllipsis" />
                <Setter Property="TextWrapping" Value="Wrap" />
                <Setter Property="Margin" Value="0,0,0,4" />
                <Setter Property="FontWeight" Value="Medium" />
            </Style>

            <!--  状态文本样式  -->
            <Style x:Key="StateTextStyle" TargetType="TextBlock">
                <Setter Property="Margin" Value="5,0,0,0" />
                <Setter Property="FontSize" Value="11" />
                <Setter Property="VerticalAlignment" Value="Center" />
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="12">
                <!--  Shuttle设备DI  -->
                <Expander
                    Header="Shuttle设备DI"
                    IsExpanded="True"
                    Style="{StaticResource ModernExpanderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  状态提示  -->
                        <TextBlock Grid.Row="0">
                            <TextBlock.Style>
                                <Style BasedOn="{StaticResource StatusTextStyle}" TargetType="TextBlock">
                                    <Setter Property="Text" Value="设备支持DI传感器读取，正在监控中..." />
                                    <Setter Property="Foreground" Value="#4CAF50" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsShuttleCoilReadingSupported}" Value="False">
                                            <Setter Property="Text" Value="设备不支持DI传感器读取功能，已禁用DI监控" />
                                            <Setter Property="Foreground" Value="#F44336" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <!--  DI列表  -->
                        <ItemsControl Grid.Row="1" ItemsSource="{Binding McuCmdService.ShuttleInputCoils}">
                            <ItemsControl.Style>
                                <Style TargetType="ItemsControl">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsShuttleCoilReadingSupported}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ItemsControl.Style>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource ModernBorderStyle}" ToolTip="{Binding Description}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <TextBlock
                                                Grid.Row="0"
                                                Style="{StaticResource AddressTextStyle}"
                                                Text="{Binding Address, StringFormat={}DI_{0}}" />

                                            <TextBlock
                                                Grid.Row="1"
                                                Style="{StaticResource TitleTextStyle}"
                                                Text="{Binding Title}" />

                                            <StackPanel
                                                Grid.Row="2"
                                                HorizontalAlignment="Center"
                                                Orientation="Horizontal">
                                                <Ellipse Style="{StaticResource ModernCoilIndicatorStyle}">
                                                    <Ellipse.Fill>
                                                        <SolidColorBrush Color="{Binding Coilvalue, Converter={StaticResource BoolToColorConverter}}" />
                                                    </Ellipse.Fill>
                                                </Ellipse>
                                                <TextBlock Style="{StaticResource StateTextStyle}" Text="{Binding Coilvalue, Converter={StaticResource BoolToStateConverter}}" />
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Grid>
                </Expander>

                <!--  Robot设备DI  -->
                <Expander
                    Header="Robot设备DI"
                    IsExpanded="True"
                    Style="{StaticResource ModernExpanderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  状态提示  -->
                        <TextBlock Grid.Row="0">
                            <TextBlock.Style>
                                <Style BasedOn="{StaticResource StatusTextStyle}" TargetType="TextBlock">
                                    <Setter Property="Text" Value="设备支持DI传感器读取，正在监控中..." />
                                    <Setter Property="Foreground" Value="#4CAF50" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsRobotCoilReadingSupported}" Value="False">
                                            <Setter Property="Text" Value="设备不支持DI传感器读取功能，已禁用DI监控" />
                                            <Setter Property="Foreground" Value="#F44336" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <!--  DI列表  -->
                        <ItemsControl Grid.Row="1" ItemsSource="{Binding McuCmdService.RobotInputCoils}">
                            <ItemsControl.Style>
                                <Style TargetType="ItemsControl">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsRobotCoilReadingSupported}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ItemsControl.Style>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource ModernBorderStyle}" ToolTip="{Binding Description}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <TextBlock
                                                Grid.Row="0"
                                                Style="{StaticResource AddressTextStyle}"
                                                Text="{Binding Address, StringFormat={}DI_{0}}" />

                                            <TextBlock
                                                Grid.Row="1"
                                                Style="{StaticResource TitleTextStyle}"
                                                Text="{Binding Title}" />

                                            <StackPanel
                                                Grid.Row="2"
                                                HorizontalAlignment="Center"
                                                Orientation="Horizontal">
                                                <Ellipse Style="{StaticResource ModernCoilIndicatorStyle}">
                                                    <Ellipse.Fill>
                                                        <SolidColorBrush Color="{Binding Coilvalue, Converter={StaticResource BoolToColorConverter}}" />
                                                    </Ellipse.Fill>
                                                </Ellipse>
                                                <TextBlock Style="{StaticResource StateTextStyle}" Text="{Binding Coilvalue, Converter={StaticResource BoolToStateConverter}}" />
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Grid>
                </Expander>

                <!--  CHA设备DI  -->
                <Expander
                    Header="CHA设备DI"
                    IsExpanded="True"
                    Style="{StaticResource ModernExpanderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  状态提示  -->
                        <TextBlock Grid.Row="0">
                            <TextBlock.Style>
                                <Style BasedOn="{StaticResource StatusTextStyle}" TargetType="TextBlock">
                                    <Setter Property="Text" Value="设备支持DI传感器读取，正在监控中..." />
                                    <Setter Property="Foreground" Value="#4CAF50" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsChaCoilReadingSupported}" Value="False">
                                            <Setter Property="Text" Value="设备不支持DI传感器读取功能，已禁用DI监控" />
                                            <Setter Property="Foreground" Value="#F44336" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <!--  DI列表  -->
                        <ItemsControl Grid.Row="1" ItemsSource="{Binding McuCmdService.ChaInputCoils}">
                            <ItemsControl.Style>
                                <Style TargetType="ItemsControl">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsChaCoilReadingSupported}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ItemsControl.Style>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource ModernBorderStyle}" ToolTip="{Binding Description}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <TextBlock
                                                Grid.Row="0"
                                                Style="{StaticResource AddressTextStyle}"
                                                Text="{Binding Address, StringFormat={}DI_{0}}" />

                                            <TextBlock
                                                Grid.Row="1"
                                                Style="{StaticResource TitleTextStyle}"
                                                Text="{Binding Title}" />

                                            <StackPanel
                                                Grid.Row="2"
                                                HorizontalAlignment="Center"
                                                Orientation="Horizontal">
                                                <Ellipse Style="{StaticResource ModernCoilIndicatorStyle}">
                                                    <Ellipse.Fill>
                                                        <SolidColorBrush Color="{Binding Coilvalue, Converter={StaticResource BoolToColorConverter}}" />
                                                    </Ellipse.Fill>
                                                </Ellipse>
                                                <TextBlock Style="{StaticResource StateTextStyle}" Text="{Binding Coilvalue, Converter={StaticResource BoolToStateConverter}}" />
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Grid>
                </Expander>

                <!--  CHB设备DI  -->
                <Expander
                    Header="CHB设备DI"
                    IsExpanded="True"
                    Style="{StaticResource ModernExpanderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  状态提示  -->
                        <TextBlock Grid.Row="0">
                            <TextBlock.Style>
                                <Style BasedOn="{StaticResource StatusTextStyle}" TargetType="TextBlock">
                                    <Setter Property="Text" Value="设备支持DI传感器读取，正在监控中..." />
                                    <Setter Property="Foreground" Value="#4CAF50" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsChbCoilReadingSupported}" Value="False">
                                            <Setter Property="Text" Value="设备不支持DI传感器读取功能，已禁用DI监控" />
                                            <Setter Property="Foreground" Value="#F44336" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <!--  DI列表  -->
                        <ItemsControl Grid.Row="1" ItemsSource="{Binding McuCmdService.ChbInputCoils}">
                            <ItemsControl.Style>
                                <Style TargetType="ItemsControl">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsChbCoilReadingSupported}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ItemsControl.Style>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource ModernBorderStyle}" ToolTip="{Binding Description}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <TextBlock
                                                Grid.Row="0"
                                                Style="{StaticResource AddressTextStyle}"
                                                Text="{Binding Address, StringFormat={}DI_{0}}" />

                                            <TextBlock
                                                Grid.Row="1"
                                                Style="{StaticResource TitleTextStyle}"
                                                Text="{Binding Title}" />

                                            <StackPanel
                                                Grid.Row="2"
                                                HorizontalAlignment="Center"
                                                Orientation="Horizontal">
                                                <Ellipse Style="{StaticResource ModernCoilIndicatorStyle}">
                                                    <Ellipse.Fill>
                                                        <SolidColorBrush Color="{Binding Coilvalue, Converter={StaticResource BoolToColorConverter}}" />
                                                    </Ellipse.Fill>
                                                </Ellipse>
                                                <TextBlock Style="{StaticResource StateTextStyle}" Text="{Binding Coilvalue, Converter={StaticResource BoolToStateConverter}}" />
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Grid>
                </Expander>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl> 