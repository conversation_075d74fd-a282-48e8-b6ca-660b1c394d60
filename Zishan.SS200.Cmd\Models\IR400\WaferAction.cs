﻿using System;
using System.Collections.Generic;
using Prism.Mvvm;

// using SqlSugar;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// Wafer前端动作绑定类
    /// </summary>
    public class WaferAction : BindableBase
    {
        #region 属性

        /// <summary>
        /// 腔体WaferSide：左边、右边
        /// </summary>
        public EnuChamberWaferSide ChamberWaferSide { get; set; }

        /// <summary>
        /// 腔体总容量
        /// </summary>
        public int Capacity { get; set; }

        ///// <summary>
        ///// Wafer列表
        ///// </summary>
        //public ObservableCollection<Wafer> Wafers { get => _Wafers; private init => SetProperty(ref _Wafers, value); }
        //private readonly ObservableCollection<Wafer> _Wafers;

        /// <summary>
        /// Wafer列表【带通知其它】
        /// </summary>
        public ObservableCollection<Wafer> Wafers
        {
            get => _Wafers;
            private init
            {
                if (_Wafers != null)
                {
                    _Wafers.CollectionChanged -= Wafers_CollectionChanged;
                }
                SetProperty(ref _Wafers, value);
                if (_Wafers != null)
                {
                    _Wafers.CollectionChanged += Wafers_CollectionChanged;
                }
            }
        }
        private readonly ObservableCollection<Wafer> _Wafers;

        ///// <summary>
        ///// 获取指定编号的Wafer
        ///// </summary>
        ///// <param name="waferNo"></param>
        ///// <returns></returns>
        //public Wafer this[int waferNo]
        //{
        //    get
        //    {
        //        waferNo = waferNo < 1 ? 1 : waferNo;
        //        waferNo = waferNo > 25 ? 25 : waferNo;
        //        var wafer = Wafers.FirstOrDefault(w => w.WaferNo == waferNo);
        //        if (wafer == null)
        //        {
        //            wafer = new Wafer(ChamberWaferSide, waferNo);
        //            wafer.WaferStatus = EnuWaferStatus.None;
        //        }
        //        return wafer;
        //    }
        //}

        /// <summary>
        /// 获取指定编号的Wafer【带UI通知功能】
        /// </summary>
        /// <param name="waferNo"></param>
        /// <returns></returns>
        //private Dictionary<int, Wafer> _tempWafers = new Dictionary<int, Wafer>();

        /// <summary>
        /// 获取指定编号的Wafer【带UI通知功能】
        /// </summary>
        /// <param name="waferNo">Wafer编号：1~25</param>
        /// <returns></returns>
        public Wafer this[int waferNo]
        {
            get
            {
                waferNo = waferNo < 1 ? 1 : waferNo;
                waferNo = waferNo > 25 ? 25 : waferNo;
                _selectedWafer = Wafers.FirstOrDefault(w => w.WaferNo == waferNo);
                //if (_selectedWafer == null)
                //{
                //    if (!_tempWafers.TryGetValue(waferNo, out _selectedWafer))
                //    {
                //        _selectedWafer = new Wafer(ChamberWaferSide, waferNo);
                //        _selectedWafer.WaferStatus = EnuWaferStatus.None;
                //        _tempWafers[waferNo] = _selectedWafer;
                //    }
                //}
                return _selectedWafer;
            }
            set
            {
                if (_selectedWafer != value)
                {
                    _selectedWafer = value;
                    RaisePropertyChanged($"Item[{waferNo}]");
                }
            }
        }
        private Wafer _selectedWafer;

        #region 暂时只能笨办法，用于UI界面不规则布局绑定，如果使用列表要规则或者指定Canvas，使用XY指定绝对布局，比较复杂

        /// <summary>
        /// definity
        /// </summary>
        public Wafer WaferNo1 { get => _WaferNo1; set => SetProperty(ref _WaferNo1, value); }
        private Wafer _WaferNo1;

        /// <summary>
        /// definity
        /// </summary>
        public Wafer WaferNo2 { get => _WaferNo2; set => SetProperty(ref _WaferNo2, value); }
        private Wafer _WaferNo2;

        /// <summary>
        /// definity
        /// </summary>
        public Wafer WaferNo3 { get => _WaferNo3; set => SetProperty(ref _WaferNo3, value); }
        private Wafer _WaferNo3;

        /// <summary>
        /// definity
        /// </summary>
        public Wafer WaferNo4 { get => _WaferNo4; set => SetProperty(ref _WaferNo4, value); }
        private Wafer _WaferNo4;

        public Wafer WaferNo5 { get => _WaferNo5; set => SetProperty(ref _WaferNo5, value); }
        private Wafer _WaferNo5;

        public Wafer WaferNo6 { get => _WaferNo6; set => SetProperty(ref _WaferNo6, value); }
        private Wafer _WaferNo6;

        public Wafer WaferNo7 { get => _WaferNo7; set => SetProperty(ref _WaferNo7, value); }
        private Wafer _WaferNo7;

        public Wafer WaferNo8 { get => _WaferNo8; set => SetProperty(ref _WaferNo8, value); }
        private Wafer _WaferNo8;

        public Wafer WaferNo9 { get => _WaferNo9; set => SetProperty(ref _WaferNo9, value); }
        private Wafer _WaferNo9;

        public Wafer WaferNo10 { get => _WaferNo10; set => SetProperty(ref _WaferNo10, value); }
        private Wafer _WaferNo10;

        public Wafer WaferNo11 { get => _WaferNo11; set => SetProperty(ref _WaferNo11, value); }
        private Wafer _WaferNo11;

        public Wafer WaferNo12 { get => _WaferNo12; set => SetProperty(ref _WaferNo12, value); }
        private Wafer _WaferNo12;

        public Wafer WaferNo13 { get => _WaferNo13; set => SetProperty(ref _WaferNo13, value); }
        private Wafer _WaferNo13;

        public Wafer WaferNo14 { get => _WaferNo14; set => SetProperty(ref _WaferNo14, value); }
        private Wafer _WaferNo14;

        public Wafer WaferNo15 { get => _WaferNo15; set => SetProperty(ref _WaferNo15, value); }
        private Wafer _WaferNo15;

        public Wafer WaferNo16 { get => _WaferNo16; set => SetProperty(ref _WaferNo16, value); }
        private Wafer _WaferNo16;

        public Wafer WaferNo17 { get => _WaferNo17; set => SetProperty(ref _WaferNo17, value); }
        private Wafer _WaferNo17;

        public Wafer WaferNo18 { get => _WaferNo18; set => SetProperty(ref _WaferNo18, value); }
        private Wafer _WaferNo18;

        public Wafer WaferNo19 { get => _WaferNo19; set => SetProperty(ref _WaferNo19, value); }
        private Wafer _WaferNo19;

        public Wafer WaferNo20 { get => _WaferNo20; set => SetProperty(ref _WaferNo20, value); }
        private Wafer _WaferNo20;

        public Wafer WaferNo21 { get => _WaferNo21; set => SetProperty(ref _WaferNo21, value); }
        private Wafer _WaferNo21;

        public Wafer WaferNo22 { get => _WaferNo22; set => SetProperty(ref _WaferNo22, value); }
        private Wafer _WaferNo22;

        public Wafer WaferNo23 { get => _WaferNo23; set => SetProperty(ref _WaferNo23, value); }
        private Wafer _WaferNo23;

        public Wafer WaferNo24 { get => _WaferNo24; set => SetProperty(ref _WaferNo24, value); }
        private Wafer _WaferNo24;

        public Wafer WaferNo25 { get => _WaferNo25; set => SetProperty(ref _WaferNo25, value); }
        private Wafer _WaferNo25;

        #endregion 暂时只能笨办法，用于UI界面不规则布局绑定，如果使用列表要规则或者指定Canvas，使用XY指定绝对布局，比较复杂

        /// <summary>
        /// 容器剩余Wafer数量
        /// </summary>
        public int RemainWaferCount { get => _RemainWaferCount; set => SetProperty(ref _RemainWaferCount, value); }
        private int _RemainWaferCount;

        /// <summary>
        /// 未完成Wafer数量
        /// </summary>
        public int WaferUnFinishedCount { get => _WaferUnFinishedCount; set => SetProperty(ref _WaferUnFinishedCount, value); }
        private int _WaferUnFinishedCount;

        /// <summary>
        /// 是否有Wafer
        /// </summary>
        public bool HaveWafer { get => _HaveWafer; set => SetProperty(ref _HaveWafer, value); }
        private bool _HaveWafer;

        private bool isGetWaferForBindPending = false;

        private void Wafers_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            RaisePropertyChanged(nameof(HaveWaferNo));
            RaisePropertyChanged(nameof(HaveWafer));
            RaisePropertyChanged(nameof(CurWaferCount));
            RaisePropertyChanged(nameof(CurWaferPoc));

            //索引器属性通知
            if (e.NewItems != null && e.NewItems.Count > 0 && e.NewItems[0] is Wafer wafer)
            {
                RaisePropertyChanged($"Item[{wafer.WaferNo}]");
            }

            if (!isGetWaferForBindPending)
            {
                isGetWaferForBindPending = true;
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    GetWaferForBind();
                    isGetWaferForBindPending = false;
                }));
            }

            //GetWaferForBind();
        }

        private void GetWaferForBind()
        {
            WaferNo1 = this[1];
            WaferNo2 = this[2];
            WaferNo3 = this[3];
            WaferNo4 = this[4];
            WaferNo5 = this[5];
            WaferNo6 = this[6];
            WaferNo7 = this[7];
            WaferNo8 = this[8];
            WaferNo9 = this[9];
            WaferNo10 = this[10];
            WaferNo11 = this[11];
            WaferNo12 = this[12];
            WaferNo13 = this[13];
            WaferNo14 = this[14];
            WaferNo15 = this[15];
            WaferNo16 = this[16];
            WaferNo17 = this[17];
            WaferNo18 = this[18];
            WaferNo19 = this[19];
            WaferNo20 = this[20];
            WaferNo21 = this[21];
            WaferNo22 = this[22];
            WaferNo23 = this[23];
            WaferNo24 = this[24];
            WaferNo25 = this[25];
        }

        /// <summary>
        /// 获取有Wafer编号,用于腔体晶圆号显示，只显示第一个，没有就显示0
        /// </summary>
        public int HaveWaferNo
        {
            get
            {
                if (Wafers.Any())
                {
                    return Wafers[0].WaferNo;
                }
                return 0;
            }
        }

        /// <summary>
        /// Wafer是否可移动【前提有Wafer】
        /// </summary>
        public bool IsMove { get => _IsMove; set => SetProperty(ref _IsMove, value); }
        private bool _IsMove;

        public bool IsMoveChecked { get => _IsMoveChecked; set => SetProperty(ref _IsMoveChecked, value, () => UpdateTime = DateTime.Now); }
        private bool _IsMoveChecked;

        /// <summary>
        /// Wafer是否可替换【前提没有Wafer】
        /// </summary>
        public bool IsReplace { get => _IsReplace; set => SetProperty(ref _IsReplace, value); }
        private bool _IsReplace;

        public bool IsReplaceChecked { get => _IsReplaceChecked; set => SetProperty(ref _IsReplaceChecked, value, () => UpdateTime = DateTime.Now); }
        private bool _IsReplaceChecked;

        /// <summary>
        /// Wafer是否可替换【前提没有Wafer】
        /// </summary>
        public bool IsCreate { get => _IsCreate; set => SetProperty(ref _IsCreate, value); }
        private bool _IsCreate;

        public bool IsCreateChecked { get => _IsCreateChecked; set => SetProperty(ref _IsCreateChecked, value, () => CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked); }
        private bool _IsCreateChecked;

        /// <summary>
        /// Wafer是否可删除【前提有Wafer】
        /// </summary>
        public bool IsDelete { get => _IsDelete; set => SetProperty(ref _IsDelete, value); }
        private bool _IsDelete;

        public bool IsDeleteChecked { get => _IsDeleteChecked; set => SetProperty(ref _IsDeleteChecked, value, () => CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked); }
        private bool _IsDeleteChecked;

        /// <summary>
        /// 是否可以运行创建或者删除操作
        /// </summary>
        public bool CanRunCreateOrDeleteCmd { get => _CanRunCreateOrDeleteCmd; set => SetProperty(ref _CanRunCreateOrDeleteCmd, value); }
        private bool _CanRunCreateOrDeleteCmd;

        /// <summary>
        /// 容器当前Wafer数量  Todo:，不然hc:Badge报错 ToDo:未知报错 无法在“System.Windows.Controls.ControlTemplate”的名称范围内内找到”Border“名称
        /// </summary>
        public int CurWaferCount { get => _CurWaferCount; set => SetProperty(ref _CurWaferCount, value); }
        private int _CurWaferCount;

        /// <summary>
        /// 当前Wafer完成百分比
        /// </summary>
        public double CurWaferPoc { get => _CurWaferPoc; set => SetProperty(ref _CurWaferPoc, value); }
        private double _CurWaferPoc;

        public DateTime UpdateTime { get; set; }

        #endregion 属性

        #region 构造函数

        public WaferAction(EnuChamberWaferSide chamberWaferSide, int capacity)
        {
            ChamberWaferSide = chamberWaferSide;
            Capacity = capacity;
            Wafers = new ObservableCollection<Wafer>();

            //搬运过程中，怎么更改Wafer状态?
            //Task.Run(() =>
            //{
            //    while (true)
            //    {
            //        foreach (var wafer in Wafers)
            //        {
            //            //更改Wafer状态
            //            wafer.WaferStatus = wafer.WaferStatus == EnuWaferStatus.Have ? EnuWaferStatus.AutoFinished : EnuWaferStatus.Have;
            //        }
            //        System.Threading.Thread.Sleep(1000);
            //    }
            //});
        }

        #endregion 构造函数

        #region 方法

        /// <summary>
        /// 向腔体中添加晶圆,批量加，最多2个一起加
        /// </summary>
        /// <param name="wafers"></param>
        /// <param name="messageInfo"></param>
        /// <param name="enuArmFetchSide"></param>
        public bool AddWafers(List<Wafer> wafers, out string messageInfo, EnuArmFetchSide enuArmFetchSide = EnuArmFetchSide.Unknow)
        {
            bool blResult = false;
            messageInfo = string.Empty;
            if (!IsFull())
            {
                wafers.ForEach(w => w.ArmFetchSide = enuArmFetchSide);//未知面，针对非机械臂选Unknown
                Application.Current.Dispatcher.Invoke(() =>
               {
                   Wafers.AddRange(wafers);
               });

                //foreach (var wafer in wafers)
                //{
                //    if (_tempWafers.ContainsKey(wafer.WaferNo))
                //    {
                //        _tempWafers.Remove(wafer.WaferNo);
                //        RaisePropertyChanged($"Item[{wafer.WaferNo}]");
                //    }
                //}
                CalWaferAction();
                //IsProcessed = false;
                blResult = true;
            }
            else
            {
                messageInfo = $"{ChamberWaferSide}容量{Capacity}，当前数量{Wafers.Count},容量已满了，无法放入!!!";
            }
            return blResult;
        }

        /// <summary>
        /// 从腔体中移出晶圆,有限制，最多2个一起减
        /// </summary>
        /// <returns></returns>

        public List<Wafer> RemoveWafers(EnuChamberWaferSide enuChamberWaferSide, int moveCount = 1, EnuWaferRemoveMode enuWaferRemoveMode = EnuWaferRemoveMode.Rear, Wafer waferRemove = null)
        {
            var removewafers = new List<Wafer>();
            for (int i = 0; i < moveCount && Wafers.Count > 0; i++)
            {
                Wafer wafer = null;

                switch (enuWaferRemoveMode)
                {
                    case EnuWaferRemoveMode.Front:
                        wafer = Wafers[0];
                        Wafers.RemoveAt(0);
                        break;

                    case EnuWaferRemoveMode.Rear:
                        wafer = Wafers[^1];
                        Wafers.RemoveAt(Wafers.Count - 1);
                        break;

                    case EnuWaferRemoveMode.specified:
                        wafer = waferRemove;
                        Wafers.Remove(wafer);
                        break;
                }

                if (wafer != null)
                {
                    wafer.ArmFetchSide = EnuArmFetchSide.Unknow;
                    removewafers.Add(wafer);
                    switch (enuChamberWaferSide)
                    {
                        case EnuChamberWaferSide.LeftWafers:
                            if (wafer.WaferNo == Golbal.CurLeftWafersId)//判断删除末尾的才可以
                            {
                                Golbal.CurLeftWafersId--;
                            }
                            break;

                        case EnuChamberWaferSide.RightWafers:
                            if (wafer.WaferNo == Golbal.CurRightWafersId)//判断删除末尾的才可以
                            {
                                Golbal.CurRightWafersId--;
                            }
                            break;
                    }
                    RaisePropertyChanged($"Item[{wafer.WaferNo}]");
                }
                else
                {
                    //不大可能执行到
                    MessageBox.Show("移除的Wafer为空，请确认！");
                }
            }
            CalWaferAction();
            return removewafers;
        }

        /// <summary>
        /// 从腔体中清空Wafer
        /// </summary>
        /// <returns></returns>

        public bool ClearWafers()
        {
            Wafers.Clear();
            CalWaferAction();
            return true;
        }

        /// <summary>
        /// 计算关联逻辑判断
        /// </summary>
        public void CalWaferAction()
        {
            RemainWaferCount = this.Capacity - this.Wafers.Count;
            CurWaferCount = this.Wafers.Count;

            var waferFinishedCount = Wafers.Count(w => w.IsFinisheded);
            WaferUnFinishedCount = this.Capacity - waferFinishedCount;
            CurWaferPoc = (waferFinishedCount / (1.0 * this.Capacity)) * 100;

            HaveWafer = this.Wafers.Any();
            IsMove = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以移出
            if (!IsMove) { IsMoveChecked = false; }
            IsReplace = !IsFull();//剩余容量>=2多可以移入
            if (!IsReplace) { IsReplaceChecked = false; }

            IsCreate = !IsFull();//剩余容量>=2多可以创建
            if (!IsCreate) { IsCreateChecked = false; }
            IsDelete = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以删除
            if (!IsDelete) { IsDeleteChecked = false; }

            //CurWaferCount = Wafers.Count / 2;

            //RemainWaferCount = Capacity - Wafers.Count;

            //CanRunCreateOrDeleteCmd = IsCreate || IsDelete;
            CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked;

            /*
            _LeftWaferAction.HaveWafer = Wafers.Any();
            _LeftWaferAction.JudgeByHaveWafer();

            //_RightWaferAction.HaveWafer = _RightWafer.Any();
            //_RightWaferAction.JudgeByHaveWafer();

            RemainWaferCount = Wafers.Count;
            //RightWaferCount = _RightWafer.Count();
            */
        }

        /// <summary>
        /// 检查腔体是否满了
        /// </summary>
        /// <returns></returns>
        public bool IsFull()
        {
            return Wafers.Count >= Capacity;
        }

        /// <summary>
        /// 检查腔体是否为空
        /// </summary>
        /// <returns></returns>
        public bool IsEmpty()
        {
            return Wafers.Count == 0;
        }

        /// <summary>
        /// 检查腔体是否有未完成的Wafer 【限定Recipe Top】
        /// </summary>
        /// <returns></returns>
        public virtual bool HasUnfinishedWafer()
        {
            return Wafers.Count(t => t.WaferNo <= Golbal.CurGolbalRunRecipeInfo.Top && !t.IsFinisheded) > 0;
        }

        /// <summary>
        /// 关联逻辑判断
        /// </summary>

        //public void JudgeByHaveWafer()
        //{
        //    IsMove = HaveWafer;
        //    if (!IsMove) { IsMoveChecked = false; }
        //    IsReplace = !HaveWafer;
        //    if (!IsReplace) { IsReplaceChecked = false; }

        //    IsCreate = !HaveWafer;
        //    if (!IsCreate) { IsCreateChecked = false; }
        //    IsDelete = HaveWafer;
        //    if (!IsDelete) { IsDeleteChecked = false; }

        //    CanRunCreateOrDeleteCmd = IsCreate || IsDelete;

        //    CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked;

        //    //_IsMove = HaveWafer;
        //    //_IsReplace = !HaveWafer;

        //    //IsCreate = !HaveWafer;
        //    //_IsDelete = HaveWafer;

        //    //_CanRunCreateOrDeleteCmd = IsCreate || IsDelete;
        //}

        public override string ToString()
        {
            return $"Wafer数量：{CurWaferCount}，分别为：{string.Join(",", Wafers)}";
        }

        #endregion 方法
    }
}