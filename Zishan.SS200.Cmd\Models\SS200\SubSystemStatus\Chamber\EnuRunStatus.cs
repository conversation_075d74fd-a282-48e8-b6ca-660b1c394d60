using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// 运行状态枚举
    /// </summary>
    public enum EnuRunStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 忙碌A状态 (MPS3A) - 当前chamber slit door动作中，或者robot于chamber交互中
        /// </summary>
        [Description("忙碌A")]
        BusyA = 1,

        /// <summary>
        /// 忙碌B状态 (MPS3B) - lift pin动作中
        /// </summary>
        [Description("忙碌B")]
        BusyB = 2,

        /// <summary>
        /// 空闲状态 (MPS4) - slit door/lift pin不在动作中，或者不在与robot交互中
        /// </summary>
        [Description("空闲")]
        Idle = 3,

        /// <summary>
        /// 处理状态 (MPS5) - recipe running，RF on
        /// </summary>
        [Description("处理中")]
        Processing = 4
    }
}
