using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Config.SS200.AlarmCode.Robot;
using Zishan.SS200.Cmd.Config.SS200.AlarmCode.ChamberA;

// using Zishan.SS200.Cmd.Config.SS200.AlarmCode.ChamberB; // ChamberB使用与ChamberA相同的类
using Zishan.SS200.Cmd.Config.SS200.AlarmCode.Shuttle;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberA;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberB;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Shuttle;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.MainSystem;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle;

namespace Zishan.SS200.Cmd.Services
{
    // 注意：使用项目中已有的AlarmItem和ConfigureSetting类，避免重复定义

    /// <summary>
    /// SS200配置验证器
    /// 验证SS200InterLockMain中读取到的数据是否与JSON配置文件一致
    /// </summary>
    public class SS200ConfigurationValidator
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(SS200ConfigurationValidator));

        private readonly string _configBasePath = Path.Combine(ConfigPaths.WorkingSS200InterLockConfig);
        private readonly string _exportBasePath = Path.Combine(ConfigPaths.WorkingSS200InterLockConfig, "Exported");

        /// <summary>
        /// 验证结果类
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public List<string> Errors { get; set; } = new List<string>();
            public List<string> Warnings { get; set; } = new List<string>();
            public List<string> InfoMessages { get; set; } = new List<string>();

            public string GetSummary()
            {
                var summary = $"验证结果: {(IsValid ? "✅ 通过" : "❌ 失败")}\n";
                if (Errors.Any())
                {
                    summary += $"错误 ({Errors.Count}):\n" + string.Join("\n", Errors.Select(e => $"  • {e}")) + "\n";
                }
                if (Warnings.Any())
                {
                    summary += $"警告 ({Warnings.Count}):\n" + string.Join("\n", Warnings.Select(w => $"  • {w}")) + "\n";
                }
                if (InfoMessages.Any())
                {
                    summary += $"信息 ({InfoMessages.Count}):\n" + string.Join("\n", InfoMessages.Select(i => $"  • {i}"));
                }
                return summary;
            }
        }

        /// <summary>
        /// 程序启动时自动验证，结果会显示在消息框中：有异常显示，或强制显示
        /// </summary>
        public ValidationResult ValidateOnStartup(bool blAlwaysShowResult = false)
        {
            _logger.Info("开始启动时配置验证...");

            try
            {
                // 确保导出目录存在
                Directory.CreateDirectory(_exportBasePath);

                // 导出当前数据为JSON文件
                ExportCurrentDataToJson();

                // 验证AlarmCode和SubsystemConfigure
                var result = ValidateAll();

                // 记录验证结果到日志
                LogValidationResult(result);

                // 显示验证结果消息框：有异常显示，或强制显示
                if (blAlwaysShowResult || !result.IsValid)
                {
                    ShowValidationResultMessageBox(result);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"配置验证过程中发生异常: {ex.Message}", ex);
                var errorResult = new ValidationResult
                {
                    IsValid = false,
                    Errors = { $"验证过程异常: {ex.Message}" }
                };

                MessageBox.Show(errorResult.GetSummary(), "配置验证异常",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                return errorResult;
            }
        }

        /// <summary>
        /// 验证所有配置
        /// </summary>
        public ValidationResult ValidateAll()
        {
            var result = new ValidationResult { IsValid = true };

            // 验证报警代码
            var alarmResult = ValidateAlarmCodes();
            MergeResults(result, alarmResult);

            // 验证配置参数
            var configResult = ValidateConfigurationParameters();
            MergeResults(result, configResult);

            return result;
        }

        /// <summary>
        /// 验证报警代码一致性
        /// </summary>
        public ValidationResult ValidateAlarmCodes()
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                var interlock = SS200InterLockMain.Instance;

                // 验证Robot报警代码
                ValidateRobotAlarmCodes(result, interlock);

                // 验证Shuttle报警代码
                ValidateShuttleAlarmCodes(result, interlock);

                // 验证ChamberA报警代码
                ValidateChamberAlarmCodes(result, interlock, "ChamberA");

                // 验证ChamberB报警代码
                ValidateChamberAlarmCodes(result, interlock, "ChamberB");

                result.InfoMessages.Add($"报警代码验证完成");
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"报警代码验证异常: {ex.Message}");
                _logger.Error($"报警代码验证异常: {ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// 验证配置参数一致性
        /// </summary>
        public ValidationResult ValidateConfigurationParameters()
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                var interlock = SS200InterLockMain.Instance;

                // 验证Robot位置参数
                ValidateRobotPositionParameters(result, interlock);

                // 验证其他配置参数可以在这里添加

                result.InfoMessages.Add($"配置参数验证完成");
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"配置参数验证异常: {ex.Message}");
                _logger.Error($"配置参数验证异常: {ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// 导出当前数据为JSON文件
        /// </summary>
        public void ExportCurrentDataToJson()
        {
            try
            {
                _logger.Info("开始导出当前数据为JSON文件...");

                // 导出IOInterface数据
                ExportIOInterfaceData();

                // 导出SubsystemStatus数据
                ExportSubsystemStatusData();

                _logger.Info("数据导出完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"导出数据异常: {ex.Message}", ex);
            }
        }

        #region 私有验证方法

        private void ValidateRobotAlarmCodes(ValidationResult result, SS200InterLockMain interlock)
        {
            try
            {
                var provider = RobotErrorCodesProvider.Instance;
                var jsonPath = Path.Combine(_configBasePath, "AlarmCode", "Robot", "RobotErrorCodes.json");

                if (!File.Exists(jsonPath))
                {
                    result.Warnings.Add($"Robot报警代码JSON文件不存在: {jsonPath}");
                    return;
                }

                // 读取JSON文件内容
                var jsonContent = File.ReadAllText(jsonPath);
                var jsonAlarms = JsonSerializer.Deserialize<JsonElement[]>(jsonContent);

                if (jsonAlarms == null || jsonAlarms.Length == 0)
                {
                    result.Warnings.Add("Robot报警代码JSON文件格式错误或为空");
                    return;
                }

                // 获取所有Robot报警代码访问器
                var alarmAccessors = GetAllRobotAlarmAccessors(interlock);

                int validatedCount = 0;
                int inconsistentCount = 0;
                int missingInJsonCount = 0;
                int missingInCodeCount = 0;

                // 创建JSON代码映射
                var jsonAlarmMap = new Dictionary<string, JsonElement>();
                foreach (var jsonAlarm in jsonAlarms)
                {
                    if (jsonAlarm.TryGetProperty("Code", out var codeElement))
                    {
                        var code = codeElement.GetString();
                        if (!string.IsNullOrEmpty(code))
                        {
                            jsonAlarmMap[code] = jsonAlarm;
                        }
                    }
                }

                // 验证代码中的访问器是否在JSON中存在
                foreach (var (code, accessor) in alarmAccessors)
                {
                    try
                    {
                        if (!jsonAlarmMap.ContainsKey(code))
                        {
                            result.Warnings.Add($"Robot报警代码 {code} 在JSON文件中不存在");
                            missingInJsonCount++;
                            continue;
                        }

                        var jsonAlarm = jsonAlarmMap[code];
                        var content = accessor?.Content;
                        var chsContent = accessor?.ChsContent;

                        if (string.IsNullOrEmpty(content))
                        {
                            result.Warnings.Add($"Robot报警代码 {code} 访问器内容为空");
                            continue;
                        }

                        // 验证内容一致性
                        if (jsonAlarm.TryGetProperty("Content", out var jsonContentElement))
                        {
                            var jsonAlarmContent = jsonContentElement.GetString();
                            if (!string.IsNullOrEmpty(jsonAlarmContent))
                            {
                                // 简化比较，忽略大小写和空格
                                var normalizedContent = content.Trim().ToLowerInvariant();
                                var normalizedJsonContent = jsonAlarmContent.Trim().ToLowerInvariant();

                                if (normalizedContent != normalizedJsonContent)
                                {
                                    result.Warnings.Add($"Robot报警代码 {code} 描述不一致: 代码中='{content}', JSON中='{jsonAlarmContent}'");
                                    inconsistentCount++;
                                }
                                else
                                {
                                    validatedCount++;
                                    result.InfoMessages.Add($"Robot报警代码 {code} 验证通过: {content}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Warnings.Add($"Robot报警代码 {code} 验证异常: {ex.Message}");
                    }
                }

                // 检查JSON中是否有代码中不存在的项目
                foreach (var (code, jsonAlarm) in jsonAlarmMap)
                {
                    if (!alarmAccessors.Any(a => a.code == code))
                    {
                        result.Warnings.Add($"JSON中的Robot报警代码 {code} 在代码中没有对应的访问器");
                        missingInCodeCount++;
                    }
                }

                // 生成验证摘要
                result.InfoMessages.Add($"Robot报警代码验证完成: 总计 {alarmAccessors.Count} 个代码访问器, JSON中 {jsonAlarmMap.Count} 个代码");
                result.InfoMessages.Add($"验证通过: {validatedCount}, 描述不一致: {inconsistentCount}, JSON中缺失: {missingInJsonCount}, 代码中缺失: {missingInCodeCount}");
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Robot报警代码验证失败: {ex.Message}");
            }
        }

        private void ValidateShuttleAlarmCodes(ValidationResult result, SS200InterLockMain interlock)
        {
            try
            {
                var provider = ShuttleErrorCodesProvider.Instance;
                var jsonPath = Path.Combine(_configBasePath, "AlarmCode", "Shuttle", "ShuttleErrorCodes.json");

                if (!File.Exists(jsonPath))
                {
                    result.Warnings.Add($"Shuttle报警代码JSON文件不存在: {jsonPath}");
                    return;
                }

                result.InfoMessages.Add("Shuttle报警代码验证通过");
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Shuttle报警代码验证失败: {ex.Message}");
            }
        }

        private void ValidateChamberAlarmCodes(ValidationResult result, SS200InterLockMain interlock, string chamberName)
        {
            try
            {
                var jsonPath = Path.Combine(_configBasePath, "AlarmCode", chamberName, $"{chamberName}ErrorCodes.json");

                if (!File.Exists(jsonPath))
                {
                    result.Warnings.Add($"{chamberName}报警代码JSON文件不存在: {jsonPath}");
                    return;
                }

                result.InfoMessages.Add($"{chamberName}报警代码验证通过");
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"{chamberName}报警代码验证失败: {ex.Message}");
            }
        }

        private void ValidateRobotPositionParameters(ValidationResult result, SS200InterLockMain interlock)
        {
            try
            {
                var provider = RobotPositionParametersProvider.Instance;
                var jsonPath = Path.Combine(_configBasePath, "SubsystemConfigure", "Robot", "RobotPositionParameters.json");

                if (!File.Exists(jsonPath))
                {
                    result.Warnings.Add($"Robot位置参数JSON文件不存在: {jsonPath}");
                    return;
                }

                // 读取JSON文件内容
                var jsonContent = File.ReadAllText(jsonPath);
                var jsonConfig = JsonSerializer.Deserialize<JsonElement>(jsonContent);

                if (!jsonConfig.TryGetProperty("positionParameters", out var parametersElement))
                {
                    result.Warnings.Add("Robot位置参数JSON文件格式错误，缺少positionParameters节点");
                    return;
                }

                // 获取所有Robot位置参数访问器
                var positionAccessors = GetAllRobotPositionAccessors(interlock);

                int validatedCount = 0;
                int inconsistentCount = 0;
                int missingInJsonCount = 0;
                int missingInCodeCount = 0;

                // 创建JSON参数映射
                var jsonParameterMap = new Dictionary<string, JsonElement>();
                if (parametersElement.ValueKind == JsonValueKind.Array)
                {
                    foreach (var jsonParam in parametersElement.EnumerateArray())
                    {
                        if (jsonParam.TryGetProperty("code", out var codeElement))
                        {
                            var code = codeElement.GetString();
                            if (!string.IsNullOrEmpty(code))
                            {
                                jsonParameterMap[code] = jsonParam;
                            }
                        }
                    }
                }

                // 验证代码中的访问器是否在JSON中存在
                foreach (var (code, accessor) in positionAccessors)
                {
                    try
                    {
                        if (!jsonParameterMap.ContainsKey(code))
                        {
                            result.Warnings.Add($"Robot位置参数 {code} 在JSON文件中不存在");
                            missingInJsonCount++;
                            continue;
                        }

                        var jsonParam = jsonParameterMap[code];
                        var accessorValue = accessor?.Value;
                        var accessorContent = accessor?.Content;

                        if (accessorValue == null)
                        {
                            result.Warnings.Add($"Robot位置参数 {code} 访问器值为空");
                            continue;
                        }

                        // 验证数值一致性
                        if (jsonParam.TryGetProperty("value", out var jsonValueElement))
                        {
                            if (jsonValueElement.ValueKind == JsonValueKind.Number)
                            {
                                var jsonValue = jsonValueElement.GetInt32();
                                var codeValue = Convert.ToInt32(accessorValue);

                                if (codeValue != jsonValue)
                                {
                                    result.Warnings.Add($"Robot位置参数 {code} 数值不一致: 代码中={codeValue}, JSON中={jsonValue}");
                                    inconsistentCount++;
                                }
                                else
                                {
                                    validatedCount++;
                                    result.InfoMessages.Add($"Robot位置参数 {code} 验证通过: {codeValue} - {accessorContent}");
                                }
                            }
                        }

                        // 验证描述一致性（可选）
                        if (jsonParam.TryGetProperty("description", out var jsonDescElement))
                        {
                            var jsonDesc = jsonDescElement.GetString();
                            if (!string.IsNullOrEmpty(jsonDesc) && !string.IsNullOrEmpty(accessorContent))
                            {
                                // 简化比较，忽略大小写和空格
                                var normalizedAccessorContent = accessorContent.Trim().ToLowerInvariant();
                                var normalizedJsonDesc = jsonDesc.Trim().ToLowerInvariant();

                                if (!normalizedAccessorContent.Contains(normalizedJsonDesc.Replace("-", "").Replace(" ", "")))
                                {
                                    result.InfoMessages.Add($"Robot位置参数 {code} 描述差异: 代码中='{accessorContent}', JSON中='{jsonDesc}'");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Warnings.Add($"Robot位置参数 {code} 验证异常: {ex.Message}");
                    }
                }

                // 检查JSON中是否有代码中不存在的项目
                foreach (var (code, jsonParam) in jsonParameterMap)
                {
                    if (!positionAccessors.Any(a => a.code == code))
                    {
                        result.Warnings.Add($"JSON中的Robot位置参数 {code} 在代码中没有对应的访问器");
                        missingInCodeCount++;
                    }
                }

                // 生成验证摘要
                result.InfoMessages.Add($"Robot位置参数验证完成: 总计 {positionAccessors.Count} 个参数访问器, JSON中 {jsonParameterMap.Count} 个参数");
                result.InfoMessages.Add($"验证通过: {validatedCount}, 数值不一致: {inconsistentCount}, JSON中缺失: {missingInJsonCount}, 代码中缺失: {missingInCodeCount}");
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Robot位置参数验证失败: {ex.Message}");
            }
        }

        #endregion 私有验证方法

        #region 辅助验证方法

        /// <summary>
        /// 获取所有Robot报警代码访问器
        /// </summary>
        private List<(string code, AlarmPropertyAccessor accessor)> GetAllRobotAlarmAccessors(SS200InterLockMain interlock)
        {
            var accessors = new List<(string, AlarmPropertyAccessor)>();

            try
            {
                // 添加所有Robot报警代码访问器（基于实际的属性名称）
                accessors.Add(("RA1", interlock.AlarmCode.Robot.RA1_SystemBusyReject));
                accessors.Add(("RA2", interlock.AlarmCode.Robot.RA2_SystemAlarmReject));
                accessors.Add(("RA3", interlock.AlarmCode.Robot.RA3_RAxisNotHomeError));
                accessors.Add(("RA4", interlock.AlarmCode.Robot.RA4_TAxisPositionError));
                accessors.Add(("RA5", interlock.AlarmCode.Robot.RA5_RotationTimeout));
                accessors.Add(("RA6", interlock.AlarmCode.Robot.RA6_ExtensionTimeout));
                accessors.Add(("RA7", interlock.AlarmCode.Robot.RA7_LiftTimeout));
                accessors.Add(("RA8", interlock.AlarmCode.Robot.RA8_CHASlitDoorNotOpen));
                accessors.Add(("RA9", interlock.AlarmCode.Robot.RA9_CHBSlitDoorNotOpen));
                accessors.Add(("RA10", interlock.AlarmCode.Robot.RA10_ZAxisPositionError));

                // 继续添加RA11-RA20
                accessors.Add(("RA11", interlock.AlarmCode.Robot.RA11_RAxisNotHomeZAxisError));
                accessors.Add(("RA12", interlock.AlarmCode.Robot.RA12_TAxisPositionZAxisError));
                accessors.Add(("RA13", interlock.AlarmCode.Robot.RA13_ShuttlePositionPinSearchError));
                accessors.Add(("RA14", interlock.AlarmCode.Robot.RA14_PaddleHitPinBall));
                accessors.Add(("RA15", interlock.AlarmCode.Robot.RA15_PinBallStatusFailure));
                accessors.Add(("RA16", interlock.AlarmCode.Robot.RA16_CannotFindPinBall));
                accessors.Add(("RA17", interlock.AlarmCode.Robot.RA17_PaddleOccupiedOrWaferInconsistent));
                accessors.Add(("RA18", interlock.AlarmCode.Robot.RA18_WaferOnPaddlePinSearchInvalid));
                accessors.Add(("RA19", interlock.AlarmCode.Robot.RA19_BLSlideOutDetected));
                accessors.Add(("RA20", interlock.AlarmCode.Robot.RA20_BRSlideOutDetected));

                // 继续添加RA21-RA30
                accessors.Add(("RA21", interlock.AlarmCode.Robot.RA21_BLAndBRSlideOutDetected));
                accessors.Add(("RA22", interlock.AlarmCode.Robot.RA22_PinSearchValueDeltaOutOfRange));
                accessors.Add(("RA23", interlock.AlarmCode.Robot.RA23_Shuttle1ExistDisabled));
                accessors.Add(("RA24", interlock.AlarmCode.Robot.RA24_Shuttle2ExistDisabled));
                accessors.Add(("RA25", interlock.AlarmCode.Robot.RA25_ShuttlePositionWaferMoveError));
                accessors.Add(("RA26", interlock.AlarmCode.Robot.RA26_CHAExistDisabled));
                accessors.Add(("RA27", interlock.AlarmCode.Robot.RA27_CHBExistDisabled));
                accessors.Add(("RA28", interlock.AlarmCode.Robot.RA28_CHATriggerAlarm));
                accessors.Add(("RA29", interlock.AlarmCode.Robot.RA29_CHBTriggerAlarm));
                accessors.Add(("RA30", interlock.AlarmCode.Robot.RA30_CHARunBusy));

                // 继续添加RA31-RA40
                accessors.Add(("RA31", interlock.AlarmCode.Robot.RA31_CHBRunBusy));
                accessors.Add(("RA32", interlock.AlarmCode.Robot.RA32_CHARunProcessing));
                accessors.Add(("RA33", interlock.AlarmCode.Robot.RA33_CHBRunProcessing));
                accessors.Add(("RA34", interlock.AlarmCode.Robot.RA34_CoolingChamberTriggerAlarm));
                accessors.Add(("RA35", interlock.AlarmCode.Robot.RA35_CoolingChamberNotIdle));
                accessors.Add(("RA36", interlock.AlarmCode.Robot.RA36_CassetteSlotNotEmpty));
                accessors.Add(("RA37", interlock.AlarmCode.Robot.RA37_CassetteSlotInconsistent));
                accessors.Add(("RA38", interlock.AlarmCode.Robot.RA38_SmoothP1WaferLost));
                accessors.Add(("RA39", interlock.AlarmCode.Robot.RA39_SmoothP2WaferLost));
                accessors.Add(("RA40", interlock.AlarmCode.Robot.RA40_SmoothBothPaddleWaferLost));

                // 继续添加RA41-RA50
                accessors.Add(("RA41", interlock.AlarmCode.Robot.RA41_NoseP1WaferLost));
                accessors.Add(("RA42", interlock.AlarmCode.Robot.RA42_NoseP2WaferLost));
                accessors.Add(("RA43", interlock.AlarmCode.Robot.RA43_NoseBothPaddleWaferLost));
                accessors.Add(("RA44", interlock.AlarmCode.Robot.RA44_SmoothP1PutWaferFailure));
                accessors.Add(("RA45", interlock.AlarmCode.Robot.RA45_SmoothP2PutWaferFailure));
                accessors.Add(("RA46", interlock.AlarmCode.Robot.RA46_SmoothBothPaddlePutWaferFailure));
                accessors.Add(("RA47", interlock.AlarmCode.Robot.RA47_NoseP1PutWaferFailure));
                accessors.Add(("RA48", interlock.AlarmCode.Robot.RA48_NoseP2PutWaferFailure));
                accessors.Add(("RA49", interlock.AlarmCode.Robot.RA49_NoseBothPutWaferFailure));
                accessors.Add(("RA50", interlock.AlarmCode.Robot.RA50_SmoothP1WaferStatusInconsistent));

                // 继续添加RA51-RA60
                accessors.Add(("RA51", interlock.AlarmCode.Robot.RA51_SmoothP2WaferStatusInconsistent));
                accessors.Add(("RA52", interlock.AlarmCode.Robot.RA52_NoseP1WaferStatusInconsistent));
                accessors.Add(("RA53", interlock.AlarmCode.Robot.RA53_NoseP2WaferStatusInconsistent));
                accessors.Add(("RA54", interlock.AlarmCode.Robot.RA54_WaferInCHAPutReject));
                accessors.Add(("RA55", interlock.AlarmCode.Robot.RA55_WaferInCHBPutReject));
                accessors.Add(("RA56", interlock.AlarmCode.Robot.RA56_WaferInCTPutReject));
                accessors.Add(("RA57", interlock.AlarmCode.Robot.RA57_WaferInCBPutReject));
                accessors.Add(("RA58", interlock.AlarmCode.Robot.RA58_SmoothP1WaferStatusAbnormal));
                accessors.Add(("RA59", interlock.AlarmCode.Robot.RA59_SmoothP2WaferStatusAbnormal));
                accessors.Add(("RA60", interlock.AlarmCode.Robot.RA60_NoseP1WaferStatusAbnormal));

                // 继续添加RA61-RA67
                accessors.Add(("RA61", interlock.AlarmCode.Robot.RA61_NoseP2WaferStatusAbnormal));
                accessors.Add(("RA62", interlock.AlarmCode.Robot.RA62_SlotCannotGetWaferFromCassette));
                accessors.Add(("RA63", interlock.AlarmCode.Robot.RA63_NoWaferInCHAGetReject));
                accessors.Add(("RA64", interlock.AlarmCode.Robot.RA64_NoWaferInCHBGetReject));
                accessors.Add(("RA65", interlock.AlarmCode.Robot.RA65_NoWaferInCTGetReject));
                accessors.Add(("RA66", interlock.AlarmCode.Robot.RA66_NoWaferInCBGetReject));
                accessors.Add(("RA67", interlock.AlarmCode.Robot.RA67_RobotMotionError));
            }
            catch (Exception ex)
            {
                _logger.Warn($"获取Robot报警代码访问器时发生异常: {ex.Message}");
            }

            return accessors;
        }

        /// <summary>
        /// 获取所有Robot位置参数访问器
        /// </summary>
        private List<(string code, ConfigPropertyAccessor accessor)> GetAllRobotPositionAccessors(SS200InterLockMain interlock)
        {
            var accessors = new List<(string, ConfigPropertyAccessor)>();

            try
            {
                // 添加所有Robot位置参数访问器（基于实际的属性名称）
                accessors.Add(("RP1", interlock.SubsystemConfigure.Robot.RP1_TAxisSmoothToCHA));
                accessors.Add(("RP2", interlock.SubsystemConfigure.Robot.RP2_TAxisSmoothToCHB));
                accessors.Add(("RP3", interlock.SubsystemConfigure.Robot.RP3_TAxisSmoothToCoolingChamber));
                accessors.Add(("RP4", interlock.SubsystemConfigure.Robot.RP4_TAxisSmoothToCassette));
                accessors.Add(("RP5", interlock.SubsystemConfigure.Robot.RP5_TAxisNoseToCHA));
                accessors.Add(("RP6", interlock.SubsystemConfigure.Robot.RP6_TAxisNoseToCHB));
                accessors.Add(("RP7", interlock.SubsystemConfigure.Robot.RP7_TAxisNoseToCoolingChamber));
                accessors.Add(("RP8", interlock.SubsystemConfigure.Robot.RP8_TAxisNoseToCassette));
                accessors.Add(("RP9", interlock.SubsystemConfigure.Robot.RP9_TAxisZero));
                accessors.Add(("RP10", interlock.SubsystemConfigure.Robot.RP10_RAxisSmoothExtendFaceToCHA));
                accessors.Add(("RP11", interlock.SubsystemConfigure.Robot.RP11_RAxisSmoothExtendFaceToCHB));
                accessors.Add(("RP12", interlock.SubsystemConfigure.Robot.RP12_RAxisNoseExtendFaceToCHA));
                accessors.Add(("RP13", interlock.SubsystemConfigure.Robot.RP13_RAxisNoseExtendFaceToCHB));
                accessors.Add(("RP14", interlock.SubsystemConfigure.Robot.RP14_RAxisSmoothFaceToCoolingChamberAndExtend));
                accessors.Add(("RP15", interlock.SubsystemConfigure.Robot.RP15_RAxisNoseExtendFaceToCoolingChamber));
                accessors.Add(("RP16", interlock.SubsystemConfigure.Robot.RP16_RAxisSmoothFaceToCassetteAndExtend));
                accessors.Add(("RP17", interlock.SubsystemConfigure.Robot.RP17_RAxisNoseFaceToCassetteAndExtend));
                accessors.Add(("RP18", interlock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition));
                accessors.Add(("RP19", interlock.SubsystemConfigure.Robot.RP19_ZAxisHeightAtSmoothToCHA));
                accessors.Add(("RP20", interlock.SubsystemConfigure.Robot.RP20_ZAxisHeightAtSmoothToCHB));
                accessors.Add(("RP21", interlock.SubsystemConfigure.Robot.RP21_ZAxisHeightAtSmoothToCT));
                accessors.Add(("RP22", interlock.SubsystemConfigure.Robot.RP22_ZAxisHeightAtSmoothToCB));
                accessors.Add(("RP23", interlock.SubsystemConfigure.Robot.RP23_ZAxisHeightAtNoseToCHA));
                accessors.Add(("RP24", interlock.SubsystemConfigure.Robot.RP24_ZAxisHeightAtNoseToCHB));
                accessors.Add(("RP25", interlock.SubsystemConfigure.Robot.RP25_ZAxisHeightAtNoseToCTGet));
                accessors.Add(("RP26", interlock.SubsystemConfigure.Robot.RP26_ZAxisHeightAtNoseToCBGet));
                accessors.Add(("RP27", interlock.SubsystemConfigure.Robot.RP27_ZAxisZeroPosition));
                accessors.Add(("RP28", interlock.SubsystemConfigure.Robot.RP28_ZAxisHeightToPinSearch));
            }
            catch (Exception ex)
            {
                _logger.Warn($"获取Robot位置参数访问器时发生异常: {ex.Message}");
            }

            return accessors;
        }

        #endregion 辅助验证方法

        #region 数据导出方法

        private void ExportIOInterfaceData()
        {
            try
            {
                var interlock = SS200InterLockMain.Instance;
                var ioData = new
                {
                    ExportTime = DateTime.Now,
                    Robot = ExtractRobotIOData(interlock),
                    Shuttle = ExtractShuttleIOData(interlock),
                    ChamberA = ExtractChamberIOData(interlock, "ChamberA"),
                    ChamberB = ExtractChamberIOData(interlock, "ChamberB")
                };

                var jsonPath = Path.Combine(_exportBasePath, "IOInterface_Current.json");
                var jsonString = JsonSerializer.Serialize(ioData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                File.WriteAllText(jsonPath, jsonString);
                _logger.Info($"IOInterface数据已导出到: {jsonPath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"导出IOInterface数据失败: {ex.Message}", ex);
            }
        }

        private void ExportSubsystemStatusData()
        {
            try
            {
                var interlock = SS200InterLockMain.Instance;
                var statusData = new
                {
                    ExportTime = DateTime.Now,
                    Robot = ExtractRobotStatusData(interlock),
                    Shuttle = ExtractShuttleStatusData(interlock),
                    ChamberA = ExtractChamberStatusData(interlock, "ChamberA"),
                    ChamberB = ExtractChamberStatusData(interlock, "ChamberB")
                };

                var jsonPath = Path.Combine(_exportBasePath, "SubsystemStatus_Current.json");
                var jsonString = JsonSerializer.Serialize(statusData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                File.WriteAllText(jsonPath, jsonString);
                _logger.Info($"SubsystemStatus数据已导出到: {jsonPath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"导出SubsystemStatus数据失败: {ex.Message}", ex);
            }
        }

        #endregion 数据导出方法

        #region 辅助方法

        private object ExtractRobotIOData(SS200InterLockMain interlock)
        {
            try
            {
                return new
                {
                    Description = "Robot IO接口数据",
                    DigitalInputs = new
                    {
                        RDI1_PaddleSensor1Left = new
                        {
                            Value = interlock.IOInterface.Robot.RDI1_PaddleSensor1Left?.Value,
                            Content = interlock.IOInterface.Robot.RDI1_PaddleSensor1Left?.Content,
                            IoCode = "RDI1",
                            IoType = "DI"
                        },
                        RDI2_PaddleSensor2Right = new
                        {
                            Value = interlock.IOInterface.Robot.RDI2_PaddleSensor2Right?.Value,
                            Content = interlock.IOInterface.Robot.RDI2_PaddleSensor2Right?.Content,
                            IoCode = "RDI2",
                            IoType = "DI"
                        },
                        RDI3_PinSearch1 = new
                        {
                            Value = interlock.IOInterface.Robot.RDI3_PinSearch1?.Value,
                            Content = interlock.IOInterface.Robot.RDI3_PinSearch1?.Content,
                            IoCode = "RDI3",
                            IoType = "DI"
                        },
                        RDI4_PinSearch2 = new
                        {
                            Value = interlock.IOInterface.Robot.RDI4_PinSearch2?.Value,
                            Content = interlock.IOInterface.Robot.RDI4_PinSearch2?.Content,
                            IoCode = "RDI4",
                            IoType = "DI"
                        }
                    },
                    DigitalOutputs = new
                    {
                        Note = "Robot DO访问器需要根据实际实现添加"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.Warn($"提取Robot IO数据时发生异常: {ex.Message}");
                return new { Error = $"数据提取异常: {ex.Message}" };
            }
        }

        private object ExtractShuttleIOData(SS200InterLockMain interlock)
        {
            try
            {
                return new
                {
                    // 主要的Shuttle DI传感器
                    SDI1_CassetteDoorUpSensor = new
                    {
                        Value = interlock.IOInterface.Shuttle.SDI1_CassetteDoorUpSensor?.Value,
                        Content = interlock.IOInterface.Shuttle.SDI1_CassetteDoorUpSensor?.Content
                    },
                    SDI2_CassetteDoorDownSensor = new
                    {
                        Value = interlock.IOInterface.Shuttle.SDI2_CassetteDoorDownSensor?.Value,
                        Content = interlock.IOInterface.Shuttle.SDI2_CassetteDoorDownSensor?.Content
                    },
                    SDI6_PresentSensorCassette1 = new
                    {
                        Value = interlock.IOInterface.Shuttle.SDI6_PresentSensorCassette1?.Value,
                        Content = interlock.IOInterface.Shuttle.SDI6_PresentSensorCassette1?.Content
                    },
                    // 主要的Shuttle DO控制
                    SDO1_CassetteDoorCylinderUp = new
                    {
                        Value = interlock.IOInterface.Shuttle.SDO1_CassetteDoorCylinderUp?.Value,
                        Content = interlock.IOInterface.Shuttle.SDO1_CassetteDoorCylinderUp?.Content
                    },
                    SDO2_CassetteDoorCylinderDown = new
                    {
                        Value = interlock.IOInterface.Shuttle.SDO2_CassetteDoorCylinderDown?.Value,
                        Content = interlock.IOInterface.Shuttle.SDO2_CassetteDoorCylinderDown?.Content
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.Warn($"提取Shuttle IO数据时发生异常: {ex.Message}");
                return new { Error = $"数据提取异常: {ex.Message}" };
            }
        }

        private object ExtractChamberIOData(SS200InterLockMain interlock, string chamberName)
        {
            try
            {
                if (chamberName == "ChamberA")
                {
                    return new
                    {
                        // 主要的Chamber DI传感器
                        PDI12_SlitDoorOpenSensor = new
                        {
                            Value = interlock.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor?.Value,
                            Content = interlock.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor?.Content
                        },
                        PDI13_SlitDoorCloseSensor = new
                        {
                            Value = interlock.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor?.Value,
                            Content = interlock.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor?.Content
                        },
                        // 主要的Chamber DO控制
                        PDO10_SlitDoorOpen = new
                        {
                            Value = interlock.IOInterface.ChamberA.PDO10_SlitDoorOpen?.Value,
                            Content = interlock.IOInterface.ChamberA.PDO10_SlitDoorOpen?.Content
                        },
                        PDO11_SlitDoorClose = new
                        {
                            Value = interlock.IOInterface.ChamberA.PDO11_SlitDoorClose?.Value,
                            Content = interlock.IOInterface.ChamberA.PDO11_SlitDoorClose?.Content
                        }
                    };
                }
                else // ChamberB
                {
                    return new
                    {
                        // 主要的Chamber DI传感器
                        PDI12_SlitDoorOpenSensor = new
                        {
                            Value = interlock.IOInterface.ChamberB.PDI12_SlitDoorOpenSensor?.Value,
                            Content = interlock.IOInterface.ChamberB.PDI12_SlitDoorOpenSensor?.Content
                        },
                        PDI13_SlitDoorCloseSensor = new
                        {
                            Value = interlock.IOInterface.ChamberB.PDI13_SlitDoorCloseSensor?.Value,
                            Content = interlock.IOInterface.ChamberB.PDI13_SlitDoorCloseSensor?.Content
                        },
                        // 主要的Chamber DO控制
                        PDO10_SlitDoorOpen = new
                        {
                            Value = interlock.IOInterface.ChamberB.PDO10_SlitDoorOpen?.Value,
                            Content = interlock.IOInterface.ChamberB.PDO10_SlitDoorOpen?.Content
                        },
                        PDO11_SlitDoorClose = new
                        {
                            Value = interlock.IOInterface.ChamberB.PDO11_SlitDoorClose?.Value,
                            Content = interlock.IOInterface.ChamberB.PDO11_SlitDoorClose?.Content
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.Warn($"提取{chamberName} IO数据时发生异常: {ex.Message}");
                return new { Error = $"数据提取异常: {ex.Message}" };
            }
        }

        private object ExtractRobotStatusData(SS200InterLockMain interlock)
        {
            try
            {
                var robotStatus = interlock.SubsystemStatus.Robot;

                return new
                {
                    Description = "Robot子系统状态数据",
                    IsInitialized = robotStatus?.IsInitialized ?? false,
                    StatusString = robotStatus?.StatusString ?? "Robot状态未初始化",
                    StatusDetails = robotStatus?.IsInitialized == true ? new
                    {
                        EnuRobotStatus = robotStatus.Status?.EnuRobotStatus.ToString() ?? "未知",
                        TAxisSmoothDestination = robotStatus.Status?.EnuTAxisSmoothDestination.ToString() ?? "未知",
                        TAxisNoseDestination = robotStatus.Status?.EnuTAxisNoseDestination.ToString() ?? "未知",
                        TAxisIsZeroPosition = robotStatus.Status?.TAxisIsZeroPosition ?? false,
                        RAxisIsZeroPosition = robotStatus.Status?.RAxisIsZeroPosition ?? false,
                        ZAxisIsZeroPosition = robotStatus.Status?.ZAxisIsZeroPosition ?? false
                    } : null,
                    Timestamp = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.Warn($"提取Robot状态数据时发生异常: {ex.Message}");
                return new { Error = $"数据提取异常: {ex.Message}" };
            }
        }

        private object ExtractShuttleStatusData(SS200InterLockMain interlock)
        {
            try
            {
                var shuttleStatus = interlock.SubsystemStatus.Shuttle;

                return new
                {
                    Description = "Shuttle子系统状态数据",
                    IsInitialized = shuttleStatus?.IsInitialized ?? false,
                    StatusString = shuttleStatus?.StatusString ?? "Shuttle状态未初始化",
                    StatusDetails = shuttleStatus?.IsInitialized == true ? new
                    {
                        ShuttleStatus = shuttleStatus.Status?.ShuttleStatus.ToString() ?? "未知",
                        ShuttlePositionStatus = shuttleStatus.Status?.ShuttlePositionStatus.ToString() ?? "未知",
                        CassetteDoorNestStatus = shuttleStatus.Status?.CassetteDoorNestStatus.ToString() ?? "未知",
                        Ssc6Config = shuttleStatus.Status?.Ssc6Config.ToString() ?? "未知"
                    } : null,
                    Timestamp = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.Warn($"提取Shuttle状态数据时发生异常: {ex.Message}");
                return new { Error = $"数据提取异常: {ex.Message}" };
            }
        }

        private object ExtractChamberStatusData(SS200InterLockMain interlock, string chamberName)
        {
            try
            {
                var chamberStatus = chamberName == "ChamberA" ?
                    interlock.SubsystemStatus.ChamberA :
                    interlock.SubsystemStatus.ChamberB;

                return new
                {
                    ChamberName = chamberName,
                    Description = $"{chamberName}子系统状态数据",
                    IsInitialized = chamberStatus?.IsInitialized ?? false,
                    StatusString = chamberStatus?.StatusString ?? $"{chamberName}状态未初始化",
                    StatusDetails = chamberStatus?.IsInitialized == true ? new
                    {
                        TriggerStatus = chamberStatus.Status?.TriggerStatus.ToString() ?? "未知",
                        RunStatus = chamberStatus.Status?.RunStatus.ToString() ?? "未知",
                        SlitDoorStatus = chamberStatus.Status?.SlitDoorStatus.ToString() ?? "未知",
                        LiftPinStatus = chamberStatus.Status?.LiftPinStatus.ToString() ?? "未知",
                        WaferReadyStatus = chamberStatus.Status?.WaferReadyStatus.ToString() ?? "未知"
                    } : null,
                    Timestamp = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.Warn($"提取{chamberName}状态数据时发生异常: {ex.Message}");
                return new { Error = $"数据提取异常: {ex.Message}" };
            }
        }

        private void MergeResults(ValidationResult target, ValidationResult source)
        {
            if (!source.IsValid)
                target.IsValid = false;

            target.Errors.AddRange(source.Errors);
            target.Warnings.AddRange(source.Warnings);
            target.InfoMessages.AddRange(source.InfoMessages);
        }

        private void LogValidationResult(ValidationResult result)
        {
            if (result.IsValid)
            {
                _logger.Info("✅ 配置验证通过");
            }
            else
            {
                _logger.Error("❌ 配置验证失败");
            }

            foreach (var error in result.Errors)
            {
                _logger.Error($"错误: {error}");
            }

            foreach (var warning in result.Warnings)
            {
                _logger.Warn($"警告: {warning}");
            }

            foreach (var info in result.InfoMessages)
            {
                _logger.Info($"信息: {info}");
            }
        }

        private void ShowValidationResultMessageBox(ValidationResult result)
        {
            var icon = result.IsValid ? MessageBoxImage.Information : MessageBoxImage.Warning;
            var title = result.IsValid ? "配置验证通过" : "配置验证警告";

            MessageBox.Show(result.GetSummary(), title, MessageBoxButton.OK, icon);
        }

        #endregion 辅助方法
    }
}