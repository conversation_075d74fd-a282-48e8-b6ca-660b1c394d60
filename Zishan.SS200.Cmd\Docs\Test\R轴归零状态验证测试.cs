using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// R轴归零状态验证测试类
    /// 用于验证R轴归零时序问题的修复效果
    /// </summary>
    public class RAxisZeroStatusVerificationTest
    {
        private readonly IS200McuCmdService _cmdService;
        private readonly SS200InterLockMain _interLock;
        private readonly ILogger _logger;

        public RAxisZeroStatusVerificationTest(IS200McuCmdService cmdService)
        {
            _cmdService = cmdService;
            _interLock = SS200InterLockMain.Instance;
            _logger = LogManager.GetCurrentClassLogger();
        }

        /// <summary>
        /// 测试R轴归零的完整流程
        /// </summary>
        public async Task<bool> TestRAxisZeroCompleteFlowAsync()
        {
            _logger.Info("开始测试R轴归零完整流程");

            try
            {
                // 1. 记录初始状态
                var initialStatus = await RecordInitialStatusAsync();
                _logger.Info($"初始状态: {initialStatus}");

                // 2. 执行R轴归零
                _logger.Info("执行R轴归零操作...");
                var zeroResult = await _cmdService.ZeroRAxisAsync();

                if (!zeroResult.Success)
                {
                    _logger.Error($"R轴归零失败: {zeroResult.Message}");
                    return false;
                }

                _logger.Info($"R轴归零命令执行结果: {zeroResult.Message}");

                // 3. 验证最终状态
                var finalStatus = await RecordFinalStatusAsync();
                _logger.Info($"最终状态: {finalStatus}");

                // 4. 状态对比验证
                bool verificationPassed = await VerifyStatusConsistencyAsync();
                
                if (verificationPassed)
                {
                    _logger.Info("✅ R轴归零状态验证测试通过");
                    return true;
                }
                else
                {
                    _logger.Error("❌ R轴归零状态验证测试失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"R轴归零测试异常: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 测试R轴移动的时序问题
        /// </summary>
        public async Task<bool> TestRAxisMovementTimingAsync()
        {
            _logger.Info("开始测试R轴移动时序问题");

            try
            {
                // 获取R轴零位配置
                int rAxisZeroPosition = _interLock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition.Value;
                
                // 测试1: 使用原始方法（不等待完成）
                _logger.Info("测试1: 使用快速移动模式（不等待完成）");
                var quickResult = await _cmdService.MoveRAxisToPositionAsync(rAxisZeroPosition, 
                    waitForCompletion: false, verifyPosition: false);
                
                var statusAfterQuick = await GetCurrentRAxisStatusAsync();
                _logger.Info($"快速移动后状态: {statusAfterQuick}");

                // 等待一段时间
                await Task.Delay(2000);

                // 测试2: 使用增强方法（等待完成并验证）
                _logger.Info("测试2: 使用完整移动模式（等待完成并验证）");
                var completeResult = await _cmdService.MoveRAxisToPositionAsync(rAxisZeroPosition, 
                    waitForCompletion: true, verifyPosition: true, timeoutMs: 15000);
                
                var statusAfterComplete = await GetCurrentRAxisStatusAsync();
                _logger.Info($"完整移动后状态: {statusAfterComplete}");

                // 验证两种方法的差异
                bool timingTestPassed = completeResult.Success && 
                                       statusAfterComplete.IsAtZeroPosition;

                if (timingTestPassed)
                {
                    _logger.Info("✅ R轴移动时序测试通过");
                    return true;
                }
                else
                {
                    _logger.Error("❌ R轴移动时序测试失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"R轴移动时序测试异常: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 压力测试：连续多次R轴归零
        /// </summary>
        public async Task<bool> TestRAxisZeroStressAsync(int testCount = 5)
        {
            _logger.Info($"开始R轴归零压力测试，测试次数: {testCount}");

            int successCount = 0;
            int failureCount = 0;

            for (int i = 1; i <= testCount; i++)
            {
                _logger.Info($"第 {i}/{testCount} 次R轴归零测试");

                try
                {
                    var result = await _cmdService.ZeroRAxisAsync();
                    
                    if (result.Success)
                    {
                        // 验证状态一致性
                        bool statusOk = await VerifyStatusConsistencyAsync();
                        if (statusOk)
                        {
                            successCount++;
                            _logger.Info($"第 {i} 次测试成功");
                        }
                        else
                        {
                            failureCount++;
                            _logger.Error($"第 {i} 次测试失败：状态不一致");
                        }
                    }
                    else
                    {
                        failureCount++;
                        _logger.Error($"第 {i} 次测试失败: {result.Message}");
                    }

                    // 测试间隔
                    if (i < testCount)
                    {
                        await Task.Delay(1000);
                    }
                }
                catch (Exception ex)
                {
                    failureCount++;
                    _logger.Error($"第 {i} 次测试异常: {ex.Message}", ex);
                }
            }

            double successRate = (double)successCount / testCount * 100;
            _logger.Info($"压力测试结果: 成功 {successCount}/{testCount} ({successRate:F1}%), 失败 {failureCount}");

            return successRate >= 90; // 90%以上成功率认为通过
        }

        #region 辅助方法

        /// <summary>
        /// 记录初始状态
        /// </summary>
        private async Task<string> RecordInitialStatusAsync()
        {
            var status = await GetCurrentRAxisStatusAsync();
            return $"位置: {status.CurrentPosition}, 零位状态: {status.IsAtZeroPosition}";
        }

        /// <summary>
        /// 记录最终状态
        /// </summary>
        private async Task<string> RecordFinalStatusAsync()
        {
            // 等待状态稳定
            await Task.Delay(500);
            var status = await GetCurrentRAxisStatusAsync();
            return $"位置: {status.CurrentPosition}, 零位状态: {status.IsAtZeroPosition}";
        }

        /// <summary>
        /// 获取当前R轴状态
        /// </summary>
        private async Task<(int CurrentPosition, bool IsAtZeroPosition)> GetCurrentRAxisStatusAsync()
        {
            await Task.Delay(100); // 确保状态更新

            var currentPosition = _interLock.RTZAxisPosition.GetCurrentRTZSteps();
            var robotStatus = _interLock.SubsystemStatus.Robot.Status;

            return (currentPosition.RAxisStep, robotStatus.RAxisIsZeroPosition);
        }

        /// <summary>
        /// 验证状态一致性
        /// </summary>
        private async Task<bool> VerifyStatusConsistencyAsync()
        {
            var (currentPosition, isAtZeroPosition) = await GetCurrentRAxisStatusAsync();
            int zeroPosition = _interLock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition.Value;
            
            // 检查物理位置和状态表是否一致（精确到位，容差为0）
            bool physicallyAtZero = Math.Abs(currentPosition - zeroPosition) <= 0;
            bool statusConsistent = physicallyAtZero == isAtZeroPosition;

            if (!statusConsistent)
            {
                _logger.Error($"状态不一致: 物理位置 {currentPosition} (零位: {zeroPosition}), " +
                             $"物理上在零位: {physicallyAtZero}, 状态表显示: {isAtZeroPosition}");
            }

            return statusConsistent;
        }

        #endregion

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task<bool> RunAllTestsAsync()
        {
            _logger.Info("开始运行R轴归零状态验证的所有测试");

            bool test1 = await TestRAxisZeroCompleteFlowAsync();
            bool test2 = await TestRAxisMovementTimingAsync();
            bool test3 = await TestRAxisZeroStressAsync(3);

            bool allPassed = test1 && test2 && test3;

            _logger.Info($"所有测试结果: {(allPassed ? "✅ 全部通过" : "❌ 存在失败")}");
            _logger.Info($"  - 完整流程测试: {(test1 ? "通过" : "失败")}");
            _logger.Info($"  - 时序问题测试: {(test2 ? "通过" : "失败")}");
            _logger.Info($"  - 压力测试: {(test3 ? "通过" : "失败")}");

            return allPassed;
        }
    }
}
