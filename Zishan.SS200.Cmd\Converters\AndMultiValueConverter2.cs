using System;
using System.Globalization;
using System.Windows.Data;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Converters
{
    public class AndMultiValueConverter2 : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2 || !(values[0] is bool) || !(values[1] is EnuMode))
            {
                return false;
            }

            bool isRunning = (bool)values[0];
            EnuMode mode = (EnuMode)values[1];

            if (Golbal.IsDevDebug)//DevDebug模式下，启用手动模式
            {
                mode = EnuMode.Manual;
            }

            return !isRunning && mode == EnuMode.Manual;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}