using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Examples
{
    /// <summary>
    /// HcGrowlExtensions 优化后的使用示例
    /// </summary>
    public class HcGrowlExtensionsExamples
    {
        /// <summary>
        /// 基础消息示例
        /// </summary>
        public void BasicMessageExamples()
        {
            // 基础消息类型
            HcGrowlExtensions.Info("系统初始化完成");
            HcGrowlExtensions.Success("数据保存成功");
            HcGrowlExtensions.Warning("配置文件版本过旧，建议更新");
            HcGrowlExtensions.Error("网络连接失败，请检查网络设置");

            // 使用预定义Token进行分组
            HcGrowlExtensions.Info("系统启动", HcGrowlExtensions.Tokens.System);
            HcGrowlExtensions.Success("验证通过", HcGrowlExtensions.Tokens.Validation);

            // 智能时间管理（-1表示使用智能时间）
            HcGrowlExtensions.Success("操作完成", waitTime: -1);  // 根据调试模式自动选择时间
            HcGrowlExtensions.Error("严重错误", waitTime: -1);    // 错误消息会显示更长时间

            // 显示时间戳
            HcGrowlExtensions.Info("带时间戳的消息", showDateTime: true);
        }

        /// <summary>
        /// 设备相关消息示例
        /// </summary>
        public void DeviceMessageExamples()
        {
            // 设备信息消息
            HcGrowlExtensions.DeviceInfo(EnuMcuDeviceType.Robot, "状态更新完成");
            HcGrowlExtensions.DeviceSuccess(EnuMcuDeviceType.ChamberA, "腔体预热完成");
            HcGrowlExtensions.DeviceWarning(EnuMcuDeviceType.Shuttle, "传感器信号异常");
            HcGrowlExtensions.DeviceError(EnuMcuDeviceType.ChamberB, "通信超时");

            // 连接状态消息
            HcGrowlExtensions.ConnectionStatus(EnuMcuDeviceType.Robot, true);   // 连接成功
            HcGrowlExtensions.ConnectionStatus(EnuMcuDeviceType.Shuttle, false); // 连接失败

            // 命令执行结果
            HcGrowlExtensions.CommandResult("PinSearch", true, "P1: 100, P2: 200", EnuMcuDeviceType.Robot);
            HcGrowlExtensions.CommandResult("MoveToPosition", false, "位置超出范围", EnuMcuDeviceType.Shuttle);
            HcGrowlExtensions.CommandResult("GlobalCommand", true);  // 不指定设备类型
        }

        /// <summary>
        /// 调试和开发相关示例
        /// </summary>
        public void DebugAndDevelopmentExamples()
        {
            // 调试消息（仅在开发模式下显示）
            HcGrowlExtensions.Debug("开始执行Robot命令");
            HcGrowlExtensions.Debug($"当前参数值: X={100}, Y={200}");

            // 性能统计
            var startTime = DateTime.Now;
            // ... 执行一些操作 ...
            var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
            HcGrowlExtensions.Performance("数据库查询", elapsed);

            // 进度显示
            for (int i = 1; i <= 10; i++)
            {
                // ... 处理第i项 ...
                HcGrowlExtensions.Progress("批量处理Wafer", i, 10);
                System.Threading.Thread.Sleep(100); // 模拟处理时间
            }
        }

        /// <summary>
        /// 高级功能示例
        /// </summary>
        public void AdvancedFeatureExamples()
        {
            // 询问对话框
            HcGrowlExtensions.Ask("确定要重置所有设备吗？", isConfirmed =>
            {
                if (isConfirmed)
                {
                    // 执行重置操作
                    ResetAllDevices();
                    HcGrowlExtensions.Success("设备重置完成");
                }
                else
                {
                    HcGrowlExtensions.Info("操作已取消");
                }
                return true; // 允许关闭对话框
            }, HcGrowlExtensions.Tokens.System, "确认重置", "取消");

            // 致命错误（保持显示直到用户手动关闭）
            HcGrowlExtensions.Fatal("系统发生致命错误，程序即将退出", staysOpen: true);

            // 自定义确认和取消按钮文本
            HcGrowlExtensions.Ask("是否保存当前配置？", result =>
            {
                if (result)
                    SaveConfiguration();
                return true;
            }, confirmStr: "保存", cancelStr: "不保存");
        }

        /// <summary>
        /// 消息清理示例
        /// </summary>
        public void MessageCleanupExamples()
        {
            // 清除特定Token的消息
            HcGrowlExtensions.Clear(HcGrowlExtensions.Tokens.Robot);

            // 清除特定设备的消息
            HcGrowlExtensions.ClearDevice(EnuMcuDeviceType.ChamberA);

            // 清除所有消息
            HcGrowlExtensions.ClearAll();
        }

        /// <summary>
        /// 线程安全示例
        /// </summary>
        public async Task ThreadSafetyExamples()
        {
            // 在后台线程中安全调用
            await Task.Run(() =>
            {
                // 模拟后台处理
                System.Threading.Thread.Sleep(2000);

                // 这些调用会自动切换到UI线程执行
                HcGrowlExtensions.Success("后台任务完成");
                HcGrowlExtensions.Debug("后台线程处理完毕");
            });

            // 在UI线程中调用（直接执行）
            HcGrowlExtensions.Info("UI线程消息");
        }

        /// <summary>
        /// 实际业务场景示例
        /// </summary>
        public async Task BusinessScenarioExamples()
        {
            try
            {
                // 1. 设备连接场景
                HcGrowlExtensions.Info("正在连接设备...", HcGrowlExtensions.Tokens.Connection);

                bool robotConnected = await ConnectRobotAsync();
                HcGrowlExtensions.ConnectionStatus(EnuMcuDeviceType.Robot, robotConnected);

                if (!robotConnected)
                {
                    HcGrowlExtensions.Error("Robot连接失败，请检查设备状态");
                    return;
                }

                // 2. 命令执行场景
                HcGrowlExtensions.Debug("开始执行PinSearch命令");

                var result = await ExecutePinSearchAsync();
                HcGrowlExtensions.CommandResult("PinSearch", result.Success, result.Message, EnuMcuDeviceType.Robot);

                // 3. 批量操作场景
                var wafers = GetWaferList();
                for (int i = 0; i < wafers.Count; i++)
                {
                    await ProcessWaferAsync(wafers[i]);
                    HcGrowlExtensions.Progress("处理Wafer", i + 1, wafers.Count);
                }

                HcGrowlExtensions.Success("所有Wafer处理完成", HcGrowlExtensions.Tokens.System);
            }
            catch (Exception ex)
            {
                HcGrowlExtensions.Fatal($"系统异常: {ex.Message}");
                HcGrowlExtensions.Debug($"异常详情: {ex}");
            }
        }

        /// <summary>
        /// 向后兼容性示例
        /// </summary>
        public void BackwardCompatibilityExamples()
        {
            // 旧的调用方式仍然有效
            HcGrowlExtensions.Success("操作成功", "", HcGrowlExtensions.WaitTime, false);
            HcGrowlExtensions.Error("操作失败", "");

            // 推荐的新调用方式
            HcGrowlExtensions.Success("操作成功");  // 使用智能时间
            HcGrowlExtensions.Error("操作失败");   // 使用智能时间
        }

        #region 综合示例

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public async Task RunAllExamples()
        {
            HcGrowlExtensions.Info("开始运行HcGrowlExtensions使用示例...", HcGrowlExtensions.Tokens.System);

            try
            {
                // 1. 基础消息示例
                HcGrowlExtensions.Info("1. 运行基础消息示例", HcGrowlExtensions.Tokens.System);
                BasicMessageExamples();
                await Task.Delay(2000);

                // 2. 设备相关消息示例
                HcGrowlExtensions.Info("2. 运行设备相关消息示例", HcGrowlExtensions.Tokens.System);
                DeviceMessageExamples();
                await Task.Delay(2000);

                // 3. 调试和开发相关示例
                HcGrowlExtensions.Info("3. 运行调试和开发相关示例", HcGrowlExtensions.Tokens.System);
                DebugAndDevelopmentExamples();
                await Task.Delay(2000);

                // 4. 高级功能示例
                HcGrowlExtensions.Info("4. 运行高级功能示例", HcGrowlExtensions.Tokens.System);
                AdvancedFeatureExamples();
                await Task.Delay(2000);

                // 5. 消息清理示例
                HcGrowlExtensions.Info("5. 运行消息清理示例", HcGrowlExtensions.Tokens.System);
                MessageCleanupExamples();
                await Task.Delay(1000);

                // 6. 线程安全示例
                HcGrowlExtensions.Info("6. 运行线程安全示例", HcGrowlExtensions.Tokens.System);
                await ThreadSafetyExamples();
                await Task.Delay(1000);

                // 7. 实际业务场景示例
                HcGrowlExtensions.Info("7. 运行实际业务场景示例", HcGrowlExtensions.Tokens.System);
                await BusinessScenarioExamples();
                await Task.Delay(1000);

                // 8. 向后兼容性示例
                HcGrowlExtensions.Info("8. 运行向后兼容性示例", HcGrowlExtensions.Tokens.System);
                BackwardCompatibilityExamples();
                await Task.Delay(1000);

                HcGrowlExtensions.Success("所有HcGrowlExtensions示例运行完成！", HcGrowlExtensions.Tokens.System);
            }
            catch (Exception ex)
            {
                HcGrowlExtensions.Fatal($"运行示例失败: {ex.Message}");
                HcGrowlExtensions.Debug($"异常详情: {ex}");
            }
        }

        #endregion 综合示例

        #region 模拟方法

        private void ResetAllDevices()
        {
            /* 模拟重置设备 */
        }

        private void SaveConfiguration()
        {
            /* 模拟保存配置 */
        }

        private async Task<bool> ConnectRobotAsync()
        {
            await Task.Delay(1000); return true;
        }

        private async Task<(bool Success, string Message)> ExecutePinSearchAsync()
        {
            await Task.Delay(500);
            return (true, "P1: 100, P2: 200");
        }

        private System.Collections.Generic.List<object> GetWaferList()
        {
            return new();
        }

        private async Task ProcessWaferAsync(object wafer)
        {
            await Task.Delay(100);
        }

        #endregion 模拟方法
    }
}