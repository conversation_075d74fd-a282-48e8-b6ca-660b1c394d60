using System;
using System.Threading.Tasks;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// R轴零位状态获取最佳实践示例
    /// 演示如何使用混合方式确保状态准确性和可靠性
    /// </summary>
    public class RAxisZeroStatus_BestPractice_Example
    {
        /// <summary>
        /// 运行所有R轴零位状态最佳实践示例
        /// </summary>
        public static async Task RunAllExamples()
        {
            Console.WriteLine("=== R轴零位状态获取最佳实践示例开始 ===");

            try
            {
                // 示例1：智能状态获取方法
                await Example1_SmartStatusRetrieval();

                // 示例2：状态同步更新方法
                await Example2_StatusSynchronization();

                Console.WriteLine("=== R轴零位状态获取最佳实践示例完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 运行R轴零位状态示例失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 示例1：智能状态获取方法
        /// 优先使用实时状态表，本地状态作为备用
        /// </summary>
        private static async Task Example1_SmartStatusRetrieval()
        {
            Console.WriteLine("--- 示例1：智能状态获取方法 ---");

            try
            {
                // 方法1：直接从实时状态表获取（推荐）
                var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
                if (robotStatus != null)
                {
                    bool realTimeStatus = robotStatus.RAxisIsZeroPosition;
                    Console.WriteLine($"实时状态表 - R轴零位状态: {realTimeStatus}");
                }

                // 方法2：使用智能获取方法（最佳实践）
                bool smartStatus = GetRAxisZeroStatusSmart();
                Console.WriteLine($"智能获取方法 - R轴零位状态: {smartStatus}");

                // 方法3：带容错的获取方法
                bool safeStatus = GetRAxisZeroStatusSafe();
                Console.WriteLine($"安全获取方法 - R轴零位状态: {safeStatus}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 示例1执行失败: {ex.Message}");
            }

            await Task.Delay(100); // 模拟异步操作
        }

        /// <summary>
        /// 示例2：状态同步更新方法
        /// 演示如何在操作后正确更新状态
        /// </summary>
        private static async Task Example2_StatusSynchronization()
        {
            Console.WriteLine("--- 示例2：状态同步更新方法 ---");

            try
            {
                // 模拟R轴操作前的状态检查
                bool beforeOperation = GetRAxisZeroStatusSmart();
                Console.WriteLine($"操作前R轴零位状态: {beforeOperation}");

                // 模拟R轴归零操作
                Console.WriteLine("模拟执行R轴归零操作...");
                await Task.Delay(500); // 模拟操作时间

                Console.WriteLine($"操作后R轴零位状态: {beforeOperation}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 示例2执行失败: {ex.Message}");
            }
        }



        #region 实用方法实现

        /// <summary>
        /// 智能获取R轴零位状态 - 优先使用实时状态表
        /// </summary>
        private static bool GetRAxisZeroStatusSmart()
        {
            try
            {
                var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
                if (robotStatus != null)
                {
                    return robotStatus.RAxisIsZeroPosition;
                }

                // 如果实时状态表不可用，使用默认值
                Console.WriteLine("[DEBUG] 实时状态表不可用，返回默认状态");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 获取R轴零位状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 安全获取R轴零位状态 - 带完整错误处理
        /// </summary>
        private static bool GetRAxisZeroStatusSafe()
        {
            try
            {
                var interlock = SS200InterLockMain.Instance;
                if (interlock?.SubsystemStatus?.Robot?.Status != null)
                {
                    return interlock.SubsystemStatus.Robot.Status.RAxisIsZeroPosition;
                }

                Console.WriteLine("[WARN] SS200InterLockMain或Robot状态不可用");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 获取R轴零位状态异常: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
