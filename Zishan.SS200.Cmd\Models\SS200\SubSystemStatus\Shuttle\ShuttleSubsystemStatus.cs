using System;
using CommunityToolkit.Mvvm.ComponentModel;
using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle
{
    /// <summary>
    /// Shuttle子系统状态模型
    /// </summary>
    public partial class ShuttleSubsystemStatus : ObservableObject
    {
        #region 1. Shuttle状态 (SHTL STATUS)

        /// <summary>
        /// Shuttle状态 (MSD1-MSD3)
        /// </summary>
        [ObservableProperty]
        private EnuShuttleStatus shuttleStatus = EnuShuttleStatus.None;

        #endregion 1. Shuttle状态 (SHTL STATUS)

        #region 2. SSC6配置

        /// <summary>
        /// SSC6配置模式 - 影响Shuttle位置状态和门巢状态的逻辑判断
        /// </summary>
        [ObservableProperty]
        private EnuSSC6Config ssc6Config = EnuSSC6Config.SMIF;

        #endregion 2. SSC6配置

        #region 3. Shuttle位置状态 (SSC6配置相关)

        /// <summary>
        /// Shuttle位置状态 (SSD1-SSD7) - SSD1~SSD7状态不能共存
        /// 注意：SSD3-SSD6的I/O条件根据SSC6配置有所不同
        /// </summary>
        [ObservableProperty]
        private EnuShuttlePositionStatus shuttlePositionStatus = EnuShuttlePositionStatus.None;

        /// <summary>
        /// 晶圆盒门和巢状态 (SSD8-SSD13) - SSD8~SSD13状态不能共存
        /// 注意：SSC6=FIXED时，SSD11-SSD13为N/A（不适用）
        /// </summary>
        [ObservableProperty]
        private EnuCassetteDoorNestStatus cassetteDoorNestStatus = EnuCassetteDoorNestStatus.None;

        /*
        // 设置门和巢状态（根据SSC6配置）
        if (shuttleStatus.Ssc6Config == EnuSSC6Config.SMIF)
        {
            // SMIF模式下可以使用所有SSD8-SSD13状态
            shuttleStatus.CassetteDoorNestStatus = EnuCassetteDoorNestStatus.CassetteNestBetween1;
        }
        else
        {
            // FIXED模式下只能使用SSD8-SSD10状态
            shuttleStatus.CassetteDoorNestStatus = EnuCassetteDoorNestStatus.CassetteDoorOpen;
        }
        */

        #endregion 3. Shuttle位置状态 (SSC6配置相关)

        #region 4. Shuttle阀门状态 (SHTL valve status)

        /// <summary>
        /// Shuttle ISO阀门状态 (SSD14-SSD15: DI22, DI23)
        /// </summary>
        [ObservableProperty]
        private EnuShuttleValveStatus shuttleIsoValveStatus = EnuShuttleValveStatus.None;

        /// <summary>
        /// Shuttle XV阀门状态 (SSD16-SSD17: DI24, DI25)
        /// </summary>
        [ObservableProperty]
        private EnuShuttleValveStatus shuttleXvValveStatus = EnuShuttleValveStatus.None;

        /// <summary>
        /// Shuttle回填阀门状态 (SSD18-SSD19: DO7)
        /// </summary>
        [ObservableProperty]
        private EnuShuttleValveStatus shuttleBackfillValveStatus = EnuShuttleValveStatus.None;

        /// <summary>
        /// 负载锁排气阀门状态 (SSD20-SSD21: DO9)
        /// </summary>
        [ObservableProperty]
        private EnuShuttleValveStatus loadlockBleedValveStatus = EnuShuttleValveStatus.None;

        /// <summary>
        /// 负载锁回填阀门状态 (SSD22-SSD23: DO10)
        /// </summary>
        [ObservableProperty]
        private EnuShuttleValveStatus loadlockBackfillValveStatus = EnuShuttleValveStatus.None;

        #endregion 4. Shuttle阀门状态 (SHTL valve status)

        #region 5. 批次状态 (lot status)

        /// <summary>
        /// Shuttle1晶圆盒1批次状态 (LSD1: DI6)
        /// </summary>
        [ObservableProperty]
        private EnuLotStatus shuttle1Cassette1LotStatus = EnuLotStatus.NoLot;

        /// <summary>
        /// Shuttle1晶圆盒2批次状态 (LSD2: DI7)
        /// </summary>
        [ObservableProperty]
        private EnuLotStatus shuttle1Cassette2LotStatus = EnuLotStatus.NoLot;

        /// <summary>
        /// Shuttle2晶圆盒1批次状态 (LSD3: DI8)
        /// </summary>
        [ObservableProperty]
        private EnuLotStatus shuttle2Cassette1LotStatus = EnuLotStatus.NoLot;

        /// <summary>
        /// Shuttle2晶圆盒2批次状态 (LSD4: DI9)
        /// </summary>
        [ObservableProperty]
        private EnuLotStatus shuttle2Cassette2LotStatus = EnuLotStatus.NoLot;

        #endregion 5. 批次状态 (lot status)

        #region 6. 槽位状态管理器

        /// <summary>
        /// 槽位状态管理器 - 管理所有槽位的晶圆状态
        /// </summary>
        [ObservableProperty]
        private ShuttleSlotStatusManager slotStatusManager = new();

        #endregion 6. 槽位状态管理器

        public override string ToString()
        {
            var sb = new System.Text.StringBuilder();

            // 1. Shuttle状态
            sb.AppendLine($"ShuttleStatus = {ShuttleStatus}");

            // 2. SSC6配置
            sb.AppendLine($"SSC6Config = {Ssc6Config}");

            // 3. 位置状态
            sb.AppendLine($"ShuttlePositionStatus = {ShuttlePositionStatus}");
            sb.AppendLine($"CassetteDoorNestStatus = {CassetteDoorNestStatus}");

            // 4. 阀门状态
            sb.AppendLine($"ShuttleIsoValveStatus = {ShuttleIsoValveStatus}");
            sb.AppendLine($"ShuttleXvValveStatus = {ShuttleXvValveStatus}");
            sb.AppendLine($"ShuttleBackfillValveStatus = {ShuttleBackfillValveStatus}");
            sb.AppendLine($"LoadlockBleedValveStatus = {LoadlockBleedValveStatus}");
            sb.AppendLine($"LoadlockBackfillValveStatus = {LoadlockBackfillValveStatus}");

            // 5. 批次状态
            sb.AppendLine($"Shuttle1Cassette1LotStatus = {Shuttle1Cassette1LotStatus}");
            sb.AppendLine($"Shuttle1Cassette2LotStatus = {Shuttle1Cassette2LotStatus}");
            sb.AppendLine($"Shuttle2Cassette1LotStatus = {Shuttle2Cassette1LotStatus}");
            sb.AppendLine($"Shuttle2Cassette2LotStatus = {Shuttle2Cassette2LotStatus}");

            // 6. 槽位状态
            sb.AppendLine($"SlotStatusManager = {SlotStatusManager != null}");

            return sb.ToString();
        }
    }
}