using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using log4net;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using Zishan.SS200.Cmd.Enums.Basic;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// SS200InterLockMain使用示例
    /// 演示如何使用单例模式的统一访问入口
    /// </summary>
    public class SS200InterLockMainUsageExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(SS200InterLockMainUsageExample));
        private readonly SS200InterLockMain _ss200;
        private Timer _monitorTimer;

        public SS200InterLockMainUsageExample()
        {
            // 获取单例实例
            _ss200 = SS200InterLockMain.Instance;
        }

        #region IO接口访问示例

        /// <summary>
        /// 演示IO接口访问
        /// </summary>
        public void DemonstrateIOAccess()
        {
            _logger.Info("=== IO接口访问示例 ===");

            try
            {
                // Robot IO访问
                var robotDI1 = _ss200.IOInterface.Robot.RDI1_PaddleSensor1Left;
                _logger.Info($"Robot DI1 - Value: {robotDI1.Value}, Content: {robotDI1.Content}");
                _logger.Info($"Robot DI1 - IsActive: {robotDI1.IsActive}, ValueNullable: {robotDI1.ValueNullable}");

                var robotDI2 = _ss200.IOInterface.Robot.RDI2_PaddleSensor2Right;
                _logger.Info($"Robot DI2 - Value: {robotDI2.Value}, Content: {robotDI2.Content}");

                // Chamber A IO访问
                var chamberADI12 = _ss200.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor;
                _logger.Info($"Chamber A DI12 - Value: {chamberADI12.Value}, Content: {chamberADI12.Content}");

                var chamberADI13 = _ss200.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor;
                _logger.Info($"Chamber A DI13 - Value: {chamberADI13.Value}, Content: {chamberADI13.Content}");

                // Chamber B IO访问
                var chamberBDI12 = _ss200.IOInterface.ChamberB.PDI12_SlitDoorOpenSensor;
                _logger.Info($"Chamber B DI12 - Value: {chamberBDI12.Value}, Content: {chamberBDI12.Content}");

                // Shuttle IO访问
                var shuttleDI6 = _ss200.IOInterface.Shuttle.SDI6_PresentSensorCassette1;
                _logger.Info($"Shuttle DI6 - Value: {shuttleDI6.Value}, Content: {shuttleDI6.Content}");

                var shuttleDI7 = _ss200.IOInterface.Shuttle.SDI7_PresentSensorCassette2;
                _logger.Info($"Shuttle DI7 - Value: {shuttleDI7.Value}, Content: {shuttleDI7.Content}");
            }
            catch (Exception ex)
            {
                _logger.Error($"IO接口访问示例失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 报警信息访问示例

        /// <summary>
        /// 演示报警信息访问
        /// </summary>
        public void DemonstrateAlarmAccess()
        {
            _logger.Info("=== 报警信息访问示例 ===");

            try
            {
                // Robot报警访问
                var robotAlarmRA1 = _ss200.AlarmCode.Robot.RA1_SystemBusyReject;
                _logger.Info($"Robot RA1 - Code: {robotAlarmRA1.Code}");
                _logger.Info($"Robot RA1 - Content: {robotAlarmRA1.Content}");
                _logger.Info($"Robot RA1 - ChsContent: {robotAlarmRA1.ChsContent}");
                _logger.Info($"Robot RA1 - Cause: {robotAlarmRA1.Cause}");
                _logger.Info($"Robot RA1 - Item: {robotAlarmRA1.Item}");

                var robotAlarmRA2 = _ss200.AlarmCode.Robot.RA2_SystemAlarmReject;
                _logger.Info($"Robot RA2 - Content: {robotAlarmRA2.Content}, ChsContent: {robotAlarmRA2.ChsContent}");

                var robotAlarmRA3 = _ss200.AlarmCode.Robot.RA3_RAxisNotHomeError;
                _logger.Info($"Robot RA3 - Content: {robotAlarmRA3.Content}, ChsContent: {robotAlarmRA3.ChsContent}");

                // Chamber报警访问
                var chamberAlarmPAC1 = _ss200.AlarmCode.ChamberA.PAC1_SystemAbnormalReject;
                _logger.Info($"Chamber PAC1 - Content: {chamberAlarmPAC1.Content}, ChsContent: {chamberAlarmPAC1.ChsContent}");

                var chamberAlarmPAC2 = _ss200.AlarmCode.ChamberA.PAC2_SystemBusyReject;
                _logger.Info($"Chamber PAC2 - Content: {chamberAlarmPAC2.Content}, ChsContent: {chamberAlarmPAC2.ChsContent}");
            }
            catch (Exception ex)
            {
                _logger.Error($"报警信息访问示例失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 配置信息访问示例

        /// <summary>
        /// 演示配置信息访问
        /// </summary>
        public void DemonstrateConfigAccess()
        {
            _logger.Info("=== 配置信息访问示例 ===");

            try
            {
                // 位置值配置访问
                var rp1Config = _ss200.SubsystemConfigure.Robot.RP1_TAxisSmoothToCHA;
                if (rp1Config != null)
                {
                    _logger.Info($"RP1 - Value: {rp1Config.Value}, Content: {rp1Config.Content}");
                    _logger.Info($"RP1 - Unit: {rp1Config.Unit}, Code: {rp1Config.Code}");
                    _logger.Info($"RP1 - AxisType: {rp1Config.AxisType}, Range: {rp1Config.Range}");
                }
                else
                {
                    _logger.Warn("RP1配置未找到");
                }

                var rp2Config = _ss200.SubsystemConfigure.Robot.RP2_TAxisSmoothToCHB;
                if (rp2Config != null)
                {
                    _logger.Info($"RP2 - Value: {rp2Config.Value}, Content: {rp2Config.Content}");
                }
                else
                {
                    _logger.Warn("RP2配置未找到");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"配置信息访问示例失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 状态信息访问示例

        /// <summary>
        /// 演示状态信息访问
        /// </summary>
        public void DemonstrateStatusAccess()
        {
            _logger.Info("=== 状态信息访问示例 ===");

            try
            {
                // Robot状态访问 - 使用新的直接访问方式
                var robotStatus = _ss200.SubsystemStatus.Robot.Status;
                _logger.Info($"Robot Status: {robotStatus?.EnuRobotStatus}");
                _logger.Info($"T-Axis Smooth Destination: {robotStatus?.EnuTAxisSmoothDestination}");
                _logger.Info($"T-Axis Nose Destination: {robotStatus?.EnuTAxisNoseDestination}");

                // Chamber A状态访问 - 使用新的直接访问方式
                var chamberAStatus = _ss200.SubsystemStatus.ChamberA.Status;
                _logger.Info($"Chamber A Slit Door: {chamberAStatus?.SlitDoorStatus}");
                _logger.Info($"Chamber A Lift Pin: {chamberAStatus?.LiftPinStatus}");

                // Chamber B状态访问 - 使用新的直接访问方式
                var chamberBStatus = _ss200.SubsystemStatus.ChamberB.Status;
                _logger.Info($"Chamber B Slit Door: {chamberBStatus?.SlitDoorStatus}");
                _logger.Info($"Chamber B Lift Pin: {chamberBStatus?.LiftPinStatus}");
            }
            catch (Exception ex)
            {
                _logger.Error($"状态信息访问示例失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 状态更新示例

        /// <summary>
        /// 演示状态更新
        /// </summary>
        public void DemonstrateStatusUpdate()
        {
            _logger.Info("=== 状态更新示例 ===");

            try
            {
                // 更新Robot状态
                var newRobotStatus = new RobotSubsystemStatus
                {
                    EnuRobotStatus = EnuRobotStatus.Busy,
                    EnuTAxisSmoothDestination = EnuLocationStationType.ChamberA,
                    TAxisIsZeroPosition = false,
                    RAxisIsZeroPosition = true
                };

                _ss200.UpdateRobotStatus(newRobotStatus);
                _logger.Info("Robot状态已更新");

                // 验证更新后的状态 - 使用新的直接访问方式
                var updatedRobotStatus = _ss200.SubsystemStatus.Robot.Status;
                _logger.Info($"更新后 - Robot Status: {updatedRobotStatus?.EnuRobotStatus}, T-Axis Destination: {updatedRobotStatus?.EnuTAxisSmoothDestination}");

                // 更新Chamber A状态 - 修复：ChamberSubsystemStatus是抽象类，需要使用具体子类
                var newChamberAStatus = new ChamberASubsystemStatus
                {
                    SlitDoorStatus = EnuSlitDoorStatus.Open,
                    LiftPinStatus = EnuLiftPinStatus.Up,
                    TriggerStatus = EnuTriggerStatus.NoAlarm,
                    RunStatus = EnuRunStatus.Idle
                };

                _ss200.UpdateChamberAStatus(newChamberAStatus);
                _logger.Info("Chamber A状态已更新");

                // 验证更新后的状态 - 使用新的直接访问方式
                var updatedChamberAStatus = _ss200.SubsystemStatus.ChamberA.Status;
                _logger.Info($"更新后 - Slit Door: {updatedChamberAStatus?.SlitDoorStatus}, Lift Pin: {updatedChamberAStatus?.LiftPinStatus}");
            }
            catch (Exception ex)
            {
                _logger.Error($"状态更新示例失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 高级操作示例

        /// <summary>
        /// 演示高级操作
        /// </summary>
        public void DemonstrateAdvancedOperations()
        {
            _logger.Info("=== 高级操作示例 ===");

            try
            {
                // 获取CoilStatusHelper进行高级操作
                var coilHelper = _ss200.GetCoilStatusHelper();
                bool customValue = coilHelper.GetCoilValue(Enums.EnuMcuDeviceType.Robot, EnuRobotDICodes.RDI1_PaddleSensor1Left);
                _logger.Info($"通过CoilHelper获取的值: {customValue}");

                // 获取MCU命令服务
                var mcuService = _ss200.GetMcuCmdService();
                var deviceStatus = mcuService.GetAllDeviceStatus();
                _logger.Info($"设备状态数量: {deviceStatus.Count}");

                foreach (var status in deviceStatus)
                {
                    _logger.Info($"设备 {status.Key}: {status.Value}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"高级操作示例失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 状态监控示例

        /// <summary>
        /// 启动状态监控
        /// </summary>
        public void StartStatusMonitoring()
        {
            _logger.Info("启动状态监控...");
            _monitorTimer = new Timer(MonitorStatus, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
        }

        /// <summary>
        /// 停止状态监控
        /// </summary>
        public void StopStatusMonitoring()
        {
            _monitorTimer?.Dispose();
            _logger.Info("状态监控已停止");
        }

        /// <summary>
        /// 监控状态回调
        /// </summary>
        private void MonitorStatus(object state)
        {
            try
            {
                // 监控Robot状态 - 使用新的直接访问方式
                var robotStatus = _ss200.SubsystemStatus.Robot.Status;
                var robotDI1 = _ss200.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
                var robotDI2 = _ss200.IOInterface.Robot.RDI2_PaddleSensor2Right.Value;

                _logger.Info($"[监控] Robot - Status: {robotStatus?.EnuRobotStatus}, Smooth Paddle: {robotDI1}, Nose Paddle: {robotDI2}");

                // 监控Chamber A状态 - 使用新的直接访问方式
                var chamberAStatus = _ss200.SubsystemStatus.ChamberA.Status;

                _logger.Info($"[监控] Chamber A - Slit Door: {chamberAStatus?.SlitDoorStatus}, Lift Pin: {chamberAStatus?.LiftPinStatus}");

                // 监控Shuttle状态
                var shuttle1Cassette1 = _ss200.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Value;
                var shuttle1Cassette2 = _ss200.IOInterface.Shuttle.SDI7_PresentSensorCassette2.Value;

                _logger.Info($"[监控] Shuttle - Cassette1: {shuttle1Cassette1}, Cassette2: {shuttle1Cassette2}");
            }
            catch (Exception ex)
            {
                _logger.Error($"状态监控失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 综合示例

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public async Task RunAllExamples()
        {
            _logger.Info("开始运行SS200InterLockMain使用示例...");

            try
            {
                // 1. IO接口访问示例
                DemonstrateIOAccess();
                await Task.Delay(1000);

                // 2. 报警信息访问示例
                DemonstrateAlarmAccess();
                await Task.Delay(1000);

                // 3. 配置信息访问示例
                DemonstrateConfigAccess();
                await Task.Delay(1000);

                // 4. 状态信息访问示例
                DemonstrateStatusAccess();
                await Task.Delay(1000);

                // 5. 状态更新示例
                DemonstrateStatusUpdate();
                await Task.Delay(1000);

                // 6. 高级操作示例
                DemonstrateAdvancedOperations();
                await Task.Delay(1000);

                // 7. 启动状态监控（运行5秒后停止）
                StartStatusMonitoring();
                await Task.Delay(5000);
                StopStatusMonitoring();

                _logger.Info("所有示例运行完成！");
            }
            catch (Exception ex)
            {
                _logger.Error($"运行示例失败: {ex.Message}", ex);
            }
        }

        #endregion
    }
}
