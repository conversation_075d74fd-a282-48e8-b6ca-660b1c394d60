using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle
{
    /// <summary>
    /// Shuttle位置状态枚举 (SSD1-SSD7)
    /// 注意：SSD1~SSD7状态不能共存
    /// </summary>
    public enum EnuShuttlePositionStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// Shuttle上升/Shuttle1外部位置 (SSD1: DI13=1 DI14=0 DI15=0 DI16=0)
        /// </summary>
        [Description("Shuttle上升/Shuttle1外部位置")]
        ShuttleUpShuttle1Outer = 1,

        /// <summary>
        /// Shuttle上升/Shuttle2外部位置 (SSD2: DI13=0 DI14=1 DI15=0 DI16=0)
        /// </summary>
        [Description("Shuttle上升/Shuttle2外部位置")]
        ShuttleUpShuttle2Outer = 2,

        /// <summary>
        /// Shuttle下降/Shuttle1外部位置
        /// SSC6=SMIF: (SSD3: DI13=1 DI14=0 DI15=0 DI16=1 DI4=0 DI5=0)
        /// SSC6=FIXED: (SSD3: DI13=1 DI14=0 DI15=0 DI16=1)
        /// </summary>
        [Description("Shuttle下降/Shuttle1外部位置")]
        ShuttleDownShuttle1Outer = 3,

        /// <summary>
        /// Shuttle下降/Shuttle2外部位置
        /// SSC6=SMIF: (SSD4: DI13=0 DI14=1 DI15=0 DI16=1 DI4=0 DI5=0)
        /// SSC6=FIXED: (SSD4: DI13=0 DI14=1 DI15=0 DI16=1)
        /// </summary>
        [Description("Shuttle下降/Shuttle2外部位置")]
        ShuttleDownShuttle2Outer = 4,

        /// <summary>
        /// Shuttle上下之间/Shuttle1外部位置
        /// SSC6=SMIF: (SSD5: DI13=1 DI14=0 DI15=1 DI16=0 DI4=0 DI5=0)
        /// SSC6=FIXED: (SSD5: DI13=1 DI14=0 DI15=1 DI16=0)
        /// </summary>
        [Description("Shuttle上下之间/Shuttle1外部位置")]
        ShuttleUpDownBetweenShuttle1Outer = 5,

        /// <summary>
        /// Shuttle上下之间/Shuttle2外部位置
        /// SSC6=SMIF: (SSD6: DI13=0 DI14=1 DI15=1 DI16=0 DI4=0 DI5=0)
        /// SSC6=FIXED: (SSD6: DI13=0 DI14=1 DI15=1 DI16=0)
        /// </summary>
        [Description("Shuttle上下之间/Shuttle2外部位置")]
        ShuttleUpDownBetweenShuttle2Outer = 6,

        /// <summary>
        /// Shuttle下降且旋转之间位置 (SSD7: DI13=0 DI14=0 DI15=0 DI16=1)
        /// </summary>
        [Description("Shuttle下降且旋转之间位置")]
        ShuttleDownRotationBetween = 7
    }
}