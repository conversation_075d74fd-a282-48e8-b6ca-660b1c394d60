﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.Pkcs;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.Recipe
{
    public class RecipeSeq
    {
        public List<EnuRecipeChamberName> ChamberNameList { get; set; }

        public List<RecipeInfo> RecipeInfoList { get; set; }

        public RecipeSeq(List<EnuRecipeChamberName> enuChamberNameList, List<RecipeInfo> recipeInfoList)
        {
            ChamberNameList = enuChamberNameList;
            RecipeInfoList = recipeInfoList;
        }
    }
}