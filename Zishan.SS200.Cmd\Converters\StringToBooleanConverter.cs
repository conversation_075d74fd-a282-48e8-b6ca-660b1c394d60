using System;
using System.Globalization;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 字符串与布尔值相互转换的转换器
    /// </summary>
    public class StringToBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string strValue)
            {
                if (bool.TryParse(strValue, out bool result))
                {
                    return result;
                }
                
                // 处理一些常见的字符串表示
                switch (strValue.ToLower())
                {
                    case "true":
                    case "1":
                    case "yes":
                    case "是":
                    case "开":
                    case "启用":
                        return true;
                    case "false":
                    case "0":
                    case "no":
                    case "否":
                    case "关":
                    case "禁用":
                        return false;
                }
            }
            
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue.ToString();
            }
            
            return "False";
        }
    }
}
