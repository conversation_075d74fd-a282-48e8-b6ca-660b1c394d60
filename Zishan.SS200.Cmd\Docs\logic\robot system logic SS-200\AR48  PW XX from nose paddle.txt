﻿AR46
PW XX
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			shuttle position status review
SSD1~SSD7
				SSD1
					shuttle 2 exist status review
SS45 or SS46
						SS45 enable
							cassette slot xx status review
LSS3/LSS4
								LSS3 xx=0 and LSS4 xx=0
or
LSS3 xx=2 and LSS4 xx=2
									paddle status review
RS54
										RS54=1
and
LSS7 or/and LSS8 (XX)≠PW (XX)
											RA17 ALARM
										RS54=1
and
LSS7 or/and LSS8 (XX)=PW (XX)
											AR8
T-axis nose to cassette
												AR37
Move Z-axis height nose put slot xx
													AR18
R-axis nose cassette extend
														AR35
Move Z-axis height nose get slot xx
															AR69
wafer status exchange nose paddle to cassette slot xx
																AR19
R-axis zero position
																	AR70
wafer status nose paddle confirm
																		RDI1=0 RDI2=0
																			command done
																		RDI1=1 RDI2=0
																			RA47 ALARM
																		RDI1=0 RDI2=1
																			RA48 ALARM
																		RDI1=1 RDI2=1
																			RA49 ALARM
										RS54=0
											AR8
T-axis nose to cassette
												AR37
Move Z-axis height nose put slot xx
													AR18
R-axis nose cassette extend
														AR35
Move Z-axis height nose get slot xx
															AR69
wafer status exchange nose paddle to cassette slot xx
																AR19
R-axis zero position
																	AR70
wafer status nose paddle confirm
																		RDI1=0 RDI2=0
																			command done
																		RDI1=1 RDI2=0
																			RA47 ALARM
																		RDI1=0 RDI2=1
																			RA48 ALARM
																		RDI1=1 RDI2=1
																			RA49 ALARM
								others status
									RA36 ALARM
						SS46 disable
							RA24 ALARM
				SSD2
					shuttle 1 exist status review
SS43 or SS44
						SS43 enable
							cassette slot xx status review
LSS1/LSS2
								LSS1 xx=0 and LSS2 xx=0
or
LSS1 xx=2 and LSS2 xx=2
									paddle status review
RS54
										RS54=1
and
LSS7 or/and LSS8 (XX)≠PW (XX)
											RA17 ALARM
										RS54=1
and
LSS7 or/and LSS8 (XX)=PW (XX)
											AR8
T-axis nose to cassette
												AR37
Move Z-axis height nose put slot xx
													AR18
R-axis nose cassette extend
														AR35
Move Z-axis height nose get slot xx
															AR69
wafer status exchange nose paddle to cassette slot xx
																AR19
R-axis zero position
																	AR70
wafer status nose paddle confirm
																		RDI1=0 RDI2=0
																			command done
																		RDI1=1 RDI2=0
																			RA47 ALARM
																		RDI1=0 RDI2=1
																			RA48 ALARM
																		RDI1=1 RDI2=1
																			RA49 ALARM
										RS54=0
											AR8
T-axis nose to cassette
												AR37
Move Z-axis height nose put slot xx
													AR18
R-axis nose cassette extend
														AR35
Move Z-axis height nose get slot xx
															AR69
wafer status exchange nose paddle to cassette slot xx
																AR19
R-axis zero position
																	AR70
wafer status nose paddle confirm
																		RDI1=0 RDI2=0
																			command done
																		RDI1=1 RDI2=0
																			RA47 ALARM
																		RDI1=0 RDI2=1
																			RA48 ALARM
																		RDI1=1 RDI2=1
																			RA49 ALARM
								others status
									RA36 ALARM
						SS44 disable
							RA23 ALARM
				others status
					RA25 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM