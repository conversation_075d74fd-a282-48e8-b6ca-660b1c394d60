# PinSearch取消功能支持

## 📋 概述

为了解决 `PinSearchAsync` 方法内部无法取消的问题，我们在整个调用链中添加了 `CancellationToken` 支持，使用户能够在需要时取消正在执行的 PinSearch 操作。

## 🔧 实现详情

### 修改的文件和方法

1. **CmdTaskHandlel.cs**
   - 添加 `TaskHandleResult.Cancelled` 枚举值
   - 为 `ExecuteCommandAsync<T>` 方法添加 `CancellationToken` 参数重载
   - 在轮询循环中添加取消令牌检查

2. **S200McuCmdService.cs**
   - 为 `McuDevice.Run<T>` 方法添加 `CancellationToken` 参数重载
   - 在命令执行结果处理中添加 `Cancelled` 状态处理
   - 更新 `TaskHandleResult` 枚举

3. **ModbusCommandUtility.cs**
   - 为 `ExecuteDeviceCommand` 方法添加 `CancellationToken` 参数重载
   - 为 `ExecuteCommandAsync<TEnum>` 方法添加取消令牌支持

4. **S200McuCmdServiceExtensions.cs**
   - 为 `ExecuteDeviceCommandAsync` 方法添加 `CancellationToken` 参数重载

5. **RobotWaferOperationsExtensions.cs**
   - 为 `PinSearchAsync` 方法添加 `CancellationToken` 参数重载
   - 修改内部 `ExecuteDeviceCommandAsync` 调用以传递取消令牌

### 关键改进点

#### 1. 轮询循环中的取消检查
```csharp
while (true)
{
    // 检查取消令牌
    cancellationToken.ThrowIfCancellationRequested();
    
    // 检查是否超时
    if ((DateTime.Now - startTime).TotalMilliseconds > timeout)
    {
        return TaskHandleResult.Timeout;
    }
    
    // ... 其他逻辑
    
    // 指数退避延迟（支持取消）
    await Task.Delay(delayMs, cancellationToken);
}
```

#### 2. 异常处理
```csharp
catch (OperationCanceledException)
{
    _logger.Info($"命令 {cmdIndex} 执行被取消");
    return TaskHandleResult.Cancelled;
}
```

#### 3. 向后兼容性
所有原有方法保持不变，新增的重载方法内部调用原方法并传入 `CancellationToken.None`：
```csharp
public static async Task<(bool Success, string Message, int PinSearchP1Value, int PinSearchP2Value)> 
    PinSearchAsync(this IS200McuCmdService cmdService, EnuRobotEndType endType, bool isTRZAxisReturnZeroed = false)
{
    return await PinSearchAsync(cmdService, endType, isTRZAxisReturnZeroed, CancellationToken.None);
}
```

## 🚀 使用方法

### 基本用法
```csharp
// 创建取消令牌源
var cts = new CancellationTokenSource();

try
{
    // 执行PinSearch操作（支持取消）
    var result = await _mcuCmdService.PinSearchAsync(
        EnuRobotEndType.Smooth, 
        false, 
        cts.Token);
        
    if (result.Success)
    {
        Console.WriteLine($"PinSearch成功: {result.Message}");
    }
}
catch (OperationCanceledException)
{
    Console.WriteLine("PinSearch操作已被取消");
}
finally
{
    cts?.Dispose();
}
```

### 超时自动取消
```csharp
var cts = new CancellationTokenSource();
cts.CancelAfter(TimeSpan.FromSeconds(10)); // 10秒后自动取消

var result = await _mcuCmdService.PinSearchAsync(
    EnuRobotEndType.Nose, 
    false, 
    cts.Token);
```

### 手动取消
```csharp
var cts = new CancellationTokenSource();

// 在另一个线程或事件处理中
cts.Cancel(); // 立即取消操作
```

### UI中的取消按钮
```csharp
private CancellationTokenSource _pinSearchCts;

private async void OnStartPinSearch()
{
    _pinSearchCts = new CancellationTokenSource();
    
    try
    {
        var result = await _mcuCmdService.PinSearchAsync(
            EnuRobotEndType.Smooth, 
            false, 
            _pinSearchCts.Token);
    }
    catch (OperationCanceledException)
    {
        // 处理取消
    }
    finally
    {
        _pinSearchCts?.Dispose();
        _pinSearchCts = null;
    }
}

private void OnCancelPinSearch()
{
    _pinSearchCts?.Cancel();
}
```

## ⚠️ 注意事项

1. **协作式取消**: 取消操作是协作式的，需要代码主动检查取消令牌。取消不会立即停止硬件动作，只是停止等待和轮询。

2. **异常处理**: 取消操作会抛出 `OperationCanceledException` 异常，需要适当处理。

3. **资源清理**: 务必在 `finally` 块中释放 `CancellationTokenSource` 资源。

4. **向后兼容**: 所有原有的方法调用仍然有效，不需要修改现有代码。

5. **取消时机**: 取消检查主要在以下时机进行：
   - 轮询循环开始时
   - `Task.Delay` 期间
   - 方法入口处（某些方法）

## 🧪 测试示例

详细的测试示例请参考：`Zishan.SS200.Cmd\Docs\Examples\PinSearchCancellationExample.cs`

该示例包含：
- 基本取消功能演示
- 手动取消操作
- 批量操作取消
- 最佳实践指导

## 📈 性能影响

添加取消令牌支持对性能的影响微乎其微：
- 取消检查是轻量级操作
- 只在必要时进行检查
- 不影响正常执行路径的性能

## 🔄 后续改进

1. 可以考虑在更多地方添加取消检查点
2. 可以添加进度报告功能
3. 可以考虑支持部分取消（只取消某些步骤）

## 📝 相关文档

- [PinSearch失败处理机制.md](../Features/PinSearch失败处理机制.md)
- [OnPinSearchTest_UI_Fix_Implementation.cs](../Fixes/OnPinSearchTest_UI_Fix_Implementation.cs)
- [OnPinSearchTest_Best_Optimization.md](../Optimizations/OnPinSearchTest_Best_Optimization.md)
