using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.ObjectModel;
using System.Threading;
using System.Threading.Tasks;

namespace $NAMESPACE$
{
    /// <summary>
    /// $CLASS_NAME$ ViewModel
    /// </summary>
    public partial class $CLASS_NAME$ : ObservableObject
    {
        private readonly ILogger<$CLASS_NAME$> _logger;

#region Properties

/// <summary>
/// 示例属性 - 使用ObservableProperty特性自动实现通知
/// </summary>
[ObservableProperty]
private string _title = "默认标题";

/// <summary>
/// 示例集合属性
/// </summary>
[ObservableProperty]
private ObservableCollection<string> _items = new ObservableCollection<string>();

/// <summary>
/// 示例布尔属性
/// </summary>
[ObservableProperty]
[NotifyCanExecuteChangedFor(nameof(SaveCommand))]
private bool _isValid;

/// <summary>
/// 示例忙碌状态属性
/// </summary>
[ObservableProperty]
private bool _isBusy;

#endregion

#region Constructor

/// <summary>
/// 构造函数
/// </summary>
/// <param name="logger">日志服务</param>
public $CLASS_NAME$(ILogger<$CLASS_NAME$> logger)
        {
            _logger = logger;
            _logger.LogInformation("$CLASS_NAME$ 已创建");
            
            // 初始化代码
            Initialize();
}

#endregion

#region Commands

/// <summary>
/// 保存命令
/// </summary>
[RelayCommand(CanExecute = nameof(CanSave))]
private async Task SaveAsync()
{
    try
    {
        IsBusy = true;
        _logger.LogInformation("执行保存操作");

        // 模拟异步操作
        await Task.Delay(1000);

        // 发送消息通知其他ViewModel
        WeakReferenceMessenger.Default.Send(new SaveCompletedMessage());

        _logger.LogInformation("保存操作完成");
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "保存操作失败");
        // 处理异常
    }
    finally
    {
        IsBusy = false;
    }
}

/// <summary>
/// 加载数据命令
/// </summary>
[RelayCommand]
private async Task LoadDataAsync(CancellationToken cancellationToken = default)
{
    if (IsBusy)
        return;

    try
    {
        IsBusy = true;
        _logger.LogInformation("加载数据");

        // 清空现有数据
        Items.Clear();

        // 模拟异步数据加载
        await Task.Delay(1000, cancellationToken);

        // 添加示例数据
        Items.Add("示例数据 1");
        Items.Add("示例数据 2");
        Items.Add("示例数据 3");

        _logger.LogInformation("数据加载完成");
    }
    catch (OperationCanceledException)
    {
        _logger.LogInformation("数据加载已取消");
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "数据加载失败");
        // 处理异常
    }
    finally
    {
        IsBusy = false;
    }
}

#endregion

#region Methods

/// <summary>
/// 初始化
/// </summary>
private void Initialize()
{
    // 注册消息处理
    WeakReferenceMessenger.Default.Register<RequestDataMessage>(this, (r, m) =>
    {
        // 处理消息
        _logger.LogInformation("收到数据请求消息");
    });
}

/// <summary>
/// 保存命令可执行条件
/// </summary>
private bool CanSave() => IsValid && !IsBusy;

#endregion

#region Message Classes

/// <summary>
/// 保存完成消息
/// </summary>
public class SaveCompletedMessage { }

/// <summary>
/// 请求数据消息
/// </summary>
public class RequestDataMessage { }

        #endregion
    }
} 