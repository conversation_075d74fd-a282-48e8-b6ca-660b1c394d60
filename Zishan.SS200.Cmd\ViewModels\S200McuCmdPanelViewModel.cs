﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using NModbus;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using log4net;
using Prism.Mvvm;
using System.Threading;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using System.Windows;
using HandyControl.Tools.Converter;
using Newtonsoft.Json;

using Zishan.SS200.Cmd.Extensions;
using MessageBox = HandyControl.Controls.MessageBox;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Views;
using Zishan.SS200.Cmd.Config;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.Command;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Diagnostics;
using System.Text;
using System.Windows.Data;
using Prism.Regions;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Utilities;
using Zishan.SS200.Cmd.ViewModels.Dock;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public partial class S200McuCmdPanelViewModel : ObservableObject, IDisposable, IConfigureService
    {
        #region 字段

        private readonly StopwatchHelper _stopwatchHelper = new StopwatchHelper();
        private readonly IS200McuCmdService _mcuCmdService;

        // 创建解析器实例
        private readonly ErrorCodeInfoParser _errorCodeInfoParser = new ErrorCodeInfoParser();

        /// <summary>
        /// 实时状态表互锁实例，用于获取Robot实时状态
        /// </summary>
        private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;

        private readonly ILog _logger = LogManager.GetLogger(typeof(MainWindowViewModel));
        private readonly IRegionManager _regionManager;
        private CancellationTokenSource _pollingCts;
        private string _strMsg;

        // 批量命令解析器
        private BatchCommandParser _batchCommandParser;

        private readonly object _logListLock = new object(); // 集合同步锁对象

        #endregion 字段

        #region 常量

        private static readonly string ModbusHost = App.AppIniConfig.Ip;
        private static readonly int ModbusPort = App.AppIniConfig.Port;

        #endregion 常量

        #region 属性

        /// <summary>
        /// 是否有任何设备连接
        /// </summary>
        [ObservableProperty]
        private bool hasAnyDeviceConnected;

        [ObservableProperty]
        private bool isTabItemtMainSelected = true;

        /// <summary>
        /// 版本
        /// </summary>
        [ObservableProperty]
        private string title = "Zishan.SS200.Cmd_2025-05-26-M1";

        /// <summary>
        /// 设备连接状态
        /// </summary>
        [ObservableProperty]
        private Dictionary<EnuMcuDeviceType, DeviceStatus> deviceStatuses = new Dictionary<EnuMcuDeviceType, DeviceStatus>();

        /// <summary>
        /// 当前选中的设备
        /// </summary>
        [ObservableProperty]
        private string selectedDevice;

        /// <summary>
        /// 设备IP地址
        /// </summary>
        [ObservableProperty]
        private string deviceIp;

        /// <summary>
        /// 设备端口
        /// </summary>
        [ObservableProperty]
        private int devicePort;

        /// <summary>
        /// 命令输入
        /// </summary>
        [ObservableProperty]
        private string commandInput;

        /// <summary>
        /// UI端线圈视图模型，用于绑定输入线圈、控制线圈状态面板
        /// </summary>
        public UiViewModel UiViewModel { get; private set; }

        /// <summary>
        /// 批量命令序列列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<BatchCommandSequence> availableBatchSequences = new ObservableCollection<BatchCommandSequence>();

        /// <summary>
        /// 选择的批量命令序列
        /// </summary>
        [ObservableProperty]
        private BatchCommandSequence selectedBatchSequence;

        /// <summary>
        /// 是否正在执行批量命令
        /// </summary>
        [ObservableProperty]
        private bool isExecutingBatchCommand = false;

        partial void OnIsExecutingBatchCommandChanged(bool value)
        {
            OnPropertyChanged(nameof(IsSingleCommandEnabled));
            OnPropertyChanged(nameof(IsBatchCommandEnabled));
        }

        /// <summary>
        /// 批量命令执行进度信息
        /// </summary>
        [ObservableProperty]
        private string batchCommandProgressInfo = string.Empty;

        /// <summary>
        /// 批量命令取消令牌源
        /// </summary>
        private CancellationTokenSource _batchCommandCts;

        /// <summary>
        /// 是否正在执行单个命令
        /// </summary>
        [ObservableProperty]
        private bool isExecutingCommand = false;

        partial void OnIsExecutingCommandChanged(bool value)
        {
            OnPropertyChanged(nameof(IsSingleCommandEnabled));
            OnPropertyChanged(nameof(IsBatchCommandEnabled));
        }

        /// <summary>
        /// 单个命令按钮是否启用
        /// </summary>
        public bool IsSingleCommandEnabled => !IsExecutingCommand && !IsExecutingBatchCommand;

        /// <summary>
        /// 批量命令按钮是否启用
        /// </summary>
        public bool IsBatchCommandEnabled => !IsExecutingCommand && !IsExecutingBatchCommand;

        #endregion 属性

        #region 命令控制面板

        // 设备选择
        [ObservableProperty]
        private bool isShuttleSelected = true;

        [ObservableProperty]
        private bool isRobotSelected;

        [ObservableProperty]
        private bool isChaSelected;

        [ObservableProperty]
        private bool isChbSelected;

        // 当设备选择改变时更新可用命令
        partial void OnIsShuttleSelectedChanged(bool value)
        {
            if (value) UpdateAvailableCommands(EnuMcuDeviceType.Shuttle);
        }

        partial void OnIsRobotSelectedChanged(bool value)
        {
            if (value) UpdateAvailableCommands(EnuMcuDeviceType.Robot);
        }

        partial void OnIsChaSelectedChanged(bool value)
        {
            if (value) UpdateAvailableCommands(EnuMcuDeviceType.ChamberA);
        }

        partial void OnIsChbSelectedChanged(bool value)
        {
            if (value) UpdateAvailableCommands(EnuMcuDeviceType.ChamberB);
        }

        // 命令选择
        public class CommandItem
        {
            public string CommandName { get; set; }
            public string DisplayName { get; set; }
            public string CommandPrompt { get; set; }
            public int CMDIndex { get; set; }
            public string Description { get; set; }
            public int Timeout { get; set; }
            public List<ConfigParameter> StaticParameterList { get; set; } = new List<ConfigParameter>();

            public override string ToString()
            {
                return $"{CMDIndex} , {DisplayName}[CommandPrompt] , {Description}, 静态参数数量：{StaticParameterList.Count}";
            }
        }

        [ObservableProperty]
        private ObservableCollection<CommandItem> availableCommands = new ObservableCollection<CommandItem>();

        [ObservableProperty]
        private CommandItem selectedCommand;

        // 当选择的命令改变时，更新命令描述和静态参数
        partial void OnSelectedCommandChanged(CommandItem value)
        {
            if (value != null)
            {
                CommandDescription = $"命令索引: {value.CMDIndex}, 超时: {value.Timeout}ms";

                // MCU命令描述信息
                CommandPrompt = "";

                // 更新静态参数显示
                StaticParameters = string.Empty;
                if (value.StaticParameterList != null && value.StaticParameterList.Count > 0)
                {
                    StaticParameters = string.Join(Environment.NewLine,
                        value.StaticParameterList.Select(p => $"{p.Name}={p.Value}"));
                }

                // 更新动态参数显示（从RunPara加载）
                DynamicParameters = string.Empty;
                if (_cmdParameterParser != null)
                {
                    try
                    {
                        var commandConfig = _cmdParameterParser.GetCommand(value.CommandName);
                        // 设置命令提示信息
                        CommandPrompt = commandConfig.Prompt ?? "";

                        if (commandConfig.RunPara != null && commandConfig.RunPara.Count > 0)
                        {
                            DynamicParameters = string.Join(Environment.NewLine,
                                commandConfig.RunPara.Select(p => $"{p.Name}={p.Value}"));
                        }
                        // 没有RunPara时，保持DynamicParameters为空
                    }
                    catch
                    {
                        // 解析异常时，保持DynamicParameters为空
                    }
                }
            }
            else
            {
                CommandDescription = string.Empty;
                StaticParameters = string.Empty;
                DynamicParameters = string.Empty;
            }
        }

        [ObservableProperty]
        private string commandDescription;

        [ObservableProperty]
        private string commandPrompt;

        // 静态参数显示（从JSON配置文件获取）
        [ObservableProperty]
        private string staticParameters;

        /// <summary>
        /// 静态参数显示 修改更新当前命令对应的静态参数列表
        /// </summary>
        /// <param name="value"></param>

        partial void OnStaticParametersChanged(string value)
        {
            if (SelectedCommand != null && !string.IsNullOrEmpty(value))
            {
                var staticParams = value.Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);

                List<ConfigParameter> tempParameterList = new List<ConfigParameter>();
                foreach (var param in staticParams)
                {
                    var parts = param.Split('=');
                    if (parts.Length == 2)
                    {
                        string name = parts[0].Trim();
                        string val = parts[1].Trim();

                        if (!string.IsNullOrWhiteSpace(name))
                        {
                            tempParameterList.Add(new ConfigParameter() { Name = name, Value = int.Parse(val) });
                        }
                    }
                }

                if (tempParameterList.Count > 0)
                {
                    SelectedCommand.StaticParameterList.Clear();
                    SelectedCommand.StaticParameterList.AddRange(tempParameterList);
                }
            }
        }

        // 动态参数（用户输入）
        [ObservableProperty]
        private string _dynamicParameters = string.Empty;

        // 动态参数校验错误集合
        [ObservableProperty]
        private ObservableCollection<string> _dynamicParameterErrors = new ObservableCollection<string>();

        // 动态参数解析结果（参数名-值）
        private Dictionary<string, ushort> _parsedDynamicParameters = new();

        partial void OnDynamicParametersChanged(string value)
        {
            DynamicParameterErrors.Clear();
            _parsedDynamicParameters.Clear();
            if (string.IsNullOrWhiteSpace(value)) return;
            // 正确分割多行，兼容\r\n、\n、\r
            var lines = value.Split(new[] { "\r\n", "\n", "\r" }, StringSplitOptions.None);
            int lineNum = 1;
            foreach (var rawLine in lines)
            {
                var line = rawLine.Trim();
                if (string.IsNullOrEmpty(line) || line.StartsWith("//") || line.StartsWith("#"))
                {
                    lineNum++;
                    continue;
                }
                var eqIdx = line.IndexOf('=');
                if (eqIdx <= 0 || eqIdx == line.Length - 1)
                {
                    DynamicParameterErrors.Add($"第{lineNum}行：格式错误，应为 参数名=值");
                    lineNum++;
                    continue;
                }
                var paramName = line.Substring(0, eqIdx).Trim();
                var paramValueStr = line.Substring(eqIdx + 1).Trim();
                if (string.IsNullOrEmpty(paramName))
                {
                    DynamicParameterErrors.Add($"第{lineNum}行：参数名不能为空");
                    lineNum++;
                    continue;
                }
                try
                {
                    ushort paramValue;
                    if (paramValueStr.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                    {
                        paramValue = Convert.ToUInt16(paramValueStr.Substring(2), 16);
                    }
                    else
                    {
                        paramValue = Convert.ToUInt16(paramValueStr);
                    }
                    _parsedDynamicParameters[paramName] = paramValue;
                }
                catch
                {
                    DynamicParameterErrors.Add($"第{lineNum}行：值格式错误，必须为十进制或0x开头16进制");
                }
                lineNum++;
            }
        }

        [ObservableProperty]
        private string _commandRunInfo = string.Empty;

        [ObservableProperty]
        private string _commandReturnInfo = string.Empty;

        private CmdParameterParser _cmdParameterParser;

        // 获取当前选中的设备名称
        private EnuMcuDeviceType GetSelectedDeviceType()
        {
            if (IsShuttleSelected) return EnuMcuDeviceType.Shuttle;
            if (IsRobotSelected) return EnuMcuDeviceType.Robot;
            if (IsChaSelected) return EnuMcuDeviceType.ChamberA;
            if (IsChbSelected) return EnuMcuDeviceType.ChamberB;
            return EnuMcuDeviceType.Shuttle; // 默认
        }

        // 更新可用命令列表
        private void UpdateAvailableCommands(EnuMcuDeviceType deviceType)
        {
            // 方便调试时使用
            //if (isDebug)
            //{
            //    if (IsRobotSelected)
            //    {
            //        DynamicParameters = "0x0001,0x0000,0x0000,1000";
            //    }
            //    else
            //    {
            //        DynamicParameters = "0x0001,0x0002,0x0003,5,6";
            //    }
            //}

            AvailableCommands.Clear();

            try
            {
                //根据枚举设备类型获取对应的配置参数
                var configFileName = ModbusCommandUtility.GetDeviceConfigFileName(deviceType);

                // 获取设备名称（用于日志显示）
                string deviceName = deviceType.ToString();

                string configPath = App.ConfigHelper.GetConfigFilePath($"Configs/CmdParameter/{configFileName}");

                // 检查文件是否存在
                if (!File.Exists(configPath))
                {
                    _logger.Warn($"配置文件不存在: {configPath}，尝试使用默认配置...");

                    // 尝试搜索工作目录中的配置文件
                    string[] configFiles = Directory.GetFiles(
                        Path.Combine(Golbal.WorkRootPath, "Configs"),
                        "*Parameter.json",
                        SearchOption.AllDirectories);

                    if (configFiles.Length == 0)
                    {
                        // 如果工作目录中没有，则尝试应用程序目录
                        configFiles = Directory.GetFiles(
                            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs"),
                            "*Parameter.json",
                            SearchOption.AllDirectories);
                    }

                    if (configFiles.Length > 0)
                    {
                        configPath = configFiles[0];
                        _logger.Info($"使用替代配置文件: {configPath}");
                    }
                    else
                    {
                        UILogService.AddLog($"未找到任何配置文件，命令列表将为空");
                        return;
                    }
                }

                _cmdParameterParser = new CmdParameterParser(configPath);

                // 获取所有命令
                var allCommands = _cmdParameterParser.GetCommandNames().ToList();

                // 添加到可用命令列表
                foreach (var cmd in allCommands)
                {
                    try
                    {
                        var config = _cmdParameterParser.GetCommand(cmd);

                        // 添加命令（Demo命令一律显示）
                        if (!string.IsNullOrWhiteSpace(cmd))
                        {
                            AvailableCommands.Add(new CommandItem
                            {
                                CommandName = cmd,
                                DisplayName = cmd,
                                CommandPrompt = config.Prompt,
                                CMDIndex = config.CMDIndex,
                                Description = $"命令: {cmd} (索引: {config.CMDIndex}, 超时: {config.TimeOut}ms)",
                                Timeout = config.TimeOut,
                                StaticParameterList = config.ConfigPara?.ToList() ?? new List<ConfigParameter>()
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"加载命令 {cmd} 失败: {ex.Message}");
                    }
                }

                // 选择第一个命令（如果有）
                if (AvailableCommands.Count > 0)
                {
                    SelectedCommand = AvailableCommands[0];
                    UILogService.AddLog($"已从{Path.GetFileName(configPath)}加载{AvailableCommands.Count}个{deviceName}设备命令");
                }
                else
                {
                    UILogService.AddLog($"未能从{Path.GetFileName(configPath)}加载任何命令");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"加载设备{deviceType}命令时发生错误: {ex.Message}");
                HcGrowlExtensions.Error($"加载{deviceType}命令配置失败: {ex.Message}");
                UILogService.AddErrorLog($"加载{deviceType}命令失败: {ex.Message}");
            }
        }

        // 执行命令
        [RelayCommand]
        private async Task ExecuteCommandAsync()
        {
            _logger.Info("\r\n");
            if (SelectedCommand == null)
            {
                UILogService.AddInfoLog("请先选择一个命令");
                return;
            }

            // 若动态参数校验有错误，禁止执行
            if (DynamicParameterErrors != null && DynamicParameterErrors.Count > 0)
            {
                UILogService.AddErrorLog("动态参数存在格式错误，请修正后再执行命令");
                HcGrowlExtensions.Error("动态参数存在格式错误，请修正后再执行命令");
                return;
            }

            try
            {
                IsExecutingCommand = true;

                EnuMcuDeviceType deviceType = GetSelectedDeviceType();
                string deviceName = deviceType.ToString(); // 用于日志显示
                string cmdName = SelectedCommand.CommandName;

                // 将命令名称转换为枚举格式（S1 SD -> S1_SD）
                string cmdEnum = cmdName.Replace(" ", "_");

                // 获取静态参数列表
                List<ushort> staticParamList = SelectedCommand.StaticParameterList?
                    .Select(p => (ushort)p.Value).ToList() ?? new List<ushort>();

                // 新动态参数解析：取 _parsedDynamicParameters.Values
                List<ushort> dynamicParamList = _parsedDynamicParameters.Values.ToList();

                // 合并静态参数和动态参数
                List<ushort> combinedParams = new List<ushort>(staticParamList.Count + dynamicParamList.Count);
                combinedParams.AddRange(staticParamList);
                combinedParams.AddRange(dynamicParamList);

                // 构建命令信息（仅用于日志显示）
                string displayDynamicParams = string.Join(",", _parsedDynamicParameters.Select(kv => $"{kv.Key}={kv.Value}"));

                string commandInfo = $"{deviceType}|{cmdEnum}|{displayDynamicParams}";

                // 执行命令
                UILogService.AddInfoLog($"执行命令: {commandInfo}");

                // 获取设备对象
                McuDevice device = deviceType switch
                {
                    EnuMcuDeviceType.Shuttle => _mcuCmdService.Shuttle,
                    EnuMcuDeviceType.Robot => _mcuCmdService.Robot,
                    EnuMcuDeviceType.ChamberA => _mcuCmdService.ChamberA,
                    EnuMcuDeviceType.ChamberB => _mcuCmdService.ChamberB,
                    _ => throw new ArgumentException($"未知的设备类型: {deviceType}")
                };

                // 检查设备连接状态
                if (!device.IsConnected)
                {
                    UILogService.AddErrorLog($"{deviceType}设备未连接，请先连接设备");
                    HcGrowlExtensions.Warning($"{deviceType}设备未连接，请先连接设备");
                    return;
                }

                // 如果是Robot设备的PinSearch命令，显示安全确认对话框
                if (deviceType == EnuMcuDeviceType.Robot && cmdEnum == EnuRobotCmdIndex.PinSearch.ToString())
                {
                    var confirmResult = MessageBox.Show(
                    "⚠️ 安全提示 ⚠️\n\n" +
                    "即将执行机械臂 PinSearch 操作，请确认：\n\n" +
                    "✓ 机械臂周围无人员和障碍物\n" +
                    "✓ 已检查机械臂PinSearch 运动路径，确保不会有碰撞的可能\n" +
                    "✓ 供电插排按钮及时可断电使用作为紧急按钮使用\n\n" +
                    "确认执行 PinSearch 操作吗？",
                    "PinSearch 安全确认",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning,
                    MessageBoxResult.No);

                    // 如果用户选择取消，则退出
                    if (confirmResult != MessageBoxResult.Yes)
                    {
                        UILogService.AddLog("用户取消了 PinSearch 操作");
                        HcGrowlExtensions.Info("已取消 PinSearch 操作");
                        return;
                    }
                }

                // 重置结果显示
                CommandReturnInfo = string.Empty;
                CommandRunInfo = string.Empty;

                // 根据设备类型直接执行对应的泛型方法，无需中间类型转换
                (string Response, ushort RunInfo, ushort ReturnInfo) result;

                // 方便调试时使用
                int timeout = Golbal.IsDevDebug ? Golbal.DebugCommandRunTimeout : SelectedCommand.Timeout;

                var sw = System.Diagnostics.Stopwatch.StartNew();
                switch (deviceType)
                {
                    case EnuMcuDeviceType.Shuttle:
                        var shuttleCmd = Enum.Parse<EnuShuttleCmdIndex>(cmdEnum);
                        result = await device.Run(shuttleCmd, combinedParams, timeout);
                        break;

                    case EnuMcuDeviceType.Robot:
                        var robotCmd = Enum.Parse<EnuRobotCmdIndex>(cmdEnum);

                        // 如果是PinSearch命令，先清零两端的PinSearch结果
                        if (robotCmd == EnuRobotCmdIndex.PinSearch)
                        {
                            UILogService.AddInfoLog("清零PinSearch结果数据...");

                            // 清零服务中的基准值
                            _mcuCmdService.SmoothBasePinSearchValue = 0;
                            _mcuCmdService.NoseBasePinSearchValue = 0;

                            // 获取机器人状态并清零PinSearch值
                            var robotStatus = _interlock.SubsystemStatus.Robot.Status;
                            robotStatus.Shuttle1PinSearchSmoothP1 = 0;
                            robotStatus.Shuttle1PinSearchSmoothP2 = 0;
                            robotStatus.Shuttle1PinSearchNoseP3 = 0;
                            robotStatus.Shuttle1PinSearchNoseP4 = 0;
                            robotStatus.Shuttle2PinSearchSmoothP1 = 0;
                            robotStatus.Shuttle2PinSearchSmoothP2 = 0;
                            robotStatus.Shuttle2PinSearchNoseP3 = 0;
                            robotStatus.Shuttle2PinSearchNoseP4 = 0;
                            robotStatus.PinSearchStatus = false;
                            robotStatus.EnuPinSearchDataEffective = EnuPinSearchStatus.None;

                            UILogService.AddSuccessLog("PinSearch结果数据已清零");
                        }

                        result = await device.Run(robotCmd, combinedParams, timeout);
                        break;

                    case EnuMcuDeviceType.ChamberA:
                        var chaCmd = Enum.Parse<EnuChaCmdIndex>(cmdEnum);
                        result = await device.Run(chaCmd, combinedParams, timeout);
                        break;

                    case EnuMcuDeviceType.ChamberB:
                        var chbCmd = Enum.Parse<EnuChbCmdIndex>(cmdEnum);
                        result = await device.Run(chbCmd, combinedParams, timeout);
                        break;

                    default:
                        throw new ArgumentException($"未知的设备类型: {deviceType}");
                }
                sw.Stop();

                var response = result.Response;
                var runInfo = result.RunInfo;
                var returnInfo = result.ReturnInfo;

                if (Golbal.IsDevDebug)
                {
                    response = "Success，备注：开发测试跳过，模拟成功，方便调试！";
                    runInfo = 0x0000;
                    returnInfo = 0x0000;
                }

                // 更新RunInfo值显示
                CommandRunInfo = $"0x{runInfo:X4} ({runInfo})";

                // 根据返回代码解析对应的返回信息
                string alarmSource = GetAlarmSource(deviceType);
                string returnInfoHex = returnInfo.ToString("X4");

                if (_errorCodeInfoParser.TryGetAlarmInfo(alarmSource, returnInfoHex, out var errorCodeInfo))
                {
                    //0000 代表返回OK
                    if (errorCodeInfo.Code == "0000")
                    {
                        CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 成功";
                    }
                    else
                    {
                        CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 错误：[{errorCodeInfo.Code}] {errorCodeInfo.Kind} - {errorCodeInfo.Cause}";
                    }
                }
                else
                {
                    CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 未知错误";
                }

                string resultMessage = $"{deviceType} {cmdName}命令执行结果: {response}, 运行信息: 0x{runInfo:X4}, 耗时: {sw.ElapsedMilliseconds}ms";
                UILogService.AddInfoLog(resultMessage);

                if (response.StartsWith("Success"))
                {
                    //程序右上角太多弹出消息框了，注释掉
                    //HcGrowlExtensions.Success($"命令执行成功: {cmdName} [{displayDynamicParams}]");

                    // 如果是Robot设备的PinSearch命令且执行成功，记录Pin Search值到日志
                    if (deviceType == EnuMcuDeviceType.Robot && cmdEnum == EnuRobotCmdIndex.PinSearch.ToString())
                    {
                        try
                        {
                            // 获取Pin Search P1和P2的值
                            int p1Value = UiViewModel.PinSearchP1Value;
                            int p2Value = UiViewModel.PinSearchP2Value;

                            // 记录到日志
                            string pinSearchValues = $"Pin Search计算结果：P1: {p1Value}, P2: {p2Value}";
                            UILogService.AddInfoLog(pinSearchValues);

                            // 显示成功提示
                            HcGrowlExtensions.Success(pinSearchValues);
                        }
                        catch (Exception ex)
                        {
                            _logger.Error($"获取Pin Search值失败: {ex.Message}", ex);
                            UILogService.AddErrorLog($"获取Pin Search值失败: {ex.Message}");
                        }
                    }
                }
                else
                {
                    HcGrowlExtensions.Error($"命令执行失败: {cmdName} [{displayDynamicParams}] - {response}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"执行命令失败: {ex.Message}", ex);
                UILogService.AddErrorLog($"执行命令失败: {ex.Message}");
                HcGrowlExtensions.Error($"执行命令失败: {ex.Message}");
            }
            finally
            {
                IsExecutingCommand = false;
            }
        }

        /// <summary>
        /// 执行配置批命令
        /// </summary>
        /// <returns></returns>
        [RelayCommand]
        private async Task ExecuteBatchCommandAsync()
        {
            if (SelectedBatchSequence == null || SelectedBatchSequence.Commands.Count == 0)
            {
                HcGrowlExtensions.Warning("请先选择一个包含命令的批量序列");
                UILogService.AddWarningLog("没有可执行的批量命令序列");
                return;
            }

            // 如果已经在执行，则返回
            if (IsExecutingBatchCommand)
            {
                HcGrowlExtensions.Warning("正在执行批量命令序列，请等待执行完成或手动停止");
                return;
            }

            try
            {
                // 预加载所有需要的设备命令配置
                var deviceCommands = SelectedBatchSequence.Commands
                    .Select(cmd => cmd.DeviceName)
                    .Distinct()
                    .ToDictionary(
                        deviceName => deviceName,
                        deviceName =>
                        {
                            // 将设备名称字符串转换为枚举类型
                            if (Enum.TryParse<EnuMcuDeviceType>(deviceName, true, out var deviceType))
                            {
                                UpdateAvailableCommands(deviceType);
                            }
                            else
                            {
                                // 处理特殊情况
                                throw new ArgumentException($"未知的设备类型: {deviceName}");
                            }
                            return AvailableCommands.ToList();
                        }
                    );

                // 验证所有命令是否存在
                foreach (var command in SelectedBatchSequence.Commands)
                {
                    if (!deviceCommands.ContainsKey(command.DeviceName) ||
                        !deviceCommands[command.DeviceName].Any(c => c.CommandName == command.CommandName))
                    {
                        string errorMessage = $"命令未找到: {command.DeviceName} - {command.CommandName}";
                        HcGrowlExtensions.Error(errorMessage);
                        UILogService.AddErrorLog(errorMessage);
                        return;
                    }
                }

                // 设置执行状态并创建取消令牌
                IsExecutingBatchCommand = true;
                _batchCommandCts = new CancellationTokenSource();

                // 获取循环执行参数
                int loopCount = SelectedBatchSequence.LoopCount;
                bool isInfiniteLoop = SelectedBatchSequence.IsInfiniteLoop;
                int loopDelayMs = SelectedBatchSequence.LoopDelayMs;

                // 当前循环计数
                int currentLoopIndex = 0;

                // 确定实际循环次数 (当IsInfiniteLoop为true时，循环次数为int.MaxValue，相当于无限循环)
                int effectiveLoopCount = isInfiniteLoop ? int.MaxValue : Math.Max(1, loopCount);

                UILogService.AddInfoLog($"开始执行批量命令序列: {SelectedBatchSequence.Name}" +
                        (effectiveLoopCount > 1 || isInfiniteLoop
                        ? $"，循环模式: {(isInfiniteLoop ? "无限循环" : $"循环{effectiveLoopCount}次")}"
                        : string.Empty));

                HcGrowlExtensions.Info($"开始执行批量命令序列: {SelectedBatchSequence.Name}" +
                       (effectiveLoopCount > 1 || isInfiniteLoop
                       ? $"，循环模式: {(isInfiniteLoop ? "无限循环" : $"循环{effectiveLoopCount}次")}"
                       : string.Empty));

                // 循环执行
                while (currentLoopIndex < effectiveLoopCount && !_batchCommandCts.IsCancellationRequested)
                {
                    currentLoopIndex++;
                    BatchCommandProgressInfo = isInfiniteLoop
                        ? $"循环执行中: 第{currentLoopIndex}次"
                        : $"循环执行中: {currentLoopIndex}/{effectiveLoopCount}";

                    if (currentLoopIndex > 1)
                    {
                        UILogService.AddInfoLog($"开始第{currentLoopIndex}次循环执行");
                    }

                    // 执行单次批量命令序列
                    bool success = await ExecuteSingleBatchSequenceAsync(SelectedBatchSequence, currentLoopIndex, effectiveLoopCount);

                    // 如果执行失败或已取消，退出循环
                    if (!success || _batchCommandCts.IsCancellationRequested)
                    {
                        if (!success)
                        {
                            UILogService.AddErrorLog($"批量命令序列执行失败，停止循环执行");
                            HcGrowlExtensions.Error($"批量命令序列执行失败，已停止");
                        }
                        break;
                    }

                    // 如果不是最后一次循环，等待指定的循环间隔时间
                    if (currentLoopIndex < effectiveLoopCount)
                    {
                        UILogService.AddInfoLog($"等待{loopDelayMs}毫秒后开始下一次循环...");

                        try
                        {
                            await Task.Delay(loopDelayMs, _batchCommandCts.Token);
                        }
                        catch (TaskCanceledException)
                        {
                            UILogService.AddWarningLog("批量命令执行已取消");
                            break;
                        }
                    }
                }

                // 更新执行结果信息
                if (_batchCommandCts.IsCancellationRequested)
                {
                    UILogService.AddWarningLog($"批量命令序列 {SelectedBatchSequence.Name} 执行已手动停止，已完成{currentLoopIndex - 1}次循环");
                    HcGrowlExtensions.Warning($"批量命令序列 {SelectedBatchSequence.Name} 执行已手动停止");
                }
                else if (currentLoopIndex < effectiveLoopCount)
                {
                    // 未完成所有循环但不是因为取消，说明是因为命令执行失败
                    UILogService.AddWarningLog($"批量命令序列 {SelectedBatchSequence.Name} 因命令执行失败而中止，已完成{currentLoopIndex}次循环");
                    // 错误消息已在命令执行失败时显示，此处不再重复
                }
                else if (isInfiniteLoop)
                {
                    // 理论上不会执行到这里，因为无限循环只能通过取消退出
                    UILogService.AddSuccessLog($"批量命令序列 {SelectedBatchSequence.Name} 无限循环执行完成，共执行{currentLoopIndex}次");
                    HcGrowlExtensions.Success($"批量命令序列 {SelectedBatchSequence.Name} 执行完成");
                }
                else
                {
                    UILogService.AddSuccessLog($"批量命令序列 {SelectedBatchSequence.Name} 循环执行完成，共执行{currentLoopIndex}次");
                    HcGrowlExtensions.Success($"批量命令序列 {SelectedBatchSequence.Name} 执行完成");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"执行批量命令序列失败: {ex.Message}");
                HcGrowlExtensions.Error($"执行批量命令序列失败: {ex.Message}");
            }
            finally
            {
                // 清理资源，重置状态
                _batchCommandCts?.Dispose();
                _batchCommandCts = null;
                IsExecutingBatchCommand = false;
                BatchCommandProgressInfo = string.Empty;
            }
        }

        /// <summary>
        /// 执行单次批量命令序列
        /// </summary>
        /// <param name="sequence">要执行的批量命令序列</param>
        /// <param name="currentLoopIndex">当前循环索引</param>
        /// <param name="totalLoopCount">总循环次数</param>
        /// <returns>执行成功返回true，否则返回false</returns>
        private async Task<bool> ExecuteSingleBatchSequenceAsync(BatchCommandSequence sequence, int currentLoopIndex, int totalLoopCount)
        {
            try
            {
                int commandCount = sequence.Commands.Count;
                int currentCommandIndex = 0;

                foreach (var commandItem in sequence.Commands)
                {
                    // 检查取消令牌
                    if (_batchCommandCts.IsCancellationRequested)
                    {
                        return false;
                    }

                    currentCommandIndex++;
                    string deviceName = commandItem.DeviceName;
                    string commandName = commandItem.CommandName;
                    string displayDynamicParams = string.Join(", ", commandItem.DynamicParameters.Select(p => $"{p.Key}={p.Value}"));

                    // 更新进度信息
                    string loopInfo = totalLoopCount > 1
                        ? (totalLoopCount == int.MaxValue
                            ? $"[循环{currentLoopIndex}] "
                            : $"[循环{currentLoopIndex}/{totalLoopCount}] ")
                        : string.Empty;

                    BatchCommandProgressInfo = $"{loopInfo}命令 {currentCommandIndex}/{commandCount}: {deviceName}-{commandName} [{displayDynamicParams}]";

                    // 查找命令项
                    var command = AvailableCommands.FirstOrDefault(c => c.CommandName == commandName);
                    if (command == null)
                    {
                        UILogService.AddWarningLog($"{loopInfo}批量命令序列中的命令未找到: {commandName}");
                        HcGrowlExtensions.Warning($"命令未找到: {commandName}");
                        continue;
                    }

                    UILogService.AddInfoLog($"{loopInfo}执行批量命令 [{currentCommandIndex}/{commandCount}]: {deviceName} - {commandName} [{displayDynamicParams}]");

                    // 获取命令参数
                    List<ushort> staticParamList = new List<ushort>();
                    if (command.StaticParameterList != null && command.StaticParameterList.Count > 0)
                    {
                        staticParamList = command.StaticParameterList.Select(p => (ushort)p.Value).ToList();
                    }

                    // 获取动态参数
                    List<ushort> dynamicParamList = new List<ushort>();
                    if (commandItem.DynamicParameters != null && commandItem.DynamicParameters.Count > 0)
                    {
                        dynamicParamList = commandItem.DynamicParameters.Values.ToList();
                    }

                    // 合并参数
                    List<ushort> combinedParams = new List<ushort>();
                    combinedParams.AddRange(staticParamList);
                    combinedParams.AddRange(dynamicParamList);

                    // 执行命令
                    CommandRunInfo = $"【批命令】正在-2222-执行 {deviceName} 命令 {commandName} [{displayDynamicParams}]...";
                    string response;
                    ushort runInfo = 0;
                    ushort returnInfo = 0;

                    bool blRunResult = false;
                    try
                    {
                        // 检查设备连接状态
                        EnuMcuDeviceType deviceType;
                        McuDevice device = null;

                        // 将字符串转换为枚举
                        if (Enum.TryParse(deviceName, true, out deviceType))
                        {
                            // 获取设备对象
                            device = deviceType switch
                            {
                                EnuMcuDeviceType.Shuttle => _mcuCmdService.Shuttle,
                                EnuMcuDeviceType.Robot => _mcuCmdService.Robot,
                                EnuMcuDeviceType.ChamberA => _mcuCmdService.ChamberA,
                                EnuMcuDeviceType.ChamberB => _mcuCmdService.ChamberB,
                                _ => null
                            };
                        }
                        else
                        {
                            device = null;
                        }

                        if (device == null)
                        {
                            UILogService.AddWarningLog($"{loopInfo}未知设备名称: {deviceName}");
                            continue;
                        }

                        if (!device.IsConnected)
                        {
                            UILogService.AddWarningLog($"{loopInfo}设备 {deviceName} 未连接，跳过命令 {commandName}");
                            continue;
                        }

                        // 根据设备类型使用对应的泛型Run方法
                        (string Response, ushort RunInfo, ushort ReturnInfo) result;
                        string cmdEnum = command.CommandName.Replace(" ", "_");

                        // 方便调试时使用
                        int timeout = Golbal.IsDevDebug ? Golbal.DebugCommandRunTimeout : command.Timeout;

                        switch (deviceType)
                        {
                            case EnuMcuDeviceType.Shuttle:
                                var shuttleCmd = Enum.Parse<EnuShuttleCmdIndex>(cmdEnum);
                                result = await device.Run(shuttleCmd, combinedParams, timeout);
                                break;

                            case EnuMcuDeviceType.Robot:
                                var robotCmd = Enum.Parse<EnuRobotCmdIndex>(cmdEnum);
                                result = await device.Run(robotCmd, combinedParams, timeout);
                                break;

                            case EnuMcuDeviceType.ChamberA:
                                var chaCmd = Enum.Parse<EnuChaCmdIndex>(cmdEnum);
                                result = await device.Run(chaCmd, combinedParams, timeout);
                                break;

                            case EnuMcuDeviceType.ChamberB:
                                var chbCmd = Enum.Parse<EnuChbCmdIndex>(cmdEnum);
                                result = await device.Run(chbCmd, combinedParams, timeout);
                                break;

                            default:
                                UILogService.AddWarningLog($"{loopInfo}未知设备名称: {deviceName}");
                                continue;
                        }

                        response = result.Response;
                        runInfo = result.RunInfo;
                        returnInfo = result.ReturnInfo;

                        if (Golbal.IsDevDebug)
                        {
                            response = "Success，备注：开发测试跳过，模拟成功，方便调试！";
                            runInfo = 0x0000;
                            returnInfo = 0x0000;
                        }

                        // 记录命令结果
                        CommandRunInfo = $"运行信息: 0x{runInfo:X4} ({runInfo})";

                        // 处理错误代码
                        ErrorCodeInfo errorCodeInfo = null;
                        string alarmSource = GetAlarmSource(deviceName);
                        string returnInfoHex = returnInfo.ToString("X4");

                        if (_errorCodeInfoParser.TryGetAlarmInfo(alarmSource, returnInfoHex, out errorCodeInfo))
                        {
                            //0000 代表返回OK
                            if (errorCodeInfo.Code == "0000")
                            {
                                CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 成功";
                                blRunResult = true;
                            }
                            else
                            {
                                CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 错误：[{errorCodeInfo.Code}] {errorCodeInfo.Kind} - {errorCodeInfo.Cause}";
                            }
                        }
                        else
                        {
                            CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 未知错误";
                        }

                        string resultMessage = $"{loopInfo}批量命令[{currentCommandIndex}/{commandCount}] {deviceName} {commandName} [{displayDynamicParams}] 执行结果: {response}, 运行信息: 0x{runInfo:X4}";
                        _logger.Info(resultMessage);
                        UILogService.AddInfoLog(resultMessage);

                        if (response.StartsWith("Success"))
                        {
                            //程序右上角太多弹出消息框了，注释掉
                            //HcGrowlExtensions.Success($"命令执行成功: {commandName} [{displayDynamicParams}]", waitTime: Golbal.IsDevDebug ? HcGrowlExtensions.WaitTime : 1);
                        }
                        else
                        {
                            HcGrowlExtensions.Error($"命令执行失败: {commandName} [{displayDynamicParams}] - {response}");
                        }

                        // 如果命令执行不成功，记录失败信息并直接返回
                        if (!blRunResult)
                        {
                            UILogService.AddErrorLog($"{loopInfo}命令执行失败: {commandName} [{displayDynamicParams}], 返回码: 0x{returnInfo:X4}");
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        UILogService.AddErrorLog($"{loopInfo}执行批量命令 {commandName} [{displayDynamicParams}] 失败: {ex.Message}");
                        HcGrowlExtensions.Error($"执行批量命令 {commandName} [{displayDynamicParams}] 失败: {ex.Message}");
                        _logger.Error($"批量命令执行异常: {ex.Message}", ex);
                        return false;
                    }

                    // 命令执行后延迟，除非是取消或最后一个命令
                    if (!_batchCommandCts.IsCancellationRequested && currentCommandIndex < commandCount)
                    {
                        int delay = commandItem.DelayAfterExecution;
                        UILogService.AddInfoLog($"{loopInfo}等待 {delay} 毫秒后执行下一条命令...");

                        try
                        {
                            await Task.Delay(delay, _batchCommandCts.Token);
                        }
                        catch (TaskCanceledException)
                        {
                            UILogService.AddWarningLog("批量命令执行已取消");
                            return false;
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"执行批量命令序列失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止执行批量命令
        /// </summary>
        [RelayCommand]
        private void StopBatchCommandExecution()
        {
            if (!IsExecutingBatchCommand || _batchCommandCts == null)
            {
                HcGrowlExtensions.Warning("没有正在执行的批量命令");
                return;
            }

            try
            {
                _batchCommandCts.Cancel();
                UILogService.AddInfoLog("正在停止批量命令执行...");
                HcGrowlExtensions.Info("正在停止批量命令执行，请等待当前命令完成");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"停止批量命令执行失败: {ex.Message}");
                HcGrowlExtensions.Error($"停止批量命令执行失败: {ex.Message}");
            }
        }

        #endregion 命令控制面板

        #region 构造函数

        public S200McuCmdPanelViewModel() : this(
            new S200McuCmdService(),
            null)
        {
            // 设计时数据绑定
            // 在这里添加设计时数据的初始化代码

            // 如果需要，可以在这里添加更多设计时专用的数据
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public S200McuCmdPanelViewModel(IS200McuCmdService mcuCmdService, IRegionManager regionManager)
        {
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
            _regionManager = regionManager; // 允许为null，设计时可能不需要

            mcuCmdService.Name = $"S200McuCmdService，创建时间：{DateTime.Now}";

            // 初始化UI日志服务
            // UILogService.Initialize(this);

            InitializeViewModel();
        }

        /// <summary>
        /// 初始化ViewModel的共享代码
        /// </summary>
        private void InitializeViewModel()
        {
            // 初始化线圈视图模型
            UiViewModel = new UiViewModel(_mcuCmdService);

            // 初始化告警信息解析器
            _errorCodeInfoParser.AddConfigFilePath("Robot", App.ConfigHelper.GetConfigFilePath("Configs/ErrorCodeInfo/RobotAlarmInfo.json"));
            _errorCodeInfoParser.AddConfigFilePath("SHTL", App.ConfigHelper.GetConfigFilePath("Configs/ErrorCodeInfo/SHTLAlarmInfo.json"));
            //_errorCodeInfoParser.AddConfigFilePath("Motor", App.ConfigHelper.GetConfigFilePath("Configs/ErrorCodeInfo/MotorAlarmInfo.json"));

            _logger.Info("主窗口ViewModel已初始化");

            // 应用程序启动时，默认加载Shuttle设备的命令
            IsShuttleSelected = true;
            UpdateAvailableCommands(EnuMcuDeviceType.Shuttle);

            // 初始化设备状态
            UpdateDeviceStatus();

            // 加载批量命令序列
            LoadBatchCommandSequences();

            // 添加设备状态变化的事件订阅
            _mcuCmdService.Shuttle.StatusChanged += Device_StatusChanged;
            _mcuCmdService.Robot.StatusChanged += Device_StatusChanged;
            _mcuCmdService.ChamberA.StatusChanged += Device_StatusChanged;
            _mcuCmdService.ChamberB.StatusChanged += Device_StatusChanged;

            // 方便调试时使用
            //if (isDebug)
            //{
            //    if (IsRobotSelected)
            //    {
            //        DynamicParameters = "0x0001,0x0000,0x0000,1000";
            //    }
            //    else
            //    {
            //        DynamicParameters = "0x0001,0x0002,0x0003,5,6";
            //    }
            //}
        }

        #endregion 构造函数

        #region 命令

        /// <summary>
        /// 初始化命令，视图加载完成后执行
        /// </summary>
        [RelayCommand]
        private void Initialize()
        {
            // 确保设备状态正确反映在UI上
            UpdateDeviceStatus();
            _logger.Info("主窗口初始化完成，已更新设备状态");
        }

        /// <summary>
        /// 连接所有设备
        /// </summary>
        [RelayCommand]
        private async Task ConnectDevicesAsync()
        {
            try
            {
                _logger.Info("正在连接所有设备...");

                // 使用配置文件中的设备IP和端口
                await _mcuCmdService.ConnectAllAsync(
                    App.AppIniConfig.ShuttleIp, App.AppIniConfig.ShuttlePort,
                    App.AppIniConfig.RobotIp, App.AppIniConfig.RobotPort,
                    App.AppIniConfig.ChaIp, App.AppIniConfig.ChaPort,
                    App.AppIniConfig.ChbIp, App.AppIniConfig.ChbPort
                );

                // 连接成功后立即更新设备状态
                UpdateDeviceStatus();

                bool isAllConnected = true;
                // 获取所有设备的状态
                foreach (var device in DeviceStatuses)
                {
                    _logger.Info($"设备 {device.Key} 状态: {device.Value}");
                    if (device.Value != DeviceStatus.Connected)
                    {
                        isAllConnected = false;
                    }
                }

                if (isAllConnected)
                {
                    HcGrowlExtensions.Success("所有设备连接成功");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"连接设备失败: {ex.Message}", ex);
                HcGrowlExtensions.Error($"连接设备失败: {ex.Message}");
                UILogService.AddLog($"连接设备时发生错误: {ex.Message}");
                // 出错时仍然尝试更新设备状态
                UpdateDeviceStatus();
            }
        }

        // 断开所有设备，并且UI上添加按钮
        /// <summary>
        /// 断开所有设备连接
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanDisconnectDevices))]
        private async Task DisconnectDevicesAsync()
        {
            try
            {
                if (HandyControl.Controls.MessageBox.Show("确定要断开所有设备连接吗？", "确认", MessageBoxButton.YesNo) != MessageBoxResult.Yes)
                    return;

                _logger.Info("正在断开所有设备连接...");

                await _mcuCmdService.DisconnectAllAsync();

                // 断开连接后更新设备状态
                UpdateDeviceStatus();

                _logger.Info("所有设备已断开连接");
                HcGrowlExtensions.Success("所有设备已断开连接");
            }
            catch (Exception ex)
            {
                _logger.Error($"断开设备连接失败: {ex.Message}", ex);
                HcGrowlExtensions.Error($"断开设备连接失败: {ex.Message}");
                UILogService.AddErrorLog($"断开设备连接时发生错误: {ex.Message}");
                // 出错时仍然尝试更新设备状态
                UpdateDeviceStatus();
            }
        }

        /// <summary>
        /// 判断是否可以断开设备连接
        /// </summary>
        /// <returns>是否可以断开设备连接</returns>
        private bool CanDisconnectDevices() => HasAnyDeviceConnected;

        /// <summary>
        /// 执行设备命令
        /// </summary>
        /// <param name="commandInfo">命令信息，格式：deviceName|cmdIndex|param1,param2,param3</param>
        [RelayCommand]
        private async Task RunDeviceAsync(string commandInfo)
        {
            try
            {
                // 解析命令信息
                var parts = commandInfo.Split('|');
                if (parts.Length < 2)
                {
                    // 举例：shuttle|S1_SD|0x0001,0x0002,0x0003
                    string message = "命令信息格式错误，应为：deviceName|cmdIndex|param1,param2,param3";
                    UILogService.AddErrorLog(message);
                    throw new ArgumentException(message);
                }

                string deviceName = parts[0];
                string cmdName = parts[1];

                // 设备名称转换为枚举类型
                if (!Enum.TryParse<EnuMcuDeviceType>(deviceName, true, out var deviceType))
                {
                    UILogService.AddErrorLog($"未知的设备名称: {deviceName}");
                    throw new ArgumentException($"未知的设备名称: {deviceName}");
                }

                // 解析参数
                List<ushort> parameters = new List<ushort>();
                if (parts.Length > 2 && !string.IsNullOrWhiteSpace(parts[2]))
                {
                    var paramStrs = parts[2].Split(',');
                    foreach (var paramStr in paramStrs)
                    {
                        if (ushort.TryParse(paramStr, out ushort paramValue))
                        {
                            parameters.Add(paramValue);
                        }
                        else if (paramStr.StartsWith("0x") && ushort.TryParse(paramStr.Substring(2), System.Globalization.NumberStyles.HexNumber, null, out paramValue))
                        {
                            parameters.Add(paramValue);
                        }
                        else
                        {
                            UILogService.AddErrorLog($"参数解析错误: {paramStr}");
                            throw new ArgumentException($"参数解析错误: {paramStr}");
                        }
                    }
                }

                // 获取设备对象
                McuDevice device = deviceType switch
                {
                    EnuMcuDeviceType.Shuttle => _mcuCmdService.Shuttle,
                    EnuMcuDeviceType.Robot => _mcuCmdService.Robot,
                    EnuMcuDeviceType.ChamberA => _mcuCmdService.ChamberA,
                    EnuMcuDeviceType.ChamberB => _mcuCmdService.ChamberB,
                    _ => throw new ArgumentException($"未知的设备类型: {deviceType}")
                };

                // 检查设备连接状态
                if (!device.IsConnected)
                {
                    string message = $"{deviceName}设备未连接，请先连接设备";
                    _logger.Warn(message);
                    UILogService.AddWarningLog(message);
                    HcGrowlExtensions.Warning(message);
                    return;
                }

                // 执行命令
                (string Response, ushort RunInfo, ushort ReturnInfo) result;

                // 设置超时时间
                int timeout = Golbal.CommandRunTimeout * 1000;

                var sw = System.Diagnostics.Stopwatch.StartNew();
                switch (deviceType)
                {
                    case EnuMcuDeviceType.Shuttle:
                        var shuttleCmd = Enum.Parse<EnuShuttleCmdIndex>(cmdName);
                        result = await device.Run(shuttleCmd, parameters, timeout);
                        break;

                    case EnuMcuDeviceType.Robot:
                        var robotCmd = Enum.Parse<EnuRobotCmdIndex>(cmdName);
                        result = await device.Run(robotCmd, parameters, timeout);
                        break;

                    case EnuMcuDeviceType.ChamberA:
                        var chaCmd = Enum.Parse<EnuChaCmdIndex>(cmdName);
                        result = await device.Run(chaCmd, parameters, timeout);
                        break;

                    case EnuMcuDeviceType.ChamberB:
                        var chbCmd = Enum.Parse<EnuChbCmdIndex>(cmdName);
                        result = await device.Run(chbCmd, parameters, timeout);
                        break;

                    default:
                        throw new ArgumentException($"未知的设备类型: {deviceType}");
                }
                sw.Stop();

                var response = result.Response;
                var runInfo = result.RunInfo;
                var returnInfo = result.ReturnInfo;

                // 更新RunInfo值显示
                CommandRunInfo = $"0x{runInfo:X4} ({runInfo})";

                // 根据返回代码解析对应的返回信息
                string alarmSource = GetAlarmSource(deviceName);
                string returnInfoHex = returnInfo.ToString("X4");

                if (_errorCodeInfoParser.TryGetAlarmInfo(alarmSource, returnInfoHex, out var errorCodeInfo))
                {
                    //0000 代表返回OK
                    if (errorCodeInfo.Code == "0000")
                    {
                        CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 成功";
                    }
                    else
                    {
                        CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 错误：[{errorCodeInfo.Code}] {errorCodeInfo.Kind} - {errorCodeInfo.Cause}";
                    }
                }
                else
                {
                    CommandReturnInfo = $"0x{returnInfo:X4} ({returnInfo}) 未知错误";
                }

                string resultMessage = $"{deviceName} {cmdName}命令执行结果: {response}, 运行信息: 0x{runInfo:X4}, 耗时: {sw.ElapsedMilliseconds}ms";
                _logger.Info(resultMessage);
                UILogService.AddLog(resultMessage);
            }
            catch (Exception ex)
            {
                _logger.Error($"执行命令失败: {ex.Message}", ex);
                UILogService.AddLog($"执行命令失败: {ex.Message}");
                HcGrowlExtensions.Error($"执行命令失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置任务区和命令参数区寄存器
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanDisconnectDevices))]
        private async Task ResetTaskAsync()
        {
            try
            {
                if (!Golbal.IsDevDebug)
                {
                    if (HandyControl.Controls.MessageBox.Show("确定要重置所有设备的任务区和命令参数区吗？", "确认", MessageBoxButton.YesNo) != MessageBoxResult.Yes)
                        return;
                }

                _logger.Info("开始重置所有设备的任务区和命令参数区");

                bool allSuccess = true;
                List<EnuMcuDeviceType> failedDevices = new List<EnuMcuDeviceType>();
                bool hasConnectedDevices = false;

                // 统一使用固定的重置值0
                ushort resetValue = 0;

                // 创建设备字典，便于统一处理
                var devices = new Dictionary<EnuMcuDeviceType, McuDevice>
                {
                    { EnuMcuDeviceType.Shuttle, _mcuCmdService.Shuttle },
                    { EnuMcuDeviceType.Robot, _mcuCmdService.Robot },
                    { EnuMcuDeviceType.ChamberA, _mcuCmdService.ChamberA },
                    { EnuMcuDeviceType.ChamberB, _mcuCmdService.ChamberB }
                };

                // 循环处理所有设备
                foreach (var device in devices)
                {
                    EnuMcuDeviceType deviceType = device.Key;
                    McuDevice mcuDevice = device.Value;

                    if (mcuDevice.IsConnected)
                    {
                        hasConnectedDevices = true;
                        bool result = await mcuDevice.ResetTaskAsync(resetValue: resetValue);

                        if (!result)
                        {
                            allSuccess = false;
                            failedDevices.Add(deviceType);
                            _logger.Warn($"{deviceType} 设备重置失败");
                        }
                        else
                        {
                            _logger.Info($"{deviceType} 设备重置成功");
                        }
                    }
                }

                // 显示结果
                if (!hasConnectedDevices)
                {
                    _logger.Warn("没有发现连接的设备，无法执行重置操作");
                    HcGrowlExtensions.Warning("没有连接的设备，请先连接设备");
                }
                else if (allSuccess)
                {
                    _logger.Info("所有设备的任务区和命令参数区重置成功");
                    HcGrowlExtensions.Success("所有设备重置成功");
                }
                else
                {
                    string failedList = string.Join(", ", failedDevices);
                    string message = $"部分设备重置失败: {failedList}";
                    _logger.Error(message);
                    HcGrowlExtensions.Error(message);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"重置设备任务区和命令参数区时发生错误: {ex.Message}", ex);
                HcGrowlExtensions.Error($"重置失败: {ex.Message}");
            }
        }

        /// <summary>
        ///  Modbus运行命令测试
        /// </summary>
        [RelayCommand]
        private async Task RunCmdTest(string parameter)
        {
            // var cmdParStartAddr = new List<ushort>
            // {
            //     0x0010, // CMD Index
            //     0x0011, // 参数1地址
            //     0x0012, // 参数2地址
            //     0x0013, // 参数3地址
            // };

            // // 1. 创建任务处理器
            // var taskHandle = new CmdTaskHandlel(
            //     // Task Handle1区域的6个寄存器地址
            //     cmdStartAddr: new List<ushort> {
            //             0x0000,  // Status Flag地址
            //             0x0001,  // Run Flag地址
            //             0x0002,  // Run Info地址
            //             0x0003,  // Return Info地址
            //             0x0004,  // CMD Start Addr地址
            //             0x0005   // CMD Len地址
            //                     },
            //  // 参数区域地址（可选）
            //  cmdParStartAddr: cmdParStartAddr
            // );

            // //清空使用命令参数寄存器为0
            // var clearResult = await taskHandle.ExecuteClearAllAsync(
            //    master: _modbusClient.Master,      // Modbus客户端
            //    slaveId: 1              // 从站地址
            //);

            // // 2. 执行简单命令
            // var result = await taskHandle.ExecuteCommandAsync(
            //     master: _modbusClient.Master,      // Modbus客户端
            //     slaveId: 1,               // 从站地址
            //     EnuShuttleCmdIndex.S10_RC,
            //     new List<ushort>() { 0x01, 0x02, 0x03 },
            //     timeout: 10000             // 超时时间(ms)
            // );

            // // 3. 检查执行结果
            // if (result == TaskHandleResult.Success)
            // {
            //     Console.WriteLine($"命令执行成功！");
            //     HcGrowlExtensions.Info($"命令执行成功！");
            //     MessageBox.Show($"命令执行成功！");
            // }
            // else
            // {
            //     Console.WriteLine($"命令执行失败：result={result.ToString()}");
            //     //Console.WriteLine($"命令执行失败：{GetResultDescription(result)}");
            //     HcGrowlExtensions.Warning($"命令执行失败：result={result.ToString()}");
            //     MessageBox.Show($"命令执行失败：result={result.ToString()}");
            // }
        }

        /// <summary>
        /// 修改设备连接参数
        /// </summary>
        [RelayCommand]
        private async Task ModifyDeviceConnectionAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(SelectedDevice))
                {
                    HcGrowlExtensions.Warning("请选择要修改的设备");
                    return;
                }

                if (string.IsNullOrEmpty(DeviceIp))
                {
                    HcGrowlExtensions.Warning("请输入设备IP地址");
                    return;
                }

                if (DevicePort <= 0 || DevicePort > 65535)
                {
                    HcGrowlExtensions.Warning("请输入有效的端口号(1-65535)");
                    return;
                }

                // 先断开当前设备的连接
                McuDevice device = GetSelectedDevice();
                if (device.IsConnected)
                {
                    await device.DisconnectAsync();
                }

                // 更新配置文件中的设备参数
                switch (SelectedDevice.ToLower())
                {
                    case "shuttle":
                        App.AppIniConfig.ShuttleIp = DeviceIp;
                        App.AppIniConfig.ShuttlePort = DevicePort;
                        break;

                    case "robot":
                        App.AppIniConfig.RobotIp = DeviceIp;
                        App.AppIniConfig.RobotPort = DevicePort;
                        break;

                    case "chambera":
                        App.AppIniConfig.ChaIp = DeviceIp;
                        App.AppIniConfig.ChaPort = DevicePort;
                        break;

                    case "chamberb":
                        App.AppIniConfig.ChbIp = DeviceIp;
                        App.AppIniConfig.ChbPort = DevicePort;
                        break;
                }

                // 保存配置到 ini 文件
                // string configPath = Path.Combine(Golbal.WorkRootPath, "Config", "App.ini");
                // IniFile.WriteValue("Device", $"{SelectedDevice}Ip", DeviceIp, configPath);
                // IniFile.WriteValue("Device", $"{SelectedDevice}Port", DevicePort.ToString(), configPath);

                // 尝试使用新参数连接设备
                await device.ConnectAsync(DeviceIp, DevicePort);

                // 更新设备状态
                UpdateDeviceStatus();

                HcGrowlExtensions.Success($"设备 {SelectedDevice} 连接参数修改成功");
                _logger.Info($"设备 {SelectedDevice} 连接参数已更新为 {DeviceIp}:{DevicePort}");
            }
            catch (Exception ex)
            {
                string errorMessage = $"修改设备连接参数失败: {ex.Message}";
                _logger.Error(errorMessage, ex);
                HcGrowlExtensions.Error(errorMessage);
            }
        }

        /// <summary>
        /// 刷新设备状态
        /// </summary>
        [RelayCommand]
        private void RefreshDeviceStatus()
        {
            try
            {
                UpdateDeviceStatus();
                HcGrowlExtensions.Success("设备状态已刷新");
            }
            catch (Exception ex)
            {
                string errorMessage = $"刷新设备状态失败: {ex.Message}";
                _logger.Error(errorMessage, ex);
                HcGrowlExtensions.Error(errorMessage);
            }
        }

        #endregion 命令

        #region 方法

        /// <summary>
        /// 初始化配置，导航到首页
        /// </summary>
        public async Task Configure()
        {
            if (Golbal.IsDevDebug)
            {
                await ConnectDevicesAsync();
            }

            //_regionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(nameof(IR400View));//导航至页面
        }

        /// <summary>
        /// 加载批量命令序列
        /// </summary>
        private void LoadBatchCommandSequences()
        {
            try
            {
                string configPath = App.ConfigHelper.GetConfigFilePath("Configs/LoopConfigTest/BatchCommands.json");
                _batchCommandParser = new BatchCommandParser(configPath);

                AvailableBatchSequences.Clear();
                var sequences = _batchCommandParser.GetAllBatchSequences();
                foreach (var sequence in sequences)
                {
                    AvailableBatchSequences.Add(sequence);
                }

                if (AvailableBatchSequences.Count > 0)
                {
                    SelectedBatchSequence = AvailableBatchSequences[0];
                    UILogService.AddSuccessLog($"已加载{AvailableBatchSequences.Count}个批量命令序列");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"加载批量命令序列失败: {ex.Message}");
                UILogService.AddErrorLog($"加载批量命令序列失败: {ex.Message}");
            }
        }

        #endregion 方法

        #region 释放资源

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _logger.Info("正在释放资源");

                // 取消设备状态变化事件订阅
                if (_mcuCmdService != null)
                {
                    _mcuCmdService.Shuttle.StatusChanged -= Device_StatusChanged;
                    _mcuCmdService.Robot.StatusChanged -= Device_StatusChanged;
                    _mcuCmdService.ChamberA.StatusChanged -= Device_StatusChanged;
                    _mcuCmdService.ChamberB.StatusChanged -= Device_StatusChanged;
                }

                _pollingCts?.Cancel();
                _pollingCts?.Dispose();

                // ✅ 优化报告修复：避免在Dispose中使用.Wait()，使用ConfigureAwait(false)
                try
                {
                    _mcuCmdService?.DisconnectAllAsync().ConfigureAwait(false).GetAwaiter().GetResult();
                }
                catch (Exception disconnectEx)
                {
                    _logger.Warn($"断开连接时发生错误: {disconnectEx.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"释放资源时发生错误: {ex.Message}", ex);
            }
        }

        #endregion 释放资源

        #region 私有方法

        /// <summary>
        /// 获取选中的设备实例
        /// </summary>
        private McuDevice GetSelectedDevice()
        {
            // 将字符串转换为枚举类型
            if (Enum.TryParse<EnuMcuDeviceType>(SelectedDevice, true, out var deviceType))
            {
                return deviceType switch
                {
                    EnuMcuDeviceType.Shuttle => _mcuCmdService.Shuttle,
                    EnuMcuDeviceType.Robot => _mcuCmdService.Robot,
                    EnuMcuDeviceType.ChamberA => _mcuCmdService.ChamberA,
                    EnuMcuDeviceType.ChamberB => _mcuCmdService.ChamberB,
                    _ => throw new ArgumentException($"未知的设备类型: {deviceType}")
                };
            }
            else
            {
                throw new ArgumentException($"无法将'{SelectedDevice}'转换为有效的设备类型");
            }
        }

        /// <summary>
        /// 更新设备状态
        /// </summary>
        private void UpdateDeviceStatus()
        {
            try
            {
                // 获取新的状态
                var newStatuses = _mcuCmdService.GetAllDeviceStatus();

                // 检查状态是否有变化
                bool hasChanges = false;
                if (DeviceStatuses != null && DeviceStatuses.Count > 0)
                {
                    foreach (var key in newStatuses.Keys)
                    {
                        if (!DeviceStatuses.TryGetValue(key, out var status) || status != newStatuses[key])
                        {
                            hasChanges = true;
                            _logger.Debug($"设备 {key} 状态已变更: {(DeviceStatuses.ContainsKey(key) ? DeviceStatuses[key] : "Unknown")} -> {newStatuses[key]}");
                        }
                    }
                }
                else
                {
                    hasChanges = true;
                }

                // 更新状态字典
                DeviceStatuses = newStatuses;

                // 强制触发UI更新 - 使用手动调用OnPropertyChanged方法
                OnPropertyChanged(nameof(DeviceStatuses));

                // 为每个设备单独触发通知
                foreach (var key in DeviceStatuses.Keys)
                {
                    OnPropertyChanged($"DeviceStatuses[{key}]");
                }

                // 更新是否有任何设备连接的状态
                HasAnyDeviceConnected = DeviceStatuses.Any(kv => kv.Value == DeviceStatus.Connected || kv.Value == DeviceStatus.Busy);

                // 手动通知命令的可执行状态改变
                DisconnectDevicesCommand.NotifyCanExecuteChanged();
                ResetTaskCommand.NotifyCanExecuteChanged();

                if (hasChanges)
                {
                    _logger.Debug($"设备状态已更新: {string.Join(", ", DeviceStatuses.Select(kv => $"{kv.Key}={kv.Value}"))}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"更新设备状态时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 设备状态变化事件处理
        /// </summary>
        private void Device_StatusChanged(object sender, DeviceStatus e)
        {
            // 确保在UI线程上执行状态更新
            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                if (sender is McuDevice device)
                {
                    _logger.Debug($"接收到设备状态变化事件: {device.DeviceType} -> {e}");
                    // 当任何设备状态变化时更新状态字典
                    UpdateDeviceStatus();
                }
            }));
        }

        /// <summary>
        /// 根据设备类型获取对应的告警信息源标识
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>告警信息源标识</returns>
        private string GetAlarmSource(EnuMcuDeviceType deviceType)
        {
            return deviceType switch
            {
                EnuMcuDeviceType.Shuttle => "SHTL",
                EnuMcuDeviceType.Robot => "Robot",
                EnuMcuDeviceType.ChamberA => "Motor",
                EnuMcuDeviceType.ChamberB => "Motor",
                _ => "SHTL" // 默认为SHTL
            };
        }

        /// <summary>
        /// 根据设备名称获取对应的告警信息源标识
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>告警信息源标识</returns>
        private string GetAlarmSource(string deviceName)
        {
            // 尝试将字符串转换为枚举
            if (Enum.TryParse<EnuMcuDeviceType>(deviceName, true, out var deviceType))
            {
                return GetAlarmSource(deviceType);
            }
            else
            {
                // 处理特殊情况
                throw new ArgumentException($"未知的设备类型: {deviceName}");
            }
        }

        #endregion 私有方法
    }
}