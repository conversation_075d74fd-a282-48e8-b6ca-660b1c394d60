using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// 晶圆准备状态枚举
    /// </summary>
    public enum EnuWaferReadyStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 晶圆准备接收状态 (SP7: SP1 SP5) - slit door open + lift pin down
        /// </summary>
        [Description("准备接收")]
        ReadyReceive = 1,

        /// <summary>
        /// 晶圆准备输出状态 (SP8: SP1 SP4) - slit door open + lift pin up
        /// </summary>
        [Description("准备输出")]
        ReadyOut = 2,

        /// <summary>
        /// 晶圆已接收状态 (SP9: SP2 SP4) - slit door close + lift pin up
        /// </summary>
        [Description("已接收")]
        Received = 3,

        /// <summary>
        /// 晶圆输出状态 (SP10: SP2 SP5) - slit door close + lift pin down
        /// </summary>
        [Description("输出")]
        Out = 4
    }
}
