using System.Collections;
using System.Windows;
using System.Windows.Controls;

namespace Zishan.SS200.Cmd.Behaviors
{
    public static class SelectedItemsBehavior
    {
        public static readonly DependencyProperty SelectedItemsProperty =
            DependencyProperty.RegisterAttached(
                "SelectedItems",
                typeof(IList),
                typeof(SelectedItemsBehavior),
                new PropertyMetadata(null, OnSelectedItemsChanged));

        public static IList GetSelectedItems(DependencyObject obj)
        {
            return (IList)obj.GetValue(SelectedItemsProperty);
        }

        public static void SetSelectedItems(DependencyObject obj, IList value)
        {
            obj.SetValue(SelectedItemsProperty, value);
        }

        private static bool _isUpdating = false;

        private static void OnSelectedItemsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ListBox listBox)
            {
                listBox.SelectionChanged -= ListBox_SelectionChanged;
                listBox.SelectionChanged += ListBox_SelectionChanged;

                if (!_isUpdating)
                {
                    _isUpdating = true;
                    IList selectedItems = GetSelectedItems(listBox);
                    if (selectedItems != null)
                    {
                        listBox.SelectedItems.Clear();
                        foreach (var item in selectedItems)
                        {
                            listBox.SelectedItems.Add(item);
                        }
                    }
                    _isUpdating = false;
                }
            }
        }

        private static void ListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox)
            {
                if (!_isUpdating)
                {
                    _isUpdating = true;
                    IList selectedItems = GetSelectedItems(listBox);
                    if (selectedItems != null)
                    {
                        selectedItems.Clear();
                        foreach (var item in listBox.SelectedItems)
                        {
                            selectedItems.Add(item);
                        }
                    }
                    _isUpdating = false;
                }
            }
        }
    }
}