using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.ViewModels.Dock;
using Zishan.SS200.Cmd.Models;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// StatusProperties性能测试类
    /// 用于验证增量更新机制的性能改进效果
    /// </summary>
    public class StatusPropertiesPerformanceTest
    {
        /// <summary>
        /// 测试增量更新性能
        /// </summary>
        public static async Task TestIncrementalUpdatePerformance()
        {
            Console.WriteLine("=== StatusProperties 性能测试 ===");
            
            // 模拟创建ViewModel实例（需要根据实际情况调整）
            // var viewModel = new RobotStatusPanelViewModel();
            
            var stopwatch = new Stopwatch();
            var testResults = new List<TestResult>();

            // 测试场景1: 少量数据变更
            Console.WriteLine("\n测试场景1: 少量数据变更 (10% 数据变化)");
            for (int i = 0; i < 10; i++)
            {
                stopwatch.Restart();
                
                // 模拟少量数据变更
                // await SimulateSmallDataChange(viewModel);
                
                stopwatch.Stop();
                testResults.Add(new TestResult 
                { 
                    Scenario = "少量变更", 
                    Iteration = i + 1, 
                    ElapsedMs = stopwatch.ElapsedMilliseconds 
                });
                
                Console.WriteLine($"  迭代 {i + 1}: {stopwatch.ElapsedMilliseconds}ms");
                await Task.Delay(100); // 模拟实际使用间隔
            }

            // 测试场景2: 大量数据变更
            Console.WriteLine("\n测试场景2: 大量数据变更 (50% 数据变化)");
            for (int i = 0; i < 10; i++)
            {
                stopwatch.Restart();
                
                // 模拟大量数据变更
                // await SimulateLargeDataChange(viewModel);
                
                stopwatch.Stop();
                testResults.Add(new TestResult 
                { 
                    Scenario = "大量变更", 
                    Iteration = i + 1, 
                    ElapsedMs = stopwatch.ElapsedMilliseconds 
                });
                
                Console.WriteLine($"  迭代 {i + 1}: {stopwatch.ElapsedMilliseconds}ms");
                await Task.Delay(100);
            }

            // 测试场景3: 全量数据变更
            Console.WriteLine("\n测试场景3: 全量数据变更 (100% 数据变化)");
            for (int i = 0; i < 10; i++)
            {
                stopwatch.Restart();
                
                // 模拟全量数据变更
                // await SimulateFullDataChange(viewModel);
                
                stopwatch.Stop();
                testResults.Add(new TestResult 
                { 
                    Scenario = "全量变更", 
                    Iteration = i + 1, 
                    ElapsedMs = stopwatch.ElapsedMilliseconds 
                });
                
                Console.WriteLine($"  迭代 {i + 1}: {stopwatch.ElapsedMilliseconds}ms");
                await Task.Delay(100);
            }

            // 输出统计结果
            PrintStatistics(testResults);
        }

        /// <summary>
        /// 打印测试统计结果
        /// </summary>
        private static void PrintStatistics(List<TestResult> results)
        {
            Console.WriteLine("\n=== 性能测试统计结果 ===");
            
            var scenarios = results.GroupBy(r => r.Scenario);
            
            foreach (var scenario in scenarios)
            {
                var times = scenario.Select(s => s.ElapsedMs).ToList();
                var avg = times.Average();
                var min = times.Min();
                var max = times.Max();
                
                Console.WriteLine($"\n{scenario.Key}:");
                Console.WriteLine($"  平均耗时: {avg:F2}ms");
                Console.WriteLine($"  最小耗时: {min}ms");
                Console.WriteLine($"  最大耗时: {max}ms");
                Console.WriteLine($"  测试次数: {times.Count}");
            }
        }

        /// <summary>
        /// 内存使用情况测试
        /// </summary>
        public static void TestMemoryUsage()
        {
            Console.WriteLine("\n=== 内存使用情况测试 ===");
            
            var initialMemory = GC.GetTotalMemory(true);
            Console.WriteLine($"初始内存使用: {initialMemory / 1024 / 1024:F2} MB");

            // 模拟多次更新操作
            for (int i = 0; i < 100; i++)
            {
                // 模拟状态更新
                // viewModel.UpdateStatusPropertiesCore();
                
                if (i % 20 == 0)
                {
                    var currentMemory = GC.GetTotalMemory(false);
                    Console.WriteLine($"第{i}次更新后内存: {currentMemory / 1024 / 1024:F2} MB");
                }
            }

            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var finalMemory = GC.GetTotalMemory(true);
            Console.WriteLine($"最终内存使用: {finalMemory / 1024 / 1024:F2} MB");
            Console.WriteLine($"内存增长: {(finalMemory - initialMemory) / 1024 / 1024:F2} MB");
        }

        /// <summary>
        /// UI响应性测试
        /// </summary>
        public static async Task TestUIResponsiveness()
        {
            Console.WriteLine("\n=== UI响应性测试 ===");
            
            var uiUpdateTimes = new List<long>();
            
            for (int i = 0; i < 50; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                
                // 模拟UI更新
                // await Application.Current.Dispatcher.InvokeAsync(() =>
                // {
                //     viewModel.UpdateStatusPropertiesCore();
                // });
                
                stopwatch.Stop();
                uiUpdateTimes.Add(stopwatch.ElapsedMilliseconds);
                
                if (i % 10 == 0)
                {
                    Console.WriteLine($"UI更新 {i}: {stopwatch.ElapsedMilliseconds}ms");
                }
                
                await Task.Delay(50); // 模拟用户操作间隔
            }

            var avgUITime = uiUpdateTimes.Average();
            var maxUITime = uiUpdateTimes.Max();
            
            Console.WriteLine($"平均UI更新时间: {avgUITime:F2}ms");
            Console.WriteLine($"最大UI更新时间: {maxUITime}ms");
            Console.WriteLine($"UI响应性评级: {GetResponsivenessRating(avgUITime)}");
        }

        /// <summary>
        /// 获取响应性评级
        /// </summary>
        private static string GetResponsivenessRating(double avgTime)
        {
            return avgTime switch
            {
                < 16 => "优秀 (60+ FPS)",
                < 33 => "良好 (30+ FPS)",
                < 50 => "一般 (20+ FPS)",
                < 100 => "较差 (10+ FPS)",
                _ => "很差 (<10 FPS)"
            };
        }

        /// <summary>
        /// 测试结果数据结构
        /// </summary>
        private class TestResult
        {
            public string Scenario { get; set; }
            public int Iteration { get; set; }
            public long ElapsedMs { get; set; }
        }

        /// <summary>
        /// 运行所有性能测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("开始StatusProperties性能测试...\n");
            
            try
            {
                await TestIncrementalUpdatePerformance();
                TestMemoryUsage();
                await TestUIResponsiveness();
                
                Console.WriteLine("\n=== 测试完成 ===");
                Console.WriteLine("建议:");
                Console.WriteLine("1. 平均更新时间应小于16ms以保证流畅体验");
                Console.WriteLine("2. 内存增长应控制在合理范围内");
                Console.WriteLine("3. UI响应性应达到'良好'或以上级别");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            }
        }
    }
}
