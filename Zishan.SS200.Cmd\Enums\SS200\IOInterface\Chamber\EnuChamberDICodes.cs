using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.IOInterface.Chamber
{
    /// <summary>
    /// Process Chamber数字输入(DI)代码枚举
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuChamberDICodes
    {
        /// <summary>
        /// CV打开传感器 (PDI1: PDI_9)
        /// 接近传感器NPN - 到位:0
        /// </summary>
        [Description("CV打开传感器")]
        PDI1_CvOpenSensor = 1,

        /// <summary>
        /// CV关闭传感器 (PDI2: PDI_8)
        /// 接近传感器NPN - 到位:0
        /// </summary>
        [Description("CV关闭传感器")]
        PDI2_CvCloseSensor = 2,

        /// <summary>
        /// T/V打开传感器 (PDI3: PDI_12)
        /// 光学传感器NPN - 到位:0
        /// </summary>
        [Description("T/V打开传感器")]
        PDI3_TvOpenSensor = 3,

        /// <summary>
        /// T/V关闭传感器 (PDI4: PDI_13)
        /// 光学传感器NPN - 到位:0
        /// </summary>
        [Description("T/V关闭传感器")]
        PDI4_TvCloseSensor = 4,

        /// <summary>
        /// 前级开关 (PDI5: PDI_11)
        /// 开关 - vacuum:1 no vacuum:0
        /// </summary>
        [Description("前级开关")]
        PDI5_ForelineSwitch = 5,

        /// <summary>
        /// Torr开关 (PDI6: PDI_10)
        /// 开关 - vacuum:1 no vacuum:0
        /// </summary>
        [Description("Torr开关")]
        PDI6_TorrSwitch = 6,

        /// <summary>
        /// 等离子传感器1 (PDI7: PDI_1)
        /// NPN - plasma on:0 plasma off:1
        /// </summary>
        [Description("等离子传感器1")]
        PDI7_PlasmaSensor1 = 7,

        /// <summary>
        /// 等离子传感器2 (PDI8: PDI_2)
        /// NPN - plasma on:0 plasma off:1
        /// </summary>
        [Description("等离子传感器2")]
        PDI8_PlasmaSensor2 = 8,

        /// <summary>
        /// 匹配互锁开关1 (PDI9: PDI_15)
        /// 开关 - 备用
        /// </summary>
        [Description("匹配互锁开关1")]
        PDI9_MatchingInterlockSwitch1 = 9,

        /// <summary>
        /// 匹配互锁开关2 (PDI10: PDI_16)
        /// 开关 - 备用
        /// </summary>
        [Description("匹配互锁开关2")]
        PDI10_MatchingInterlockSwitch2 = 10,

        /// <summary>
        /// 温控器 (PDI11: PDI_3)
        /// 开关 - over temp:0 temp normal:1
        /// </summary>
        [Description("温控器")]
        PDI11_Thermostat = 11,

        /// <summary>
        /// Slit Door打开传感器 (PDI12: PDI_5)
        /// 接近传感器NPN - 到位:0
        /// </summary>
        [Description("Slit Door打开传感器")]
        PDI12_SlitDoorOpenSensor = 12,

        /// <summary>
        /// Slit Door关闭传感器 (PDI13: PDI_4)
        /// 接近传感器NPN - 到位:0
        /// </summary>
        [Description("Slit Door关闭传感器")]
        PDI13_SlitDoorCloseSensor = 13,

        /// <summary>
        /// Lift Pin上升传感器 (PDI14: PDI_6)
        /// 接近传感器NPN - 到位:0
        /// </summary>
        [Description("Lift Pin上升传感器")]
        PDI14_LiftPinUpSensor = 14,

        /// <summary>
        /// Lift Pin下降传感器 (PDI15: PDI_7)
        /// 接近传感器NPN - 到位:0
        /// </summary>
        [Description("Lift Pin下降传感器")]
        PDI15_LiftPinDownSensor = 15
    }
}
