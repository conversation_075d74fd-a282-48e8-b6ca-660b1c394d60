# Chamber子系统状态自动更新功能

## 功能概述

本功能实现了当Chamber相关的DI（数字输入）、DO（数字输出）值发生变化时，自动调用`UpdateChamberSubsystemStatus`方法更新Chamber子系统状态。

## 实现原理

### 1. 监听机制

系统通过监听以下线圈集合的变化来检测DI、DO值的更新：

- **ChamberA输入线圈**: `_mcuCmdService.ChaInputCoils`
- **ChamberA控制线圈**: `_mcuCmdService.ChaCoils`
- **ChamberB输入线圈**: `_mcuCmdService.ChbInputCoils`
- **ChamberB控制线圈**: `_mcuCmdService.ChbCoils`

### 2. 防抖机制

为了避免频繁更新导致的性能问题，系统采用了500ms的防抖延迟：

```csharp
// 初始化Chamber状态更新防抖定时器
_chamberStatusUpdateTimer = new DispatcherTimer();
_chamberStatusUpdateTimer.Interval = TimeSpan.FromMilliseconds(500); // 500ms防抖延迟
_chamberStatusUpdateTimer.Tick += (sender, e) =>
{
    _chamberStatusUpdateTimer.Stop();
    UpdateChamberSubsystemStatus(true);
};
```

### 3. 事件处理流程

1. **线圈集合变化监听**: 当线圈集合有新增或删除时，自动为新线圈添加监听器
2. **线圈值变化监听**: 监听每个线圈的`Coilvalue`属性变化
3. **防抖处理**: 检测到变化后，启动防抖定时器
4. **状态更新**: 防抖延迟结束后，调用`UpdateChamberSubsystemStatus`方法

## 核心方法

### InitializeChamberCoilChangeHandlers()

初始化Chamber线圈变化监听器，在构造函数中调用。

### OnChamberCoilCollectionChanged()

处理线圈集合变化事件，为新添加的线圈添加监听器。

### OnChamberCoilPropertyChanged()

处理单个线圈属性变化事件，当检测到`Coilvalue`变化时触发防抖更新。

## 日志记录

系统提供了详细的日志记录功能：

- **Debug级别**: 记录线圈值变化和状态更新详情
- **Info级别**: 记录初始化成功信息
- **Error级别**: 记录异常和错误信息

### 日志示例

```
[DEBUG] 检测到Chamber线圈值变化: ChamberA - PDI12 = True
[DEBUG] 开始更新Chamber子系统状态（由DI/DO值变化自动触发）
[DEBUG] Chamber子系统状态已更新: TriggerStatus=NoAlarm, RunStatus=Idle, SlitDoorStatus=Open...
```

## 使用方法

### 自动触发

当Chamber设备的DI、DO值发生变化时，系统会自动更新状态，无需手动干预。

### 手动触发

如果需要手动更新状态，可以通过以下方式：

1. **通过UI界面**：点击"更新Chamber子系统状态"按钮
2. **通过代码**：由于相关方法是私有的，建议通过UI界面触发
3. **间接触发**：通过修改线圈值来触发自动更新机制

```csharp
// 注意：UpdateChamberSubsystemStatus是私有方法，无法直接调用
// 建议通过UI界面的按钮来手动触发更新
```

## 性能考虑

1. **防抖机制**: 500ms防抖延迟避免频繁更新
2. **设备类型过滤**: 只监听Chamber相关的线圈变化
3. **异常处理**: 完善的异常处理确保系统稳定性
4. **资源管理**: 正确添加和移除事件监听器

## 故障排除

### 常见问题

1. **状态未自动更新**
   - 检查线圈监控是否已启动：`_mcuCmdService.StartCoilsMonitoring()`
   - 检查设备连接状态
   - 查看日志中是否有错误信息

2. **更新过于频繁**
   - 检查防抖定时器设置
   - 考虑增加防抖延迟时间

3. **内存泄漏**
   - 确保事件监听器正确移除
   - 检查定时器是否正确停止

### 调试技巧

1. **启用Debug日志**: 设置日志级别为Debug查看详细信息
2. **监控线圈值**: 通过UI界面观察线圈值变化
3. **检查状态表**: 观察状态表中的数据更新

## 相关文件

- `ViewModels/Dock/RobotStatusPanelViewModel.cs`: 主要实现文件
- `Services/CoilStatusHelper.cs`: 状态计算辅助类
- `Models/SS200/SubSystemStatus/Chamber/ChamberSubsystemStatus.cs`: 状态模型
- `Services/S200McuCmdService.cs`: MCU服务，提供线圈监控

## 扩展功能

如需为其他子系统（如Shuttle、Robot）添加类似的自动更新功能，可以参考Chamber的实现方式：

1. 添加对应的防抖定时器
2. 创建监听器初始化方法
3. 实现事件处理方法
4. 在构造函数中调用初始化方法
