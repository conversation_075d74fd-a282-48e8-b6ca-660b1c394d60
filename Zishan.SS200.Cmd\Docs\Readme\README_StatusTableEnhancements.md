# 状态表格增强功能

## 概述

本次更新为状态表格添加了设备类型过滤功能，现在可以按设备类型查看不同子系统的状态信息。

## 新增功能

### 1. 设备类型过滤

#### UI 增强
- **设备类型 ComboBox**: 在过滤查询前添加了设备类型选择下拉框
- **选项包括**:
  - 全部设备（显示所有子系统状态）
  - Robot（仅显示Robot子系统状态）
  - Shuttle（仅显示Shuttle子系统状态）
  - ChamberA（仅显示Chamber子系统状态）
  - ChamberB（预留，当前映射到ChamberA）

#### 表格列增强
- **子系统列**: 显示状态属于哪个子系统（Robot、Chamber、Shuttle）
- **设备类型列**: 显示对应的设备类型枚举值
- **参数名称列**: 现在包含子系统前缀，如 `[Robot] EnuRobotStatus`
- **状态值列**: 显示当前状态值

### 2. 数据模型增强

#### StatusProperty 类更新
```csharp
public class StatusProperty
{
    public string Name { get; set; }           // 参数名称（含子系统前缀）
    public string Value { get; set; }          // 状态值
    public EnuMcuDeviceType DeviceType { get; set; }  // 设备类型
    public string SubsystemType { get; set; }  // 子系统类型
}
```

#### DeviceTypeOption 类（新增）
```csharp
public class DeviceTypeOption
{
    public string DisplayName { get; set; }    // 显示名称
    public EnuMcuDeviceType? DeviceType { get; set; }  // 设备类型（null=全部）
}
```

### 3. ViewModel 功能增强

#### 新增属性
- `SelectedDeviceType`: 当前选择的设备类型过滤器
- `DeviceTypeOptions`: 可用的设备类型选项列表

#### 更新的方法
- `UpdateStatusProperties()`: 现在包含所有子系统的状态
- `AddSubsystemProperties()`: 新增方法，用于添加特定子系统的属性

### 4. 多子系统支持

现在状态表格同时显示：
- **Robot 子系统状态**: 包括位置、运动状态等
- **Chamber 子系统状态**: 包括触发状态、运行状态、位置状态、压力状态、气体状态等
- **Shuttle 子系统状态**: 包括穿梭机相关状态

## 使用方法

### 1. 查看所有状态
- 在设备类型下拉框中选择"全部设备"
- 表格将显示所有子系统的状态信息

### 2. 按设备类型过滤
- 选择特定的设备类型（Robot、Shuttle、ChamberA）
- 表格将仅显示该设备类型的状态信息

### 3. 文本搜索过滤
- 在搜索框中输入关键字
- 系统将过滤包含该关键字的参数名称或状态值

### 4. 组合过滤
- 可以同时使用设备类型过滤和文本搜索
- 两个过滤条件同时生效

### 5. 更新状态
- **刷新状态**: 更新Robot子系统状态
- **更新Chamber**: 更新Chamber子系统状态（新增按钮）
- 状态更新后表格会自动刷新

## 技术实现

### 过滤逻辑
1. **设备类型过滤**: 在 `AddSubsystemProperties` 方法中实现
2. **文本搜索过滤**: 在参数名称和状态值中搜索关键字
3. **实时更新**: 过滤条件变化时自动更新表格

### 数据绑定
- 使用 `ObservableCollection<StatusProperty>` 确保UI实时更新
- 通过 `PropertyChanged` 事件触发过滤逻辑
- 支持双向数据绑定

### UI布局
- 使用Grid布局管理过滤控件
- ComboBox和TextBox并排显示
- 按钮组合提供不同的操作选项

## 扩展性

### 添加新的设备类型
1. 在 `DeviceTypeOptions` 中添加新选项
2. 在 `UpdateStatusProperties` 中添加对应的子系统
3. 确保子系统状态对象已正确初始化

### 添加新的子系统
1. 创建子系统状态类
2. 在ViewModel中添加对应属性
3. 在 `UpdateStatusProperties` 中调用 `AddSubsystemProperties`

### 自定义过滤逻辑
- 可以在 `AddSubsystemProperties` 中添加更复杂的过滤条件
- 支持基于属性类型、值范围等的过滤

## 相关文件

- `Models/StatusProperty.cs`: 状态属性数据模型
- `Models/DeviceTypeOption.cs`: 设备类型选项模型
- `ViewModels/Dock/RobotStatusPanelViewModel.cs`: 主要业务逻辑
- `Views/Dock/RobotStatusPanel.xaml`: UI界面定义
- `README_ChamberStatusParsing.md`: Chamber状态解析功能文档
