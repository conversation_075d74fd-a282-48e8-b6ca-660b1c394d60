using System;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// RPS30访问器测试类
    /// 专门测试新增的RPS30 - 插销搜索Z轴高度参数访问器
    /// </summary>
    public class RPS30AccessorTest
    {
        /// <summary>
        /// 测试RPS30访问器的基本功能
        /// </summary>
        public static bool TestRPS30BasicAccess()
        {
            try
            {
                Console.WriteLine("测试RPS30访问器基本功能...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 测试RPS30访问器
                var rps30Accessor = robotConfig.RPS30_ZAxisHeightForPinSearch;
                
                Console.WriteLine($"  RPS30访问器创建: {(rps30Accessor != null ? "✓" : "✗")}");
                
                if (rps30Accessor != null)
                {
                    Console.WriteLine($"  RPS30参数代码: {rps30Accessor.Code}");
                    Console.WriteLine($"  RPS30参数值: {rps30Accessor.Value}");
                    Console.WriteLine($"  RPS30参数描述: {rps30Accessor.Content}");
                    Console.WriteLine($"  RPS30参数单位: {rps30Accessor.Unit}");
                    Console.WriteLine($"  RPS30轴类型: {rps30Accessor.AxisType}");
                }
                
                return rps30Accessor != null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"RPS30访问器基本功能测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试RPS30访问器的缓存机制
        /// </summary>
        public static bool TestRPS30CacheMechanism()
        {
            try
            {
                Console.WriteLine("测试RPS30访问器缓存机制...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 多次访问同一个参数，应该返回相同的对象实例（缓存机制）
                var rps30First = robotConfig.RPS30_ZAxisHeightForPinSearch;
                var rps30Second = robotConfig.RPS30_ZAxisHeightForPinSearch;
                var rps30Third = robotConfig.RPS30_ZAxisHeightForPinSearch;
                
                bool sameInstance = ReferenceEquals(rps30First, rps30Second) && 
                                   ReferenceEquals(rps30Second, rps30Third);
                
                Console.WriteLine($"  缓存机制测试: {(sameInstance ? "✓" : "✗")}");
                Console.WriteLine($"  第一次访问: {rps30First?.GetHashCode()}");
                Console.WriteLine($"  第二次访问: {rps30Second?.GetHashCode()}");
                Console.WriteLine($"  第三次访问: {rps30Third?.GetHashCode()}");
                
                return sameInstance;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"RPS30访问器缓存机制测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试RPS30与其他RPS参数的一致性
        /// </summary>
        public static bool TestRPS30Consistency()
        {
            try
            {
                Console.WriteLine("测试RPS30与其他RPS参数的一致性...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 获取几个RPS参数进行对比
                var rps1 = robotConfig.RPS1_RobotRotateSpeed;
                var rps29 = robotConfig.RPS29_ChamberPressureCheck;
                var rps30 = robotConfig.RPS30_ZAxisHeightForPinSearch;
                
                Console.WriteLine($"  RPS1访问器: {(rps1 != null ? "✓" : "✗")}");
                Console.WriteLine($"  RPS29访问器: {(rps29 != null ? "✓" : "✗")}");
                Console.WriteLine($"  RPS30访问器: {(rps30 != null ? "✓" : "✗")}");
                
                // 检查单位一致性（都应该是step）
                bool unitConsistency = true;
                if (rps1 != null && rps1.Unit != "step") unitConsistency = false;
                if (rps29 != null && rps29.Unit != "step") unitConsistency = false;
                if (rps30 != null && rps30.Unit != "step") unitConsistency = false;
                
                Console.WriteLine($"  单位一致性: {(unitConsistency ? "✓" : "✗")}");
                
                // 检查代码格式一致性
                bool codeConsistency = true;
                if (rps1 != null && rps1.Code != "RPS1") codeConsistency = false;
                if (rps29 != null && rps29.Code != "RPS29") codeConsistency = false;
                if (rps30 != null && rps30.Code != "RPS30") codeConsistency = false;
                
                Console.WriteLine($"  代码格式一致性: {(codeConsistency ? "✓" : "✗")}");
                
                return rps1 != null && rps29 != null && rps30 != null && 
                       unitConsistency && codeConsistency;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"RPS30一致性测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试RPS30的描述信息
        /// </summary>
        public static bool TestRPS30Description()
        {
            try
            {
                Console.WriteLine("测试RPS30描述信息...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                var rps30 = robotConfig.RPS30_ZAxisHeightForPinSearch;
                
                if (rps30 != null)
                {
                    bool hasDescription = !string.IsNullOrEmpty(rps30.Content);
                    bool hasCode = !string.IsNullOrEmpty(rps30.Code);
                    bool hasUnit = !string.IsNullOrEmpty(rps30.Unit);
                    
                    Console.WriteLine($"  有描述信息: {(hasDescription ? "✓" : "✗")} - '{rps30.Content}'");
                    Console.WriteLine($"  有参数代码: {(hasCode ? "✓" : "✗")} - '{rps30.Code}'");
                    Console.WriteLine($"  有单位信息: {(hasUnit ? "✓" : "✗")} - '{rps30.Unit}'");
                    Console.WriteLine($"  参数值: {rps30.Value}");
                    Console.WriteLine($"  轴类型: {rps30.AxisType}");
                    
                    return hasDescription && hasCode && hasUnit;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"RPS30描述信息测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行所有RPS30测试
        /// </summary>
        public static void RunAllRPS30Tests()
        {
            Console.WriteLine("=== RPS30访问器测试开始 ===\n");
            
            bool basicTest = TestRPS30BasicAccess();
            Console.WriteLine();
            
            bool cacheTest = TestRPS30CacheMechanism();
            Console.WriteLine();
            
            bool consistencyTest = TestRPS30Consistency();
            Console.WriteLine();
            
            bool descriptionTest = TestRPS30Description();
            Console.WriteLine();
            
            // 汇总测试结果
            int passedTests = 0;
            int totalTests = 4;
            
            if (basicTest) passedTests++;
            if (cacheTest) passedTests++;
            if (consistencyTest) passedTests++;
            if (descriptionTest) passedTests++;
            
            Console.WriteLine("=== RPS30测试结果汇总 ===");
            Console.WriteLine($"通过测试: {passedTests}/{totalTests}");
            Console.WriteLine($"测试状态: {(passedTests == totalTests ? "全部通过 ✓" : "部分失败 ✗")}");
            
            if (passedTests == totalTests)
            {
                Console.WriteLine("\n🎉 RPS30访问器测试全部通过！新增的RPS30参数访问器工作正常。");
            }
            else
            {
                Console.WriteLine("\n⚠️ 部分RPS30测试失败，请检查配置文件和实现。");
            }
        }
    }
}
