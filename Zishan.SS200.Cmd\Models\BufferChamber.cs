﻿using Zishan.SS200.Cmd.Enums;
using Prism.Mvvm;
using Zishan.SS200.Cmd.Models.IR400.Plc;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;

namespace Zishan.SS200.Cmd.Models
{
    public class BufferChamber : BaseContainer
    {
        /// <summary>
        /// Cha SlitDoor门状态
        /// </summary>
        public EnuSlitDoorStatus ChaSlitDoorStatus { get => _ChaSlitDoorStatus; set => SetProperty(ref _ChaSlitDoorStatus, value); }
        private EnuSlitDoorStatus _ChaSlitDoorStatus;

        /// <summary>
        /// Chb SlitDoor门状态
        /// </summary>
        public EnuSlitDoorStatus ChbSlitDoorStatus { get => _ChbSlitDoorStatus; set => SetProperty(ref _ChbSlitDoorStatus, value); }
        private EnuSlitDoorStatus _ChbSlitDoorStatus;

        /// <summary>
        /// Chc SlitDoor门状态
        /// </summary>
        public EnuSlitDoorStatus ChcSlitDoorStatus { get => _ChcSlitDoorStatus; set => SetProperty(ref _ChcSlitDoorStatus, value); }
        private EnuSlitDoorStatus _ChcSlitDoorStatus;

        /// <summary>
        /// LoadLock SlitDoor门状态
        /// </summary>
        public EnuSlitDoorStatus LoadLocklitDoorStatus { get => _LoadLocklitDoorStatus; set => SetProperty(ref _LoadLocklitDoorStatus, value); }
        private EnuSlitDoorStatus _LoadLocklitDoorStatus;

        public BufferChamber()
        {
        }
    }
}