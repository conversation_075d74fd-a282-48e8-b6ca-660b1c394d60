﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot
{
    /// <summary>
    /// Robot IO代码枚举
    /// </summary>
    [TypeConverter(typeof(EnuRobotDICodes))]
    public enum EnuRobotDICodes
    {
        /// <summary>
        /// Paddle传感器1左侧 (RDI1: SDI_26/RDI_26)
        /// 光学传感器NPN - 无晶圆:0 有晶圆:1
        /// </summary>
        [Description("Paddle传感器1左侧")]
        RDI1_PaddleSensor1Left = 1,

        /// <summary>
        /// Paddle传感器2右侧 (RDI2: SDI_27/RDI_25)
        /// 光学传感器NPN - 无晶圆:0 有晶圆:1
        /// </summary>
        [Description("Paddle传感器2右侧")]
        RDI2_PaddleSensor2Right = 2,

        /// <summary>
        /// Pin搜索1 (RDI3: RDI_31)
        /// 开关 - 检测到:0 未检测到:1
        /// </summary>
        [Description("Pin搜索1")]
        RDI3_PinSearch1 = 3,

        /// <summary>
        /// Pin搜索2 (RDI4: RDI_32)
        /// 开关 - 检测到:0 未检测到:1
        /// </summary>
        [Description("Pin搜索2")]
        RDI4_PinSearch2 = 4
    }
}