# SS200InterLockMain 使用指南

## 快速开始

### 1. 基本用法

```csharp
// 获取单例实例
var interlock = SS200InterLockMain.Instance;

// 读取机器人传感器状态
bool paddleStatus = interlock.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;

// 获取报警信息
string alarmContent = interlock.AlarmCode.Robot.RA1_SystemBusyReject.Content;

// 读取配置参数
int tAxisPosition = interlock.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Value;

// 获取运行状态
var robotStatus = interlock.SubsystemStatus.Robot.RS1_RobotStatus.Value;
```

### 2. 访问模式说明

#### IO访问模式
```csharp
// 格式：SS200InterLockMain.Instance.IOInterface.{设备}.{IO代码}.Value
var robotDI1 = interlock.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;      // 机器人DI1
var chamberDO5 = interlock.IOInterface.Chamber.PDO5_VacuumValveControl.Value;  // 腔体DO5
var shuttleDI10 = interlock.IOInterface.Shuttle.SDI10_PositionSensor.Value;    // 穿梭机DI10
```

#### 报警访问模式
```csharp
// 格式：SS200InterLockMain.Instance.AlarmCode.{设备}.{报警代码}.Content
var robotAlarm = interlock.AlarmCode.Robot.RA1_SystemBusyReject.Content;       // 机器人报警
var chamberAlarm = interlock.AlarmCode.Chamber.CA5_VacuumFailure.Content;      // 腔体报警
var shuttleAlarm = interlock.AlarmCode.Shuttle.SA10_MotionTimeout.Content;     // 穿梭机报警
```

#### 配置访问模式
```csharp
// 格式：SS200InterLockMain.Instance.SubsystemConfigure.{配置类型}.{参数代码}.Value
var robotPos = interlock.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Value;    // 机器人位置参数
var chamberParam = interlock.SubsystemConfigure.ProcessValue.CP5_PressureLimit.Value; // 腔体工艺参数
var shuttleSpeed = interlock.SubsystemConfigure.MotionValue.SP10_MaxSpeed.Value;      // 穿梭机运动参数
```

#### 状态访问模式
```csharp
// 格式：SS200InterLockMain.Instance.SubsystemStatus.{设备}.{状态代码}.Value
var robotStatus = interlock.SubsystemStatus.Robot.RS1_RobotStatus.Value;       // 机器人状态
var chamberStatus = interlock.SubsystemStatus.Chamber.CS5_ProcessStatus.Value; // 腔体状态
var shuttleStatus = interlock.SubsystemStatus.Shuttle.SS10_MotionStatus.Value; // 穿梭机状态
```

## 常用操作示例

### 1. 批量读取IO状态

```csharp
public Dictionary<string, bool> GetAllRobotDIStatus()
{
    var robot = SS200InterLockMain.Instance.IOInterface.Robot;
    return new Dictionary<string, bool>
    {
        ["RDI1_PaddleSmoothSensor"] = robot.RDI1_PaddleSmoothSensor.Value,
        ["RDI2_PaddleNoseSensor"] = robot.RDI2_PaddleNoseSensor.Value,
        ["RDI3_PinSearchSmoothSensor"] = robot.RDI3_PinSearchSmoothSensor.Value,
        ["RDI4_PinSearchNoseSensor"] = robot.RDI4_PinSearchNoseSensor.Value
    };
}
```

### 2. 检查系统状态

```csharp
public bool IsSystemReady()
{
    var interlock = SS200InterLockMain.Instance;
    
    // 检查机器人状态
    var robotStatus = interlock.SubsystemStatus.Robot.RS1_RobotStatus.Value;
    if (robotStatus != EnuRobotStatus.Ready)
        return false;
    
    // 检查腔体状态
    var chamberStatus = interlock.SubsystemStatus.Chamber.CS1_ChamberStatus.Value;
    if (chamberStatus != EnuChamberStatus.Ready)
        return false;
    
    // 检查穿梭机状态
    var shuttleStatus = interlock.SubsystemStatus.Shuttle.SS1_ShuttleStatus.Value;
    if (shuttleStatus != EnuShuttleStatus.Ready)
        return false;
    
    return true;
}
```

### 3. 获取配置信息

```csharp
public void PrintRobotConfiguration()
{
    var config = SS200InterLockMain.Instance.SubsystemConfigure.PositionValue;
    
    Console.WriteLine("=== 机器人位置配置 ===");
    Console.WriteLine($"T轴位置1: {config.RP1_TAxisPosition.Value}");
    Console.WriteLine($"R轴位置1: {config.RP2_RAxisPosition.Value}");
    Console.WriteLine($"Z轴位置1: {config.RP3_ZAxisPosition.Value}");
    // ... 更多配置参数
}
```

### 4. 监控报警状态

```csharp
public List<string> GetActiveAlarms()
{
    var alarms = new List<string>();
    var alarmCode = SS200InterLockMain.Instance.AlarmCode;
    
    // 检查机器人报警
    if (IsAlarmActive("RA1"))
        alarms.Add(alarmCode.Robot.RA1_SystemBusyReject.Content);
    
    // 检查腔体报警
    if (IsAlarmActive("CA1"))
        alarms.Add(alarmCode.Chamber.CA1_VacuumFailure.Content);
    
    // 检查穿梭机报警
    if (IsAlarmActive("SA1"))
        alarms.Add(alarmCode.Shuttle.SA1_MotionTimeout.Content);
    
    return alarms;
}

private bool IsAlarmActive(string alarmCode)
{
    // 实现报警状态检查逻辑
    // 这里需要根据实际的报警检测机制来实现
    return false;
}
```

## 最佳实践

### 1. 性能优化

```csharp
// ✅ 推荐：缓存频繁访问的实例
private static readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;

public bool GetSensorStatus()
{
    return _interlock.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;
}

// ❌ 不推荐：每次都访问 Instance
public bool GetSensorStatusBad()
{
    return SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;
}
```

### 2. 错误处理

```csharp
public bool SafeGetSensorStatus()
{
    try
    {
        return SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;
    }
    catch (Exception ex)
    {
        // 记录错误日志
        Console.WriteLine($"获取传感器状态失败: {ex.Message}");
        return false; // 返回安全的默认值
    }
}
```

### 3. 异步操作

```csharp
public async Task<Dictionary<string, bool>> GetAllIOStatusAsync()
{
    return await Task.Run(() =>
    {
        var interlock = SS200InterLockMain.Instance;
        return new Dictionary<string, bool>
        {
            ["Robot_RDI1"] = interlock.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value,
            ["Chamber_PDI1"] = interlock.IOInterface.Chamber.PDI1_DoorSensor.Value,
            ["Shuttle_SDI1"] = interlock.IOInterface.Shuttle.SDI1_PositionSensor.Value
        };
    });
}
```

## 注意事项

1. **线程安全**：SS200InterLockMain 是线程安全的，可以在多线程环境中使用
2. **性能考虑**：避免在循环中频繁访问 Instance 属性
3. **错误处理**：始终包含适当的异常处理逻辑
4. **资源管理**：系统会自动管理资源，无需手动释放
5. **配置更新**：配置更改后需要重启应用程序才能生效

## 故障排除

### 常见问题

1. **"配置未找到"警告**
   - 检查配置文件是否存在
   - 验证配置文件格式是否正确
   - 确认枚举值与配置文件中的键匹配

2. **IO访问失败**
   - 检查 MCU 服务是否正常运行
   - 验证设备连接状态
   - 确认 IO 地址映射是否正确

3. **性能问题**
   - 使用缓存减少重复访问
   - 避免在UI线程中进行耗时操作
   - 考虑使用异步方法

### 调试技巧

```csharp
// 启用详细日志
public void EnableDebugLogging()
{
    var interlock = SS200InterLockMain.Instance;
    // 输出配置信息用于调试
    foreach (var config in interlock.SubsystemConfigure.PositionValue.RobotPositionValue)
    {
        Console.WriteLine($"配置: {config.Code} = {config.Value} ({config.Description})");
    }
}
```

这个使用指南提供了完整的使用方法和最佳实践，帮助开发者快速上手并正确使用 SS200InterLockMain 系统。
