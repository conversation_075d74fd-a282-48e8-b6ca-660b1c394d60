using System;
using <PERSON>ishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberA;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberB;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// ChamberA和ChamberB配置分离测试
    /// 验证两个Chamber能够独立读取各自的配置文件
    /// </summary>
    public class ChamberConfigSeparationTest
    {
        /// <summary>
        /// 测试ChamberA和ChamberB配置分离
        /// </summary>
        public static bool TestChamberConfigSeparation()
        {
            try
            {
                Console.WriteLine("=== 测试ChamberA和ChamberB配置分离 ===");
                
                var ss200Main = SS200InterLockMain.Instance;
                
                // 获取ChamberA和ChamberB的配置访问器
                var chamberAConfig = ss200Main.SubsystemConfigure.ChamberA; // ChamberAConfigureAccessor
                var chamberBConfig = ss200Main.SubsystemConfigure.ChamberB; // ChamberBConfigureAccessor
                
                Console.WriteLine("✓ 成功获取ChamberA和ChamberB配置访问器");
                
                // 测试配置参数访问
                var chamberA_PPS1 = chamberAConfig.PPS1_SlitDoorMotionMinTime;
                var chamberB_PPS1 = chamberBConfig.PPS1_SlitDoorMotionMinTime;
                
                Console.WriteLine($"ChamberA PPS1 访问器: {(chamberA_PPS1 != null ? "✓" : "✗")}");
                Console.WriteLine($"ChamberB PPS1 访问器: {(chamberB_PPS1 != null ? "✓" : "✗")}");
                
                if (chamberA_PPS1 != null && chamberB_PPS1 != null)
                {
                    Console.WriteLine($"ChamberA PPS1 值: {chamberA_PPS1.Value}");
                    Console.WriteLine($"ChamberB PPS1 值: {chamberB_PPS1.Value}");
                    
                    // 验证两个配置访问器是不同的实例
                    bool isDifferentInstance = !ReferenceEquals(chamberAConfig, chamberBConfig);
                    Console.WriteLine($"ChamberA和ChamberB是不同实例: {(isDifferentInstance ? "✓" : "✗")}");
                    
                    return isDifferentInstance;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试配置提供者分离
        /// </summary>
        public static bool TestConfigProviderSeparation()
        {
            try
            {
                Console.WriteLine("\n=== 测试配置提供者分离 ===");
                
                // 直接测试配置提供者
                var chamberAProvider = ChaConfigParametersProvider.Instance;
                var chamberBProvider = ChbConfigParametersProvider.Instance;
                
                Console.WriteLine("✓ 成功获取ChamberA和ChamberB配置提供者");
                
                // 验证两个提供者是不同的实例
                bool isDifferentProvider = !ReferenceEquals(chamberAProvider, chamberBProvider);
                Console.WriteLine($"ChamberA和ChamberB提供者是不同实例: {(isDifferentProvider ? "✓" : "✗")}");
                
                // 测试配置文件路径
                Console.WriteLine("配置文件路径测试:");
                Console.WriteLine("- ChamberA: Configs/SS200/SubsystemConfigure/ChamberA/ChaConfigParameters.json");
                Console.WriteLine("- ChamberB: Configs/SS200/SubsystemConfigure/ChamberB/ChbConfigParameters.json");
                
                return isDifferentProvider;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始ChamberA和ChamberB配置分离测试...\n");
            
            bool test1 = TestChamberConfigSeparation();
            bool test2 = TestConfigProviderSeparation();
            
            Console.WriteLine("\n=== 测试结果汇总 ===");
            Console.WriteLine($"配置访问器分离测试: {(test1 ? "✓ 通过" : "✗ 失败")}");
            Console.WriteLine($"配置提供者分离测试: {(test2 ? "✓ 通过" : "✗ 失败")}");
            
            bool allPassed = test1 && test2;
            Console.WriteLine($"\n总体结果: {(allPassed ? "✓ 所有测试通过" : "✗ 部分测试失败")}");
            
            if (allPassed)
            {
                Console.WriteLine("\n🎉 ChamberA和ChamberB配置已成功分离！");
                Console.WriteLine("现在两个Chamber可以独立读取各自的配置文件了。");
            }
        }
    }
}
