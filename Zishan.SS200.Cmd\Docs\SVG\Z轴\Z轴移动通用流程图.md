# SS-200机器人系统Z轴移动通用流程图 (AR21-AR33)

## 概述

基于对AR21到AR33文档的深度分析，本流程图整合了所有Z轴移动操作的共同模式和逻辑，包括高度移动操作（Get/Put）和零位操作，适用于CHA、CHB、CT、CB等所有目标位置的Z轴移动操作。

## 流程图

```mermaid
flowchart TD
    Start([开始: Z轴移动命令]) --> CheckRobotStatus{检查机器人状态<br/>MRS1~MRS3}
    
    CheckRobotStatus -->|MRS1 IDLE| CheckOperationType{检查操作类型}
    CheckRobotStatus -->|MRS2 BUSY| AlarmRA1[RA1 ALARM<br/>机器人忙碌]
    CheckRobotStatus -->|MRS3 ALARM| AlarmRA2[RA2 ALARM<br/>机器人报警]
    
    CheckOperationType -->|Z轴零位操作| CheckTAxisForZero{检查T轴位置<br/>RS9?}
    CheckOperationType -->|高度移动操作| CheckSlideOutSensor{检查滑出传感器<br/>安装状态 SPS11}
    
    CheckTAxisForZero -->|RS9| CheckRAxisForZero{检查R轴位置<br/>RS18?}
    CheckTAxisForZero -->|其他位置| AlarmRA11[RA11 ALARM<br/>T轴位置错误]
    
    CheckRAxisForZero -->|RS18| ExecuteZeroMove[执行Z轴零位移动<br/>AR39-RP27]
    CheckRAxisForZero -->|其他位置| AlarmRA12[RA12 ALARM<br/>R轴位置错误]
    
    ExecuteZeroMove --> CommandDone([命令完成])
    
    CheckSlideOutSensor -->|SPS11=Y<br/>已安装| CheckSensorStatus{检查滑出传感器状态<br/>DI19/DI20}
    CheckSlideOutSensor -->|SPS11=N<br/>未安装| CheckTAxisPosition{检查T轴位置}
    
    CheckSensorStatus -->|DI19=0 DI20=0<br/>正常| CheckTAxisPosition
    CheckSensorStatus -->|DI19=0 DI20=1| AlarmRA20[RA20 ALARM<br/>传感器状态异常]
    CheckSensorStatus -->|DI19=1 DI20=0| AlarmRA19[RA19 ALARM<br/>传感器状态异常]
    CheckSensorStatus -->|DI19=1 DI20=1| AlarmRA21[RA21 ALARM<br/>传感器状态异常]
    
    CheckTAxisPosition -->|位置匹配| CheckChamberPressure{检查腔室压力<br/>RPS29}
    CheckTAxisPosition -->|位置不匹配| AlarmRA12B[RA12 ALARM<br/>T轴位置错误]
    
    CheckChamberPressure -->|RPS29=Y<br/>需要压力补偿| CheckLoadlockPressure{检查负载锁压力<br/>SP13/SP14}
    CheckChamberPressure -->|RPS29=N<br/>无需压力补偿| CalculateBasePosition[/计算基础位置<br/>根据目标和桨叶类型/]
    
    CheckLoadlockPressure -->|SP13<br/>负载锁真空| CalculateWithVacuum[/计算位置<br/>基础位置 - RPS16/]
    CheckLoadlockPressure -->|SP14<br/>负载锁非真空| CalculateWithoutVacuum[/计算位置<br/>基础位置/]
    
    CalculateBasePosition --> CheckOperationTypeForOffset{检查操作类型<br/>Get/Put?}
    CalculateWithVacuum --> CheckOperationTypeForOffset
    CalculateWithoutVacuum --> CheckOperationTypeForOffset
    
    CheckOperationTypeForOffset -->|Get操作| FinalPosition[/最终位置 = 计算位置/]
    CheckOperationTypeForOffset -->|Put操作| AddTransferOffset[/最终位置 = 计算位置 + RPS24/]
    
    AddTransferOffset --> ExecuteZAxisMove[执行Z轴移动<br/>AR39-最终位置]
    FinalPosition --> ExecuteZAxisMove
    
    ExecuteZAxisMove --> CheckSensorAfterMove{检查传感器安装<br/>SPS11}
    
    CheckSensorAfterMove -->|SPS11=Y<br/>已安装| FinalSensorCheck{最终传感器检查<br/>DI19/DI20}
    CheckSensorAfterMove -->|SPS11=N<br/>未安装| CommandDone
    
    FinalSensorCheck -->|DI19=0 DI20=0<br/>正常| CommandDone
    FinalSensorCheck -->|DI19=0 DI20=1| AlarmRA20B[RA20 ALARM<br/>最终传感器异常]
    FinalSensorCheck -->|DI19=1 DI20=0| AlarmRA19B[RA19 ALARM<br/>最终传感器异常]
    FinalSensorCheck -->|DI19=1 DI20=1| AlarmRA21B[RA21 ALARM<br/>最终传感器异常]
    
    %% 报警处理
    AlarmRA1 --> End([流程结束])
    AlarmRA2 --> End
    AlarmRA11 --> End
    AlarmRA12 --> End
    AlarmRA12B --> End
    AlarmRA19 --> End
    AlarmRA19B --> End
    AlarmRA20 --> End
    AlarmRA20B --> End
    AlarmRA21 --> End
    AlarmRA21B --> End
    CommandDone --> End
    
    %% 位置映射说明
    subgraph PositionMapping["位置映射关系"]
        direction TB
        PM1["CHA: RP19(smooth get), RP23(nose get)"]
        PM2["CHB: RP20(smooth get), RP24(nose get)"]
        PM3["CT: RP21(smooth get/put), RP25(nose get/put)"]
        PM4["CB: RP22(smooth get/put), RP26(nose get/put)"]
        PM5["Zero: RP27"]
    end
    
    %% T轴位置映射说明
    subgraph TAxisMapping["T轴位置要求"]
        direction TB
        TM1["RS1: CHA smooth操作"]
        TM2["RS2: CHB smooth操作"]
        TM3["RS3: CT/CB smooth操作"]
        TM4["RS5: CHA nose操作"]
        TM5["RS6: CHB nose操作"]
        TM6["RS7: CT/CB nose操作"]
        TM7["RS9: Z轴零位操作"]
    end
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef calculation fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef alarm fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef mapping fill:#f1f8e9,stroke:#33691e,stroke-width:1px
    
    class Start,End,CommandDone startEnd
    class CheckRobotStatus,CheckOperationType,CheckTAxisForZero,CheckRAxisForZero,CheckSlideOutSensor,CheckSensorStatus,CheckTAxisPosition,CheckChamberPressure,CheckLoadlockPressure,CheckOperationTypeForOffset,CheckSensorAfterMove,FinalSensorCheck decision
    class ExecuteZeroMove,ExecuteZAxisMove process
    class CalculateBasePosition,CalculateWithVacuum,CalculateWithoutVacuum,AddTransferOffset,FinalPosition calculation
    class AlarmRA1,AlarmRA2,AlarmRA11,AlarmRA12,AlarmRA12B,AlarmRA19,AlarmRA19B,AlarmRA20,AlarmRA20B,AlarmRA21,AlarmRA21B alarm
    class PM1,PM2,PM3,PM4,PM5,TM1,TM2,TM3,TM4,TM5,TM6,TM7 mapping
```

## 核心流程结构

### 1. 系统状态检查层
- **机器人状态检查** (MRS1~MRS3)：确保机器人处于可操作状态
- **操作类型判断**：区分零位操作和高度移动操作

### 2. 传感器检查层
- **滑出传感器安装检查** (SPS11)：Y=已安装，N=未安装
- **滑出传感器状态检查** (DI19/DI20)：四种组合状态的处理
  - `00`：正常状态，继续执行
  - `01`、`10`、`11`：异常状态，触发相应报警

### 3. 位置验证层
- **T轴位置检查**：根据操作类型验证当前T轴位置
- **R轴位置检查**：仅零位操作需要验证R轴位置

### 4. 压力系统检查层
- **腔室压力检查** (RPS29)：决定是否需要压力补偿
- **负载锁压力检查** (SP13/SP14)：决定压力补偿的具体值

### 5. 位置计算层
- **基础位置计算**：根据目标位置和桨叶类型选择基础位置
- **压力补偿计算**：SP13状态下减去RPS16补偿值
- **传输偏移计算**：Put操作需要加上RPS24偏移量

### 6. 执行层
- **Z轴移动执行** (AR39-计算后的位置)：执行实际的Z轴移动
- **移动完成检查**：验证移动是否成功完成
- **最终传感器检查**：移动完成后再次确认传感器状态

## 位置计算公式

### Get操作
- **无压力补偿**：最终位置 = 基础位置
- **有压力补偿**：
  - SP13(真空)：最终位置 = 基础位置 - RPS16
  - SP14(非真空)：最终位置 = 基础位置

### Put操作
- **无压力补偿**：最终位置 = 基础位置 + RPS24
- **有压力补偿**：
  - SP13(真空)：最终位置 = 基础位置 + RPS24 - RPS16
  - SP14(非真空)：最终位置 = 基础位置 + RPS24

### Zero操作
- **直接移动**：最终位置 = RP27

## 位置映射关系

| 目标位置 | Smooth Get | Nose Get | Smooth Put | Nose Put |
|---------|-----------|----------|-----------|----------|
| CHA | RP19 | RP23 | - | - |
| CHB | RP20 | RP24 | - | - |
| CT | RP21 | RP25 | RP21+RPS24 | RP25+RPS24 |
| CB | RP22 | RP26 | RP22+RPS24 | RP26+RPS24 |
| Zero | - | - | - | RP27 |

## T轴位置要求

| T轴位置 | 对应操作 |
|--------|---------|
| RS1 | CHA smooth操作 |
| RS2 | CHB smooth操作 |
| RS3 | CT/CB smooth操作 |
| RS5 | CHA nose操作 |
| RS6 | CHB nose操作 |
| RS7 | CT/CB nose操作 |
| RS9 | Z轴零位操作 |

## 错误处理机制

| 报警代码 | 描述 | 触发条件 |
|---------|------|---------|
| RA1 | 机器人忙碌 | MRS2 BUSY |
| RA2 | 机器人报警 | MRS3 ALARM |
| RA11 | T轴位置错误 | 零位操作时T轴不在RS9 |
| RA12 | 轴位置错误 | T轴或R轴位置不匹配 |
| RA19/RA20/RA21 | 传感器状态异常 | DI19/DI20异常组合 |

## 关键特性

1. **操作类型区分**：零位操作和高度移动操作采用不同的流程
2. **复杂位置计算**：支持压力补偿和传输偏移的组合计算
3. **传感器状态管理**：移动前后都进行传感器状态检查
4. **压力系统集成**：与腔室和负载锁压力系统紧密集成
5. **完整覆盖**：涵盖了AR21-AR33中所有的逻辑分支和异常处理

## 使用说明

本流程图可以作为SS-200机器人系统Z轴移动操作的标准参考，帮助：
- 理解Z轴移动的完整流程和位置计算逻辑
- 实现各种Z轴移动功能
- 进行系统调试和故障排除
- 制定操作规程和培训材料

## 相关文档

- AR21: move Z-axis height at smooth to CHA get position
- AR22: move Z-axis height at smooth to CHB get position
- AR23: move Z-axis height at smooth to CT get position
- AR24: move Z-axis height at smooth to CB get position
- AR25: move Z-axis height at nose to CHA get position
- AR26: move Z-axis height at nose to CHB get position
- AR27: move Z-axis height at nose to CT get position
- AR28: move Z-axis height at nose to CB get position
- AR29: move Z-axis height at smooth to CT put position
- AR30: move Z-axis height at smooth to CB put position
- AR31: move Z-axis height at nose to CT put position
- AR32: move Z-axis height at nose to CB put position
- AR33: Z-axis zero

---
*文档生成时间：2025-07-10*  
*基于文档：AR21-AR33 机器人系统逻辑*
