# SS200InterLockMain 简明使用指南

## 🎯 什么是SS200InterLockMain？

想象一下，SS200InterLockMain就像是一个**智能控制中心**，它能帮你：
- 📊 **查看设备状态** - 就像看仪表盘一样，了解所有设备的工作情况
- 🚨 **获取报警信息** - 当设备出现问题时，告诉你具体是什么问题
- ⚙️ **读取配置参数** - 查看设备的各种设置参数
- 🔌 **检查IO状态** - 了解各种传感器和开关的状态

## 🏗️ 系统结构（用生活化比喻）

### 就像一栋智能大楼的管理系统

```
SS200InterLockMain (总控制室)
├── IOInterface (传感器监控室)
│   ├── Robot (机器人区域)
│   ├── ChamberA (工作间A)
│   ├── ChamberB (工作间B)
│   └── Shuttle (运输系统)
├── AlarmCode (报警中心)
├── SubsystemConfigure (配置管理室)
└── SubsystemStatus (状态监控室)
```

## 🚀 基本使用方法

### 1. 获取控制中心实例
```csharp
// 就像走进控制室，获取控制面板
var controlCenter = SS200InterLockMain.Instance;
```

### 2. 查看传感器状态（IO接口）

#### 机器人传感器状态
```csharp
// 检查机器人的"手"上是否有晶圆
bool leftPaddleHasWafer = controlCenter.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
bool rightPaddleHasWafer = controlCenter.IOInterface.Robot.RDI2_PaddleSensor2Right.Value;

// 获取传感器的中文说明
string leftPaddleDescription = controlCenter.IOInterface.Robot.RDI1_PaddleSensor1Left.Content;
// 结果：leftPaddleDescription = "Paddle传感器1左侧"
```

#### 工作间（Chamber）传感器状态
```csharp
// 检查工作间A的门是否打开
bool chamberADoorOpen = controlCenter.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
bool chamberADoorClosed = controlCenter.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor.Value;

// 获取传感器说明
string doorOpenDescription = controlCenter.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Content;
// 结果：doorOpenDescription = "Slit Door Open传感器"
```

#### 运输系统（Shuttle）传感器状态
```csharp
// 检查晶圆盒是否存在
bool cassette1Present = controlCenter.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Value;
bool cassette2Present = controlCenter.IOInterface.Shuttle.SDI7_PresentSensorCassette2.Value;

// 获取传感器说明
string cassette1Description = controlCenter.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Content;
// 结果：cassette1Description = "存在传感器晶圆盒1"
```

### 3. 查看报警信息

#### 机器人报警
```csharp
// 获取机器人系统忙碌报警信息
var robotBusyAlarm = controlCenter.AlarmCode.Robot.RA1_SystemBusyReject;

string alarmCode = robotBusyAlarm.Code;           // 报警代码：RA1
string englishDesc = robotBusyAlarm.Content;      // 英文描述
string chineseDesc = robotBusyAlarm.ChsContent;   // 中文描述
string alarmCause = robotBusyAlarm.Cause;         // 报警原因
```

#### 工作间报警
```csharp
// 获取工作间系统异常报警
var chamberAlarm = controlCenter.AlarmCode.ChamberA.PAC1_SystemAbnormalReject;

string chamberAlarmCode = chamberAlarm.Code;      // 报警代码：PAC1
string chamberAlarmDesc = chamberAlarm.ChsContent; // 中文描述
```

#### Shuttle报警
```csharp
// 获取Shuttle系统忙碌报警
var shuttleAlarm = controlCenter.AlarmCode.Shuttle.SA1_SystemBusyReject;

string shuttleAlarmCode = shuttleAlarm.Code;      // 报警代码：SA1
string shuttleAlarmDesc = shuttleAlarm.ChsContent; // 中文描述

// 其他常用Shuttle报警
var shuttleSystemAlarm = controlCenter.AlarmCode.Shuttle.SA2_SystemAlarmReject;
var shuttleTimeoutAlarm = controlCenter.AlarmCode.Shuttle.SA6_ShuttleMoveTimeout;
var shuttleSpeedAlarm = controlCenter.AlarmCode.Shuttle.SA7_ShuttleMoveTooFast;
```

### 4. 查看配置参数

```csharp
// 获取机器人T轴位置配置
var tAxisConfig = controlCenter.SubsystemConfigure.PositionValue.RP1_TAxisPosition;

int tAxisValue = tAxisConfig.Value;        // 配置值（步数）
string tAxisDesc = tAxisConfig.Content;    // 配置描述
string tAxisUnit = tAxisConfig.Unit;       // 单位：step
string tAxisCode = tAxisConfig.Code;       // 配置代码：RP1

// 获取机器人R轴位置配置
var rAxisConfig = controlCenter.SubsystemConfigure.PositionValue.RP2_RAxisPosition;
int rAxisValue = rAxisConfig.Value;

// 获取Z轴安全旋转高度
var zAxisSafeHeight = controlCenter.SubsystemConfigure.PositionValue.RP27_ZForRobotRotation;
int safeHeight = zAxisSafeHeight.Value;
```

### 5. 查看子系统状态

#### 机器人状态
```csharp
// 获取机器人主状态
var robotMainStatus = controlCenter.SubsystemStatus.Robot.RS1_RobotStatus;
EnuRobotStatus robotStatus = robotMainStatus.Value;  // 当前机器人状态
string robotStatusDesc = robotMainStatus.Content;    // 状态描述

// 获取T轴目的地状态
var tAxisDestination = controlCenter.SubsystemStatus.Robot.RS2_TAxisSmoothDestination;
EnuTAxisDestination destination = tAxisDestination.Value;

// 直接获取完整的机器人状态对象
RobotSubsystemStatus fullRobotStatus = controlCenter.SubsystemStatus.Robot.Status;
```

#### 工作间状态
```csharp
// 获取工作间A的Slit Door状态
var slitDoorStatus = controlCenter.SubsystemStatus.ChamberA.SlitDoorStatus;
EnuSlitDoorStatus doorStatus = slitDoorStatus.Value;
string doorStatusDesc = slitDoorStatus.Content;

// 获取Lift Pin状态
var liftPinStatus = controlCenter.SubsystemStatus.ChamberA.LiftPinStatus;
EnuLiftPinStatus pinStatus = liftPinStatus.Value;

// 获取完整的工作间状态
ChamberSubsystemStatus fullChamberStatus = controlCenter.SubsystemStatus.ChamberA.Status;
```

#### 运输系统状态
```csharp
// 获取完整的Shuttle状态
ShuttleSubsystemStatus shuttleStatus = controlCenter.SubsystemStatus.Shuttle.Status;
```

## 💡 实际应用场景示例

### 场景1：检查是否可以进行晶圆传输

```csharp
public bool CanTransferWafer()
{
    var control = SS200InterLockMain.Instance;
    
    // 1. 检查机器人是否空闲
    var robotStatus = control.SubsystemStatus.Robot.RS1_RobotStatus.Value;
    if (robotStatus != EnuRobotStatus.Idle)
    {
        Console.WriteLine($"机器人忙碌，当前状态：{robotStatus}");
        return false;
    }
    
    // 2. 检查机器人手臂是否为空
    bool leftPaddle = control.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
    bool rightPaddle = control.IOInterface.Robot.RDI2_PaddleSensor2Right.Value;
    
    if (leftPaddle || rightPaddle)
    {
        Console.WriteLine("机器人手臂上已有晶圆");
        return false;
    }
    
    // 3. 检查目标工作间是否准备好
    bool doorOpen = control.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
    if (!doorOpen)
    {
        Console.WriteLine("工作间A的门未打开");
        return false;
    }
    
    Console.WriteLine("✅ 可以进行晶圆传输");
    return true;
}
```

### 场景2：系统健康检查

```csharp
public void CheckSystemHealth()
{
    var control = SS200InterLockMain.Instance;
    
    Console.WriteLine("=== 系统健康检查 ===");
    
    // 检查机器人状态
    var robotStatus = control.SubsystemStatus.Robot.RS1_RobotStatus.Value;
    Console.WriteLine($"机器人状态: {robotStatus}");
    
    // 检查关键传感器
    bool paddle1 = control.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
    bool paddle2 = control.IOInterface.Robot.RDI2_PaddleSensor2Right.Value;
    Console.WriteLine($"左侧Paddle传感器: {(paddle1 ? "有晶圆" : "无晶圆")}");
    Console.WriteLine($"右侧Paddle传感器: {(paddle2 ? "有晶圆" : "无晶圆")}");
    
    // 检查工作间门状态
    bool doorOpen = control.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
    bool doorClosed = control.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor.Value;
    Console.WriteLine($"工作间A门状态: {(doorOpen ? "打开" : doorClosed ? "关闭" : "中间位置")}");
    
    // 检查晶圆盒状态
    bool cassette1 = control.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Value;
    bool cassette2 = control.IOInterface.Shuttle.SDI7_PresentSensorCassette2.Value;
    Console.WriteLine($"晶圆盒1: {(cassette1 ? "存在" : "不存在")}");
    Console.WriteLine($"晶圆盒2: {(cassette2 ? "存在" : "不存在")}");
}
```

### 场景3：获取配置信息

```csharp
public void ShowRobotConfiguration()
{
    var control = SS200InterLockMain.Instance;
    
    Console.WriteLine("=== 机器人位置配置 ===");
    
    // T轴位置
    var tAxisPos = control.SubsystemConfigure.PositionValue.RP1_TAxisPosition;
    Console.WriteLine($"{tAxisPos.Content}: {tAxisPos.Value} {tAxisPos.Unit}");
    
    // R轴位置
    var rAxisPos = control.SubsystemConfigure.PositionValue.RP2_RAxisPosition;
    Console.WriteLine($"{rAxisPos.Content}: {rAxisPos.Value} {rAxisPos.Unit}");
    
    // Z轴安全高度
    var zAxisSafe = control.SubsystemConfigure.PositionValue.RP27_ZForRobotRotation;
    Console.WriteLine($"{zAxisSafe.Content}: {zAxisSafe.Value} {zAxisSafe.Unit}");
}
```

## ⚠️ 重要注意事项

### 1. 单例模式使用
```csharp
// ✅ 正确使用方式
var control = SS200InterLockMain.Instance;

// ❌ 错误！不要尝试创建新实例
// var control = new SS200InterLockMain(); // 这会报错
```

### 2. 线程安全
```csharp
// ✅ 读取操作是线程安全的
bool sensorValue = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;

// ⚠️ 如果在UI线程中使用，确保正确更新UI
Application.Current.Dispatcher.Invoke(() => {
    // 更新UI控件
    SensorStatusLabel.Content = sensorValue ? "有晶圆" : "无晶圆";
});
```

### 3. 性能考虑
```csharp
// ✅ 推荐：缓存频繁使用的访问器
var robotIO = SS200InterLockMain.Instance.IOInterface.Robot;
bool sensor1 = robotIO.RDI1_PaddleSensor1Left.Value;
bool sensor2 = robotIO.RDI2_PaddleSensor2Right.Value;

// ❌ 避免：重复长链式访问
bool sensor1 = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
bool sensor2 = SS200InterLockMain.Instance.IOInterface.Robot.RDI2_PaddleSensor2Right.Value;
```

## 🎓 总结

SS200InterLockMain就像是一个**万能遥控器**，通过它你可以：

1. **📊 监控** - 实时查看所有设备和传感器状态
2. **🚨 报警** - 获取详细的报警信息和原因
3. **⚙️ 配置** - 读取系统配置参数
4. **🔍 诊断** - 进行系统健康检查和故障诊断

使用时记住：
- 总是通过 `SS200InterLockMain.Instance` 获取实例
- 善用缓存，避免重复访问
- 注意线程安全，特别是UI更新
- 充分利用中文描述信息，便于理解

---

*这个指南用简单易懂的方式介绍了SS200InterLockMain的使用方法，即使是非技术人员也能快速上手！*
