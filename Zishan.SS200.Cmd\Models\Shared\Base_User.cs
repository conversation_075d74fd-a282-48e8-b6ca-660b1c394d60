﻿using SqlSugar;
using System;
using System.Collections.Generic;

namespace Zishan.SS200.Cmd.Models.Shared
{
    /// <summary>
    /// 用户信息表
    /// </summary>
    [SugarTable("Base_User", TableDescription = "用户信息表")]
    public class Base_User
    {
        /// <summary>
        /// ID号
        /// </summary>
        [SugarColumn(IsIdentity = true, IsPrimaryKey = true, ColumnDescription = "ID号")]
        public int Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [SugarColumn(ColumnDescription = "用户名")]
        public string UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        [SugarColumn(ColumnDescription = "密码")]
        public string Password { get; set; }

        /// <summary>
        /// 真实姓名
        /// </summary>
        [SugarColumn(ColumnDescription = "真实姓名")]
        public string RealName { get; set; }

        /// <summary>
        /// 性别(0-女，1-男)
        /// </summary>
        [SugarColumn(ColumnDescription = "性别(0-女，1-男)")]
        public int Sex { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        [SugarColumn(ColumnDescription = "生日")]
        public DateTime? Birthday { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        [SugarColumn(ColumnDescription = "部门ID")]
        public string DepartmentId { get; set; }

        /// <summary>
        /// 是否软删除
        /// </summary>
        [SugarColumn(ColumnDescription = "是否软删除")]
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间")]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnDescription = "修改时间")]
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(ColumnDescription = "创建人ID")]
        public string CreatorId { get; set; }

        /// <summary>
        /// 创建人姓名
        /// </summary>
        [SugarColumn(ColumnDescription = "创建人姓名")]
        public string CreatorName { get; set; }

        /// <summary>
        /// 修改人ID
        /// </summary>
        [SugarColumn(ColumnDescription = "修改人ID")]
        public string ModifyId { get; set; }

        /// <summary>
        /// 修改人姓名
        /// </summary>
        [SugarColumn(ColumnDescription = "修改人姓名")]
        public string ModifyName { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        [SugarColumn(ColumnDescription = "租户ID")]
        public string TenantId { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        [SugarColumn(ColumnDescription = "头像")]
        public string Avatar { get; set; }

        /// <summary>
        /// 电话号码
        /// </summary>
        [SugarColumn(ColumnDescription = "电话号码")]
        public string PhoneNumber { get; set; }
    }
}