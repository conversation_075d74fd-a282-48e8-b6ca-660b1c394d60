using System;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200;
using <PERSON>ishan.SS200.Cmd.Enums.SS200.AlarmCode.ChamberA;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// Chamber报警访问器简单测试类
    /// 用于验证Chamber报警代码访问器的基本功能
    /// </summary>
    public class ChamberAlarmAccessorSimpleTest
    {
        /// <summary>
        /// 简单测试Chamber报警代码访问器
        /// </summary>
        public static void SimpleTest()
        {
            try
            {
                Console.WriteLine("=== Chamber报警代码访问器简单测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var chamberAlarm = interLock.AlarmCode.ChamberA;

                // 测试前10个报警代码
                Console.WriteLine("\n测试前10个Chamber报警代码:");
                TestAccessor("PAC1", chamberAlarm.PAC1_SystemAbnormalReject);
                TestAccessor("PAC2", chamberAlarm.PAC2_SystemBusyReject);
                TestAccessor("PAC3", chamberAlarm.PAC3_RunProcessingReject);
                TestAccessor("PAC4", chamberAlarm.PAC4_DeltaPressureOutOfSetpoint);
                TestAccessor("PAC5", chamberAlarm.PAC5_SlitDoorSensorAbnormal);
                TestAccessor("PAC6", chamberAlarm.PAC6_SlitDoorSensorFailure);
                TestAccessor("PAC7", chamberAlarm.PAC7_PressureNotVacuumSlitDoorError);
                TestAccessor("PAC8", chamberAlarm.PAC8_SlitDoorOpenTimeout);
                TestAccessor("PAC9", chamberAlarm.PAC9_SlitDoorOpenTooFast);
                TestAccessor("PAC10", chamberAlarm.PAC10_LiftPinSensorAbnormal);

                // 测试中间的一些报警代码
                Console.WriteLine("\n测试中间的一些Chamber报警代码:");
                TestAccessor("PAC20", chamberAlarm.PAC20_ThrottleValveTimeout);
                TestAccessor("PAC30", chamberAlarm.PAC30_Gas4ServoTimeout);
                TestAccessor("PAC40", chamberAlarm.PAC40_CMValveNotOpenPressureError);
                TestAccessor("PAC50", chamberAlarm.PAC50_ISONotOpenRFError);

                // 测试最后几个报警代码
                Console.WriteLine("\n测试最后几个Chamber报警代码:");
                TestAccessor("PAC61", chamberAlarm.PAC61_RF2ForwardPowerUnstable);
                TestAccessor("PAC62", chamberAlarm.PAC62_RFOffFailure);
                TestAccessor("PAC63", chamberAlarm.PAC63_RF1ReflectorPowerUnstable);
                TestAccessor("PAC64", chamberAlarm.PAC64_RF2ReflectorPowerUnstable);
                TestAccessor("PAC65", chamberAlarm.PAC65_ChamberPumpDownTimeout);

                Console.WriteLine("\n=== Chamber报警代码访问器简单测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试单个访问器
        /// </summary>
        /// <param name="code">报警代码</param>
        /// <param name="accessor">访问器</param>
        private static void TestAccessor(string code, AlarmPropertyAccessor accessor)
        {
            try
            {
                if (accessor != null)
                {
                    string content = accessor.Content ?? "未定义";
                    string chsContent = accessor.ChsContent ?? "未定义";
                    Console.WriteLine($"  {code}: {content}");
                    Console.WriteLine($"       中文: {chsContent}");
                }
                else
                {
                    Console.WriteLine($"  {code}: 访问器为空");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  {code}: 访问器测试失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 统计测试 - 验证所有65个访问器都已定义
        /// </summary>
        public static void CountTest()
        {
            try
            {
                Console.WriteLine("=== Chamber报警代码访问器统计测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var chamberAlarm = interLock.AlarmCode.ChamberA;
                var chamberAlarmType = chamberAlarm.GetType();
                
                // 获取所有以PAC开头的属性
                var properties = chamberAlarmType.GetProperties();
                int pacPropertyCount = 0;
                
                foreach (var property in properties)
                {
                    if (property.Name.StartsWith("PAC") && property.Name.Contains("_"))
                    {
                        pacPropertyCount++;
                        Console.WriteLine($"  发现访问器: {property.Name}");
                    }
                }
                
                Console.WriteLine($"\n总计发现 {pacPropertyCount} 个Chamber报警代码访问器");
                
                if (pacPropertyCount == 65)
                {
                    Console.WriteLine("✅ 所有65个Chamber报警代码访问器都已正确定义！");
                }
                else
                {
                    Console.WriteLine($"❌ 预期65个访问器，实际发现{pacPropertyCount}个");
                }
                
                Console.WriteLine("=== Chamber报警代码访问器统计测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"统计测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 按功能分类测试Chamber报警代码访问器
        /// </summary>
        public static void CategoryTest()
        {
            try
            {
                Console.WriteLine("=== Chamber报警代码访问器分类测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var chamberAlarm = interLock.AlarmCode.ChamberA;

                // 系统状态报警
                Console.WriteLine("\n系统状态报警 (PAC1-PAC3):");
                TestAccessor("PAC1", chamberAlarm.PAC1_SystemAbnormalReject);
                TestAccessor("PAC2", chamberAlarm.PAC2_SystemBusyReject);
                TestAccessor("PAC3", chamberAlarm.PAC3_RunProcessingReject);

                // 狭缝门控制报警
                Console.WriteLine("\n狭缝门控制报警:");
                TestAccessor("PAC5", chamberAlarm.PAC5_SlitDoorSensorAbnormal);
                TestAccessor("PAC8", chamberAlarm.PAC8_SlitDoorOpenTimeout);
                TestAccessor("PAC48", chamberAlarm.PAC48_SlitDoorNotCloseRFError);

                // 升降针控制报警
                Console.WriteLine("\n升降针控制报警:");
                TestAccessor("PAC10", chamberAlarm.PAC10_LiftPinSensorAbnormal);
                TestAccessor("PAC12", chamberAlarm.PAC12_LiftPinOpenTimeout);
                TestAccessor("PAC15", chamberAlarm.PAC15_RobotRAxisLiftPinError);

                // 气体控制报警
                Console.WriteLine("\n气体控制报警:");
                TestAccessor("PAC27", chamberAlarm.PAC27_Gas1ServoTimeout);
                TestAccessor("PAC31", chamberAlarm.PAC31_Gas1FlowFault);
                TestAccessor("PAC35", chamberAlarm.PAC35_Gas1FlowUnstable);

                // 压力控制报警
                Console.WriteLine("\n压力控制报警:");
                TestAccessor("PAC4", chamberAlarm.PAC4_DeltaPressureOutOfSetpoint);
                TestAccessor("PAC42", chamberAlarm.PAC42_PressureServoTimeout);
                TestAccessor("PAC44", chamberAlarm.PAC44_PressureUnstable);

                // RF功率控制报警
                Console.WriteLine("\nRF功率控制报警:");
                TestAccessor("PAC52", chamberAlarm.PAC52_NoPlasmaLeftHead);
                TestAccessor("PAC54", chamberAlarm.PAC54_RF1PowerForwardTimeout);
                TestAccessor("PAC60", chamberAlarm.PAC60_RF1ForwardPowerUnstable);

                // 温度控制报警
                Console.WriteLine("\n温度控制报警:");
                TestAccessor("PAC45", chamberAlarm.PAC45_TemperatureServoTimeout);
                TestAccessor("PAC46", chamberAlarm.PAC46_TemperatureOutOfSetpoint);
                TestAccessor("PAC47", chamberAlarm.PAC47_TemperatureUnstable);

                // 泵浦控制报警
                Console.WriteLine("\n泵浦控制报警:");
                TestAccessor("PAC65", chamberAlarm.PAC65_ChamberPumpDownTimeout);

                Console.WriteLine("\n=== Chamber报警代码访问器分类测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分类测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            SimpleTest();
            Console.WriteLine();
            CountTest();
            Console.WriteLine();
            CategoryTest();
        }
    }
}
