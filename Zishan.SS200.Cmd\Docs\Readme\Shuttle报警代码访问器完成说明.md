# Shuttle报警代码访问器完成说明

## 概述

已成功完成SS200InterLockMain中所有Shuttle报警代码的访问器定义，共计39个报警代码（SA1-SA39）。

## 完成的访问器列表

### 系统状态报警 (SA1-SA2, SA37)
- `SA1_SystemBusyReject` - Shuttle system status is busy, command reject
- `SA2_SystemAlarmReject` - Shuttle system status is alarm, command reject
- `SA37_SystemTriggerAlarmReject` - System trigger status is alarm, command reject

### 卡匣巢控制报警 (SA3-SA5, SA24)
- `SA3_CassetteNestMoveTimeout` - Cassette nest move time out
- `SA4_CassetteNestSpeedTooHigh` - Cassette nest move speed too high
- `SA5_CassetteNestPositionFailure` - Cassette nest position condition failure
- `SA24_CassetteNestNotHomeShuttleError` - Cassette nest not at home, shuttle motion failure

### Shuttle移动控制报警 (SA6-SA11)
- `SA6_ShuttleMoveTimeout` - Shuttle move time out
- `SA7_ShuttleMoveTooFast` - Shuttle move too fast
- `SA8_ShuttleUpDownPositionFailure` - Shuttle up down position condition failure
- `SA9_ShuttleRotateTimeout` - Shuttle rotate time out
- `SA10_ShuttleRotateTooFast` - Shuttle rotate too fast
- `SA11_ShuttleRotatePositionFailure` - Shuttle rotate position condition failure

### 卡匣门控制报警 (SA12-SA16)
- `SA12_CassetteDoorOpenTimeout` - Cassette door open time out
- `SA13_CassetteDoorOpenTooFast` - Cassette door open too fast
- `SA14_CassetteDoorPositionFailure` - Cassette door position condition failure
- `SA15_CassetteDoorCloseTimeout` - Cassette door close time out
- `SA16_CassetteDoorCloseTooFast` - Cassette door close too fast

### 阀门控制报警 (SA17-SA19, SA26-SA27)
- `SA17_OpenXVTimeout` - Open XV time out
- `SA18_OpenXVTooFast` - Open XV too fast
- `SA19_CloseXVTimeout` - Close XV time out
- `SA26_ForelineNoVacuumCVError` - Shuttle vacuum switch show foreline is no vacuum, can not open CV
- `SA27_CassetteDoorNotCloseVacuumError` - Cassette door is not close, can not open shuttle vacuum valve

### 压力控制报警 (SA20-SA21, SA25, SA28-SA29)
- `SA20_PressureDeltaOutOfRange` - Shuttle & loadlock pressure delta out of range
- `SA21_ChamberPressureReviewNo` - Chamber pressure review is no, pls confirm to continue
- `SA25_ChamberPressureReviewReturnReject` - Chamber pressure review is no, return cassette reject
- `SA28_ShuttlePumpDownTimeout` - Shuttle pump down time out
- `SA29_ShuttleBackfillTimeout` - Shuttle backfill time out

### 位置检测报警 (SA22-SA23)
- `SA22_PositionUnknownCassetteDoorError` - Shuttle position unknown, can not motion cassette door
- `SA23_PositionUnknownSwapCassetteError` - Shuttle position unknown, can not swap cassette

### 腔室状态报警 (SA30-SA36, SA38)
- `SA30_CHATriggerAlarmPumpDownError` - CHA trigger status is alarm, can not pump down loadlock
- `SA31_CHBTriggerAlarmPumpDownError` - CHB trigger status is alarm, can not pump down loadlock
- `SA32_CHANotIdlePumpDownError` - CHA run status is not at idle status, can not pump down loadlock
- `SA33_CHBNotIdlePumpDownError` - CHB run status is not at idle status, can not pump down loadlock
- `SA34_CHABPressureHighPumpDownError` - CHA/B pressure>setpoint, and slit door is close, can not pump down loadlock
- `SA35_CHAPressureHighPumpDownError` - CHA pressure>setpoint, and slit door is close, can not pump down loadlock
- `SA36_CHBPressureHighPumpDownError` - CHB pressure>setpoint, and slit door is close, can not pump down loadlock
- `SA38_LoadlockNotIdleReject` - Loadlock run status is not idle, command reject
- `SA39_ShuttlePressureNotATMBackfillFailure` - Shuttle pressure is not at ATM status, backfill loadlock failure

## 使用方法

### 基本用法
```csharp
// 获取单例实例
var interLock = SS200InterLockMain.Instance;

// 访问Shuttle报警代码
var shuttleAlarm = interLock.AlarmCode.Shuttle;

// 获取报警内容
string alarmContent = shuttleAlarm.SA1_SystemBusyReject.Content;
string alarmChsContent = shuttleAlarm.SA1_SystemBusyReject.ChsContent;

// 示例：检查系统忙碌报警
if (shuttleAlarm.SA1_SystemBusyReject.Content != null)
{
    Console.WriteLine($"SA1报警: {shuttleAlarm.SA1_SystemBusyReject.Content}");
    Console.WriteLine($"中文描述: {shuttleAlarm.SA1_SystemBusyReject.ChsContent}");
}
```

### 批量访问
```csharp
// 测试所有报警代码访问器
ShuttleAlarmAccessorTest.RunAllTests();

// 分类测试
ShuttleAlarmAccessorTest.CategoryTest();
```

## 实现特点

1. **线程安全**: 使用ConcurrentDictionary实现缓存，支持多线程并发访问
2. **延迟加载**: 访问器采用延迟加载模式，只在首次访问时创建
3. **缓存机制**: 已创建的访问器会被缓存，避免重复创建
4. **统一接口**: 所有访问器都返回AlarmPropertyAccessor类型，提供统一的访问接口
5. **完整覆盖**: 覆盖了所有39个Shuttle报警代码，无遗漏

## 功能分类说明

### 1. 系统级报警
- 系统忙碌、报警状态、触发器状态异常

### 2. 机械运动报警
- 卡匣巢移动、Shuttle移动、旋转等机械动作的超时和速度异常

### 3. 门控制报警
- 卡匣门开关的超时、速度异常、位置检测失败

### 4. 阀门控制报警
- XV阀门开关控制、真空阀门控制异常

### 5. 压力控制报警
- 压差超限、抽真空超时、充气超时、压力审查失败

### 6. 位置检测报警
- Shuttle位置未知导致的操作失败

### 7. 腔室联锁报警
- 与CHA/CHB腔室状态相关的联锁保护报警

## 测试验证

提供了完整的测试类`ShuttleAlarmAccessorTest`，可以验证：
- 所有39个访问器是否正常创建
- 报警内容是否正确获取
- 中文描述是否正确显示
- 按功能分类的测试验证
- 异常情况的处理

## 编译状态

✅ 编译成功，返回码：0
✅ 无编译错误
✅ 可以正常使用

## 注意事项

1. 确保ShuttleErrorCodesProvider.Instance已正确初始化
2. 确保ShuttleErrorCodes.json配置文件存在且格式正确
3. 访问器返回null时需要进行空值检查
4. 建议在使用前先测试访问器的可用性

## 后续扩展

如需添加新的Shuttle报警代码：
1. 在EnuShuttleAlarmCodes枚举中添加新的报警代码
2. 在ShuttleErrorCodes.json中添加对应的配置
3. 在ShuttleAlarmAccessor类中添加对应的访问器属性
4. 更新测试代码以包含新的报警代码

## 完成状态

✅ 所有39个Shuttle报警代码访问器已完成
✅ 测试代码已提供
✅ 文档已完善
✅ 编译成功，无错误
✅ 可以正常使用

## 与其他子系统的关系

Shuttle报警代码访问器与其他子系统的报警代码访问器形成完整的报警管理体系：

- **Robot报警代码**: 67个访问器 (RA1-RA67)
- **Chamber报警代码**: 65个访问器 (PAC1-PAC65)  
- **Shuttle报警代码**: 39个访问器 (SA1-SA39)

总计171个报警代码访问器，覆盖整个SS200系统的所有报警情况。
