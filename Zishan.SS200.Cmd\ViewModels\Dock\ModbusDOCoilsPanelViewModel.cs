﻿using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using Zishan.SS200.Cmd.Models;
using System.Threading.Tasks;
using System;
using System.Linq;
using System.Collections.Generic;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Config;
using log4net;
using System.IO;
using System.Threading;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using System.Text;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.ViewModels.Dock
{
    /// <summary>
    /// 主窗口视图模型
    /// </summary>
    public partial class ModbusDOCoilsPanelViewModel : UiViewModel
    {
        private readonly IS200McuCmdService _mcuCmdService;

        /// <summary>
        /// 状态栏信息
        /// </summary>
        [ObservableProperty]
        private string title = nameof(ModbusDOCoilsPanelViewModel);

        #region 构造函数

        public ModbusDOCoilsPanelViewModel() : this(new S200McuCmdService())
        {
        }

        /// <summary>
        /// 构造函数（带依赖注入）
        /// </summary>
        public ModbusDOCoilsPanelViewModel(IS200McuCmdService mcuCmdService) : base(mcuCmdService)
        {
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
        }

        #endregion 构造函数
    }
}