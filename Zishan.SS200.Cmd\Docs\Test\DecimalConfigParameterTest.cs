using System;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// 小数配置参数测试类
    /// 验证Json配置参数中的小数点值能够正确获取
    /// </summary>
    public static class DecimalConfigParameterTest
    {
        /// <summary>
        /// 测试小数配置参数的获取
        /// </summary>
        public static bool TestDecimalConfigParameters()
        {
            try
            {
                Console.WriteLine("=== 测试小数配置参数获取 ===");
                
                var ss200Main = SS200InterLockMain.Instance;
                var configure = ss200Main.SubsystemConfigure;
                
                // 测试ChamberA的小数参数
                Console.WriteLine("\n--- ChamberA小数参数测试 ---");
                TestChamberADecimalParameters(configure);
                
                // 测试ChamberB的小数参数
                Console.WriteLine("\n--- ChamberB小数参数测试 ---");
                TestChamberBDecimalParameters(configure);
                
                Console.WriteLine("\n=== 小数配置参数测试完成 ===");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试ChamberA的小数参数
        /// </summary>
        private static void TestChamberADecimalParameters(SubsystemConfigureAccessor configure)
        {
            // PPS1 - 狭缝门运动最小时间 (应该是0.3)
            var pps1 = configure.ChamberA.PPS1_SlitDoorMotionMinTime;
            Console.WriteLine($"PPS1 狭缝门运动最小时间:");
            Console.WriteLine($"  Value (int): {pps1?.Value ?? 0}");
            Console.WriteLine($"  DoubleValue: {pps1?.DoubleValue ?? 0}");
            Console.WriteLine($"  RawValue: {pps1?.RawValue ?? "null"}");
            Console.WriteLine($"  期望值: 0.3");
            
            // PPS3 - 升降针运动最小时间 (应该是0.3)
            var pps3 = configure.ChamberA.PPS3_LiftPinMotionMinTime;
            Console.WriteLine($"\nPPS3 升降针运动最小时间:");
            Console.WriteLine($"  Value (int): {pps3?.Value ?? 0}");
            Console.WriteLine($"  DoubleValue: {pps3?.DoubleValue ?? 0}");
            Console.WriteLine($"  RawValue: {pps3?.RawValue ?? "null"}");
            Console.WriteLine($"  期望值: 0.3");
            
            // PPS46 - 工艺腔室抽真空压力偏差 (应该是0.5)
            var pps46 = configure.ChamberA.PPS46_ProcessChamberPumpingDownPressureDeviation;
            Console.WriteLine($"\nPPS46 工艺腔室抽真空压力偏差:");
            Console.WriteLine($"  Value (int): {pps46?.Value ?? 0}");
            Console.WriteLine($"  DoubleValue: {pps46?.DoubleValue ?? 0}");
            Console.WriteLine($"  RawValue: {pps46?.RawValue ?? "null"}");
            Console.WriteLine($"  期望值: 0.5");

            // PPS17 - RF频率最小值 (应该是13.56)
            var pps17 = configure.ChamberA.PPS17_RfFrequencyMin;
            Console.WriteLine($"\nPPS17 RF频率最小值:");
            Console.WriteLine($"  Value (int): {pps17?.Value ?? 0}");
            Console.WriteLine($"  DoubleValue: {pps17?.DoubleValue ?? 0}");
            Console.WriteLine($"  RawValue: {pps17?.RawValue ?? "null"}");
            Console.WriteLine($"  期望值: 13.56");
        }
        
        /// <summary>
        /// 测试ChamberB的小数参数
        /// </summary>
        private static void TestChamberBDecimalParameters(SubsystemConfigureAccessor configure)
        {
            // PPS1 - 狭缝门运动最小时间 (应该是0.3)
            var pps1 = configure.ChamberB.PPS1_SlitDoorMotionMinTime;
            Console.WriteLine($"PPS1 狭缝门运动最小时间:");
            Console.WriteLine($"  Value (int): {pps1?.Value ?? 0}");
            Console.WriteLine($"  DoubleValue: {pps1?.DoubleValue ?? 0}");
            Console.WriteLine($"  RawValue: {pps1?.RawValue ?? "null"}");
            Console.WriteLine($"  期望值: 0.3");
            
            // PPS3 - 升降针运动最小时间 (应该是0.3)
            var pps3 = configure.ChamberB.PPS3_LiftPinMotionMinTime;
            Console.WriteLine($"\nPPS3 升降针运动最小时间:");
            Console.WriteLine($"  Value (int): {pps3?.Value ?? 0}");
            Console.WriteLine($"  DoubleValue: {pps3?.DoubleValue ?? 0}");
            Console.WriteLine($"  RawValue: {pps3?.RawValue ?? "null"}");
            Console.WriteLine($"  期望值: 0.3");
            
            // PPS46 - 工艺腔室抽真空压力偏差 (应该是0.5)
            var pps46 = configure.ChamberB.PPS46_ProcessChamberPumpingDownPressureDeviation;
            Console.WriteLine($"\nPPS46 工艺腔室抽真空压力偏差:");
            Console.WriteLine($"  Value (int): {pps46?.Value ?? 0}");
            Console.WriteLine($"  DoubleValue: {pps46?.DoubleValue ?? 0}");
            Console.WriteLine($"  RawValue: {pps46?.RawValue ?? "null"}");
            Console.WriteLine($"  期望值: 0.5");
        }
        
        /// <summary>
        /// 测试BasicCommandTestViewModel中的用法
        /// </summary>
        public static void TestBasicCommandTestViewModelUsage()
        {
            Console.WriteLine("\n=== 测试BasicCommandTestViewModel用法 ===");
            
            var interLock = SS200InterLockMain.Instance;
            var configure = interLock.SubsystemConfigure;
            
            // 原始用法（会得到0，因为Value返回int）
            var config_ChamberA_old = configure.ChamberA.PPS1_SlitDoorMotionMinTime?.Value ?? 0;
            var config_ChamberB_old = configure.ChamberB.PPS1_SlitDoorMotionMinTime?.Value ?? 0;
            
            Console.WriteLine($"原始用法 (Value属性):");
            Console.WriteLine($"  ChamberA PPS1: {config_ChamberA_old}");
            Console.WriteLine($"  ChamberB PPS1: {config_ChamberB_old}");
            
            // 修复后的用法（使用DoubleValue获取正确的小数值）
            var config_ChamberA_new = configure.ChamberA.PPS1_SlitDoorMotionMinTime?.DoubleValue ?? 0;
            var config_ChamberB_new = configure.ChamberB.PPS1_SlitDoorMotionMinTime?.DoubleValue ?? 0;
            
            Console.WriteLine($"\n修复后用法 (DoubleValue属性):");
            Console.WriteLine($"  ChamberA PPS1: {config_ChamberA_new}");
            Console.WriteLine($"  ChamberB PPS1: {config_ChamberB_new}");
            
            // 使用RawValue的用法
            var config_ChamberA_raw = configure.ChamberA.PPS1_SlitDoorMotionMinTime?.RawValue ?? 0;
            var config_ChamberB_raw = configure.ChamberB.PPS1_SlitDoorMotionMinTime?.RawValue ?? 0;
            
            Console.WriteLine($"\n使用RawValue属性:");
            Console.WriteLine($"  ChamberA PPS1: {config_ChamberA_raw}");
            Console.WriteLine($"  ChamberB PPS1: {config_ChamberB_raw}");
        }
    }
}
