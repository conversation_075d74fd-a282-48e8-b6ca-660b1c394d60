using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.ViewModels.Dock
{
    /// <summary>
    /// LogView的视图模型，符合Prism的命名约定
    /// </summary>
    public partial class LogViewModel : ObservableObject
    {
        //private readonly IS200McuCmdService _mcuCmdService;
        private readonly ILog _logger = LogManager.GetLogger(typeof(LogViewModel));

        private readonly object _logListLock = new object(); // 集合同步锁对象

        [ObservableProperty]
        private string title = "日志信息";

        /// <summary>
        /// 是否启用UI日志显示
        /// </summary>
        [ObservableProperty]
        private bool isUILogEnabled = true;

        public LogViewModel()
        {
            // 初始化UI日志服务
            UILogService.Initialize(this);

            // 从配置中读取UI日志启用状态
            IsUILogEnabled = App.AppIniConfig?.ShowUILog ?? true;

            UILogService.AddInfoLog("LogViewModel已初始化");

            // 启用 ObservableCollection 的线程同步
            BindingOperations.EnableCollectionSynchronization(LogList, _logListLock);
        }

        #region 日志记录绑定展示

        [ObservableProperty]
        private ObservableCollection<LogInfo> logList = new ObservableCollection<LogInfo>();

        public void ScrollToBottom()
        {
            if (LogList.Count > 0)
            {
                // 使用同步锁保护集合访问
                lock (_logListLock)
                {
                    if (LogList.Count > 0) // 再次检查，因为锁期间可能被清空
                    {
                        var lastItem = LogList[^1];
                        // 异步调用以确保界面刷新完毕后再进行滚动
                        Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            // 此处代码已移除，因为没有实际功能
                        });
                    }
                }
            }
        }

        public int currentLogIndex = 1;

        public void AddLog(string log)
        {
            LogInfo logModel = new LogInfo
            {
                Index = currentLogIndex,
                Message = $"{log}"
            };

            // 使用 Dispatcher.Invoke 确保在 UI 线程上操作集合
            Application.Current.Dispatcher.Invoke(() =>
            {
                // 使用同步锁保护集合操作
                lock (_logListLock)
                {
                    LogList.Add(logModel);

                    // 日志不断的增加，超过1000条，就删除第一条
                    if (LogList.Count > 1000)
                    {
                        LogList.RemoveAt(0);
                    }
                }
            });

            _logger.Info(logModel.Message);
            currentLogIndex++;
        }

        #endregion 日志记录绑定展示

        #region 命令

        /// <summary>
        /// 复制消息到剪贴板
        /// </summary>
        [RelayCommand]
        private void CopyMessageToClipboard(string message)
        {
            if (!string.IsNullOrEmpty(message))
            {
                try
                {
                    Clipboard.SetText(message);
                    _logger.Info($"已复制消息到剪贴板: {message}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"复制到剪贴板失败: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 事件：双击清除日志
        /// </summary>
        [RelayCommand]
        private void ExecuteLogListDoubleClick(object parameter)
        {
            // 使用 Dispatcher.Invoke 确保在 UI 线程上操作集合
            Application.Current.Dispatcher.Invoke(() =>
            {
                // 使用同步锁保护集合操作
                lock (_logListLock)
                {
                    currentLogIndex = 1;
                    LogList.Clear();
                }
            });
        }

        /// <summary>
        /// UI日志启用状态变化命令
        /// </summary>
        [RelayCommand]
        private void ToggleUILogEnabled()
        {
            try
            {
                // 更新应用程序配置
                if (App.AppIniConfig != null)
                {
                    App.AppIniConfig.ShowUILog = IsUILogEnabled;

                    string status = IsUILogEnabled ? "启用" : "禁用";
                    _logger.Info($"UI日志显示已{status}");

                    // 如果启用了UI日志，添加一条提示信息
                    if (IsUILogEnabled)
                    {
                        UILogService.AddInfoLog($"UI日志显示已{status}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"切换UI日志启用状态失败: {ex.Message}", ex);
            }
        }

        #endregion 命令
    }
}