using System;
using System.Globalization;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 已执行次数显示转换器
    /// 根据执行状态和已执行次数显示相应文本
    /// 未开始时显示 "未开始"
    /// 开始执行后显示 "已执行: X次"
    /// </summary>
    public class ExecutedCountDisplayConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 2 && values[0] is int executedCount && values[1] is bool hasStartedExecution)
            {
                if (!hasStartedExecution)
                {
                    return "未开始";
                }

                return $"已执行: {executedCount}次";
            }

            return "未知";
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
