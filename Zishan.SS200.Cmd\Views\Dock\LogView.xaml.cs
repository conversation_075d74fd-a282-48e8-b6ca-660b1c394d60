﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace Zishan.SS200.Cmd.Views.Dock
{
    /// <summary>
    /// LogView.xaml 的交互逻辑
    /// </summary>
    public partial class LogView : UserControl
    {
        public LogView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        // private void InitializeComponent()
        // {
        //     Uri resourceLocator = new Uri("/Zishan.SS200.Cmd;component/Views/Dock/LogView.xaml", UriKind.Relative);
        //     System.Windows.Application.LoadComponent(this, resourceLocator);
        // }
    }
}