# 直接访问状态实体对象示例

## 概述

SS200InterLockMain 现在提供了直接访问完整状态实体对象的方式，无需通过 GetOrCreateStatusAccessor 逐个访问属性。

## 使用方式

### Robot状态访问

```csharp
// 获取完整的Robot状态实体对象
var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;

// 直接访问所有Robot状态属性
var robotMainStatus = robotStatus.EnuRobotStatus;
var tAxisSmoothDestination = robotStatus.EnuTAxisSmoothDestination;
var tAxisNoseDestination = robotStatus.EnuTAxisNoseDestination;
var tAxisIsZero = robotStatus.TAxisIsZeroPosition;

// R轴状态
var rAxisSmoothExtend = robotStatus.EnuTAndRAxisSmoothExtendDestination;
var rAxisNoseExtend = robotStatus.EnuTAndRAxisNoseExtendDestination;
var rAxisIsZero = robotStatus.RAxisIsZeroPosition;

// Z轴状态
var zAxisHeight = robotStatus.EnuTAndZAxisHeightStatus;
var zAxisIsZero = robotStatus.ZAxisIsZeroPosition;

// Pin Search状态
var pinSearchStatus = robotStatus.PinSearchStatus;
var pinSearchDataEffective = robotStatus.EnuPinSearchDataEffective;
var shuttle1PinSearchP1 = robotStatus.Shuttle1PinSearchSmoothP1;
var shuttle1PinSearchP2 = robotStatus.Shuttle1PinSearchSmoothP2;

// Paddle状态
var smoothP1Status = robotStatus.SmoothPaddleP1Status;
var smoothP2Status = robotStatus.SmoothPaddleP2Status;
var noseP3Status = robotStatus.NosePaddleP3Status;
var noseP4Status = robotStatus.NosePaddleP4Status;
```

### Chamber状态访问

```csharp
// 获取ChamberA状态
var chamberAStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberA.Status;
var slitDoorStatus = chamberAStatus.SlitDoorStatus;
var liftPinStatus = chamberAStatus.LiftPinStatus;

// 获取ChamberB状态
var chamberBStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberB.Status;
var chamberBSlitDoor = chamberBStatus.SlitDoorStatus;
var chamberBLiftPin = chamberBStatus.LiftPinStatus;
```

### Shuttle状态访问

```csharp
// 获取Shuttle状态
var shuttleStatus = SS200InterLockMain.Instance.SubsystemStatus.Shuttle.Status;

// 访问Shuttle的各种状态属性
// 注意：具体属性取决于ShuttleSubsystemStatus类的实际定义
// var shuttlePosition = shuttleStatus.ShuttlePosition;
// var cassetteStatus = shuttleStatus.CassetteStatus;
```

## 优势

### 1. 简化访问
- **之前**: 需要通过 `GetOrCreateStatusAccessor` 逐个访问
- **现在**: 直接访问完整的状态实体对象

### 2. 性能提升
- 减少了缓存查找和访问器创建的开销
- 直接访问属性，无需额外的包装层

### 3. 类型安全
- 直接使用强类型的状态实体对象
- 编译时类型检查，减少运行时错误

### 4. 智能感知支持
- IDE可以提供完整的属性列表和类型信息
- 更好的代码补全和重构支持

## 兼容性

- 原有的访问器方式仍然保留，确保向后兼容
- 新的直接访问方式是推荐的使用方法
- 可以根据需要逐步迁移到新的访问方式

## 调试支持

```csharp
// 检查状态是否已初始化
bool isRobotInitialized = SS200InterLockMain.Instance.SubsystemStatus.Robot.IsInitialized;

// 获取状态的字符串表示（用于调试和日志）
string robotStatusString = SS200InterLockMain.Instance.SubsystemStatus.Robot.StatusString;
string chamberStatusString = SS200InterLockMain.Instance.SubsystemStatus.ChamberA.StatusString;
string shuttleStatusString = SS200InterLockMain.Instance.SubsystemStatus.Shuttle.StatusString;
```

## 示例：在ViewModel中使用

```csharp
public class RobotStatusViewModel : ObservableObject
{
    private void UpdateRobotStatus()
    {
        // 直接获取完整的Robot状态
        var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
        
        if (robotStatus != null)
        {
            // 更新UI绑定的属性
            RobotMainStatus = robotStatus.EnuRobotStatus;
            TAxisDestination = robotStatus.EnuTAxisSmoothDestination;
            SmoothPaddleStatus = robotStatus.SmoothPaddleP1Status;
            
            // 可以直接使用所有状态属性，无需逐个访问器调用
        }
    }
}
```
