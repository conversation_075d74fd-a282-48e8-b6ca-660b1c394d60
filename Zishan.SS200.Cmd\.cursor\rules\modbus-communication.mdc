---
description: 
globs: 
alwaysApply: false
---
# Modbus通信规范

## 通信基础规则

### 协议实现
- 使用NModbus库作为Modbus协议的基础实现
- 支持Modbus RTU和Modbus TCP两种通信方式
- 所有通信操作应实现超时处理
- 通信错误应有明确的错误码和描述
- 实现通信重试机制，但有最大重试次数限制

### 设备地址
- 设备地址应通过配置文件设置，不硬编码
- 支持通过UI配置设备地址
- 设备地址应在通信前进行验证
- 保存设备地址映射关系，便于诊断和维护

### 数据类型
- 明确定义不同寄存器的数据类型（如Int16, UInt16, Float32等）
- 实现数据类型转换工具，处理字节序和位序
- 考虑不同设备的字节序差异（大端/小端）
- 使用枚举类型表示状态和命令码

## 通信服务设计

### 服务接口
- 定义清晰的通信服务接口（IModbusService）
- 接口方法应返回结构化的结果，包含成功标志和数据
- 所有通信方法应支持异步操作
- 提供事件机制通知通信状态变化

### 实现类
- 通信服务实现类应封装底层通信细节
- 实现连接池管理，避免频繁建立连接
- 提供通信统计信息（成功率、延迟等）
- 实现日志记录，记录所有通信操作

### 错误处理
- 定义明确的错误类型和错误码
- 实现通信异常处理机制
- 区分临时错误和永久错误
- 提供诊断工具辅助排查通信问题

## 命令规范

### 命令结构
- 命令应有明确的格式和参数定义
- 复杂命令应拆分为多个简单操作
- 命令执行应有状态反馈
- 实现命令队列，避免命令冲突

### 读写操作
- 读操作应指定起始地址和长度
- 写操作应验证数据范围和类型
- 批量读写操作应考虑设备支持的最大长度
- 实现原子操作，确保数据一致性

### 轮询机制
- 实现高效的轮询机制，避免过度轮询
- 根据数据重要性和变化频率调整轮询间隔
- 提供按需轮询和周期性轮询两种模式
- 轮询操作应在后台线程执行，不阻塞UI

## 安全性考虑

### 访问控制
- 实现基于角色的命令访问控制
- 关键操作应要求确认或更高权限
- 记录所有命令操作的执行者和时间
- 提供命令审计日志

### 数据验证
- 所有发送的数据应进行范围和类型验证
- 接收的数据应验证其有效性和一致性
- 实现CRC校验确保数据完整性
- 对异常数据进行标记和报告

## 性能优化

### 通信效率
- 合并相邻寄存器的读写操作
- 优化轮询策略，减少不必要的通信
- 实现数据缓存机制，避免重复读取
- 监控和记录通信性能指标

### 资源管理
- 正确管理通信资源，确保及时释放
- 实现连接池，重用通信连接
- 限制并发通信请求数量
- 在应用退出时正确关闭所有连接
