﻿<UserControl
    x:Class="Zishan.SS200.Cmd.UserControls.UContainer"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:beavior="clr-namespace:Zishan.SS200.Cmd.Behaviors"
    xmlns:chamber="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    xmlns:conv="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:gg="clr-namespace:Zishan.SS200.Cmd.Common"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:<PERSON>ishan.SS200.Cmd.UserControls"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DataContext="{d:DesignInstance Type=chamber:TransferWaferViewModel,
                                     IsDesignTimeCreatable=True}"
    d:DesignHeight="450"
    d:DesignWidth="500"
    mc:Ignorable="d">
    <UserControl.Resources>
        <conv:DragDropWaferDescriptionConverter x:Key="DragDropWaferDescriptionConverter" />
        <conv:AndMultiValueConverter2 x:Key="AndMultiValueConverter2" />
        <DataTemplate x:Key="DragAdorner">
            <Border
                Width="100"
                Height="120"
                Background="Silver"
                BorderBrush="Gray"
                BorderThickness="1"
                CornerRadius="2"
                SnapsToDevicePixels="True">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Image
                        Grid.Row="0"
                        Width="100"
                        HorizontalAlignment="Left"
                        Source="/Assets/Images/plate.png" />
                    <TextBlock
                        Grid.Row="1"
                        Width="100"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        FontSize="12"
                        FontWeight="Bold"
                        Foreground="Green"
                        Text="{Binding Converter={StaticResource DragDropWaferDescriptionConverter}}"
                        TextAlignment="Center" />
                </Grid>
            </Border>
        </DataTemplate>
    </UserControl.Resources>
    <Grid Name="mainGrid">
        <Border
            Grid.Column="0"
            Background="LightBlue"
            BorderBrush="Green"
            BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="5*" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="5*" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <WrapPanel
                    Grid.Column="0"
                    Grid.ColumnSpan="3"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center">

                    <!--<TextBlock FontSize="20">
                        <Run Text="{Binding Title}" />
                        <Run Text=" " />
                        <Run Text="{Binding ChamberName}" />
                    </TextBlock>-->
                    <!--  多值绑定  -->
                    <TextBlock FontSize="16">
                        <TextBlock.Text>
                            <MultiBinding FallbackValue="标题绑定失败提示" StringFormat="{}{0}【{1}】左边总容量[{2}],剩余[{3}]">
                                <Binding Path="Title" />
                                <Binding Path="ChamberName" />
                                <Binding Path="LeftWaferAction.Capacity" />
                                <Binding Path="LeftWaferAction.RemainWaferCount" />
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </WrapPanel>

                <!--  左边Chamber图片展示 - 替换Badge控件以避免HandyControl动画异常  -->
                <Grid
                    Grid.Row="1"
                    Grid.Column="0"
                    Visibility="{Binding LeftWaferAction.HaveWafer, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Image
                        Width="100"
                        HorizontalAlignment="Left"
                        Source="/Assets/Images/plate.png" />

                    <!--  Badge数字显示  -->
                    <Border
                        MinWidth="20"
                        MinHeight="20"
                        Margin="2,20,5,0"
                        Padding="4,2"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Background="Green"
                        CornerRadius="10">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontWeight="Bold"
                            Foreground="White"
                            Text="{Binding LeftWaferAction.CurWaferCount}" />
                    </Border>
                </Grid>

                <!--  左边Chamber Move、Replace判断  -->
                <StackPanel Grid.Row="1" Grid.Column="1">
                    <StackPanel HorizontalAlignment="Left" Orientation="Horizontal">
                        <CheckBox
                            HorizontalAlignment="Left"
                            Content="Move"
                            IsChecked="{Binding LeftWaferAction.IsMoveChecked}"
                            IsEnabled="{Binding LeftWaferAction.IsMove}" />
                        <CheckBox
                            Margin="10,5,0,0"
                            HorizontalAlignment="Left"
                            Content="Replace"
                            IsChecked="{Binding LeftWaferAction.IsReplaceChecked}"
                            IsEnabled="{Binding LeftWaferAction.IsReplace}" />
                    </StackPanel>
                    <!--  左边Chamber Create、Delete判断  -->
                    <StackPanel
                        Margin="0,5,0,0"
                        VerticalAlignment="Top"
                        Orientation="Horizontal">
                        <StackPanel HorizontalAlignment="Left" Orientation="Horizontal">
                            <CheckBox
                                Name="chkLeftWafeCreate"
                                HorizontalAlignment="Left"
                                Content="Create"
                                IsChecked="{Binding LeftWaferAction.IsCreateChecked}"
                                IsEnabled="{Binding LeftWaferAction.IsCreate}" />
                            <CheckBox
                                Name="chkLeftWafeDelete"
                                Margin="5,5,0,0"
                                HorizontalAlignment="Left"
                                Content="Delete"
                                IsChecked="{Binding LeftWaferAction.IsDeleteChecked}"
                                IsEnabled="{Binding LeftWaferAction.IsDelete}" />
                            <!--<StackPanel VerticalAlignment="Top" Orientation="Horizontal">
                                <StackPanel />
                                <TextBlock Text="Wafer可选列表:" />
                                <ListBox ItemsSource="{Binding WaferNO}" />
                            </StackPanel>-->
                        </StackPanel>

                        <StackPanel Orientation="Horizontal">
                            <ComboBox
                                x:Name="cbLeftAvailableWaferIds"
                                Margin="5,5,5,10"
                                d:ItemsSource="{d:SampleData ItemCount=5}"
                                hc:InfoElement.Title="SLOT："
                                hc:InfoElement.TitlePlacement="Left"
                                DisplayMemberPath="WaferNo"
                                ItemsSource="{x:Static gg:Golbal.CurLeftAvailableWafers}"
                                Style="{StaticResource ComboBoxExtend}" />

                            <Button
                                Name="btnLeftWaferRun"
                                Margin="0,5,0,0"
                                VerticalAlignment="Top"
                                Click="btnLeftWaferRun_Click"
                                Content="执行"
                                IsEnabled="{Binding LeftWaferAction.CanRunCreateOrDeleteCmd}"
                                Style="{StaticResource ButtonDefault}" />
                        </StackPanel>
                    </StackPanel>
                </StackPanel>

                <!--  左边Wafer列表绑定  -->
                <Grid Grid.Row="1" Grid.Column="2">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <TextBlock Margin="15,0,0,0" Text="左边Wafer列表：" />
                    <ListBox
                        x:Name="leftWafersListBox"
                        Grid.Row="1"
                        Margin="5,5,5,10"
                        dd:DragDrop.DragAdornerTemplate="{StaticResource DragAdorner}"
                        dd:DragDrop.DropHandler="{Binding Path=DataContext.WaferDragDropHandler, RelativeSource={RelativeSource AncestorType={x:Type local:UContainer}}}"
                        dd:DragDrop.IsDropTarget="True"
                        dd:DragDrop.UseDefaultDragAdorner="False"
                        dd:DragDrop.UseDefaultEffectDataTemplate="True"
                        ItemsSource="{Binding LeftWaferAction.Wafers}"
                        MouseDoubleClick="WafersListBox_MouseDoubleClick"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectionMode="Single"
                        Tag="LeftWafer"
                        ToolTip="双击选择项移除">
                        <i:Interaction.Behaviors>
                            <beavior:ListBoxScrollToBottomBehavior />
                        </i:Interaction.Behaviors>
                        <dd:DragDrop.IsDragSource>
                            <MultiBinding Converter="{StaticResource AndMultiValueConverter2}">
                                <Binding
                                    Mode="OneWay"
                                    Path="DataContext.IsRunning"
                                    RelativeSource="{RelativeSource AncestorType={x:Type local:UContainer}}" />
                                <Binding
                                    Mode="OneWay"
                                    Path="DataContext.Host.Mode"
                                    RelativeSource="{RelativeSource AncestorType={x:Type local:UContainer}}" />
                            </MultiBinding>
                        </dd:DragDrop.IsDragSource>
                    </ListBox>
                </Grid>

                <!--<StackPanel
                    Grid.Row="1"
                    Grid.RowSpan="3"
                    Grid.Column="3"
                    VerticalAlignment="Top"
                    Background="LightBlue"
                    Orientation="Vertical">
                    <TextBlock Margin="15,0,0,0" Text="Wafer列表：" />
                    <ListBox
                        Margin="5,5,5,10"
                        ItemsSource="{Binding Wafers}"
                        ScrollViewer.VerticalScrollBarVisibility="Visible" />
                </StackPanel>-->

                <WrapPanel
                    Grid.Row="2"
                    Grid.Column="0"
                    Grid.ColumnSpan="3"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center">

                    <!--<TextBlock FontSize="20">
                        <Run Text="{Binding Title}" />
                        <Run Text=" " />
                        <Run Text="{Binding ChamberName}" />
                    </TextBlock>-->
                    <!--  多值绑定  -->
                    <TextBlock FontSize="16">
                        <TextBlock.Text>
                            <MultiBinding FallbackValue="标题绑定失败提示" StringFormat="{}{0}【{1}】左边总容量[{2}],剩余[{3}]">
                                <Binding Path="Title" />
                                <Binding Path="ChamberName" />
                                <Binding Path="RightWaferAction.Capacity" />
                                <Binding Path="RightWaferAction.RemainWaferCount" />
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </WrapPanel>
                <!--  右边Chamber图片展示 - 替换Badge控件以避免HandyControl动画异常  -->
                <Grid
                    Grid.Row="3"
                    Grid.Column="0"
                    Visibility="{Binding RightWaferAction.HaveWafer, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Image
                        Width="100"
                        HorizontalAlignment="Left"
                        Source="/Assets/Images/plate.png" />

                    <!--  Badge数字显示  -->
                    <Border
                        MinWidth="20"
                        MinHeight="20"
                        Margin="2,20,5,0"
                        Padding="4,2"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Background="Green"
                        CornerRadius="10">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontWeight="Bold"
                            Foreground="White"
                            Text="{Binding RightWaferAction.CurWaferCount}" />
                    </Border>
                </Grid>

                <!--  右边Chamber Move、Replace判断  -->
                <StackPanel Grid.Row="3" Grid.Column="1">
                    <StackPanel HorizontalAlignment="Left" Orientation="Horizontal">
                        <CheckBox
                            HorizontalAlignment="Left"
                            Content="Move"
                            IsChecked="{Binding RightWaferAction.IsMoveChecked}"
                            IsEnabled="{Binding RightWaferAction.IsMove}" />
                        <CheckBox
                            Margin="10,5,0,0"
                            HorizontalAlignment="Left"
                            Content="Replace"
                            IsChecked="{Binding RightWaferAction.IsReplaceChecked}"
                            IsEnabled="{Binding RightWaferAction.IsReplace}" />
                    </StackPanel>
                    <!--  右边Chamber Create、Delete判断  -->
                    <StackPanel
                        Margin="0,5,0,0"
                        VerticalAlignment="Top"
                        Orientation="Horizontal">
                        <StackPanel HorizontalAlignment="Left" Orientation="Horizontal">
                            <CheckBox
                                Name="chkRightWafeCreate"
                                HorizontalAlignment="Left"
                                Content="Create"
                                IsChecked="{Binding RightWaferAction.IsCreateChecked}"
                                IsEnabled="{Binding RightWaferAction.IsCreate}" />
                            <CheckBox
                                Name="chkRightWafeDelete"
                                Margin="5,5,0,0"
                                HorizontalAlignment="Left"
                                Content="Delete"
                                IsChecked="{Binding RightWaferAction.IsDeleteChecked}"
                                IsEnabled="{Binding RightWaferAction.IsDelete}" />
                            <!--<StackPanel VerticalAlignment="Top" Orientation="Horizontal">
                                <StackPanel />
                                <TextBlock Text="Wafer可选列表:" />
                                <ListBox ItemsSource="{Binding WaferNO}" />
                            </StackPanel>-->
                            <StackPanel Orientation="Horizontal">
                                <ComboBox
                                    x:Name="cbRightAvailableWaferIds"
                                    Margin="5,5,5,10"
                                    d:ItemsSource="{d:SampleData ItemCount=5}"
                                    hc:InfoElement.Title="SLOT："
                                    hc:InfoElement.TitlePlacement="Left"
                                    DisplayMemberPath="WaferNo"
                                    ItemsSource="{x:Static gg:Golbal.CurRightAvailableWafers}"
                                    Style="{StaticResource ComboBoxExtend}" />

                                <Button
                                    Name="btnRightWaferRun"
                                    Margin="0,0,0,0"
                                    Click="btnRightWaferRun_Click"
                                    Content="执行"
                                    IsEnabled="{Binding RightWaferAction.CanRunCreateOrDeleteCmd}"
                                    Style="{StaticResource ButtonDefault}" />
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>

                <!--  右边Wafer列表绑定，默认最多为1,  -->
                <Grid Grid.Row="3" Grid.Column="2">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <TextBlock Margin="15,0,0,0" Text="右边Wafer列表：" />
                    <ListBox
                        x:Name="rightWafersListBox"
                        Grid.Row="1"
                        Margin="5,5,5,10"
                        dd:DragDrop.DragAdornerTemplate="{StaticResource DragAdorner}"
                        dd:DragDrop.DropHandler="{Binding Path=DataContext.WaferDragDropHandler, RelativeSource={RelativeSource AncestorType={x:Type local:UContainer}}}"
                        dd:DragDrop.IsDropTarget="True"
                        dd:DragDrop.UseDefaultDragAdorner="False"
                        dd:DragDrop.UseDefaultEffectDataTemplate="True"
                        ItemsSource="{Binding RightWaferAction.Wafers}"
                        MouseDoubleClick="WafersListBox_MouseDoubleClick"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectionMode="Single"
                        Tag="RightWafer">
                        <i:Interaction.Behaviors>
                            <beavior:ListBoxScrollToBottomBehavior />
                        </i:Interaction.Behaviors>
                        <dd:DragDrop.IsDragSource>
                            <MultiBinding Converter="{StaticResource AndMultiValueConverter2}">
                                <Binding
                                    Mode="OneWay"
                                    Path="DataContext.IsRunning"
                                    RelativeSource="{RelativeSource AncestorType={x:Type local:UContainer}}" />
                                <Binding
                                    Mode="OneWay"
                                    Path="DataContext.Host.Mode"
                                    RelativeSource="{RelativeSource AncestorType={x:Type local:UContainer}}" />
                            </MultiBinding>
                        </dd:DragDrop.IsDragSource>
                    </ListBox>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>