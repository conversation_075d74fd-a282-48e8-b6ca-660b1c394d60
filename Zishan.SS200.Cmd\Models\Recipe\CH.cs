﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.Pkcs;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.Recipe
{
    public class CH : BindableBase
    {
        /// <summary>
        /// 腔体名
        /// </summary>
        public EnuRecipeChamberName ChamberName { get => _ChamberName; set => SetProperty(ref _ChamberName, value); }
        private EnuRecipeChamberName _ChamberName;

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool Enable { get => _Enable; set => SetProperty(ref _Enable, value); }
        private bool _Enable;

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get => _Order; set => SetProperty(ref _Order, value); }
        private int _Order;

        public CH(EnuRecipeChamberName enuChamberName, int order)
        {
            ChamberName = enuChamberName;
            Order = order;
        }

        public override string ToString()
        {
            return $"ChamberName:{ChamberName}Enable:{Enable},Order:{Order},";
        }
    }
}