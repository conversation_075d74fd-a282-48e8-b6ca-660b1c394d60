﻿AR9
T-axis zero
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			T-axis position status review
RS9 or others position
				RS9
					command done
				others position
					Robot T-axis position confirm
						min(ABS(RTF-RP1),ABS(RTF-RP2),ABS(RTF-RP3),ABS(RTF-RP4),ABS(RTF-RP5),ABS(RTF-RP6),ABS(RFT-RP7),ABS(RTF-RP8))
							AR10-min RP X
T-axis position to min RP X
								AR19
R-axis zero position
									AR39-RPS27
Z-axis postion to RPS27
RPS27=Z-axis for rotation
										AR10-RP9
											slide out back sensor installation
SPS11
												SPS11=Y
													shuttle slide out sensor status review
shuttle DI19~DI20
														DI19=0 and DI20 =0
															command done
														DI19=0 and DI20=1
															RA20 ALARM
														DI19=1 and DI20=0
															RA19 ALARM
														DI19=1 and DI20=1
															RA21 ALARM
												SPS11=N
													command done
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM