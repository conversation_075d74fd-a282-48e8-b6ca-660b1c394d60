<UserControl
    x:Class="Zishan.SS200.Cmd.Views.TransferWafer"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:behavior="clr-namespace:Zishan.SS200.Cmd.Behaviors"
    xmlns:common="clr-namespace:Zishan.SS200.Cmd.Common"
    xmlns:conv="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dvm="clr-namespace:Zishan.SS200.Cmd.ViewModels.DesignViewModels"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:userControls="clr-namespace:Zishan.SS200.Cmd.UserControls"
    xmlns:viewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    xmlns:visualBasic="clr-namespace:Microsoft.VisualBasic;assembly=Microsoft.VisualBasic.Core"
    xmlns:wuext="https://github.com/Monika1313/Wu"
    d:Background="LightBlue"
    d:DataContext="{x:Static dvm:TransferWaferrDesignViewModel.Instance}"
    d:DesignHeight="1290"
    d:DesignWidth="2146"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <UserControl.Resources>
        <conv:DescriptionConverter x:Key="DescriptionConverter" />
        <conv:AndMultiValueConverter x:Key="AndMultiValueConverter" />
        <conv:RecipeNameToDetailConverter x:Key="RecipeNameToDetailConverter" />
        <conv:ExecutedCountDisplayConverter x:Key="ExecutedCountDisplayConverter" />

        <!--  现代化样式资源  -->
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Offset="0" Color="#FFFFFF" />
                        <GradientStop Offset="1" Color="#F8F9FA" />
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="CornerRadius" Value="8" />
            <Setter Property="BorderBrush" Value="#E9ECEF" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect
                        BlurRadius="8"
                        Opacity="0.1"
                        ShadowDepth="2"
                        Color="#000000" />
                </Setter.Value>
            </Setter>
            <Setter Property="Margin" Value="5" />
            <Setter Property="Padding" Value="10" />
        </Style>

        <Style
            x:Key="ModernButtonStyle"
            BasedOn="{StaticResource ButtonPrimary}"
            TargetType="Button">
            <Setter Property="Padding" Value="12,6" />
            <Setter Property="Margin" Value="4" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <!--  现代化GroupBox样式  -->
        <Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Offset="0" Color="#FAFBFC" />
                        <GradientStop Offset="1" Color="#F1F3F4" />
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="BorderBrush" Value="#DEE2E6" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="Padding" Value="12" />
            <Setter Property="Margin" Value="5" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="GroupBox">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                            <Border.Effect>
                                <DropShadowEffect
                                    BlurRadius="4"
                                    Opacity="0.08"
                                    ShadowDepth="1"
                                    Color="#000000" />
                            </Border.Effect>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Border
                                    Grid.Row="0"
                                    Margin="0,0,0,8"
                                    Padding="12,6"
                                    Background="#E3F2FD"
                                    BorderBrush="#BBDEFB"
                                    BorderThickness="0,0,0,1"
                                    CornerRadius="4,4,0,0">
                                    <ContentPresenter
                                        ContentSource="Header"
                                        TextElement.FontSize="13"
                                        TextElement.FontWeight="SemiBold"
                                        TextElement.Foreground="#1565C0" />
                                </Border>
                                <ContentPresenter Grid.Row="1" Margin="{TemplateBinding Padding}" />
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                <GradientStop Offset="0" Color="#F8F9FA" />
                <GradientStop Offset="1" Color="#E9ECEF" />
            </LinearGradientBrush>
        </Grid.Background>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="5" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  主界面展示区域 - 使用现代化卡片布局  -->
        <Border
            Grid.Row="0"
            Grid.Column="0"
            Style="{StaticResource ModernCardStyle}"
            Visibility="Visible">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="5*" />
                    <RowDefinition Height="5" />
                    <RowDefinition Height="auto" />
                </Grid.RowDefinitions>
                <!--  Chamber、Robot双臂、Cassette、Cooling展示区域  -->
                <UniformGrid Columns="3" Rows="2">
                    <!--  Wafer搬运界面展示区域 优化，只需要UContainer用户控件界面优化，小屏幕显示自适应优化，避免列表中的wafer状态被遮挡，界面美观  -->
                    <userControls:UContainer CurCharber="{Binding ChamberA}" MaxWafers="{Binding WaferTotalCount}" />
                    <userControls:UContainer CurCharber="{Binding ChamberB}" MaxWafers="{Binding WaferTotalCount}" />
                    <userControls:UContainer CurCharber="{Binding ChamberC}" MaxWafers="{Binding WaferTotalCount}" />
                    <userControls:UContainer CurCharber="{Binding Cassette}" MaxWafers="{Binding WaferTotalCount}" />
                    <userControls:UContainer CurCharber="{Binding Cooling}" MaxWafers="{Binding WaferTotalCount}" />

                    <!--<Setter Property="Command" Value="{Binding DataContext.DoubleClickCommand, RelativeSource={RelativeSource AncestorType={x:Type ListBox}}}" />
            <Setter Property="CommandParameter" Value="{Binding}" />-->

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="1*" />
                            <RowDefinition Height="5" />
                            <RowDefinition Height="1*" />
                        </Grid.RowDefinitions>
                        <Border
                            Margin="0,5"
                            CornerRadius="5"
                            ToolTip="机械臂Nose端">
                            <userControls:UContainer CurCharber="{Binding LeftRobotIRArm}" MaxWafers="{Binding WaferTotalCount}" />
                        </Border>
                        <GridSplitter
                            Grid.Row="1"
                            Grid.Column="0"
                            Height="5"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center" />
                        <Border
                            Grid.Row="2"
                            CornerRadius="5"
                            ToolTip="机械臂Smooth端">
                            <userControls:UContainer CurCharber="{Binding RightRobotIRArm}" MaxWafers="{Binding WaferTotalCount}" />
                        </Border>
                    </Grid>
                </UniformGrid>

                <!--  上下水平分割线  -->
                <GridSplitter
                    Grid.Row="1"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Height="5"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Center" />

                <!--  PLC信号仿真、配方选择、操作按钮  -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <Border CornerRadius="5">
                        <GroupBox
                            Margin="5,0"
                            Background="Transparent"
                            Header="PLC信号仿真">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                    <WrapPanel>
                                        <CheckBox
                                            Margin="0,5,0,0"
                                            HorizontalAlignment="Left"
                                            Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=RobotNorthHaseWafer}"
                                            IsChecked="{Binding CurPLcsignalSimulation.RobotNorthHaseWafer}"
                                            IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />

                                        <TextBlock Margin="30,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotNorth, StringFormat='SLOT：{0}'}" />
                                    </WrapPanel>
                                    <WrapPanel>
                                        <CheckBox
                                            Margin="0,5,0,0"
                                            HorizontalAlignment="Left"
                                            Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=RobotSmothHaseWafer}"
                                            IsChecked="{Binding CurPLcsignalSimulation.RobotSmothHaseWafer}"
                                            IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />

                                        <TextBlock Margin="18,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotSmooth, StringFormat='SLOT：{0}'}" />
                                    </WrapPanel>
                                </StackPanel>

                                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                    <WrapPanel>
                                        <CheckBox
                                            Margin="0,5,0,0"
                                            HorizontalAlignment="Left"
                                            Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChaHasWafer}"
                                            IsChecked="{Binding CurPLcsignalSimulation.ChaHasWafer}"
                                            IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />
                                        <TextBlock Margin="50,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotCha, StringFormat='SLOT：{0}'}" />
                                    </WrapPanel>

                                    <WrapPanel>
                                        <CheckBox
                                            Margin="0,5,0,0"
                                            HorizontalAlignment="Left"
                                            Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChbHasWafer}"
                                            IsChecked="{Binding CurPLcsignalSimulation.ChbHasWafer}"
                                            IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />
                                        <TextBlock Margin="50,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotChb, StringFormat='SLOT：{0}'}" />
                                    </WrapPanel>

                                    <WrapPanel>
                                        <CheckBox
                                            Margin="0,5,0,0"
                                            HorizontalAlignment="Left"
                                            Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChcHasWafer}"
                                            IsChecked="{Binding CurPLcsignalSimulation.ChcHasWafer}"
                                            IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />
                                        <TextBlock Margin="50,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotChc, StringFormat='SLOT：{0}'}" />
                                    </WrapPanel>

                                    <WrapPanel>
                                        <CheckBox
                                            Margin="0,5,0,0"
                                            HorizontalAlignment="Left"
                                            Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=CoolingHasWafer}"
                                            IsChecked="{Binding CurPLcsignalSimulation.CoolingHasWafer}"
                                            IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />
                                        <TextBlock Margin="30,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotCooling, StringFormat='SLOT：{0}'}" />
                                    </WrapPanel>
                                </StackPanel>
                                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                    <CheckBox
                                        Margin="30,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChaProcessFinished}"
                                        IsChecked="{Binding CurPLcsignalSimulation.ChaProcessFinished}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.EnableChaProcessFinished}" />
                                    <CheckBox
                                        Margin="30,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChbProcessFinished}"
                                        IsChecked="{Binding CurPLcsignalSimulation.ChbProcessFinished}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.EnableChbProcessFinished}" />
                                    <CheckBox
                                        Margin="30,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChcProcessFinished}"
                                        IsChecked="{Binding CurPLcsignalSimulation.ChcProcessFinished}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.EnableChcProcessFinished}" />
                                    <CheckBox
                                        Margin="30,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=CoolingProcessFinished}"
                                        IsChecked="{Binding CurPLcsignalSimulation.CoolingProcessFinished, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

                                    <!--  是否启用启用自动点击  -->
                                    <CheckBox
                                        Margin="30,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=IsAutoClick}"
                                        IsChecked="{Binding CurPLcsignalSimulation.IsAutoClick}" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                    </Border>
                    <Border Grid.Column="1" CornerRadius="5">
                        <GroupBox Background="Transparent" Header="{Binding Title}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="auto" />
                                </Grid.ColumnDefinitions>
                                <StackPanel HorizontalAlignment="Left">
                                    <TextBox
                                        Width="140"
                                        Margin="5,0,0,0"
                                        VerticalAlignment="Center"
                                        hc:InfoElement.Placeholder="定义Wafer数量"
                                        hc:InfoElement.Title="定义Wafer数量:"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        hc:TitleElement.TitleWidth="100"
                                        wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        IsEnabled="{Binding IsStopLoopRunning}"
                                        Style="{StaticResource TextBoxExtend}"
                                        Text="{Binding WaferTotalCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        ToolTip="定义Wafer数量" />

                                    <Button
                                        Margin="5,0,0,0"
                                        HorizontalAlignment="Left"
                                        Command="{Binding RunRecipeCommand}"
                                        Content="RunRecipe"
                                        Style="{StaticResource ButtonPrimary.Small}" />
                                </StackPanel>

                                <Grid Grid.Column="1" Margin="10,0,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.ColumnSpan="3" Orientation="Horizontal">
                                        <TextBox
                                            Width="190"
                                            VerticalAlignment="Center"
                                            hc:InfoElement.Title="左边条码输入"
                                            hc:InfoElement.TitlePlacement="Left"
                                            hc:TitleElement.HorizontalAlignment="Left"
                                            wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                            Style="{StaticResource TextBoxExtend.Small}"
                                            Text="{Binding CurRunRecipeInfo.LeftBarcode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                        <TextBox
                                            Width="190"
                                            Margin="5,0,0,0"
                                            VerticalAlignment="Center"
                                            hc:InfoElement.Title="右边条码输入"
                                            hc:InfoElement.TitlePlacement="Left"
                                            hc:TitleElement.HorizontalAlignment="Left"
                                            wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                            Style="{StaticResource TextBoxExtend.Small}"
                                            Text="{Binding CurRunRecipeInfo.RightBarcode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                    </StackPanel>

                                    <StackPanel
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="3"
                                        Orientation="Horizontal">
                                        <TextBlock
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Center"
                                            Text="制定配方" />

                                        <ComboBox
                                            MinWidth="110"
                                            Margin="30,3,3,3"
                                            IsEnabled="{Binding IsRunning, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource Boolean2BooleanReConverter}}"
                                            ItemsSource="{Binding RecipeList}"
                                            SelectedItem="{Binding CurSelectedRecipe}"
                                            Style="{StaticResource ComboBox.Small}">
                                            <ComboBox.ItemTemplate>
                                                <DataTemplate>
                                                    <StackPanel>
                                                        <TextBlock FontWeight="Bold" Text="{Binding}" />
                                                        <!--<TextBlock FontStyle="Italic" Text="{Binding Description}" />-->
                                                    </StackPanel>
                                                </DataTemplate>
                                            </ComboBox.ItemTemplate>
                                        </ComboBox>

                                        <!--  显示选中项的详细信息  -->
                                        <ContentControl Content="{Binding CurSelectedRecipe, Converter={StaticResource RecipeNameToDetailConverter}}">
                                            <ContentControl.ContentTemplate>
                                                <DataTemplate>
                                                    <WrapPanel>
                                                        <StackPanel>
                                                            <TextBlock FontWeight="Bold" Text="{Binding ChRecipeName, StringFormat={}CH:{0}, TargetNullValue='腔体配方'}" />
                                                            <TextBlock FontWeight="Bold" Text="{Binding CoolingRecipeName, StringFormat={}CP:{0}, TargetNullValue='Cooling配方'}" />
                                                        </StackPanel>
                                                        <!--  机限多值绑定，小屏幕会挡住，暂时注释掉  -->
                                                        <!--<TextBlock
                                                    VerticalAlignment="Center"
                                                    FontSize="12"
                                                    FontWeight="Light">
                                                    <TextBlock.Text>
                                                            <MultiBinding StringFormat="{}[机限 ChamberA:{0}{1},ChamberB:{2}{3},CHC:{4}{5}]" TargetNullValue="机限">
                                                            <Binding Path="ChaEnable" />
                                                            <Binding Path="ChaOrder" />
                                                            <Binding Path="ChbEnable" />
                                                            <Binding Path="ChbOrder" />
                                                            <Binding Path="ChcEnable" />
                                                            <Binding Path="ChcOrder" />
                                                        </MultiBinding>
                                                    </TextBlock.Text>
                                                </TextBlock>-->
                                                    </WrapPanel>
                                                </DataTemplate>
                                            </ContentControl.ContentTemplate>
                                        </ContentControl>
                                        <!--<ComboBox
                                    MinWidth="100"
                                    Margin="30,3,3,3"
                                    ItemsSource="{Binding RecipeList}"
                                    SelectedItem="{Binding CurSelectedRecipe}"
                                    Style="{StaticResource ComboBox.Small}" />-->

                                        <TextBlock
                                            Margin="25,0,0,0"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Center"
                                            Text="Top:" />
                                        <ComboBox
                                            MinWidth="30"
                                            Margin="10,3,3,3"
                                            ItemsSource="{Binding WaferCountList}"
                                            SelectedItem="{Binding CurRunRecipeInfo.Top}"
                                            Style="{StaticResource ComboBox.Small}" />
                                        <TextBlock
                                            Margin="25,0,0,0"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Center"
                                            Text="Bottom:" />
                                        <ComboBox
                                            MinWidth="30"
                                            Margin="10,3,3,3"
                                            ItemsSource="{Binding WaferCountList}"
                                            SelectedItem="{Binding CurRunRecipeInfo.Bottom}"
                                            Style="{StaticResource ComboBox.Small}" />
                                    </StackPanel>

                                    <StackPanel
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="3"
                                        Margin="0,5"
                                        HorizontalAlignment="Left"
                                        Orientation="Horizontal">

                                        <Button
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Content="Run"
                                            Style="{StaticResource ButtonPrimary.Small}" />
                                        <Button
                                            Margin="20,0,0,0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Content="Stop"
                                            Style="{StaticResource ButtonWarning.Small}" />
                                        <TextBlock Text="{Binding AppConfigInfo}" />
                                    </StackPanel>
                                </Grid>

                                <Grid Grid.Column="2" Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0">
                                        <CheckBox
                                            Margin="5"
                                            HorizontalAlignment="Left"
                                            Content="是否读取PLC日志"
                                            IsChecked="{Binding IsReadPlcLog, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                        <ToggleButton
                                            HorizontalAlignment="Left"
                                            Command="{Binding ReadPLCCurRunStatusCommand}"
                                            Content="读取PLC运行状态"
                                            Style="{StaticResource ToggleButtonPrimary.Small}" />

                                        <Grid ToolTip="安全提醒：在非运行状态下才可以执行同步PLC运行状态">
                                            <ToggleButton
                                                HorizontalAlignment="Left"
                                                Command="{Binding ReadAndSetPLCCurRunStatusCommand}"
                                                Content="同步PLC运行状态"
                                                IsEnabled="{Binding IsRunning, Converter={StaticResource Boolean2BooleanReConverter}}"
                                                Style="{StaticResource ToggleButtonDefault.Small}" />
                                        </Grid>

                                        <ToggleButton
                                            Margin="0,5,0,0"
                                            HorizontalAlignment="Left"
                                            Command="{Binding DevTestCommand}"
                                            Content="DevTest"
                                            Style="{StaticResource ToggleButtonInfo.Small}" />
                                    </StackPanel>
                                    <StackPanel Grid.Column="1" Margin="5,0,0,0">
                                        <CheckBox
                                            Margin="5"
                                            HorizontalAlignment="Left"
                                            Content="IsRunning"
                                            IsChecked="{Binding IsRunning, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                            IsEnabled="False" />

                                        <CheckBox
                                            Margin="5,10"
                                            HorizontalAlignment="Left"
                                            Content="UI同步PLC运行状态"
                                            IsChecked="{Binding IsSyncPlcCurRunStatus, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </GroupBox>
                    </Border>
                </Grid>
            </Grid>
        </Border>

        <!--  上下水平分割线  -->
        <GridSplitter
            Grid.Row="1"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Height="5"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Center" />

        <!--  手动模式命令布局 - 现代化卡片样式  -->
        <Border
            Grid.Row="2"
            Grid.Column="0"
            Grid.ColumnSpan="3"
            Style="{StaticResource ModernCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  标题区域  -->
                <TextBlock
                    Grid.Row="0"
                    Margin="0,0,0,12"
                    HorizontalAlignment="Center"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Foreground="#2C3E50"
                    Text="手动模式命令" />

                <!--  内容区域 - 一行显示布局  -->
                <Grid Grid.Row="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="10" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--  Sequence循环控制区域 - 一行显示  -->
                    <GroupBox
                        Grid.Row="0"
                        Grid.Column="0"
                        Margin="5"
                        Header="Sequence循环控制"
                        Style="{StaticResource ModernGroupBoxStyle}">
                        <WrapPanel VerticalAlignment="Center" Orientation="Horizontal">
                            <CheckBox
                                Margin="5"
                                VerticalAlignment="Center"
                                Command="{Binding PinSearchBeforeTransferCommand}"
                                Content="PinSearch"
                                FontSize="11"
                                IsChecked="{Binding PinSearchBeforeTransfer}"
                                Style="{StaticResource CheckBoxBaseStyle}"
                                ToolTip="启用后，在搬运操作开始之前会自动执行PinSearch，获取Smooth、Nose基准值，后面搬运会根据基准值计算精确定位SLOT" />

                            <ToggleButton
                                Width="60"
                                Margin="5"
                                Command="{Binding ProcessResetCommand}"
                                Content="重置"
                                IsEnabled="{Binding IsReset}"
                                Style="{StaticResource ToggleButtonPrimary.Small}"
                                Tag="GVL.sPcControlWord"
                                ToolTip="重置系统状态和计数器" />

                            <TextBox
                                Width="70"
                                Margin="5"
                                VerticalAlignment="Center"
                                hc:InfoElement.Placeholder="次数"
                                hc:InfoElement.Title="次数:"
                                hc:InfoElement.TitlePlacement="Left"
                                hc:TitleElement.HorizontalAlignment="Left"
                                hc:TitleElement.TitleWidth="35"
                                wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                IsEnabled="{Binding IsStopLoopRunning}"
                                Style="{StaticResource TextBoxExtend}"
                                Text="{Binding LoopCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="设置循环执行次数：-1代表无限循环" />

                            <!--  已执行次数显示 - 紧凑样式  -->
                            <Border
                                MinWidth="80"
                                Margin="5"
                                Padding="6,3"
                                Background="LightGreen"
                                BorderBrush="Gray"
                                BorderThickness="1"
                                CornerRadius="3"
                                ToolTip="当前循环执行的累计次数">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    d:Text="已执行: 0次"
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#2E7D32">
                                    <TextBlock.Text>
                                        <MultiBinding Converter="{StaticResource ExecutedCountDisplayConverter}">
                                            <Binding Path="ExecutedCount" />
                                            <Binding Path="HasStartedExecution" />
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </Border>

                            <ToggleButton
                                Width="60"
                                Margin="5"
                                Command="{Binding ExecuteProcessLoopCommand}"
                                Content="循环"
                                IsEnabled="{Binding IsStopLoopRunning}"
                                Style="{StaticResource ToggleButtonInfo.Small}"
                                Tag="GVL.sPcControlWord"
                                ToolTip="开始循环执行配方流程" />

                            <ToggleButton
                                Width="60"
                                Margin="5"
                                d:Content="暂停"
                                Command="{Binding ProcessPauseNewCommand}"
                                Content="{Binding IsEnablePauseContent, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                IsEnabled="{Binding IsEnablePauseCmd}"
                                Style="{StaticResource ToggleButtonWarning.Small}"
                                ToolTip="暂停/继续当前执行的流程" />

                            <ToggleButton
                                Width="60"
                                Margin="5"
                                Command="{Binding ProcessStopCommand}"
                                Content="停止"
                                IsEnabled="{Binding IsEnableStopCmd}"
                                Style="{StaticResource ToggleButtonDanger.Small}"
                                Tag="GVL.sPcControlWord"
                                ToolTip="停止当前执行的流程" />
                        </WrapPanel>
                    </GroupBox>

                    <!--  PinSearch状态显示区域  -->
                    <GroupBox
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="5"
                        Header="PinSearch状态"
                        Style="{StaticResource ModernGroupBoxStyle}"
                        Visibility="{Binding PinSearchBeforeTransfer, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!--  执行状态  -->
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="5,2"
                                Text="状态：" />
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="5,2"
                                FontWeight="Medium"
                                Text="{Binding PinSearchStatus}" />

                            <!--  基准值显示  -->
                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="5,2"
                                Text="基准值：" />
                            <!--
                                ，在PinSearch运行过程中，获取到的新PinSearch值，在对应的Smooth或者Nose: 前面加个*号，代表是新PinSearch获取到的值，
                                搬运过程中使用新值一直带*，如果用默认值、或者失败使用上一次PinSearch获取的值，则不带*号
                            -->
                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,2">
                                <Run Text="{Binding SmoothPinSearchDisplayText}" />
                                <Run
                                    d:Text="1900"
                                    FontWeight="Bold"
                                    Foreground="Blue"
                                    Text="{Binding SmoothBasePinSearchValue}" />
                                <Run Text=", " />
                                <Run Text="{Binding NosePinSearchDisplayText}" />
                                <Run
                                    d:Text="1900"
                                    FontWeight="Bold"
                                    Foreground="Green"
                                    Text="{Binding NoseBasePinSearchValue}" />
                            </TextBlock>

                            <!--  最后执行时间  -->
                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="5,2"
                                Text="执行时间：" />
                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="1"
                                Margin="5,2"
                                Text="{Binding PinSearchLastExecuteTime}" />
                        </Grid>
                    </GroupBox>

                    <!--  单命令搬运控制区域 - 自适应列宽布局  -->
                    <GroupBox
                        Grid.Row="0"
                        Grid.Column="3"
                        Margin="5"
                        Header="单命令搬运"
                        Style="{StaticResource ModernGroupBoxStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!--  第一行：控制元素 - 一行显示自适应布局  -->
                            <Grid Grid.Row="0">
                                <Grid.ColumnDefinitions>
                                    <!--  From位置 - 自适应宽度  -->
                                    <ColumnDefinition Width="*" MinWidth="100" />
                                    <!--  From SLOT - 固定较小宽度  -->
                                    <ColumnDefinition Width="Auto" />
                                    <!--  To位置 - 自适应宽度  -->
                                    <ColumnDefinition Width="*" MinWidth="100" />
                                    <!--  To SLOT - 固定较小宽度  -->
                                    <ColumnDefinition Width="Auto" />
                                    <!--  机械臂 - 自适应宽度  -->
                                    <ColumnDefinition Width="*" MinWidth="80" />
                                    <!--  执行按钮 - 固定宽度  -->
                                    <ColumnDefinition Width="Auto" />
                                    <!--  执行状态 - 自适应宽度，占用剩余空间  -->
                                    <ColumnDefinition Width="*" MinWidth="150" />
                                </Grid.ColumnDefinitions>

                                <ComboBox
                                    Grid.Column="0"
                                    Margin="3"
                                    VerticalAlignment="Center"
                                    hc:InfoElement.Necessary="True"
                                    hc:InfoElement.Placeholder="From位置"
                                    hc:InfoElement.Symbol="*"
                                    hc:InfoElement.Title="From:"
                                    hc:InfoElement.TitlePlacement="Left"
                                    hc:TitleElement.TitleWidth="50"
                                    Cursor="Hand"
                                    DisplayMemberPath="ChamberName"
                                    ItemsSource="{Binding FromChamber}"
                                    SelectedItem="{Binding SelectedFromChamber}"
                                    SelectedValuePath="ChamberName"
                                    Style="{StaticResource ComboBoxExtend.Small}"
                                    ToolTip="选择晶圆搬运的源位置" />

                                <ComboBox
                                    Grid.Column="1"
                                    MinWidth="100"
                                    Margin="3"
                                    VerticalAlignment="Center"
                                    d:ItemsSource="{d:SampleData ItemCount=5}"
                                    hc:InfoElement.Necessary="True"
                                    hc:InfoElement.Placeholder="SLOT"
                                    hc:InfoElement.Symbol="*"
                                    hc:InfoElement.Title="SLOT:"
                                    hc:InfoElement.TitlePlacement="Left"
                                    hc:TitleElement.TitleWidth="50"
                                    DisplayMemberPath="WaferNo"
                                    ItemsSource="{Binding FromAvailableWafers}"
                                    SelectedValue="{Binding SelectedFromSlot, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, FallbackValue=1, TargetNullValue=1}"
                                    SelectedValuePath="WaferNo"
                                    Style="{StaticResource ComboBoxExtend.Small}"
                                    ToolTip="选择源位置的晶圆槽位号" />

                                <ComboBox
                                    Grid.Column="2"
                                    Margin="3"
                                    VerticalAlignment="Center"
                                    hc:InfoElement.Necessary="True"
                                    hc:InfoElement.Placeholder="To位置"
                                    hc:InfoElement.Symbol="*"
                                    hc:InfoElement.Title="To:"
                                    hc:InfoElement.TitlePlacement="Left"
                                    hc:TitleElement.TitleWidth="50"
                                    Cursor="Hand"
                                    DisplayMemberPath="ChamberName"
                                    ItemsSource="{Binding ToChamber}"
                                    SelectedItem="{Binding SelectedToChamber}"
                                    SelectedValuePath="ChamberName"
                                    Style="{StaticResource ComboBoxExtend.Small}"
                                    ToolTip="选择晶圆搬运的目标位置" />

                                <ComboBox
                                    Grid.Column="3"
                                    MinWidth="100"
                                    Margin="3"
                                    VerticalAlignment="Center"
                                    d:ItemsSource="{d:SampleData ItemCount=5}"
                                    hc:InfoElement.Necessary="True"
                                    hc:InfoElement.Placeholder="SLOT"
                                    hc:InfoElement.Symbol="*"
                                    hc:InfoElement.Title="SLOT:"
                                    hc:InfoElement.TitlePlacement="Left"
                                    hc:TitleElement.TitleWidth="50"
                                    DisplayMemberPath="WaferNo"
                                    ItemsSource="{Binding ToAvailableWafers}"
                                    SelectedValue="{Binding SelectedToSlot, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, FallbackValue=1, TargetNullValue=1}"
                                    SelectedValuePath="WaferNo"
                                    Style="{StaticResource ComboBoxExtend.Small}"
                                    ToolTip="选择目标位置的晶圆槽位号" />

                                <ComboBox
                                    Grid.Column="4"
                                    Margin="3"
                                    VerticalAlignment="Center"
                                    hc:InfoElement.Necessary="True"
                                    hc:InfoElement.Placeholder="搬运机械臂"
                                    hc:InfoElement.Symbol="*"
                                    hc:InfoElement.Title="机械臂:"
                                    hc:InfoElement.TitlePlacement="Left"
                                    hc:TitleElement.TitleWidth="60"
                                    Cursor="Hand"
                                    ItemsSource="{Binding ByArmFetchSide}"
                                    SelectedIndex="0"
                                    SelectedItem="{Binding SelectedByArmFetchSide}"
                                    Style="{StaticResource ComboBoxExtend.Small}"
                                    ToolTip="选择执行搬运操作的机械臂端（Nose/Smooth）" />

                                <Button
                                    Grid.Column="5"
                                    Width="80"
                                    Margin="8,3,3,3"
                                    d:Visibility="Visible"
                                    Command="{Binding TrasferWaferCommand}"
                                    Content="执行搬运"
                                    Style="{StaticResource ModernButtonStyle}"
                                    ToolTip="安全提醒：在手动模式非运行状态下才可以执行手动命令"
                                    Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />

                                <!--  执行状态显示区域 - 同一行显示  -->
                                <TextBox
                                    Grid.Column="6"
                                    Margin="8,3,3,3"
                                    VerticalAlignment="Center"
                                    hc:InfoElement.Placeholder="PLC执行状态"
                                    hc:InfoElement.Title="状态:"
                                    hc:InfoElement.TitlePlacement="Left"
                                    hc:TitleElement.HorizontalAlignment="Left"
                                    hc:TitleElement.TitleWidth="35"
                                    Style="{StaticResource TextBoxExtend}"
                                    Text="{Binding CommandResult}"
                                    ToolTip="GVL.CMD_State.CMD_Busy" />
                            </Grid>
                        </Grid>
                    </GroupBox>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>