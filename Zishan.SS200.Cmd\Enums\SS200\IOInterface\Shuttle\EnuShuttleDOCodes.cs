using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.IOInterface.Shuttle
{
    /// <summary>
    /// Shuttle数字输出(DO)代码枚举
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuShuttleDOCodes
    {
        /// <summary>
        /// 晶圆盒门气缸上升 (SDO1: SEVDO_1)
        /// EV - enable:0
        /// </summary>
        [Description("晶圆盒门气缸上升")]
        SDO1_CassetteDoorCylinderUp = 1,

        /// <summary>
        /// 晶圆盒门气缸下降 (SDO2: SEVDO_2)
        /// EV - enable:0
        /// </summary>
        [Description("晶圆盒门气缸下降")]
        SDO2_CassetteDoorCylinderDown = 2,

        /// <summary>
        /// 晶圆盒门运动使能 (SDO3: SEVDO_3)
        /// EV - enable:0
        /// </summary>
        [Description("晶圆盒门运动使能")]
        SDO3_CassetteDoorMotionEnable = 3,

        /// <summary>
        /// 晶圆盒巢气缸伸出 (SDO4: SEVDO_4)
        /// EV - enable:0
        /// </summary>
        [Description("晶圆盒巢气缸伸出")]
        SDO4_CassetteNestCylinderExtend = 4,

        /// <summary>
        /// 晶圆盒巢气缸收回 (SDO5: SEVDO_5)
        /// EV - enable:0
        /// </summary>
        [Description("晶圆盒巢气缸收回")]
        SDO5_CassetteNestCylinderRetract = 5,

        /// <summary>
        /// Shuttle真空ISO阀门 (SDO6: SEVDO_6)
        /// EV - enable:0
        /// </summary>
        [Description("Shuttle真空ISO阀门")]
        SDO6_ShuttleVacuumIsoValve = 6,

        /// <summary>
        /// Shuttle回填阀门 (SDO7: SEVDO_7)
        /// EV - enable:0
        /// </summary>
        [Description("Shuttle回填阀门")]
        SDO7_ShuttleBackfillValve = 7,

        /// <summary>
        /// XV交叉阀门 (SDO8: SEVDO_8)
        /// EV - enable:0
        /// </summary>
        [Description("XV交叉阀门")]
        SDO8_XvCrossValve = 8,

        /// <summary>
        /// 负载锁排气阀门 (SDO9: SEVDO_9)
        /// EV - enable:0
        /// </summary>
        [Description("负载锁排气阀门")]
        SDO9_LoadlockBleedValve = 9,

        /// <summary>
        /// 负载锁回填阀门 (SDO10: SEVDO_10)
        /// EV - enable:0
        /// </summary>
        [Description("负载锁回填阀门")]
        SDO10_LoadlockBackfillValve = 10,

        /// <summary>
        /// Shuttle组件顺时针旋转 (SDO11: SDO_1)
        /// </summary>
        [Description("Shuttle组件顺时针旋转")]
        SDO11_ShuttleAssyRotationCW = 11,

        /// <summary>
        /// Shuttle组件逆时针旋转 (SDO12: SDO_2)
        /// </summary>
        [Description("Shuttle组件逆时针旋转")]
        SDO12_ShuttleAssyRotationCCW = 12,

        /// <summary>
        /// Shuttle电机上升 (SDO13: SDO_3)
        /// </summary>
        [Description("Shuttle电机上升")]
        SDO13_ShuttleMotorUp = 13,

        /// <summary>
        /// Shuttle电机下降 (SDO14: SDO_4)
        /// </summary>
        [Description("Shuttle电机下降")]
        SDO14_ShuttleMotorDown = 14,

        /// <summary>
        /// Shuttle制动器 (SDO15: SDO_5)
        /// </summary>
        [Description("Shuttle制动器")]
        SDO15_ShuttleBraker = 15,

        /// <summary>
        /// PCWS开关 (SDO16: SDO_15)
        /// </summary>
        [Description("PCWS开关")]
        SDO16_PcwsSwitch = 16,

        /// <summary>
        /// LOAD_READY1 (SDO17: SDO_6)
        /// </summary>
        [Description("LOAD_READY1")]
        SDO17_LoadReady1 = 17,

        /// <summary>
        /// UNLOAD_READY1 (SDO18: SDO_7)
        /// </summary>
        [Description("UNLOAD_READY1")]
        SDO18_UnloadReady1 = 18,

        /// <summary>
        /// LOAD_READY2 (SDO19: SDO_8)
        /// </summary>
        [Description("LOAD_READY2")]
        SDO19_LoadReady2 = 19,

        /// <summary>
        /// UNLOAD_READY2 (SDO20: SDO_9)
        /// </summary>
        [Description("UNLOAD_READY2")]
        SDO20_UnloadReady2 = 20,

        /// <summary>
        /// 紫色蜂鸣器 (SDO21: SDO_10)
        /// </summary>
        [Description("紫色蜂鸣器")]
        SDO21_PurpleBuzzer = 21,

        /// <summary>
        /// 蓝色LED (SDO22: SDO_11)
        /// </summary>
        [Description("蓝色LED")]
        SDO22_BlueLed = 22,

        /// <summary>
        /// 绿色LED (SDO23: SDO_12)
        /// </summary>
        [Description("绿色LED")]
        SDO23_GreenLed = 23,

        /// <summary>
        /// 橙色LED (SDO24: SDO_13)
        /// </summary>
        [Description("橙色LED")]
        SDO24_OrangeLed = 24,

        /// <summary>
        /// 红色LED (SDO25: SDO_14)
        /// </summary>
        [Description("红色LED")]
        SDO25_RedLed = 25
    }
}