using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Zishan.SS200.Cmd.Enums;
using System.Collections.ObjectModel;
using Zishan.SS200.Cmd.Models.IR400;
using System.ComponentModel;
using Wafer = Zishan.SS200.Cmd.Models.IR400.Wafer;
using Zishan.SS200.Cmd.ViewModels;

namespace Zishan.SS200.Cmd.UserControls
{
    /// <summary>
    /// Chamber.xaml 的交互逻辑
    /// </summary>
    public partial class UContainer : UserControl, IDisposable
    {
        [Category("Property")]
        [Description("当前腔体")]
        public BContainer CurCharber
        {
            get { return (BContainer)GetValue(CurCharberProperty); }
            set { SetValue(CurCharberProperty, value); }
        }

        public static readonly DependencyProperty CurCharberProperty =
                    DependencyProperty.Register("CurCharber", typeof(BContainer), typeof(UContainer),
                        new FrameworkPropertyMetadata(default(BContainer), FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        [Category("Property")]
        [Description("最大Wafer数量")]
        public int MaxWafers
        {
            get { return (int)GetValue(MaxWafersProperty); }
            set { SetValue(MaxWafersProperty, value); }
        }

        public static readonly DependencyProperty MaxWafersProperty =
                    DependencyProperty.Register("MaxWafers", typeof(int), typeof(UContainer),
                        new FrameworkPropertyMetadata(25, FrameworkPropertyMetadataOptions.AffectsMeasure | FrameworkPropertyMetadataOptions.AffectsRender));

        public UContainer()
        {
            InitializeComponent();
            this.Loaded += UContainer_Loaded;

            //我这里需要绑定两个TextBlock,因此将这两个TextBlock进行绑定,绑定的是用于中转的变量,后续看的到.
            Binding bindCurCharber = new Binding("CurCharber") { Source = this };
            this.mainGrid.SetBinding(Grid.DataContextProperty, bindCurCharber);

            //this.DataContext = CurCharber;//不能绑定上去？
        }

        private void UContainer_Loaded(object sender, RoutedEventArgs e)
        {
            // 获取父窗口的数据上下文
            var parentDataContext = this.Parent as FrameworkElement;
            while (parentDataContext != null && !(parentDataContext.DataContext is TransferWaferViewModel))
            {
                parentDataContext = parentDataContext.Parent as FrameworkElement;
            }

            // 如果找到了IR400ViewModel，那么设置为当前用户控件的数据上下文
            if (parentDataContext != null)
            {
                this.DataContext = parentDataContext.DataContext;
            }
        }

        #region 已注释掉

        //private void btnLeftWaferRun_Click(object sender, RoutedEventArgs e)
        //{
        //    if (chkLeftWafeCreate.IsChecked == true)
        //    {
        //        //this.CurCharber.LeftWaferAction.HaveWafer = !this.CurCharber.LeftWaferAction.HaveWafer;
        //        //this.CurCharber.LeftWafer.WaferNo++;

        //        //this.CurCharber.LeftWafer.JudgeByHaveWafer();
        //        //chkLeftWafeCreate.IsChecked = false;
        //        var wafers = new List<Wafer>();
        //        Wafer waferGet = null;
        //        for (int i = 0; i < 1; i++)
        //        {
        //            if (string.IsNullOrEmpty(cbLeftAvailableWaferIds.Text))
        //            {
        //                Golbal.CurLeftWafersId++;
        //                wafers.Add(new Wafer(EnuChamberWaferSide.LeftWafers, Golbal.CurLeftWafersId));
        //            }
        //            else
        //            {
        //                var id = Int32.Parse(cbLeftAvailableWaferIds.Text);
        //                waferGet = Golbal.CurLeftAvailableWafers.FirstOrDefault(t => t.WaferNo == id);
        //                wafers.Add(waferGet);
        //            }
        //        }
        //        var blResult = CurCharber.LeftWaferAction.AddWafers(wafers, out string _strMsg);
        //        if (!blResult)
        //        {
        //            //messageInfo.Add(_strMsg);
        //            Console.WriteLine(_strMsg);
        //        }
        //        if (waferGet != null)
        //        {
        //            Golbal.CurLeftAvailableWafers.Remove(waferGet);
        //        }
        //    }
        //    else if (chkLeftWafeDelete.IsChecked == true)
        //    {
        //        //this.CurCharber.LeftWaferAction.HaveWafer = !this.CurCharber.LeftWaferAction.HaveWafer;
        //        //this.CurCharber.LeftWaferAction.JudgeByHaveWafer();

        //        var lstRemoveWafers = this.CurCharber.LeftWaferAction.RemoveWafers(EnuChamberWaferSide.LeftWafers, 1, isFront: false);
        //        Golbal.CurLeftAvailableWafers.AddRange(lstRemoveWafers);

        //        chkLeftWafeDelete.IsChecked = false;
        //    }
        //    else
        //    {
        //        MessageBox.Show("没有选中的动作要执行！");
        //    }
        //}

        //private void btnRightWaferRun_Click(object sender, RoutedEventArgs e)
        //{
        //    if (chkRightWafeCreate.IsChecked == true)
        //    {
        //        //this.CurCharber.LeftWaferAction.HaveWafer = !this.CurCharber.LeftWaferAction.HaveWafer;
        //        //this.CurCharber.LeftWafer.WaferNo++;

        //        //this.CurCharber.LeftWafer.JudgeByHaveWafer();
        //        //chkLeftWafeCreate.IsChecked = false;

        //        var wafers = new List<Wafer>();
        //        Wafer waferGet = null;
        //        for (int i = 0; i < 1; i++)
        //        {
        //            if (string.IsNullOrEmpty(cbRightAvailableWaferIds.Text))
        //            {
        //                Golbal.CurRightWafersId++;
        //                wafers.Add(new Wafer(EnuChamberWaferSide.RightWafers, Golbal.CurRightWafersId));
        //            }
        //            else
        //            {
        //                var id = Int32.Parse(cbRightAvailableWaferIds.Text);
        //                waferGet = Golbal.CurRightAvailableWafers.FirstOrDefault(t => t.WaferNo == id);
        //                wafers.Add(waferGet);
        //            }
        //        }
        //        var blResult = CurCharber.RightWaferAction.AddWafers(wafers, out string _strMsg);
        //        if (!blResult)
        //        {
        //            //messageInfo.Add(_strMsg);
        //            Console.WriteLine(_strMsg);
        //        }
        //        if (waferGet != null)
        //        {
        //            Golbal.CurRightAvailableWafers.Remove(waferGet);
        //        }
        //    }
        //    else if (chkRightWafeDelete.IsChecked == true)
        //    {
        //        //this.CurCharber.LeftWaferAction.HaveWafer = !this.CurCharber.LeftWaferAction.HaveWafer;
        //        //this.CurCharber.LeftWaferAction.JudgeByHaveWafer();

        //        var lstRemoveWafers = this.CurCharber.RightWaferAction.RemoveWafers(EnuChamberWaferSide.RightWafers, 1, isFront: false);
        //        Golbal.CurRightAvailableWafers.AddRange(lstRemoveWafers);
        //        chkRightWafeDelete.IsChecked = false;
        //    }
        //    else
        //    {
        //        MessageBox.Show("没有选中的动作要执行！");
        //    }
        //}

        #endregion 已注释掉

        private void btnLeftWaferRun_Click(object sender, RoutedEventArgs e)
        {
            HandleWaferAction(true, chkLeftWafeCreate, chkLeftWafeDelete, cbLeftAvailableWaferIds);
        }

        private void btnRightWaferRun_Click(object sender, RoutedEventArgs e)
        {
            HandleWaferAction(false, chkRightWafeCreate, chkRightWafeDelete, cbRightAvailableWaferIds);
        }

        private void HandleWaferAction(bool isLeftSide, CheckBox createCheckbox, CheckBox deleteCheckbox, ComboBox availableWaferIds)
        {
            //按SLOT创建可用的Wafer
            if (createCheckbox.IsChecked == true)
            {
                var wafers = new List<Wafer>();
                Wafer waferGet = null;
                for (int i = 0; i < 1; i++)
                {
                    if (string.IsNullOrEmpty(availableWaferIds.Text))
                    {
                        //if (isLeftSide)
                        //{
                        //    Golbal.CurLeftWafersId++;
                        //    wafers.Add(new Wafer(EnuChamberWaferSide.LeftWafers, Golbal.CurLeftWafersId));
                        //}
                        //else
                        //{
                        //    Golbal.CurRightWafersId++;
                        //    wafers.Add(new Wafer(EnuChamberWaferSide.RightWafers, Golbal.CurRightWafersId));
                        //}
                        MessageBox.Show("SLOT 不能为空，请选择！");
                    }
                    else
                    {
                        var id = Int32.Parse(availableWaferIds.Text);
                        waferGet = isLeftSide ? Golbal.CurLeftAvailableWafers.FirstOrDefault(t => t.WaferNo == id) :
                            Golbal.CurRightAvailableWafers.FirstOrDefault(t => t.WaferNo == id);
                        wafers.Add(waferGet);
                    }
                }

                string _strMsg;
                var actionResult = isLeftSide ? CurCharber.LeftWaferAction.AddWafers(wafers, out _strMsg) :
                    CurCharber.RightWaferAction.AddWafers(wafers, out _strMsg);
                if (!actionResult)
                {
                    Console.WriteLine(_strMsg);
                }
                if (waferGet != null)
                {
                    if (isLeftSide)
                    {
                        Golbal.CurLeftAvailableWafers.Remove(waferGet);
                    }
                    else
                    {
                        Golbal.CurRightAvailableWafers.Remove(waferGet);
                    }
                }

                createCheckbox.IsChecked = false;
            }
            //ToDO：按SLOT移除Wafer
            else if (deleteCheckbox.IsChecked == true)
            {
                var actionResult = isLeftSide ? this.CurCharber.LeftWaferAction.RemoveWafers(EnuChamberWaferSide.LeftWafers) :
                    this.CurCharber.RightWaferAction.RemoveWafers(EnuChamberWaferSide.RightWafers);

                //过滤掉不在容量的Wafer,不回收
                actionResult.RemoveAll(t => t.WaferNo > MaxWafers);
                if (isLeftSide)
                {
                    Golbal.CurLeftAvailableWafers.AddRange(actionResult);
                }
                else
                {
                    Golbal.CurRightAvailableWafers.AddRange(actionResult);
                }

                deleteCheckbox.IsChecked = false;
            }
            else
            {
                MessageBox.Show("没有选中的动作要执行！");
            }
        }

        /// <summary>
        /// Wafer双击删除
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void WafersListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            var leftOrRightWafersListBox = sender as ListBox;
            if (leftOrRightWafersListBox != null)
            {
                if (leftOrRightWafersListBox.SelectedItem != null)
                {
                    var messageBoxResult = MessageBox.Show("是否要移除选择项", "移除选择项", MessageBoxButton.YesNo, MessageBoxImage.Question, MessageBoxResult.Yes);

                    if (messageBoxResult == MessageBoxResult.Yes)
                    {
                        Wafer selectedItem = (Wafer)leftOrRightWafersListBox.SelectedItem;
                        var wafers = (ObservableCollection<Wafer>)leftOrRightWafersListBox.ItemsSource;
                        wafers.Remove(selectedItem);
                        if (leftOrRightWafersListBox.Tag != null)
                        {
                            if (leftOrRightWafersListBox.Tag.ToString()?.ToUpper() == "LeftWafer".ToUpper())
                            {
                                Golbal.CurLeftAvailableWafers.Add(selectedItem);
                                CurCharber.LeftWaferAction.CalWaferAction();
                            }
                            else
                            {
                                Golbal.CurRightAvailableWafers.Add(selectedItem);
                                CurCharber.RightWaferAction.CalWaferAction();
                            }
                        }
                        else
                        {
                            MessageBox.Show("Tag 为空");
                        }
                    }
                }
            }

            //leftWafersListBox.Items.RemoveAt(leftWafersListBox.Items.IndexOf(leftWafersListBox.SelectedItem));
        }

        public void Dispose()
        {
        }
    }
}