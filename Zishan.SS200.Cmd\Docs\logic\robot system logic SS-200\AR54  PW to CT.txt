﻿AR54
PW to CT
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			CT slot status review
LSS13 / LSS14
				LSS13=0 LSS14=0
					paddle slot status review
LSS5~LSS8
						LSS5~LSS8=0
							compare ABS(RTF-RP3) with ABS(RTF-RP7)
								ABS(RTF-RP3)≤ABS(RTF-RP7)
									AR3
T-axis smooth to cooling
										AR29
Move Z-axis height smooth to CT put
											AR13
R-axis smooth to cooling extend
												AR23
Move Z-axis height smooth to CT get
													AR64
wafer status smooth paddle to CT
														AR19
R-axis zero position
															command done
								ABS(RTF-RP3)>ABS(RTF-RP7)
									AR7
T-axis nose to cooling
										AR31
Move Z-axis height nose to CT put
											AR17
R-axis nose to cooling extend
												AR27
Move Z-axis height nose to CT get
													AR75
wafer status nose paddle to CT
														AR19
R-axis zero position
															command done
						LSS7 XX=1
or/and
LSS8 XX=1
							AR7
T-axis nose to cooling
								AR31
Move Z-axis height nose to CT put
									AR17
R-axis nose to cooling extend
										AR27
Move Z-axis height nose to CT get
											AR75
wafer status nose paddle to CT
												AR19
R-axis zero position
													command done
						LSS5 XX=1
or/and
LSS6 XX=1
							AR3
T-axis smooth to cooling
								AR29
Move Z-axis height smooth to CT put
									AR13
R-axis smooth to cooling extend
										AR23
Move Z-axis height smooth to CT get
											AR64
wafer status smooth paddle to CT
												AR19
R-axis zero position
													command done
				others status
					RA54 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM