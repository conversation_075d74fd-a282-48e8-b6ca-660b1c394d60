using System;
using System.Windows;
using System.Windows.Media;
using GongSolutions.Wpf.DragDrop;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Models.IR400;
using Zishan.SS200.Cmd.UserControls;
using Zishan.SS200.Cmd.ViewModels;
using Zishan.SS200.Cmd.Views;

namespace Zishan.SS200.Cmd.DragDropHandler
{
    public class WaferDragDropHandler : IDropTarget, IDragSource
    {
        private readonly TransferWaferViewModel _IR400ViewModel;
        private string _strMsg;

        public WaferDragDropHandler(TransferWaferViewModel iR400ViewModel)
        {
            _IR400ViewModel = iR400ViewModel;
        }

        void IDropTarget.DragOver(IDropInfo dropInfo)
        {
            //同项目内禁止拖动调整顺序
            if (dropInfo.VisualTarget == dropInfo.DragInfo.VisualSource)
            {
                dropInfo.NotHandled = dropInfo.VisualTarget == dropInfo.DragInfo.VisualSource;
                return;
            }

            dropInfo.Effects = DragDropEffects.Move;
            dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
            //dropInfo.DropTargetAdorner = typeof(DropTargetHighlightAdorner);
            //dropInfo.DropTargetAdorner = typeof(SimpleCircleAdorner);
        }

        async void IDropTarget.Drop(IDropInfo dropInfo)
        {
            //同项目内禁止拖动调整顺序
            if (dropInfo.VisualTarget == dropInfo.DragInfo.VisualSource)
            {
                dropInfo.NotHandled = dropInfo.VisualTarget == dropInfo.DragInfo.VisualSource;
                return;
            }

            // 获取源列表的父控件
            var sourceParent = dropInfo.DragInfo.VisualSource as FrameworkElement;
            // 获取目标列表的父控件
            var targetParent = dropInfo.VisualTarget as FrameworkElement;

            // 获取源列表父控件的 UContainer 实例
            var sourceUContainer = GetParentOfType<UContainer>(sourceParent);
            // 获取目标列表父控件的 UContainer 实例
            var targetUContainer = GetParentOfType<UContainer>(targetParent);

            //判断目标是否已经有Wafer，有绝不允许拖入
            if (targetUContainer.CurCharber.HasUnfinishedWafer() && targetUContainer.CurCharber.ChamberName != EnuChamberName.Cassette)
            {
                MessageBox.Show($"目标：{targetUContainer.CurCharber.ChamberName}，已经有Wafer，无法再次拖入！！！");
                return;
            }

            var waferSrc = dropInfo.Data as Wafer;

            /*_IR400ViewModel.SelectedFromSlot = _IR400ViewModel.SelectedToSlot = waferSrc.WaferNo;
            _IR400ViewModel.SelectedFromChamber = sourceUContainer.CurCharber;
            _IR400ViewModel.SelectedToChamber = targetUContainer.CurCharber;*/
            //通过界面选择
            _strMsg = $"源头信息: {sourceUContainer.CurCharber.ChamberName} Slot：{waferSrc.WaferNo}\r\n" +
                      $"目标信息: {targetUContainer.CurCharber.ChamberName} Slot：{waferSrc.WaferNo}\r\n";
            MessageBoxResult result = MessageBox.Show($"{_strMsg}\r\n请选择机械臂 Nose端【Y】 或 Smooth端【N】,取消【ESC】", "选择", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);
            /*if (result == MessageBoxResult.Yes)
            {
                _IR400ViewModel.SelectedByArmFetchSide = EnuArmFetchSide.Nose;
            }
            else if (result == MessageBoxResult.No)
            {
                _IR400ViewModel.SelectedByArmFetchSide = EnuArmFetchSide.Smooth;
            }
            else if (result == MessageBoxResult.Cancel)
            {
                return;
            }
            _IR400ViewModel.currentLogIndex = 1;//日志索引从1开始

            switch (_IR400ViewModel.SelectedByArmFetchSide)
            {
                case EnuArmFetchSide.Nose:

                    _IR400ViewModel.ExcuteResult = await _IR400ViewModel.LeftRobotIRArm.TransferWafer(_IR400ViewModel.SelectedFromChamber, waferSrc.WaferNo, _IR400ViewModel.SelectedToChamber, waferSrc.WaferNo, _IR400ViewModel.Cassette, _IR400ViewModel.SelectedByArmFetchSide);//out _strMsg
                    break;

                case EnuArmFetchSide.Smooth:

                    _IR400ViewModel.ExcuteResult = await _IR400ViewModel.RightRobotIRArm.TransferWafer(_IR400ViewModel.SelectedFromChamber, waferSrc.WaferNo, _IR400ViewModel.SelectedToChamber, waferSrc.WaferNo, _IR400ViewModel.Cassette, _IR400ViewModel.SelectedByArmFetchSide);//out _strMsg

                    break;

                case EnuArmFetchSide.Unknow:
                    MessageBox.Show(EnuArmFetchSide.Unknow.ToString());
                    break;
            }

            if (!_IR400ViewModel.ExcuteResult)
            {
                return;
            }*/

            // 获取另外一边Wafer对象
            //Wafer waferAnotherSide;

            //if (waferSrc.ChamberWaferSide == EnuChamberWaferSide.LeftWafers)
            //{
            //    waferAnotherSide = sourceUContainer.CurCharber.RightWaferAction.Wafers.Where(w => w.WaferNo == waferSrc.WaferNo).FirstOrDefault();
            //}
            //else
            //{
            //    waferAnotherSide = sourceUContainer.CurCharber.LeftWaferAction.Wafers.Where(w => w.WaferNo == waferSrc.WaferNo).FirstOrDefault();
            //}

            //// 从源 UContainer 中移除 Wafer 对象
            //sourceUContainer.CurCharber.RemoveWafers(1, EnuWaferRemoveMode.specified, waferSrc.WaferNo);
            //// 将 Wafer 对象添加到目标 UContainer 中
            //List<Wafer> wafers = new List<Wafer>();
            //wafers.Add(waferSrc);
            //if (waferAnotherSide != null)
            //{
            //    wafers.Add(waferAnotherSide);
            //}
            //targetUContainer.CurCharber.AddWafers(wafers, out string msg);

            //OnProcessCustom("CUSTOM");

            // 显示源列表父控件和目标列表父控件的 UContainer 实例
            //_strMsg = $"源列表父控件的 UContainer 实例: {sourceUContainer.CurCharber.ChamberName} Slot：{waferSrc.WaferNo}\n" +
            //         $"目标列表父控件的 UContainer 实例: {targetUContainer.CurCharber.ChamberName} Slot：{waferSrc.WaferNo}";
            //HcGrowlExtensions.Info(_strMsg);

            //// 获取源列表的父控件
            //var sourceParent = dropInfo.DragInfo.VisualSource as FrameworkElement;
            //// 获取目标列表的父控件
            //var targetParent = dropInfo.VisualTarget as FrameworkElement;

            //// 获取源列表父控件的 UContainer 实例
            //var sourceUContainer = VisualTreeHelper.GetParent(sourceParent) as UContainer;
            //// 获取目标列表父控件的 UContainer 实例
            //var targetUContainer = VisualTreeHelper.GetParent(targetParent) as UContainer;

            //// 显示源列表父控件和目标列表父控件的 UContainer 实例
            //MessageBox.Show($"源列表父控件的 UContainer 实例: {sourceUContainer}\n目标列表父控件的 UContainer 实例: {targetUContainer}");

            //// 获取源列表的父控件
            //var sourceParent = dropInfo.DragInfo.VisualSource as FrameworkElement;
            //// 获取目标列表的父控件
            //var targetParent = dropInfo.VisualTarget as FrameworkElement;

            //// 获取源列表父控件的名字
            //var sourceParentName = sourceParent?.Name;

            //// 获取目标列表父控件的名字
            //var targetParentName = targetParent?.Name;

            //// 显示源列表父控件和目标列表父控件的名字
            //MessageBox.Show($"源列表父控件的名字: {sourceParentName}\n目标列表父控件的名字: {targetParentName}");

            //MessageBox.Show("Drop");

            //throw new NotImplementedException();
        }

        public static T GetParentOfType<T>(DependencyObject start) where T : DependencyObject
        {
            var parent = VisualTreeHelper.GetParent(start);

            if (parent == null) return null;

            if (parent is T correctlyTyped)
            {
                return correctlyTyped;
            }

            return GetParentOfType<T>(parent);
        }

        public void StartDrag(IDragInfo dragInfo)
        {
            //MessageBox.Show("StartDrag");
            //throw new NotImplementedException();
        }

        public bool CanStartDrag(IDragInfo dragInfo)
        {
            //MessageBox.Show("CanStartDrag");
            return true;
            //throw new NotImplementedException();
        }

        public void Dropped(IDropInfo dropInfo)
        {
            MessageBox.Show("Dropped");

            // 获取源列表的父控件
            var sourceParent = dropInfo.DragInfo.VisualSource as FrameworkElement;
            // 获取目标列表的父控件
            var targetParent = dropInfo.VisualTarget as FrameworkElement;

            // 获取源列表父控件的名字
            var sourceParentName = sourceParent?.Name;
            // 获取目标列表父控件的名字
            var targetParentName = targetParent?.Name;

            // 显示源列表父控件和目标列表父控件的名字
            MessageBox.Show($"源列表父控件的名字: {sourceParentName}\n目标列表父控件的名字: {targetParentName}");

            MessageBox.Show("Drop");
            //throw new NotImplementedException();
        }

        public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
        {
            MessageBox.Show("DragDropOperationFinished");
            //throw new NotImplementedException();
        }

        public void DragCancelled()
        {
            MessageBox.Show("DragCancelled");
            //throw new NotImplementedException();
        }

        public bool TryCatchOccurredException(Exception exception)
        {
            MessageBox.Show("TryCatchOccurredException");
            //throw new NotImplementedException();
            return true;
        }
    }
}