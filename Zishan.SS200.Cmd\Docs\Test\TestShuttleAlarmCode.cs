using System;
using log4net;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// 测试 Shuttle AlarmCode 功能
    /// </summary>
    public class TestShuttleAlarmCode
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(TestShuttleAlarmCode));

        /// <summary>
        /// 测试 Shuttle 报警代码访问
        /// </summary>
        public static void TestShuttleAlarmAccess()
        {
            try
            {
                _logger.Info("开始测试 Shuttle AlarmCode 访问...");

                // 获取 SS200InterLockMain 实例
                var interlock = SS200InterLockMain.Instance;

                // 测试访问 Shuttle 报警代码
                var shuttleAlarm_SA1 = interlock.AlarmCode.Shuttle.SA1_SystemBusyReject;
                var shuttleAlarm_SA2 = interlock.AlarmCode.Shuttle.SA2_SystemAlarmReject;
                var shuttleAlarm_SA3 = interlock.AlarmCode.Shuttle.SA3_CassetteNestMoveTimeout;

                _logger.Info("=== Shuttle 报警代码测试结果 ===");

                if (shuttleAlarm_SA1 != null)
                {
                    _logger.Info($"SA1 报警代码: {shuttleAlarm_SA1.Code}");
                    _logger.Info($"SA1 英文描述: {shuttleAlarm_SA1.Content}");
                    _logger.Info($"SA1 中文描述: {shuttleAlarm_SA1.ChsContent}");
                }
                else
                {
                    _logger.Warn("SA1 报警代码访问器为空");
                }

                if (shuttleAlarm_SA2 != null)
                {
                    _logger.Info($"SA2 报警代码: {shuttleAlarm_SA2.Code}");
                    _logger.Info($"SA2 英文描述: {shuttleAlarm_SA2.Content}");
                    _logger.Info($"SA2 中文描述: {shuttleAlarm_SA2.ChsContent}");
                }
                else
                {
                    _logger.Warn("SA2 报警代码访问器为空");
                }

                if (shuttleAlarm_SA3 != null)
                {
                    _logger.Info($"SA3 报警代码: {shuttleAlarm_SA3.Code}");
                    _logger.Info($"SA3 英文描述: {shuttleAlarm_SA3.Content}");
                    _logger.Info($"SA3 中文描述: {shuttleAlarm_SA3.ChsContent}");
                }
                else
                {
                    _logger.Warn("SA3 报警代码访问器为空");
                }

                // 测试与原有的 Robot 和 Chamber 报警代码对比
                _logger.Info("=== 对比其他子系统报警代码 ===");

                var robotAlarm = interlock.AlarmCode.Robot.RA1_SystemBusyReject;
                var chamberAlarm = interlock.AlarmCode.ChamberA.PAC1_SystemAbnormalReject;

                _logger.Info($"Robot RA1: {robotAlarm?.Content ?? "null"}");
                _logger.Info($"Chamber PAC1: {chamberAlarm?.Content ?? "null"}");
                _logger.Info($"Shuttle SA1: {shuttleAlarm_SA1?.Content ?? "null"}");

                _logger.Info("Shuttle AlarmCode 测试完成！");
            }
            catch (Exception ex)
            {
                _logger.Error($"测试 Shuttle AlarmCode 时发生错误: {ex.Message}", ex);
            }
        }
    }
}