using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.Command;

/// <summary>
/// Robot 逻辑命令索引枚举
/// </summary>
[TypeConverter(typeof(EnumDescriptionTypeConverter))]
public enum EnuRobotCmd
{
    /// <summary>
    /// T-axis smooth to CHA - T轴Smooth端移动到工艺腔室A
    /// </summary>
    [Description("AR1_T-axis smooth to CHA")]
    AR1 = 0,

    /// <summary>
    /// T-axis smooth to CHB - T轴Smooth端移动到工艺腔室B
    /// </summary>
    [Description("AR2_T-axis smooth to CHB")]
    AR2 = 1,

    /// <summary>
    /// T-axis smooth to cooling chamber - T轴Smooth端移动到冷却腔
    /// </summary>
    [Description("AR3_T-axis smooth to cooling chamber")]
    AR3 = 2,

    /// <summary>
    /// T-axis smooth to cassette - T轴Smooth端移动到晶圆盒
    /// </summary>
    [Description("AR4_T-axis smooth to cassette")]
    AR4 = 3,

    /// <summary>
    /// T-axis nose to CHA - T轴Nose端移动到工艺腔室A
    /// </summary>
    [Description("AR5_T-axis nose to CHA")]
    AR5 = 4,

    /// <summary>
    /// T-axis nose to CHB - T轴Nose端移动到工艺腔室B
    /// </summary>
    [Description("AR6_T-axis nose to CHB")]
    AR6 = 5,

    /// <summary>
    /// T-axis nose to cooling chamber - T轴Nose端移动到冷却腔
    /// </summary>
    [Description("AR7_T-axis nose to cooling chamber")]
    AR7 = 6,

    /// <summary>
    /// T-axis nose to cassette - T轴Nose端移动到晶圆盒
    /// </summary>
    [Description("AR8_T-axis nose to cassette")]
    AR8 = 7,

    /// <summary>
    /// Zero T-axis - T轴归零
    /// </summary>
    [Description("AR9_Zero T-axis")]
    AR9 = 8,

    /// <summary>
    /// T-axis position to xxx - T轴移动到指定位置
    /// </summary>
    [Description("AR10_T-axis position to xxx")]
    AR10 = 9,

    /// <summary>
    /// R-axis smooth CHA extend - R轴Smooth端伸展到工艺腔室A
    /// </summary>
    [Description("AR11_R-axis smooth CHA extend")]
    AR11 = 10,

    /// <summary>
    /// R-axis smooth CHB extend - R轴Smooth端伸展到工艺腔室B
    /// </summary>
    [Description("AR12_R-axis smooth CHB extend")]
    AR12 = 11,

    /// <summary>
    /// R-axis smooth cooling chamber extend - R轴Smooth端伸展到冷却腔
    /// </summary>
    [Description("AR13_R-axis smooth cooling chamber extend")]
    AR13 = 12,

    /// <summary>
    /// R-axis smooth cassette extend - R轴Smooth端伸展到晶圆盒
    /// </summary>
    [Description("AR14_R-axis smooth cassette extend")]
    AR14 = 13,

    /// <summary>
    /// R-axis nose CHA extend - R轴Nose端伸展到工艺腔室A
    /// </summary>
    [Description("AR15_R-axis nose CHA extend")]
    AR15 = 14,

    /// <summary>
    /// R-axis nose CHB extend - R轴Nose端伸展到工艺腔室B
    /// </summary>
    [Description("AR16_R-axis nose CHB extend")]
    AR16 = 15,

    /// <summary>
    /// R-axis nose cooling chamber extend - R轴Nose端伸展到冷却腔
    /// </summary>
    [Description("AR17_R-axis nose cooling chamber extend")]
    AR17 = 16,

    /// <summary>
    /// R-axis nose cassette extend - R轴Nose端伸展到晶圆盒
    /// </summary>
    [Description("AR18_R-axis nose cassette extend")]
    AR18 = 17,

    /// <summary>
    /// R-axis zero position - R轴归零
    /// </summary>
    [Description("AR19_R-axis zero position")]
    AR19 = 18,

    /// <summary>
    /// R-axis position to xxx - R轴移动到指定位置
    /// </summary>
    [Description("AR20_R-axis position to xxx")]
    AR20 = 19,

    /// <summary>
    /// Move Z-axis height at smooth to CHA get - Z轴在Smooth端位置取工艺腔室A晶圆高度
    /// </summary>
    [Description("AR21_Move Z-axis height at smooth to CHA get")]
    AR21 = 20,

    /// <summary>
    /// Move Z-axis height at smooth to CHB get - Z轴在Smooth端位置取工艺腔室B晶圆高度
    /// </summary>
    [Description("AR22_Move Z-axis height at smooth to CHB get")]
    AR22 = 21,

    /// <summary>
    /// Move Z-axis height at smooth to CT get - Z轴在Smooth端位置取冷却TOP晶圆高度
    /// </summary>
    [Description("AR23_Move Z-axis height at smooth to CT get")]
    AR23 = 22,

    /// <summary>
    /// Move Z-axis height at smooth to CB get - Z轴在Smooth端位置取冷却BOTTOM晶圆高度
    /// </summary>
    [Description("AR24_Move Z-axis height at smooth to CB get")]
    AR24 = 23,

    /// <summary>
    /// Move Z-axis height at nose to CHA get - Z轴在Nose端位置取工艺腔室A晶圆高度
    /// </summary>
    [Description("AR25_Move Z-axis height at nose to CHA get")]
    AR25 = 24,

    /// <summary>
    /// Move Z-axis height at nose to CHB get - Z轴在Nose端位置取工艺腔室B晶圆高度
    /// </summary>
    [Description("AR26_Move Z-axis height at nose to CHB get")]
    AR26 = 25,

    /// <summary>
    /// Move Z-axis height at nose to CT get - Z轴在Nose端位置取冷却TOP晶圆高度
    /// </summary>
    [Description("AR27_Move Z-axis height at nose to CT get")]
    AR27 = 26,

    /// <summary>
    /// Move Z-axis height at nose to CB get - Z轴在Nose端位置取冷却BOTTOM晶圆高度
    /// </summary>
    [Description("AR28_Move Z-axis height at nose to CB get")]
    AR28 = 27,

    /// <summary>
    /// Move Z-axis height at smooth to CT put - Z轴在Smooth端位置放冷却TOP晶圆
    /// </summary>
    [Description("AR29_Move Z-axis height at smooth to CT put")]
    AR29 = 28,

    /// <summary>
    /// Move Z-axis height at smooth to CB put - Z轴在Smooth端位置放冷却BOTTOM晶圆
    /// </summary>
    [Description("AR30_Move Z-axis height at smooth to CB put")]
    AR30 = 29,

    /// <summary>
    /// Move Z-axis height at nose to CT put - Z轴在Nose端位置放冷却TOP晶圆
    /// </summary>
    [Description("AR31_Move Z-axis height at nose to CT put")]
    AR31 = 30,

    /// <summary>
    /// Move Z-axis height at nose to CB put - Z轴在Nose端位置放冷却BOTTOM晶圆
    /// </summary>
    [Description("AR32_Move Z-axis height at nose to CB put")]
    AR32 = 31,

    /// <summary>
    /// Move Z-axis zero position - Z轴归零
    /// </summary>
    [Description("AR33_Move Z-axis zero position")]
    AR33 = 32,

    /// <summary>
    /// Move Z-axis height smooth get slot xx - Z轴Smooth端位置取插槽
    /// </summary>
    [Description("AR34_Move Z-axis height smooth get slot xx")]
    AR34 = 33,

    /// <summary>
    /// Move Z-axis height nose get slot xx - Z轴Nose端位置取插槽
    /// </summary>
    [Description("AR35_Move Z-axis height nose get slot xx")]
    AR35 = 34,

    /// <summary>
    /// Move Z-axis height smooth put slot xx - Z轴Smooth端位置放插槽
    /// </summary>
    [Description("AR36_Move Z-axis height smooth put slot xx")]
    AR36 = 35,

    /// <summary>
    /// Move Z-axis height nose put slot xx - Z轴Nose端位置放插槽
    /// </summary>
    [Description("AR37_Move Z-axis height nose put slot xx")]
    AR37 = 36,

    /// <summary>
    /// Move Z-axis height to pin search - Z轴到插销搜索位置
    /// </summary>
    [Description("AR38_Move Z-axis height to pin search")]
    AR38 = 37,

    /// <summary>
    /// Z-axis position to xxx - Z轴移动到指定位置
    /// </summary>
    [Description("AR39_Z-axis position to xxx")]
    AR39 = 38,

    /// <summary>
    /// Z-axis cassette position delta - 晶圆盒位置增量调整
    /// </summary>
    [Description("AR40_Z-axis cassette position delta")]
    AR40 = 39,

    /// <summary>
    /// Z-axis chamber position delta - 腔室位置增量调整
    /// </summary>
    [Description("AR41_Z-axis chamber position delta")]
    AR41 = 40,

    /// <summary>
    /// Pin search - 插销搜索
    /// </summary>
    [Description("AR42_Pin search")]
    AR42 = 41,

    /// <summary>
    /// GW XX - 从晶圆盒取晶圆
    /// </summary>
    [Description("AR43_Get wafer from cassette")]
    AR43 = 42,

    /// <summary>
    /// GW XX to smooth paddle - 从Smooth端位置取晶圆到挡板
    /// </summary>
    [Description("AR44_Get wafer to smooth paddle")]
    AR44 = 43,

    /// <summary>
    /// GW XX to nose paddle - 从Nose端位置取晶圆到挡板
    /// </summary>
    [Description("AR45_Get wafer to nose paddle")]
    AR45 = 44,

    /// <summary>
    /// PW XX from smooth paddle - 从Smooth端挡板放晶圆
    /// </summary>
    [Description("AR46_Put wafer from smooth paddle")]
    AR46 = 45,

    /// <summary>
    /// PW XX from nose paddle - 从Nose端挡板放晶圆
    /// </summary>
    [Description("AR47_Put wafer from nose paddle")]
    AR47 = 46,

    /// <summary>
    /// PW XX - 放晶圆
    /// </summary>
    [Description("AR48_Put wafer")]
    AR48 = 47,

    /// <summary>
    /// GW from CHA - 从工艺腔室A取晶圆
    /// </summary>
    [Description("AR49_Get wafer from chamber A")]
    AR49 = 48,

    /// <summary>
    /// PW from CHA - 放晶圆到工艺腔室A
    /// </summary>
    [Description("AR50_Put wafer to chamber A")]
    AR50 = 49,

    /// <summary>
    /// GW from CHB - 从工艺腔室B取晶圆
    /// </summary>
    [Description("AR51_Get wafer from chamber B")]
    AR51 = 50,

    /// <summary>
    /// PW from CHB - 放晶圆到工艺腔室B
    /// </summary>
    [Description("AR52_Put wafer to chamber B")]
    AR52 = 51,

    /// <summary>
    /// GW from CT - 从冷却TOP取晶圆
    /// </summary>
    [Description("AR53_Get wafer from cooling top")]
    AR53 = 52,

    /// <summary>
    /// PW from CT - 放晶圆到冷却TOP
    /// </summary>
    [Description("AR54_Put wafer to cooling top")]
    AR54 = 53,

    /// <summary>
    /// GW from CB - 从冷却BOTTOM取晶圆
    /// </summary>
    [Description("AR55_Get wafer from cooling bottom")]
    AR55 = 54,

    /// <summary>
    /// PW from CB - 放晶圆到冷却BOTTOM
    /// </summary>
    [Description("AR56_Put wafer to cooling bottom")]
    AR56 = 55,

    /// <summary>
    /// Wafer status exchange cassette to smooth paddle slot xx - 晶圆盒到Smooth端挡板状态交换
    /// </summary>
    [Description("AR57_Wafer status exchange cassette to smooth paddle")]
    AR57 = 56,

    /// <summary>
    /// Wafer status exchange smooth paddle to cassette slot xx - Smooth端挡板到晶圆盒状态交换
    /// </summary>
    [Description("AR58_Wafer status exchange smooth paddle to cassette")]
    AR58 = 57,

    /// <summary>
    /// Spare - 备用
    /// </summary>
    [Description("AR59_Spare")]
    AR59 = 58,

    /// <summary>
    /// Wafer status exchange smooth paddle to chamber A slot xx - Smooth端挡板到工艺腔室A状态交换
    /// </summary>
    [Description("AR60_Wafer status exchange smooth paddle to chamber A")]
    AR60 = 59,

    /// <summary>
    /// Wafer status exchange chamber A to smooth paddle slot xx - 工艺腔室A到Smooth端挡板状态交换
    /// </summary>
    [Description("AR61_Wafer status exchange chamber A to smooth paddle")]
    AR61 = 60,

    /// <summary>
    /// Wafer status exchange smooth paddle to chamber B slot xx - Smooth端挡板到工艺腔室B状态交换
    /// </summary>
    [Description("AR62_Wafer status exchange smooth paddle to chamber B")]
    AR62 = 61,

    /// <summary>
    /// Wafer status exchange chamber B to smooth paddle slot xx - 工艺腔室B到Smooth端挡板状态交换
    /// </summary>
    [Description("AR63_Wafer status exchange chamber B to smooth paddle")]
    AR63 = 62,

    /// <summary>
    /// Wafer status exchange smooth paddle to CT slot xx - Smooth端挡板到冷却TOP状态交换
    /// </summary>
    [Description("AR64_Wafer status exchange smooth paddle to cooling top")]
    AR64 = 63,

    /// <summary>
    /// Wafer status exchange CT to smooth paddle slot xx - 冷却TOP到Smooth端挡板状态交换
    /// </summary>
    [Description("AR65_Wafer status exchange cooling top to smooth paddle")]
    AR65 = 64,

    /// <summary>
    /// Wafer status exchange smooth paddle to CB slot xx - Smooth端挡板到冷却BOTTOM状态交换
    /// </summary>
    [Description("AR66_Wafer status exchange smooth paddle to cooling bottom")]
    AR66 = 65,

    /// <summary>
    /// Wafer status exchange CB to smooth paddle slot xx - 冷却BOTTOM到Smooth端挡板状态交换
    /// </summary>
    [Description("AR67_Wafer status exchange cooling bottom to smooth paddle")]
    AR67 = 66,

    /// <summary>
    /// Wafer status exchange cassette to nose paddle slot xx - 晶圆盒到Nose端挡板状态交换
    /// </summary>
    [Description("AR68_Wafer status exchange cassette to nose paddle")]
    AR68 = 67,

    /// <summary>
    /// Wafer status exchange nose paddle to cassette slot xx - Nose端挡板到晶圆盒状态交换
    /// </summary>
    [Description("AR69_Wafer status exchange nose paddle to cassette")]
    AR69 = 68,

    /// <summary>
    /// Spare - 备用
    /// </summary>
    [Description("AR70_Spare")]
    AR70 = 69,

    /// <summary>
    /// Wafer status exchange nose paddle to chamber A slot xx - Nose端挡板到工艺腔室A状态交换
    /// </summary>
    [Description("AR71_Wafer status exchange nose paddle to chamber A")]
    AR71 = 70,

    /// <summary>
    /// Wafer status exchange chamber A to nose paddle slot xx - 工艺腔室A到Nose端挡板状态交换
    /// </summary>
    [Description("AR72_Wafer status exchange chamber A to nose paddle")]
    AR72 = 71,

    /// <summary>
    /// Wafer status exchange nose paddle to chamber B slot xx - Nose端挡板到工艺腔室B状态交换
    /// </summary>
    [Description("AR73_Wafer status exchange nose paddle to chamber B")]
    AR73 = 72,

    /// <summary>
    /// Wafer status exchange chamber B to nose paddle slot xx - 工艺腔室B到Nose端挡板状态交换
    /// </summary>
    [Description("AR74_Wafer status exchange chamber B to nose paddle")]
    AR74 = 73,

    /// <summary>
    /// Wafer status exchange nose paddle to CT slot xx - Nose端挡板到冷却TOP状态交换
    /// </summary>
    [Description("AR75_Wafer status exchange nose paddle to cooling top")]
    AR75 = 74,

    /// <summary>
    /// Wafer status exchange CT to nose paddle slot xx - 冷却TOP到Nose端挡板状态交换
    /// </summary>
    [Description("AR76_Wafer status exchange cooling top to nose paddle")]
    AR76 = 75,

    /// <summary>
    /// Wafer status exchange nose paddle to CB slot xx - Nose端挡板到冷却BOTTOM状态交换
    /// </summary>
    [Description("AR77_Wafer status exchange nose paddle to cooling bottom")]
    AR77 = 76,

    /// <summary>
    /// Wafer status exchange CB to nose paddle slot xx - 冷却BOTTOM到Nose端挡板状态交换
    /// </summary>
    [Description("AR78_Wafer status exchange cooling bottom to nose paddle")]
    AR78 = 77,

    /// <summary>
    /// Wafer status compare cassette to smooth paddle confirm - 晶圆盒与Smooth端挡板状态比较
    /// </summary>
    [Description("AR79_Wafer status compare cassette to smooth paddle")]
    AR79 = 78,

    /// <summary>
    /// Wafer status compare smooth paddle confirm to cassette - Smooth端挡板与晶圆盒状态比较
    /// </summary>
    [Description("AR80_Wafer status compare smooth paddle to cassette")]
    AR80 = 79,

    /// <summary>
    /// Wafer status compare cassette to nose paddle confirm - 晶圆盒与Nose端挡板状态比较
    /// </summary>
    [Description("AR81_Wafer status compare cassette to nose paddle confirm")]
    AR81 = 80,

    /// <summary>
    /// Wafer status compare nose paddle confirm to cassette - Nose端挡板与晶圆盒状态比较
    /// </summary>
    [Description("AR82_Wafer status compare nose paddle confirm to cassette")]
    AR82 = 81,

    /// <summary>
    /// GW CHA to smooth paddle - 工艺腔室A到Smooth端挡板路径
    /// </summary>
    [Description("AR83_GW CHA to smooth paddle")]
    AR83 = 82,

    /// <summary>
    /// GW CHA to nose paddle - 工艺腔室A到Nose端挡板路径
    /// </summary>
    [Description("AR84_GW CHA to nose paddle")]
    AR84 = 83,

    /// <summary>
    /// GW CHB to smooth paddle - 工艺腔室B到Smooth端挡板路径
    /// </summary>
    [Description("AR85_GW CHB to smooth paddle")]
    AR85 = 84,

    /// <summary>
    /// GW CHB to nose paddle - 工艺腔室B到Nose端挡板路径
    /// </summary>
    [Description("AR86_GW CHB to nose paddle")]
    AR86 = 85,

    /// <summary>
    /// PW CHA from smooth paddle - Smooth端挡板到工艺腔室A路径
    /// </summary>
    [Description("AR87_PW CHA from smooth paddle")]
    AR87 = 86,

    /// <summary>
    /// PW CHA from nose paddle - Nose端挡板到工艺腔室A路径
    /// </summary>
    [Description("AR88_PW CHA from nose paddle")]
    AR88 = 87,

    /// <summary>
    /// PW CHB from smooth paddle - Smooth端挡板到工艺腔室B路径
    /// </summary>
    [Description("AR89_PW CHB from smooth paddle")]
    AR89 = 88,

    /// <summary>
    /// PW CHB from nose paddle - Nose端挡板到工艺腔室B路径
    /// </summary>
    [Description("AR90_PW CHB from nose paddle")]
    AR90 = 89,

    /// <summary>
    /// GW CT to smooth paddle - 冷却TOP到Smooth端挡板路径
    /// </summary>
    [Description("AR91_GW CT to smooth paddle")]
    AR91 = 90,

    /// <summary>
    /// GW CT to nose paddle - 冷却TOP到Nose端挡板路径
    /// </summary>
    [Description("AR92_GW CT to nose paddle")]
    AR92 = 91,

    /// <summary>
    /// GW CB to smooth paddle - 冷却BOTTOM到Smooth端挡板路径
    /// </summary>
    [Description("AR93_GW CB to smooth paddle")]
    AR93 = 92,

    /// <summary>
    /// GW CB to nose paddle - 冷却BOTTOM到Nose端挡板路径
    /// </summary>
    [Description("AR94_GW CB to nose paddle")]
    AR94 = 93,

    /// <summary>
    /// PW CT from smooth paddle - Smooth端挡板到冷却TOP路径
    /// </summary>
    [Description("AR95_PW CT from smooth paddle")]
    AR95 = 94,

    /// <summary>
    /// PW CT from nose paddle - Nose端挡板到冷却TOP路径
    /// </summary>
    [Description("AR96_PW CT from nose paddle")]
    AR96 = 95,

    /// <summary>
    /// PW CB from smooth paddle - Smooth端挡板到冷却BOTTOM路径
    /// </summary>
    [Description("AR97_PW CB from smooth paddle")]
    AR97 = 96,

    /// <summary>
    /// PW CB from nose paddle - Nose端挡板到冷却BOTTOM路径
    /// </summary>
    [Description("AR98_PW CB from nose paddle")]
    AR98 = 97,

    /// <summary>
    /// Composite command test (calls AR1 and other commands) - 复合命令测试
    /// </summary>
    [Description("ARCompositeTest_Composite command test")]
    ARCompositeTest = 98
}