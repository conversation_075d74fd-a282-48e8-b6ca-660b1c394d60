# 性能日志分析脚本
# 用于分析 StopwatchHelper 生成的性能日志，快速识别性能问题

param(
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "Logs\perf.log",
    
    [Parameter(Mandatory=$false)]
    [int]$TopCount = 10,
    
    [Parameter(Mandatory=$false)]
    [double]$SlowThreshold = 1000,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "Logs\performance_analysis.txt"
)

# 检查日志文件是否存在
if (-not (Test-Path $LogPath)) {
    Write-Host "错误: 日志文件不存在 - $LogPath" -ForegroundColor Red
    exit 1
}

Write-Host "=== 性能日志分析开始 ===" -ForegroundColor Green
Write-Host "日志文件: $LogPath"
Write-Host "分析时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Host ""

# 读取日志内容
$logContent = Get-Content $LogPath -Encoding UTF8

# 解析性能记录
$performanceRecords = @()
$warningRecords = @()

foreach ($line in $logContent) {
    # 解析性能结果日志
    if ($line -match '\[性能计时结果\]\s+操作:\s*(.+?)\s*\|\s*耗时:\s*([\d.]+)ms.*?\|\s*位置:\s*(.+)') {
        $performanceRecords += [PSCustomObject]@{
            Operation = $matches[1].Trim()
            ElapsedMs = [double]$matches[2]
            Location = $matches[3].Trim()
            IsWarning = $false
            Line = $line
        }
    }
    # 解析性能警告日志
    elseif ($line -match '\[性能警告\].*?操作:\s*(.+?)\s*\|\s*耗时:\s*([\d.]+)ms.*?\|\s*位置:\s*(.+?)\s*\|\s*超过阈值:\s*([\d.]+)ms') {
        $warningRecords += [PSCustomObject]@{
            Operation = $matches[1].Trim()
            ElapsedMs = [double]$matches[2]
            Location = $matches[3].Trim()
            ThresholdMs = [double]$matches[4]
            IsWarning = $true
            Line = $line
        }
        
        # 同时添加到性能记录中
        $performanceRecords += [PSCustomObject]@{
            Operation = $matches[1].Trim()
            ElapsedMs = [double]$matches[2]
            Location = $matches[3].Trim()
            IsWarning = $true
            Line = $line
        }
    }
}

Write-Host "解析结果:" -ForegroundColor Yellow
Write-Host "- 总性能记录数: $($performanceRecords.Count)"
Write-Host "- 性能警告记录数: $($warningRecords.Count)"
Write-Host ""

if ($performanceRecords.Count -eq 0) {
    Write-Host "没有找到性能记录，分析结束。" -ForegroundColor Yellow
    exit 0
}

# 开始输出分析结果
$output = @()
$output += "# 性能日志分析报告"
$output += "生成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
$output += "日志文件: $LogPath"
$output += "总记录数: $($performanceRecords.Count)"
$output += "警告记录数: $($warningRecords.Count)"
$output += ""

# 1. 按操作分组统计
Write-Host "1. 按操作分组统计 (前 $TopCount 个最慢操作):" -ForegroundColor Cyan

$operationStats = $performanceRecords | Group-Object Operation | ForEach-Object {
    $group = $_.Group
    [PSCustomObject]@{
        Operation = $_.Name
        Count = $_.Count
        AverageMs = [math]::Round(($group | Measure-Object ElapsedMs -Average).Average, 2)
        MinMs = [math]::Round(($group | Measure-Object ElapsedMs -Minimum).Minimum, 2)
        MaxMs = [math]::Round(($group | Measure-Object ElapsedMs -Maximum).Maximum, 2)
        TotalMs = [math]::Round(($group | Measure-Object ElapsedMs -Sum).Sum, 2)
        WarningCount = ($group | Where-Object IsWarning).Count
        WarningRate = if ($_.Count -gt 0) { [math]::Round((($group | Where-Object IsWarning).Count / $_.Count) * 100, 1) } else { 0 }
    }
} | Sort-Object AverageMs -Descending | Select-Object -First $TopCount

$operationStats | Format-Table -AutoSize
$output += "## 操作性能统计 (按平均耗时排序)"
$output += "| 操作名称 | 调用次数 | 平均耗时(ms) | 最小耗时(ms) | 最大耗时(ms) | 总耗时(ms) | 警告次数 | 警告率(%) |"
$output += "|----------|----------|--------------|--------------|--------------|------------|----------|-----------|"
foreach ($stat in $operationStats) {
    $output += "| $($stat.Operation) | $($stat.Count) | $($stat.AverageMs) | $($stat.MinMs) | $($stat.MaxMs) | $($stat.TotalMs) | $($stat.WarningCount) | $($stat.WarningRate) |"
}
$output += ""

# 2. 最慢的单次操作
Write-Host "`n2. 最慢的单次操作 (前 $TopCount 个):" -ForegroundColor Cyan

$slowestOperations = $performanceRecords | Sort-Object ElapsedMs -Descending | Select-Object -First $TopCount
$slowestOperations | Select-Object Operation, ElapsedMs, Location | Format-Table -AutoSize

$output += "## 最慢的单次操作"
$output += "| 操作名称 | 耗时(ms) | 位置 |"
$output += "|----------|----------|------|"
foreach ($op in $slowestOperations) {
    $output += "| $($op.Operation) | $($op.ElapsedMs) | $($op.Location) |"
}
$output += ""

# 3. 超过阈值的慢操作
Write-Host "`n3. 超过 ${SlowThreshold}ms 的慢操作:" -ForegroundColor Cyan

$slowOperations = $performanceRecords | Where-Object { $_.ElapsedMs -gt $SlowThreshold } | Sort-Object ElapsedMs -Descending
if ($slowOperations.Count -gt 0) {
    $slowOperations | Select-Object Operation, ElapsedMs, Location | Format-Table -AutoSize
    
    $output += "## 超过 ${SlowThreshold}ms 的慢操作"
    $output += "| 操作名称 | 耗时(ms) | 位置 |"
    $output += "|----------|----------|------|"
    foreach ($op in $slowOperations) {
        $output += "| $($op.Operation) | $($op.ElapsedMs) | $($op.Location) |"
    }
} else {
    Write-Host "没有发现超过 ${SlowThreshold}ms 的操作。" -ForegroundColor Green
    $output += "## 超过 ${SlowThreshold}ms 的慢操作"
    $output += "没有发现超过 ${SlowThreshold}ms 的操作。"
}
$output += ""

# 4. 性能警告分析
if ($warningRecords.Count -gt 0) {
    Write-Host "`n4. 性能警告分析:" -ForegroundColor Cyan
    
    $warningStats = $warningRecords | Group-Object Operation | ForEach-Object {
        $group = $_.Group
        [PSCustomObject]@{
            Operation = $_.Name
            WarningCount = $_.Count
            AverageElapsedMs = [math]::Round(($group | Measure-Object ElapsedMs -Average).Average, 2)
            MaxElapsedMs = [math]::Round(($group | Measure-Object ElapsedMs -Maximum).Maximum, 2)
            AverageThresholdMs = [math]::Round(($group | Measure-Object ThresholdMs -Average).Average, 2)
        }
    } | Sort-Object WarningCount -Descending
    
    $warningStats | Format-Table -AutoSize
    
    $output += "## 性能警告统计"
    $output += "| 操作名称 | 警告次数 | 平均耗时(ms) | 最大耗时(ms) | 平均阈值(ms) |"
    $output += "|----------|----------|--------------|--------------|--------------|"
    foreach ($stat in $warningStats) {
        $output += "| $($stat.Operation) | $($stat.WarningCount) | $($stat.AverageElapsedMs) | $($stat.MaxElapsedMs) | $($stat.AverageThresholdMs) |"
    }
} else {
    Write-Host "`n4. 性能警告分析: 没有发现性能警告。" -ForegroundColor Green
    $output += "## 性能警告统计"
    $output += "没有发现性能警告。"
}
$output += ""

# 5. 按位置分组分析
Write-Host "`n5. 按调用位置分组分析 (前 $TopCount 个):" -ForegroundColor Cyan

$locationStats = $performanceRecords | Group-Object Location | ForEach-Object {
    $group = $_.Group
    [PSCustomObject]@{
        Location = $_.Name
        Count = $_.Count
        AverageMs = [math]::Round(($group | Measure-Object ElapsedMs -Average).Average, 2)
        MaxMs = [math]::Round(($group | Measure-Object ElapsedMs -Maximum).Maximum, 2)
        Operations = ($group | Group-Object Operation | ForEach-Object { $_.Name }) -join ", "
    }
} | Sort-Object AverageMs -Descending | Select-Object -First $TopCount

$locationStats | Format-Table -AutoSize -Wrap

$output += "## 按调用位置统计"
$output += "| 位置 | 调用次数 | 平均耗时(ms) | 最大耗时(ms) | 相关操作 |"
$output += "|------|----------|--------------|--------------|----------|"
foreach ($stat in $locationStats) {
    $output += "| $($stat.Location) | $($stat.Count) | $($stat.AverageMs) | $($stat.MaxMs) | $($stat.Operations) |"
}
$output += ""

# 6. 时间趋势分析（简化版）
Write-Host "`n6. 性能趋势分析:" -ForegroundColor Cyan

# 提取时间戳并分析趋势
$timePattern = '\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}'
$timestampRecords = @()

foreach ($record in $performanceRecords) {
    if ($record.Line -match $timePattern) {
        try {
            $timestamp = [DateTime]::ParseExact($matches[0], "yyyy-MM-dd HH:mm:ss.fff", $null)
            $timestampRecords += [PSCustomObject]@{
                Timestamp = $timestamp
                Operation = $record.Operation
                ElapsedMs = $record.ElapsedMs
            }
        } catch {
            # 忽略解析失败的记录
        }
    }
}

if ($timestampRecords.Count -gt 0) {
    $timeRange = $timestampRecords | Measure-Object Timestamp -Minimum -Maximum
    $startTime = $timeRange.Minimum
    $endTime = $timeRange.Maximum
    $duration = $endTime - $startTime
    
    Write-Host "时间范围: $($startTime.ToString('yyyy-MM-dd HH:mm:ss')) ~ $($endTime.ToString('yyyy-MM-dd HH:mm:ss'))"
    Write-Host "总时长: $($duration.TotalHours.ToString('F2')) 小时"
    
    # 按小时分组统计
    $hourlyStats = $timestampRecords | Group-Object { $_.Timestamp.ToString("yyyy-MM-dd HH") } | ForEach-Object {
        $group = $_.Group
        [PSCustomObject]@{
            Hour = $_.Name
            Count = $_.Count
            AverageMs = [math]::Round(($group | Measure-Object ElapsedMs -Average).Average, 2)
            MaxMs = [math]::Round(($group | Measure-Object ElapsedMs -Maximum).Maximum, 2)
        }
    } | Sort-Object Hour
    
    if ($hourlyStats.Count -le 24) {
        $hourlyStats | Format-Table -AutoSize
    } else {
        Write-Host "时间跨度较长，显示前10个和后10个小时的统计："
        ($hourlyStats | Select-Object -First 10) + ($hourlyStats | Select-Object -Last 10) | Format-Table -AutoSize
    }
    
    $output += "## 时间趋势分析"
    $output += "时间范围: $($startTime.ToString('yyyy-MM-dd HH:mm:ss')) ~ $($endTime.ToString('yyyy-MM-dd HH:mm:ss'))"
    $output += "总时长: $($duration.TotalHours.ToString('F2')) 小时"
    $output += ""
    $output += "### 按小时统计"
    $output += "| 小时 | 操作次数 | 平均耗时(ms) | 最大耗时(ms) |"
    $output += "|------|----------|--------------|--------------|"
    foreach ($stat in $hourlyStats) {
        $output += "| $($stat.Hour) | $($stat.Count) | $($stat.AverageMs) | $($stat.MaxMs) |"
    }
} else {
    Write-Host "无法解析时间戳，跳过趋势分析。"
    $output += "## 时间趋势分析"
    $output += "无法解析时间戳，跳过趋势分析。"
}

# 7. 保存分析结果到文件
Write-Host "`n7. 保存分析结果到文件: $OutputPath" -ForegroundColor Cyan

try {
    $output | Out-File -FilePath $OutputPath -Encoding UTF8
    Write-Host "分析结果已保存到: $OutputPath" -ForegroundColor Green
} catch {
    Write-Host "保存文件失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 性能日志分析完成 ===" -ForegroundColor Green

# 8. 生成建议
Write-Host "`n8. 性能优化建议:" -ForegroundColor Magenta

$suggestions = @()

# 检查是否有超慢操作
$verySlow = $performanceRecords | Where-Object { $_.ElapsedMs -gt 5000 }
if ($verySlow.Count -gt 0) {
    $suggestions += "发现 $($verySlow.Count) 个超过5秒的极慢操作，建议优先优化"
}

# 检查警告率
$highWarningOps = $operationStats | Where-Object { $_.WarningRate -gt 50 }
if ($highWarningOps.Count -gt 0) {
    $suggestions += "发现 $($highWarningOps.Count) 个操作的警告率超过50%，建议调整阈值或优化性能"
}

# 检查频繁调用的慢操作
$frequentSlowOps = $operationStats | Where-Object { $_.Count -gt 10 -and $_.AverageMs -gt 1000 }
if ($frequentSlowOps.Count -gt 0) {
    $suggestions += "发现 $($frequentSlowOps.Count) 个频繁调用且较慢的操作，优化这些操作将显著提升整体性能"
}

if ($suggestions.Count -gt 0) {
    foreach ($suggestion in $suggestions) {
        Write-Host "- $suggestion" -ForegroundColor Yellow
    }
} else {
    Write-Host "- 整体性能表现良好，未发现明显的性能问题" -ForegroundColor Green
}

Write-Host "`n分析完成！请查看详细报告: $OutputPath" -ForegroundColor Cyan
