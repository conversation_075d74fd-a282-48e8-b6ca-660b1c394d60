{"BatchSequences": [{"Name": "Shuttle初始化", "Description": "执行Shuttle的标准初始化过程", "LoopCount": 1, "IsInfiniteLoop": false, "LoopDelayMs": 2000, "Commands": [{"DeviceName": "Shuttle", "CommandName": "S1 SD", "DelayAfterExecution": 2000, "DynamicParameters": {}}, {"DeviceName": "Shuttle", "CommandName": "S2 SU", "DelayAfterExecution": 3000, "DynamicParameters": {}}]}, {"Name": "Robot测试", "Description": "执行Robot的基本测试", "LoopCount": 1, "IsInfiniteLoop": false, "LoopDelayMs": 2000, "Commands": [{"DeviceName": "Robot", "CommandName": "R1 PTS", "DelayAfterExecution": 2000, "DynamicParameters": {}}, {"DeviceName": "Robot", "CommandName": "R2 PTR", "DelayAfterExecution": 2000, "DynamicParameters": {}}]}, {"Name": "循环测试示例", "Description": "循环执行Shuttle和Robot命令5次", "LoopCount": 5, "IsInfiniteLoop": false, "LoopDelayMs": 3000, "Commands": [{"DeviceName": "Shuttle", "CommandName": "S1 SD", "DelayAfterExecution": 1000, "DynamicParameters": {}}, {"DeviceName": "Robot", "CommandName": "R1 PTS", "DelayAfterExecution": 1000, "DynamicParameters": {}}]}, {"Name": "无限循环测试", "Description": "无限循环执行，直到手动停止", "LoopCount": 0, "IsInfiniteLoop": true, "LoopDelayMs": 5000, "Commands": [{"DeviceName": "Robot", "CommandName": "R1 PTS", "DelayAfterExecution": 1000, "DynamicParameters": {}}, {"DeviceName": "Shuttle", "CommandName": "S1 SD", "DelayAfterExecution": 1000, "DynamicParameters": {}}]}]}