# RobotStatusPanelViewModel 轴位置标题直接绑定实现

## 问题描述

在 `RobotStatusPanelViewModel.cs` 中，原本使用 `AxisInfoHeader` 计算属性来显示轴位置信息：
- `TAxisStep`、`RAxisStep`、`ZAxisStep` - 来自 RobotAlarmRegisters
- `McuCmdService.SmoothBasePinSearchValue` 和 `McuCmdService.NoseBasePinSearchValue` - 来自 McuCmdService

当这些值发生变化时，UI 没有收到完整的通知，导致显示的信息不会自动更新。用户反馈："里面的所有值变更，比如：TAxisStep、RAxisStep、ZAxisStep等，属性没有被通知"。

## 最终解决方案：使用直接 XAML 绑定

根据用户要求："标题内容通过直接绑定实现，不使用AxisInfoHeader"，我们采用了更优雅的解决方案。

### 1. XAML 中使用 MultiBinding 和 StringFormat

在 `RobotStatusPanel.xaml` 中，将原来的单一属性绑定替换为 MultiBinding：

```xml
<TextBlock Grid.Row="0" Margin="0,12,0,8" 
           d:Text="【T轴：11 Step;R轴：22 Step;Z轴：33 PinSearch：Smooth：1000；Nose：1001】" 
           Style="{StaticResource StatusHeaderStyle}">
    <TextBlock.Text>
        <MultiBinding StringFormat="【T轴：{0} Step;R轴：{1} Step;Z轴：{2} PinSearch：Smooth：{3}；Nose：{4}】">
            <Binding Path="TAxisStep" />
            <Binding Path="RAxisStep" />
            <Binding Path="ZAxisStep" />
            <Binding Path="McuCmdService.SmoothBasePinSearchValue" />
            <Binding Path="McuCmdService.NoseBasePinSearchValue" />
        </MultiBinding>
    </TextBlock.Text>
</TextBlock>
```

### 2. 移除不再需要的代码

- 删除了 `AxisInfoHeader` 计算属性
- 移除了所有 `OnPropertyChanged(nameof(AxisInfoHeader))` 调用
- 不再需要复杂的属性变化监听逻辑

### 方案优势

1. **性能更好**：WPF 自动处理绑定更新，无需手动管理属性变化通知
2. **代码更简洁**：移除了复杂的计算属性和通知逻辑
3. **维护性更好**：直接绑定减少了出错的可能性
4. **响应更及时**：任何依赖属性的变化都会自动触发 UI 更新

## 实现细节

### 修改的文件

1. **RobotStatusPanel.xaml**
   - 替换了 Expander Header 的绑定方式
   - 使用 MultiBinding 直接绑定多个属性

2. **RobotStatusPanelViewModel.cs**
   - 移除了 `AxisInfoHeader` 属性定义
   - 清理了所有相关的属性变化通知代码

### 技术说明

- **MultiBinding**：允许将多个数据源绑定到单个目标属性
- **StringFormat**：提供格式化字符串，类似于 `string.Format()`
- **自动更新**：当任何绑定的属性发生变化时，WPF 会自动重新计算并更新 UI

## 测试建议

1. 修改 T轴、R轴、Z轴的位置值，验证标题是否实时更新
2. 修改 SmoothBasePinSearchValue 和 NoseBasePinSearchValue，验证标题是否实时更新
3. 验证编译无错误，运行时无异常

## 总结

通过使用 WPF 的 MultiBinding 功能，我们彻底解决了属性变化通知的问题，同时简化了代码结构，提高了性能和可维护性。这种方案比手动管理属性变化通知更加可靠和高效。
