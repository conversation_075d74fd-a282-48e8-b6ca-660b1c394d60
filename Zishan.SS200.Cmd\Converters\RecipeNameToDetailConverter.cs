using System;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Windows.Data;

// using Zishan.Robot.DbManage;

namespace Zishan.SS200.Cmd.Converters
{
    public class RecipeNameToDetailConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 假设有一个方法 GetRecipeDetailByName 用于获取配方详细信息
            string recipeName = value as string;
            if (recipeName != null)
            {
                /*
                DBAccessService _dBAccessService = new DBAccessService(App.AppIniConfig.DatabaseAccessType);
                var CurDbRecipeSeqInfo = _dBAccessService.GetRecipeSeqInfoList().FirstOrDefault(t => t.RecipeName == recipeName);
                return CurDbRecipeSeqInfo;*/
                return null;
            }
            return null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}