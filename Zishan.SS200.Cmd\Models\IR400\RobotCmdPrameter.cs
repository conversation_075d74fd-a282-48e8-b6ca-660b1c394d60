﻿using System.Collections.ObjectModel;
using Prism.Mvvm;

namespace Zishan.SS200.Cmd.Models.IR400
{
    public class RobotCmdPrameter : BindableBase
    {
        public string Name { get => _Name; set => SetProperty(ref _Name, value); }
        private string _Name;

        public string DescriptionChs { get => _DescriptionChs; set => SetProperty(ref _DescriptionChs, value); }
        private string _DescriptionChs;

        /// <summary>
        /// 自定义手动输入距离，单位：1/100mm
        /// </summary>
        public bool IsEditable { get => _IsEditable; set => SetProperty(ref _IsEditable, value); }
        private bool _IsEditable;

        public bool IsEnable { get => _IsEnable; set => SetProperty(ref _IsEnable, value); }
        private bool _IsEnable;

        /// <summary>
        /// 下位机参数列表
        /// </summary>
        public ObservableCollection<RobotCmdInfo> CmdPrameters { get => _CmdPrameters; set => SetProperty(ref _CmdPrameters, value); }
        private ObservableCollection<RobotCmdInfo> _CmdPrameters = new ObservableCollection<RobotCmdInfo>();

        public string Remark { get => _Remark; set => SetProperty(ref _Remark, value); }
        private string _Remark;

        public void Clear()
        {
            Name = string.Empty;
            DescriptionChs = string.Empty;
            IsEditable = false;
            IsEnable = false;
            CmdPrameters.Clear();
            Remark = string.Empty;
        }
    }
}