# Shuttle子系统状态自动更新功能

## 功能概述

本功能实现了当Shuttle相关的DI（数字输入）、DO（数字输出）值发生变化时，自动调用`UpdateShuttleSubsystemStatus`方法更新Shuttle子系统状态，包括新实现的批次状态(LSD1-LSD4)功能。

## 实现原理

### 1. 监听机制

系统通过监听以下线圈集合的变化来检测DI、DO值的更新：

- **Shuttle输入线圈**: `_mcuCmdService.ShuttleInputCoils`
- **Shuttle控制线圈**: `_mcuCmdService.ShuttleCoils`

### 2. 防抖机制

为了避免频繁更新导致的性能问题，系统采用了500ms的防抖延迟：

```csharp
// 初始化Shuttle状态更新防抖定时器
_shuttleStatusUpdateTimer = new DispatcherTimer();
_shuttleStatusUpdateTimer.Interval = TimeSpan.FromMilliseconds(500); // 500ms防抖延迟
_shuttleStatusUpdateTimer.Tick += (sender, e) =>
{
    _shuttleStatusUpdateTimer.Stop();
    UpdateShuttleSubsystemStatus(true);
};
```

### 3. 事件处理流程

1. **线圈集合变化监听**: 当线圈集合有新增或删除时，自动为新线圈添加监听器
2. **线圈值变化监听**: 监听每个线圈的`Coilvalue`属性变化
3. **防抖处理**: 检测到变化后，启动防抖定时器
4. **状态更新**: 防抖延迟结束后，调用`UpdateShuttleSubsystemStatus`方法

## 核心方法

### InitializeShuttleCoilChangeHandlers()

初始化Shuttle线圈变化监听器，在构造函数中调用。

```csharp
private void InitializeShuttleCoilChangeHandlers()
{
    try
    {
        // 监听Shuttle输入线圈变化
        if (_mcuCmdService.ShuttleInputCoils is ObservableCollection<ModbusCoil> shuttleInputCollection)
        {
            shuttleInputCollection.CollectionChanged += OnShuttleCoilCollectionChanged;
            foreach (var coil in shuttleInputCollection)
            {
                coil.PropertyChanged += OnShuttleCoilPropertyChanged;
            }
        }

        // 监听Shuttle控制线圈变化
        if (_mcuCmdService.ShuttleCoils is ObservableCollection<ModbusCoil> shuttleControlCollection)
        {
            shuttleControlCollection.CollectionChanged += OnShuttleCoilCollectionChanged;
            foreach (var coil in shuttleControlCollection)
            {
                coil.PropertyChanged += OnShuttleCoilPropertyChanged;
            }
        }

        _logger?.Info("Shuttle线圈变化监听器初始化成功");
    }
    catch (Exception ex)
    {
        _logger?.Error($"初始化Shuttle线圈变化监听器失败: {ex.Message}", ex);
    }
}
```

### OnShuttleCoilCollectionChanged()

处理线圈集合变化事件，为新添加的线圈添加监听器。

### OnShuttleCoilPropertyChanged()

处理单个线圈属性变化事件，当检测到`Coilvalue`变化时触发防抖更新。

```csharp
private void OnShuttleCoilPropertyChanged(object sender, PropertyChangedEventArgs e)
{
    try
    {
        // 只监听Coilvalue属性的变化
        if (e.PropertyName == nameof(ModbusCoil.Coilvalue))
        {
            var coil = sender as ModbusCoil;
            if (coil != null && coil.DeviceType == EnuMcuDeviceType.Shuttle)
            {
                // 使用防抖机制，避免频繁更新
                _shuttleStatusUpdateTimer.Stop();
                _shuttleStatusUpdateTimer.Start();

                _logger?.Debug($"检测到Shuttle线圈值变化: {coil.DeviceType} - {coil.IoCode} = {coil.Coilvalue}");
            }
        }
    }
    catch (Exception ex)
    {
        _logger?.Error($"处理Shuttle线圈属性变化事件失败: {ex.Message}", ex);
    }
}
```

## 自动更新的状态内容

当DI/DO值变化时，系统会自动更新以下Shuttle状态：

### 1. 基本状态 (MSD1-MSD3)
- Shuttle基本运行状态

### 2. 位置状态 (SSD1-SSD7)
- Shuttle位置状态（考虑SSC6配置）

### 3. 门巢状态 (SSD8-SSD13)
- 晶圆盒门和巢状态（考虑SSC6配置）

### 4. 阀门状态 (SSD14-SSD23)
- ShuttleIso阀门状态
- ShuttleXv阀门状态
- ShuttleBackfill阀门状态
- LoadlockBleed阀门状态
- LoadlockBackfill阀门状态

### 5. 批次状态 (LSD1-LSD4) ⭐ 新功能
- **LSD1**: Shuttle1 Cassette1批次状态（基于SDI6传感器）
- **LSD2**: Shuttle1 Cassette2批次状态（基于SDI7传感器）
- **LSD3**: Shuttle2 Cassette1批次状态（基于SDI8传感器）
- **LSD4**: Shuttle2 Cassette2批次状态（基于SDI9传感器）

## 日志记录

系统提供了详细的日志记录功能：

- **Debug级别**: 记录线圈值变化和状态更新详情
- **Info级别**: 记录初始化成功信息
- **Error级别**: 记录异常和错误信息

### 日志示例

```
[DEBUG] 检测到Shuttle线圈值变化: Shuttle - SDI6 = True
[DEBUG] 开始更新Shuttle子系统状态（由DI/DO值变化自动触发）
[DEBUG] Shuttle子系统状态更新完成: 位置状态=AtPosition1, 门巢状态=DoorOpen, ISO阀门=Open, 批次状态: LSD1=HasLot, LSD2=NoLot, LSD3=NoLot, LSD4=HasLot
```

## 使用方法

### 自动触发

当Shuttle设备的DI、DO值发生变化时，系统会自动更新状态，无需手动干预。特别是当晶圆盒存在传感器(SDI6-SDI9)状态变化时，会自动更新对应的批次状态。

### 手动触发

如果需要手动更新状态，可以通过以下方式：

1. **通过UI界面**：点击"更新Shuttle子系统状态"按钮
2. **通过代码**：调用命令 `OnUpdateShuttleSubsystemStatusCommand.Execute(null)`

## 性能考虑

1. **防抖机制**: 500ms防抖延迟避免频繁更新
2. **设备类型过滤**: 只监听Shuttle相关的线圈变化
3. **异常处理**: 完善的异常处理确保系统稳定性
4. **资源管理**: 正确添加和移除事件监听器

## 与Chamber自动更新的对比

| 功能 | Chamber | Shuttle |
|------|---------|---------|
| 防抖延迟 | 500ms | 500ms |
| 监听线圈 | ChaInputCoils, ChaCoils, ChbInputCoils, ChbCoils | ShuttleInputCoils, ShuttleCoils |
| 设备类型过滤 | ChamberA, ChamberB | Shuttle |
| 状态更新方法 | UpdateChamberSubsystemStatus | UpdateShuttleSubsystemStatus |
| 特殊功能 | 位置、阀门状态 | 批次状态(LSD1-LSD4) |

## 故障排除

### 常见问题

1. **状态未自动更新**
   - 检查线圈监控是否已启动：`_mcuCmdService.StartCoilsMonitoring()`
   - 检查Shuttle设备连接状态
   - 查看日志中是否有错误信息

2. **批次状态未更新**
   - 检查SDI6-SDI9传感器连接状态
   - 验证晶圆盒存在传感器是否正常工作
   - 查看调试日志中的传感器值变化

3. **更新过于频繁**
   - 检查防抖定时器设置
   - 考虑增加防抖延迟时间

4. **内存泄漏**
   - 确保事件监听器正确移除
   - 检查定时器是否正确停止

## 相关文件

- `ViewModels/Dock/RobotStatusPanelViewModel.cs` - 主要实现文件
- `Services/CoilStatusHelper.cs` - 状态计算辅助类
- `Models/SS200/SubSystemStatus/Shuttle/ShuttleSubsystemStatus.cs` - 状态模型
- `Docs/Examples/ShuttleLotStatusTest.cs` - 批次状态测试文件
