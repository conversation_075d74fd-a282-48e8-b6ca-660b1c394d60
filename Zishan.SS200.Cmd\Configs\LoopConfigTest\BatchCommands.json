{"BatchSequences": [{"Name": "Robot三轴回原点", "Description": "按R、Z、T轴顺序执行Robot三轴回原点过程", "LoopCount": 1, "IsInfiniteLoop": false, "LoopDelayMs": 2000, "Commands": [{"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 0, "Start slope": 8000, "Stop slope": 8000, "Run Current": 500}}, {"DeviceName": "Robot", "CommandName": "Move_T_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"T-Axis Move Position H": 0, "T-Axis Move Position L": 0, "Start slope": 25000, "Stop slope": 25000, "Run Current": 1000}}]}, {"Name": "单次：Wafer取放取动作", "Description": "单次：半导体Wafer取放取动作序列", "LoopCount": 1, "IsInfiniteLoop": false, "LoopDelayMs": 1000, "Commands": [{"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 10000, "Start slope": 6000, "Stop slope": 6000, "Run Current": 500}}, {"DeviceName": "Robot", "CommandName": "Move_T_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"T-Axis Move Position H": 0, "T-Axis Move Position L": 0, "Start slope": 25000, "Stop slope": 25000, "Run Current": 1000}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 11270, "Start slope": 8000, "Stop slope": 8000, "Run Current": 500}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 7000, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_T_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"T-Axis Move Position H": 0, "T-Axis Move Position L": 50000, "Start slope": 25000, "Stop slope": 25000, "Run Current": 1000}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 10000, "Start slope": 8000, "Stop slope": 8000, "Run Current": 500}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 7000, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 11270, "Start slope": 8000, "Stop slope": 8000, "Run Current": 500}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 7000, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_T_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"T-Axis Move Position H": 0, "T-Axis Move Position L": 0, "Start slope": 25000, "Stop slope": 25000, "Run Current": 1000}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 10000, "Start slope": 8000, "Stop slope": 8000, "Run Current": 500}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 11270, "Start slope": 8000, "Stop slope": 8000, "Run Current": 500}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}]}, {"Name": "循环：Wafer取放取动作", "Description": "循环：半导体Wafer取放取动作序列", "LoopCount": 0, "IsInfiniteLoop": true, "LoopDelayMs": 0, "Commands": [{"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 10000, "Start slope": 6000, "Stop slope": 6000, "Run Current": 800}}, {"DeviceName": "Robot", "CommandName": "Move_T_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"T-Axis Move Position H": 0, "T-Axis Move Position L": 0, "Start slope": 25000, "Stop slope": 25000, "Run Current": 1000}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 11270, "Start slope": 8000, "Stop slope": 8000, "Run Current": 800}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 7000, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_T_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"T-Axis Move Position H": 0, "T-Axis Move Position L": 50000, "Start slope": 25000, "Stop slope": 25000, "Run Current": 1000}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 10000, "Start slope": 8000, "Stop slope": 8000, "Run Current": 800}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 7000, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 11270, "Start slope": 8000, "Stop slope": 8000, "Run Current": 800}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 7000, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_T_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"T-Axis Move Position H": 0, "T-Axis Move Position L": 0, "Start slope": 25000, "Stop slope": 25000, "Run Current": 1000}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 10000, "Start slope": 8000, "Stop slope": 8000, "Run Current": 800}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 8333, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}, {"DeviceName": "Robot", "CommandName": "Move_Z_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"Z-Axis Move Position H": 0, "Z-Axis Move Position L": 11270, "Start slope": 8000, "Stop slope": 8000, "Run Current": 800}}, {"DeviceName": "Robot", "CommandName": "Move_R_Axis", "DelayAfterExecution": 0, "DynamicParameters": {"R-Axis Move Position H": 0, "R-Axis Move Position L": 0, "Start slope": 8333, "Stop slope": 7000, "Run Current": 600}}]}]}