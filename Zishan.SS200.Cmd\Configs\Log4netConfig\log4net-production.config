<?xml version="1.0" encoding="utf-8" ?>
<!-- 生产环境配置 - 最优性能，仅记录重要信息 -->
<log4net>
	<!-- 异步错误日志Appender -->
	<appender name="asyncErrorAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="1024" />
		<lossy value="false" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="ERROR" />
		</evaluator>
		<appender-ref ref="errorAppender" />
	</appender>

	<!-- 异步警告日志Appender -->
	<appender name="asyncWarnAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="512" />
		<lossy value="false" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="WARN" />
		</evaluator>
		<appender-ref ref="warnAppender" />
	</appender>

	<!-- 基础错误日志Appender -->
	<appender name="errorAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="ERROR" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\err.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<!-- 生产环境保留更多历史文件 -->
		<param name="MaxSizeRollBackups" value="50" />
		<param name="MaximumFileSize" value="5MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 基础警告日志Appender -->
	<appender name="warnAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="WARN" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\warn.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="30" />
		<param name="MaximumFileSize" value="5MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 性能日志Appender -->
	<appender name="perfAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="INFO" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\perf.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="20" />
		<param name="MaximumFileSize" value="2MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 根日志配置 - 生产环境仅记录警告和错误 -->
	<root>
		<level value="WARN" />
		<appender-ref ref="asyncErrorAppender" />
		<appender-ref ref="asyncWarnAppender" />
	</root>

	<!-- 性能日志记录器 -->
	<logger name="Performance" additivity="false">
		<level value="INFO" />
		<appender-ref ref="perfAppender" />
	</logger>
</log4net>
