using System;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.ChamberA;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberA
{
    /// <summary>
    /// 处理室配置参数提供者 - 使用枚举字典实现
    /// </summary>
    public class ChaConfigParametersProviderNew : ConfigProviderBase<EnuChaConfigParameterCodes, ConfigParametersBase>
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ChaConfigParametersProviderNew));

        // 单例实现
        private static readonly Lazy<ChaConfigParametersProviderNew> _instance =
            new Lazy<ChaConfigParametersProviderNew>(() => new ChaConfigParametersProviderNew());

        // 配置文件路径
        private const string CONFIG_PATH = "Configs/SS200/SubsystemConfigure/ChamberA/ChaConfigParameters.json";

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static ChaConfigParametersProviderNew Instance => _instance.Value;

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private ChaConfigParametersProviderNew() : base(CONFIG_PATH)
        {
        }

        /// <summary>
        /// 初始化默认值
        /// </summary>
        protected override void InitializeDefaultValues()
        {
            // 处理室门控制参数
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS1, 3);  // 处理室门开关最小时间(秒)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS2, 5);  // 处理室门开关最大时间(秒)

            // 处理室ISO阀控制参数
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS3, 2);  // 处理室ISO阀开关最小时间(秒)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS4, 4);  // 处理室ISO阀开关最大时间(秒)

            // 处理室真空系统参数
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS5, 10);  // 处理室抽真空最大时间(分钟)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS6, 5);   // 处理室回填最大时间(分钟)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS7, 2);   // 处理室传输压力(Torr)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS8, 730); // 处理室大气压力最小值(Torr)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS9, 0);   // 处理室压力偏移(Torr)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS10, 760); // 处理室大气压力设定点(Torr)

            // 处理室电源控制参数
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS11, 2);  // 处理室电源开关最小时间(秒)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS12, 5);  // 处理室电源开关最大时间(秒)

            // 处理室阀门控制参数
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS13, 1);  // 处理室阀门开关最小时间(秒)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS14, 3);  // 处理室阀门开关最大时间(秒)

            // 处理室RF参数
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS15, 0);    // 处理室RF功率最小值(W)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS16, 1000); // 处理室RF功率最大值(W)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS17, 13.56); // 处理室RF频率最小值(MHz)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS18, 13.56); // 处理室RF频率最大值(MHz)

            // 处理室温度参数
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS19, 20);  // 处理室温度最小值(℃)
            Settings.SetDefault(EnuChaConfigParameterCodes.PPS20, 100); // 处理室温度最大值(℃)
        }

        #region 特定参数访问方法

        /// <summary>
        /// 获取处理室门最小操作时间(秒)
        /// </summary>
        public int GetChamberDoorMinTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS1);
        }

        /// <summary>
        /// 获取处理室门最大操作时间(秒)
        /// </summary>
        public int GetChamberDoorMaxTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS2);
        }

        /// <summary>
        /// 获取处理室ISO阀最小操作时间(秒)
        /// </summary>
        public int GetChamberISOValveMinTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS3);
        }

        /// <summary>
        /// 获取处理室ISO阀最大操作时间(秒)
        /// </summary>
        public int GetChamberISOValveMaxTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS4);
        }

        /// <summary>
        /// 获取处理室抽真空最大时间(分钟)
        /// </summary>
        public int GetChamberPumpDownMaxTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS5);
        }

        /// <summary>
        /// 获取处理室回填最大时间(分钟)
        /// </summary>
        public int GetChamberBackfillMaxTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS6);
        }

        /// <summary>
        /// 获取处理室传输压力(Torr)
        /// </summary>
        public int GetChamberTransferPressure()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS7);
        }

        /// <summary>
        /// 获取处理室大气压力最小值(Torr)
        /// </summary>
        public int GetChamberATMPressureMinimum()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS8);
        }

        /// <summary>
        /// 获取处理室压力偏移(Torr)
        /// </summary>
        public int GetChamberPressureOffset()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS9);
        }

        /// <summary>
        /// 获取处理室大气压力设定点(Torr)
        /// </summary>
        public int GetChamberATMPressureSetpoint()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS10);
        }

        /// <summary>
        /// 获取处理室电源最小操作时间(秒)
        /// </summary>
        public int GetChamberPowerMinTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS11);
        }

        /// <summary>
        /// 获取处理室电源最大操作时间(秒)
        /// </summary>
        public int GetChamberPowerMaxTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS12);
        }

        /// <summary>
        /// 获取处理室阀门最小操作时间(秒)
        /// </summary>
        public int GetChamberValveMinTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS13);
        }

        /// <summary>
        /// 获取处理室阀门最大操作时间(秒)
        /// </summary>
        public int GetChamberValveMaxTime()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS14);
        }

        /// <summary>
        /// 获取处理室RF功率最小值(W)
        /// </summary>
        public int GetChamberRFPowerMin()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS15);
        }

        /// <summary>
        /// 获取处理室RF功率最大值(W)
        /// </summary>
        public int GetChamberRFPowerMax()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS16);
        }

        /// <summary>
        /// 获取处理室RF频率最小值(MHz)
        /// </summary>
        public double GetChamberRFFrequencyMin()
        {
            return GetDoubleValue(EnuChaConfigParameterCodes.PPS17);
        }

        /// <summary>
        /// 获取处理室RF频率最大值(MHz)
        /// </summary>
        public double GetChamberRFFrequencyMax()
        {
            return GetDoubleValue(EnuChaConfigParameterCodes.PPS18);
        }

        /// <summary>
        /// 获取处理室温度最小值(℃)
        /// </summary>
        public int GetChamberTemperatureMin()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS19);
        }

        /// <summary>
        /// 获取处理室温度最大值(℃)
        /// </summary>
        public int GetChamberTemperatureMax()
        {
            return GetIntValue(EnuChaConfigParameterCodes.PPS20);
        }

        #endregion
    }
}