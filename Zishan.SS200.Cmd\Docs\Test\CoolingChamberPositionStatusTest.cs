using System;
using <PERSON><PERSON>an.SS200.Cmd.Enums.Basic;
using <PERSON><PERSON>an.SS200.Cmd.ViewModels.Dock;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// 冷却腔体位置状态测试类
    /// 验证RS12和RS16状态能够正确区分CoolingTop和CoolingBottom
    /// </summary>
    public class CoolingChamberPositionStatusTest
    {
        /// <summary>
        /// 测试RS12状态解析 - Smooth端冷却腔伸展
        /// </summary>
        public static void TestRS12PositionStatus()
        {
            Console.WriteLine("=== 测试RS12状态解析 - Smooth端冷却腔伸展 ===");

            // 使用单例实例获取位置参数
            var positionProvider = RobotPositionParametersProvider.Instance;
            
            // T轴和R轴位置（匹配冷却腔）
            int tAxisStep = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP3);  // T轴Smooth端到冷却腔
            int rAxisStep = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP14); // R轴Smooth端到冷却腔
            
            // 测试CoolingTop情况
            int zAxisStepForTop = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP21); // Z轴Smooth端到CoolingTop
            Console.WriteLine($"测试CoolingTop: T={tAxisStep}, R={rAxisStep}, Z={zAxisStepForTop}");
            Console.WriteLine($"预期结果: EnuTAndRAxisSmoothExtendDestination = CoolingTop");
            
            // 测试CoolingBottom情况
            int zAxisStepForBottom = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP22); // Z轴Smooth端到CoolingBottom
            Console.WriteLine($"测试CoolingBottom: T={tAxisStep}, R={rAxisStep}, Z={zAxisStepForBottom}");
            Console.WriteLine($"预期结果: EnuTAndRAxisSmoothExtendDestination = CoolingBottom");
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试RS16状态解析 - Nose端冷却腔伸展
        /// </summary>
        public static void TestRS16PositionStatus()
        {
            Console.WriteLine("=== 测试RS16状态解析 - Nose端冷却腔伸展 ===");

            // 使用单例实例获取位置参数
            var positionProvider = RobotPositionParametersProvider.Instance;
            
            // T轴和R轴位置（匹配冷却腔）
            int tAxisStep = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP7);  // T轴Nose端到冷却腔
            int rAxisStep = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP15); // R轴Nose端到冷却腔
            
            // 测试CoolingTop情况
            int zAxisStepForTop = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP25); // Z轴Nose端到CoolingTop
            Console.WriteLine($"测试CoolingTop: T={tAxisStep}, R={rAxisStep}, Z={zAxisStepForTop}");
            Console.WriteLine($"预期结果: EnuTAndRAxisNoseExtendDestination = CoolingTop");
            
            // 测试CoolingBottom情况
            int zAxisStepForBottom = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP26); // Z轴Nose端到CoolingBottom
            Console.WriteLine($"测试CoolingBottom: T={tAxisStep}, R={rAxisStep}, Z={zAxisStepForBottom}");
            Console.WriteLine($"预期结果: EnuTAndRAxisNoseExtendDestination = CoolingBottom");
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试DetermineCoolingDestinationByZAxis方法逻辑
        /// </summary>
        public static void TestDetermineCoolingDestinationByZAxis()
        {
            Console.WriteLine("=== 测试DetermineCoolingDestinationByZAxis方法逻辑 ===");

            var positionProvider = RobotPositionParametersProvider.Instance;
            // const int tolerance = 50; // 位置容差 - 暂时注释掉未使用的变量
            
            // 测试Smooth端
            Console.WriteLine("Smooth端测试:");
            int zSmoothCT = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP21);
            int zSmoothCB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP22);
            
            Console.WriteLine($"  Z轴={zSmoothCT} (RP21) → 预期: CoolingTop");
            Console.WriteLine($"  Z轴={zSmoothCB} (RP22) → 预期: CoolingBottom");
            Console.WriteLine($"  Z轴=9999 (未知位置) → 预期: CoolingChamber");
            
            // 测试Nose端
            Console.WriteLine("Nose端测试:");
            int zNoseCT = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP25);
            int zNoseCB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP26);
            
            Console.WriteLine($"  Z轴={zNoseCT} (RP25) → 预期: CoolingTop");
            Console.WriteLine($"  Z轴={zNoseCB} (RP26) → 预期: CoolingBottom");
            Console.WriteLine($"  Z轴=9999 (未知位置) → 预期: CoolingChamber");
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 显示相关配置参数
        /// </summary>
        public static void ShowConfigurationParameters()
        {
            Console.WriteLine("=== 相关配置参数 ===");

            var positionProvider = RobotPositionParametersProvider.Instance;
            
            Console.WriteLine("T轴位置参数:");
            Console.WriteLine($"  RP3 (T轴Smooth端到冷却腔): {positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP3)}");
            Console.WriteLine($"  RP7 (T轴Nose端到冷却腔): {positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP7)}");
            
            Console.WriteLine("R轴位置参数:");
            Console.WriteLine($"  RP14 (R轴Smooth端到冷却腔): {positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP14)}");
            Console.WriteLine($"  RP15 (R轴Nose端到冷却腔): {positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP15)}");
            
            Console.WriteLine("Z轴位置参数:");
            Console.WriteLine($"  RP21 (Z轴Smooth端到CoolingTop): {positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP21)}");
            Console.WriteLine($"  RP22 (Z轴Smooth端到CoolingBottom): {positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP22)}");
            Console.WriteLine($"  RP25 (Z轴Nose端到CoolingTop): {positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP25)}");
            Console.WriteLine($"  RP26 (Z轴Nose端到CoolingBottom): {positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP26)}");
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始冷却腔体位置状态测试...\n");
            
            ShowConfigurationParameters();
            TestRS12PositionStatus();
            TestRS16PositionStatus();
            TestDetermineCoolingDestinationByZAxis();
            
            Console.WriteLine("测试完成！");
            Console.WriteLine("\n重要说明:");
            Console.WriteLine("1. RS12和RS16状态现在会根据Z轴高度动态判断CoolingTop或CoolingBottom");
            Console.WriteLine("2. 这修复了之前硬编码为CoolingTop的问题");
            Console.WriteLine("3. 确保了冷却腔体两个层级都能被正确识别");
        }
    }
}
