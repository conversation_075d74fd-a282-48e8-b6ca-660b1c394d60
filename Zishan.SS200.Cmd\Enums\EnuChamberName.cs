﻿using System.ComponentModel;

using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 腔名
    /// 用手臂A去取单元0里面的片盒的第一层的晶圆.（0：CHA  1：CHB  2：CHC  3：Cooling  4:Cassette )
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuChamberName
    {
        /// <summary>
        /// 普通腔体A
        /// </summary>
        [Description("CHA")]
        CHA = 0,

        /// <summary>
        /// 普通腔体B
        /// </summary>
        [Description("CHB")]
        CHB = 1,

        /// <summary>
        /// 普通腔体C
        /// </summary>
        [Description("CHC")]
        CHC = 2,

        /// <summary>
        /// Cooling
        /// </summary>
        [Description("Cooling")]
        Cooling = 3,

        /// <summary>
        /// Cassette
        /// </summary>
        [Description("Cassette")]
        Cassette = 4,

        /// <summary>
        /// LoadLock
        /// </summary>
        [Description("LoadLock")]
        LoadLock = 5,

        /// <summary>
        /// RobotArmNose
        /// </summary>
        [Description("RobotArmNose")]
        RobotArmNose = 6,

        /// <summary>
        /// RobotArmSmooth
        /// </summary>
        [Description("RobotArmSmooth")]
        RobotArmSmooth = 7,

        /// <summary>
        /// Home RobotUI用户控件使用
        /// </summary>
        [Description("Home")]
        Home = 8,

        /// <summary>
        /// 主机
        /// </summary>
        [Description("Host")]
        Host = 9,

        /// <summary>
        /// Buffer
        /// </summary>
        [Description("Buffer")]
        Buffer = 10,
    }
}