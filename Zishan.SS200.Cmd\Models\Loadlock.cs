﻿using Prism.Mvvm;
using <PERSON><PERSON>an.SS200.Cmd.Enums;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;

namespace Zishan.SS200.Cmd.Models
{
    public class Loadlock : BaseContainer
    {
        /// <summary>
        /// 工作模式
        /// </summary>
        public EnuMode Mode { get => _Mode; set => SetProperty(ref _Mode, value); }
        private EnuMode _Mode;

        /// <summary>
        /// 左边SN号
        /// </summary>
        public string LeftSn { get => _LeftSn; set => SetProperty(ref _LeftSn, value); }
        private string _LeftSn;

        /// <summary>
        /// 右边SN号
        /// </summary>
        public string RightSn { get => _RightSn; set => SetProperty(ref _RightSn, value); }
        private string _RightSn;

        /// <summary>
        /// 左边完成进度
        /// </summary>
        public double LeftFinishedProcess { get => _LeftFinishedProcess; set => SetProperty(ref _LeftFinishedProcess, value); }
        private double _LeftFinishedProcess;

        /// <summary>
        /// 右边完成进度
        /// </summary>
        public double RightFinishedProcess { get => _RightFinishedProcess; set => SetProperty(ref _RightFinishedProcess, value); }
        private double _RightFinishedProcess;

        /// <summary>
        /// SlitDoor门状态
        /// </summary>
        public EnuSlitDoorStatus SlitDoorStatus { get => _SlitDoorStatus; set => SetProperty(ref _SlitDoorStatus, value); }
        private EnuSlitDoorStatus _SlitDoorStatus;

        public Loadlock()
        {
        }
    }
}