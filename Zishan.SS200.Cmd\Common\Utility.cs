﻿#nullable enable

using System;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Text;

namespace Zishan.SS200.Cmd.Common
{
    public static class Utility
    {
        #region 取得指定前綴的第一個IP

        /// <summary>
        ///     取得指定前綴的第一個IP
        /// </summary>
        /// <param name="pre">指定前綴</param>
        /// <returns></returns>
        public static string GetLocalIp(string pre)
        {
            var adds = Dns.GetHostAddresses(Dns.GetHostName());
            var lstIP = adds.Select(localAddr => localAddr.ToString())
                .Where(ipTemp => CheckIp(ipTemp) && ipTemp.StartsWith(pre)).ToList();
            if (lstIP.Count <= 0) return "0.0.0.0";
            var ip = lstIP.FirstOrDefault(p => !p.StartsWith("192."));
            if (string.IsNullOrEmpty(ip))
            {
                ip = lstIP[0];
            }
            return ip;
        }

        #endregion 取得指定前綴的第一個IP

        #region 驗證IP

        /// <summary>
        ///     驗證IP
        /// </summary>
        /// <param name="ip"></param>
        /// <returns></returns>
        public static bool CheckIp(string ip)
        {
            int x;
            string[] dg = ip.Split('.');
            if (dg.Length != 4)
                return false;
            if (!int.TryParse(dg[0], out x) || x > 255 || x < 0)
                return false;
            if (!int.TryParse(dg[1], out x) || x > 255 || x < 0)
                return false;
            if (!int.TryParse(dg[2], out x) || x > 255 || x < 0)
                return false;
            if (!int.TryParse(dg[3], out x) || x > 255 || x < 0)
                return false;
            return true;
        }

        #endregion 驗證IP

        #region Ping 網絡是否OK

        /// <summary>
        ///     Ping Robot網絡是否OK
        /// </summary>
        /// <returns></returns>
        public static bool Ping(string ip)
        {
            Ping pingSender = new Ping();
            //PingOptions options = new PingOptions {DontFragment = true};

            PingReply? reply = null;
            try
            {
                reply = pingSender.Send(ip, 1000);
            }
            catch (PingException)
            {
                //AppLog.Error(Ip, this.Name, ex);
            }
            catch (Exception)
            {
                //AppLog.Error(.Ip, this.Name, ex);
            }

            if (reply != null && reply.Status == IPStatus.Success)
            {
                pingSender.Dispose();
                return true;
            }
            pingSender.Dispose();
            return false;
        }

        #endregion Ping 網絡是否OK

        #region 复制文件夹文件到指定的文件夹下(递归)

        /// <summary>
        /// 复制文件夹文件到指定的文件夹下(递归)
        /// </summary>
        /// <param name="strFromDirectory">源文件夹路径</param>
        /// <param name="strToDirectory">目标文件夹路径</param>
        /// <param name="blOverWrite">是否覆盖目标文件</param>
        public static void CopyFolder(string strFromDirectory, string strToDirectory, bool blOverWrite)
        {
            Directory.CreateDirectory(strToDirectory);

            if (!Directory.Exists(strFromDirectory)) return;

            string[] directories = Directory.GetDirectories(strFromDirectory);

            if (directories.Length > 0)
            {
                foreach (string d in directories)
                {
                    CopyFolder(d, strToDirectory + d.Substring(d.LastIndexOf("\\", StringComparison.Ordinal)), blOverWrite);
                }
            }
            string[] files = Directory.GetFiles(strFromDirectory);
            if (files.Length > 0)
            {
                foreach (string s in files)
                {
                    File.Copy(s, strToDirectory + s.Substring(s.LastIndexOf("\\", StringComparison.Ordinal)), true);
                }
            }
        }

        #endregion 复制文件夹文件到指定的文件夹下(递归)

        #region 检测指定目录是否存在

        /// <summary>
        /// 检测指定目录是否存在
        /// </summary>
        /// <param name="directoryPath">目录的绝对路径</param>
        /// <returns></returns>
        public static bool IsExistDirectory(string directoryPath)
        {
            return Directory.Exists(directoryPath);
        }

        #endregion 检测指定目录是否存在

        #region 获取指定文件夹下指定匹配的文件数量

        /// <summary>
        /// 获取指定文件夹下指定匹配的文件数量
        /// </summary>
        /// <param name="dirPath"></param>
        /// <param name="searchPattern">例如：*.dat</param>
        /// <returns></returns>
        public static int GetFilesCountByDirector(string dirPath, string searchPattern)
        {
            DirectoryInfo dir = new DirectoryInfo(dirPath);
            FileInfo[] arrFileinfos = dir.GetFiles(searchPattern, SearchOption.TopDirectoryOnly);
            return arrFileinfos.Length;
        }

        #endregion 获取指定文件夹下指定匹配的文件数量

        #region 检测指定文件是否存在,如果存在返回true

        /// <summary>
        /// 检测指定文件是否存在,如果存在则返回true。
        /// </summary>
        /// <param name="filePath">文件的绝对路径</param>
        public static bool IsExistFile(string filePath)
        {
            return File.Exists(filePath);
        }

        #endregion 检测指定文件是否存在,如果存在返回true

        #region 如果目录不存在，则创建一个目录

        /// <summary>
        /// 如果目录不存在，则创建一个目录
        /// </summary>
        /// <param name="directoryPath">目录的绝对路径</param>
        public static void CreateDirectory(string directoryPath)
        {
            //如果目录不存在则创建该目录
            if (!IsExistDirectory(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }

        #endregion 如果目录不存在，则创建一个目录

        #region 測試指定的文件是否可以移動

        /// <summary>
        /// 測試指定的文件是否可以移動
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static bool IsMoveAvaliable(String filePath)
        {
            bool blMoveAvaliable = false;
            try
            {
                if (File.Exists(filePath))
                {
                    File.Move(filePath, filePath);
                    blMoveAvaliable = true;
                }
            }
            catch (Exception ex)
            {
                String strErrorMessage = ex.Message;
                blMoveAvaliable = false;
            }
            return blMoveAvaliable;
        }

        #endregion 測試指定的文件是否可以移動

        #region TimeSpan转换为：天+小时+分钟+秒+毫秒

        /// <summary>
        /// TimeSpan转换为：天+小时+分钟+秒+毫秒
        /// </summary>
        /// <param name="timeSpan"></param>
        /// <returns></returns>
        public static string GetHourMinutesInfo(TimeSpan timeSpan)
        {
            string strResult = string.Empty;
            bool lessZero = timeSpan.TotalMilliseconds < 0;

            //TimeSpan timeSpan = new TimeSpan(0, 0, (Int32)seconds);
            strResult = $"{timeSpan.Days}天{timeSpan.Hours}小时{timeSpan.Minutes}分钟{timeSpan.Seconds}秒{timeSpan.Milliseconds}毫秒";

            string[] arrZero = { "0天", "0小时", "0分钟" };

            foreach (var zero in arrZero)
            {
                if (strResult.StartsWith(zero))
                {
                    strResult = strResult.Substring(zero.Length);
                }
                else
                {
                    break;//不滿足必須跳出循環
                }
            }

            strResult = lessZero ? "-" + strResult : strResult;
            return strResult;
        }

        #endregion TimeSpan转换为：天+小时+分钟+秒+毫秒

        #region 運行指定的命令行程序

        /// <summary>
        /// 運行指定的命令行程序
        /// </summary>
        /// <param name="cmd">DOS命令</param>
        public static void RunCmdShell(string cmd)
        {
            System.Diagnostics.Process myProcess = new System.Diagnostics.Process();
            myProcess.StartInfo.FileName = "cmd.exe";//启动cmd命令
            myProcess.StartInfo.UseShellExecute = false;//是否使用系统外壳程序启动进程
            myProcess.StartInfo.RedirectStandardInput = true;//是否从流中读取
            myProcess.StartInfo.RedirectStandardOutput = true;//是否写入流
            myProcess.StartInfo.RedirectStandardError = true;//是否将错误信息写入流
            myProcess.StartInfo.CreateNoWindow = true;//是否在新窗口中启动进程
            myProcess.Start();//启动进程
            myProcess.StandardInput.WriteLine(cmd);//执行重启计算机命令
        }

        #endregion 運行指定的命令行程序

        #region 将序列化的json字符串内容写入Json文件，并且保存

        /// <summary>
        /// 将序列化的json字符串内容写入Json文件，并且保存
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="jsonConents">Json内容</param>
        public static void WriteJsonFile(string filePath, string jsonConents)
        {
            //清除旧的文本
            using (FileStream stream = File.Open(filePath, FileMode.OpenOrCreate, FileAccess.Write))
            {
                stream.Seek(0, SeekOrigin.Begin);
                stream.SetLength(0);
            }
            using FileStream fs = new(filePath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite);
            using StreamWriter sw = new(fs, Encoding.UTF8);
            sw.WriteLine(jsonConents);
        }

        #endregion 将序列化的json字符串内容写入Json文件，并且保存

        #region 获取到本地的Json文件并且解析返回对应的json字符串

        /// <summary>
        /// 获取到本地的Json文件并且解析返回对应的json字符串
        /// </summary>
        /// <param name="filepath">文件路径</param>
        /// <returns>Json内容</returns>
        public static string ReadJsonFile(string filepath)
        {
            string json = string.Empty;
            using (FileStream fs = new(filepath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite))
            {
                using StreamReader sr = new(fs, Encoding.UTF8);
                json = sr.ReadToEnd().ToString();
            }
            return json;
        }

        #endregion 获取到本地的Json文件并且解析返回对应的json字符串

        #region 获取枚举备注信息

        /// <summary>
        /// 获取枚举备注信息
        /// </summary>
        /// <param name="enumInfo"></param>
        /// <returns></returns>
        public static string GetRemark(this Enum enumInfo)
        {
            Type type = enumInfo.GetType();
            //获取字段信息
            FieldInfo? field = type.GetField(enumInfo.ToString());
            //检查字段是否含有指定特性
            if (field != null && field.IsDefined(typeof(DescriptionAttribute), true))
            {
                //获取字段上的自定义特性
                DescriptionAttribute? remarkAttribute = field.GetCustomAttribute(typeof(DescriptionAttribute)) as DescriptionAttribute;
                if (remarkAttribute != null) return remarkAttribute.Description;
            }
            else
            {
                return enumInfo.ToString();
            }

            return string.Empty;
        }

        #endregion 获取枚举备注信息

        #region PLC日期字符串转换为日期型，比如：2023-12-20-14:37:55.690

        /// <summary>
        /// PLC日期字符串转换为日期型，比如：2023-12-20-14:37:55.690
        /// </summary>
        /// <param name="strPlcDateTime"></param>
        /// <returns></returns>
        public static DateTime GetDateTimeByPLcStringDateTime(string strPlcDateTime)
        {
            DateTime dtResult = DateTime.MinValue;
            if (DateTime.TryParseExact(strPlcDateTime, "yyyy-MM-dd-HH:mm:ss.fff", CultureInfo.InvariantCulture, DateTimeStyles.None, out dtResult)) return dtResult;
            if (DateTime.TryParseExact(strPlcDateTime, "yyyy-M-dd-HH:mm:ss.fff", CultureInfo.InvariantCulture, DateTimeStyles.None, out dtResult)) return dtResult;
            return dtResult;
        }

        #endregion PLC日期字符串转换为日期型，比如：2023-12-20-14:37:55.690
    }
}