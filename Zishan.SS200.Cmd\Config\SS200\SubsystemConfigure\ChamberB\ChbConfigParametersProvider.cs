using System;
using System.Collections.Generic;
using System.IO;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.ChamberB;
using Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberB
{
    /// <summary>
    /// 处理室B配置参数根配置
    /// </summary>
    public class ChbConfigParametersConfig
    {
        public List<ConfigureSetting> ConfigureSettings { get; set; }
    }

    /// <summary>
    /// 处理室B配置参数提供者 - 从JSON配置文件加载参数
    /// </summary>
    public class ChbConfigParametersProvider : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ChbConfigParametersProvider));
        private readonly Dictionary<string, object> _settings = new Dictionary<string, object>();
        private readonly FileSystemWatcher _configWatcher;

        private static readonly Lazy<ChbConfigParametersProvider> _instance =
            new Lazy<ChbConfigParametersProvider>(() => new ChbConfigParametersProvider());

        // 配置文件路径
        private const string CONFIG_PATH = "Configs/SS200/SubsystemConfigure/ChamberB/ChbConfigParameters.json";

        // 最后修改时间，用于监测配置文件变化
        private DateTime _lastModifiedTime = DateTime.MinValue;

        public static ChbConfigParametersProvider Instance => _instance.Value;

        // 私有构造函数
        private ChbConfigParametersProvider()
        {
            // 初始化文件系统监视器
            string configDir = Path.GetDirectoryName(GetConfigFilePath());
            string configFileName = Path.GetFileName(CONFIG_PATH);

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            _configWatcher = new FileSystemWatcher(configDir, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            // 注册文件变化事件处理
            _configWatcher.Changed += OnConfigFileChanged;
            _configWatcher.Created += OnConfigFileChanged;

            // 初始化默认值
            InitializeDefaultValues();

            // 尝试加载配置文件
            LoadFromJson();
        }

        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 由于文件系统事件可能会触发多次，这里添加简单的防抖动处理
                if ((DateTime.Now - _lastModifiedTime).TotalMilliseconds < 100)
                {
                    return;
                }

                _logger.Info($"检测到ChamberB配置文件变化: {e.FullPath}, 变化类型: {e.ChangeType}");
                LoadFromJson();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理ChamberB配置文件变化事件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaultValues()
        {
            // 处理室门控制参数
            _settings["PPS1"] = 0.3;  // 处理室门开关最小时间(秒) - 支持小数
            _settings["PPS2"] = 5;    // 处理室门开关最大时间(秒)

            // 处理室ISO阀控制参数
            _settings["PPS3"] = 0.3;  // 处理室ISO阀开关最小时间(秒) - 支持小数
            _settings["PPS4"] = 4;    // 处理室ISO阀开关最大时间(秒)

            // 处理室真空系统参数
            _settings["PPS5"] = 10;  // 处理室抽真空最大时间(分钟)
            _settings["PPS6"] = 5;   // 处理室回填最大时间(分钟)
            _settings["PPS7"] = 2;   // 处理室传输压力(Torr)
            _settings["PPS8"] = 730; // 处理室大气压力最小值(Torr)
            _settings["PPS9"] = 0;   // 处理室压力偏移(Torr)
            _settings["PPS10"] = 760; // 处理室大气压力设定点(Torr)

            // 处理室电源控制参数
            _settings["PPS11"] = 2;  // 处理室电源开关最小时间(秒)
            _settings["PPS12"] = 5;  // 处理室电源开关最大时间(秒)

            // 处理室阀门控制参数
            _settings["PPS13"] = 1;  // 处理室阀门开关最小时间(秒)
            _settings["PPS14"] = 3;  // 处理室阀门开关最大时间(秒)

            // 处理室RF参数
            _settings["PPS15"] = 0;   // 处理室RF功率最小值(W)
            _settings["PPS16"] = 1000; // 处理室RF功率最大值(W)
            _settings["PPS17"] = 13.56; // 处理室RF频率最小值(MHz)
            _settings["PPS18"] = 13.56; // 处理室RF频率最大值(MHz)

            // 处理室温度参数
            _settings["PPS19"] = 20;  // 处理室温度最小值(℃)
            _settings["PPS20"] = 100; // 处理室温度最大值(℃)

            // 添加其他参数的默认值 (PPS21-PPS45)
            for (int i = 21; i <= 45; i++)
            {
                _settings[$"PPS{i}"] = 0; // 默认值为0
            }

            // 处理室压力控制参数
            _settings["PPS46"] = 0.5; // 处理室抽真空压力偏差(Torr) - 支持小数
        }

        /// <summary>
        /// 从JSON配置文件加载参数
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool LoadFromJson()
        {
            try
            {
                string jsonFilePath = GetConfigFilePath();
                if (!File.Exists(jsonFilePath))
                {
                    _logger.Warn($"处理室B配置参数文件不存在: {jsonFilePath}，将使用默认值");
                    return false;
                }

                // 获取文件最后修改时间
                DateTime currentModified = File.GetLastWriteTime(jsonFilePath);

                // 如果文件未修改，则不重新加载
                if (currentModified == _lastModifiedTime)
                {
                    return true;
                }

                _lastModifiedTime = currentModified;
                string jsonContent;
                using (var fileStream = new FileStream(jsonFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    jsonContent = reader.ReadToEnd();
                }
                var config = JsonConvert.DeserializeObject<ChbConfigParametersConfig>(jsonContent);

                if (config?.ConfigureSettings == null || config.ConfigureSettings.Count == 0)
                {
                    _logger.Warn("未找到有效的ChamberB配置参数，将使用默认值");
                    return false;
                }

                // 临时字典，验证成功后再替换
                var tempSettings = new Dictionary<string, object>();
                foreach (var setting in config.ConfigureSettings)
                {
                    if (string.IsNullOrEmpty(setting.Code))
                    {
                        _logger.Warn($"ChamberB参数ID {setting.Id} 缺少代码标识，已跳过");
                        continue;
                    }

                    tempSettings[setting.Code] = setting.Value;
                    _logger.Debug($"加载ChamberB参数 {setting.Code} = {setting.Value} ({setting.Description})");
                }

                // 验证所有必要参数都存在
                for (int i = 1; i <= 46; i++)
                {
                    string code = $"PPS{i}";
                    if (!tempSettings.ContainsKey(code))
                    {
                        _logger.Warn($"ChamberB配置文件中缺少必要参数 {code}，将使用默认值");
                        tempSettings[code] = _settings[code]; // 使用默认值
                    }
                }

                // 更新参数字典
                foreach (var kvp in tempSettings)
                {
                    _settings[kvp.Key] = kvp.Value;
                }

                _logger.Info($"成功从 {jsonFilePath} 加载 {tempSettings.Count} 个处理室B配置参数");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载处理室B配置参数文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        private string GetConfigFilePath()
        {
            try
            {
                return App.ConfigHelper.GetConfigFilePath(CONFIG_PATH);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取ChamberB配置文件路径失败: {ex.Message}", ex);

                // 回退策略 - 尝试直接拼接路径
                string fallbackPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    CONFIG_PATH);

                _logger.Info($"使用ChamberB回退路径: {fallbackPath}");
                return fallbackPath;
            }
        }

        /// <summary>
        /// 获取参数值（泛型方法）
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="code">参数代码 (如 "PPS1")</param>
        /// <returns>参数值</returns>
        public T GetSettingValue<T>(string code)
        {
            if (_settings.TryGetValue(code, out object value))
            {
                if (value is T typedValue)
                {
                    return typedValue;
                }

                try
                {
                    // 尝试转换类型
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch (Exception ex)
                {
                    _logger.Error($"ChamberB参数类型转换失败: {code}, 期望类型: {typeof(T).Name}, 实际值: {value}", ex);
                    throw new InvalidCastException($"ChamberB参数类型不匹配: {code}");
                }
            }

            throw new KeyNotFoundException($"找不到处理室B配置参数: {code}");
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        /// <param name="enuChbConfigParameterCodes">参数代码枚举</param>
        /// <returns>参数值</returns>
        public T GetSettingValue<T>(EnuChbConfigParameterCodes enuChbConfigParameterCodes)
        {
            return GetSettingValue<T>(enuChbConfigParameterCodes.ToString());
        }

        /// <summary>
        /// 获取int类型参数值
        /// </summary>
        /// <param name="enuChbConfigParameterCodes">参数代码枚举</param>
        /// <returns>int类型参数值</returns>
        public int GetIntSettingValue(EnuChbConfigParameterCodes enuChbConfigParameterCodes)
        {
            return GetSettingValue<int>(enuChbConfigParameterCodes);
        }

        /// <summary>
        /// 获取double类型参数值
        /// </summary>
        /// <param name="enuChbConfigParameterCodes">参数代码枚举</param>
        /// <returns>double类型参数值</returns>
        public double GetDoubleSettingValue(EnuChbConfigParameterCodes enuChbConfigParameterCodes)
        {
            return GetSettingValue<double>(enuChbConfigParameterCodes);
        }

        /// <summary>
        /// 获取string类型参数值
        /// </summary>
        /// <param name="enuChbConfigParameterCodes">参数代码枚举</param>
        /// <returns>string类型参数值</returns>
        public string GetStringSettingValue(EnuChbConfigParameterCodes enuChbConfigParameterCodes)
        {
            return GetSettingValue<string>(enuChbConfigParameterCodes);
        }

        /// <summary>
        /// 获取bool类型参数值
        /// </summary>
        /// <param name="enuChbConfigParameterCodes">参数代码枚举</param>
        /// <returns>bool类型参数值</returns>
        public bool GetBoolSettingValue(EnuChbConfigParameterCodes enuChbConfigParameterCodes)
        {
            return GetSettingValue<bool>(enuChbConfigParameterCodes);
        }

        /// <summary>
        /// 设置参数值
        /// </summary>
        /// <param name="code">参数代码</param>
        /// <param name="value">参数值</param>
        public void SetSettingValue(string code, object value)
        {
            _settings[code] = value;
            _logger.Debug($"设置ChamberB参数 {code} = {value}");
        }

        /// <summary>
        /// 设置参数值
        /// </summary>
        /// <param name="enuChbConfigParameterCodes">参数代码枚举</param>
        /// <param name="value">参数值</param>
        public void SetSettingValue(EnuChbConfigParameterCodes enuChbConfigParameterCodes, object value)
        {
            SetSettingValue(enuChbConfigParameterCodes.ToString(), value);
        }

        /// <summary>
        /// 保存配置到JSON文件
        /// </summary>
        /// <returns>是否保存成功</returns>
        public bool SaveToJson()
        {
            try
            {
                var config = new ChbConfigParametersConfig
                {
                    ConfigureSettings = new List<ConfigureSetting>()
                };

                int id = 1;
                foreach (var kvp in _settings)
                {
                    config.ConfigureSettings.Add(new ConfigureSetting
                    {
                        Id = id++,
                        Code = kvp.Key,
                        Value = kvp.Value,
                        Description = GetParameterDescription(kvp.Key),
                        Unit = GetParameterUnit(kvp.Key)
                    });
                }

                string jsonContent = JsonConvert.SerializeObject(config, Formatting.Indented);
                string jsonFilePath = GetConfigFilePath();

                // 确保目录存在
                string directory = Path.GetDirectoryName(jsonFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(jsonFilePath, jsonContent);
                _logger.Info($"成功保存ChamberB配置参数到 {jsonFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"保存ChamberB配置参数文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取参数描述
        /// </summary>
        private string GetParameterDescription(string code)
        {
            // 这里可以根据参数代码返回相应的描述
            // 为了简化，返回基本描述
            return $"ChamberB parameter {code}";
        }

        /// <summary>
        /// 获取参数单位
        /// </summary>
        private string GetParameterUnit(string code)
        {
            // 根据参数代码返回相应的单位
            return code switch
            {
                "PPS1" or "PPS2" or "PPS3" or "PPS4" => "S",
                "PPS5" or "PPS6" or "PPS7" => "Torr",
                "PPS8" or "PPS9" or "PPS10" or "PPS11" => "sccm",
                _ => ""
            };
        }

        #region 辅助方法 - 获取特定配置参数

        /// <summary>
        /// 获取处理室抽真空压力偏差
        /// </summary>
        /// <returns>处理室抽真空压力偏差(Torr)</returns>
        public double GetChamberPumpDownPressureDeviation()
        {
            return GetDoubleSettingValue(EnuChbConfigParameterCodes.PPS46);
        }

        #endregion 辅助方法 - 获取特定配置参数

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _configWatcher?.Dispose();
        }
    }
}
