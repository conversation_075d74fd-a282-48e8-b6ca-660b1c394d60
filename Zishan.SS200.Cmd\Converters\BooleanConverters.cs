using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 将布尔值转换为对应的颜色
    /// ？(其它) ：灰色 - 表示未激活状态
    /// 0（false ：绿色 - 表示正常激活状态
    /// 1（true）：红色 - 表示异常状态
    /// </summary>
    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Colors.Red : Colors.LimeGreen;
            }

            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}