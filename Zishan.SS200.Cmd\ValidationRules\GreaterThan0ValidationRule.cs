﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace Zishan.SS200.Cmd.ValidationRules
{
    public class GreaterThan0ValidationRule : ValidationRule
    {
        public ValidationParams MinNum { get; set; }

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            double result = 0;
            if (value != null && !double.TryParse(value.ToString(), out result))
            {
                return new ValidationResult(false, "输入的不是数字");
            }

            var minNum = (double)MinNum.Data;

            if (result <= minNum)
            {
                return new ValidationResult(false, $"输入的数字必须大于{minNum}");
            }

            return ValidationResult.ValidResult;
        }
    }
}