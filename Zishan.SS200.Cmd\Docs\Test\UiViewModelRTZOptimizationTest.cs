using System;
using log4net;
using Zishan.SS200.Cmd.ViewModels.Dock;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// UiViewModel RTZ轴位置访问优化测试
    /// 验证优化后的RTZ轴位置访问功能是否正常工作
    /// </summary>
    public class UiViewModelRTZOptimizationTest
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(UiViewModelRTZOptimizationTest));

        /// <summary>
        /// 测试基本的RTZ轴位置访问功能
        /// </summary>
        public static void TestBasicRTZAccess()
        {
            try
            {
                _logger.Info("=== UiViewModel RTZ轴位置访问基本功能测试 ===");

                // 获取服务实例
                var mcuService = S200McuCmdService.Instance;
                var viewModel = new UiViewModel(mcuService);

                // 测试数据有效性检查
                bool isDataValid = viewModel.IsRTZPositionDataValid;
                _logger.Info($"RTZ轴位置数据有效性: {isDataValid}");

                if (!isDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，请确保Robot设备已连接并启动了报警监控");
                    return;
                }

                // 测试单独的轴位置访问
                _logger.Info("测试单独轴位置访问:");
                _logger.Info($"  T轴步进值: {viewModel.TAxisStep}");
                _logger.Info($"  R轴步进值: {viewModel.RAxisStep}");
                _logger.Info($"  Z轴步进值: {viewModel.ZAxisStep}");

                // 测试物理单位值访问
                _logger.Info("测试物理单位值访问:");
                _logger.Info($"  T轴角度: {viewModel.TAxisDegree:F2}°");
                _logger.Info($"  R轴长度: {viewModel.RAxisLength:F2}mm");
                _logger.Info($"  Z轴高度: {viewModel.ZAxisHeight:F2}mm");

                _logger.Info("=== 基本功能测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"基本RTZ轴位置访问测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试组合信息获取功能
        /// </summary>
        public static void TestCombinedRTZAccess()
        {
            try
            {
                _logger.Info("=== UiViewModel RTZ轴组合信息获取测试 ===");

                var mcuService = S200McuCmdService.Instance;
                var viewModel = new UiViewModel(mcuService);

                if (!viewModel.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过组合信息测试");
                    return;
                }

                // 测试步进值组合获取
                var (tAxisStep, rAxisStep, zAxisStep) = viewModel.GetRTZSteps();
                _logger.Info($"RTZ轴步进值组合: T={tAxisStep}, R={rAxisStep}, Z={zAxisStep}");

                // 测试物理值组合获取
                var (tAxisDegree, rAxisLength, zAxisHeight) = viewModel.GetRTZPhysicalValues();
                _logger.Info($"RTZ轴物理值组合: T={tAxisDegree:F2}°, R={rAxisLength:F2}mm, Z={zAxisHeight:F2}mm");

                _logger.Info("=== 组合信息获取测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"组合RTZ轴位置访问测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试格式化字符串功能
        /// </summary>
        public static void TestFormattedStringMethods()
        {
            try
            {
                _logger.Info("=== UiViewModel 格式化字符串方法测试 ===");

                var mcuService = S200McuCmdService.Instance;
                var viewModel = new UiViewModel(mcuService);

                // 测试详细显示文本
                string displayText = viewModel.GetRTZPositionDisplayText();
                _logger.Info($"详细显示文本: {displayText}");

                // 测试简化显示文本
                string simpleText = viewModel.GetRTZPositionSimpleText();
                _logger.Info($"简化显示文本: {simpleText}");

                _logger.Info("=== 格式化字符串方法测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"格式化字符串方法测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试与原有实现的一致性
        /// </summary>
        public static void TestConsistencyWithOriginal()
        {
            try
            {
                _logger.Info("=== UiViewModel 与原有实现一致性测试 ===");

                var mcuService = S200McuCmdService.Instance;
                var viewModel = new UiViewModel(mcuService);

                if (!viewModel.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过一致性测试");
                    return;
                }

                // 比较优化后的属性与服务接口的一致性
                bool tAxisConsistent = viewModel.TAxisStep == mcuService.CurrentTAxisStep;
                bool rAxisConsistent = viewModel.RAxisStep == mcuService.CurrentRAxisStep;
                bool zAxisConsistent = viewModel.ZAxisStep == mcuService.CurrentZAxisStep;

                _logger.Info($"T轴步进值一致性: {tAxisConsistent} (ViewModel: {viewModel.TAxisStep}, Service: {mcuService.CurrentTAxisStep})");
                _logger.Info($"R轴步进值一致性: {rAxisConsistent} (ViewModel: {viewModel.RAxisStep}, Service: {mcuService.CurrentRAxisStep})");
                _logger.Info($"Z轴步进值一致性: {zAxisConsistent} (ViewModel: {viewModel.ZAxisStep}, Service: {mcuService.CurrentZAxisStep})");

                // 比较物理值的一致性
                bool tDegreeConsistent = Math.Abs(viewModel.TAxisDegree - mcuService.CurrentTAxisDegree) < 0.001;
                bool rLengthConsistent = Math.Abs(viewModel.RAxisLength - mcuService.CurrentRAxisLength) < 0.001;
                bool zHeightConsistent = Math.Abs(viewModel.ZAxisHeight - mcuService.CurrentZAxisHeight) < 0.001;

                _logger.Info($"T轴角度一致性: {tDegreeConsistent} (ViewModel: {viewModel.TAxisDegree:F6}, Service: {mcuService.CurrentTAxisDegree:F6})");
                _logger.Info($"R轴长度一致性: {rLengthConsistent} (ViewModel: {viewModel.RAxisLength:F6}, Service: {mcuService.CurrentRAxisLength:F6})");
                _logger.Info($"Z轴高度一致性: {zHeightConsistent} (ViewModel: {viewModel.ZAxisHeight:F6}, Service: {mcuService.CurrentZAxisHeight:F6})");

                bool allConsistent = tAxisConsistent && rAxisConsistent && zAxisConsistent && 
                                   tDegreeConsistent && rLengthConsistent && zHeightConsistent;

                _logger.Info($"整体一致性: {allConsistent}");

                if (allConsistent)
                {
                    _logger.Info("✓ 优化后的实现与服务接口完全一致");
                }
                else
                {
                    _logger.Warn("✗ 发现不一致的地方，需要检查实现");
                }

                _logger.Info("=== 一致性测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"一致性测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试性能对比
        /// </summary>
        public static void TestPerformanceComparison()
        {
            try
            {
                _logger.Info("=== UiViewModel 性能对比测试 ===");

                var mcuService = S200McuCmdService.Instance;
                var viewModel = new UiViewModel(mcuService);

                if (!viewModel.IsRTZPositionDataValid)
                {
                    _logger.Warn("RTZ轴位置数据无效，跳过性能测试");
                    return;
                }

                const int iterations = 10000;

                // 测试优化后的访问性能
                var sw = System.Diagnostics.Stopwatch.StartNew();
                for (int i = 0; i < iterations; i++)
                {
                    var _ = viewModel.TAxisStep;
                    var __ = viewModel.RAxisStep;
                    var ___ = viewModel.ZAxisStep;
                }
                sw.Stop();
                long optimizedTime = sw.ElapsedMilliseconds;

                // 测试组合访问性能
                sw.Restart();
                for (int i = 0; i < iterations; i++)
                {
                    var (t, r, z) = viewModel.GetRTZSteps();
                }
                sw.Stop();
                long combinedTime = sw.ElapsedMilliseconds;

                _logger.Info($"优化后单独访问性能 ({iterations}次): {optimizedTime}ms");
                _logger.Info($"组合访问性能 ({iterations}次): {combinedTime}ms");
                _logger.Info($"平均单次访问时间: {(double)optimizedTime / iterations:F6}ms");

                _logger.Info("=== 性能对比测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error($"性能对比测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            _logger.Info("开始运行UiViewModel RTZ轴位置访问优化所有测试...");

            TestBasicRTZAccess();
            Console.WriteLine();

            TestCombinedRTZAccess();
            Console.WriteLine();

            TestFormattedStringMethods();
            Console.WriteLine();

            TestConsistencyWithOriginal();
            Console.WriteLine();

            TestPerformanceComparison();

            _logger.Info("所有UiViewModel RTZ轴位置访问优化测试运行完成");
        }
    }
}
