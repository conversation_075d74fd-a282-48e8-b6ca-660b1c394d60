- [ ] 确认InterLock统一按Code访问
- [ ] 位置方向解析需要添加上下偏差2个字段
- [ ] IO逻辑判断中跟实际MCU读出来True、False怎么保持一致，例如：低电平有效还是高电平有效，还是都有可能，不然要单读封装一个属性，专门用来InterLock判断
- [ ] IO口坏了，怎么处理，Modbus读过来是True还是False，影响InterLock的判断，添加启用/禁用功能
- [ ] DO上位机只是展示：不做任何处理
- [ ] Action Code 上的Level 指的指令级别，有什么用途
- [ ] alarm code额外预留中文翻译、问题原因
- [ ] Robot命令轴分类整理：1、轴分类；2、Smooth、Nose端分类；3、最后选择具体的腔体，比如：ChamberA、ChamberB、Cooling
- [ ] **subsystem configure Value值数字和字符混在一起，想办法解决**
- [ ] subsystem configure  范围考虑2个字段，上下限，还有非上下限
- [ ] subsystem status表中状态2大类：1、位置状态，简单办法根据当前RTZ值逐行方向解析(预留上下偏差判断) 2、IO判断解析后的逻辑值，使用计算属性动态获取
- [ ] Process chamber目前是一个，要区分ChamberA、ChamberB？应该区分吧


定义全局变量好还是单例待考虑，调用使用举例：
//IO调用
SS200.IOinterface.Robot.RDI1_枚举用途描述.Value（动态计算属性）
SS200.IOinterface.Robot.RDI1_枚举用途描述.Content（预先设置值）
SS200.IOinterface.ChamberA.PDI1_枚举用途描述.Value（动态计算属性）
SS200.IOinterface.ChamberB.PDI2_枚举用途描述.Content（预先设置值）
//报警调用
SS200.AlarmCode.Robt.RA1_枚举用途描述.Content（预先设置值）
SS200.AlarmCode.Robt.RA1_枚举用途描述.ChsContent（预先设置值）
SS200.AlarmCode.Robt.RA1_枚举用途描述.Cause（预先设置值）

//子系统配置调用
SS200.SubsystemConfigure.PositionValue.RP1_枚举用途描述.Value（预先设置值）
SS200.SubsystemConfigure.PositionValue.RP1_枚举用途描述.Range（预先设置值）

//子系统状态调用
SS200.SubsystemStatus.Robot.RS1_枚举用途描述.Value（动态计算属性）
SS200.SubsystemStatus.Robot.RS1_枚举用途描述.Content（预先设置值）

SS200.SubsystemStatus.ChamberA.PositonS. SlitDoorStatus.Open（动态计算属性，能尽量使用枚举，确保唯一性）
  

- [ ] InterLock怎么控制，比如一键启用，一键不启用，单独启用/禁用
- [ ] 