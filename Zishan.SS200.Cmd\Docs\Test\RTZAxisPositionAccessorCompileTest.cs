using System;
using log4net;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// RTZ轴位置访问器编译测试
    /// 验证新创建的RTZAxisPositionAccessor类是否能正常编译和基本功能
    /// </summary>
    public class RTZAxisPositionAccessorCompileTest
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RTZAxisPositionAccessorCompileTest));

        /// <summary>
        /// 测试RTZAxisPositionAccessor是否能正常实例化和访问
        /// </summary>
        public static void TestBasicCompilation()
        {
            try
            {
                _logger.Info("=== RTZ轴位置访问器编译测试 ===");

                // 测试通过SS200InterLockMain获取RTZAxisPosition
                var interlock = SS200InterLockMain.Instance;
                _logger.Info("✓ SS200InterLockMain实例获取成功");

                // 测试RTZAxisPosition属性是否存在
                var rtzPosition = interlock.RTZAxisPosition;
                if (rtzPosition != null)
                {
                    _logger.Info("✓ RTZAxisPosition属性存在且不为null");
                }
                else
                {
                    _logger.Error("✗ RTZAxisPosition属性为null");
                    return;
                }

                // 测试RTZAxisPosition的类型
                string typeName = rtzPosition.GetType().Name;
                if (typeName == "RTZAxisPositionAccessor")
                {
                    _logger.Info($"✓ RTZAxisPosition类型正确: {typeName}");
                }
                else
                {
                    _logger.Error($"✗ RTZAxisPosition类型错误: {typeName}");
                    return;
                }

                // 测试基本属性访问（不检查值，只检查是否能访问）
                _logger.Info("--- 测试基本属性访问 ---");
                
                try
                {
                    var tStep = rtzPosition.CurrentTAxisStep;
                    var rStep = rtzPosition.CurrentRAxisStep;
                    var zStep = rtzPosition.CurrentZAxisStep;
                    _logger.Info($"✓ 步进值属性访问成功: T={tStep}, R={rStep}, Z={zStep}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 步进值属性访问失败: {ex.Message}");
                }

                try
                {
                    var tDegree = rtzPosition.CurrentTAxisDegree;
                    var rLength = rtzPosition.CurrentRAxisLength;
                    var zHeight = rtzPosition.CurrentZAxisHeight;
                    _logger.Info($"✓ 物理值属性访问成功: T={tDegree:F2}°, R={rLength:F2}mm, Z={zHeight:F2}mm");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 物理值属性访问失败: {ex.Message}");
                }

                // 测试数据有效性检查
                try
                {
                    bool isValid = rtzPosition.IsRTZPositionDataValid;
                    _logger.Info($"✓ 数据有效性检查成功: {isValid}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 数据有效性检查失败: {ex.Message}");
                }

                // 测试安全检查功能
                try
                {
                    bool allSafe = rtzPosition.AreAllAxesInSafeRange;
                    bool tSafe = rtzPosition.IsAxisPositionInSafeRange("T");
                    bool rSafe = rtzPosition.IsAxisPositionInSafeRange("R");
                    bool zSafe = rtzPosition.IsAxisPositionInSafeRange("Z");
                    _logger.Info($"✓ 安全检查功能成功: 全部={allSafe}, T={tSafe}, R={rSafe}, Z={zSafe}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 安全检查功能失败: {ex.Message}");
                }

                // 测试组合访问方法
                try
                {
                    var (t, r, z) = rtzPosition.GetCurrentRTZSteps();
                    _logger.Info($"✓ 步进值组合访问成功: T={t}, R={r}, Z={z}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 步进值组合访问失败: {ex.Message}");
                }

                try
                {
                    var (tDeg, rLen, zHeight) = rtzPosition.GetCurrentRTZPhysicalValues();
                    _logger.Info($"✓ 物理值组合访问成功: T={tDeg:F2}°, R={rLen:F2}mm, Z={zHeight:F2}mm");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 物理值组合访问失败: {ex.Message}");
                }

                // 测试格式化显示方法
                try
                {
                    string displayText = rtzPosition.GetRTZPositionDisplayText();
                    string simpleText = rtzPosition.GetRTZPositionSimpleText();
                    string jsonText = rtzPosition.GetRTZPositionJsonText();
                    
                    _logger.Info($"✓ 格式化显示方法成功:");
                    _logger.Info($"  详细文本长度: {displayText?.Length ?? 0}");
                    _logger.Info($"  简化文本长度: {simpleText?.Length ?? 0}");
                    _logger.Info($"  JSON文本长度: {jsonText?.Length ?? 0}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 格式化显示方法失败: {ex.Message}");
                }

                // 测试详细信息获取
                try
                {
                    var detailedInfo = rtzPosition.GetDetailedPositionInfo();
                    string diagnosticInfo = rtzPosition.GetDiagnosticInfo();
                    
                    _logger.Info($"✓ 详细信息获取成功:");
                    _logger.Info($"  详细信息有效性: {detailedInfo?.IsValid ?? false}");
                    _logger.Info($"  诊断信息长度: {diagnosticInfo?.Length ?? 0}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ 详细信息获取失败: {ex.Message}");
                }

                // 测试RTZAxisPositionInfo类
                try
                {
                    var invalidInfo = RTZAxisPositionInfo.CreateInvalid("测试错误");
                    var validInfo = RTZAxisPositionInfo.CreateValid(1000, 2000, 3000, 3.6, 100.5, 15.0, true, true, true);
                    
                    _logger.Info($"✓ RTZAxisPositionInfo类测试成功:");
                    _logger.Info($"  无效信息: {invalidInfo.IsValid}");
                    _logger.Info($"  有效信息: {validInfo.IsValid}");
                    _logger.Info($"  ToString测试: {validInfo.ToString()}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"✗ RTZAxisPositionInfo类测试失败: {ex.Message}");
                }

                _logger.Info("=== RTZ轴位置访问器编译测试完成 ===");
                _logger.Info("✓ 所有基本功能编译和访问测试通过");
            }
            catch (Exception ex)
            {
                _logger.Error($"RTZ轴位置访问器编译测试失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 运行所有编译测试
        /// </summary>
        public static void RunAllCompileTests()
        {
            try
            {
                _logger.Info("开始运行RTZ轴位置访问器编译测试...");
                TestBasicCompilation();
                _logger.Info("RTZ轴位置访问器编译测试全部完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"运行编译测试时发生错误: {ex.Message}", ex);
            }
        }
    }
}
