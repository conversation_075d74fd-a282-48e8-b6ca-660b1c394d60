using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.Generic;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle
{
    /// <summary>
    /// Shuttle槽位状态管理器
    /// 管理所有槽位的晶圆状态 (LSS1-LSS16, LSSW1-LSSW4)
    /// </summary>
    public partial class ShuttleSlotStatusManager : ObservableObject
    {
        #region Shuttle1、Shuttle2晶圆盒Slot有无状态 (slot status) - LSS1-LSS4

        /// <summary>
        /// Shuttle1晶圆盒1槽位状态字典 (LSS1 xx)
        /// Key: 槽位号(xx), Value: 晶圆状态
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> shuttle1Cassette1SlotStatus = new();

        /// <summary>
        /// Shuttle1晶圆盒2槽位状态字典 (LSS2 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> shuttle1Cassette2SlotStatus = new();

        /// <summary>
        /// Shuttle2晶圆盒1槽位状态字典 (LSS3 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> shuttle2Cassette1SlotStatus = new();

        /// <summary>
        /// Shuttle2晶圆盒2槽位状态字典 (LSS4 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> shuttle2Cassette2SlotStatus = new();

        #endregion Shuttle1、Shuttle2晶圆盒Slot有无状态 (slot status) - LSS1-LSS4

        #region Robot双臂晶圆Slot有无状态 (slot status) - LSS4-LSS8

        /// <summary>
        /// Smooth P1槽位状态字典 (LSS5 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> smoothP1SlotStatus = new();

        /// <summary>
        /// Smooth P2槽位状态字典 (LSS6 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> smoothP2SlotStatus = new();

        /// <summary>
        /// Nose P1槽位状态字典 (LSS7 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> noseP1SlotStatus = new();

        /// <summary>
        /// Nose P2槽位状态字典 (LSS8 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> noseP2SlotStatus = new();

        #endregion Robot双臂晶圆Slot有无状态 (slot status) - LSS4-LSS8

        #region 腔体晶圆Slot有无状态 ( (chamber slot status) - LSS9-LSS16

        /// <summary>
        /// CHAL晶圆状态 (LSS9)
        /// </summary>
        [ObservableProperty]
        private EnuSlotWaferStatus chalWaferStatus = EnuSlotWaferStatus.NoWafer;

        /// <summary>
        /// CHAR晶圆状态 (LSS10)
        /// </summary>
        [ObservableProperty]
        private EnuSlotWaferStatus charWaferStatus = EnuSlotWaferStatus.NoWafer;

        /// <summary>
        /// CHBL晶圆状态 (LSS11)
        /// </summary>
        [ObservableProperty]
        private EnuSlotWaferStatus chblWaferStatus = EnuSlotWaferStatus.NoWafer;

        /// <summary>
        /// CHBR晶圆状态 (LSS12)
        /// </summary>
        [ObservableProperty]
        private EnuSlotWaferStatus chbrWaferStatus = EnuSlotWaferStatus.NoWafer;

        /// <summary>
        /// CTL晶圆状态 (LSS13)
        /// </summary>
        [ObservableProperty]
        private EnuSlotWaferStatus ctlWaferStatus = EnuSlotWaferStatus.NoWafer;

        /// <summary>
        /// CTR晶圆状态 (LSS14)
        /// </summary>
        [ObservableProperty]
        private EnuSlotWaferStatus ctrWaferStatus = EnuSlotWaferStatus.NoWafer;

        /// <summary>
        /// CBL晶圆状态 (LSS15)
        /// </summary>
        [ObservableProperty]
        private EnuSlotWaferStatus cblWaferStatus = EnuSlotWaferStatus.NoWafer;

        /// <summary>
        /// CBR晶圆状态 (LSS16)
        /// </summary>
        [ObservableProperty]
        private EnuSlotWaferStatus cbrWaferStatus = EnuSlotWaferStatus.NoWafer;

        #endregion 腔体晶圆Slot有无状态 ( (chamber slot status) - LSS9-LSS16

        #region 传输晶圆Slot有无状态 ( (wafer status) - LSSW1-LSSW4

        /// <summary>
        /// Shuttle1左晶圆盒槽位状态字典 (LSSW1 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> shuttle1LeftCassetteWaferStatus = new();

        /// <summary>
        /// Shuttle1右晶圆盒槽位状态字典 (LSSW2 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> shuttle1RightCassetteWaferStatus = new();

        /// <summary>
        /// Shuttle2左晶圆盒槽位状态字典 (LSSW3 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> shuttle2LeftCassetteWaferStatus = new();

        /// <summary>
        /// Shuttle2右晶圆盒槽位状态字典 (LSSW4 xx)
        /// </summary>
        [ObservableProperty]
        private Dictionary<int, EnuSlotWaferStatus> shuttle2RightCassetteWaferStatus = new();

        #endregion 传输晶圆Slot有无状态 ( (wafer status) - LSSW1-LSSW4

        /// <summary>
        /// 构造函数 - 初始化所有字典
        /// </summary>
        public ShuttleSlotStatusManager()
        {
            InitializeSlotDictionaries();
        }

        /// <summary>
        /// 初始化所有槽位字典
        /// </summary>
        private void InitializeSlotDictionaries()
        {
            // 假设每个晶圆盒有25个槽位 (可根据实际情况调整)
            const int maxSlots = 25;

            for (int i = Golbal.CassetteSlotMax; i <= Golbal.CassetteSlotMax; i++)
            {
                Shuttle1Cassette1SlotStatus[i] = EnuSlotWaferStatus.NoWafer;
                Shuttle1Cassette2SlotStatus[i] = EnuSlotWaferStatus.NoWafer;
                Shuttle2Cassette1SlotStatus[i] = EnuSlotWaferStatus.NoWafer;
                Shuttle2Cassette2SlotStatus[i] = EnuSlotWaferStatus.NoWafer;
                SmoothP1SlotStatus[i] = EnuSlotWaferStatus.NoWafer;
                SmoothP2SlotStatus[i] = EnuSlotWaferStatus.NoWafer;
                NoseP1SlotStatus[i] = EnuSlotWaferStatus.NoWafer;
                NoseP2SlotStatus[i] = EnuSlotWaferStatus.NoWafer;
                Shuttle1LeftCassetteWaferStatus[i] = EnuSlotWaferStatus.NoWafer;
                Shuttle1RightCassetteWaferStatus[i] = EnuSlotWaferStatus.NoWafer;
                Shuttle2LeftCassetteWaferStatus[i] = EnuSlotWaferStatus.NoWafer;
                Shuttle2RightCassetteWaferStatus[i] = EnuSlotWaferStatus.NoWafer;
            }
        }
    }
}