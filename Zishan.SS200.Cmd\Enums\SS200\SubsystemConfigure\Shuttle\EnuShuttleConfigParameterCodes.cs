﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Shuttle
{
    /// <summary>
    /// Shuttle 设置参数设置代码枚举类型
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuShuttleConfigParameterCodes
    {
        /// <summary>
        /// cassette nest extend/retract min time
        /// </summary>
        [Description("cassette nest extend/retract min time")]
        SPS1 = 0,

        /// <summary>
        /// cassette nest extend/retract max time
        /// </summary>
        [Description("cassette nest extend/retract max time")]
        SPS2 = 1,

        /// <summary>
        /// shuttle up/down min time
        /// </summary>
        [Description("shuttle up/down min time")]
        SPS3 = 2,

        /// <summary>
        /// shuttle up/down max time
        /// </summary>
        [Description("shuttle up/down max time")]
        SPS4 = 3,

        /// <summary>
        /// shuttle rotate max time
        /// </summary>
        [Description("shuttle rotate max time")]
        SPS5 = 4,

        /// <summary>
        /// cassette door motion min time
        /// </summary>
        [Description("cassette door motion min time")]
        SPS6 = 5,

        /// <summary>
        /// cassette door motion max time
        /// </summary>
        [Description("cassette door motion max time")]
        SPS7 = 6,

        /// <summary>
        /// spare
        /// </summary>
        [Description("spare")]
        SPS8 = 7,

        /// <summary>
        /// ISO valve motion max time
        /// </summary>
        [Description("ISO valve motion max time")]
        SPS9 = 8,

        /// <summary>
        /// slide out front sensor enable
        /// </summary>
        [Description("slide out front sensor enable")]
        SPS10 = 9,

        /// <summary>
        /// slide out back sensor enable
        /// </summary>
        [Description("slide out back sensor enable")]
        SPS11 = 10,

        /// <summary>
        /// shuttle transfer pressure
        /// </summary>
        [Description("shuttle transfer pressure")]
        SPS12 = 11,

        /// <summary>
        /// delta pressure for shuttle up/down
        /// </summary>
        [Description("delta pressure for shuttle up/down")]
        SPS13 = 12,

        /// <summary>
        /// loadlock ATM pressure minimun
        /// </summary>
        [Description("loadlock ATM pressure minimun")]
        SPS14 = 13,

        /// <summary>
        /// shuttle ATM pressure minimun
        /// </summary>
        [Description("shuttle ATM pressure minimun")]
        SPS15 = 14,

        /// <summary>
        /// transfer rate
        /// </summary>
        [Description("transfer rate")]
        SPS16 = 15,

        /// <summary>
        /// pump down shuttle max time
        /// </summary>
        [Description("pump down shuttle max time")]
        SPS17 = 16,

        /// <summary>
        /// loadlock ATM pressure setpoint
        /// </summary>
        [Description("loadlock ATM pressure setpoint")]
        SPS18 = 17,

        /// <summary>
        /// shuttle ATM pressure minimun setpoint
        /// </summary>
        [Description("shuttle ATM pressure minimun setpoint")]
        SPS19 = 18,

        /// <summary>
        /// shuttle pressure offset
        /// </summary>
        [Description("shuttle pressure offset")]
        SPS20 = 19,

        /// <summary>
        /// loadlock pressure offset
        /// </summary>
        [Description("loadlock pressure offset")]
        SPS21 = 20,

        /// <summary>
        /// shuttle backfill max time
        /// </summary>
        [Description("shuttle backfill max time")]
        SPS22 = 21,

        /// <summary>
        /// loadlock backfill max time
        /// </summary>
        [Description("loadlock backfill max time")]
        SPS23 = 22
    }
}