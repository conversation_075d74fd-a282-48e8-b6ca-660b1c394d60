# AppLog迁移到ILog模式指南

## 概述

本文档说明如何将代码中的全局AppLog.cs使用改为私有ILog实例模式，提高代码的可维护性和测试性。

## 迁移原因

1. **避免全局依赖**：减少对静态类的依赖，提高代码的可测试性
2. **更好的日志隔离**：每个类有自己的日志实例，便于日志分类和过滤
3. **符合最佳实践**：遵循依赖注入和面向对象设计原则
4. **准备删除AppLog.cs**：为后续删除全局AppLog.cs做准备

## 标准ILog模式

### 推荐的实现方式

```csharp
using log4net;

public class YourClassName
{
    // 在类的字段区域添加私有只读ILog实例
    private readonly ILog _logger = LogManager.GetLogger(typeof(YourClassName));
    
    public void SomeMethod()
    {
        try
        {
            // 使用_logger替代AppLog
            _logger.Info("操作开始");
            
            // 业务逻辑
            
            _logger.Info("操作完成");
        }
        catch (Exception ex)
        {
            _logger.Error("操作失败", ex);
            throw;
        }
    }
}
```

## 迁移对照表

### AppLog方法到ILog方法的映射

| AppLog方法 | ILog方法 | 说明 |
|-----------|----------|------|
| `AppLog.Debug(message)` | `_logger.Debug(message)` | 调试信息 |
| `AppLog.Info(message)` | `_logger.Info(message)` | 一般信息 |
| `AppLog.Warn(message)` | `_logger.Warn(message)` | 警告信息 |
| `AppLog.Error(message, ex)` | `_logger.Error(message, ex)` | 错误信息 |
| `AppLog.InfoFormatted(format, args)` | `_logger.InfoFormat(format, args)` | 格式化信息 |
| `AppLog.DebugFormatted(format, args)` | `_logger.DebugFormat(format, args)` | 格式化调试 |
| `AppLog.WarnFormatted(format, args)` | `_logger.WarnFormat(format, args)` | 格式化警告 |

## 具体迁移示例

### 示例1：PubHelper.cs迁移

**迁移前：**
```csharp
public static class PubHelper
{
    public static List<string> GetRecipeList(string path, string searchPattern)
    {
        // ...
        if (lstFileNameWithoutExt.Count > 1)
        {
            var filePaths = files.Where(file => lstFileNameWithoutExt.Contains(Path.GetFileNameWithoutExtension(file)));
            AppLog.Error("找到多个有效的配方文件名: " + string.Join(", ", filePaths));
        }
        // ...
    }
}
```

**迁移后：**
```csharp
public static class PubHelper
{
    private static readonly ILog _logger = LogManager.GetLogger(typeof(PubHelper));
    
    public static List<string> GetRecipeList(string path, string searchPattern)
    {
        // ...
        if (lstFileNameWithoutExt.Count > 1)
        {
            var filePaths = files.Where(file => lstFileNameWithoutExt.Contains(Path.GetFileNameWithoutExtension(file)));
            _logger.Error("找到多个有效的配方文件名: " + string.Join(", ", filePaths));
        }
        // ...
    }
}
```

### 示例2：LogViewModel.cs迁移

**迁移前：**
```csharp
public partial class LogViewModel : ObservableObject
{
    private readonly ILog _logger = LogManager.GetLogger(typeof(LogViewModel));
    
    public void AddLog(string log)
    {
        // ...
        AppLog.Info(logModel.Message);
        // ...
    }
    
    [RelayCommand]
    private void CopyMessageToClipboard(string message)
    {
        try
        {
            Clipboard.SetText(message);
            AppLog.Info($"已复制消息到剪贴板: {message}");
        }
        catch (Exception ex)
        {
            AppLog.Error($"复制到剪贴板失败: {ex.Message}");
        }
    }
}
```

**迁移后：**
```csharp
public partial class LogViewModel : ObservableObject
{
    private readonly ILog _logger = LogManager.GetLogger(typeof(LogViewModel));
    
    public void AddLog(string log)
    {
        // ...
        _logger.Info(logModel.Message);
        // ...
    }
    
    [RelayCommand]
    private void CopyMessageToClipboard(string message)
    {
        try
        {
            Clipboard.SetText(message);
            _logger.Info($"已复制消息到剪贴板: {message}");
        }
        catch (Exception ex)
        {
            _logger.Error($"复制到剪贴板失败: {ex.Message}", ex);
        }
    }
}
```

### 示例3：UILogService.cs迁移

**迁移前：**
```csharp
public static class UILogService
{
    private static readonly ILog _logger = LogManager.GetLogger(typeof(UILogService));
    
    public static void AddLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
    {
        if (_LogViewModel == null)
        {
            AppLog.Info(formattedMessage);
            return;
        }
        
        // ...
        if (!blUiAdd)
        {
            AppLog.Info(logModel.Message);
        }
        // ...
        catch (Exception ex)
        {
            _logger.Error($"添加UI日志失败: {ex.Message}", ex);
            AppLog.Error($"添加UI日志失败: {ex.Message}", ex);
        }
    }
}
```

**迁移后：**
```csharp
public static class UILogService
{
    private static readonly ILog _logger = LogManager.GetLogger(typeof(UILogService));
    
    public static void AddLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
    {
        if (_LogViewModel == null)
        {
            _logger.Info(formattedMessage);
            return;
        }
        
        // ...
        if (!blUiAdd)
        {
            _logger.Info(logModel.Message);
        }
        // ...
        catch (Exception ex)
        {
            _logger.Error($"添加UI日志失败: {ex.Message}", ex);
        }
    }
}
```

## 迁移步骤

### 第一步：添加ILog字段
在每个使用AppLog的类中添加私有ILog字段：
```csharp
private readonly ILog _logger = LogManager.GetLogger(typeof(YourClassName));
```

### 第二步：替换AppLog调用
将所有AppLog.方法调用替换为_logger.方法调用

### 第三步：处理异常日志
确保异常日志正确传递Exception对象：
```csharp
// 正确方式
_logger.Error("错误信息", ex);

// 避免这样做
_logger.Error($"错误信息: {ex.Message}");
```

### 第四步：测试验证
确保迁移后的日志功能正常工作

## 需要迁移的文件列表

根据代码分析，以下文件需要进行AppLog到ILog的迁移：

1. `Common/PubHelper.cs` - 1处AppLog.Error调用
2. `ViewModels/Dock/LogViewModel.cs` - 3处AppLog调用
3. `Services/UILogService.cs` - 3处AppLog调用  
4. `ViewModels/TransferWaferViewModel.cs` - 1处AppLog.Debug调用
5. `Common/PerformanceAnalyzer.cs` - 2处AppLog调用
6. `Common/StopwatchHelper.cs` - 1处AppLog.Error调用
7. `App.xaml.cs` - 1处AppLog.Error调用

## 注意事项

1. **保持日志级别一致**：确保迁移后的日志级别与原来相同
2. **异常处理**：正确传递Exception对象到Error方法
3. **格式化字符串**：使用ILog的Format方法而不是字符串插值
4. **静态类处理**：静态类中使用static readonly ILog字段
5. **测试覆盖**：迁移后进行充分测试

## 迁移后的优势

1. **更好的日志分类**：每个类的日志都有明确的来源标识
2. **便于调试**：可以针对特定类设置不同的日志级别
3. **提高可测试性**：可以通过依赖注入模拟日志行为
4. **符合SOLID原则**：减少对全局静态类的依赖

## 后续计划

完成所有AppLog迁移后，可以安全删除`Common/AppLog.cs`文件。
