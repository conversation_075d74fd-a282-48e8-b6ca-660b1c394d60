﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.ChamberA
{
    /// <summary>
    /// Chamber A 设置参数设置代码枚举类型
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuChaConfigParameterCodes
    {
        /// <summary>
        /// slit door motion min time
        /// </summary>
        [Description("slit door motion min time")]
        PPS1 = 0,

        /// <summary>
        /// slit door motion max time
        /// </summary>
        [Description("slit door motion max time")]
        PPS2 = 1,

        /// <summary>
        /// lift pin motion min time
        /// </summary>
        [Description("lift pin motion min time")]
        PPS3 = 2,

        /// <summary>
        /// lift pin motion max time
        /// </summary>
        [Description("lift pin motion max time")]
        PPS4 = 3,

        /// <summary>
        /// max delta pressure for open slit door
        /// </summary>
        [Description("max delta pressure for open slit door")]
        PPS5 = 4,

        /// <summary>
        /// chamber A process vacuum pressure
        /// </summary>
        [Description("chamber A process vacuum pressure")]
        PPS6 = 5,

        /// <summary>
        /// loadlock process vacuum pressure (PAI7 read)
        /// </summary>
        [Description("loadlock process vacuum pressure (PAI7 read)")]
        PPS7 = 6,

        /// <summary>
        /// MFC1 flow range
        /// </summary>
        [Description("MFC1 flow range")]
        PPS8 = 7,

        /// <summary>
        /// MFC2 flow range
        /// </summary>
        [Description("MFC2 flow range")]
        PPS9 = 8,

        /// <summary>
        /// MFC3 flow range
        /// </summary>
        [Description("MFC3 flow range")]
        PPS10 = 9,

        /// <summary>
        /// MFC4 flow range
        /// </summary>
        [Description("MFC4 flow range")]
        PPS11 = 10,

        /// <summary>
        /// fault for MFC1 flow deviation
        /// </summary>
        [Description("fault for MFC1 flow deviation")]
        PPS12 = 11,

        /// <summary>
        /// fault for MFC2 flow deviation
        /// </summary>
        [Description("fault for MFC2 flow deviation")]
        PPS13 = 12,

        /// <summary>
        /// fault for MFC3 flow deviation
        /// </summary>
        [Description("fault for MFC3 flow deviation")]
        PPS14 = 13,

        /// <summary>
        /// fault for MFC4 flow deviation
        /// </summary>
        [Description("fault for MFC4 flow deviation")]
        PPS15 = 14,

        /// <summary>
        /// max time for gas flow stable
        /// </summary>
        [Description("max time for gas flow stable")]
        PPS16 = 15,

        /// <summary>
        /// control line for MFC1 flow
        /// </summary>
        [Description("control line for MFC1 flow")]
        PPS17 = 16,

        /// <summary>
        /// control line for MFC2 flow
        /// </summary>
        [Description("control line for MFC2 flow")]
        PPS18 = 17,

        /// <summary>
        /// control line for MFC3 flow
        /// </summary>
        [Description("control line for MFC3 flow")]
        PPS19 = 18,

        /// <summary>
        /// control line for MFC4 flow
        /// </summary>
        [Description("control line for MFC4 flow")]
        PPS20 = 19,

        /// <summary>
        /// point for MFC flow out of control line/10point (2points/s)
        /// </summary>
        [Description("point for MFC flow out of control line/10point (2points/s)")]
        PPS21 = 20,

        /// <summary>
        /// chamber A pressure gauge range
        /// </summary>
        [Description("chamber A pressure gauge range")]
        PPS22 = 21,

        /// <summary>
        /// loadlock pressure gauge range
        /// </summary>
        [Description("loadlock pressure gauge range")]
        PPS23 = 22,

        /// <summary>
        /// fault for pressure flow deviation (setpoint)
        /// </summary>
        [Description("fault for pressure flow deviation (setpoint)")]
        PPS24 = 23,

        /// <summary>
        /// max time for pressure control
        /// </summary>
        [Description("max time for pressure control")]
        PPS25 = 24,

        /// <summary>
        /// control line for pressure control (setpoint)
        /// </summary>
        [Description("control line for pressure control (setpoint)")]
        PPS26 = 25,

        /// <summary>
        /// point for pressure control out of control line/10point
        /// </summary>
        [Description("point for pressure control out of control line/10point")]
        PPS27 = 26,

        /// <summary>
        /// max time for temperature servo
        /// </summary>
        [Description("max time for temperature servo")]
        PPS28 = 27,

        /// <summary>
        /// low temperature deviation
        /// </summary>
        [Description("low temperature deviation")]
        PPS29 = 28,

        /// <summary>
        /// high temperature deviation
        /// </summary>
        [Description("high temperature deviation")]
        PPS30 = 29,

        /// <summary>
        /// chamber temperature deviation for wafer revcieve
        /// </summary>
        [Description("chamber temperature deviation for wafer revcieve")]
        PPS31 = 30,

        /// <summary>
        /// control line for chamber temperature
        /// </summary>
        [Description("control line for chamber temperature")]
        PPS32 = 31,

        /// <summary>
        /// point for temperature out of control line/10point
        /// </summary>
        [Description("point for temperature out of control line/10point")]
        PPS33 = 32,

        /// <summary>
        /// throttle valve motion max time
        /// </summary>
        [Description("throttle valve motion max time")]
        PPS34 = 33,

        /// <summary>
        /// RF forward power deviation fault
        /// </summary>
        [Description("RF forward power deviation fault")]
        PPS35 = 34,

        /// <summary>
        /// RF reflecter out of limit
        /// </summary>
        [Description("RF reflecter out of limit")]
        PPS36 = 35,

        /// <summary>
        /// point for RF forward power out of control line/10point
        /// </summary>
        [Description("point for RF forward power out of control line/10point")]
        PPS37 = 36,

        /// <summary>
        /// point for RF reflecter power out of control line/10point
        /// </summary>
        [Description("point for RF reflecter power out of control line/10point")]
        PPS38 = 37,

        /// <summary>
        /// RF forward power control line
        /// </summary>
        [Description("RF forward power control line")]
        PPS39 = 38,

        /// <summary>
        /// RF reflector power control line
        /// </summary>
        [Description("RF reflector power control line")]
        PPS40 = 39,

        /// <summary>
        /// plasma on check
        /// </summary>
        [Description("plasma on check")]
        PPS41 = 40,

        /// <summary>
        /// max time for RF stable
        /// </summary>
        [Description("max time for RF stable")]
        PPS42 = 41,

        /// <summary>
        /// point for plasma on/off tripped of control line/10point
        /// </summary>
        [Description("point for plasma on/off tripped of control line/10point")]
        PPS43 = 42,

        /// <summary>
        /// max time for process chamber pump down
        /// </summary>
        [Description("max time for process chamber pump down")]
        PPS44 = 43,

        /// <summary>
        /// active chamber
        /// </summary>
        [Description("active chamber")]
        PPS45 = 44,

        /// <summary>
        /// process chamber pumping down pressure deviation
        /// </summary>
        [Description("process chamber pumping down pressure deviation")]
        PPS46 = 45,
    }
}