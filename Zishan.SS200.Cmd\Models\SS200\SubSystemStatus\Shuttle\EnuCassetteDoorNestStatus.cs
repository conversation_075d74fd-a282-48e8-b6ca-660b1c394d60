using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle
{
    /// <summary>
    /// 晶圆盒门和巢状态枚举 (SSD8-SSD13)
    /// 注意：SSD8~SSD13状态不能共存，且根据SSC6配置有所不同
    /// SSC6=SMIF: SSD8-SSD13全部有效
    /// SSC6=FIXED: 仅SSD8-SSD10有效，SSD11-SSD13为N/A
    /// </summary>
    public enum EnuCassetteDoorNestStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 晶圆盒门打开/晶圆盒巢伸出 (SSD8: DI1=1 DI2=0 DI13=0 DI4=1 DI5=1) [SSC6=SMIF]
        /// 晶圆盒门打开 (SSD8: DI1=1 DI2=0) [SSC6=FIXED]
        /// </summary>
        [Description("晶圆盒门打开")]
        CassetteDoorOpen = 1,

        /// <summary>
        /// 晶圆盒门关闭/晶圆盒巢收回 (SSD9: DI1=0 DI2=1 DI13=1 DI4=0 DI5=0) [SSC6=SMIF]
        /// 晶圆盒门关闭 (SSD9: DI1=0 DI2=1) [SSC6=FIXED]
        /// </summary>
        [Description("晶圆盒门关闭")]
        CassetteDoorClose = 2,

        /// <summary>
        /// 晶圆盒门在上下位置之间/晶圆盒巢收回 (SSD10: DI1=0 DI2=0 DI13=1 DI4=0 DI5=0) [SSC6=SMIF]
        /// 晶圆盒门在上下位置之间 (SSD10: DI1=0 DI2=0) [SSC6=FIXED]
        /// </summary>
        [Description("晶圆盒门在上下位置之间")]
        CassetteDoorBetween = 3,

        /// <summary>
        /// 晶圆盒门打开/晶圆盒巢收回 (SSD11: DI1=1 DI2=0 DI13=1 DI4=0 DI5=0)
        /// 注意：仅在SSC6=SMIF时有效，SSC6=FIXED时为N/A
        /// </summary>
        [Description("晶圆盒门打开/晶圆盒巢收回")]
        CassetteDoorOpenNestRetract = 4,

        /// <summary>
        /// 晶圆盒巢在伸出收回位置之间1/晶圆盒门打开 (SSD12: DI1=1 DI2=0 DI13=1 DI4=1 DI5=1)
        /// 注意：仅在SSC6=SMIF时有效，SSC6=FIXED时为N/A
        /// </summary>
        [Description("晶圆盒巢在伸出收回位置之间1")]
        CassetteNestBetween1 = 5,

        /// <summary>
        /// 晶圆盒巢在伸出收回位置之间2/晶圆盒门打开 (SSD13: DI1=1 DI2=0 DI13=1 DI4=1 DI5=0)
        /// 注意：仅在SSC6=SMIF时有效，SSC6=FIXED时为N/A
        /// </summary>
        [Description("晶圆盒巢在伸出收回位置之间2")]
        CassetteNestBetween2 = 6
    }
}
