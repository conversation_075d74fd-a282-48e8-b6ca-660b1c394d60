using System;
using <PERSON>ishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Models.IR400;

namespace Zishan.SS200.Cmd.Test
{
    /// <summary>
    /// GetFinishedChamberWithWafer方法测试代码
    /// </summary>
    public class GetFinishedChamberWithWaferTest
    {
        /// <summary>
        /// 测试GetFinishedChamberWithWafer方法的各种场景
        /// </summary>
        public static void RunTests()
        {
            var simulation = new PLcsignalSimulation();
            
            Console.WriteLine("=== GetFinishedChamberWithWafer方法测试 ===\n");

            // 测试场景1：单腔体配方B，CHB有晶圆且已完成
            TestScenario1(simulation);
            
            // 测试场景2：多腔体配方AB，CHA先完成但无晶圆，CHB后完成且有晶圆
            TestScenario2(simulation);
            
            // 测试场景3：多腔体配方AB，两个腔体都有晶圆且已完成
            TestScenario3(simulation);
            
            // 测试场景4：用户报告的问题场景
            TestUserReportedScenario(simulation);
        }

        /// <summary>
        /// 测试场景1：单腔体配方B，CHB有晶圆且已完成
        /// </summary>
        private static void TestScenario1(PLcsignalSimulation simulation)
        {
            Console.WriteLine("测试场景1：单腔体配方B，CHB有晶圆且已完成");
            
            // 重置状态
            ResetSimulation(simulation);
            
            // 设置CHB状态
            simulation.ChbHasWafer = true;
            simulation.ChbProcessFinished = true;
            simulation.ChbDateTimeProcessFinished = DateTime.Now;
            
            // 测试
            var result = simulation.GetFinishedChamberWithWafer("配方B");
            Console.WriteLine($"配方B -> 期望: CHB, 实际: {result}");
            Console.WriteLine($"测试结果: {(result == EnuChamberName.CHB ? "通过" : "失败")}\n");
        }

        /// <summary>
        /// 测试场景2：多腔体配方AB，CHA先完成但无晶圆，CHB后完成且有晶圆
        /// </summary>
        private static void TestScenario2(PLcsignalSimulation simulation)
        {
            Console.WriteLine("测试场景2：多腔体配方AB，CHA先完成但无晶圆，CHB后完成且有晶圆");
            
            // 重置状态
            ResetSimulation(simulation);
            
            // 设置CHA状态（先完成但无晶圆）
            simulation.ChaHasWafer = false;
            simulation.ChaProcessFinished = true;
            simulation.ChaDateTimeProcessFinished = DateTime.Now.AddMinutes(-10); // 10分钟前完成
            
            // 设置CHB状态（后完成但有晶圆）
            simulation.ChbHasWafer = true;
            simulation.ChbProcessFinished = true;
            simulation.ChbDateTimeProcessFinished = DateTime.Now; // 刚完成
            
            // 测试新方法
            var result = simulation.GetFinishedChamberWithWafer("配方AB");

            Console.WriteLine($"配方AB -> 期望: CHB, 实际: {result}");
            Console.WriteLine($"测试结果: {(result == EnuChamberName.CHB ? "通过" : "失败")}");
            Console.WriteLine($"说明: 优先返回有晶圆且已完成的腔体，而不是最早完成的腔体\n");
        }

        /// <summary>
        /// 测试场景3：多腔体配方AB，两个腔体都有晶圆且已完成
        /// </summary>
        private static void TestScenario3(PLcsignalSimulation simulation)
        {
            Console.WriteLine("测试场景3：多腔体配方AB，两个腔体都有晶圆且已完成");
            
            // 重置状态
            ResetSimulation(simulation);
            
            // 设置CHA状态
            simulation.ChaHasWafer = true;
            simulation.ChaProcessFinished = true;
            simulation.ChaDateTimeProcessFinished = DateTime.Now.AddMinutes(-5);
            
            // 设置CHB状态
            simulation.ChbHasWafer = true;
            simulation.ChbProcessFinished = true;
            simulation.ChbDateTimeProcessFinished = DateTime.Now;
            
            // 测试（应该优先返回CHB，因为在方法中CHB检查在前）
            var result = simulation.GetFinishedChamberWithWafer("配方AB");
            Console.WriteLine($"配方AB -> 期望: CHB（优先级）, 实际: {result}");
            Console.WriteLine($"测试结果: {(result == EnuChamberName.CHB ? "通过" : "失败")}\n");
        }

        /// <summary>
        /// 测试场景4：用户报告的问题场景
        /// Top=7, Bottom=6，最后一片在B腔体
        /// </summary>
        private static void TestUserReportedScenario(PLcsignalSimulation simulation)
        {
            Console.WriteLine("测试场景4：用户报告的问题场景（Top=7, Bottom=6，最后一片在B腔体）");
            
            // 重置状态
            ResetSimulation(simulation);
            
            // 模拟用户场景：使用配方AB，最后一片在B腔体
            simulation.ChaHasWafer = false; // CHA已经处理完，无晶圆
            simulation.ChaProcessFinished = true;
            simulation.ChaDateTimeProcessFinished = DateTime.Now.AddMinutes(-30); // 30分钟前完成
            
            simulation.ChbHasWafer = true; // CHB有最后一片晶圆
            simulation.ChbProcessFinished = true;
            simulation.ChbDateTimeProcessFinished = DateTime.Now; // 刚完成
            
            // 测试条件判断（模拟TransferWaferViewModel中的逻辑）
            string recipeName = "配方AB"; // 包含"B"
            bool chbProcessFinished = simulation.ChbProcessFinished ?? false;
            bool chbHasWafer = simulation.ChbHasWafer;
            var finishedChamber = simulation.GetFinishedChamberWithWafer(recipeName);
            
            bool conditionMet = recipeName.Contains("B") && 
                               chbProcessFinished && 
                               chbHasWafer && 
                               finishedChamber == EnuChamberName.CHB;
            
            Console.WriteLine($"配方: {recipeName}");
            Console.WriteLine($"CHB已完成: {chbProcessFinished}");
            Console.WriteLine($"CHB有晶圆: {chbHasWafer}");
            Console.WriteLine($"GetFinishedChamberWithWafer返回: {finishedChamber}");
            Console.WriteLine($"条件判断结果: {(conditionMet ? "满足，程序继续运行" : "不满足，程序停止")}");
            Console.WriteLine($"修复效果: {(conditionMet ? "成功" : "失败")}\n");
        }

        /// <summary>
        /// 重置模拟器状态
        /// </summary>
        private static void ResetSimulation(PLcsignalSimulation simulation)
        {
            simulation.ChaHasWafer = false;
            simulation.ChaProcessFinished = null;
            simulation.ChbHasWafer = false;
            simulation.ChbProcessFinished = null;
            simulation.ChcHasWafer = false;
            simulation.ChcProcessFinished = null;
        }
    }
}
