using System;
using System.Collections.ObjectModel;
using System.Runtime.CompilerServices;
using System.Windows;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.ViewModels;
using Zishan.SS200.Cmd.ViewModels.Dock;

namespace Zishan.SS200.Cmd.Services
{
    /// <summary>
    /// 全局UI日志服务静态类，提供全局UI日志记录功能
    /// 支持层次化日志显示，通过缩进来表示调用层级关系
    /// </summary>
    public static class UILogService
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(UILogService));
        private static LogViewModel _LogViewModel;
        private static readonly object _lock = new object();
        private static int _currentLogIndex = 1;

        #region 缩进层级管理

        /// <summary>
        /// 线程静态变量，存储当前线程的缩进层级
        /// </summary>
        [ThreadStatic]
        private static int _indentLevel = 0;

        /// <summary>
        /// 缩进字符串配置，默认为4个空格
        /// </summary>
        private static string _indentString = "    ";

        /// <summary>
        /// 最大缩进层级限制，防止过度嵌套
        /// </summary>
        private static int _maxIndentLevel = 10;

        /// <summary>
        /// 是否启用缩进功能
        /// </summary>
        private static bool _indentEnabled = true;

        #endregion 缩进层级管理

        #region 缩进配置方法

        /// <summary>
        /// 设置缩进字符串
        /// </summary>
        /// <param name="indentString">缩进字符串，如"    "（4个空格）或"\t"（Tab）</param>
        public static void SetIndentString(string indentString)
        {
            _indentString = indentString ?? "    ";
        }

        /// <summary>
        /// 设置最大缩进层级
        /// </summary>
        /// <param name="maxLevel">最大层级数</param>
        public static void SetMaxIndentLevel(int maxLevel)
        {
            _maxIndentLevel = Math.Max(0, maxLevel);
        }

        /// <summary>
        /// 启用或禁用缩进功能
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public static void SetIndentEnabled(bool enabled)
        {
            _indentEnabled = enabled;
        }

        /// <summary>
        /// 获取当前缩进层级
        /// </summary>
        /// <returns>当前缩进层级</returns>
        public static int GetCurrentIndentLevel()
        {
            return _indentLevel;
        }

        #endregion 缩进配置方法

        #region 日志格式化辅助方法

        /// <summary>
        /// 格式化日志消息，根据配置决定是否包含调用信息
        /// </summary>
        /// <param name="log">原始日志内容</param>
        /// <param name="lineNumber">调用行号</param>
        /// <param name="memberName">调用方法名</param>
        /// <returns>格式化后的日志消息</returns>
        private static string FormatLogMessage(string log, int lineNumber, string memberName)
        {
            try
            {
                // 检查配置是否显示调用信息
                bool showCallerInfo = false;
                try
                {
                    showCallerInfo = App.AppIniConfig?.ShowCallerInfoInUILog ?? false;
                }
                catch (Exception ex)
                {
                    // 如果获取配置失败，默认不显示调用信息
                    _logger?.Debug($"获取UI日志配置失败，使用默认值: {ex.Message}");
                }

                // 根据配置决定是否包含调用信息
                if (showCallerInfo)
                {
                    return $"{log} (Line: {lineNumber}, Method: {memberName})";
                }
                else
                {
                    return log;
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"格式化日志消息时发生错误: {ex.Message}", ex);
                // 发生错误时返回原始日志内容
                return log;
            }
        }

        #endregion 日志格式化辅助方法

        #region 缩进层级控制方法

        /// <summary>
        /// 增加缩进层级
        /// </summary>
        public static void IncreaseIndent()
        {
            if (_indentLevel < _maxIndentLevel)
            {
                _indentLevel++;
            }
        }

        /// <summary>
        /// 减少缩进层级
        /// </summary>
        public static void DecreaseIndent()
        {
            if (_indentLevel > 0)
            {
                _indentLevel--;
            }
        }

        /// <summary>
        /// 重置缩进层级为0
        /// </summary>
        public static void ResetIndent()
        {
            _indentLevel = 0;
        }

        /// <summary>
        /// 创建缩进作用域，使用using语句自动管理缩进层级
        /// </summary>
        /// <returns>缩进作用域对象</returns>
        public static IndentScope CreateIndentScope()
        {
            return new IndentScope();
        }

        /// <summary>
        /// 获取当前缩进字符串
        /// </summary>
        /// <returns>根据当前层级生成的缩进字符串</returns>
        private static string GetIndentString()
        {
            if (!_indentEnabled || _indentLevel <= 0)
                return string.Empty;

            return string.Concat(System.Linq.Enumerable.Repeat(_indentString, _indentLevel));
        }

        #endregion 缩进层级控制方法

        /// <summary>
        /// 初始化UI日志服务
        /// </summary>
        /// <param name="viewModel">主窗口视图模型</param>
        public static void Initialize(LogViewModel viewModel)
        {
            _LogViewModel = viewModel;
            _logger.Info("UI日志服务已初始化");
        }

        /// <summary>
        /// 添加日志到UI界面 【UI界面上的日志太多了，根据缩进层级过滤，或者日志内容模糊过滤，有什么更好的方式过滤UI显示日志，方便用户查看】
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void AddLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            // 添加缩进前缀
            string indentedLog = GetIndentString() + log;

            // 根据配置格式化日志消息
            string formattedMessage = FormatLogMessage(indentedLog, lineNumber, memberName);

            if (_LogViewModel == null)
            {
                // 如果主视图模型未初始化，只记录到日志文件
                //_logger.Warn($"UI日志服务未初始化，日志将只记录到文件: {formattedMessage}");
                _logger.Info(formattedMessage);
                return;
            }

            try
            {
                LogInfo logModel = new LogInfo
                {
                    Index = _currentLogIndex,
                    Message = formattedMessage
                };

                bool blUiAdd = false;

                // 检查配置是否在UI界面上展示日志
                bool showUILog = false;
                try
                {
                    showUILog = App.AppIniConfig?.ShowUILog ?? true;
                }
                catch (Exception ex)
                {
                    // 如果获取配置失败，默认显示UI日志
                    _logger?.Debug($"获取UI日志显示配置失败，使用默认值: {ex.Message}");
                    showUILog = true;
                }

                // 根据配置决定是否在UI界面上添加日志
                if (showUILog)
                {
                    // 使用 Dispatcher.Invoke 确保在 UI 线程上操作集合
                    Application.Current?.Dispatcher?.Invoke(() =>
                    {
                        // 调用主视图模型的AddLog方法
                        _LogViewModel.AddLog(logModel.Message);
                        blUiAdd = true;
                    });
                }

                // 无论是否在UI显示，都记录到日志文件
                if (!blUiAdd)
                {
                    _logger.Info(logModel.Message);
                }

                _currentLogIndex++;
            }
            catch (Exception ex)
            {
                _logger.Error($"添加UI日志失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 添加成功日志
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void AddSuccessLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            AddLog($"✅ {log}", lineNumber, memberName);
        }

        /// <summary>
        /// 添加错误日志
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void AddErrorLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            AddLog($"❌ {log}", lineNumber, memberName);
        }

        /// <summary>
        /// 添加警告日志
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void AddWarningLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            AddLog($"⚠️ {log}", lineNumber, memberName);
        }

        /// <summary>
        /// 添加信息日志
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void AddInfoLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            AddLog($"ℹ️ {log}", lineNumber, memberName);
        }

        #region 带有自动缩进管理的便捷方法

        /// <summary>
        /// 添加日志并自动增加缩进层级，适用于开始某个操作
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void AddLogAndIncreaseIndent(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            AddLog(log, lineNumber, memberName);
            IncreaseIndent();
        }

        /// <summary>
        /// 减少缩进层级并添加日志，适用于结束某个操作
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void DecreaseIndentAndAddLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            DecreaseIndent();
            AddLog(log, lineNumber, memberName);
        }

        /// <summary>
        /// 添加成功日志并减少缩进层级，适用于成功完成某个操作
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void DecreaseIndentAndAddSuccessLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            DecreaseIndent();
            AddSuccessLog(log, lineNumber, memberName);
        }

        /// <summary>
        /// 添加错误日志并减少缩进层级，适用于操作失败
        /// </summary>
        /// <param name="log">日志内容</param>
        /// <param name="lineNumber">调用行号（自动获取）</param>
        /// <param name="memberName">调用方法名（自动获取）</param>
        public static void DecreaseIndentAndAddErrorLog(string log, [CallerLineNumber] int lineNumber = 0, [CallerMemberName] string memberName = "")
        {
            DecreaseIndent();
            AddErrorLog(log, lineNumber, memberName);
        }

        #endregion 带有自动缩进管理的便捷方法
    }

    /// <summary>
    /// 缩进作用域类，用于自动管理缩进层级
    /// 使用using语句可以自动在作用域开始时增加缩进，结束时减少缩进
    /// </summary>
    public class IndentScope : IDisposable
    {
        private bool _disposed = false;

        /// <summary>
        /// 构造函数，自动增加缩进层级
        /// </summary>
        public IndentScope()
        {
            UILogService.IncreaseIndent();
        }

        /// <summary>
        /// 释放资源，自动减少缩进层级
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                UILogService.DecreaseIndent();
                _disposed = true;
            }
        }
    }
}