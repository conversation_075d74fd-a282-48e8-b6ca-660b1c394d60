﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="Zishan.SS200.Cmd.Models.IR400.Plc.Arm">
    <Position X="5.5" Y="7.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAQAAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Arm.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR450.Models.IR400.Plc.Buffer">
    <Position X="12.25" Y="7.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Buffer.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR450.Models.IR400.Plc.Chamber">
    <Position X="16.75" Y="7.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIA=</HashCode>
      <FileName>Models\IR400\Plc\Chamber.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.SS200.Cmd.Models.IR400.Plc.Pos">
    <Position X="19" Y="7.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAABAAAAAAAAAAAAAAAAAAAAAgAAAAAAEAAEAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Pos.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR450.Models.IR400.Plc.Wafer">
    <Position X="16.75" Y="2.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAARBAAAAASABIQAAIBAAAAAAAAKAAAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Wafer.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR450.Models.IR400.Plc.WaferAction">
    <Position X="19" Y="2.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAkAAAgAABAAAAAAAACAAAgAAAAECAAAAAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\WaferAction.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Enum Name="Zishan.RobotIR450.Models.IR400.Plc.EnuArmFetchSide">
    <Position X="6.75" Y="1" Width="1.5" />
    <TypeIdentifier>
      <HashCode>KAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Enums\EnuArmFetchSide.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.SS200.Cmd.Models.IR400.Plc.Enums.EnuEAPCommunicationStatus">
    <Position X="8.5" Y="1" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAEAAIAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Enums\EnuEAPCommunicationStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.SS200.Cmd.Models.IR400.Plc.Enums.EnuMode">
    <Position X="10.25" Y="1" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Enums\EnuMode.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.SS200.Cmd.Models.IR400.Plc.Enums.EnuProcessStatus">
    <Position X="6.75" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>QAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAgACAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Enums\EnuProcessStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.SS200.Cmd.Models.IR400.Plc.Enums.EnuWaferMappingStatus">
    <Position X="8.5" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAEAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Enums\EnuWaferMappingStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.SS200.Cmd.Models.IR400.Plc.Enums.EnuWaferStatus">
    <Position X="10.25" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Enums\EnuWaferStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.SS200.Cmd.Models.IR400.Plc.Enums.EnuWarnStatus">
    <Position X="6.75" Y="4.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAQAAAAAAAAAEAAAAAAAAAAAA=</HashCode>
      <FileName>Models\IR400\Plc\Enums\EnuWarnStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.SS200.Cmd.Models.IR400.Plc.Enums.EnuWorkStatus">
    <Position X="8.5" Y="4" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAABABAABAAAAAgAAAAAAAAAAAAAAAABAAAAAAAI=</HashCode>
      <FileName>Models\IR400\Plc\Enums\EnuWorkStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Font Name="Microsoft YaHei UI" Size="9" />
</ClassDiagram>