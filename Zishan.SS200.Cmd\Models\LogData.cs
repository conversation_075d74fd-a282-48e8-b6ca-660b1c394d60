﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models
{
    /// <summary>
    ///  获取PLC Job、Alarm 数据封装格式，PLC 结构体变量名：strLogData
    /// </summary>
    [SugarTable("PlcLogData", TableDescription = "获取PLC Job、Alarm 数据封装格式，PLC 结构体变量名：strLogData")]
    public class LogData
    {
        /// <summary>
        /// Sugar需要无参构造函数,勿删
        /// </summary>
        public LogData()
        {
        }

        public LogData(EnuLogDataType enuLogDataType)
        {
            LogDataType = enuLogDataType;
        }

        /// <summary>
        /// ID号
        /// </summary>
        [SugarColumn(IsIdentity = true, IsPrimaryKey = true, ColumnDescription = "ID号")]
        public int Id { get; set; }

        [SugarColumn(ColumnDescription = "日志类型 1：工作日志、2：报警日志、3:操作日志、4：数据日志、5：其它日志")]
        public EnuLogDataType LogDataType { get; set; }

        /// <summary>
        /// 消息_已拼接
        /// </summary>
        [SugarColumn(ColumnDescription = "消息_已拼接", IsNullable = true)]
        public string Message_Concated { get; set; }

        /// <summary>
        /// 系统时间_字符串
        /// </summary>
        [SugarColumn(ColumnDescription = "系统时间_字符串")]
        public string SystemTime_String { get; set; } = string.Empty;

        /// <summary>
        /// 系统时间_日期型
        /// </summary>
        [SugarColumn(ColumnDescription = "系统时间")]
        public DateTime SystemTime { get; set; }

        /// <summary>
        /// 消息_名称
        /// (*位置名称 array        [0]: Main(主位置)
        //[1]: Sub1(子位置1)
        //[2]: Sub2(子位置2)
        //[3]: Sub3(子位置3)
        //[4]: Sub4(子位置4)
        //[5]: Sub5(子位置5)
        //[6]: Sub6(子位置6)
        //[7]: Sub7(子位置7)
        /// </summary>
        [SugarColumn(ColumnDescription = "消息_名称_位置名称 array", IsIgnore = true)]
        public List<string> PosNameList { get; set; }

        [SugarColumn(ColumnDescription = "消息_名称_Main(主位置)")]
        public string PosName0 { get; set; } = string.Empty;

        [SugarColumn(ColumnDescription = "消息_名称_Sub1(子位置1)", IsNullable = true)]
        public string PosName1 { get; set; }

        [SugarColumn(ColumnDescription = "消息_名称_Sub2(子位置2)", IsNullable = true)]
        public string PosName2 { get; set; }

        [SugarColumn(ColumnDescription = "消息_名称_Sub3(子位置3)", IsNullable = true)]
        public string PosName3 { get; set; }

        [SugarColumn(ColumnDescription = "消息_名称_Sub4(子位置4)", IsNullable = true)]
        public string PosName4 { get; set; }

        [SugarColumn(ColumnDescription = "消息_名称_Sub5(子位置5)", IsNullable = true)]
        public string PosName5 { get; set; }

        [SugarColumn(ColumnDescription = "消息_名称_Sub6(子位置6)", IsNullable = true)]
        public string PosName6 { get; set; }

        [SugarColumn(ColumnDescription = "消息_名称_Sub7(子位置7)", IsNullable = true)]
        public string PosName7 { get; set; }

        /// <summary>
        /// 消息_名称
        /// </summary>
        [SugarColumn(ColumnDescription = "消息_名称")]
        public string MessageName { get; set; } = string.Empty;

        /// <summary>
        /// 消息_详情
        /// </summary>
        [SugarColumn(ColumnDescription = "消息_详情")]
        public string MessageDetail { get; set; }

        /// <summary>
        /// 消息_详情
        /// </summary>
        [SugarColumn(ColumnDescription = "消息_状态")]
        public string MessageStatus { get; set; }

        /// <summary>
        /// 是否有效，有效为‘Y’，无效为‘N’
        /// </summary>
        [SugarColumn(ColumnDescription = "是否有效")]
        public string Valid { get; set; } = "Y";

        /// <summary>
        /// 备注信息
        /// </summary>
        [SugarColumn(ColumnDescription = "备注信息", IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", InsertServerTime = true)]
        public DateTime CreateTime { get; set; }
    }
}