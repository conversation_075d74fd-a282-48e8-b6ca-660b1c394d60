using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.Process
{
    /// <summary>
    /// 工艺控制类型
    /// </summary>
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum EnuProcessControl
    {
        /// <summary>
        /// 按时间控制
        /// </summary>
        [Description("By Time")]
        ByTime = 0,

        /// <summary>
        /// 按终点控制
        /// </summary>
        [Description("By EPD")]
        ByEPD = 1,

        /// <summary>
        /// 无，不升不降【开发测试额外添加】
        /// </summary>
        [Description("By None")]
        None = 27
    }
}