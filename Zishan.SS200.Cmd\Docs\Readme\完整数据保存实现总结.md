# 完整数据保存实现总结

## 概述

已成功完善 `SavaDataToRedis()` 方法，实现了完整的 InterLock 数据保存功能，确保不遗漏任何重要数据。

## 完善内容

### 1. IO接口数据完整保存

#### Robot IO数据
- **RDI1-RDI4**: 完整的数字输入状态
  - RDI1_PaddleSensor1Left: Paddle传感器1左侧
  - RDI2_PaddleSensor2Right: Paddle传感器2右侧  
  - RDI3_PinSearch1: Pin搜索1
  - RDI4_PinSearch2: Pin搜索2
- **每个IO点包含**: Value、Content、IsActive 三个属性

#### Chamber A/B IO数据
- **关键数字输入**: PDI1_CvOpenSensor、PDI2_CvCloseSensor、PDI12_SlitDoorOpenSensor、PDI13_SlitDoorCloseSensor、PDI14_LiftPinUpSensor、PDI15_LiftPinDownSensor
- **关键数字输出**: PDO10_SlitDoorOpen、PDO11_SlitDoorClose、PDO12_LiftPinUp、PDO13_LiftPinDown
- **每个IO点包含**: Value、Content、IsActive 三个属性

#### Shuttle IO数据
- **关键数字输入**: SDI1_CassetteDoorUpSensor、SDI2_CassetteDoorDownSensor、SDI6_PresentSensorCassette1
- **关键数字输出**: SDO1_CassetteDoorCylinderUp、SDO2_CassetteDoorCylinderDown
- **每个IO点包含**: Value、Content、IsActive 三个属性

### 2. 报警数据完整保存

#### Robot报警
- **RA1_SystemBusyReject**: 系统忙碌指令被拒绝
- **RA2_SystemAlarmReject**: 系统报警指令被拒绝
- **每个报警包含**: Content、ChsContent、Code、Cause、Item 五个属性

#### Chamber A/B报警
- **基础结构**: AlarmSystemAvailable、LastCheckTime、Note
- **预留扩展**: 可根据实际存在的报警代码添加具体报警项

#### Shuttle报警
- **SA1_SystemBusyReject**: 系统忙碌指令被拒绝
- **每个报警包含**: Content、ChsContent、Code、Cause、Item 五个属性

#### 报警统计
- **TotalAlarmTypes**: 总报警类型数量
- **各子系统报警计数**: RobotAlarmCount、ChamberAAlarmCount等
- **LastUpdateTime**: 最后更新时间

### 3. 配置数据完整保存

#### 主系统配置
- **SSC1_Shuttle1WaferSize**: Shuttle1晶圆尺寸
- **SSC2_Shuttle2WaferSize**: Shuttle2晶圆尺寸
- **SSC3_ChamberLocation**: 腔室位置
- **SSC6_CassetteNestType**: 卡匣巢类型

#### Robot配置
- **T轴Smooth位置**: RP1_TAxisSmoothToCHA、RP2_TAxisSmoothToCHB、RP4_TAxisSmoothToCassette
- **T轴Nose位置**: RP5_TAxisNoseToCHA、RP6_TAxisNoseToCHB、RP8_TAxisNoseToCassette
- **配置统计**: ConfigCount、LastUpdateTime

#### Chamber和Shuttle配置
- **基础信息**: ConfigSystemAvailable、LastUpdateTime、Note

### 4. 轴位置数据完整保存

#### T轴数据
- **CurrentPosition**: 当前步进位置
- **CurrentDegree**: 当前角度值
- **IsZeroPosition**: 是否在零位
- **SmoothDestination**: Smooth目的地
- **NoseDestination**: Nose目的地

#### R轴数据
- **CurrentPosition**: 当前步进位置
- **CurrentLength**: 当前长度值
- **IsZeroPosition**: 是否在零位
- **SmoothExtendDestination**: Smooth扩展目的地
- **NoseExtendDestination**: Nose扩展目的地

#### Z轴数据
- **CurrentPosition**: 当前步进位置
- **CurrentHeight**: 当前高度值
- **IsZeroPosition**: 是否在零位
- **HeightStatus**: 高度状态

### 5. 时间序列数据完整保存

#### Robot时间序列
- **Status**: 机器人状态字符串
- **TAxisPosition**: T轴位置
- **RAxisPosition**: R轴位置
- **ZAxisPosition**: Z轴位置

#### Chamber A/B时间序列
- **TriggerStatus**: 触发状态字符串
- **RunStatus**: 运行状态字符串

#### Shuttle时间序列
- **Status**: Shuttle状态字符串
- **PositionStatus**: 位置状态字符串

## 数据存储策略

### 1. Redis键命名规范
```
SS200:Status:Current        - 当前完整状态快照（不过期）
SS200:Status:History:{time} - 历史状态快照（24小时过期）
SS200:Status:Hash          - Hash结构状态数据
SS200:IO:Current           - 当前IO状态（1小时过期）
SS200:Alarm:Current        - 当前报警状态（7天过期）
SS200:Config:Current       - 当前配置信息（不过期）
SS200:Axis:Position        - 轴位置信息（1小时过期）
SS200:Timeline:Status      - 时间序列状态数据（保留1000条）
```

### 2. 数据过期管理
- **实时数据**: 1小时过期（IO状态、轴位置）
- **历史数据**: 24小时过期（状态快照）
- **报警数据**: 7天过期（便于故障分析）
- **配置数据**: 不过期（重要系统参数）
- **时间序列**: 滚动保留1000条记录

### 3. 数据结构优化
- **完整属性**: 每个数据点包含Value、Content、IsActive等完整信息
- **层次结构**: 按子系统分类，便于查询和管理
- **元数据**: 包含时间戳、统计信息、系统状态等

## 性能特性

### 1. 异步执行
- 所有Redis操作异步执行，不阻塞UI线程
- 支持并发操作，提高数据保存效率

### 2. 错误处理
- 连接状态检查
- 异常捕获和详细日志记录
- 操作结果返回和性能监控

### 3. 内存管理
- 时间序列数据自动清理
- 过期数据自动删除
- 优化的数据结构设计

## 使用方法

### 1. 完整数据保存
```csharp
// 保存所有类型的数据
var success = await SavaDataToRedis();
```

### 2. 选择性数据保存
```csharp
// 仅保存IO和轴位置数据
var success = await SavaDataToRedis(
    includeIOData: true,
    includeAlarmData: false,
    includeConfigData: false,
    includeAxisData: true
);
```

### 3. 生产环境配置
```csharp
// 生产环境不保存配置数据
bool isProduction = App.AppIniConfig.DatabaseAccessType == EnuDatabaseAccessType.Product_In;
var success = await SavaDataToRedis(
    includeIOData: true,
    includeAlarmData: true,
    includeConfigData: !isProduction,
    includeAxisData: true
);
```

## 监控和诊断

### 1. 日志记录
```
INFO: InterLock数据保存到Redis成功，耗时: 45ms
ERROR: 保存IO接口数据失败
WARN: Redis连接未建立，跳过数据保存
```

### 2. 数据验证
- Redis连接状态检查
- 数据完整性验证
- 操作结果确认

### 3. 性能监控
- 执行时间统计
- 数据大小监控
- 成功/失败率跟踪

## 扩展建议

### 1. 数据完整性
- 可根据实际需要添加更多IO点
- 可扩展更多报警代码
- 可增加更多配置参数

### 2. 性能优化
- 考虑数据压缩
- 实现批量操作
- 添加缓存机制

### 3. 功能增强
- 数据分析功能
- 趋势预测
- 异常检测

## 总结

完善后的 `SavaDataToRedis()` 方法现在能够：

1. **完整保存**：涵盖所有重要的InterLock数据
2. **结构化存储**：按子系统和数据类型分类存储
3. **灵活配置**：支持选择性保存不同类型的数据
4. **高性能**：异步执行，不影响系统性能
5. **可靠性**：完善的错误处理和监控机制

这为SS200系统的实时监控、数据分析、故障诊断等应用提供了坚实的数据基础。
