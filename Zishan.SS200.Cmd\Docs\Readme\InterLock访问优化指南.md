# InterLock 访问优化指南

## 📋 概述

本指南介绍如何优化 `SS200InterLockMain` 的访问调用，减少重复的单例访问，提高代码性能和可读性。

## ❌ 优化前的问题

### 1. 重复的单例调用
```csharp
// 每次都要调用 SS200InterLockMain.Instance
var robotValue = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
var chamberValue = SS200InterLockMain.Instance.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
var shuttleValue = SS200InterLockMain.Instance.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Value;
var alarmContent = SS200InterLockMain.Instance.AlarmCode.Robot.RA1_SystemBusyReject.Content;
var configValue = SS200InterLockMain.Instance.SubsystemConfigure.Robot.RP1_TAxisSmoothToCHA.Value;
```

### 2. 性能问题
- 每次调用 `Instance` 都要进行单例检查
- 重复的长链式调用增加了访问开销
- 代码冗长，可读性差

### 3. 维护问题
- 代码重复度高
- 修改访问路径时需要多处修改
- 容易出现拼写错误

## ✅ 优化方案

### 1. 使用 InterLockAccessHelper 助手类

```csharp
using Zishan.SS200.Cmd.Helpers;

// 缓存实例，批量访问
InterLockAccessHelper.WithIOInterface(io =>
{
    var robotValue = io.Robot.RDI1_PaddleSensor1Left.Value;
    var chamberValue = io.ChamberA.PDI12_SlitDoorOpenSensor.Value;
    var shuttleValue = io.Shuttle.SDI6_PresentSensorCassette1.Value;
});
```

### 2. 手动缓存实例

```csharp
// 在方法开始时缓存实例
var interLock = SS200InterLockMain.Instance;
var ioInterface = interLock.IOInterface;
var alarmCode = interLock.AlarmCode;
var configure = interLock.SubsystemConfigure;

// 使用缓存的对象
var robotValue = ioInterface.Robot.RDI1_PaddleSensor1Left.Value;
var alarmContent = alarmCode.Robot.RA1_SystemBusyReject.Content;
var configValue = configure.Robot.RP1_TAxisSmoothToCHA.Value;
```

## 🚀 优化效果

### 1. 性能提升
- **减少单例调用次数**：从 N 次减少到 1 次
- **减少对象创建开销**：缓存中间对象引用
- **提高访问速度**：避免重复的链式调用

### 2. 代码质量提升
- **提高可读性**：代码更简洁清晰
- **减少重复**：DRY 原则（Don't Repeat Yourself）
- **易于维护**：集中管理访问逻辑

### 3. 错误减少
- **减少拼写错误**：统一的访问方式
- **类型安全**：编译时检查
- **一致性**：标准化的访问模式

## 📚 使用方法

### 1. 基本用法

```csharp
// 执行操作
InterLockAccessHelper.WithInterLock(interLock =>
{
    // 在这里使用 interLock 进行各种操作
    var robotStatus = interLock.SubsystemStatus.Robot.Status;
    var ioInterface = interLock.IOInterface;
});

// 返回结果
var result = InterLockAccessHelper.WithInterLock(interLock =>
{
    return interLock.SubsystemStatus.Robot.Status.TAxisIsZeroPosition;
});
```

### 2. 专用方法

```csharp
// IO 接口访问
InterLockAccessHelper.WithIOInterface(io =>
{
    var robotSensor = io.Robot.RDI1_PaddleSensor1Left.Value;
    var chamberSensor = io.ChamberA.PDI12_SlitDoorOpenSensor.Value;
});

// 配置访问
InterLockAccessHelper.WithSubsystemConfigure(config =>
{
    var robotConfig = config.Robot.RP1_TAxisSmoothToCHA.Value;
    var shuttleConfig = config.Shuttle.SPS1_CassetteNestExtendRetractMinTime.Value;
});

// 状态访问
var robotStatus = InterLockAccessHelper.GetRobotStatus();
var chamberStatus = InterLockAccessHelper.GetChamberAStatus();
```

### 3. 批量操作

```csharp
// 批量读取传感器
var sensorValues = InterLockAccessHelper.GetSensorValues(
    io => io.Robot.RDI1_PaddleSensor1Left.Value,
    io => io.ChamberA.PDI12_SlitDoorOpenSensor.Value,
    io => io.Shuttle.SDI6_PresentSensorCassette1.Value
);

// 批量读取配置
var configValues = InterLockAccessHelper.GetConfigValues(
    config => config.Robot.RP1_TAxisSmoothToCHA?.Value ?? 0,
    config => config.Shuttle.SPS1_CassetteNestExtendRetractMinTime?.Value ?? 0
);
```

## 🎯 最佳实践

### 1. 选择合适的优化方式

- **少量访问**：使用手动缓存
- **大量访问**：使用 InterLockAccessHelper
- **批量操作**：使用批量方法
- **复杂逻辑**：使用 WithInterLock 方法

### 2. 代码组织

```csharp
private void UpdateSystemStatus()
{
    // ✅ 推荐：在方法开始时缓存
    var interLock = SS200InterLockMain.Instance;
    
    // 缓存常用对象
    var robotStatus = interLock.SubsystemStatus.Robot.Status;
    var ioInterface = interLock.IOInterface;
    
    // 执行业务逻辑
    UpdateRobotStatus(robotStatus);
    UpdateSensorStatus(ioInterface);
}
```

### 3. 错误处理

```csharp
InterLockAccessHelper.WithInterLock(interLock =>
{
    try
    {
        // 业务逻辑
        var status = interLock.SubsystemStatus.Robot.Status;
        // ...
    }
    catch (Exception ex)
    {
        // 错误处理
        Console.WriteLine($"访问 InterLock 时发生错误: {ex.Message}");
    }
});
```

## 📊 性能对比

| 优化方式 | 单例调用次数 | 代码行数 | 可读性 | 维护性 |
|---------|-------------|---------|--------|--------|
| 优化前 | N 次 | 多 | 差 | 差 |
| 手动缓存 | 1 次 | 中等 | 好 | 好 |
| 助手类 | 1 次 | 少 | 很好 | 很好 |

## 🔧 迁移指南

### 1. 识别需要优化的代码
- 查找多次调用 `SS200InterLockMain.Instance` 的方法
- 关注长链式调用的代码段
- 重点优化频繁调用的方法

### 2. 逐步迁移
1. 先优化最频繁调用的方法
2. 使用助手类替换重复调用
3. 测试确保功能正常
4. 逐步扩展到其他方法

### 3. 验证优化效果
- 检查代码可读性是否提升
- 验证功能是否正常
- 测量性能是否有改善

## 📝 总结

通过使用 InterLock 访问优化技术，我们可以：

1. **提高性能**：减少重复的单例调用
2. **改善代码质量**：提高可读性和维护性
3. **减少错误**：统一的访问模式
4. **便于扩展**：标准化的优化方案

建议在新代码中直接使用优化方案，在维护现有代码时逐步迁移到优化版本。
