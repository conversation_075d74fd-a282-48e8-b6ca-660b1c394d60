{"0000": {"cause": "", "kind": "成功"}, "0001": {"cause": "I2C通讯错误或者RS485通讯失败(超时, 指令错误等)", "kind": "硬件错误"}, "0002": {"cause": "执行指令时参数长度或者数值不对", "kind": "参数错误"}, "0004": {"cause": "请检查运行指令是否支持", "kind": "未定义指令"}, "0003": {"cause": "", "kind": "电机报警"}, "0005": {"cause": "", "kind": "RA1 Robot system status is busy, command reject"}, "0006": {"cause": "", "kind": "RA2 Robot system status is alarm, command reject"}, "0007": {"cause": "", "kind": "RA3 Robot R-axis not at home position, T-axis can not motion"}, "0008": {"cause": "", "kind": "RA4 Robot T-axis not in right position, R-axis can not motion "}, "0009": {"cause": "", "kind": "RA5 Robot rotate time out"}, "000A": {"cause": "", "kind": "RA6 Robot extend time out"}, "000B": {"cause": "", "kind": "RA7 Robot retract time out"}, "000C": {"cause": "", "kind": "RA8 Robot up down time out"}, "000D": {"cause": "", "kind": "RA9 Robot home T-axis time out"}, "000E": {"cause": "", "kind": "RA10 Robot home R-axis time out"}, "000F": {"cause": "", "kind": "RA11 Robot home Z-axis time out"}}