# HcGrowl 消息通知组件使用说明

## 目录
- [概述](#概述)
- [功能特点](#功能特点)
- [消息类型详解](#消息类型详解)
- [显示模式对比](#显示模式对比)
- [高级配置](#高级配置)
- [在 SS200 项目中的应用示例](#在-ss200-项目中的应用示例)
- [最佳实践](#最佳实践)
- [扩展方法说明](#扩展方法说明)
- [注意事项](#注意事项)
- [常见问题与故障排除](#常见问题与故障排除)
- [开发调试技巧](#开发调试技巧)
- [扩展功能建议](#扩展功能建议)
- [相关资源](#相关资源)

## 概述

HcGrowl 是基于 HandyControl 库的消息通知组件，用于在应用程序中显示各种类型的提示信息。它提供了类似于系统Toast通知的功能，支持多种消息类型和显示模式。

> 💡 **快速开始**：如果您只想快速了解如何使用，可以直接跳转到 [在 SS200 项目中的应用示例](#在-ss200-项目中的应用示例) 部分。

## 功能特点

- 🎨 **多种消息类型**：Info、Success、Warning、Error、Ask、Fatal
- 📍 **两种显示模式**：Window（窗口内）、Desktop（全局桌面）
- ⏰ **自定义停留时间**：可设置消息显示时长
- 🔧 **灵活配置**：支持自定义样式和行为
- 🎯 **Token机制**：支持分组管理消息

## 消息类型详解

### 1. Info（信息消息）🔵
**用途**：显示一般性信息和状态更新
```csharp
// 基本用法
HcGrowlExtensions.Info("数据加载完成");

// 完整参数
HcGrowlExtensions.Info("系统已连接", token: "main", waitTime: 3, showDateTime: true);

// 原生API
Growl.Info("操作提示");
Growl.InfoGlobal("全局信息");
```

**应用场景**：
- 数据加载完成提示
- 系统状态更新
- 操作进度通知

### 2. Success（成功消息）✅
**用途**：显示操作成功的反馈信息
```csharp
// 扩展方法
HcGrowlExtensions.Success("文件保存成功");

// 原生API
Growl.Success("操作成功");
Growl.SuccessGlobal("全局成功消息");
```

**应用场景**：
- 文件保存成功
- 设备连接成功
- 配置更新成功

### 3. Warning（警告消息）⚠️
**用途**：显示需要用户注意的警告信息
```csharp
// 扩展方法
HcGrowlExtensions.Warning("磁盘空间不足");

// 带确认操作的警告
Growl.Warning(new GrowlInfo
{
    Message = "检测到异常参数，是否继续？",
    CancelStr = "取消",
    ConfirmStr = "继续",
    ActionBeforeClose = isConfirmed =>
    {
        if (isConfirmed)
        {
            // 用户点击确认后的操作
            ContinueOperation();
        }
        return true;
    }
});
```

**应用场景**：
- 参数异常提醒
- 资源不足警告
- 操作风险提示

### 4. Error（错误消息）❌
**用途**：显示错误信息和异常状态
```csharp
// 扩展方法
HcGrowlExtensions.Error("网络连接失败");

// 原生API
Growl.Error("操作失败");
Growl.ErrorGlobal("系统错误");
```

**应用场景**：
- 网络连接失败
- 数据库操作异常
- 设备通信错误

### 5. Ask（询问消息）❓
**用途**：询问用户是否执行某个操作
```csharp
Growl.Ask("确定要删除这个配置吗？", isConfirmed =>
{
    if (isConfirmed)
    {
        DeleteConfiguration();
        HcGrowlExtensions.Success("配置已删除");
    }
    else
    {
        HcGrowlExtensions.Info("操作已取消");
    }
    return true;
});
```

**应用场景**：
- 删除确认
- 退出程序确认
- 重要操作确认

### 6. Fatal（致命错误）💀
**用途**：显示严重错误和系统级异常
```csharp
Growl.Fatal(new GrowlInfo
{
    Message = "系统发生致命错误，程序即将退出",
    ShowDateTime = false,
    StaysOpen = true  // 保持显示直到用户手动关闭
});
```

**应用场景**：
- 系统崩溃
- 关键组件失效
- 不可恢复的错误

## 显示模式对比

| 模式 | 方法后缀 | 显示位置 | 适用场景 |
|------|----------|----------|----------|
| Window | 无后缀 | 当前窗口内 | 窗口相关操作反馈 |
| Desktop | Global | 整个桌面 | 系统级重要通知 |

```csharp
// Window模式 - 在当前窗口内显示
Growl.Info("窗口内消息");

// Desktop模式 - 在桌面全局显示
Growl.InfoGlobal("桌面全局消息");
```

## 高级配置

### GrowlInfo 参数详解
```csharp
var growlInfo = new GrowlInfo
{
    Message = "消息内容",           // 必需：消息文本
    WaitTime = 5,                  // 停留时间（秒）
    Token = "group1",              // 分组标识
    ShowDateTime = true,           // 显示时间戳
    StaysOpen = false,             // 是否保持打开
    ConfirmStr = "确认",           // 确认按钮文本
    CancelStr = "取消",            // 取消按钮文本
    ActionBeforeClose = isConfirmed => // 关闭前回调
    {
        // 处理用户操作
        return true; // 返回true允许关闭
    }
};
```

### Token 分组管理
```csharp
// 为不同模块使用不同的Token
HcGrowlExtensions.Info("Chamber状态更新", token: "chamber");
HcGrowlExtensions.Info("Robot状态更新", token: "robot");

// 清除特定分组的消息
Growl.Clear("chamber");

// 清除所有消息
Growl.Clear();
```

## 在 SS200 项目中的应用示例

### 1. 设备连接状态通知
```csharp
// MainWindowViewModel.cs - 断开设备连接
[RelayCommand(CanExecute = nameof(CanDisconnectDevices))]
private async Task DisconnectDevicesAsync()
{
    try
    {
        if (HandyControl.Controls.MessageBox.Show("确定要断开所有设备连接吗？", "确认", MessageBoxButton.YesNo) != MessageBoxResult.Yes)
            return;

        _logger.Info("正在断开所有设备连接...");
        await _mcuCmdService.DisconnectAllAsync();

        UpdateDeviceStatus();

        _logger.Info("所有设备已断开连接");
        HcGrowlExtensions.Success("所有设备已断开连接");
    }
    catch (Exception ex)
    {
        _logger.Error($"断开设备连接失败: {ex.Message}", ex);
        HcGrowlExtensions.Error($"断开设备连接失败: {ex.Message}");
        UILogService.AddErrorLog($"断开设备连接时发生错误: {ex.Message}");
    }
}
```

### 2. 命令执行反馈
```csharp
// S200McuCmdPanelViewModel.cs - 执行MCU命令
[RelayCommand]
private async Task ExecuteCommandAsync()
{
    if (SelectedCommand == null)
    {
        UILogService.AddInfoLog("请先选择一个命令");
        return;
    }

    // 参数验证
    if (DynamicParameterErrors != null && DynamicParameterErrors.Count > 0)
    {
        UILogService.AddErrorLog("动态参数存在格式错误，请修正后再执行命令");
        HcGrowlExtensions.Error("动态参数存在格式错误，请修正后再执行命令");
        return;
    }

    try
    {
        var response = await ExecuteCommand();

        if (response.StartsWith("Success"))
        {
            HcGrowlExtensions.Success($"命令执行成功: {cmdName} [{displayDynamicParams}]");

            // 特殊处理：Robot PinSearch命令成功后显示结果
            if (deviceType == EnuMcuDeviceType.Robot && cmdEnum == EnuRobotCmdIndex.PinSearch.ToString())
            {
                int p1Value = UiViewModel.PinSearchP1Value;
                int p2Value = UiViewModel.PinSearchP2Value;
                string pinSearchValues = $"Pin Search计算结果：P1: {p1Value}, P2: {p2Value}";
                HcGrowlExtensions.Success(pinSearchValues);
            }
        }
        else
        {
            HcGrowlExtensions.Error($"命令执行失败: {cmdName} [{displayDynamicParams}] - {response}");
        }
    }
    catch (Exception ex)
    {
        _logger.Error($"执行命令失败: {ex.Message}", ex);
        HcGrowlExtensions.Error($"执行命令失败: {ex.Message}");
    }
}
```

### 3. 批量命令执行
```csharp
// S200McuCmdPanelViewModel.cs - 批量命令执行
[RelayCommand]
private async Task ExecuteBatchCommandAsync()
{
    if (SelectedBatchSequence == null || SelectedBatchSequence.Commands.Count == 0)
    {
        HcGrowlExtensions.Warning("请先选择一个包含命令的批量序列");
        UILogService.AddWarningLog("没有可执行的批量命令序列");
        return;
    }

    if (IsExecutingBatchCommand)
    {
        HcGrowlExtensions.Warning("正在执行批量命令序列，请等待执行完成或手动停止");
        return;
    }

    // 执行批量命令...
    foreach (var command in commands)
    {
        var result = await ExecuteCommand(command);

        if (result.StartsWith("Success"))
        {
            // 开发调试模式显示详细信息，生产模式简化显示
            int waitTime = Golbal.IsDevDebug ? HcGrowlExtensions.WaitTime : 1;
            HcGrowlExtensions.Success($"命令执行成功: {commandName} [{displayDynamicParams}]", waitTime: waitTime);
        }
        else
        {
            HcGrowlExtensions.Error($"命令执行失败: {commandName} [{displayDynamicParams}] - {result}");
            break; // 失败时停止执行
        }
    }
}
```

### 4. 状态更新和刷新
```csharp
// S200McuCmdPanelViewModel.cs - 刷新设备状态
[RelayCommand]
private void RefreshDeviceStatus()
{
    try
    {
        UpdateDeviceStatus();
        HcGrowlExtensions.Success("设备状态已刷新");
    }
    catch (Exception ex)
    {
        string errorMessage = $"刷新设备状态失败: {ex.Message}";
        _logger.Error(errorMessage, ex);
        HcGrowlExtensions.Error(errorMessage);
    }
}

// RobotStatusPanelViewModel.cs - 更新Robot子系统状态
[RelayCommand]
private void OnUpdateRobotSubsystemStatus()
{
    var msg = $"Robot子系统状态已更新:\r\n{RobotSubsystemStatus}";
    HcGrowlExtensions.Info(msg);
}
```

### 5. 测试和调试场景
```csharp
// WaferInfoDisplayViewModel.cs - 更新操作
private void ExecuteRunCmd(string parameter)
{
    switch (parameter.Trim())
    {
        case "Update":
            _stopwatchHelper.Restart();
            TimeSpan elapsed = _stopwatchHelper.Stop();
            HcGrowlExtensions.Info($"更新成功，代码执行时间：{elapsed.TotalMilliseconds} 毫秒，当前时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}!");
            break;
        default:
            HcGrowlExtensions.Info($"未知命令：{parameter}");
            break;
    }
}

// BasicCommandTestViewModel.cs - Robot命令测试
private async Task ExecuteRobotCommandAsync()
{
    try
    {
        var result = await _mcuCmdService.ExecuteRobotCommandAsync(SelectedRobotCommand);
        bool isSuccess = !result.StartsWith("Error:") && !result.StartsWith("Failed:");

        if (isSuccess)
        {
            string msg = $"执行Robot命令 {SelectedRobotCommand.GetDescription()} 成功: {result}";
            HcGrowlExtensions.Success(msg);
            UILogService.AddLog($"✅ {msg}");
        }
        else
        {
            string msg = $"执行Robot命令 {SelectedRobotCommand.GetDescription()} 失败: {result}";
            HcGrowlExtensions.Error(msg);
            UILogService.AddLog($"❌ {msg}");
        }
    }
    catch (Exception ex)
    {
        string msg = $"Robot命令执行异常: {ex.Message}";
        HcGrowlExtensions.Error(msg);
        _logger.Error("Robot命令执行异常", ex);
    }
}
```

### 6. PinSearch 特殊处理
```csharp
// BasicCommandTestViewModel.cs - PinSearch测试
private async Task TestPinSearchAsync()
{
    try
    {
        // 执行 Smooth 端 PinSearch
        var smoothResult = await _mcuCmdService.PinSearchAsync(EnuRobotEndType.Smooth);
        if (smoothResult.Success)
        {
            string msg = $"执行 Smooth 端 PinSearch 测试成功: {smoothResult.Message}，P1Value={smoothResult.PinSearchP1Value}，P2Value={smoothResult.PinSearchP2Value}";
            UILogService.AddSuccessLog(msg);
            HcGrowlExtensions.Success(msg);
        }
        else
        {
            string msg = $"Smooth 端 PinSearch 测试失败: {smoothResult.Message}";
            UILogService.AddErrorLog(msg);
            HcGrowlExtensions.Error(msg);
        }

        await Task.Delay(2000); // 等待2秒

        // 执行 Nose 端 PinSearch
        var noseResult = await _mcuCmdService.PinSearchAsync(EnuRobotEndType.Nose);
        if (noseResult.Success)
        {
            string msg = $"执行 Nose 端 PinSearch 测试成功: {noseResult.Message}，P1Value={noseResult.PinSearchP1Value}，P2Value={noseResult.PinSearchP2Value}";
            UILogService.AddSuccessLog(msg);
            HcGrowlExtensions.Success(msg);
        }
        else
        {
            string msg = $"Nose 端 PinSearch 测试失败: {noseResult.Message}";
            UILogService.AddErrorLog(msg);
            HcGrowlExtensions.Error(msg);
        }
    }
    catch (Exception ex)
    {
        string msg = $"PinSearch 测试异常: {ex.Message}";
        UILogService.AddErrorLog(msg);
        HcGrowlExtensions.Error(msg);
        _logger.Error("PinSearch 测试异常", ex);
    }
}
```

## 最佳实践

### 1. 消息内容规范
- ✅ **简洁明了**：消息内容要简短、清晰
- ✅ **用户友好**：使用用户能理解的语言
- ✅ **包含上下文**：提供足够的信息帮助用户理解
- ✅ **统一格式**：保持消息格式的一致性

```csharp
// ✅ 好的示例
HcGrowlExtensions.Success("Chamber-1 配置保存成功");
HcGrowlExtensions.Error("Robot通信超时，请检查网络连接");
HcGrowlExtensions.Info($"Pin Search计算结果：P1: {p1Value}, P2: {p2Value}");

// ❌ 不好的示例
HcGrowlExtensions.Error("Error: 0x80004005");
HcGrowlExtensions.Info("OK");
HcGrowlExtensions.Success("成功"); // 缺少具体信息
```

### 2. 消息类型选择指南
- **Info** 🔵：一般信息，状态更新，不需要特别关注
  - 数据加载完成、状态刷新、系统连接等
- **Success** ✅：操作成功，给用户正面反馈
  - 保存成功、连接成功、命令执行成功等
- **Warning** ⚠️：需要注意但不影响继续操作
  - 参数异常、资源不足、操作提醒等
- **Error** ❌：操作失败，需要用户处理
  - 连接失败、命令执行失败、系统错误等
- **Ask** ❓：需要用户做决定
  - 删除确认、退出确认、重要操作确认等
- **Fatal** 💀：严重错误，可能影响系统稳定性
  - 系统崩溃、关键组件失效、不可恢复错误等

### 3. 显示模式选择策略
- **Window模式**：窗口相关的操作反馈，用户在当前窗口的操作结果
- **Desktop模式**：重要的系统级通知，即使窗口最小化也要让用户看到

### 4. 性能优化建议
- ✅ **避免频繁显示**：不要在循环中频繁显示消息
- ✅ **及时清理**：使用Token分组管理，适时清理过期消息
- ✅ **合理设置停留时间**：根据消息重要性调整显示时长
- ✅ **批量操作优化**：对于批量操作，显示汇总信息而非每个操作的详细信息

```csharp
// ❌ 避免在循环中频繁显示消息
foreach (var item in items)
{
    ProcessItem(item);
    // 不要这样做 - 会产生大量通知
    HcGrowlExtensions.Info($"处理完成：{item.Name}");
}

// ✅ 在循环结束后显示汇总信息
HcGrowlExtensions.Success($"批量处理完成，共处理 {items.Count} 项");

// ✅ 开发模式和生产模式区别对待
int waitTime = Golbal.IsDevDebug ? HcGrowlExtensions.WaitTime : 1;
HcGrowlExtensions.Success($"命令执行成功: {commandName}", waitTime: waitTime);
```

### 5. Token 分组管理策略
```csharp
// 为不同功能模块使用不同的Token
public static class GrowlTokens
{
    public const string Chamber = "chamber";
    public const string Robot = "robot";
    public const string Shuttle = "shuttle";
    public const string System = "system";
    public const string Command = "command";
}

// 使用示例
HcGrowlExtensions.Info("Chamber状态更新", token: GrowlTokens.Chamber);
HcGrowlExtensions.Success("Robot命令执行成功", token: GrowlTokens.Robot);

// 清理特定模块的消息
Growl.Clear(GrowlTokens.Chamber);
```

### 6. 错误处理和日志结合
```csharp
// 将Growl通知与日志系统结合使用
try
{
    await ExecuteOperation();
    HcGrowlExtensions.Success("操作执行成功");
    UILogService.AddSuccessLog("操作执行成功");
}
catch (Exception ex)
{
    string errorMsg = $"操作执行失败: {ex.Message}";
    HcGrowlExtensions.Error(errorMsg);
    UILogService.AddErrorLog(errorMsg);
    _logger.Error("操作执行失败", ex); // 详细错误记录到日志文件
}
```

## 扩展方法说明

项目中的 `HcGrowlExtensions` 类提供了简化的扩展方法：

```csharp
public static class HcGrowlExtensions
{
    public const int WaitTime = 5; // 默认停留时间

    // 简化的方法，默认使用Global模式
    public static void Info(string message, string token = "", int waitTime = WaitTime, bool showDateTime = false)
    public static void Success(string message, string token = "", int waitTime = WaitTime, bool showDateTime = false)
    public static void Warning(string message, string token = "", int waitTime = WaitTime, bool showDateTime = false)
    public static void Error(string message, string token = "")
}
```

这些扩展方法简化了常用场景的调用，推荐在项目中优先使用。

## 注意事项

1. **线程安全**：Growl组件是线程安全的，可以在任何线程中调用
2. **内存管理**：消息会自动清理，但建议在适当时候手动清理
3. **用户体验**：避免同时显示过多消息，影响用户体验
4. **国际化**：消息内容支持多语言，建议使用资源文件管理

## 常见问题与故障排除

### 1. 消息不显示
**问题**：调用HcGrowlExtensions方法后消息不显示

**可能原因和解决方案**：
- ✅ 检查是否正确引用HandyControl库
- ✅ 确认UI线程调用：如果在后台线程中调用，需要使用Dispatcher
- ✅ 检查消息内容是否为空或null

```csharp
// 在后台线程中调用需要使用Dispatcher
Task.Run(() =>
{
    // 后台处理...
    Application.Current.Dispatcher.Invoke(() =>
    {
        HcGrowlExtensions.Success("后台任务完成");
    });
});
```

### 2. 消息显示位置不正确
**问题**：消息显示在错误的位置或窗口

**解决方案**：
- 检查是否使用了正确的显示模式（Window vs Desktop）
- 确认Token参数是否正确设置

### 3. 消息过多影响性能
**问题**：频繁显示消息导致界面卡顿

**解决方案**：
```csharp
// 使用防抖机制
private DateTime _lastNotificationTime = DateTime.MinValue;
private const int NotificationInterval = 1000; // 1秒间隔

private void ShowThrottledNotification(string message)
{
    var now = DateTime.Now;
    if ((now - _lastNotificationTime).TotalMilliseconds > NotificationInterval)
    {
        HcGrowlExtensions.Info(message);
        _lastNotificationTime = now;
    }
}
```

### 4. 消息内容显示不完整
**问题**：长消息内容被截断

**解决方案**：
- 控制消息长度，建议不超过100个字符
- 重要信息放在前面
- 使用换行符分隔多行内容

```csharp
// 长消息处理
string longMessage = GetLongMessage();
if (longMessage.Length > 100)
{
    string shortMessage = longMessage.Substring(0, 97) + "...";
    HcGrowlExtensions.Info(shortMessage);
    // 详细信息记录到日志
    _logger.Info($"完整消息: {longMessage}");
}
```

## 开发调试技巧

### 1. 调试模式增强
```csharp
public static class DebugGrowl
{
    public static void DebugInfo(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
    {
        if (Golbal.IsDevDebug)
        {
            string fileName = Path.GetFileNameWithoutExtension(filePath);
            string debugMessage = $"[{fileName}.{memberName}:{lineNumber}] {message}";
            HcGrowlExtensions.Info(debugMessage, waitTime: 10);
        }
    }
}

// 使用示例
DebugGrowl.DebugInfo("开始执行Robot命令"); // 会显示调用位置信息
```

### 2. 消息分类统计
```csharp
public static class GrowlStatistics
{
    private static readonly Dictionary<string, int> _messageCount = new();

    public static void TrackMessage(string type)
    {
        _messageCount[type] = _messageCount.GetValueOrDefault(type, 0) + 1;
    }

    public static void ShowStatistics()
    {
        var stats = string.Join(", ", _messageCount.Select(kv => $"{kv.Key}: {kv.Value}"));
        HcGrowlExtensions.Info($"消息统计: {stats}");
    }
}
```

## 扩展功能建议

### 1. 自定义扩展方法
```csharp
public static class CustomGrowlExtensions
{
    /// <summary>
    /// 显示带设备名称的消息
    /// </summary>
    public static void DeviceInfo(EnuMcuDeviceType deviceType, string message)
    {
        string deviceName = deviceType.GetDescription();
        HcGrowlExtensions.Info($"[{deviceName}] {message}", token: deviceType.ToString().ToLower());
    }

    /// <summary>
    /// 显示命令执行结果
    /// </summary>
    public static void CommandResult(string commandName, bool success, string details = "")
    {
        if (success)
        {
            HcGrowlExtensions.Success($"命令 {commandName} 执行成功{(string.IsNullOrEmpty(details) ? "" : $": {details}")}");
        }
        else
        {
            HcGrowlExtensions.Error($"命令 {commandName} 执行失败{(string.IsNullOrEmpty(details) ? "" : $": {details}")}");
        }
    }

    /// <summary>
    /// 显示进度信息
    /// </summary>
    public static void Progress(string operation, int current, int total)
    {
        double percentage = (double)current / total * 100;
        HcGrowlExtensions.Info($"{operation}: {current}/{total} ({percentage:F1}%)", waitTime: 2);
    }
}

// 使用示例
CustomGrowlExtensions.DeviceInfo(EnuMcuDeviceType.Robot, "设备连接成功");
CustomGrowlExtensions.CommandResult("PinSearch", true, "P1: 100, P2: 200");
CustomGrowlExtensions.Progress("批量处理", 5, 10);
```

### 2. 消息模板
```csharp
public static class GrowlTemplates
{
    public static void DeviceConnection(string deviceName, bool connected)
    {
        if (connected)
            HcGrowlExtensions.Success($"{deviceName} 连接成功");
        else
            HcGrowlExtensions.Error($"{deviceName} 连接失败，请检查设备状态");
    }

    public static void ParameterValidation(string paramName, object value, bool isValid)
    {
        if (!isValid)
            HcGrowlExtensions.Warning($"参数 {paramName} 值 {value} 超出有效范围");
    }

    public static void OperationTimeout(string operation, int timeoutSeconds)
    {
        HcGrowlExtensions.Error($"操作 {operation} 超时（{timeoutSeconds}秒），请重试");
    }
}
```

## 相关资源

- [HandyControl 官方文档](https://handyorg.github.io/handycontrol/)
- [Growl 控件文档](https://handyorg.github.io/handycontrol/extend_controls/growl/)
- 项目源码：`Extensions/HcGrowlExtensions.cs`
- 错误处理文档：`Docs/Report/8.错误处理和日志.md`

## 更新日志

- **v1.0** - 初始版本，包含基本的Info、Success、Warning、Error方法
- **v1.1** - 添加项目实际使用示例和最佳实践
- **v1.2** - 补充常见问题解决方案和扩展功能建议
