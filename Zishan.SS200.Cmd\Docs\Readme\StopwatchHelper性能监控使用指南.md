# StopwatchHelper 性能监控使用指南

## 📋 概述

`StopwatchHelper` 是一个增强的性能计时工具，专门用于监控代码执行性能并自动记录到日志文件中。通过这个工具，您可以：

- 🎯 **快速定位性能瓶颈**
- 📊 **建立性能基线和趋势分析**  
- ⚠️ **及时发现性能退化**
- 🔧 **为性能优化提供数据支撑**

## 🚀 快速开始

### 1. 基本使用

```csharp
// 创建计时器
var stopwatch = new StopwatchHelper("数据库查询");

// 开始计时
stopwatch.Start();

// 执行要监控的操作
var users = await userService.GetUsersAsync();

// 停止计时
var elapsed = stopwatch.Stop();
```

### 2. 简化使用（推荐）

```csharp
// 使用静态方法直接测量代码块
var elapsed = StopwatchHelper.Measure(() =>
{
    // 要监控的代码
    ProcessData();
}, "数据处理操作", warnThresholdMs: 500);
```

### 3. 异步操作监控

```csharp
// 监控异步操作
var elapsed = await StopwatchHelper.MeasureAsync(async () =>
{
    await ProcessDataAsync();
}, "异步数据处理");
```

## 📊 日志输出说明

### 日志文件位置
- **性能专用日志**: `Logs/perf.log`
- **详细调试日志**: `Logs/debug.log`
- **一般信息日志**: `Logs/info.log`
- **警告日志**: `Logs/warn.log`

### 日志格式示例

```
2024-01-15 10:30:15.123 [性能计时结果] 操作: 数据库查询 | 耗时: 245.67ms (245毫秒) | 位置: UserService.cs:GetUsers:42
2024-01-15 10:30:20.456 [性能警告] 操作: 文件处理 | 耗时: 1234.56ms (1秒234毫秒) | 位置: FileProcessor.cs:ProcessFile:28 | 超过阈值: 1000ms
2024-01-15 10:31:05.789 [性能计时结果] 操作: 数据传输 | 耗时: 65432.10ms (1分钟5秒432毫秒) | 位置: DataTransfer.cs:Transfer:156
```

### 时间格式优化
- **中文直观显示**: 使用 `Utility.GetHourMinutesInfo` 方法，将耗时转换为易读的中文格式
- **智能省略**: 自动省略前面的0值（如0天、0小时、0分钟），只显示有意义的时间单位
- **双重显示**: 同时提供毫秒数和可读格式，满足不同分析需求

## 🔧 配置说明

### 警告阈值设置

根据不同操作类型设置合适的警告阈值：

```csharp
// 数据库操作 - 建议100-500ms
StopwatchHelper.Measure(() => dbQuery(), "数据库查询", warnThresholdMs: 300);

// 文件I/O - 建议50-200ms  
StopwatchHelper.Measure(() => fileOperation(), "文件操作", warnThresholdMs: 100);

// 网络请求 - 建议1000-3000ms
StopwatchHelper.Measure(() => httpRequest(), "网络请求", warnThresholdMs: 2000);
```

## 📈 性能分析工具

### 1. PowerShell 分析脚本

使用提供的 PowerShell 脚本快速分析性能日志：

```powershell
# 基本分析
.\Docs\Analysis\性能日志分析脚本.ps1

# 自定义参数
.\Docs\Analysis\性能日志分析脚本.ps1 -LogPath "Logs\perf.log" -TopCount 20 -SlowThreshold 2000
```

### 2. C# 分析器

```csharp
var analyzer = new PerformanceAnalyzer();

// 解析日志文件
var records = analyzer.ParseLogFile("Logs/perf.log");

// 生成统计报告
var statistics = analyzer.GenerateStatistics(records);

// 检测性能退化
var degradations = analyzer.IdentifyPerformanceDegradation(records);

// 生成详细报告
analyzer.GeneratePerformanceReport("Logs/perf.log", "performance_report.md");
```

## 🎯 最佳实践

### ✅ 推荐做法

1. **为关键路径添加监控**
   ```csharp
   // 关键业务操作
   StopwatchHelper.Measure(() => criticalBusinessLogic(), "关键业务逻辑");
   
   // 外部依赖调用
   StopwatchHelper.Measure(() => externalApiCall(), "外部API调用");
   ```

2. **使用描述性操作名称**
   ```csharp
   // 好的命名
   StopwatchHelper.Measure(() => code, "用户登录验证");
   StopwatchHelper.Measure(() => code, "订单数据同步");
   
   // 避免的命名
   StopwatchHelper.Measure(() => code, "操作1");
   StopwatchHelper.Measure(() => code, "test");
   ```

3. **分层监控策略**
   ```csharp
   // 方法级别监控
   public async Task<List<User>> GetUsersAsync()
   {
       return await StopwatchHelper.MeasureAsync(async () =>
       {
           // 操作级别监控
           var data = await StopwatchHelper.MeasureAsync(async () =>
           {
               return await database.QueryAsync<User>("SELECT * FROM Users");
           }, "数据库查询-用户表");
           
           return data.ToList();
       }, "获取用户列表");
   }
   ```

### ❌ 避免做法

1. **过度监控** - 不要为每个简单操作都添加监控
2. **阈值设置过低** - 避免产生过多无意义的警告
3. **忽略异常情况** - 确保异常处理中也包含性能监控

## 🔍 性能问题排查流程

### 1. 识别问题

```powershell
# 运行分析脚本
.\Docs\Analysis\性能日志分析脚本.ps1 -SlowThreshold 1000

# 查看最慢的操作
Get-Content "Logs\perf.log" | Select-String "性能警告" | Select-Object -Last 10
```

### 2. 定位根因

1. **查看调用位置**: 根据日志中的文件名和行号定位代码
2. **分析调用频率**: 高频调用的慢操作影响更大
3. **检查时间趋势**: 确定是偶发问题还是持续退化

### 3. 验证优化效果

```csharp
// 优化前后对比
var beforeOptimization = StopwatchHelper.Measure(() => oldMethod(), "旧方法");
var afterOptimization = StopwatchHelper.Measure(() => newMethod(), "新方法");

var improvement = beforeOptimization.TotalMilliseconds - afterOptimization.TotalMilliseconds;
Console.WriteLine($"性能提升: {improvement:F2}ms ({improvement / beforeOptimization.TotalMilliseconds * 100:F1}%)");
```

## 📋 常见使用场景

### 1. 数据库操作监控

```csharp
public async Task<List<Order>> GetOrdersAsync(int userId)
{
    return await StopwatchHelper.MeasureAsync(async () =>
    {
        using var connection = new SqlConnection(connectionString);
        return await connection.QueryAsync<Order>(
            "SELECT * FROM Orders WHERE UserId = @UserId", 
            new { UserId = userId });
    }, $"查询用户订单-{userId}", warnThresholdMs: 500);
}
```

### 2. 文件操作监控

```csharp
public void ProcessFile(string filePath)
{
    StopwatchHelper.Measure(() =>
    {
        var content = File.ReadAllText(filePath);
        var processed = ProcessContent(content);
        File.WriteAllText(filePath + ".processed", processed);
    }, $"文件处理-{Path.GetFileName(filePath)}", warnThresholdMs: 200);
}
```

### 3. 批量操作监控

```csharp
public void ProcessBatch(List<Item> items)
{
    var totalStopwatch = new StopwatchHelper("批量处理总计");
    totalStopwatch.Start();
    
    foreach (var item in items)
    {
        StopwatchHelper.Measure(() =>
        {
            ProcessSingleItem(item);
        }, $"处理单项-{item.Id}", warnThresholdMs: 100);
    }
    
    var totalTime = totalStopwatch.Stop();
    // 计算耗时使用 Utility.GetHourMinutesInfo 方法，用户更加直观查看
    var readableTime = Utility.GetHourMinutesInfo(totalTime);
    Console.WriteLine($"批量处理完成，总耗时: {totalTime.TotalSeconds:F2}秒 ({readableTime})");
}
```

## 🛠️ 故障排查

### 常见问题

1. **日志文件未生成**
   - 检查 log4net 配置文件
   - 确认日志目录权限
   - 验证 AppLog 初始化

2. **性能数据不准确**
   - 确保在正确位置调用 Start() 和 Stop()
   - 避免在计时期间重复调用 Start()
   - 检查是否有异常导致计时器未正确停止

3. **日志文件过大**
   - 配置日志轮转策略
   - 定期清理历史日志
   - 调整监控粒度

### 调试技巧

```csharp
var stopwatch = new StopwatchHelper("调试操作");

// 检查计时器状态
if (!stopwatch.IsRunning)
    stopwatch.Start();

// 获取当前已计时时长
var currentElapsed = stopwatch.Elapsed;
Console.WriteLine($"当前已耗时: {currentElapsed.TotalMilliseconds:F2}ms");

// 确保异常情况下也能记录时间
try
{
    DoSomething();
}
finally
{
    if (stopwatch.IsRunning)
        stopwatch.Stop();
}
```

## 📚 相关文档

- [StopwatchHelper性能监控功能说明.md](../Features/StopwatchHelper性能监控功能说明.md) - 详细功能说明
- [StopwatchHelper性能分析示例.cs](../Examples/StopwatchHelper性能分析示例.cs) - 完整使用示例
- [StopwatchHelper性能监控测试.cs](../Test/StopwatchHelper性能监控测试.cs) - 测试用例
- [性能日志分析脚本.ps1](../Analysis/性能日志分析脚本.ps1) - PowerShell 分析工具

## 🤝 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看相关文档和示例代码
2. 检查日志文件中的错误信息
3. 运行测试用例验证功能
4. 联系开发团队获取支持

---

**记住**: 性能监控是一个持续的过程，定期分析性能数据，及时发现和解决性能问题，才能保持应用程序的最佳性能状态。
