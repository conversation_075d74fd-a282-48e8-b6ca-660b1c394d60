using System;
using System.IO;
using log4net;
using log4net.Config;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// Log4net配置管理器，支持动态切换不同环境的配置
    /// </summary>
    public static class Log4netConfigManager
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(Log4netConfigManager));

        /// <summary>
        /// 环境类型枚举
        /// </summary>
        public enum EnvironmentType
        {
            /// <summary>
            /// 开发环境 - 详细日志，包含DEBUG信息
            /// </summary>
            Development,
            
            /// <summary>
            /// 测试环境 - 平衡性能和信息量
            /// </summary>
            Testing,
            
            /// <summary>
            /// 生产环境 - 最优性能，仅记录重要信息
            /// </summary>
            Production,
            
            /// <summary>
            /// 优化配置 - 通用优化版本
            /// </summary>
            Optimized
        }

        /// <summary>
        /// 根据环境类型初始化Log4net配置
        /// </summary>
        /// <param name="environmentType">环境类型</param>
        /// <returns>是否初始化成功</returns>
        public static bool InitializeConfiguration(EnvironmentType environmentType)
        {
            try
            {
                var configFileName = GetConfigFileName(environmentType);
                var configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                    "Configs", "Log4netConfig", configFileName);

                if (!File.Exists(configFilePath))
                {
                    // 如果指定配置文件不存在，回退到默认配置
                    configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "log4net.config");
                    if (!File.Exists(configFilePath))
                    {
                        throw new FileNotFoundException($"Log4net配置文件不存在: {configFilePath}");
                    }
                }

                XmlConfigurator.ConfigureAndWatch(new FileInfo(configFilePath));
                
                _logger?.Info($"Log4net配置已加载: {configFileName} (环境: {environmentType})");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Log4net配置初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 自动检测环境并初始化配置
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public static bool AutoInitializeConfiguration()
        {
            var environmentType = DetectEnvironment();
            return InitializeConfiguration(environmentType);
        }

        /// <summary>
        /// 动态切换Log4net配置
        /// </summary>
        /// <param name="environmentType">目标环境类型</param>
        /// <returns>是否切换成功</returns>
        public static bool SwitchConfiguration(EnvironmentType environmentType)
        {
            try
            {
                // 重新初始化配置
                var success = InitializeConfiguration(environmentType);
                
                if (success)
                {
                    _logger?.Info($"Log4net配置已切换到: {environmentType}");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                _logger?.Error($"Log4net配置切换失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取当前配置信息
        /// </summary>
        /// <returns>配置信息字符串</returns>
        public static string GetCurrentConfigurationInfo()
        {
            try
            {
                var repository = LogManager.GetRepository();
                var appenders = repository.GetAppenders();
                
                var info = $"Log4net配置信息:\n";
                info += $"- 仓库名称: {repository.Name}\n";
                info += $"- Appender数量: {appenders.Length}\n";
                info += $"- 根日志级别: {((log4net.Repository.Hierarchy.Hierarchy)repository).Root.Level}\n";
                
                foreach (var appender in appenders)
                {
                    info += $"- Appender: {appender.Name} ({appender.GetType().Name})\n";
                }
                
                return info;
            }
            catch (Exception ex)
            {
                return $"获取配置信息失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 检测当前运行环境
        /// </summary>
        /// <returns>环境类型</returns>
        private static EnvironmentType DetectEnvironment()
        {
            // 检查编译配置
#if DEBUG
            return EnvironmentType.Development;
#endif

            // 检查环境变量
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") 
                           ?? Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT")
                           ?? Environment.GetEnvironmentVariable("LOG_ENVIRONMENT");

            return environment?.ToLowerInvariant() switch
            {
                "development" => EnvironmentType.Development,
                "testing" => EnvironmentType.Testing,
                "production" => EnvironmentType.Production,
                _ => EnvironmentType.Optimized // 默认使用优化配置
            };
        }

        /// <summary>
        /// 根据环境类型获取配置文件名
        /// </summary>
        /// <param name="environmentType">环境类型</param>
        /// <returns>配置文件名</returns>
        private static string GetConfigFileName(EnvironmentType environmentType)
        {
            return environmentType switch
            {
                EnvironmentType.Development => "log4net-development.config",
                EnvironmentType.Testing => "log4net-optimized.config",
                EnvironmentType.Production => "log4net-production.config",
                EnvironmentType.Optimized => "log4net-optimized.config",
                _ => "log4net.config"
            };
        }

        /// <summary>
        /// 验证配置文件是否存在
        /// </summary>
        /// <param name="environmentType">环境类型</param>
        /// <returns>配置文件是否存在</returns>
        public static bool ValidateConfigurationExists(EnvironmentType environmentType)
        {
            var configFileName = GetConfigFileName(environmentType);
            var configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                "Configs", "Log4netConfig", configFileName);
            
            return File.Exists(configFilePath);
        }

        /// <summary>
        /// 获取所有可用的配置文件
        /// </summary>
        /// <returns>可用配置文件列表</returns>
        public static string[] GetAvailableConfigurations()
        {
            var configDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                "Configs", "Log4netConfig");
            
            if (!Directory.Exists(configDir))
                return new string[0];
            
            return Directory.GetFiles(configDir, "*.config");
        }
    }
}
