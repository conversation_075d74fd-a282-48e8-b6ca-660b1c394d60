﻿//=====================================================================================
// All Rights Reserved , Copyright © Learun 2013
//=====================================================================================

#nullable enable

using System;
using System.Collections.Generic;
using System.Text;
using System.Reflection;
using System.Data;
using System.Collections;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Converters;
using log4net;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 转换Json格式帮助类
    /// </summary>
    public static class JsonHelper
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(JsonHelper));
        private static readonly JsonSerializerSettings? DefaultSettings = new()
        {
            Converters = new List<JsonConverter>
            {
                new IsoDateTimeConverter { DateTimeFormat = "yyyy-MM-dd HH:mm:ss" },
                new StringEnumConverter()
            },
            Formatting = Formatting.Indented
        };

        public static object? ToJson(this string Json)
        {
            return JsonConvert.DeserializeObject(Json);
        }

        public static string ToJson(this object? obj)
        {
            return JsonConvert.SerializeObject(obj, DefaultSettings);
        }

        public static List<T>? JonsToList<T>(this string Json)
        {
            return JsonConvert.DeserializeObject<List<T>>(Json);
        }

        public static T? JsonToEntity<T>(this string json)
        {
            try
            {
                return JsonConvert.DeserializeObject<T>(json, DefaultSettings);
            }
            catch (Exception ex)
            {
                _logger.Error($"JSON反序列化失败: {ex.Message}", ex);
                return default;
            }
        }
    }
}