using System;
using System.Threading.Tasks;
using System.IO;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Test
{
    /// <summary>
    /// StopwatchHelper 性能监控功能测试
    /// 用于验证性能监控和日志记录功能是否正常工作
    /// </summary>
    public class StopwatchHelperPerformanceTest
    {
        private readonly string _testLogPath;
        private readonly PerformanceAnalyzer _analyzer;

        public StopwatchHelperPerformanceTest()
        {
            _testLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "test_perf.log");
            _analyzer = new PerformanceAnalyzer();
        }

        /// <summary>
        /// 运行完整的性能监控测试
        /// </summary>
        public async Task RunCompleteTest()
        {
            Console.WriteLine("=== StopwatchHelper 性能监控测试开始 ===\n");

            // 1. 基础功能测试
            Console.WriteLine("1. 基础功能测试...");
            TestBasicFunctionality();

            // 2. 性能警告测试
            Console.WriteLine("\n2. 性能警告测试...");
            TestPerformanceWarnings();

            // 3. 异步操作测试
            Console.WriteLine("\n3. 异步操作测试...");
            await TestAsyncOperations();

            // 4. 异常处理测试
            Console.WriteLine("\n4. 异常处理测试...");
            TestExceptionHandling();

            // 5. 批量操作测试
            Console.WriteLine("\n5. 批量操作测试...");
            TestBatchOperations();

            // 6. 性能退化模拟测试
            Console.WriteLine("\n6. 性能退化模拟测试...");
            TestPerformanceDegradation();

            // 7. 日志分析测试
            Console.WriteLine("\n7. 日志分析测试...");
            await TestLogAnalysis();

            Console.WriteLine("\n=== 所有测试完成 ===");
            Console.WriteLine("请查看以下日志文件获取详细信息:");
            Console.WriteLine($"- {Path.Combine("Logs", "perf.log")} - 性能专用日志");
            Console.WriteLine($"- {Path.Combine("Logs", "info.log")} - 信息日志");
            Console.WriteLine($"- {Path.Combine("Logs", "debug.log")} - 调试日志");
        }

        /// <summary>
        /// 测试基础功能
        /// </summary>
        private void TestBasicFunctionality()
        {
            // 测试手动控制计时
            var stopwatch = new StopwatchHelper("基础功能测试");
            stopwatch.Start();
            
            SimulateWork(200);
            
            var elapsed = stopwatch.Stop();
            Console.WriteLine($"  手动计时结果: {elapsed.TotalMilliseconds:F2}ms");

            // 测试重启功能
            stopwatch.Start();
            SimulateWork(150);
            var phase1 = stopwatch.Restart();
            
            SimulateWork(100);
            var phase2 = stopwatch.Stop();
            
            Console.WriteLine($"  阶段1耗时: {phase1.TotalMilliseconds:F2}ms");
            Console.WriteLine($"  阶段2耗时: {phase2.TotalMilliseconds:F2}ms");

            // 测试静态方法
            var staticElapsed = StopwatchHelper.Measure(() =>
            {
                SimulateWork(180);
            }, "静态方法测试");
            
            Console.WriteLine($"  静态方法耗时: {staticElapsed.TotalMilliseconds:F2}ms");
        }

        /// <summary>
        /// 测试性能警告功能
        /// </summary>
        private void TestPerformanceWarnings()
        {
            // 测试正常操作（不触发警告）
            StopwatchHelper.Measure(() =>
            {
                SimulateWork(300);
            }, "正常操作", warnThresholdMs: 500);

            // 测试慢操作（触发警告）
            StopwatchHelper.Measure(() =>
            {
                SimulateWork(800);
            }, "慢操作", warnThresholdMs: 500);

            // 测试极慢操作（触发警告）
            StopwatchHelper.Measure(() =>
            {
                SimulateWork(1200);
            }, "极慢操作", warnThresholdMs: 1000);

            Console.WriteLine("  性能警告测试完成，请查看日志文件");
        }

        /// <summary>
        /// 测试异步操作
        /// </summary>
        private async Task TestAsyncOperations()
        {
            // 测试异步操作监控
            var elapsed1 = await StopwatchHelper.MeasureAsync(async () =>
            {
                await SimulateAsyncWork(400);
            }, "异步操作1");

            var elapsed2 = await StopwatchHelper.MeasureAsync(async () =>
            {
                await SimulateAsyncWork(600);
                await SimulateAsyncWork(200);
            }, "异步操作2", warnThresholdMs: 500);

            Console.WriteLine($"  异步操作1耗时: {elapsed1.TotalMilliseconds:F2}ms");
            Console.WriteLine($"  异步操作2耗时: {elapsed2.TotalMilliseconds:F2}ms");
        }

        /// <summary>
        /// 测试异常处理
        /// </summary>
        private void TestExceptionHandling()
        {
            try
            {
                StopwatchHelper.Measure(() =>
                {
                    SimulateWork(300);
                    throw new InvalidOperationException("测试异常");
                }, "异常操作测试");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  捕获异常: {ex.Message}");
            }

            try
            {
                var stopwatch = new StopwatchHelper("手动异常测试");
                stopwatch.Start();
                SimulateWork(250);
                throw new ArgumentException("手动异常");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  手动异常测试: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试批量操作
        /// </summary>
        private void TestBatchOperations()
        {
            var batchStopwatch = new StopwatchHelper("批量操作总计");
            batchStopwatch.Start();

            for (int i = 1; i <= 5; i++)
            {
                StopwatchHelper.Measure(() =>
                {
                    // 模拟逐渐变慢的操作
                    SimulateWork(100 + i * 50);
                }, $"批量操作第{i}次", warnThresholdMs: 200);
            }

            var totalElapsed = batchStopwatch.Stop();
            Console.WriteLine($"  批量操作总耗时: {totalElapsed.TotalMilliseconds:F2}ms");
        }

        /// <summary>
        /// 测试性能退化模拟
        /// </summary>
        private void TestPerformanceDegradation()
        {
            // 模拟同一操作在不同时间的性能变化
            for (int round = 1; round <= 3; round++)
            {
                for (int i = 1; i <= 5; i++)
                {
                    var baseTime = 200;
                    var degradationFactor = round * 0.5; // 每轮增加50%耗时
                    var actualTime = (int)(baseTime * (1 + degradationFactor));

                    StopwatchHelper.Measure(() =>
                    {
                        SimulateWork(actualTime);
                    }, "数据库查询操作", warnThresholdMs: 500);

                    // 短暂延迟模拟时间间隔
                    System.Threading.Thread.Sleep(10);
                }

                Console.WriteLine($"  第{round}轮性能测试完成，平均耗时约: {200 * (1 + round * 0.5):F0}ms");
            }
        }

        /// <summary>
        /// 测试日志分析功能
        /// </summary>
        private async Task TestLogAnalysis()
        {
            // 等待日志写入完成
            await Task.Delay(1000);

            var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "perf.log");
            
            if (!File.Exists(logPath))
            {
                Console.WriteLine("  性能日志文件不存在，跳过分析测试");
                return;
            }

            try
            {
                // 解析日志记录
                var records = _analyzer.ParseLogFile(logPath);
                Console.WriteLine($"  解析到 {records.Count} 条性能记录");

                if (records.Count > 0)
                {
                    // 生成统计报告
                    var statistics = _analyzer.GenerateStatistics(records, 5);
                    Console.WriteLine($"  生成 {statistics.Count} 项统计数据");

                    // 显示前3个最慢的操作
                    Console.WriteLine("  最慢的3个操作:");
                    for (int i = 0; i < Math.Min(3, statistics.Count); i++)
                    {
                        var stat = statistics[i];
                        Console.WriteLine($"    {i + 1}. {stat.OperationName}: 平均 {stat.AverageMs:F2}ms, 最大 {stat.MaxMs:F2}ms");
                    }

                    // 检测性能退化
                    var degradations = _analyzer.IdentifyPerformanceDegradation(records, 30.0);
                    if (degradations.Count > 0)
                    {
                        Console.WriteLine($"  检测到 {degradations.Count} 个性能退化操作");
                        foreach (var degradation in degradations.Take(3))
                        {
                            Console.WriteLine($"    - {degradation.OperationName}: 退化 {degradation.DegradationPercent:F1}%");
                        }
                    }

                    // 生成详细报告
                    var reportPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "performance_report.md");
                    _analyzer.GeneratePerformanceReport(logPath, reportPath);
                    Console.WriteLine($"  详细报告已生成: {reportPath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  日志分析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 模拟同步工作
        /// </summary>
        private void SimulateWork(int milliseconds)
        {
            System.Threading.Thread.Sleep(milliseconds);
        }

        /// <summary>
        /// 模拟异步工作
        /// </summary>
        private async Task SimulateAsyncWork(int milliseconds)
        {
            await Task.Delay(milliseconds);
        }

        /// <summary>
        /// 运行性能压力测试
        /// </summary>
        public async Task RunStressTest(int operationCount = 100)
        {
            Console.WriteLine($"=== 性能压力测试开始 (操作数量: {operationCount}) ===\n");

            var overallStopwatch = new StopwatchHelper("压力测试总计");
            overallStopwatch.Start();

            var random = new Random();
            var tasks = new List<Task>();

            // 并发执行多个操作
            for (int i = 0; i < operationCount; i++)
            {
                var operationIndex = i;
                var task = Task.Run(async () =>
                {
                    var operationType = operationIndex % 4;
                    var operationName = operationType switch
                    {
                        0 => "数据库操作",
                        1 => "文件操作",
                        2 => "网络请求",
                        _ => "计算操作"
                    };

                    var delay = random.Next(50, 500);
                    
                    if (operationIndex % 10 == 0) // 10%的操作是异步的
                    {
                        await StopwatchHelper.MeasureAsync(async () =>
                        {
                            await Task.Delay(delay);
                        }, $"{operationName}_{operationIndex}", warnThresholdMs: 300);
                    }
                    else
                    {
                        StopwatchHelper.Measure(() =>
                        {
                            System.Threading.Thread.Sleep(delay);
                        }, $"{operationName}_{operationIndex}", warnThresholdMs: 300);
                    }
                });

                tasks.Add(task);
            }

            await Task.WhenAll(tasks);
            var totalElapsed = overallStopwatch.Stop();

            // 计算耗时使用 Utility.GetHourMinutesInfo 方法，用户更加直观查看
            var readableTotalTime = Utility.GetHourMinutesInfo(totalElapsed);
            Console.WriteLine($"压力测试完成:");
            Console.WriteLine($"- 总操作数: {operationCount}");
            Console.WriteLine($"- 总耗时: {totalElapsed.TotalSeconds:F2}秒 ({readableTotalTime})");
            Console.WriteLine($"- 平均每操作: {totalElapsed.TotalMilliseconds / operationCount:F2}ms");
            Console.WriteLine($"- 吞吐量: {operationCount / totalElapsed.TotalSeconds:F2} 操作/秒");

            Console.WriteLine("\n=== 压力测试完成 ===");
        }
    }

    /// <summary>
    /// 测试程序入口
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var test = new StopwatchHelperPerformanceTest();

            if (args.Length > 0 && args[0] == "stress")
            {
                var count = args.Length > 1 && int.TryParse(args[1], out var c) ? c : 100;
                await test.RunStressTest(count);
            }
            else
            {
                await test.RunCompleteTest();
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
