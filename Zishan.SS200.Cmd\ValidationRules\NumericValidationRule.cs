﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace Zishan.SS200.Cmd.ValidationRules
{
    public interface IValidationRule
    {
        ValidationResult Validate(object value, CultureInfo cultureInfo);
    }

    public class NumericValidationRule : ValidationRule
    {
        public ValidationParams MinNum { get; set; }

        public ValidationParams MaxNum { get; set; }

        public ValidationParams IsValidationRequired { get; set; }

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            double result = 0;
            if (value != null && !double.TryParse(value.ToString(), out result))
            {
                return new ValidationResult(false, "输入的不是数字");
            }

            var isValidationRequired = (bool)IsValidationRequired.Data;

            isValidationRequired = false; // 无尘室内IR400调机参数设置校验，暂时不需要，后续需要再打开

            if (isValidationRequired)
            {
                var minNum = (double)MinNum.Data;
                var maxNum = (double)MaxNum.Data;
                if (result < minNum || result > maxNum)
                {
                    return new ValidationResult(false, $"输入的数字必须要在 {minNum}和{maxNum}之间");
                }
            }

            return ValidationResult.ValidResult;
        }
    }
}