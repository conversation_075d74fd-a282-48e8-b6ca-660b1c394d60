﻿AR8
T-axis nose to cassette
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			T-axis position status review
RS8 or others position
				RS8
					command done
				others position
					R-axisposition status review
(RS18 or others position)
						others position
							RA3 ALARM
						RS18
							slide out back sensor installation
SPS11
								SPS11=Y
									shuttle slide out sensor status review
shuttle DI19~DI20
										DI19=0 and DI20 =0
											AR39-RPS23
Z-axis postion to RPS23
RPS23=Z-axis for rotation
												AR10-RP8
													shuttle slide out sensor status review
shuttle DI19~DI20
														DI19=0 and DI20 =0
															compare paddle wafer slot status with paddle sensor
(LSS7 XX LSS8 XX) with (RDI1 RDI2)
																LSS7=RDI and LSS8=RDI2 
																	command done
																LSS7=1 RDI1=0
																	RA41 ALARM
																LSS7=0 RDI1=1
																	RA60 ALARM
																LSS7=1 RDI1=0
																	RA42 ALARM
																LSS7=0 RDI1=1
																	RA61 ALARM
														DI19=0 and DI20=1
															RA20 ALARM
														DI19=1 and DI20=0
															RA19 ALARM
														DI19=1 and DI20=1
															RA21 ALARM
										DI19=0 and DI20=1
											RA20 ALARM
										DI19=1 and DI20=0
											RA19 ALARM
										DI19=1 and DI20=1
											RA21 ALARM
								SPS11=N
									AR39-RPS23
Z-axis postion to RPS23
RPS23=Z-axis for rotation
										AR10-RP8
											command done
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM