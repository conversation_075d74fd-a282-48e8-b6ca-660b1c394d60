using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// Lift Pin状态枚举
    /// </summary>
    public enum EnuLiftPinStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// Lift Pin上升状态 (SP4: PDI14=1 PDI15=0)
        /// </summary>
        [Description("上升")]
        Up = 1,

        /// <summary>
        /// Lift Pin下降状态 (SP5: PDI14=0 PDI15=1)
        /// </summary>
        [Description("下降")]
        Down = 2,

        /// <summary>
        /// Lift Pin在上升与下降状态之间 (SP6: PDI14=0 PDI15=0)
        /// </summary>
        [Description("上下之间")]
        BetweenUpDown = 3
    }
}
