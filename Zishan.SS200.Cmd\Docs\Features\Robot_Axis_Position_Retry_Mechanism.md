# Robot轴位置检查重试机制改进

## 概述

针对Robot晶圆搬运过程中出现的"Z轴高度位置不正确"错误，我们改进了T轴和Z轴位置检查的重试机制，从原来的2次检查增加到5次重试机会，并在最后一次重试前添加用户确认对话框。

## 问题背景

### 原始问题
在Robot晶圆搬运测试中，发现在第二次循环的放片操作中出现以下错误：
```
❌ Nose端的CoolingTop位置，Z轴不在对应的高度位置
❌ R轴伸展失败: Z轴高度位置不正确
```

### 根本原因
1. **Z轴位置偏差**：Z轴移动目标位置2150步，实际位置2211步，差异61步
2. **状态检查逻辑**：状态检查使用配置参数计算期望位置，但实际位置存在偏差
3. **容差设置**：如果容差设置小于实际偏差，状态检查就会失败

## 解决方案

### 改进前的重试机制
```csharp
// T轴检查：只有2次检查机会
bool firstCheckResult = PerformTAxisPositionCheck(endType, stationType);
if (firstCheckResult) return true;

// 延迟200ms后重试一次
Task.Delay(200).Wait();
bool secondCheckResult = PerformTAxisPositionCheck(endType, stationType);
return secondCheckResult;

// Z轴检查：直接检查，无重试机制
var tAndZAxisHeightStatus = _interLock.SubsystemStatus.Robot.Status.EnuTAndZAxisHeightStatus;
bool zAxisPositionCorrect = CheckZAxisPosition(endType, stationType, tAndZAxisHeightStatus);
if (!zAxisPositionCorrect) return false;
```

### 改进后的重试机制

#### T轴位置检查重试
```csharp
// 最多5次检查机会
const int maxRetries = 5;
const int delayMs = 200;

// 首次检查
bool firstCheckResult = PerformTAxisPositionCheck(endType, stationType);
if (firstCheckResult) return true;

// 重试流程（第2-5次）
for (int retryCount = 1; retryCount < maxRetries; retryCount++)
{
    // 最后一次重试前弹出确认对话框
    if (retryCount == maxRetries - 1)
    {
        var dialogResult = MessageBox.Show(
            $"T轴位置检查已重试{retryCount}次仍未通过。\n\n" +
            $"端口类型: {endType}\n" +
            $"站点类型: {stationType}\n\n" +
            $"是否继续进行最后一次重试？",
            "T轴位置检查重试确认",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (dialogResult == MessageBoxResult.No) return false;
    }

    Task.Delay(delayMs).Wait();
    bool retryResult = PerformTAxisPositionCheck(endType, stationType);
    if (retryResult) return true;
}
```

#### Z轴位置检查重试
```csharp
// 使用重试机制检查Z轴位置
bool zAxisPositionCorrect = CheckZAxisPositionWithRetry(endType, stationType);

// CheckZAxisPositionWithRetry方法内部实现
const int maxRetries = 5;
const int delayMs = 200;

// 首次检查
bool firstCheckResult = PerformZAxisPositionCheck(endType, stationType);
if (firstCheckResult) return true;

// 重试流程（第2-5次）
for (int retryCount = 1; retryCount < maxRetries; retryCount++)
{
    // 最后一次重试前弹出确认对话框
    if (retryCount == maxRetries - 1)
    {
        var dialogResult = MessageBox.Show(
            $"Z轴高度位置检查已重试{retryCount}次仍未通过。\n\n" +
            $"端口类型: {endType}\n" +
            $"站点类型: {stationType}\n\n" +
            $"是否继续进行最后一次重试？",
            "Z轴高度位置检查重试确认",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (dialogResult == MessageBoxResult.No) return false;
    }

    Task.Delay(delayMs).Wait();
    bool retryResult = PerformZAxisPositionCheck(endType, stationType);
    if (retryResult) return true;
}
```

## 改进特性

### 1. 增加重试次数
- **原来**：T轴2次检查机会（首次 + 1次重试），Z轴无重试机制
- **现在**：T轴和Z轴都有5次检查机会（首次 + 4次重试）
- **优势**：显著提高成功率，应对更复杂的状态更新延迟情况

### 2. 用户确认机制
- **触发时机**：第4次检查失败后，第5次检查前
- **T轴对话框内容**：
  - 显示已重试次数
  - 显示端口类型和站点类型
  - 询问是否继续最后一次重试
- **Z轴对话框内容**：
  - 显示已重试次数
  - 显示端口类型和站点类型
  - 显示当前T和Z轴高度状态
  - 询问是否继续最后一次重试
- **用户选择**：
  - 点击"是"：继续最后一次重试
  - 点击"否"：直接返回失败

### 3. 详细日志记录
- 记录每次检查的结果和当前状态
- 记录重试次数和延迟时间
- 记录用户确认对话框的选择
- 提供清晰的成功/失败信息
- Z轴检查增加当前状态调试信息

### 4. 独立的重试方法
- **T轴**：`CheckTAxisPositionForZAxisMove` 方法
- **Z轴**：`CheckZAxisPositionWithRetry` 方法
- **核心检查**：`PerformTAxisPositionCheck` 和 `PerformZAxisPositionCheck` 方法
- **模块化设计**：便于维护和测试

## 使用场景

### 场景1：首次检查成功
```
检查T轴位置 - 端口类型: Nose, 站点类型: CoolingTop
✅ T轴位置检查通过 (首次检查)
```

### 场景2：T轴重试后成功
```
检查T轴位置 - 端口类型: Nose, 站点类型: CoolingTop
⚠️ T轴位置检查失败 (首次检查)，可能状态表还未更新，开始重试流程...
延迟200ms等待状态表更新 (第2次检查)
✅ T轴位置检查通过 (第2次检查成功)
```

### 场景3：Z轴重试后成功
```
检查Z轴高度位置 - 端口类型: Nose, 站点类型: CoolingTop
当前T和Z轴高度状态: NoseToCTGet
Z轴位置检查结果: false (期望状态匹配: Nose端到CoolingTop)
⚠️ Z轴高度位置检查失败 (首次检查)，开始重试流程...
延迟200ms等待状态表更新 (第3次检查)
当前T和Z轴高度状态: NoseToCTPut
Z轴位置检查结果: true (期望状态匹配: Nose端到CoolingTop)
✅ Z轴高度位置检查通过 (第3次检查成功)
```

### 场景4：T轴需要用户确认
```
检查T轴位置 - 端口类型: Nose, 站点类型: CoolingTop
⚠️ T轴位置检查失败 (首次检查)，开始重试流程...
⚠️ T轴位置检查失败 (第2次检查)
⚠️ T轴位置检查失败 (第3次检查)
⚠️ T轴位置检查失败 (第4次检查)
⚠️ 即将进行最后一次T轴位置检查重试 (第5次)
[弹出T轴确认对话框]
用户确认继续最后一次重试
延迟200ms等待状态表更新 (第5次检查)
✅ T轴位置检查通过 (第5次检查成功)
```

### 场景5：Z轴需要用户确认
```
检查Z轴高度位置 - 端口类型: Nose, 站点类型: CoolingTop
当前T和Z轴高度状态: NoseToCTGet
⚠️ Z轴高度位置检查失败 (首次检查)，开始重试流程...
⚠️ Z轴高度位置检查失败 (第2次检查)
⚠️ Z轴高度位置检查失败 (第3次检查)
⚠️ Z轴高度位置检查失败 (第4次检查)
⚠️ 即将进行最后一次Z轴高度位置检查重试 (第5次)
[弹出Z轴确认对话框]
用户确认继续最后一次重试
延迟200ms等待状态表更新 (第5次检查)
当前T和Z轴高度状态: NoseToCTPut
✅ Z轴高度位置检查通过 (第5次检查成功)
```

## 配置参数

### 重试参数
- `maxRetries`: 5 (最大重试次数)
- `delayMs`: 200 (每次重试间隔毫秒数)

### 可调整参数
如需调整重试参数，可以修改相应方法中的常量：
```csharp
const int maxRetries = 5;    // 可调整为其他值
const int delayMs = 200;     // 可调整延迟时间
```

## 测试验证

创建了专门的测试类来验证重试机制：
- `RobotAxisPositionRetryTest` - 综合轴位置测试
- `ZAxisRetryMechanismTest` - Z轴专项测试
- 测试首次检查成功场景
- 测试重试后成功场景
- 测试所有重试失败场景
- 模拟详细的重试流程

## 注意事项

1. **UI线程安全**：MessageBox调用需要在UI线程中执行
2. **异常处理**：重试过程中的异常会被捕获并记录
3. **用户体验**：对话框提供清晰的信息和选择
4. **性能影响**：每次重试有200ms延迟，最多增加800ms总延迟

## 未来改进建议

1. **动态容差调整**：根据历史偏差数据动态调整容差值
2. **智能重试间隔**：根据失败次数递增延迟时间
3. **状态预测**：基于运动轨迹预测状态更新时间
4. **配置化参数**：将重试次数和延迟时间配置化
