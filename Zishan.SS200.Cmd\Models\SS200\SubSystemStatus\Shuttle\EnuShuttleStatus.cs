using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle
{
    /// <summary>
    /// Shuttle状态枚举 (MSD1-MSD3)
    /// </summary>
    public enum EnuShuttleStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 空闲状态 (MSD1)
        /// </summary>
        [Description("空闲")]
        Idle = 1,

        /// <summary>
        /// 忙碌状态 (MSD2) - shuttle motion
        /// </summary>
        [Description("忙碌")]
        Busy = 2,

        /// <summary>
        /// 报警状态 (MSD3) - shuttle alarm
        /// </summary>
        [Description("报警")]
        Alarm = 3
    }
}