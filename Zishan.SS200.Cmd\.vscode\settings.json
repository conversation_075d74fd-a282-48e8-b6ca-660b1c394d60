{"editor.formatOnSave": true, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": true, "editor.trimTrailingWhitespace": true, "editor.insertFinalNewline": true, "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/packages": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/packages": true}, "omnisharp.enableRoslynAnalyzers": true, "omnisharp.enableEditorConfigSupport": true, "csharp.format.enable": true, "csharp.semanticHighlighting.enabled": true, "[csharp]": {"editor.defaultFormatter": "ms-dotnettools.csharp", "editor.formatOnSave": true, "editor.formatOnType": true}, "[xaml]": {"editor.defaultFormatter": "ms-dotnettools.csharp", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features", "editor.tabSize": 2}, "[xml]": {"editor.defaultFormatter": "DotJoshJohnson.xml", "editor.tabSize": 2}, "files.associations": {"*.xaml": "xml"}, "dotnet.defaultSolution": "Zishan.SS200.Cmd.sln"}