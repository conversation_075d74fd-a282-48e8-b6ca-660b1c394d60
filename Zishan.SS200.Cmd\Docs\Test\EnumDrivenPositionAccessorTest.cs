using System;
using System.Linq;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// 枚举驱动的位置参数访问器测试类
    /// 验证使用EnuRobotPositionParameterCodes枚举而不是字符串代码的改进实现
    /// </summary>
    public class EnumDrivenPositionAccessorTest
    {
        /// <summary>
        /// 测试枚举驱动的访问器基本功能
        /// </summary>
        public static bool TestEnumDrivenAccessorBasics()
        {
            try
            {
                Console.WriteLine("测试枚举驱动的访问器基本功能...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 测试几个代表性的访问器
                var rp1 = robotConfig.RP1_TAxisSmoothToCHA;
                var rp10 = robotConfig.RP10_RAxisSmoothExtendFaceToCHA;
                var rp19 = robotConfig.RP19_ZAxisHeightAtSmoothToCHA;
                var rp28 = robotConfig.RP28_ZAxisHeightToPinSearch;
                
                bool allAccessorsWork = rp1 != null && rp10 != null && rp19 != null && rp28 != null;
                
                Console.WriteLine($"  RP1 (T轴): {(rp1 != null ? "✓" : "✗")}");
                Console.WriteLine($"  RP10 (R轴): {(rp10 != null ? "✓" : "✗")}");
                Console.WriteLine($"  RP19 (Z轴): {(rp19 != null ? "✓" : "✗")}");
                Console.WriteLine($"  RP28 (Z轴): {(rp28 != null ? "✓" : "✗")}");
                
                return allAccessorsWork;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"枚举驱动访问器基本功能测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试枚举驱动的轴类型分类
        /// </summary>
        public static bool TestEnumDrivenAxisTypeClassification()
        {
            try
            {
                Console.WriteLine("测试枚举驱动的轴类型分类...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 测试T轴 (应该是AxisType = 1)
                var tAxisParams = new[]
                {
                    robotConfig.RP1_TAxisSmoothToCHA,
                    robotConfig.RP5_TAxisNoseToCHA,
                    robotConfig.RP9_TAxisZero
                };
                
                // 测试R轴 (应该是AxisType = 2)
                var rAxisParams = new[]
                {
                    robotConfig.RP10_RAxisSmoothExtendFaceToCHA,
                    robotConfig.RP15_RAxisNoseExtendFaceToCoolingChamber,
                    robotConfig.RP18_RAxisZeroPosition
                };
                
                // 测试Z轴 (应该是AxisType = 3)
                var zAxisParams = new[]
                {
                    robotConfig.RP19_ZAxisHeightAtSmoothToCHA,
                    robotConfig.RP25_ZAxisHeightAtNoseToCTGet,
                    robotConfig.RP28_ZAxisHeightToPinSearch
                };
                
                bool tAxisCorrect = tAxisParams.All(p => p?.AxisType == 1);
                bool rAxisCorrect = rAxisParams.All(p => p?.AxisType == 2);
                bool zAxisCorrect = zAxisParams.All(p => p?.AxisType == 3);
                
                Console.WriteLine($"  T轴类型分类 (期望=1): {(tAxisCorrect ? "✓" : "✗")}");
                Console.WriteLine($"  R轴类型分类 (期望=2): {(rAxisCorrect ? "✓" : "✗")}");
                Console.WriteLine($"  Z轴类型分类 (期望=3): {(zAxisCorrect ? "✓" : "✗")}");
                
                return tAxisCorrect && rAxisCorrect && zAxisCorrect;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"枚举驱动轴类型分类测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试枚举驱动的描述准确性
        /// </summary>
        public static bool TestEnumDrivenDescriptionAccuracy()
        {
            try
            {
                Console.WriteLine("测试枚举驱动的描述准确性...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 测试几个关键参数的描述
                var testCases = new[]
                {
                    new { Accessor = robotConfig.RP1_TAxisSmoothToCHA, ExpectedDesc = "T-axis smooth to CHA" },
                    new { Accessor = robotConfig.RP5_TAxisNoseToCHA, ExpectedDesc = "T-axis nose to CHA" },
                    new { Accessor = robotConfig.RP10_RAxisSmoothExtendFaceToCHA, ExpectedDesc = "R-axis smooth extend and face to CHA" },
                    new { Accessor = robotConfig.RP18_RAxisZeroPosition, ExpectedDesc = "R-axis zero position" },
                    new { Accessor = robotConfig.RP19_ZAxisHeightAtSmoothToCHA, ExpectedDesc = "Z-axis height at smooth to CHA" },
                    new { Accessor = robotConfig.RP27_ZAxisZeroPosition, ExpectedDesc = "Z-axis zero position" },
                    new { Accessor = robotConfig.RP28_ZAxisHeightToPinSearch, ExpectedDesc = "Z-axis height to pin search" }
                };
                
                int correctDescriptions = 0;
                foreach (var testCase in testCases)
                {
                    bool isCorrect = testCase.Accessor?.Content == testCase.ExpectedDesc;
                    Console.WriteLine($"  {testCase.Accessor?.Code}: {(isCorrect ? "✓" : "✗")} '{testCase.Accessor?.Content}'");
                    if (isCorrect) correctDescriptions++;
                }
                
                bool allCorrect = correctDescriptions == testCases.Length;
                Console.WriteLine($"  描述准确性: {correctDescriptions}/{testCases.Length} {(allCorrect ? "✓" : "✗")}");
                
                return allCorrect;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"枚举驱动描述准确性测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试枚举驱动的性能优势
        /// </summary>
        public static bool TestEnumDrivenPerformance()
        {
            try
            {
                Console.WriteLine("测试枚举驱动的性能优势...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 多次访问同一个参数，验证缓存机制
                var startTime = DateTime.Now;
                
                for (int i = 0; i < 1000; i++)
                {
                    var rp1 = robotConfig.RP1_TAxisSmoothToCHA;
                    var rp10 = robotConfig.RP10_RAxisSmoothExtendFaceToCHA;
                    var rp28 = robotConfig.RP28_ZAxisHeightToPinSearch;
                }
                
                var endTime = DateTime.Now;
                var duration = endTime - startTime;
                
                Console.WriteLine($"  1000次访问耗时: {duration.TotalMilliseconds:F2}ms");
                
                // 验证缓存机制 - 多次访问应该返回相同实例
                var rp1First = robotConfig.RP1_TAxisSmoothToCHA;
                var rp1Second = robotConfig.RP1_TAxisSmoothToCHA;
                var rp1Third = robotConfig.RP1_TAxisSmoothToCHA;
                
                bool cacheWorks = ReferenceEquals(rp1First, rp1Second) && ReferenceEquals(rp1Second, rp1Third);
                Console.WriteLine($"  缓存机制: {(cacheWorks ? "✓" : "✗")}");
                
                // 性能应该很好 (小于100ms) 且缓存正常工作
                bool performanceGood = duration.TotalMilliseconds < 100 && cacheWorks;
                
                return performanceGood;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"枚举驱动性能测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试枚举驱动的类型安全性
        /// </summary>
        public static bool TestEnumDrivenTypeSafety()
        {
            try
            {
                Console.WriteLine("测试枚举驱动的类型安全性...");
                
                var ss200Main = SS200InterLockMain.Instance;
                var robotConfig = ss200Main.SubsystemConfigure.Robot;
                
                // 验证所有枚举值都有对应的访问器
                var enumValues = Enum.GetValues<EnuRobotPositionParameterCodes>();
                Console.WriteLine($"  枚举定义的参数数量: {enumValues.Length}");
                
                // 使用反射获取所有RP访问器
                var rpProperties = robotConfig.GetType()
                    .GetProperties()
                    .Where(p => p.Name.StartsWith("RP") && p.Name.Contains("_"))
                    .ToArray();
                
                Console.WriteLine($"  实现的访问器数量: {rpProperties.Length}");
                
                // 验证数量匹配
                bool countMatches = enumValues.Length == rpProperties.Length;
                Console.WriteLine($"  数量匹配: {(countMatches ? "✓" : "✗")}");
                
                // 验证每个枚举值都有对应的访问器
                int matchedAccessors = 0;
                foreach (var enumValue in enumValues)
                {
                    string expectedPropertyPrefix = $"{enumValue}_";
                    bool hasMatchingProperty = rpProperties.Any(p => p.Name.StartsWith(expectedPropertyPrefix));
                    if (hasMatchingProperty) matchedAccessors++;
                }
                
                bool allEnumsHaveAccessors = matchedAccessors == enumValues.Length;
                Console.WriteLine($"  枚举覆盖率: {matchedAccessors}/{enumValues.Length} {(allEnumsHaveAccessors ? "✓" : "✗")}");
                
                return countMatches && allEnumsHaveAccessors;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"枚举驱动类型安全性测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行所有枚举驱动的位置参数访问器测试
        /// </summary>
        public static void RunAllEnumDrivenTests()
        {
            Console.WriteLine("=== 枚举驱动位置参数访问器测试开始 ===\n");
            
            bool basicsTest = TestEnumDrivenAccessorBasics();
            Console.WriteLine();
            
            bool axisTypeTest = TestEnumDrivenAxisTypeClassification();
            Console.WriteLine();
            
            bool descriptionTest = TestEnumDrivenDescriptionAccuracy();
            Console.WriteLine();
            
            bool performanceTest = TestEnumDrivenPerformance();
            Console.WriteLine();
            
            bool typeSafetyTest = TestEnumDrivenTypeSafety();
            Console.WriteLine();
            
            // 汇总测试结果
            int passedTests = 0;
            int totalTests = 5;
            
            if (basicsTest) passedTests++;
            if (axisTypeTest) passedTests++;
            if (descriptionTest) passedTests++;
            if (performanceTest) passedTests++;
            if (typeSafetyTest) passedTests++;
            
            Console.WriteLine("=== 枚举驱动测试结果汇总 ===");
            Console.WriteLine($"通过测试: {passedTests}/{totalTests}");
            Console.WriteLine($"测试状态: {(passedTests == totalTests ? "全部通过 ✓" : "部分失败 ✗")}");
            
            if (passedTests == totalTests)
            {
                Console.WriteLine("\n🎉 枚举驱动位置参数访问器测试全部通过！");
                Console.WriteLine("✅ 类型安全性提升：使用强类型枚举替代字符串");
                Console.WriteLine("✅ 性能优化：避免字符串解析和枚举转换");
                Console.WriteLine("✅ 编译时检查：IDE智能提示和类型验证");
                Console.WriteLine("✅ 维护性增强：重构安全，减少运行时错误");
            }
            else
            {
                Console.WriteLine("\n⚠️ 部分枚举驱动测试失败，请检查实现。");
            }
        }
    }
}
