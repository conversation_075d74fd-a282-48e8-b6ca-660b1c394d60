using System.ComponentModel;

namespace Zish<PERSON>.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// 前级真空状态枚举
    /// </summary>
    public enum EnuForlineVacuumStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 传感器显示前级无真空 (SP21: PDI5=0)
        /// </summary>
        [Description("无真空")]
        NoVacuum = 1,

        /// <summary>
        /// 传感器显示前级有真空 (SP22: PDI5=1)
        /// </summary>
        [Description("有真空")]
        HasVacuum = 2
    }
}
