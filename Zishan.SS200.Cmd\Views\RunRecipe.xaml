﻿<UserControl
    x:Class="Zishan.SS200.Cmd.Views.RunRecipe"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:UserControls="clr-namespace:Zishan.SS200.Cmd.UserControls"
    xmlns:behaviors="clr-namespace:Zishan.SS200.Cmd.Behaviors"
    xmlns:conv="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dvm="clr-namespace:Zishan.SS200.Cmd.ViewModels.DesignViewModels"
    xmlns:enums="clr-namespace:Zishan.SS200.Cmd.Enums"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:md="clr-namespace:MaterialDesignThemes.Wpf;assembly=MaterialDesignThemes.Wpf"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:viewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    MinWidth="420"
    d:Background="LightBlue"
    d:DataContext="{d:DesignInstance Type={x:Type viewModels:RunRecipeViewModel}}"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <UserControl.Resources>
        <conv:RecipeNameToDetailConverter x:Key="RecipeNameToDetailConverter" />
    </UserControl.Resources>

    <md:DialogHost Identifier="{x:Static local:RunRecipe.ViewName}">
        <md:DrawerHost>
            <DockPanel>
                <md:ColorZone
                    x:Name="ColorZone"
                    Padding="16"
                    md:ElevationAssist.Elevation="Dp4"
                    DockPanel.Dock="Top"
                    Mode="PrimaryMid">
                    <Grid>
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="24"
                            FontWeight="Bold"
                            Text="运行配方" />
                        <Button
                            HorizontalAlignment="Right"
                            Command="{Binding CancelCommand}"
                            Content="✕"
                            Style="{StaticResource MaterialDesignFlatButton}" />
                    </Grid>
                </md:ColorZone>

                <Grid Margin="32">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  条码输入  -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,16">
                        <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="左边条码输入" />
                        <TextBox Margin="0,8,0,0" Text="{Binding LeftBarcode}" />
                    </StackPanel>

                    <StackPanel Grid.Row="1" Margin="0,0,0,16">
                        <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="右边条码输入" />
                        <TextBox Margin="0,8,0,0" Text="{Binding RightBarcode}" />
                    </StackPanel>

                    <!--  配方选择  -->
                    <StackPanel Grid.Row="2" Margin="0,0,0,16">
                        <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="制定配方" />
                        <ComboBox
                            Margin="0,8,0,0"
                            ItemsSource="{Binding RecipeList}"
                            SelectedItem="{Binding CurSelectedRecipe}"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock
                                            Grid.Column="0"
                                            VerticalAlignment="Center"
                                            FontSize="20"
                                            FontWeight="Bold"
                                            Text="{Binding StringFormat='{}{0}'}" />
                                        <!--  显示选中项的详细信息  -->
                                        <ContentControl
                                            Grid.Column="1"
                                            Margin="10,0,0,0"
                                            VerticalAlignment="Center"
                                            Content="{Binding Converter={StaticResource RecipeNameToDetailConverter}}">
                                            <ContentControl.ContentTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto" />
                                                            <RowDefinition Height="Auto" />
                                                            <RowDefinition Height="Auto" />
                                                        </Grid.RowDefinitions>
                                                        <TextBlock
                                                            Grid.Row="0"
                                                            FontWeight="Bold"
                                                            Text="{Binding ChRecipeName, StringFormat={}CH:{0}, TargetNullValue='腔体配方'}" />
                                                        <TextBlock
                                                            Grid.Row="1"
                                                            FontWeight="Bold"
                                                            Text="{Binding CoolingRecipeName, StringFormat={}CP:{0}, TargetNullValue='Cooling配方'}" />
                                                        <!--  机限多值绑定  -->
                                                        <TextBlock
                                                            Grid.Row="2"
                                                            VerticalAlignment="Center"
                                                            FontWeight="Light">
                                                            <TextBlock.Text>
                                                                <MultiBinding StringFormat="{}机限 CHA:{0},CHB:{2},CHC:{4}" TargetNullValue="机限">
                                                                    <Binding Path="ChaEnable" />
                                                                    <Binding Path="ChaOrder" />
                                                                    <Binding Path="ChbEnable" />
                                                                    <Binding Path="ChbOrder" />
                                                                    <Binding Path="ChcEnable" />
                                                                    <Binding Path="ChcOrder" />
                                                                </MultiBinding>
                                                            </TextBlock.Text>
                                                        </TextBlock>
                                                    </Grid>
                                                </DataTemplate>
                                            </ContentControl.ContentTemplate>
                                        </ContentControl>
                                    </Grid>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                    </StackPanel>

                    <!--  数量设定  -->
                    <Grid Grid.Row="3" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="Top 数量" />
                            <ComboBox
                                Margin="0,8,8,0"
                                ItemsSource="{Binding WaferCountList}"
                                SelectedItem="{Binding Top, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}" />
                        </StackPanel>
                        <StackPanel Grid.Column="1">
                            <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="Bottom 数量" />
                            <ComboBox
                                Margin="8,8,0,0"
                                ItemsSource="{Binding WaferCountList}"
                                SelectedItem="{Binding Bottom, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}" />
                        </StackPanel>
                    </Grid>

                    <!--  高级数量设定  -->
                    <StackPanel Grid.Row="4" Margin="0,0,0,24">
                        <TextBlock
                            Margin="0,0,0,8"
                            Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                            Text="高级数量设定" />
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock
                                    Margin="0,0,0,8"
                                    Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                    Text="左侧" />
                                <ListBox
                                    Name="LeftListBox"
                                    MaxHeight="200"
                                    behaviors:SelectedItemsBehavior.SelectedItems="{Binding LeftSelectedItems, Mode=TwoWay}"
                                    ItemsSource="{Binding WaferCountList}"
                                    ScrollViewer.VerticalScrollBarVisibility="Auto"
                                    SelectionChanged="ListBox_SelectionChanged_Left"
                                    SelectionMode="Multiple"
                                    Style="{StaticResource MaterialDesignListBox}">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Margin="4">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <CheckBox
                                                    Margin="0,0,8,0"
                                                    VerticalAlignment="Center"
                                                    IsChecked="{Binding IsSelected, RelativeSource={RelativeSource AncestorType=ListBoxItem}, Mode=TwoWay}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    ToolTip="选择数量" />
                                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                    <TextBlock
                                                        FontSize="16"
                                                        Foreground="{Binding RelativeSource={RelativeSource AncestorType=ListBox}, Path=Foreground, FallbackValue=Black}"
                                                        Text="{Binding}"
                                                        ToolTip="{Binding}" />
                                                </StackPanel>
                                            </Grid>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </StackPanel>
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <TextBlock
                                    Margin="0,0,0,8"
                                    Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                    Text="右侧" />
                                <ListBox
                                    Name="RightListBox"
                                    MaxHeight="200"
                                    behaviors:SelectedItemsBehavior.SelectedItems="{Binding RightSelectedItems, Mode=TwoWay}"
                                    ItemsSource="{Binding WaferCountList}"
                                    ScrollViewer.VerticalScrollBarVisibility="Auto"
                                    SelectionChanged="ListBox_SelectionChanged_Right"
                                    SelectionMode="Multiple"
                                    Style="{StaticResource MaterialDesignListBox}">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Margin="4">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <CheckBox
                                                    Margin="0,0,8,0"
                                                    VerticalAlignment="Center"
                                                    IsChecked="{Binding IsSelected, RelativeSource={RelativeSource AncestorType=ListBoxItem}, Mode=TwoWay}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    ToolTip="选择数量" />
                                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                    <TextBlock
                                                        FontSize="16"
                                                        Foreground="{Binding RelativeSource={RelativeSource AncestorType=ListBox}, Path=Foreground, FallbackValue=Black}"
                                                        Text="{Binding}"
                                                        ToolTip="{Binding}" />
                                                </StackPanel>
                                            </Grid>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </StackPanel>
                        </Grid>
                    </StackPanel>

                    <!--  按钮  -->
                    <StackPanel
                        Grid.Row="5"
                        HorizontalAlignment="Center"
                        Orientation="Horizontal">
                        <Button
                            Width="120"
                            Height="40"
                            Margin="0,0,16,0"
                            Command="{Binding StopCommand}"
                            Content="结束"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                        <Button
                            Width="120"
                            Height="40"
                            Command="{Binding RunRecipeCommand}"
                            Content="运行"
                            Style="{StaticResource MaterialDesignRaisedButton}" />
                    </StackPanel>
                </Grid>
            </DockPanel>
        </md:DrawerHost>
    </md:DialogHost>
</UserControl>