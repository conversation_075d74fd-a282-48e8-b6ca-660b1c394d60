请分析项目代码库，用mermaid图表展示项目结构，以及各种文件的依赖关系等


需求详细复述及实现计划
核心需求
多任务并行处理功能
利用现有的三个Task Handle区域（Task Handle 1、2、3）实现命令并行执行
不同Task Handle可以同时执行不同的命令，无需等待前一个命令完成
Task Handle优先级分配
默认按优先级从高到低：Task Handle 1 > Task Handle 2 > Task Handle 3
新命令优先分配给优先级最高的空闲Handle
如果高优先级Handle空闲，优先使用，不考虑低优先级Handle
UI上的Task Handle选择功能
添加Task Handle选择下拉框
选项包括：Task Handle 1、Task Handle 2、Task Handle 3、自动选择
默认为"自动选择"（按优先级分配）
用户可以手动指定使用特定的Task Handle
Task Handle状态冲突处理
当用户指定的Task Handle忙碌时，提示用户该Handle忙碌
提供两个选项：等待该Handle空闲，或选择其他Handle
Task Handle状态显示
在UI上实时显示各Task Handle的工作状态
显示内容：空闲/忙碌/执行中的命令信息/执行进度
错误处理机制
命令执行失败时不自动重试
出错时释放Handle资源
向用户提供明确的错误信息和原因
超时处理
使用现有的超时处理机制
通过命令参数传入超时时间
实现计划
Task Handle管理器设计
创建TaskHandleManager类管理三个Task Handle
实现Handle状态跟踪和分配逻辑
处理优先级分配和冲突解决
UI界面设计
添加Task Handle选择控件
设计Task Handle状态显示面板
实现命令执行状态和结果反馈
命令执行流程优化
修改现有命令执行逻辑，支持指定Task Handle
实现Handle状态检查和冲突处理
添加命令执行进度和结果反馈
错误处理和状态监控
完善错误处理机制
实现实时状态监控
添加用户友好的错误提示
测试和验证
测试不同场景下的命令分配
验证并行执行的正确性
确保UI反馈的准确性和实时性
这样的需求理解和实现计划是否符合您的预期？