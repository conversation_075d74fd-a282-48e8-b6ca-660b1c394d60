using System;

namespace <PERSON>ish<PERSON>.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// ChamberA子系统状态模型
    /// 继承自ChamberSubsystemStatus，确保ChamberA有独立的实例
    /// </summary>
    public class ChamberASubsystemStatus : ChamberSubsystemStatus
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ChamberASubsystemStatus() : base()
        {
            // ChamberA特有的初始化逻辑（如果需要）
        }

        /// <summary>
        /// 重写ToString方法，标识这是ChamberA的状态
        /// </summary>
        /// <returns>ChamberA状态的字符串表示</returns>
        public override string ToString()
        {
            var baseString = base.ToString();
            return $"=== ChamberA 子系统状态 ===\n{baseString}";
        }
    }
}
