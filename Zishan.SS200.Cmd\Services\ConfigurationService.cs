using System;
using log4net;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Services
{
    public class ConfigurationService
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ConfigurationService));
        private readonly string _configPath;

        public ConfigurationService(string configPath = "config.json")
        {
            _configPath = configPath;
            _logger.Info($"配置服务已初始化，配置文件路径: {configPath}");
        }

        public async Task<T> LoadConfigAsync<T>() where T : new()
        {
            try
            {
                _logger.Debug($"正在加载配置文件: {_configPath}");
                if (!File.Exists(_configPath))
                {
                    _logger.Warn("配置文件不存在，将创建默认配置");
                    return new T();
                }

                var json = await File.ReadAllTextAsync(_configPath);
                var config = JsonSerializer.Deserialize<T>(json);
                _logger.Info("配置文件加载成功");
                return config;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载配置文件失败: {ex.Message}", ex);
                throw;
            }
        }

        public async Task SaveConfigAsync<T>(T config)
        {
            try
            {
                _logger.Debug($"正在保存配置到文件: {_configPath}");
                var json = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_configPath, json);
                _logger.Info("配置文件保存成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"保存配置文件失败: {ex.Message}", ex);
                throw;
            }
        }
    }
}