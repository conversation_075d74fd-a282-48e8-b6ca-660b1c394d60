using System;
using System.Globalization;
using System.Windows.Data;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 循环次数显示转换器
    /// 将循环次数转换为友好的显示文本
    /// -1 显示为 "无限循环"
    /// 0 显示为 "已完成"
    /// 其他正数显示为 "剩余: X次"
    /// </summary>
    public class LoopCountDisplayConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int loopCount)
            {
                return loopCount switch
                {
                    -1 => "无限循环",
                    0 => "已完成",
                    _ => $"剩余: {loopCount}次"
                };
            }
            
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
