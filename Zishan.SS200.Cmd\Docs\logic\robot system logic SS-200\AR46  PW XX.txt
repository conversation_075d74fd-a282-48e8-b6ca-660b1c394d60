﻿AR46
PW XX
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			shuttle position status review
SSD1~SSD7
				SSD1
					shuttle 2 exist status review
SS45 or SS46
						SS45 
shuttle 2 enable
							cassette slot xx status review
LSS3/LSS4
								LSS3 xx=0 and LSS4 xx=0
or
LSS3 xx=2 and LSS4 xx=2
									paddle status review
RS53~RS54
										RS53=0 RS54=0
											compare ABS(RTF-RP4) with ABS(RTF-RP8)
												ABS(RTF-RP4)≤ABS(RTF-RP8)
													AR4
T-axis smooth to cassette
														AR36
Move Z-axis height smooth put slot xx
															AR14
R-axis smooth cassette extend
																AR34
Move Z-axis height smooth get slot xx
																	AR58
wafer status exchange smooth paddle to cassette slot xx
																		AR19
R-axis zero position
																			paddle sensor status review
																				RDI1=0 RDI2=0
																					command done
																				RDI1=1 RDI2=0
																					RA44 ALARM
																				RDI1=0 RDI2=1
																					RA45 ALARM
																				RDI1=1 RDI2=1
																					RA46 ALARM
												ABS(RTF-RP4)>ABS(RTF-RP8)
													AR8
T-axis nose to cassette
														AR37
Move Z-axis height nose put slot xx
															AR18
R-axis nose cassette extend
																AR35
Move Z-axis height nose get slot xx
																	AR69
wafer status exchange nose paddle to cassette slot xx
																		AR19
R-axis zero position
																			AR70
wafer status nose paddle confirm
																				RDI1=0 RDI2=0
																					command done
																				RDI1=1 RDI2=0
																					RA47 ALARM
																				RDI1=0 RDI2=1
																					RA48 ALARM
																				RDI1=1 RDI2=1
																					RA49 ALARM
										RS53=1
and
LSS5 or/and LSS6 (XX)=PW (XX)
											AR4
T-axis smooth to cassette
												AR36
Move Z-axis height smooth put slot xx
													AR14
R-axis smooth cassette extend
														AR34
Move Z-axis height smooth get slot xx
															AR58
wafer status exchange smooth paddle to cassette slot xx
																AR19
R-axis zero position
																	AR59
wafer status smooth paddle confirm
																		RDI1=0 RDI2=0
																			command done
																		RDI1=1 RDI2=0
																			RA44 ALARM
																		RDI1=0 RDI2=1
																			RA45 ALARM
																		RDI1=1 RDI2=1
																			RA46 ALARM
										RS54=1
and
LSS7 or/and LSS8 (XX)=PW (XX)
											AR8
T-axis nose to cassette
												AR37
Move Z-axis height nose put slot xx
													AR18
R-axis nose cassette extend
														AR35
Move Z-axis height nose get slot xx
															AR69
wafer status exchange nose paddle to cassette slot xx
																AR19
R-axis zero position
																	AR70
wafer status nose paddle confirm
																		RDI1=0 RDI2=0
																			command done
																		RDI1=1 RDI2=0
																			RA47 ALARM
																		RDI1=0 RDI2=1
																			RA48 ALARM
																		RDI1=1 RDI2=1
																			RA49 ALARM
										RS53=1 RS54=1
LSS5 or/and LSS6 (XX)≠PW (XX)
LSS7 or/and LSS8 (XX)≠PW (XX)
											RA17 ALARM
								others status
									RA36 ALARM
						SS46 
shuttle 2 disable
							RA24 ALARM
				SSD2
					shuttle 1 exist status review
SS43 or SS44
						SS43 
shuttle 1 enable
							cassette slot xx status review
LSS1/LSS2
								LSS1 xx=0 and LSS2 xx=0
or
LSS1 xx=2 and LSS2 xx=2
									paddle status review
RS53~RS54
										RS53=0 RS54=0
											compare ABS(RTF-RP4) with ABS(RTF-RP8)
												ABS(RTF-RP4)≤ABS(RTF-RP8)
													AR4
T-axis smooth to cassette
														AR36
Move Z-axis height smooth put slot xx
															AR14
R-axis smooth cassette extend
																AR34
Move Z-axis height smooth get slot xx
																	AR58
wafer status exchange smooth paddle to cassette slot xx
																		AR19
R-axis zero position
																			paddle sensor status review
																				RDI1=0 RDI2=0
																					command done
																				RDI1=1 RDI2=0
																					RA44 ALARM
																				RDI1=0 RDI2=1
																					RA45 ALARM
																				RDI1=1 RDI2=1
																					RA46 ALARM
												ABS(RTF-RP4)>ABS(RTF-RP8)
													AR8
T-axis nose to cassette
														AR37
Move Z-axis height nose put slot xx
															AR18
R-axis nose cassette extend
																AR35
Move Z-axis height nose get slot xx
																	AR69
wafer status exchange nose paddle to cassette slot xx
																		AR19
R-axis zero position
																			AR70
wafer status nose paddle confirm
																				RDI1=0 RDI2=0
																					command done
																				RDI1=1 RDI2=0
																					RA47 ALARM
																				RDI1=0 RDI2=1
																					RA48 ALARM
																				RDI1=1 RDI2=1
																					RA49 ALARM
										RS53=1
and
LSS5 or/and LSS6 (XX)=PW (XX)
											AR4
T-axis smooth to cassette
												AR36
Move Z-axis height smooth put slot xx
													AR14
R-axis smooth cassette extend
														AR34
Move Z-axis height smooth get slot xx
															AR58
wafer status exchange smooth paddle to cassette slot xx
																AR19
R-axis zero position
																	AR59
wafer status smooth paddle confirm
																		RDI1=0 RDI2=0
																			command done
																		RDI1=1 RDI2=0
																			RA44 ALARM
																		RDI1=0 RDI2=1
																			RA45 ALARM
																		RDI1=1 RDI2=1
																			RA46 ALARM
										RS54=1
and
LSS7 or/and LSS8 (XX)=PW (XX)
											AR8
T-axis nose to cassette
												AR37
Move Z-axis height nose put slot xx
													AR18
R-axis nose cassette extend
														AR35
Move Z-axis height nose get slot xx
															AR69
wafer status exchange nose paddle to cassette slot xx
																AR19
R-axis zero position
																	AR70
wafer status nose paddle confirm
																		RDI1=0 RDI2=0
																			command done
																		RDI1=1 RDI2=0
																			RA47 ALARM
																		RDI1=0 RDI2=1
																			RA48 ALARM
																		RDI1=1 RDI2=1
																			RA49 ALARM
										RS53=1 RS54=1
LSS5 or/and LSS6 (XX)≠PW (XX)
LSS7 or/and LSS8 (XX)≠PW (XX)
											RA17 ALARM
								others status
									RA36 ALARM
						SS44 
shuttle 1 disable
							RA23 ALARM
				others status
					RA25 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM