using Zishan.SS200.Cmd.Enums;
using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Zishan.SS200.Cmd.Converters
{
    public class WorkStatusStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is EnuWorkStatus workStatus)
            {
                switch (workStatus)
                {
                    case EnuWorkStatus.Idle:
                        return "空闲状态";

                    case EnuWorkStatus.Alarm:
                        return "报警状态";

                    case EnuWorkStatus.Run:
                        return "运行状态";

                    case EnuWorkStatus.Busy:
                        return "繁忙状态";

                    case EnuWorkStatus.Process:
                        return "处理状态";

                    case EnuWorkStatus.Mapping:
                        return "扫片状态";

                    default:
                        return Binding.DoNothing;
                }
            }
            return Binding.DoNothing;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}