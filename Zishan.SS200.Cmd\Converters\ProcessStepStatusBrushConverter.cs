using Zishan.SS200.Cmd.Enums;
using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Zishan.SS200.Cmd.Converters
{
    public class ProcessStepStatusBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int stepStatus)
            {
                if (stepStatus == 0)
                {
                    return Brushes.Black;
                }
                else
                {
                    return Brushes.White;
                }
            }
            return Binding.DoNothing;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}