﻿using System.ComponentModel;

using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums;

/// <summary>
/// MCU设备类型：Shuttle、Robot、ChamberA、ChamberB
/// </summary>
[TypeConverter(typeof(EnuMcuDeviceType))]
public enum EnuMcuDeviceType
{
    /// <summary>
    /// Shuttle
    /// </summary>
    [Description("Shuttle")]
    Shuttle,

    /// <summary>
    /// Robot
    /// </summary>
    [Description("Robot")]
    Robot,

    /// <summary>
    /// 腔体A
    /// </summary>
    [Description("ChamberA")]
    ChamberA,

    /// <summary>
    /// 腔体B
    /// </summary>
    [Description("ChamberB")]
    ChamberB,
}