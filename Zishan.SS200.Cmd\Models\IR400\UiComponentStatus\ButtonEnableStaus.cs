﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Models.IR400.UiComponentStatus
{
    /// <summary>
    /// 按钮状态
    /// </summary>
    public class ButtonEnableStaus : BindableBase
    {
        /// <summary>
        /// definity
        /// </summary>
        public bool Add { get => _Add; set => SetProperty(ref _Add, value); }

        private bool _Add;

        /// <summary>
        /// definity
        /// </summary>
        public bool Rename { get => _Rename; set => SetProperty(ref _Rename, value); }

        private bool _Rename;

        /// <summary>
        /// definity
        /// </summary>
        public bool Save { get => _Save; set => SetProperty(ref _Save, value); }

        private bool _Save;

        /// <summary>
        /// definity
        /// </summary>
        public bool Delete { get => _Delete; set => SetProperty(ref _Delete, value); }

        private bool _Delete;

        /// <summary>
        /// definity
        /// </summary>
        public bool Clear { get => _Clear; set => SetProperty(ref _Clear, value); }

        private bool _Clear;

        #region DataGrid 配方2D数据添加、删除功能

        /// <summary>
        /// definity
        /// </summary>
        public bool BeforeAdd { get => _BeforeAdd; set => SetProperty(ref _BeforeAdd, value); }

        private bool _BeforeAdd;

        /// <summary>
        /// definity
        /// </summary>
        public bool AfterAdd { get => _AfterAdd; set => SetProperty(ref _AfterAdd, value); }

        private bool _AfterAdd;

        /// <summary>
        /// definity
        /// </summary>
        public bool DeleteCurItem { get => _DeleteCurItem; set => SetProperty(ref _DeleteCurItem, value); }

        private bool _DeleteCurItem;

        #endregion DataGrid 配方2D数据添加、删除功能

        public void LoadDefault()
        {
            Add = false;
            Rename = false;
            Save = false;
            Delete = false;
            Clear = false;

            BeforeAdd = false;
            AfterAdd = false;
            DeleteCurItem = false;
        }
    }
}