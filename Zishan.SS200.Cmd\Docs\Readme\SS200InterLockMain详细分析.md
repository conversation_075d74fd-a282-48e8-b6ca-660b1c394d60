# SS200InterLockMain.cs 详细分析文档

## 📋 概述

`SS200InterLockMain.cs` 是SS200系统的核心互锁管理类，采用单例模式设计，为整个系统提供统一的设备IO、报警信息、配置信息和状态信息访问入口。该类是系统架构中的关键组件，负责协调各个子系统之间的交互。

## 🏗️ 1. 类的整体架构和设计模式

### 1.1 设计模式分析

#### 单例模式 (Singleton Pattern)
```csharp
private static readonly Lazy<SS200InterLockMain> _instance =
    new Lazy<SS200InterLockMain>(() => new SS200InterLockMain(), true);

public static SS200InterLockMain Instance => _instance.Value;
```

**设计亮点：**
- 使用 `Lazy<T>` 实现线程安全的延迟初始化
- 确保整个应用程序生命周期内只有一个实例
- 避免了传统单例模式的双重检查锁定复杂性

#### 外观模式 (Facade Pattern)
该类为复杂的子系统提供了简化的统一接口：
- `IOInterface` - IO接口访问外观
- `AlarmCode` - 报警代码访问外观  
- `SubsystemConfigure` - 子系统配置访问外观
- `SubsystemStatus` - 子系统状态访问外观

#### 访问器模式 (Accessor Pattern)
通过多层访问器类提供结构化的数据访问：
```csharp
// 示例：三层访问结构
SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value
//     主入口        ->  设备类型 -> 具体IO点 -> 属性值
```

### 1.2 类的职责和作用

**核心职责：**
1. **统一访问入口** - 为所有子系统提供一致的访问接口
2. **状态协调** - 协调Robot、Chamber、Shuttle三大子系统的状态
3. **配置管理** - 管理系统配置参数（如机器人位置参数）
4. **依赖注入集成** - 与IoC容器深度集成，管理对象生命周期

**在系统中的作用：**
- 作为业务逻辑层和数据访问层之间的桥梁
- 提供类型安全的强类型访问接口
- 封装复杂的IO状态计算逻辑
- 统一管理系统配置和状态信息

### 1.3 生命周期管理

```csharp
private SS200InterLockMain()
{
    // 从IOC容器获取服务实例
    _mcuCmdService = App.GetInstance<IS200McuCmdService>();
    _coilHelper = new CoilStatusHelper(_mcuCmdService);
    
    // 从IOC容器获取状态实例
    _currentRobotStatus = App.GetInstance<RobotSubsystemStatus>();
    _currentChamberAStatus = App.GetInstance<ChamberSubsystemStatus>();
    _currentChamberBStatus = App.GetInstance<ChamberSubsystemStatus>();
    _currentShuttleStatus = App.GetInstance<ShuttleSubsystemStatus>();
}
```

**生命周期特点：**
- **应用程序级单例** - 与应用程序同生命周期
- **延迟初始化** - 首次访问时才创建实例
- **依赖注入集成** - 通过IoC容器管理依赖关系
- **自动资源管理** - 依赖IoC容器的生命周期管理

## 🔧 2. 核心功能模块

### 2.1 属性访问器基类

#### IOPropertyAccessor<TEnum> - IO属性访问器
```csharp
public class IOPropertyAccessor<TEnum> where TEnum : Enum
{
    public bool Value => _coilHelper.GetCoilValue(_deviceType, _enumValue);
    public string Content => _enumValue.GetDescription();
    public bool? ValueNullable => _coilHelper.GetCoilValueNullable(_deviceType, _enumValue);
    public bool IsActive => _coilHelper.IsCoilActive(_deviceType, _enumValue);
}
```

**功能说明：**
- `Value` - 实时获取IO点的布尔值状态
- `Content` - 直接从枚举的DescriptionAttribute获取描述信息
- `ValueNullable` - 获取可空的IO状态（用于检测通信异常）
- `IsActive` - 检查IO点是否激活

#### AlarmPropertyAccessor - 报警属性访问器
```csharp
public class AlarmPropertyAccessor
{
    public string Content => _alarmItem.Content;        // 英文描述
    public string ChsContent => _alarmItem.ChineseDescription; // 中文描述
    public string Cause => _alarmItem.ChineseDescription;      // 报警原因
    public string Code => _alarmItem.Code;              // 报警代码
    public int Item => _alarmItem.Item;                 // 报警项ID
}
```

#### StatusPropertyAccessor<TStatus> - 状态属性访问器
```csharp
public class StatusPropertyAccessor<TStatus>
{
    public TStatus Value => _valueGetter();    // 动态获取状态值
    public string Content => _description;     // 状态描述
}
```

### 2.2 设备IO访问器

#### RobotIOAccessor - 机器人IO访问器
```csharp
public class RobotIOAccessor
{
    // RDI1 - Paddle传感器1左侧
    public IOPropertyAccessor<EnuRobotDICodes> RDI1_PaddleSensor1Left =>
        GetOrCreateAccessor(EnuRobotDICodes.RDI1_PaddleSensor1Left, "Paddle传感器1左侧");
}
```

**缓存机制：**
- 使用 `ConcurrentDictionary<string, object>` 实现线程安全缓存
- 避免重复创建访问器对象，提高性能
- 支持高并发访问场景

### 2.3 统一访问入口

```csharp
public class SS200InterLockMain
{
    public IOInterfaceAccessor IOInterface { get; }           // IO接口访问
    public AlarmCodeAccessor AlarmCode { get; }               // 报警代码访问  
    public SubsystemConfigureAccessor SubsystemConfigure { get; } // 配置访问
    public SubsystemStatusAccessor SubsystemStatus { get; }   // 状态访问
}
```

## 💻 3. 技术实现细节

### 3.1 技术栈和框架

**主要技术栈：**
- **.NET Framework/Core** - 基础运行时
- **CommunityToolkit.Mvvm** - MVVM框架，提供ObservableObject
- **Prism + DryIoc** - 依赖注入和模块化框架
- **log4net** - 日志记录框架
- **System.Collections.Concurrent** - 线程安全集合

### 3.2 依赖注入（IoC）使用方式

#### 服务注册（App.xaml.cs）
```csharp
// 注册为单例
containerRegistry.RegisterSingleton<RobotSubsystemStatus>();
containerRegistry.RegisterSingleton<ChamberSubsystemStatus>();
containerRegistry.RegisterSingleton<ShuttleSubsystemStatus>();
containerRegistry.RegisterSingleton<SS200InterLockMain>();
```

#### 服务获取
```csharp
// 在构造函数中获取依赖
_mcuCmdService = App.GetInstance<IS200McuCmdService>();
_currentRobotStatus = App.GetInstance<RobotSubsystemStatus>();
```

**IoC集成优势：**
- **松耦合** - 通过接口依赖而非具体实现
- **可测试性** - 便于单元测试时注入Mock对象
- **生命周期管理** - 由容器统一管理对象生命周期
- **配置集中化** - 所有依赖关系在启动时配置

### 3.3 线程安全和并发处理

#### 线程安全措施
```csharp
// 1. 使用Lazy<T>确保单例线程安全
private static readonly Lazy<SS200InterLockMain> _instance = 
    new Lazy<SS200InterLockMain>(() => new SS200InterLockMain(), true);

// 2. 使用ConcurrentDictionary实现线程安全缓存
private readonly ConcurrentDictionary<string, object> _cache = new();

// 3. 同步锁对象
private readonly object _logListLock = new object();
```

#### 并发访问支持
- **读操作无锁** - 大部分读操作通过不可变对象实现无锁访问
- **写操作保护** - 关键写操作使用锁或原子操作保护
- **缓存优化** - 通过缓存减少重复计算和对象创建

## 🎯 4. 实际应用场景

### 4.1 IO状态查询示例

```csharp
// 查询Robot的Paddle传感器状态
bool paddleStatus = SS200InterLockMain.Instance
    .IOInterface.Robot.RDI1_PaddleSensor1Left.Value;

string paddleDescription = SS200InterLockMain.Instance
    .IOInterface.Robot.RDI1_PaddleSensor1Left.Content;

// 查询Chamber的Slit Door传感器状态  
bool slitDoorOpen = SS200InterLockMain.Instance
    .IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
```

### 4.2 报警信息查询示例

```csharp
// 获取Robot报警信息
var robotAlarm = SS200InterLockMain.Instance.AlarmCode.Robot.RA1_SystemBusyReject;
string alarmContent = robotAlarm.Content;      // 英文描述
string alarmChsContent = robotAlarm.ChsContent; // 中文描述
string alarmCode = robotAlarm.Code;            // 报警代码
```

### 4.3 配置参数查询示例

```csharp
// 获取机器人位置配置
int tAxisPosition = SS200InterLockMain.Instance
    .SubsystemConfigure.PositionValue.RP1_TAxisPosition.Value;

string positionDescription = SS200InterLockMain.Instance
    .SubsystemConfigure.PositionValue.RP1_TAxisPosition.Content;
```

### 4.4 状态信息查询示例

```csharp
// 获取Robot状态
var robotStatus = SS200InterLockMain.Instance
    .SubsystemStatus.Robot.RS1_RobotStatus.Value;

// 直接访问完整状态对象
var fullRobotStatus = SS200InterLockMain.Instance
    .SubsystemStatus.Robot.Status;
```

### 4.5 在穿梭车子系统中的应用

```csharp
// 检查晶圆盒存在状态
bool cassette1Present = SS200InterLockMain.Instance
    .IOInterface.Shuttle.SDI6_PresentSensorCassette1.Value;

bool cassette2Present = SS200InterLockMain.Instance
    .IOInterface.Shuttle.SDI7_PresentSensorCassette2.Value;

// 获取Shuttle完整状态
var shuttleStatus = SS200InterLockMain.Instance
    .SubsystemStatus.Shuttle.Status;
```

## ✨ 5. 最佳实践和注意事项

### 5.1 设计亮点

1. **类型安全** - 通过强类型枚举避免魔法字符串
2. **性能优化** - 多级缓存机制减少重复计算
3. **扩展性好** - 访问器模式便于添加新的设备类型
4. **错误处理** - 完善的异常处理和日志记录
5. **文档完善** - 详细的XML注释和使用示例

### 5.2 使用注意事项

#### ⚠️ 重要提醒

1. **IoC容器依赖**
   ```csharp
   // ✅ 正确：通过IoC容器获取
   var robotStatus = App.GetInstance<RobotSubsystemStatus>();
   
   // ❌ 错误：直接new实例
   var robotStatus = new RobotSubsystemStatus();
   ```

2. **线程安全考虑**
   ```csharp
   // ✅ 线程安全：读取操作
   bool value = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
   
   // ⚠️ 注意：状态更新需要在UI线程进行
   Application.Current.Dispatcher.Invoke(() => {
       // 更新UI绑定的状态
   });
   ```

3. **性能考虑**
   ```csharp
   // ✅ 推荐：缓存频繁访问的对象
   var robotIO = SS200InterLockMain.Instance.IOInterface.Robot;
   bool sensor1 = robotIO.RDI1_PaddleSensor1Left.Value;
   bool sensor2 = robotIO.RDI2_PaddleSensor2Right.Value;
   
   // ❌ 避免：重复访问长链路
   bool sensor1 = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
   bool sensor2 = SS200InterLockMain.Instance.IOInterface.Robot.RDI2_PaddleSensor2Right.Value;
   ```

### 5.3 可能的改进方向

1. **异步支持** - 为IO操作添加异步版本
2. **事件通知** - 添加状态变化事件通知机制
3. **配置热更新** - 支持运行时配置参数更新
4. **监控集成** - 集成性能监控和健康检查
5. **批量操作** - 支持批量IO状态查询优化

## 🚀 6. 高级应用示例

### 6.1 复杂业务逻辑示例

#### 晶圆传输安全检查
```csharp
public class WaferTransferSafetyChecker
{
    private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;

    public bool CanTransferWafer(string fromLocation, string toLocation)
    {
        // 1. 检查Robot状态
        var robotStatus = _interlock.SubsystemStatus.Robot.RS1_RobotStatus.Value;
        if (robotStatus != EnuRobotStatus.Idle)
        {
            LogWarning($"Robot状态不是空闲: {robotStatus}");
            return false;
        }

        // 2. 检查Paddle传感器状态
        bool leftPaddle = _interlock.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
        bool rightPaddle = _interlock.IOInterface.Robot.RDI2_PaddleSensor2Right.Value;

        if (leftPaddle || rightPaddle)
        {
            LogWarning("Paddle上已有晶圆，无法进行传输");
            return false;
        }

        // 3. 检查目标Chamber状态
        if (toLocation.StartsWith("CHA"))
        {
            var chamberStatus = _interlock.SubsystemStatus.ChamberA.Status;
            if (chamberStatus.SlitDoorStatus != EnuSlitDoorStatus.Open)
            {
                LogWarning("Chamber A Slit Door未打开");
                return false;
            }

            if (chamberStatus.LiftPinStatus != EnuLiftPinStatus.Down)
            {
                LogWarning("Chamber A Lift Pin未下降");
                return false;
            }
        }

        return true;
    }
}
```

#### 系统状态监控器
```csharp
public class SystemStatusMonitor
{
    private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;
    private readonly Timer _monitorTimer;

    public SystemStatusMonitor()
    {
        _monitorTimer = new Timer(MonitorSystemStatus, null,
            TimeSpan.Zero, TimeSpan.FromSeconds(1));
    }

    private void MonitorSystemStatus(object state)
    {
        try
        {
            // 监控关键IO状态
            MonitorCriticalIOStatus();

            // 监控子系统状态
            MonitorSubsystemStatus();

            // 检查报警状态
            CheckAlarmStatus();
        }
        catch (Exception ex)
        {
            LogError($"系统状态监控异常: {ex.Message}");
        }
    }

    private void MonitorCriticalIOStatus()
    {
        // 监控关键传感器状态
        var criticalSensors = new[]
        {
            ("Robot Paddle Left", _interlock.IOInterface.Robot.RDI1_PaddleSensor1Left),
            ("Robot Paddle Right", _interlock.IOInterface.Robot.RDI2_PaddleSensor2Right),
            ("ChamberA Slit Door Open", _interlock.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor),
            ("ChamberA Slit Door Close", _interlock.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor),
            ("Shuttle Cassette1", _interlock.IOInterface.Shuttle.SDI6_PresentSensorCassette1),
            ("Shuttle Cassette2", _interlock.IOInterface.Shuttle.SDI7_PresentSensorCassette2)
        };

        foreach (var (name, sensor) in criticalSensors)
        {
            var value = sensor.ValueNullable;
            if (!value.HasValue)
            {
                LogWarning($"传感器 {name} 通信异常");
            }
        }
    }
}
```

### 6.2 配置管理示例

#### 动态配置更新
```csharp
public class RobotPositionConfigManager
{
    private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;

    public void UpdateRobotPosition(string positionCode, int newValue)
    {
        try
        {
            // 1. 验证配置代码
            if (!IsValidPositionCode(positionCode))
            {
                throw new ArgumentException($"无效的位置代码: {positionCode}");
            }

            // 2. 获取当前配置
            var currentConfig = GetPositionConfig(positionCode);
            if (currentConfig == null)
            {
                throw new InvalidOperationException($"未找到位置配置: {positionCode}");
            }

            // 3. 验证新值范围
            if (!IsValueInRange(positionCode, newValue))
            {
                throw new ArgumentOutOfRangeException(nameof(newValue),
                    $"位置值 {newValue} 超出允许范围");
            }

            // 4. 更新配置
            currentConfig.Value = newValue;

            // 5. 保存到文件
            SaveConfigurationToFile();

            LogInfo($"成功更新位置配置 {positionCode} = {newValue}");
        }
        catch (Exception ex)
        {
            LogError($"更新位置配置失败: {ex.Message}");
            throw;
        }
    }

    private ConfigPropertyAccessor GetPositionConfig(string code)
    {
        return code switch
        {
            "RP1" => _interlock.SubsystemConfigure.PositionValue.RP1_TAxisPosition,
            "RP2" => _interlock.SubsystemConfigure.PositionValue.RP2_RAxisPosition,
            "RP27" => _interlock.SubsystemConfigure.PositionValue.RP27_ZForRobotRotation,
            _ => null
        };
    }
}
```

### 6.3 故障诊断和调试

#### 系统诊断工具
```csharp
public class SystemDiagnosticTool
{
    private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;

    public SystemDiagnosticReport GenerateDiagnosticReport()
    {
        var report = new SystemDiagnosticReport
        {
            Timestamp = DateTime.Now,
            RobotDiagnostic = DiagnoseRobotSubsystem(),
            ChamberADiagnostic = DiagnoseChamberSubsystem(EnuMcuDeviceType.ChamberA),
            ChamberBDiagnostic = DiagnoseChamberSubsystem(EnuMcuDeviceType.ChamberB),
            ShuttleDiagnostic = DiagnoseShuttleSubsystem()
        };

        return report;
    }

    private RobotDiagnosticInfo DiagnoseRobotSubsystem()
    {
        var robotStatus = _interlock.SubsystemStatus.Robot.Status;
        var robotIO = _interlock.IOInterface.Robot;

        return new RobotDiagnosticInfo
        {
            Status = robotStatus.EnuRobotStatus,
            TAxisDestination = robotStatus.EnuTAxisSmoothDestination,
            RAxisDestination = robotStatus.EnuTAndRAxisSmoothExtendDestination,
            PaddleLeftSensor = robotIO.RDI1_PaddleSensor1Left.Value,
            PaddleRightSensor = robotIO.RDI2_PaddleSensor2Right.Value,
            PinSearch1 = robotIO.RDI3_PinSearch1.Value,
            PinSearch2 = robotIO.RDI4_PinSearch2.Value,
            IsHealthy = EvaluateRobotHealth(robotStatus, robotIO)
        };
    }

    private bool EvaluateRobotHealth(RobotSubsystemStatus status, RobotIOAccessor io)
    {
        // 健康检查逻辑
        if (status.EnuRobotStatus == EnuRobotStatus.Alarm)
            return false;

        // 检查传感器通信状态
        if (!io.RDI1_PaddleSensor1Left.ValueNullable.HasValue ||
            !io.RDI2_PaddleSensor2Right.ValueNullable.HasValue)
            return false;

        return true;
    }
}
```

## 🔧 7. 性能优化和最佳实践

### 7.1 性能优化技巧

#### 批量状态查询优化
```csharp
public class BatchStatusQuery
{
    private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;

    public Dictionary<string, bool> GetAllRobotSensorStatus()
    {
        // 一次性获取所有Robot IO访问器，避免重复访问
        var robotIO = _interlock.IOInterface.Robot;

        return new Dictionary<string, bool>
        {
            ["PaddleLeft"] = robotIO.RDI1_PaddleSensor1Left.Value,
            ["PaddleRight"] = robotIO.RDI2_PaddleSensor2Right.Value,
            ["PinSearch1"] = robotIO.RDI3_PinSearch1.Value,
            ["PinSearch2"] = robotIO.RDI4_PinSearch2.Value
        };
    }
}
```

#### 缓存策略优化
```csharp
public class OptimizedStatusReader
{
    private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;
    private readonly ConcurrentDictionary<string, (DateTime timestamp, object value)> _cache = new();
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMilliseconds(100);

    public T GetCachedValue<T>(string key, Func<T> valueGetter)
    {
        var now = DateTime.Now;

        if (_cache.TryGetValue(key, out var cached) &&
            now - cached.timestamp < _cacheExpiry)
        {
            return (T)cached.value;
        }

        var newValue = valueGetter();
        _cache[key] = (now, newValue);
        return newValue;
    }

    public bool GetRobotPaddleStatus()
    {
        return GetCachedValue("RobotPaddleLeft",
            () => _interlock.IOInterface.Robot.RDI1_PaddleSensor1Left.Value);
    }
}
```

### 7.2 错误处理最佳实践

#### 健壮的错误处理
```csharp
public class SafeStatusReader
{
    private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;
    private readonly ILog _logger = LogManager.GetLogger(typeof(SafeStatusReader));

    public bool TryGetSensorStatus(string deviceType, string sensorName, out bool status)
    {
        status = false;

        try
        {
            status = deviceType.ToUpper() switch
            {
                "ROBOT" when sensorName == "PaddleLeft" =>
                    _interlock.IOInterface.Robot.RDI1_PaddleSensor1Left.Value,
                "ROBOT" when sensorName == "PaddleRight" =>
                    _interlock.IOInterface.Robot.RDI2_PaddleSensor2Right.Value,
                "CHAMBERA" when sensorName == "SlitDoorOpen" =>
                    _interlock.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value,
                "SHUTTLE" when sensorName == "Cassette1" =>
                    _interlock.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Value,
                _ => throw new ArgumentException($"未知的设备或传感器: {deviceType}.{sensorName}")
            };

            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"读取传感器状态失败 {deviceType}.{sensorName}: {ex.Message}");
            return false;
        }
    }
}
```

## 🔗 相关文档

- [CoilStatusHelper使用指南](./CoilStatusHelper使用指南.md)
- [子系统状态管理](./子系统状态管理.md)
- [IoC容器配置](./IoC容器配置.md)
- [SS200系统架构](./SS200系统架构.md)
- [性能优化指南](./性能优化指南.md)
- [故障诊断手册](./故障诊断手册.md)

---

*本文档详细分析了SS200InterLockMain.cs的设计和实现，为开发人员提供了全面的技术参考和实际应用指导。*
