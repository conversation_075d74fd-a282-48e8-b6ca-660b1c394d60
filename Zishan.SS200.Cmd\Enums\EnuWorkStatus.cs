﻿using System.ComponentModel;

using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// 工作状态
    /// </summary>
    [TypeConverter(typeof(EnuWorkStatus))]
    public enum EnuWorkStatus
    {
        /// <summary>
        /// 空闲状态【公共】
        /// </summary>
        [Description("空闲状态")]
        Idle = 0,

        /// <summary>
        /// 报警状态【公共】
        /// </summary>
        [Description("报警状态")]
        Alarm = 1,

        /// <summary>
        /// 运行状态【整机】
        /// </summary>
        [Description("运行状态")]
        Run = 2,

        /// <summary>
        //  繁忙状态【腔体、Cooling、Loadlock、Buffer】
        /// </summary>
        [Description("繁忙状态")]
        Busy = 3,

        /// <summary>
        //  处理状态【腔体、Cooling】
        /// </summary>
        [Description("处理状态")]
        Process = 4,

        /// <summary>
        //  扫片状态【Loadlock】
        /// </summary>
        [Description("扫片状态")]
        Mapping = 5
    }
}