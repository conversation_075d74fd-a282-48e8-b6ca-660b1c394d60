using System;
using <PERSON><PERSON>an.SS200.Cmd.Enums.Basic;

namespace Zishan.SS200.Cmd.Extensions
{
    /// <summary>
    /// 冷却腔位置转换扩展方法
    /// 解决T轴/R轴只需要CoolingChamber，而Z轴需要CoolingTop/CoolingBottom的问题
    /// </summary>
    public static class CoolingChamberLocationExtensions
    {
        #region 位置判断方法

        /// <summary>
        /// 判断是否为冷却腔相关位置（包括区域和精确位置）
        /// </summary>
        /// <param name="location">位置类型</param>
        /// <returns>是否为冷却腔相关位置</returns>
        public static bool IsCoolingLocation(this EnuLocationStationType location)
        {
            return location == EnuLocationStationType.CoolingChamber ||
                   location == EnuLocationStationType.CoolingTop ||
                   location == EnuLocationStationType.CoolingBottom;
        }

        /// <summary>
        /// 判断是否为冷却腔精确位置（上层或下层）
        /// </summary>
        /// <param name="location">位置类型</param>
        /// <returns>是否为冷却腔精确位置</returns>
        public static bool IsCoolingPreciseLocation(this EnuLocationStationType location)
        {
            return location == EnuLocationStationType.CoolingTop ||
                   location == EnuLocationStationType.CoolingBottom;
        }

        /// <summary>
        /// 判断是否为工艺腔室位置
        /// </summary>
        /// <param name="location">位置类型</param>
        /// <returns>是否为工艺腔室位置</returns>
        public static bool IsProcessChamberLocation(this EnuLocationStationType location)
        {
            return location == EnuLocationStationType.ChamberA ||
                   location == EnuLocationStationType.ChamberB;
        }

        #endregion 位置判断方法

        #region 转换方法

        /// <summary>
        /// 将冷却腔精确位置转换为区域位置
        /// 用于T轴、R轴等只需要区域信息的场景
        /// </summary>
        /// <param name="location">精确位置</param>
        /// <returns>对应的区域位置</returns>
        public static EnuLocationStationType ToCoolingChamberArea(this EnuLocationStationType location)
        {
            return location switch
            {
                EnuLocationStationType.CoolingTop => EnuLocationStationType.CoolingChamber,
                EnuLocationStationType.CoolingBottom => EnuLocationStationType.CoolingChamber,
                EnuLocationStationType.CoolingChamber => EnuLocationStationType.CoolingChamber,
                _ => location // 非冷却腔位置保持不变
            };
        }

        /// <summary>
        /// 将冷却腔区域位置转换为默认的精确位置
        /// 当需要从区域位置转换为精确位置时使用（默认选择上层）
        /// </summary>
        /// <param name="location">区域位置</param>
        /// <param name="defaultPreciseEnuLocationStationType"> 默认选择上层：EnuLocationStationType.CoolingTop</param>
        /// <returns>默认的精确位置</returns>
        public static EnuLocationStationType ToDefaultCoolingPreciseLocation(this EnuLocationStationType location, EnuLocationStationType defaultPreciseEnuLocationStationType = EnuLocationStationType.CoolingTop)
        {
            return location switch
            {
                EnuLocationStationType.CoolingChamber => defaultPreciseEnuLocationStationType, // 默认选择上层
                _ => location // 非冷却腔区域保持不变
            };
        }

        /// <summary>
        /// 获取冷却腔区域内的所有精确位置
        /// </summary>
        /// <param name="location">位置类型</param>
        /// <returns>如果是冷却腔区域，返回所有精确位置；否则返回原位置</returns>
        public static EnuLocationStationType[] GetCoolingPreciseLocations(this EnuLocationStationType location)
        {
            return location switch
            {
                EnuLocationStationType.CoolingChamber => new[]
                {
                    EnuLocationStationType.CoolingTop,
                    EnuLocationStationType.CoolingBottom
                },
                _ => new[] { location }
            };
        }

        #endregion 转换方法

        #region 比较方法

        /// <summary>
        /// 判断两个位置是否在同一个冷却腔区域内
        /// </summary>
        /// <param name="location1">第一个位置</param>
        /// <param name="location2">第二个位置</param>
        /// <returns>是否在同一个冷却腔区域</returns>
        public static bool InSameCoolingArea(this EnuLocationStationType location1, EnuLocationStationType location2)
        {
            // 如果都是冷却腔相关位置，则认为在同一区域
            return location1.IsCoolingLocation() && location2.IsCoolingLocation();
        }

        /// <summary>
        /// 判断精确位置是否属于某个区域位置
        /// </summary>
        /// <param name="preciseLocation">精确位置</param>
        /// <param name="areaLocation">区域位置</param>
        /// <returns>是否属于该区域</returns>
        public static bool BelongsToCoolingArea(this EnuLocationStationType preciseLocation, EnuLocationStationType areaLocation)
        {
            if (areaLocation != EnuLocationStationType.CoolingChamber)
                return preciseLocation == areaLocation;

            return preciseLocation.IsCoolingLocation();
        }

        #endregion 比较方法

        #region 使用场景辅助方法

        /// <summary>
        /// 获取T轴使用的位置（区域级别）
        /// T轴只关心旋转方向，不区分冷却腔的上下层
        /// </summary>
        /// <param name="targetLocation">目标位置</param>
        /// <returns>T轴使用的位置</returns>
        public static EnuLocationStationType GetTAxisLocation(this EnuLocationStationType targetLocation)
        {
            return targetLocation.ToCoolingChamberArea();
        }

        /// <summary>
        /// 获取R轴使用的位置（区域级别）
        /// R轴只关心伸缩距离，不区分冷却腔的上下层
        /// </summary>
        /// <param name="targetLocation">目标位置</param>
        /// <returns>R轴使用的位置</returns>
        public static EnuLocationStationType GetRAxisLocation(this EnuLocationStationType targetLocation)
        {
            return targetLocation.ToCoolingChamberArea();
        }

        /// <summary>
        /// 获取Z轴使用的位置（精确级别）
        /// Z轴必须知道精确的高度，需要区分冷却腔的上下层
        /// </summary>
        /// <param name="targetLocation">目标位置</param>
        /// <returns>Z轴使用的位置</returns>
        public static EnuLocationStationType GetZAxisLocation(this EnuLocationStationType targetLocation)
        {
            // Z轴需要精确位置，如果是区域位置则转换为默认精确位置
            return targetLocation == EnuLocationStationType.CoolingChamber
                ? targetLocation.ToDefaultCoolingPreciseLocation()
                : targetLocation;
        }

        #endregion 使用场景辅助方法

        #region 描述方法

        /// <summary>
        /// 获取位置的详细描述
        /// </summary>
        /// <param name="location">位置类型</param>
        /// <returns>位置描述</returns>
        public static string GetLocationDescription(this EnuLocationStationType location)
        {
            return location switch
            {
                EnuLocationStationType.None => "未定义位置",
                EnuLocationStationType.Cassette => "晶圆盒",
                EnuLocationStationType.ChamberA => "工艺腔室A",
                EnuLocationStationType.ChamberB => "工艺腔室B",
                EnuLocationStationType.CoolingChamber => "冷却腔区域（用于T轴、R轴）",
                EnuLocationStationType.CoolingTop => "冷却腔上层（用于Z轴）",
                EnuLocationStationType.CoolingBottom => "冷却腔下层（用于Z轴）",
                _ => $"未知位置: {location}"
            };
        }

        /// <summary>
        /// 获取位置的使用场景说明
        /// </summary>
        /// <param name="location">位置类型</param>
        /// <returns>使用场景说明</returns>
        public static string GetUsageScenario(this EnuLocationStationType location)
        {
            return location switch
            {
                EnuLocationStationType.CoolingChamber => "T轴旋转、R轴伸缩使用",
                EnuLocationStationType.CoolingTop => "Z轴精确高度控制使用（上层）",
                EnuLocationStationType.CoolingBottom => "Z轴精确高度控制使用（下层）",
                _ => "通用位置"
            };
        }

        #endregion 描述方法
    }
}