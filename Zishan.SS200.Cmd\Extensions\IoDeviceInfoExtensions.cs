using System;
using System.Collections.Generic;
using System.Linq;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Extensions
{
    /// <summary>
    /// IO设备信息扩展方法
    /// </summary>
    public static class IoDeviceInfoExtensions
    {
        /// <summary>
        /// 获取设备的完整描述信息
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="index">索引</param>
        /// <param name="ioType">IO类型前缀 (如: DI, DO)</param>
        /// <returns>完整描述</returns>
        public static string GetFullDescription(this IoDeviceInfo deviceInfo, string deviceName, int index, string ioType)
        {
            if (deviceInfo == null)
                return $"{deviceName} {ioType}_{index}";

            var descriptionParts = new List<string>
            {
                $"{deviceName} {ioType} {index}: {deviceInfo.Name}"
            };

            if (!string.IsNullOrEmpty(deviceInfo.IoCode))
                descriptionParts.Add($"IO代码: {deviceInfo.IoCode}");

            if (!string.IsNullOrEmpty(deviceInfo.McuIo))
                descriptionParts.Add($"MCU IO: {deviceInfo.McuIo}");

            if (!string.IsNullOrEmpty(deviceInfo.SensorType))
                descriptionParts.Add($"传感器类型: {deviceInfo.SensorType}");

            if (!string.IsNullOrEmpty(deviceInfo.ControlType))
                descriptionParts.Add($"控制类型: {deviceInfo.ControlType}");

            if (!string.IsNullOrEmpty(deviceInfo.Remark))
                descriptionParts.Add($"备注: {deviceInfo.Remark}");

            return string.Join(" | ", descriptionParts);
        }

        /// <summary>
        /// 获取设备的简短标题
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="defaultTitle">默认标题</param>
        /// <returns>标题</returns>
        public static string GetTitle(this IoDeviceInfo deviceInfo, string defaultTitle)
        {
            return deviceInfo?.Name ?? defaultTitle;
        }

        /// <summary>
        /// 检查设备信息是否有效
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>是否有效</returns>
        public static bool IsValid(this IoDeviceInfo deviceInfo)
        {
            return deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Name);
        }

        /// <summary>
        /// 获取IO代码，如果为空则返回默认值
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="defaultCode">默认代码</param>
        /// <returns>IO代码</returns>
        public static string GetIoCode(this IoDeviceInfo deviceInfo, string defaultCode)
        {
            return deviceInfo?.IoCode ?? defaultCode;
        }

        /// <summary>
        /// 获取MCU IO映射，如果为空则返回默认值
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="defaultMcuIo">默认MCU IO</param>
        /// <returns>MCU IO映射</returns>
        public static string GetMcuIo(this IoDeviceInfo deviceInfo, string defaultMcuIo)
        {
            return deviceInfo?.McuIo ?? defaultMcuIo;
        }

        /// <summary>
        /// 获取备注信息，如果为空则返回默认值
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="defaultRemark">默认备注</param>
        /// <returns>备注信息</returns>
        public static string GetRemark(this IoDeviceInfo deviceInfo, string defaultRemark = "")
        {
            return deviceInfo?.Remark ?? defaultRemark;
        }

        /// <summary>
        /// 检查是否为特定类型的传感器
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="sensorType">传感器类型</param>
        /// <returns>是否匹配</returns>
        public static bool IsSensorType(this IoDeviceInfo deviceInfo, string sensorType)
        {
            return deviceInfo?.SensorType?.Contains(sensorType, StringComparison.OrdinalIgnoreCase) == true;
        }

        /// <summary>
        /// 检查是否为特定类型的控制器
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="controlType">控制类型</param>
        /// <returns>是否匹配</returns>
        public static bool IsControlType(this IoDeviceInfo deviceInfo, string controlType)
        {
            return deviceInfo?.ControlType?.Contains(controlType, StringComparison.OrdinalIgnoreCase) == true;
        }

        /// <summary>
        /// 获取所有非空属性的键值对
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>属性字典</returns>
        public static Dictionary<string, string> GetProperties(this IoDeviceInfo deviceInfo)
        {
            var properties = new Dictionary<string, string>();

            if (deviceInfo == null)
                return properties;

            if (!string.IsNullOrEmpty(deviceInfo.Name))
                properties["名称"] = deviceInfo.Name;

            if (!string.IsNullOrEmpty(deviceInfo.IoCode))
                properties["IO代码"] = deviceInfo.IoCode;

            if (!string.IsNullOrEmpty(deviceInfo.McuIo))
                properties["MCU IO"] = deviceInfo.McuIo;

            if (!string.IsNullOrEmpty(deviceInfo.IoType))
                properties["IO类型"] = deviceInfo.IoType;

            if (!string.IsNullOrEmpty(deviceInfo.SensorType))
                properties["传感器类型"] = deviceInfo.SensorType;

            if (!string.IsNullOrEmpty(deviceInfo.ControlType))
                properties["控制类型"] = deviceInfo.ControlType;

            if (!string.IsNullOrEmpty(deviceInfo.Remark))
                properties["备注"] = deviceInfo.Remark;

            return properties;
        }

        /// <summary>
        /// 将IoDeviceInfo的详细信息应用到ModbusCoil
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="coil">要设置的ModbusCoil</param>
        public static void ApplyToModbusCoil(this IoDeviceInfo deviceInfo, Models.ModbusCoil coil)
        {
            if (deviceInfo == null || coil == null)
                return;

            coil.IoCode = deviceInfo.IoCode ?? "";
            coil.McuIo = deviceInfo.McuIo ?? "";
            coil.IoType = deviceInfo.IoType ?? "";
            coil.SensorType = deviceInfo.SensorType ?? "";
            coil.ControlType = deviceInfo.ControlType ?? "";
            coil.DetailedRemark = deviceInfo.Remark ?? "";
        }
    }
}
