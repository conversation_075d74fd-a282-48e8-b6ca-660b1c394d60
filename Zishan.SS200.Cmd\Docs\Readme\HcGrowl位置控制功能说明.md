# HcGrowl 位置控制功能说明

## 📍 概述

HandyControl 的 Growl 消息通知组件支持灵活的位置控制，可以在不同的显示区域展示消息。本文档详细介绍了 `HcGrowlExtensions` 中新增的位置控制功能。

## 🎯 显示模式

### 1. Desktop 模式（桌面模式）
- **显示位置**：整个桌面的右下角
- **特点**：即使应用程序窗口最小化或失去焦点，消息仍然可见
- **适用场景**：系统级重要通知、后台任务完成提示、全局状态更新

### 2. Window 模式（窗口模式）
- **显示位置**：当前应用程序窗口内的右上角
- **特点**：只在应用程序窗口内显示，窗口最小化时不可见
- **适用场景**：窗口相关操作反馈、界面交互提示、局部功能状态

## 🔧 使用方法

### 基础方法（支持位置参数）

所有基础消息方法都新增了 `displayMode` 参数：

```csharp
// 信息消息
HcGrowlExtensions.Info("消息内容", displayMode: GrowlDisplayMode.Window);
HcGrowlExtensions.Info("消息内容", displayMode: GrowlDisplayMode.Desktop);

// 成功消息
HcGrowlExtensions.Success("操作成功", displayMode: GrowlDisplayMode.Window);
HcGrowlExtensions.Success("操作成功", displayMode: GrowlDisplayMode.Desktop);

// 警告消息
HcGrowlExtensions.Warning("注意事项", displayMode: GrowlDisplayMode.Window);
HcGrowlExtensions.Warning("注意事项", displayMode: GrowlDisplayMode.Desktop);

// 错误消息
HcGrowlExtensions.Error("错误信息", displayMode: GrowlDisplayMode.Window);
HcGrowlExtensions.Error("错误信息", displayMode: GrowlDisplayMode.Desktop);
```

### 便捷方法（明确指定位置）

为了更直观的使用，提供了明确指定位置的便捷方法：

#### 窗口内显示
```csharp
// 在窗口内显示各种类型的消息
HcGrowlExtensions.InfoInWindow("数据加载完成");
HcGrowlExtensions.SuccessInWindow("文件保存成功");
HcGrowlExtensions.WarningInWindow("参数可能有误");
HcGrowlExtensions.ErrorInWindow("操作失败");
```

#### 桌面显示
```csharp
// 在桌面显示各种类型的消息
HcGrowlExtensions.InfoOnDesktop("系统状态更新");
HcGrowlExtensions.SuccessOnDesktop("后台任务完成");
HcGrowlExtensions.WarningOnDesktop("系统资源不足");
HcGrowlExtensions.ErrorOnDesktop("网络连接失败");
```

### 设备相关方法（支持位置参数）

设备相关的方法也支持位置控制：

```csharp
// 设备消息 - 窗口内显示
HcGrowlExtensions.DeviceInfo(EnuMcuDeviceType.Robot, "设备状态正常", 
    displayMode: GrowlDisplayMode.Window);

HcGrowlExtensions.DeviceSuccess(EnuMcuDeviceType.Shuttle, "命令执行成功", 
    displayMode: GrowlDisplayMode.Window);

// 设备消息 - 桌面显示（默认）
HcGrowlExtensions.DeviceWarning(EnuMcuDeviceType.ChamberA, "温度异常");
HcGrowlExtensions.DeviceError(EnuMcuDeviceType.ChamberB, "通信失败");
```

## 🎨 实际应用场景

### 场景1：用户界面操作反馈
```csharp
// 用户在界面上的操作，使用窗口模式
private void OnSaveButtonClick()
{
    try
    {
        SaveConfiguration();
        HcGrowlExtensions.SuccessInWindow("配置保存成功");
    }
    catch (Exception ex)
    {
        HcGrowlExtensions.ErrorInWindow($"保存失败：{ex.Message}");
    }
}
```

### 场景2：后台任务状态通知
```csharp
// 后台任务完成，使用桌面模式确保用户能看到
private async Task ProcessDataInBackground()
{
    try
    {
        await ProcessLargeDataSet();
        HcGrowlExtensions.SuccessOnDesktop("数据处理完成");
    }
    catch (Exception ex)
    {
        HcGrowlExtensions.ErrorOnDesktop($"数据处理失败：{ex.Message}");
    }
}
```

### 场景3：设备状态监控
```csharp
// 设备状态变化 - 根据重要性选择显示位置
private void OnDeviceStatusChanged(EnuMcuDeviceType deviceType, bool isConnected)
{
    if (isConnected)
    {
        // 连接成功 - 窗口内显示即可
        HcGrowlExtensions.DeviceSuccess(deviceType, "设备连接成功", 
            displayMode: GrowlDisplayMode.Window);
    }
    else
    {
        // 连接失败 - 桌面显示，确保用户注意到
        HcGrowlExtensions.DeviceError(deviceType, "设备连接失败", 
            displayMode: GrowlDisplayMode.Desktop);
    }
}
```

### 场景4：混合使用策略
```csharp
public class NotificationStrategy
{
    public static void ShowMessage(string message, MessageImportance importance)
    {
        switch (importance)
        {
            case MessageImportance.Low:
                // 低重要性 - 窗口内显示
                HcGrowlExtensions.InfoInWindow(message);
                break;
                
            case MessageImportance.Normal:
                // 普通重要性 - 默认桌面显示
                HcGrowlExtensions.Info(message);
                break;
                
            case MessageImportance.High:
                // 高重要性 - 桌面显示，延长时间
                HcGrowlExtensions.WarningOnDesktop(message, waitTime: 10);
                break;
                
            case MessageImportance.Critical:
                // 关键重要性 - 桌面显示，更长时间
                HcGrowlExtensions.ErrorOnDesktop(message, waitTime: 15);
                break;
        }
    }
}

public enum MessageImportance
{
    Low,
    Normal, 
    High,
    Critical
}
```

## 📋 最佳实践

### 1. 选择合适的显示模式
- ✅ **窗口模式**：用户界面操作反馈、表单验证提示、局部功能状态
- ✅ **桌面模式**：系统级通知、后台任务完成、设备状态变化、错误警告

### 2. 根据消息重要性选择位置
```csharp
// 一般信息 - 窗口内
HcGrowlExtensions.InfoInWindow("数据刷新完成");

// 重要警告 - 桌面显示
HcGrowlExtensions.WarningOnDesktop("磁盘空间不足");

// 严重错误 - 桌面显示 + 延长时间
HcGrowlExtensions.ErrorOnDesktop("系统发生严重错误", waitTime: 15);
```

### 3. 结合 Token 机制管理消息
```csharp
// 使用 Token 分组管理不同位置的消息
HcGrowlExtensions.InfoInWindow("界面操作完成", token: "ui-feedback");
HcGrowlExtensions.InfoOnDesktop("后台任务进度", token: "background-tasks");

// 分别清理不同位置的消息
Growl.Clear("ui-feedback");        // 清理窗口内消息
Growl.ClearGlobal("background-tasks"); // 清理桌面消息
```

## ⚠️ 注意事项

1. **默认行为保持不变**：所有现有代码无需修改，默认使用桌面模式
2. **向后兼容**：新增的 `displayMode` 参数为可选参数，不影响现有调用
3. **清理消息**：窗口模式和桌面模式的消息需要分别清理
4. **性能考虑**：避免同时在两个位置显示大量消息

## 🔄 迁移建议

### 现有代码无需修改
```csharp
// 现有代码继续正常工作
HcGrowlExtensions.Info("消息内容");  // 默认桌面模式
HcGrowlExtensions.Success("操作成功"); // 默认桌面模式
```

### 逐步优化建议
```csharp
// 可以逐步优化为更合适的显示位置
// 原来：
HcGrowlExtensions.Info("表单验证通过");

// 优化后：
HcGrowlExtensions.InfoInWindow("表单验证通过"); // 更适合界面操作反馈
```

## 🧪 快速测试示例

您可以使用以下代码快速测试位置控制功能：

```csharp
public class GrowlPositionTestExample
{
    public static void TestPositionControl()
    {
        // 测试窗口内显示
        HcGrowlExtensions.InfoInWindow("这是窗口内的信息消息");
        HcGrowlExtensions.SuccessInWindow("这是窗口内的成功消息");
        HcGrowlExtensions.WarningInWindow("这是窗口内的警告消息");
        HcGrowlExtensions.ErrorInWindow("这是窗口内的错误消息");

        // 等待一下，然后测试桌面显示
        await Task.Delay(2000);

        // 测试桌面显示
        HcGrowlExtensions.InfoOnDesktop("这是桌面的信息消息");
        HcGrowlExtensions.SuccessOnDesktop("这是桌面的成功消息");
        HcGrowlExtensions.WarningOnDesktop("这是桌面的警告消息");
        HcGrowlExtensions.ErrorOnDesktop("这是桌面的错误消息");

        // 测试设备消息的位置控制
        HcGrowlExtensions.DeviceInfo(EnuMcuDeviceType.Robot, "窗口内设备消息",
            displayMode: GrowlDisplayMode.Window);
        HcGrowlExtensions.DeviceError(EnuMcuDeviceType.Shuttle, "桌面设备错误",
            displayMode: GrowlDisplayMode.Desktop);
    }
}
```

通过这些位置控制功能，您可以为用户提供更加精准和合适的消息通知体验！
