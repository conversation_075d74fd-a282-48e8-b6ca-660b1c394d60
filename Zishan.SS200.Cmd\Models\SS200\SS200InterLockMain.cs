﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Models.SS200.AlarmCode;
using Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.ViewModels;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberB;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.ChamberB;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Chamber;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Shuttle;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Robot;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.ChamberA;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Shuttle;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.ChamberA;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Shuttle;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.MainSystem;
using Zishan.SS200.Cmd.Config.SS200.AlarmCode.Robot;
using Zishan.SS200.Cmd.Config.SS200.AlarmCode.ChamberA;
using Zishan.SS200.Cmd.Config.SS200.AlarmCode.Shuttle;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberA;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Shuttle;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.MainSystem;

namespace Zishan.SS200.Cmd.Models.SS200
{
    #region 属性访问器基类

    /// <summary>
    /// IO属性访问器基类
    /// </summary>
    /// <typeparam name="TEnum">枚举类型</typeparam>
    public class IOPropertyAccessor<TEnum> where TEnum : Enum
    {
        private readonly CoilStatusHelper _coilHelper;
        private readonly EnuMcuDeviceType _deviceType;
        private readonly TEnum _enumValue;

        public IOPropertyAccessor(CoilStatusHelper coilHelper, EnuMcuDeviceType deviceType, TEnum enumValue)
        {
            _coilHelper = coilHelper ?? throw new ArgumentNullException(nameof(coilHelper));
            _deviceType = deviceType;
            _enumValue = enumValue;
        }

        /// <summary>
        /// 动态计算属性 - 实时从设备获取当前值
        /// </summary>
        public bool Value => _coilHelper.GetCoilValue(_deviceType, _enumValue);

        /// <summary>
        /// 枚举的描述信息 - 直接从枚举的DescriptionAttribute获取
        /// </summary>
        public string Content => _enumValue.GetDescription();

        /// <summary>
        /// 获取可空的线圈值
        /// </summary>
        public bool? ValueNullable => _coilHelper.GetCoilValueNullable(_deviceType, _enumValue);

        /// <summary>
        /// 检查线圈是否激活
        /// </summary>
        public bool IsActive => _coilHelper.IsCoilActive(_deviceType, _enumValue);
    }

    /// <summary>
    /// 报警属性访问器
    /// </summary>
    public class AlarmPropertyAccessor
    {
        private readonly AlarmItem _alarmItem;

        public AlarmPropertyAccessor(AlarmItem alarmItem)
        {
            _alarmItem = alarmItem ?? throw new ArgumentNullException(nameof(alarmItem));
        }

        /// <summary>
        /// 报警内容（英文）
        /// </summary>
        public string Content => _alarmItem.Content;

        /// <summary>
        /// 报警内容（中文描述）
        /// </summary>
        public string ChsContent => _alarmItem.ChineseDescription;

        /// <summary>
        /// 报警原因（暂时返回中文描述，后续可扩展）
        /// </summary>
        public string Cause => _alarmItem.ChineseDescription;

        /// <summary>
        /// 报警代码
        /// </summary>
        public string Code => _alarmItem.Code;

        /// <summary>
        /// 报警项ID
        /// </summary>
        public int Item => _alarmItem.Item;
    }

    /// <summary>
    /// 配置属性访问器
    /// </summary>
    public class ConfigPropertyAccessor
    {
        private readonly ConfigureSetting _setting;

        public ConfigPropertyAccessor(ConfigureSetting setting)
        {
            _setting = setting ?? throw new ArgumentNullException(nameof(setting));
        }

        /// <summary>
        /// 配置值（整数类型，向后兼容）
        /// 注意：对于小数参数，请使用DoubleValue或RawValue属性
        /// </summary>
        public int Value => _setting.IntValue;

        /// <summary>
        /// 配置值（双精度浮点类型）
        /// </summary>
        public double DoubleValue => _setting.GetValue<double>();

        /// <summary>
        /// 获取强类型的配置值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <returns>转换后的值</returns>
        public T GetValue<T>() => _setting.GetValue<T>();

        /// <summary>
        /// 原始配置值（object类型）
        /// </summary>
        public object RawValue => _setting.Value;

        /// <summary>
        /// 字符串配置值
        /// </summary>
        public string StringValue => _setting.StringValue;

        /// <summary>
        /// 布尔配置值
        /// </summary>
        public bool BoolValue => _setting.BoolValue;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Content => _setting.Description;

        /// <summary>
        /// 配置单位
        /// </summary>
        public string Unit => _setting.Unit;

        /// <summary>
        /// 配置代码
        /// </summary>
        public string Code => _setting.Code;

        /// <summary>
        /// 轴类型
        /// </summary>
        public int AxisType => _setting.AxisType;

        /// <summary>
        /// 配置范围（暂时返回描述，后续可扩展）
        /// </summary>
        public string Range => _setting.Description;
    }

    #endregion 属性访问器基类

    #region 设备IO访问器

    /// <summary>
    /// Robot IO访问器
    /// </summary>
    public class RobotIOAccessor
    {
        private readonly CoilStatusHelper _coilHelper;
        private readonly ConcurrentDictionary<string, object> _cache = new();

        public RobotIOAccessor(CoilStatusHelper coilHelper)
        {
            _coilHelper = coilHelper ?? throw new ArgumentNullException(nameof(coilHelper));
        }

        #region Robot IO访问器

        /// <summary>
        /// RDI1 - Paddle传感器1左侧
        /// </summary>
        public IOPropertyAccessor<EnuRobotDICodes> RDI1_PaddleSensor1Left =>
            GetOrCreateAccessor(EnuRobotDICodes.RDI1_PaddleSensor1Left);

        /// <summary>
        /// RDI2 - Paddle传感器2右侧
        /// </summary>
        public IOPropertyAccessor<EnuRobotDICodes> RDI2_PaddleSensor2Right =>
            GetOrCreateAccessor(EnuRobotDICodes.RDI2_PaddleSensor2Right);

        /// <summary>
        /// RDI3 - Pin搜索1
        /// </summary>
        public IOPropertyAccessor<EnuRobotDICodes> RDI3_PinSearch1 =>
            GetOrCreateAccessor(EnuRobotDICodes.RDI3_PinSearch1);

        /// <summary>
        /// RDI4 - Pin搜索2
        /// </summary>
        public IOPropertyAccessor<EnuRobotDICodes> RDI4_PinSearch2 =>
            GetOrCreateAccessor(EnuRobotDICodes.RDI4_PinSearch2);

        #endregion Robot IO访问器

        private IOPropertyAccessor<EnuRobotDICodes> GetOrCreateAccessor(EnuRobotDICodes enumValue)
        {
            string key = enumValue.ToString();
            return (IOPropertyAccessor<EnuRobotDICodes>)_cache.GetOrAdd(key,
                _ => new IOPropertyAccessor<EnuRobotDICodes>(_coilHelper, EnuMcuDeviceType.Robot, enumValue));
        }
    }

    /// <summary>
    /// Chamber IO访问器
    /// </summary>
    public class ChamberIOAccessor
    {
        private readonly CoilStatusHelper _coilHelper;
        private readonly EnuMcuDeviceType _deviceType;
        private readonly ConcurrentDictionary<string, object> _cache = new();

        public ChamberIOAccessor(CoilStatusHelper coilHelper, EnuMcuDeviceType deviceType)
        {
            _coilHelper = coilHelper ?? throw new ArgumentNullException(nameof(coilHelper));
            _deviceType = deviceType;
        }

        #region Chamber IO访问器

        #region Chamber DI 访问器

        /// <summary>
        /// PDI1 - CV打开传感器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI1_CvOpenSensor =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI1_CvOpenSensor);

        /// <summary>
        /// PDI2 - CV关闭传感器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI2_CvCloseSensor =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI2_CvCloseSensor);

        /// <summary>
        /// PDI3 - T/V打开传感器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI3_TvOpenSensor =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI3_TvOpenSensor);

        /// <summary>
        /// PDI4 - T/V关闭传感器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI4_TvCloseSensor =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI4_TvCloseSensor);

        /// <summary>
        /// PDI5 - 前级开关
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI5_ForelineSwitch =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI5_ForelineSwitch);

        /// <summary>
        /// PDI6 - Torr开关
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI6_TorrSwitch =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI6_TorrSwitch);

        /// <summary>
        /// PDI7 - 等离子传感器1
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI7_PlasmaSensor1 =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI7_PlasmaSensor1);

        /// <summary>
        /// PDI8 - 等离子传感器2
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI8_PlasmaSensor2 =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI8_PlasmaSensor2);

        /// <summary>
        /// PDI9 - 匹配互锁开关1
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI9_MatchingInterlockSwitch1 =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI9_MatchingInterlockSwitch1);

        /// <summary>
        /// PDI10 - 匹配互锁开关2
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI10_MatchingInterlockSwitch2 =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI10_MatchingInterlockSwitch2);

        /// <summary>
        /// PDI11 - 温控器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI11_Thermostat =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI11_Thermostat);

        /// <summary>
        /// PDI12 - Slit Door打开传感器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI12_SlitDoorOpenSensor =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI12_SlitDoorOpenSensor);

        /// <summary>
        /// PDI13 - Slit Door关闭传感器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI13_SlitDoorCloseSensor =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI13_SlitDoorCloseSensor);

        /// <summary>
        /// PDI14 - Lift Pin上升传感器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI14_LiftPinUpSensor =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI14_LiftPinUpSensor);

        /// <summary>
        /// PDI15 - Lift Pin下降传感器
        /// </summary>
        public IOPropertyAccessor<EnuChamberDICodes> PDI15_LiftPinDownSensor =>
            GetOrCreateAccessor(EnuChamberDICodes.PDI15_LiftPinDownSensor);

        #endregion Chamber DI 访问器

        #region Chamber DO 访问器

        /// <summary>
        /// PDO1 - T/V打开
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO1_TvOpen =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO1_TvOpen);

        /// <summary>
        /// PDO2 - T/V关闭
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO2_TvClose =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO2_TvClose);

        /// <summary>
        /// PDO3 - T/V FRZ
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO3_TvFrz =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO3_TvFrz);

        /// <summary>
        /// PDO4 - CV控制
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO4_CvControl =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO4_CvControl);

        /// <summary>
        /// PDO5 - 气体C1控制
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO5_GasC1Control =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO5_GasC1Control);

        /// <summary>
        /// PDO6 - 气体C2控制
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO6_GasC2Control =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO6_GasC2Control);

        /// <summary>
        /// PDO7 - 气体C3控制
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO7_GasC3Control =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO7_GasC3Control);

        /// <summary>
        /// PDO8 - 气体C4控制
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO8_GasC4Control =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO8_GasC4Control);

        /// <summary>
        /// PDO9 - 气体CM阀门控制
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO9_GasCmValveControl =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO9_GasCmValveControl);

        /// <summary>
        /// PDO10 - Slit Door打开
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO10_SlitDoorOpen =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO10_SlitDoorOpen);

        /// <summary>
        /// PDO11 - Slit Door关闭
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO11_SlitDoorClose =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO11_SlitDoorClose);

        /// <summary>
        /// PDO12 - Lift Pin上升
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO12_LiftPinUp =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO12_LiftPinUp);

        /// <summary>
        /// PDO13 - Lift Pin下降
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO13_LiftPinDown =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO13_LiftPinDown);

        /// <summary>
        /// PDO14 - RF1使能
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO14_Rf1Enable =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO14_Rf1Enable);

        /// <summary>
        /// PDO15 - RF2使能
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO15_Rf2Enable =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO15_Rf2Enable);

        /// <summary>
        /// PDO16 - 加热器使能
        /// </summary>
        public IOPropertyAccessor<EnuChamberDOCodes> PDO16_HeaterEnable =>
            GetOrCreateDOAccessor(EnuChamberDOCodes.PDO16_HeaterEnable);

        #endregion Chamber DO 访问器

        #endregion Chamber IO访问器

        private IOPropertyAccessor<EnuChamberDICodes> GetOrCreateAccessor(EnuChamberDICodes enumValue)
        {
            string key = $"{_deviceType}_{enumValue}";
            return (IOPropertyAccessor<EnuChamberDICodes>)_cache.GetOrAdd(key,
                _ => new IOPropertyAccessor<EnuChamberDICodes>(_coilHelper, _deviceType, enumValue));
        }

        private IOPropertyAccessor<EnuChamberDOCodes> GetOrCreateDOAccessor(EnuChamberDOCodes enumValue)
        {
            string key = $"{_deviceType}_{enumValue}";
            return (IOPropertyAccessor<EnuChamberDOCodes>)_cache.GetOrAdd(key,
                _ => new IOPropertyAccessor<EnuChamberDOCodes>(_coilHelper, _deviceType, enumValue));
        }
    }

    /// <summary>
    /// Shuttle IO访问器
    /// </summary>
    public class ShuttleIOAccessor
    {
        private readonly CoilStatusHelper _coilHelper;
        private readonly ConcurrentDictionary<string, object> _cache = new();

        public ShuttleIOAccessor(CoilStatusHelper coilHelper)
        {
            _coilHelper = coilHelper ?? throw new ArgumentNullException(nameof(coilHelper));
        }

        #region Shuttle IO访问器

        #region Shuttle DI 访问器

        /// <summary>
        /// SDI1 - 晶圆盒门上升传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI1_CassetteDoorUpSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI1_CassetteDoorUpSensor);

        /// <summary>
        /// SDI2 - 晶圆盒门下降传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI2_CassetteDoorDownSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI2_CassetteDoorDownSensor);

        /// <summary>
        /// SDI3 - 晶圆盒巢伸出传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI3_CassetteNestExtendSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI3_CassetteNestExtendSensor);

        /// <summary>
        /// SDI4 - 晶圆盒巢收回传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI4_CassetteNestRetractSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI4_CassetteNestRetractSensor);

        /// <summary>
        /// SDI5 - 晶圆盒巢原位开关
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI5_CassetteNestHomeSwitch =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI5_CassetteNestHomeSwitch);

        /// <summary>
        /// SDI6 - 存在传感器晶圆盒1
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI6_PresentSensorCassette1 =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI6_PresentSensorCassette1);

        /// <summary>
        /// SDI7 - 存在传感器晶圆盒2
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI7_PresentSensorCassette2 =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI7_PresentSensorCassette2);

        /// <summary>
        /// SDI8 - 存在传感器晶圆盒3
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI8_PresentSensorCassette3 =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI8_PresentSensorCassette3);

        /// <summary>
        /// SDI9 - 存在传感器晶圆盒4
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI9_PresentSensorCassette4 =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI9_PresentSensorCassette4);

        /// <summary>
        /// SDI10 - 水流开关
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI10_WaterFlowSwitch =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI10_WaterFlowSwitch);

        /// <summary>
        /// SDI11 - 光幕1
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI11_LightCurtain1 =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI11_LightCurtain1);

        /// <summary>
        /// SDI12 - 光幕2
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI12_LightCurtain2 =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI12_LightCurtain2);

        /// <summary>
        /// SDI13 - Shuttle旋转传感器1
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI13_ShuttleRotateSensor1 =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI13_ShuttleRotateSensor1);

        /// <summary>
        /// SDI14 - Shuttle旋转传感器2
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI14_ShuttleRotateSensor2 =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI14_ShuttleRotateSensor2);

        /// <summary>
        /// SDI15 - Shuttle上升传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI15_ShuttleUpSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI15_ShuttleUpSensor);

        /// <summary>
        /// SDI16 - Shuttle下降传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI16_ShuttleDownSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI16_ShuttleDownSensor);

        /// <summary>
        /// SDI17 - 晶圆滑出传感器1 FL
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI17_WaferSlideOutSensor1FL =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI17_WaferSlideOutSensor1FL);

        /// <summary>
        /// SDI18 - 晶圆滑出传感器2 FR
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI18_WaferSlideOutSensor2FR =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI18_WaferSlideOutSensor2FR);

        /// <summary>
        /// SDI19 - 晶圆滑出传感器3 BL
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI19_WaferSlideOutSensor3BL =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI19_WaferSlideOutSensor3BL);

        /// <summary>
        /// SDI20 - 晶圆滑出传感器4 BR
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI20_WaferSlideOutSensor4BR =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI20_WaferSlideOutSensor4BR);

        /// <summary>
        /// SDI21 - Shuttle前级开关
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI21_ShuttleForlineSwitch =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI21_ShuttleForlineSwitch);

        /// <summary>
        /// SDI22 - Shuttle真空ISO阀门打开传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI22_ShuttleVacuumIsoValveOpenSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI22_ShuttleVacuumIsoValveOpenSensor);

        /// <summary>
        /// SDI23 - Shuttle真空ISO阀门关闭传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI23_ShuttleVacuumIsoValveCloseSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI23_ShuttleVacuumIsoValveCloseSensor);

        /// <summary>
        /// SDI24 - XV交叉阀门打开传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI24_XvCrossValveOpenSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI24_XvCrossValveOpenSensor);

        /// <summary>
        /// SDI25 - XV交叉阀门关闭传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI25_XvCrossValveCloseSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI25_XvCrossValveCloseSensor);

        /// <summary>
        /// SDI26 - 冷却腔泄漏传感器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI26_CoolingChamberLeakSensor =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI26_CoolingChamberLeakSensor);

        /// <summary>
        /// SDI27 - 备用
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI27_Spare =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI27_Spare);

        /// <summary>
        /// SDI28 - 备用
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDICodes> SDI28_Spare =>
            GetOrCreateAccessor(EnuShuttleDICodes.SDI28_Spare);

        #endregion Shuttle DI 访问器

        #region Shuttle DO 访问器

        /// <summary>
        /// SDO1 - 晶圆盒门气缸上升
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO1_CassetteDoorCylinderUp =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO1_CassetteDoorCylinderUp);

        /// <summary>
        /// SDO2 - 晶圆盒门气缸下降
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO2_CassetteDoorCylinderDown =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO2_CassetteDoorCylinderDown);

        /// <summary>
        /// SDO3 - 晶圆盒门运动使能
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO3_CassetteDoorMotionEnable =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO3_CassetteDoorMotionEnable);

        /// <summary>
        /// SDO4 - 晶圆盒巢气缸伸出
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO4_CassetteNestCylinderExtend =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO4_CassetteNestCylinderExtend);

        /// <summary>
        /// SDO5 - 晶圆盒巢气缸收回
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO5_CassetteNestCylinderRetract =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO5_CassetteNestCylinderRetract);

        /// <summary>
        /// SDO6 - Shuttle真空ISO阀门
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO6_ShuttleVacuumIsoValve =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO6_ShuttleVacuumIsoValve);

        /// <summary>
        /// SDO7 - Shuttle回填阀门
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO7_ShuttleBackfillValve =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO7_ShuttleBackfillValve);

        /// <summary>
        /// SDO8 - XV交叉阀门
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO8_XvCrossValve =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO8_XvCrossValve);

        /// <summary>
        /// SDO9 - 负载锁排气阀门
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO9_LoadlockBleedValve =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO9_LoadlockBleedValve);

        /// <summary>
        /// SDO10 - 负载锁回填阀门
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO10_LoadlockBackfillValve =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO10_LoadlockBackfillValve);

        /// <summary>
        /// SDO11 - Shuttle组件顺时针旋转
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO11_ShuttleAssyRotationCW =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO11_ShuttleAssyRotationCW);

        /// <summary>
        /// SDO12 - Shuttle组件逆时针旋转
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO12_ShuttleAssyRotationCCW =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO12_ShuttleAssyRotationCCW);

        /// <summary>
        /// SDO13 - Shuttle电机上升
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO13_ShuttleMotorUp =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO13_ShuttleMotorUp);

        /// <summary>
        /// SDO14 - Shuttle电机下降
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO14_ShuttleMotorDown =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO14_ShuttleMotorDown);

        /// <summary>
        /// SDO15 - Shuttle制动器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO15_ShuttleBraker =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO15_ShuttleBraker);

        /// <summary>
        /// SDO16 - PCWS开关
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO16_PcwsSwitch =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO16_PcwsSwitch);

        /// <summary>
        /// SDO17 - LOAD_READY1
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO17_LoadReady1 =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO17_LoadReady1);

        /// <summary>
        /// SDO18 - UNLOAD_READY1
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO18_UnloadReady1 =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO18_UnloadReady1);

        /// <summary>
        /// SDO19 - LOAD_READY2
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO19_LoadReady2 =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO19_LoadReady2);

        /// <summary>
        /// SDO20 - UNLOAD_READY2
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO20_UnloadReady2 =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO20_UnloadReady2);

        /// <summary>
        /// SDO21 - 紫色蜂鸣器
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO21_PurpleBuzzer =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO21_PurpleBuzzer);

        /// <summary>
        /// SDO22 - 蓝色LED
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO22_BlueLed =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO22_BlueLed);

        /// <summary>
        /// SDO23 - 绿色LED
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO23_GreenLed =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO23_GreenLed);

        /// <summary>
        /// SDO24 - 橙色LED
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO24_OrangeLed =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO24_OrangeLed);

        /// <summary>
        /// SDO25 - 红色LED
        /// </summary>
        public IOPropertyAccessor<EnuShuttleDOCodes> SDO25_RedLed =>
            GetOrCreateDOAccessor(EnuShuttleDOCodes.SDO25_RedLed);

        #endregion Shuttle DO 访问器

        #endregion Shuttle IO访问器

        private IOPropertyAccessor<EnuShuttleDICodes> GetOrCreateAccessor(EnuShuttleDICodes enumValue)
        {
            string key = enumValue.ToString();
            return (IOPropertyAccessor<EnuShuttleDICodes>)_cache.GetOrAdd(key,
                _ => new IOPropertyAccessor<EnuShuttleDICodes>(_coilHelper, EnuMcuDeviceType.Shuttle, enumValue));
        }

        private IOPropertyAccessor<EnuShuttleDOCodes> GetOrCreateDOAccessor(EnuShuttleDOCodes enumValue)
        {
            string key = enumValue.ToString();
            return (IOPropertyAccessor<EnuShuttleDOCodes>)_cache.GetOrAdd(key,
                _ => new IOPropertyAccessor<EnuShuttleDOCodes>(_coilHelper, EnuMcuDeviceType.Shuttle, enumValue));
        }
    }

    #endregion 设备IO访问器

    #region 报警访问器

    /// <summary>
    /// Robot报警访问器
    /// </summary>
    public class RobotAlarmAccessor
    {
        private readonly RobotErrorCodesProvider _provider;
        private readonly ConcurrentDictionary<string, AlarmPropertyAccessor> _cache = new();

        public RobotAlarmAccessor()
        {
            _provider = RobotErrorCodesProvider.Instance;
        }

        #region Robot报警代码的访问器定义

        /// <summary>
        /// RA1 - Robot system status is busy, command reject
        /// </summary>
        public AlarmPropertyAccessor RA1_SystemBusyReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA1);

        /// <summary>
        /// RA2 - Robot system status is alarm, command reject
        /// </summary>
        public AlarmPropertyAccessor RA2_SystemAlarmReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA2);

        /// <summary>
        /// RA3 - Robot R-axis not at home position, T-axis motion error
        /// </summary>
        public AlarmPropertyAccessor RA3_RAxisNotHomeError =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA3);

        /// <summary>
        /// RA4 - Robot T-axis not in right position, PLS confirm to R-axis motion
        /// </summary>
        public AlarmPropertyAccessor RA4_TAxisPositionError =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA4);

        /// <summary>
        /// RA5 - Robot rotation time out
        /// </summary>
        public AlarmPropertyAccessor RA5_RotationTimeout =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA5);

        /// <summary>
        /// RA6 - Robot extension time out
        /// </summary>
        public AlarmPropertyAccessor RA6_ExtensionTimeout =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA6);

        /// <summary>
        /// RA7 - Robot lift time out
        /// </summary>
        public AlarmPropertyAccessor RA7_LiftTimeout =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA7);

        /// <summary>
        /// RA8 - CHA slit door not at open status, robot can not extend
        /// </summary>
        public AlarmPropertyAccessor RA8_CHASlitDoorNotOpen =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA8);

        /// <summary>
        /// RA9 - CHB slit door not at open status, robot can not extend
        /// </summary>
        public AlarmPropertyAccessor RA9_CHBSlitDoorNotOpen =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA9);

        /// <summary>
        /// RA10 - Robot Z-axis not in right position, PLS confirm to R-axis motion
        /// </summary>
        public AlarmPropertyAccessor RA10_ZAxisPositionError =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA10);

        /// <summary>
        /// RA11 - Robot R-axis not at home position, Z-axis can not move to right position
        /// </summary>
        public AlarmPropertyAccessor RA11_RAxisNotHomeZAxisError =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA11);

        /// <summary>
        /// RA12 - Robot T-axis not in right position, Z-axis can not move to right position
        /// </summary>
        public AlarmPropertyAccessor RA12_TAxisPositionZAxisError =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA12);

        /// <summary>
        /// RA13 - Shuttle position status not at right position, can not do pin search
        /// </summary>
        public AlarmPropertyAccessor RA13_ShuttlePositionPinSearchError =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA13);

        /// <summary>
        /// RA14 - paddle hit to pin ball, pin search failure
        /// </summary>
        public AlarmPropertyAccessor RA14_PaddleHitPinBall =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA14);

        /// <summary>
        /// RA15 - pin ball status failure, pin search failure
        /// </summary>
        public AlarmPropertyAccessor RA15_PinBallStatusFailure =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA15);

        /// <summary>
        /// RA16 - Can not find pin ball, pin search filure
        /// </summary>
        public AlarmPropertyAccessor RA16_CannotFindPinBall =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA16);

        /// <summary>
        /// RA17 - Paddle status occupied or wafer status inconsistent, command reject
        /// </summary>
        public AlarmPropertyAccessor RA17_PaddleOccupiedOrWaferInconsistent =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA17);

        /// <summary>
        /// RA18 - There is(are) wafer(s) on paddle, and pin search data invalid command reject
        /// </summary>
        public AlarmPropertyAccessor RA18_WaferOnPaddlePinSearchInvalid =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA18);

        /// <summary>
        /// RA19 - BL slide out sensor detector wafer slide out
        /// </summary>
        public AlarmPropertyAccessor RA19_BLSlideOutDetected =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA19);

        /// <summary>
        /// RA20 - BR slide out sensor detector wafer slide out
        /// </summary>
        public AlarmPropertyAccessor RA20_BRSlideOutDetected =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA20);

        /// <summary>
        /// RA21 - BL and BR slide out sensor detector wafer slide out
        /// </summary>
        public AlarmPropertyAccessor RA21_BLAndBRSlideOutDetected =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA21);

        /// <summary>
        /// RA22 - Pin search value delta out of setpoint
        /// </summary>
        public AlarmPropertyAccessor RA22_PinSearchValueDeltaOutOfRange =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA22);

        /// <summary>
        /// RA23 - Shuttle 1 exist status is diable, command reject
        /// </summary>
        public AlarmPropertyAccessor RA23_Shuttle1ExistDisabled =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA23);

        /// <summary>
        /// RA24 - Shuttle 2 exist status is diable, command reject
        /// </summary>
        public AlarmPropertyAccessor RA24_Shuttle2ExistDisabled =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA24);

        /// <summary>
        /// RA25 - shuttle position status not at right position, can not move wafer
        /// </summary>
        public AlarmPropertyAccessor RA25_ShuttlePositionWaferMoveError =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA25);

        /// <summary>
        /// RA26 - CHA exist status is disable, command reject
        /// </summary>
        public AlarmPropertyAccessor RA26_CHAExistDisabled =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA26);

        /// <summary>
        /// RA27 - CHB exist status is disable, command reject
        /// </summary>
        public AlarmPropertyAccessor RA27_CHBExistDisabled =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA27);

        /// <summary>
        /// RA28 - CHA trigger status is alarm, command reject
        /// </summary>
        public AlarmPropertyAccessor RA28_CHATriggerAlarm =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA28);

        /// <summary>
        /// RA29 - CHB trigger status is alarm, command reject
        /// </summary>
        public AlarmPropertyAccessor RA29_CHBTriggerAlarm =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA29);

        /// <summary>
        /// RA30 - CHA run status is busy, command reject
        /// </summary>
        public AlarmPropertyAccessor RA30_CHARunBusy =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA30);

        /// <summary>
        /// RA31 - CHB run status is busy, command reject
        /// </summary>
        public AlarmPropertyAccessor RA31_CHBRunBusy =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA31);

        /// <summary>
        /// RA32 - CHA run status is processing, command reject
        /// </summary>
        public AlarmPropertyAccessor RA32_CHARunProcessing =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA32);

        /// <summary>
        /// RA33 - CHB run status is processing, command reject
        /// </summary>
        public AlarmPropertyAccessor RA33_CHBRunProcessing =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA33);

        /// <summary>
        /// RA34 - Cooling chamber trigger status is alarm, command reject
        /// </summary>
        public AlarmPropertyAccessor RA34_CoolingChamberTriggerAlarm =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA34);

        /// <summary>
        /// RA35 - Cooling chamber run status is not idle status, command reject
        /// </summary>
        public AlarmPropertyAccessor RA35_CoolingChamberNotIdle =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA35);

        /// <summary>
        /// RA36 - cassette slot is not empty, can not put wafer to cassette slot
        /// </summary>
        public AlarmPropertyAccessor RA36_CassetteSlotNotEmpty =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA36);

        /// <summary>
        /// RA37 - cassette slot status inconsistent, command reject
        /// </summary>
        public AlarmPropertyAccessor RA37_CassetteSlotInconsistent =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA37);

        /// <summary>
        /// RA38 - smooth P1 wafer has been lost
        /// </summary>
        public AlarmPropertyAccessor RA38_SmoothP1WaferLost =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA38);

        /// <summary>
        /// RA39 - smooth P2 wafer has been lost
        /// </summary>
        public AlarmPropertyAccessor RA39_SmoothP2WaferLost =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA39);

        /// <summary>
        /// RA40 - smooth side both paddle wafer has been lost
        /// </summary>
        public AlarmPropertyAccessor RA40_SmoothBothPaddleWaferLost =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA40);

        /// <summary>
        /// RA41 - nose P1 wafer has been lost
        /// </summary>
        public AlarmPropertyAccessor RA41_NoseP1WaferLost =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA41);

        /// <summary>
        /// RA42 - nose P2 wafer has been lost
        /// </summary>
        public AlarmPropertyAccessor RA42_NoseP2WaferLost =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA42);

        /// <summary>
        /// RA43 - nose side both paddle wafer has been lost
        /// </summary>
        public AlarmPropertyAccessor RA43_NoseBothPaddleWaferLost =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA43);

        /// <summary>
        /// RA44 - smooth P1 wafer put wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA44_SmoothP1PutWaferFailure =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA44);

        /// <summary>
        /// RA45 - smooth P2 wafer put wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA45_SmoothP2PutWaferFailure =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA45);

        /// <summary>
        /// RA46 - smooth side both paddle wafer put wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA46_SmoothBothPaddlePutWaferFailure =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA46);

        /// <summary>
        /// RA47 - nose P1 wafer put wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA47_NoseP1PutWaferFailure =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA47);

        /// <summary>
        /// RA48 - nose P2 wafer put wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA48_NoseP2PutWaferFailure =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA48);

        /// <summary>
        /// RA49 - nose side both wafer put wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA49_NoseBothPutWaferFailure =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA49);

        /// <summary>
        /// RA50 - sensor show smooth P1 wafer status disconsister with slot, move wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA50_SmoothP1WaferStatusInconsistent =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA50);

        /// <summary>
        /// RA51 - sensor show smooth P2 wafer status disconsister with slot, move wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA51_SmoothP2WaferStatusInconsistent =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA51);

        /// <summary>
        /// RA52 - sensor show nose P1 wafer status disconsister with slot, move wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA52_NoseP1WaferStatusInconsistent =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA52);

        /// <summary>
        /// RA53 - sensor show nose P2 wafer status disconsister with slot, move wafer failure
        /// </summary>
        public AlarmPropertyAccessor RA53_NoseP2WaferStatusInconsistent =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA53);

        /// <summary>
        /// RA54 - There is(are) wafer(s) in CHA, put wafer to CHA command reject
        /// </summary>
        public AlarmPropertyAccessor RA54_WaferInCHAPutReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA54);

        /// <summary>
        /// RA55 - There is(are) wafer(s) in CHB, put wafer to CHA command reject
        /// </summary>
        public AlarmPropertyAccessor RA55_WaferInCHBPutReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA55);

        /// <summary>
        /// RA56 - There is(are) wafer(s) in CT, put wafer to CHA command reject
        /// </summary>
        public AlarmPropertyAccessor RA56_WaferInCTPutReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA56);

        /// <summary>
        /// RA57 - There is(are) wafer(s) in CB, put wafer to CHA command reject
        /// </summary>
        public AlarmPropertyAccessor RA57_WaferInCBPutReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA57);

        /// <summary>
        /// RA58 - smooth P1 wafer status abnormal
        /// </summary>
        public AlarmPropertyAccessor RA58_SmoothP1WaferStatusAbnormal =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA58);

        /// <summary>
        /// RA59 - smooth P2 wafer status abnormal
        /// </summary>
        public AlarmPropertyAccessor RA59_SmoothP2WaferStatusAbnormal =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA59);

        /// <summary>
        /// RA60 - nose P1 wafer status abnormal
        /// </summary>
        public AlarmPropertyAccessor RA60_NoseP1WaferStatusAbnormal =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA60);

        /// <summary>
        /// RA61 - nose P2 wafer status abnormal
        /// </summary>
        public AlarmPropertyAccessor RA61_NoseP2WaferStatusAbnormal =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA61);

        /// <summary>
        /// RA62 - slot is can not get wafer from cassette
        /// </summary>
        public AlarmPropertyAccessor RA62_SlotCannotGetWaferFromCassette =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA62);

        /// <summary>
        /// RA63 - no wafer in CHA, get wafer from CHA command reject
        /// </summary>
        public AlarmPropertyAccessor RA63_NoWaferInCHAGetReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA63);

        /// <summary>
        /// RA64 - no wafer in CHB, get wafer from CHA command reject
        /// </summary>
        public AlarmPropertyAccessor RA64_NoWaferInCHBGetReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA64);

        /// <summary>
        /// RA65 - no wafer in CT, get wafer from CHA command reject
        /// </summary>
        public AlarmPropertyAccessor RA65_NoWaferInCTGetReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA65);

        /// <summary>
        /// RA66 - no wafer in CB, get wafer from CHA command reject
        /// </summary>
        public AlarmPropertyAccessor RA66_NoWaferInCBGetReject =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA66);

        /// <summary>
        /// RA67 - robot motion error
        /// </summary>
        public AlarmPropertyAccessor RA67_RobotMotionError =>
            GetOrCreateAccessor(EnuRobotAlarmCodes.RA67);

        #endregion Robot报警代码的访问器定义

        private AlarmPropertyAccessor GetOrCreateAccessor(EnuRobotAlarmCodes alarmCode)
        {
            string key = alarmCode.ToString();
            return _cache.GetOrAdd(key, _ =>
            {
                var alarmItem = _provider.GetAlarmItemByCode(alarmCode);
                return alarmItem != null ? new AlarmPropertyAccessor(alarmItem) : null;
            });
        }
    }

    /// <summary>
    /// Chamber报警访问器
    /// </summary>
    public class ChamberAlarmAccessor
    {
        private readonly ChaErrorCodesProvider _provider;
        private readonly ConcurrentDictionary<string, AlarmPropertyAccessor> _cache = new();

        public ChamberAlarmAccessor()
        {
            _provider = ChaErrorCodesProvider.Instance;
        }

        #region Chamber报警代码的访问器定义

        /// <summary>
        /// PAC1 - chamber system status abnormal, command reject
        /// </summary>
        public AlarmPropertyAccessor PAC1_SystemAbnormalReject =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC1);

        /// <summary>
        /// PAC2 - CHX run status or robot run status is busy, command reject
        /// </summary>
        public AlarmPropertyAccessor PAC2_SystemBusyReject =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC2);

        /// <summary>
        /// PAC3 - CHX run status is processing, command reject
        /// </summary>
        public AlarmPropertyAccessor PAC3_RunProcessingReject =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC3);

        /// <summary>
        /// PAC4 - delta pressure about CHX and loadlock out of setpoint
        /// </summary>
        public AlarmPropertyAccessor PAC4_DeltaPressureOutOfSetpoint =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC4);

        /// <summary>
        /// PAC5 - slit door sensor position status abnormal
        /// </summary>
        public AlarmPropertyAccessor PAC5_SlitDoorSensorAbnormal =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC5);

        /// <summary>
        /// PAC6 - slit door sensor failure or system error
        /// </summary>
        public AlarmPropertyAccessor PAC6_SlitDoorSensorFailure =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC6);

        /// <summary>
        /// PAC7 - chamber pressure not at vacuum status, can not open slit door
        /// </summary>
        public AlarmPropertyAccessor PAC7_PressureNotVacuumSlitDoorError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC7);

        /// <summary>
        /// PAC8 - slit door open time out
        /// </summary>
        public AlarmPropertyAccessor PAC8_SlitDoorOpenTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC8);

        /// <summary>
        /// PAC9 - slit door open too fast
        /// </summary>
        public AlarmPropertyAccessor PAC9_SlitDoorOpenTooFast =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC9);

        /// <summary>
        /// PAC10 - lift pin sensor position status abnormal
        /// </summary>
        public AlarmPropertyAccessor PAC10_LiftPinSensorAbnormal =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC10);

        /// <summary>
        /// PAC11 - lift pin sensor show cylinder is between, lift pin motion failure
        /// </summary>
        public AlarmPropertyAccessor PAC11_LiftPinCylinderBetween =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC11);

        /// <summary>
        /// PAC12 - lift pin open time out
        /// </summary>
        public AlarmPropertyAccessor PAC12_LiftPinOpenTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC12);

        /// <summary>
        /// PAC13 - lift pin open too fast
        /// </summary>
        public AlarmPropertyAccessor PAC13_LiftPinOpenTooFast =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC13);

        /// <summary>
        /// PAC14 - lift pin sensor failure or system error
        /// </summary>
        public AlarmPropertyAccessor PAC14_LiftPinSensorFailure =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC14);

        /// <summary>
        /// PAC15 - robot R-axis not at right position, lift pin motion failure
        /// </summary>
        public AlarmPropertyAccessor PAC15_RobotRAxisLiftPinError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC15);

        /// <summary>
        /// PAC16 - robot run status is not idle, lift pin motion failure
        /// </summary>
        public AlarmPropertyAccessor PAC16_RobotNotIdleLiftPinError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC16);

        /// <summary>
        /// PAC17 - robot R-axis not at right position, slit door motion failure
        /// </summary>
        public AlarmPropertyAccessor PAC17_RobotRAxisSlitDoorError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC17);

        /// <summary>
        /// PAC18 - robot run status is not idle, slit door motion failure
        /// </summary>
        public AlarmPropertyAccessor PAC18_RobotNotIdleSlitDoorError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC18);

        /// <summary>
        /// PAC19 - throttle valve position sensor failure or flag position error
        /// </summary>
        public AlarmPropertyAccessor PAC19_ThrottleValveSensorFailure =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC19);

        /// <summary>
        /// PAC20 - throttle valve motion time out
        /// </summary>
        public AlarmPropertyAccessor PAC20_ThrottleValveTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC20);

        /// <summary>
        /// PAC21 - foreline not at vacuum status, can not open ISO valve
        /// </summary>
        public AlarmPropertyAccessor PAC21_ForelineNotVacuumISOError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC21);

        /// <summary>
        /// PAC22 - chamber ISO valve sensor failure or position error
        /// </summary>
        public AlarmPropertyAccessor PAC22_ISOValveSensorFailure =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC22);

        /// <summary>
        /// PAC23 - CV valve open time out
        /// </summary>
        public AlarmPropertyAccessor PAC23_CVValveOpenTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC23);

        /// <summary>
        /// PAC24 - ISO valve not at open position, can not open CM valve
        /// </summary>
        public AlarmPropertyAccessor PAC24_ISONotOpenCMError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC24);

        /// <summary>
        /// PAC25 - chamber no process vacuum, can not open CM valve
        /// </summary>
        public AlarmPropertyAccessor PAC25_NoProcessVacuumCMError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC25);

        /// <summary>
        /// PAC26 - CM valve not at open position, can not open gas valve
        /// </summary>
        public AlarmPropertyAccessor PAC26_CMNotOpenGasError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC26);

        /// <summary>
        /// PAC27 - gas 1 servo time out
        /// </summary>
        public AlarmPropertyAccessor PAC27_Gas1ServoTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC27);

        /// <summary>
        /// PAC28 - gas 2 servo time out
        /// </summary>
        public AlarmPropertyAccessor PAC28_Gas2ServoTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC28);

        /// <summary>
        /// PAC29 - gas 3 servo time out
        /// </summary>
        public AlarmPropertyAccessor PAC29_Gas3ServoTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC29);

        /// <summary>
        /// PAC30 - gas 4 servo time out
        /// </summary>
        public AlarmPropertyAccessor PAC30_Gas4ServoTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC30);

        /// <summary>
        /// PAC31 - gas1 flow fault
        /// </summary>
        public AlarmPropertyAccessor PAC31_Gas1FlowFault =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC31);

        /// <summary>
        /// PAC32 - gas2 flow fault
        /// </summary>
        public AlarmPropertyAccessor PAC32_Gas2FlowFault =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC32);

        /// <summary>
        /// PAC33 - gas3 flow fault
        /// </summary>
        public AlarmPropertyAccessor PAC33_Gas3FlowFault =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC33);

        /// <summary>
        /// PAC34 - gas4 flow fault
        /// </summary>
        public AlarmPropertyAccessor PAC34_Gas4FlowFault =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC34);

        /// <summary>
        /// PAC35 - gas1 flow unstable
        /// </summary>
        public AlarmPropertyAccessor PAC35_Gas1FlowUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC35);

        /// <summary>
        /// PAC36 - gas2 flow unstable
        /// </summary>
        public AlarmPropertyAccessor PAC36_Gas2FlowUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC36);

        /// <summary>
        /// PAC37 - gas3 flow unstable
        /// </summary>
        public AlarmPropertyAccessor PAC37_Gas3FlowUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC37);

        /// <summary>
        /// PAC38 - gas4 flow unstable
        /// </summary>
        public AlarmPropertyAccessor PAC38_Gas4FlowUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC38);

        /// <summary>
        /// PAC39 - CV valve status is no opened, pressure control failure
        /// </summary>
        public AlarmPropertyAccessor PAC39_CVValveNotOpenPressureError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC39);

        /// <summary>
        /// PAC40 - CM valve not at open position, pressure control failure
        /// </summary>
        public AlarmPropertyAccessor PAC40_CMValveNotOpenPressureError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC40);

        /// <summary>
        /// PAC41 - no open gas valve, can not control pressure
        /// </summary>
        public AlarmPropertyAccessor PAC41_NoGasValveOpenPressureError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC41);

        /// <summary>
        /// PAC42 - pressure servo time out
        /// </summary>
        public AlarmPropertyAccessor PAC42_PressureServoTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC42);

        /// <summary>
        /// PAC43 - pressure flow fault
        /// </summary>
        public AlarmPropertyAccessor PAC43_PressureFlowFault =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC43);

        /// <summary>
        /// PAC44 - pressure unstable
        /// </summary>
        public AlarmPropertyAccessor PAC44_PressureUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC44);

        /// <summary>
        /// PAC45 - temperature servo time out
        /// </summary>
        public AlarmPropertyAccessor PAC45_TemperatureServoTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC45);

        /// <summary>
        /// PAC46 - temperature out of setpoint
        /// </summary>
        public AlarmPropertyAccessor PAC46_TemperatureOutOfSetpoint =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC46);

        /// <summary>
        /// PAC47 - temperature unstable
        /// </summary>
        public AlarmPropertyAccessor PAC47_TemperatureUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC47);

        /// <summary>
        /// PAC48 - slit door not close, can not turn on RF
        /// </summary>
        public AlarmPropertyAccessor PAC48_SlitDoorNotCloseRFError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC48);

        /// <summary>
        /// PAC49 - chamber no vacuum, RF off
        /// </summary>
        public AlarmPropertyAccessor PAC49_NoVacuumRFOff =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC49);

        /// <summary>
        /// PAC50 - ISO valve not open, can not turn on RF
        /// </summary>
        public AlarmPropertyAccessor PAC50_ISONotOpenRFError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC50);

        /// <summary>
        /// PAC51 - CM valve not at open position, can not turn on RF
        /// </summary>
        public AlarmPropertyAccessor PAC51_CMNotOpenRFError =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC51);

        /// <summary>
        /// PAC52 - no plasma was detected on CHX left head
        /// </summary>
        public AlarmPropertyAccessor PAC52_NoPlasmaLeftHead =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC52);

        /// <summary>
        /// PAC53 - no plasma was detected on CHX right head
        /// </summary>
        public AlarmPropertyAccessor PAC53_NoPlasmaRightHead =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC53);

        /// <summary>
        /// PAC54 - RF1 power forward to setpoint time out
        /// </summary>
        public AlarmPropertyAccessor PAC54_RF1PowerForwardTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC54);

        /// <summary>
        /// PAC55 - RF1 reflector power out of limit
        /// </summary>
        public AlarmPropertyAccessor PAC55_RF1ReflectorPowerOutOfLimit =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC55);

        /// <summary>
        /// PAC56 - RF2 power forward to setpoint time out
        /// </summary>
        public AlarmPropertyAccessor PAC56_RF2PowerForwardTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC56);

        /// <summary>
        /// PAC57 - RF2 reflector power out of limit
        /// </summary>
        public AlarmPropertyAccessor PAC57_RF2ReflectorPowerOutOfLimit =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC57);

        /// <summary>
        /// PAC58 - RF1 forward power out of setpoint
        /// </summary>
        public AlarmPropertyAccessor PAC58_RF1ForwardPowerOutOfSetpoint =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC58);

        /// <summary>
        /// PAC59 - RF2 forward power out of setpoint
        /// </summary>
        public AlarmPropertyAccessor PAC59_RF2ForwardPowerOutOfSetpoint =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC59);

        /// <summary>
        /// PAC60 - RF1 forward power output unstable
        /// </summary>
        public AlarmPropertyAccessor PAC60_RF1ForwardPowerUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC60);

        /// <summary>
        /// PAC61 - RF2 forward power output unstable
        /// </summary>
        public AlarmPropertyAccessor PAC61_RF2ForwardPowerUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC61);

        /// <summary>
        /// PAC62 - RF off failure
        /// </summary>
        public AlarmPropertyAccessor PAC62_RFOffFailure =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC62);

        /// <summary>
        /// PAC63 - RF1 reflector power output unstable
        /// </summary>
        public AlarmPropertyAccessor PAC63_RF1ReflectorPowerUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC63);

        /// <summary>
        /// PAC64 - RF2 reflector power output unstable
        /// </summary>
        public AlarmPropertyAccessor PAC64_RF2ReflectorPowerUnstable =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC64);

        /// <summary>
        /// PAC65 - Chamber pump down time out
        /// </summary>
        public AlarmPropertyAccessor PAC65_ChamberPumpDownTimeout =>
            GetOrCreateAccessor(EnuChaAlarmCodes.PAC65);

        #endregion Chamber报警代码的访问器定义

        private AlarmPropertyAccessor GetOrCreateAccessor(EnuChaAlarmCodes alarmCode)
        {
            string key = alarmCode.ToString();
            return _cache.GetOrAdd(key, _ =>
            {
                var alarmItem = _provider.GetAlarmItemByCode(alarmCode);
                return alarmItem != null ? new AlarmPropertyAccessor(alarmItem) : null;
            });
        }
    }

    /// <summary>
    /// Shuttle报警访问器
    /// </summary>
    public class ShuttleAlarmAccessor
    {
        private readonly ShuttleErrorCodesProvider _provider;
        private readonly ConcurrentDictionary<string, AlarmPropertyAccessor> _cache = new();

        public ShuttleAlarmAccessor()
        {
            _provider = ShuttleErrorCodesProvider.Instance;
        }

        #region Shuttle报警代码的访问器定义

        /// <summary>
        /// SA1 - Shuttle system status is busy, command reject
        /// </summary>
        public AlarmPropertyAccessor SA1_SystemBusyReject =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA1);

        /// <summary>
        /// SA2 - Shuttle system status is alarm, command reject
        /// </summary>
        public AlarmPropertyAccessor SA2_SystemAlarmReject =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA2);

        /// <summary>
        /// SA3 - Cassette nest move time out
        /// </summary>
        public AlarmPropertyAccessor SA3_CassetteNestMoveTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA3);

        /// <summary>
        /// SA4 - Cassette nest move speed too high
        /// </summary>
        public AlarmPropertyAccessor SA4_CassetteNestSpeedTooHigh =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA4);

        /// <summary>
        /// SA5 - Cassette nest position condition failure
        /// </summary>
        public AlarmPropertyAccessor SA5_CassetteNestPositionFailure =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA5);

        /// <summary>
        /// SA6 - Shuttle move time out
        /// </summary>
        public AlarmPropertyAccessor SA6_ShuttleMoveTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA6);

        /// <summary>
        /// SA7 - Shuttle move too fast
        /// </summary>
        public AlarmPropertyAccessor SA7_ShuttleMoveTooFast =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA7);

        /// <summary>
        /// SA8 - Shuttle up down position condition failure
        /// </summary>
        public AlarmPropertyAccessor SA8_ShuttleUpDownPositionFailure =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA8);

        /// <summary>
        /// SA9 - Shuttle rotate time out
        /// </summary>
        public AlarmPropertyAccessor SA9_ShuttleRotateTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA9);

        /// <summary>
        /// SA10 - Shuttle rotate too fast
        /// </summary>
        public AlarmPropertyAccessor SA10_ShuttleRotateTooFast =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA10);

        /// <summary>
        /// SA11 - Shuttle rotate position condition failure
        /// </summary>
        public AlarmPropertyAccessor SA11_ShuttleRotatePositionFailure =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA11);

        /// <summary>
        /// SA12 - Cassette door open time out
        /// </summary>
        public AlarmPropertyAccessor SA12_CassetteDoorOpenTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA12);

        /// <summary>
        /// SA13 - Cassette door open too fast
        /// </summary>
        public AlarmPropertyAccessor SA13_CassetteDoorOpenTooFast =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA13);

        /// <summary>
        /// SA14 - Cassette door position condition failure
        /// </summary>
        public AlarmPropertyAccessor SA14_CassetteDoorPositionFailure =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA14);

        /// <summary>
        /// SA15 - Cassette door close time out
        /// </summary>
        public AlarmPropertyAccessor SA15_CassetteDoorCloseTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA15);

        /// <summary>
        /// SA16 - Cassette door close too fast
        /// </summary>
        public AlarmPropertyAccessor SA16_CassetteDoorCloseTooFast =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA16);

        /// <summary>
        /// SA17 - Open XV time out
        /// </summary>
        public AlarmPropertyAccessor SA17_OpenXVTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA17);

        /// <summary>
        /// SA18 - Open XV too fast
        /// </summary>
        public AlarmPropertyAccessor SA18_OpenXVTooFast =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA18);

        /// <summary>
        /// SA19 - Close XV time out
        /// </summary>
        public AlarmPropertyAccessor SA19_CloseXVTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA19);

        /// <summary>
        /// SA20 - Shuttle & loadlock pressure delta out of range
        /// </summary>
        public AlarmPropertyAccessor SA20_PressureDeltaOutOfRange =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA20);

        /// <summary>
        /// SA21 - Chamber pressure review is no, pls confirm to continue
        /// </summary>
        public AlarmPropertyAccessor SA21_ChamberPressureReviewNo =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA21);

        /// <summary>
        /// SA22 - Shuttle position unknown, can not motion cassette door
        /// </summary>
        public AlarmPropertyAccessor SA22_PositionUnknownCassetteDoorError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA22);

        /// <summary>
        /// SA23 - Shuttle position unknown, can not swap cassette
        /// </summary>
        public AlarmPropertyAccessor SA23_PositionUnknownSwapCassetteError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA23);

        /// <summary>
        /// SA24 - Cassette nest not at home, shuttle motion failure
        /// </summary>
        public AlarmPropertyAccessor SA24_CassetteNestNotHomeShuttleError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA24);

        /// <summary>
        /// SA25 - Chamber pressure review is no, return cassette reject
        /// </summary>
        public AlarmPropertyAccessor SA25_ChamberPressureReviewReturnReject =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA25);

        /// <summary>
        /// SA26 - Shuttle vacuum switch show foreline is no vacuum, can not open CV
        /// </summary>
        public AlarmPropertyAccessor SA26_ForelineNoVacuumCVError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA26);

        /// <summary>
        /// SA27 - Cassette door is not close, can not open shuttle vacuum valve
        /// </summary>
        public AlarmPropertyAccessor SA27_CassetteDoorNotCloseVacuumError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA27);

        /// <summary>
        /// SA28 - Shuttle pump down time out
        /// </summary>
        public AlarmPropertyAccessor SA28_ShuttlePumpDownTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA28);

        /// <summary>
        /// SA29 - Shuttle backfill time out
        /// </summary>
        public AlarmPropertyAccessor SA29_ShuttleBackfillTimeout =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA29);

        /// <summary>
        /// SA30 - CHA trigger status is alarm, can not pump down loadlock
        /// </summary>
        public AlarmPropertyAccessor SA30_CHATriggerAlarmPumpDownError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA30);

        /// <summary>
        /// SA31 - CHB trigger status is alarm, can not pump down loadlock
        /// </summary>
        public AlarmPropertyAccessor SA31_CHBTriggerAlarmPumpDownError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA31);

        /// <summary>
        /// SA32 - CHA run status is not at idle status, can not pump down loadlock
        /// </summary>
        public AlarmPropertyAccessor SA32_CHANotIdlePumpDownError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA32);

        /// <summary>
        /// SA33 - CHB run status is not at idle status, can not pump down loadlock
        /// </summary>
        public AlarmPropertyAccessor SA33_CHBNotIdlePumpDownError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA33);

        /// <summary>
        /// SA34 - CHA/B pressure>setpoint, and slit door is close, can not pump down loadlock
        /// </summary>
        public AlarmPropertyAccessor SA34_CHABPressureHighPumpDownError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA34);

        /// <summary>
        /// SA35 - CHA pressure>setpoint, and slit door is close, can not pump down loadlock
        /// </summary>
        public AlarmPropertyAccessor SA35_CHAPressureHighPumpDownError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA35);

        /// <summary>
        /// SA36 - CHB pressure>setpoint, and slit door is close, can not pump down loadlock
        /// </summary>
        public AlarmPropertyAccessor SA36_CHBPressureHighPumpDownError =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA36);

        /// <summary>
        /// SA37 - System trigger status is alarm, command reject
        /// </summary>
        public AlarmPropertyAccessor SA37_SystemTriggerAlarmReject =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA37);

        /// <summary>
        /// SA38 - Loadlock run status is not idle, command reject
        /// </summary>
        public AlarmPropertyAccessor SA38_LoadlockNotIdleReject =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA38);

        /// <summary>
        /// SA39 - Shuttle pressure is not at ATM status, backfill loadlock failure
        /// </summary>
        public AlarmPropertyAccessor SA39_ShuttlePressureNotATMBackfillFailure =>
            GetOrCreateAccessor(EnuShuttleAlarmCodes.SA39);

        #endregion Shuttle报警代码的访问器定义

        private AlarmPropertyAccessor GetOrCreateAccessor(EnuShuttleAlarmCodes alarmCode)
        {
            string key = alarmCode.ToString();
            return _cache.GetOrAdd(key, _ =>
            {
                var alarmItem = _provider.GetAlarmItemByCode(alarmCode);
                return alarmItem != null ? new AlarmPropertyAccessor(alarmItem) : null;
            });
        }
    }

    #endregion 报警访问器

    #region 配置访问器

    /// <summary>
    /// Robot配置访问器
    /// </summary>
    public class RobotConfigureAccessor
    {
        private readonly RobotConfigureSettingsProvider _settingsProvider;
        private readonly RobotPositionParametersProvider _positionProvider;
        private readonly ConcurrentDictionary<string, ConfigPropertyAccessor> _cache = new();

        public RobotConfigureAccessor()
        {
            _settingsProvider = RobotConfigureSettingsProvider.Instance;
            _positionProvider = RobotPositionParametersProvider.Instance;
        }

        #region Robot配置参数访问器

        /// <summary>
        /// RPS1 - 机器人旋转速度
        /// </summary>
        public ConfigPropertyAccessor RPS1_RobotRotateSpeed =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS1);

        /// <summary>
        /// RPS2 - 机器人伸展速度
        /// </summary>
        public ConfigPropertyAccessor RPS2_RobotExtendSpeed =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS2);

        /// <summary>
        /// RPS3 - 机器人上下速度
        /// </summary>
        public ConfigPropertyAccessor RPS3_RobotUpDownSpeed =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS3);

        /// <summary>
        /// RPS4 - 机器人慢速移动
        /// </summary>
        public ConfigPropertyAccessor RPS4_RobotMoveSlowly =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS4);

        /// <summary>
        /// RPS5 - T轴慢速移动速度
        /// </summary>
        public ConfigPropertyAccessor RPS5_TAxisMoveSpeedSlowly =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS5);

        /// <summary>
        /// RPS6 - R轴慢速移动速度
        /// </summary>
        public ConfigPropertyAccessor RPS6_RAxisMoveSpeedSlowly =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS6);

        /// <summary>
        /// RPS7 - Z轴慢速移动速度
        /// </summary>
        public ConfigPropertyAccessor RPS7_ZAxisMoveSpeedSlowly =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS7);

        /// <summary>
        /// RPS8 - 机器人旋转超时时间
        /// </summary>
        public ConfigPropertyAccessor RPS8_RobotRotateTimeout =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS8);

        /// <summary>
        /// RPS9 - 机器人伸展超时时间
        /// </summary>
        public ConfigPropertyAccessor RPS9_RobotExtendTimeout =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS9);

        /// <summary>
        /// RPS10 - 机器人上下超时时间
        /// </summary>
        public ConfigPropertyAccessor RPS10_RobotUpDownTimeout =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS10);

        /// <summary>
        /// RPS11 - 插销搜索超时时间
        /// </summary>
        public ConfigPropertyAccessor RPS11_PinSearchTimeout =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS11);

        /// <summary>
        /// RPS12 - 插销搜索速度
        /// </summary>
        public ConfigPropertyAccessor RPS12_PinSearchSpeed =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS12);

        /// <summary>
        /// RPS13 - 插销搜索步进
        /// </summary>
        public ConfigPropertyAccessor RPS13_PinSearchStep =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS13);

        /// <summary>
        /// RPS14 - 插销搜索重试次数
        /// </summary>
        public ConfigPropertyAccessor RPS14_PinSearchRetryCount =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS14);

        /// <summary>
        /// RPS15 - T轴偏差为R轴零位
        /// </summary>
        public ConfigPropertyAccessor RPS15_TAxisDeviationForRAxisZero =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS15);

        /// <summary>
        /// RPS16 - Z轴步进偏差用于反馈真空
        /// </summary>
        public ConfigPropertyAccessor RPS16_ZAxisStepDeviationForVacuumFeedback =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS16);

        /// <summary>
        /// RPS17 - Z轴步进偏差用于穿梭无真空
        /// </summary>
        public ConfigPropertyAccessor RPS17_ZAxisStepDeviationForShuttleNoVacuum =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS17);

        /// <summary>
        /// RPS18 - 4/5寸晶圆Z轴位置
        /// </summary>
        public ConfigPropertyAccessor RPS18_Wafer4_5InchZAxisPosition =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS18);

        /// <summary>
        /// RPS19 - 6寸晶圆Z轴位置
        /// </summary>
        public ConfigPropertyAccessor RPS19_Wafer6InchZAxisPosition =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS19);

        /// <summary>
        /// RPS20 - 8寸晶圆Z轴位置
        /// </summary>
        public ConfigPropertyAccessor RPS20_Wafer8InchZAxisPosition =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS20);

        /// <summary>
        /// RPS21 - 4寸晶圆盒Z轴取放晶圆增量
        /// </summary>
        public ConfigPropertyAccessor RPS21_Wafer4InchCassetteZAxisIncrement =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS21);

        /// <summary>
        /// RPS22 - 6寸晶圆盒Z轴取放晶圆增量
        /// </summary>
        public ConfigPropertyAccessor RPS22_Wafer6InchCassetteZAxisIncrement =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS22);

        /// <summary>
        /// RPS23 - 8寸晶圆盒Z轴取放晶圆增量
        /// </summary>
        public ConfigPropertyAccessor RPS23_Wafer8InchCassetteZAxisIncrement =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS23);

        /// <summary>
        /// RPS24 - 冷却腔Z轴取放晶圆增量
        /// </summary>
        public ConfigPropertyAccessor RPS24_CoolingChamberZAxisIncrement =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS24);

        /// <summary>
        /// RPS25 - 插销搜索最大偏差值
        /// </summary>
        public ConfigPropertyAccessor RPS25_PinSearchMaxDeviation =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS25);

        /// <summary>
        /// RPS26 - 晶圆实际状态检查
        /// </summary>
        public ConfigPropertyAccessor RPS26_WaferActualStatusCheck =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS26);

        /// <summary>
        /// RPS27 - 机器人旋转时Z轴高度
        /// </summary>
        public ConfigPropertyAccessor RPS27_ZAxisHeightForRobotRotation =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS27);

        /// <summary>
        /// RPS28 - 插销搜索最低步进
        /// </summary>
        public ConfigPropertyAccessor RPS28_PinSearchMinStep =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS28);

        /// <summary>
        /// RPS29 - 腔室压力检查
        /// </summary>
        public ConfigPropertyAccessor RPS29_ChamberPressureCheck =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS29);

        /// <summary>
        /// RPS30 - 插销搜索Z轴高度
        /// </summary>
        public ConfigPropertyAccessor RPS30_ZAxisHeightForPinSearch =>
            GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes.RPS30);

        #endregion Robot配置参数访问器

        #region Robot位置参数访问器

        /// <summary>
        /// RP1 - T轴smooth到CHA位置
        /// </summary>
        public ConfigPropertyAccessor RP1_TAxisSmoothToCHA =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP1);

        /// <summary>
        /// RP2 - T轴smooth到CHB位置
        /// </summary>
        public ConfigPropertyAccessor RP2_TAxisSmoothToCHB =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP2);

        /// <summary>
        /// RP3 - T轴smooth到冷却腔位置
        /// </summary>
        public ConfigPropertyAccessor RP3_TAxisSmoothToCoolingChamber =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP3);

        /// <summary>
        /// RP4 - T轴smooth到卡匣位置
        /// </summary>
        public ConfigPropertyAccessor RP4_TAxisSmoothToCassette =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP4);

        /// <summary>
        /// RP5 - T轴nose到CHA位置
        /// </summary>
        public ConfigPropertyAccessor RP5_TAxisNoseToCHA =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP5);

        /// <summary>
        /// RP6 - T轴nose到CHB位置
        /// </summary>
        public ConfigPropertyAccessor RP6_TAxisNoseToCHB =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP6);

        /// <summary>
        /// RP7 - T轴nose到冷却腔位置
        /// </summary>
        public ConfigPropertyAccessor RP7_TAxisNoseToCoolingChamber =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP7);

        /// <summary>
        /// RP8 - T轴nose到卡匣位置
        /// </summary>
        public ConfigPropertyAccessor RP8_TAxisNoseToCassette =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP8);

        /// <summary>
        /// RP9 - T轴零位
        /// </summary>
        public ConfigPropertyAccessor RP9_TAxisZero =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP9);

        /// <summary>
        /// RP10 - R轴smooth伸展面向CHA位置
        /// </summary>
        public ConfigPropertyAccessor RP10_RAxisSmoothExtendFaceToCHA =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP10);

        /// <summary>
        /// RP11 - R轴smooth伸展面向CHB位置
        /// </summary>
        public ConfigPropertyAccessor RP11_RAxisSmoothExtendFaceToCHB =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP11);

        /// <summary>
        /// RP12 - R轴nose伸展面向CHA位置
        /// </summary>
        public ConfigPropertyAccessor RP12_RAxisNoseExtendFaceToCHA =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP12);

        /// <summary>
        /// RP13 - R轴nose伸展面向CHB位置
        /// </summary>
        public ConfigPropertyAccessor RP13_RAxisNoseExtendFaceToCHB =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP13);

        /// <summary>
        /// RP14 - R轴smooth面向冷却腔并伸展位置
        /// </summary>
        public ConfigPropertyAccessor RP14_RAxisSmoothFaceToCoolingChamberAndExtend =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP14);

        /// <summary>
        /// RP15 - R轴nose伸展面向冷却腔位置
        /// </summary>
        public ConfigPropertyAccessor RP15_RAxisNoseExtendFaceToCoolingChamber =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP15);

        /// <summary>
        /// RP16 - R轴smooth面向卡匣并伸展位置
        /// </summary>
        public ConfigPropertyAccessor RP16_RAxisSmoothFaceToCassetteAndExtend =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP16);

        /// <summary>
        /// RP17 - R轴nose面向卡匣并伸展位置
        /// </summary>
        public ConfigPropertyAccessor RP17_RAxisNoseFaceToCassetteAndExtend =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP17);

        /// <summary>
        /// RP18 - R轴零位
        /// </summary>
        public ConfigPropertyAccessor RP18_RAxisZeroPosition =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP18);

        /// <summary>
        /// RP19 - Z轴smooth到CHA高度
        /// </summary>
        public ConfigPropertyAccessor RP19_ZAxisHeightAtSmoothToCHA =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP19);

        /// <summary>
        /// RP20 - Z轴smooth到CHB高度
        /// </summary>
        public ConfigPropertyAccessor RP20_ZAxisHeightAtSmoothToCHB =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP20);

        /// <summary>
        /// RP21 - Z轴smooth到CT高度
        /// </summary>
        public ConfigPropertyAccessor RP21_ZAxisHeightAtSmoothToCT =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP21);

        /// <summary>
        /// RP22 - Z轴smooth到CB高度
        /// </summary>
        public ConfigPropertyAccessor RP22_ZAxisHeightAtSmoothToCB =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP22);

        /// <summary>
        /// RP23 - Z轴nose到CHA高度
        /// </summary>
        public ConfigPropertyAccessor RP23_ZAxisHeightAtNoseToCHA =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP23);

        /// <summary>
        /// RP24 - Z轴nose到CHB高度
        /// </summary>
        public ConfigPropertyAccessor RP24_ZAxisHeightAtNoseToCHB =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP24);

        /// <summary>
        /// RP25 - Z轴nose到CT取晶圆高度
        /// </summary>
        public ConfigPropertyAccessor RP25_ZAxisHeightAtNoseToCTGet =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP25);

        /// <summary>
        /// RP26 - Z轴nose到CB取晶圆高度
        /// </summary>
        public ConfigPropertyAccessor RP26_ZAxisHeightAtNoseToCBGet =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP26);

        /// <summary>
        /// RP27 - Z轴零位
        /// </summary>
        public ConfigPropertyAccessor RP27_ZAxisZeroPosition =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP27);

        /// <summary>
        /// RP28 - Z轴插销搜索高度
        /// </summary>
        public ConfigPropertyAccessor RP28_ZAxisHeightToPinSearch =>
            GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes.RP28);

        #endregion Robot位置参数访问器

        private ConfigPropertyAccessor GetOrCreateSettingAccessor(EnuRobotConfigureSettingCodes enumValue)
        {
            string key = $"Setting_{enumValue}";
            return _cache.GetOrAdd(key, _ =>
            {
                try
                {
                    int value = _settingsProvider.GetSettingValue(enumValue);
                    var setting = new ConfigureSetting
                    {
                        Code = enumValue.ToString(),
                        Value = value,
                        Description = enumValue.GetDescription(),
                        Unit = "step" // 默认单位
                    };
                    return new ConfigPropertyAccessor(setting);
                }
                catch
                {
                    return null;
                }
            });
        }

        /// <summary>
        /// 使用EnuRobotPositionParameterCodes枚举获取或创建机器人位置参数访问器
        /// </summary>
        /// <param name="enumValue">位置参数枚举代码</param>
        /// <returns></returns>
        private ConfigPropertyAccessor GetOrCreatePositionAccessor(EnuRobotPositionParameterCodes enumValue)
        {
            string key = $"Position_{enumValue}";
            return _cache.GetOrAdd(key, _ =>
            {
                try
                {
                    int value = _positionProvider.GetParameterValue(enumValue);
                    string code = enumValue.ToString();
                    var setting = new ConfigureSetting
                    {
                        Code = code,
                        Value = value,
                        Description = enumValue.GetDescription(),
                        Unit = "step",
                        AxisType = GetAxisTypeForRPInternal(enumValue)
                    };
                    return new ConfigPropertyAccessor(setting);
                }
                catch
                {
                    return null;
                }
            });
        }

        /// <summary>
        /// 获取RP参数对应的轴类型
        /// </summary>
        private int GetAxisTypeForRPInternal(EnuRobotPositionParameterCodes enumCode)
        {
            return enumCode switch
            {
                EnuRobotPositionParameterCodes.RP1 or EnuRobotPositionParameterCodes.RP2 or
                EnuRobotPositionParameterCodes.RP3 or EnuRobotPositionParameterCodes.RP4 or
                EnuRobotPositionParameterCodes.RP5 or EnuRobotPositionParameterCodes.RP6 or
                EnuRobotPositionParameterCodes.RP7 or EnuRobotPositionParameterCodes.RP8 or
                EnuRobotPositionParameterCodes.RP9 => 1, // T轴

                EnuRobotPositionParameterCodes.RP10 or EnuRobotPositionParameterCodes.RP11 or
                EnuRobotPositionParameterCodes.RP12 or EnuRobotPositionParameterCodes.RP13 or
                EnuRobotPositionParameterCodes.RP14 or EnuRobotPositionParameterCodes.RP15 or
                EnuRobotPositionParameterCodes.RP16 or EnuRobotPositionParameterCodes.RP17 or
                EnuRobotPositionParameterCodes.RP18 => 2, // R轴

                EnuRobotPositionParameterCodes.RP19 or EnuRobotPositionParameterCodes.RP20 or
                EnuRobotPositionParameterCodes.RP21 or EnuRobotPositionParameterCodes.RP22 or
                EnuRobotPositionParameterCodes.RP23 or EnuRobotPositionParameterCodes.RP24 or
                EnuRobotPositionParameterCodes.RP25 or EnuRobotPositionParameterCodes.RP26 or
                EnuRobotPositionParameterCodes.RP27 or EnuRobotPositionParameterCodes.RP28 => 3, // Z轴

                _ => 0 // 未知
            };
        }
    }

    /// <summary>
    /// ChamberA配置访问器 - 专门使用EnuChaConfigParameterCodes枚举
    /// </summary>
    public class ChamberAConfigureAccessor
    {
        private readonly ChaConfigParametersProvider _provider;
        private readonly ConcurrentDictionary<string, ConfigPropertyAccessor> _cache = new();

        public ChamberAConfigureAccessor()
        {
            _provider = ChaConfigParametersProvider.Instance;
        }

        #region Chamber配置参数访问器【ChamberA、ChamberB _provider虽然已经区分开了，但是GetOrCreateAccessor 参数用的都是ChamberA的枚举类型，ChamberB枚举类型没有用到，虽然2个枚举几乎是一样的】

        /// <summary>
        /// PPS1 - 狭缝门运动最小时间
        /// </summary>
        public ConfigPropertyAccessor PPS1_SlitDoorMotionMinTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS1);

        /// <summary>
        /// PPS2 - 狭缝门运动最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS2_SlitDoorMotionMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS2);

        /// <summary>
        /// PPS3 - 升降销运动最小时间
        /// </summary>
        public ConfigPropertyAccessor PPS3_LiftPinMotionMinTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS3);

        /// <summary>
        /// PPS4 - 升降销运动最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS4_LiftPinMotionMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS4);

        /// <summary>
        /// PPS5 - 节流阀运动最小时间
        /// </summary>
        public ConfigPropertyAccessor PPS5_ThrottleValveMotionMinTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS5);

        /// <summary>
        /// PPS6 - 节流阀运动最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS6_ThrottleValveMotionMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS6);

        /// <summary>
        /// PPS7 - 腔室抽真空最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS7_ChamberPumpDownMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS7);

        /// <summary>
        /// PPS8 - 腔室回填最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS8_ChamberBackfillMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS8);

        /// <summary>
        /// PPS9 - 腔室真空压力设定点
        /// </summary>
        public ConfigPropertyAccessor PPS9_ChamberVacuumPressureSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS9);

        /// <summary>
        /// PPS10 - 腔室大气压力设定点
        /// </summary>
        public ConfigPropertyAccessor PPS10_ChamberAtmPressureSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS10);

        /// <summary>
        /// PPS11 - 腔室压力偏差
        /// </summary>
        public ConfigPropertyAccessor PPS11_ChamberPressureDeviation =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS11);

        /// <summary>
        /// PPS12 - 腔室传输压力
        /// </summary>
        public ConfigPropertyAccessor PPS12_ChamberTransferPressure =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS12);

        /// <summary>
        /// PPS13 - 气体1流量设定点
        /// </summary>
        public ConfigPropertyAccessor PPS13_Gas1FlowSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS13);

        /// <summary>
        /// PPS14 - 气体2流量设定点
        /// </summary>
        public ConfigPropertyAccessor PPS14_Gas2FlowSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS14);

        /// <summary>
        /// PPS15 - 气体3流量设定点
        /// </summary>
        public ConfigPropertyAccessor PPS15_Gas3FlowSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS15);

        /// <summary>
        /// PPS16 - 气体4流量设定点
        /// </summary>
        public ConfigPropertyAccessor PPS16_Gas4FlowSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS16);

        /// <summary>
        /// PPS17 - RF频率最小值
        /// </summary>
        public ConfigPropertyAccessor PPS17_RfFrequencyMin =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS17);

        /// <summary>
        /// PPS18 - RF频率最大值
        /// </summary>
        public ConfigPropertyAccessor PPS18_RfFrequencyMax =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS18);

        /// <summary>
        /// PPS19 - RF1功率设定点
        /// </summary>
        public ConfigPropertyAccessor PPS19_Rf1PowerSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS19);

        /// <summary>
        /// PPS20 - RF2功率设定点
        /// </summary>
        public ConfigPropertyAccessor PPS20_Rf2PowerSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS20);

        /// <summary>
        /// PPS21 - RF1反射功率限制
        /// </summary>
        public ConfigPropertyAccessor PPS21_Rf1ReflectedPowerLimit =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS21);

        /// <summary>
        /// PPS22 - RF2反射功率限制
        /// </summary>
        public ConfigPropertyAccessor PPS22_Rf2ReflectedPowerLimit =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS22);

        /// <summary>
        /// PPS23 - 温度设定点
        /// </summary>
        public ConfigPropertyAccessor PPS23_TemperatureSetpoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS23);

        /// <summary>
        /// PPS24 - 温度偏差
        /// </summary>
        public ConfigPropertyAccessor PPS24_TemperatureDeviation =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS24);

        /// <summary>
        /// PPS25 - 加热器功率限制
        /// </summary>
        public ConfigPropertyAccessor PPS25_HeaterPowerLimit =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS25);

        /// <summary>
        /// PPS26 - 压力控制控制线(设定点)
        /// </summary>
        public ConfigPropertyAccessor PPS26_PressureControlLine =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS26);

        /// <summary>
        /// PPS27 - 压力控制超出控制线点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS27_PressureControlOutOfLinePoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS27);

        /// <summary>
        /// PPS28 - 温度伺服最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS28_TemperatureServoMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS28);

        /// <summary>
        /// PPS29 - 低温度偏差
        /// </summary>
        public ConfigPropertyAccessor PPS29_LowTemperatureDeviation =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS29);

        /// <summary>
        /// PPS30 - 高温度偏差
        /// </summary>
        public ConfigPropertyAccessor PPS30_HighTemperatureDeviation =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS30);

        /// <summary>
        /// PPS31 - 晶圆接收腔室温度偏差
        /// </summary>
        public ConfigPropertyAccessor PPS31_ChamberTemperatureDeviationForWaferReceive =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS31);

        /// <summary>
        /// PPS32 - 腔室温度控制线
        /// </summary>
        public ConfigPropertyAccessor PPS32_ChamberTemperatureControlLine =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS32);

        /// <summary>
        /// PPS33 - 温度超出控制线点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS33_TemperatureOutOfControlLinePoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS33);

        /// <summary>
        /// PPS34 - 节流阀运动最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS34_ThrottleValveMotionMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS34);

        /// <summary>
        /// PPS35 - RF前向功率偏差故障
        /// </summary>
        public ConfigPropertyAccessor PPS35_RfForwardPowerDeviationFault =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS35);

        /// <summary>
        /// PPS36 - RF反射器超出限制
        /// </summary>
        public ConfigPropertyAccessor PPS36_RfReflectorOutOfLimit =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS36);

        /// <summary>
        /// PPS37 - RF前向功率超出控制线点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS37_RfForwardPowerOutOfControlLinePoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS37);

        /// <summary>
        /// PPS38 - RF反射功率超出控制线点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS38_RfReflectorPowerOutOfControlLinePoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS38);

        /// <summary>
        /// PPS39 - RF前向功率控制线
        /// </summary>
        public ConfigPropertyAccessor PPS39_RfForwardPowerControlLine =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS39);

        /// <summary>
        /// PPS40 - RF反射功率控制线
        /// </summary>
        public ConfigPropertyAccessor PPS40_RfReflectorPowerControlLine =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS40);

        /// <summary>
        /// PPS41 - 等离子体开启检查
        /// </summary>
        public ConfigPropertyAccessor PPS41_PlasmaOnCheck =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS41);

        /// <summary>
        /// PPS42 - RF稳定最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS42_RfStableMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS42);

        /// <summary>
        /// PPS43 - 等离子体开/关控制线跳闸点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS43_PlasmaOnOffTrippedControlLinePoint =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS43);

        /// <summary>
        /// PPS44 - 工艺腔抽真空最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS44_ProcessChamberPumpDownMaxTime =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS44);

        /// <summary>
        /// PPS45 - 活动腔室
        /// </summary>
        public ConfigPropertyAccessor PPS45_ActiveChamber =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS45);

        /// <summary>
        /// PPS46 - 工艺腔抽真空压力偏差
        /// </summary>
        public ConfigPropertyAccessor PPS46_ProcessChamberPumpingDownPressureDeviation =>
            GetOrCreateAccessor(EnuChaConfigParameterCodes.PPS46);

        #endregion Chamber配置参数访问器【ChamberA、ChamberB _provider虽然已经区分开了，但是GetOrCreateAccessor 参数用的都是ChamberA的枚举类型，ChamberB枚举类型没有用到，虽然2个枚举几乎是一样的】

        private ConfigPropertyAccessor GetOrCreateAccessor(EnuChaConfigParameterCodes enumValue)
        {
            string key = enumValue.ToString();
            return _cache.GetOrAdd(key, _ =>
            {
                try
                {
                    // 根据参数类型获取相应的值
                    object value = GetParameterValue(enumValue);
                    var setting = new ConfigureSetting
                    {
                        Code = enumValue.ToString(),
                        Value = value,
                        Description = enumValue.GetDescription(),
                        Unit = GetUnitForParameter(enumValue)
                    };
                    return new ConfigPropertyAccessor(setting);
                }
                catch
                {
                    return null;
                }
            });
        }

        /// <summary>
        /// 根据参数类型获取相应的值
        /// </summary>
        /// <param name="enumValue">参数枚举</param>
        /// <returns>参数值</returns>
        private object GetParameterValue(EnuChaConfigParameterCodes enumValue)
        {
            // 需要使用double类型的参数
            switch (enumValue)
            {
                case EnuChaConfigParameterCodes.PPS1:  // 狭缝门运动最小时间
                case EnuChaConfigParameterCodes.PPS2:  // 狭缝门运动最大时间
                case EnuChaConfigParameterCodes.PPS3:  // 升降针运动最小时间
                case EnuChaConfigParameterCodes.PPS4:  // 升降针运动最大时间
                case EnuChaConfigParameterCodes.PPS46: // 工艺腔室抽真空压力偏差
                    return _provider.GetDoubleSettingValue(enumValue);

                default:
                    return _provider.GetIntSettingValue(enumValue);
            }
        }

        private string GetUnitForParameter(EnuChaConfigParameterCodes parameter)
        {
            return parameter switch
            {
                EnuChaConfigParameterCodes.PPS1 or EnuChaConfigParameterCodes.PPS2 or
                EnuChaConfigParameterCodes.PPS3 or EnuChaConfigParameterCodes.PPS4 or
                EnuChaConfigParameterCodes.PPS5 or EnuChaConfigParameterCodes.PPS6 or
                EnuChaConfigParameterCodes.PPS7 or EnuChaConfigParameterCodes.PPS8 => "sec",

                EnuChaConfigParameterCodes.PPS9 or EnuChaConfigParameterCodes.PPS10 or
                EnuChaConfigParameterCodes.PPS11 or EnuChaConfigParameterCodes.PPS12 => "mTorr",

                EnuChaConfigParameterCodes.PPS13 or EnuChaConfigParameterCodes.PPS14 or
                EnuChaConfigParameterCodes.PPS15 or EnuChaConfigParameterCodes.PPS16 => "sccm",

                EnuChaConfigParameterCodes.PPS17 or EnuChaConfigParameterCodes.PPS18 => "MHz",

                EnuChaConfigParameterCodes.PPS19 or EnuChaConfigParameterCodes.PPS20 or
                EnuChaConfigParameterCodes.PPS21 or EnuChaConfigParameterCodes.PPS22 or
                EnuChaConfigParameterCodes.PPS25 => "W",

                EnuChaConfigParameterCodes.PPS23 or EnuChaConfigParameterCodes.PPS24 => "°C",

                _ => ""
            };
        }
    }

    /// <summary>
    /// ChamberB配置访问器 - 专门使用EnuChbConfigParameterCodes枚举
    /// </summary>
    public class ChamberBConfigureAccessor
    {
        private readonly ChbConfigParametersProvider _provider;
        private readonly ConcurrentDictionary<string, ConfigPropertyAccessor> _cache = new();

        public ChamberBConfigureAccessor()
        {
            _provider = ChbConfigParametersProvider.Instance;
        }

        #region ChamberB配置参数访问器

        /// <summary>
        /// PPS1 - 狭缝门运动最小时间
        /// </summary>
        public ConfigPropertyAccessor PPS1_SlitDoorMotionMinTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS1);

        /// <summary>
        /// PPS2 - 狭缝门运动最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS2_SlitDoorMotionMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS2);

        /// <summary>
        /// PPS3 - 升降销运动最小时间
        /// </summary>
        public ConfigPropertyAccessor PPS3_LiftPinMotionMinTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS3);

        /// <summary>
        /// PPS4 - 升降销运动最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS4_LiftPinMotionMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS4);

        /// <summary>
        /// PPS5 - 节流阀运动最小时间
        /// </summary>
        public ConfigPropertyAccessor PPS5_ThrottleValveMotionMinTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS5);

        /// <summary>
        /// PPS6 - 节流阀运动最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS6_ThrottleValveMotionMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS6);

        /// <summary>
        /// PPS7 - 腔室抽真空最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS7_ChamberPumpDownMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS7);

        /// <summary>
        /// PPS8 - 腔室回填最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS8_ChamberBackfillMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS8);

        /// <summary>
        /// PPS9 - 腔室真空压力设定点
        /// </summary>
        public ConfigPropertyAccessor PPS9_ChamberVacuumPressureSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS9);

        /// <summary>
        /// PPS10 - 腔室大气压力设定点
        /// </summary>
        public ConfigPropertyAccessor PPS10_ChamberAtmPressureSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS10);

        /// <summary>
        /// PPS11 - 腔室压力偏差
        /// </summary>
        public ConfigPropertyAccessor PPS11_ChamberPressureDeviation =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS11);

        /// <summary>
        /// PPS12 - 腔室传输压力
        /// </summary>
        public ConfigPropertyAccessor PPS12_ChamberTransferPressure =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS12);

        /// <summary>
        /// PPS13 - 气体1流量设定点
        /// </summary>
        public ConfigPropertyAccessor PPS13_Gas1FlowSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS13);

        /// <summary>
        /// PPS14 - 气体2流量设定点
        /// </summary>
        public ConfigPropertyAccessor PPS14_Gas2FlowSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS14);

        /// <summary>
        /// PPS15 - 气体3流量设定点
        /// </summary>
        public ConfigPropertyAccessor PPS15_Gas3FlowSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS15);

        /// <summary>
        /// PPS16 - 气体4流量设定点
        /// </summary>
        public ConfigPropertyAccessor PPS16_Gas4FlowSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS16);

        /// <summary>
        /// PPS17 - RF频率最小值
        /// </summary>
        public ConfigPropertyAccessor PPS17_RfFrequencyMin =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS17);

        /// <summary>
        /// PPS18 - RF频率最大值
        /// </summary>
        public ConfigPropertyAccessor PPS18_RfFrequencyMax =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS18);

        /// <summary>
        /// PPS19 - RF1功率设定点
        /// </summary>
        public ConfigPropertyAccessor PPS19_Rf1PowerSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS19);

        /// <summary>
        /// PPS20 - RF2功率设定点
        /// </summary>
        public ConfigPropertyAccessor PPS20_Rf2PowerSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS20);

        /// <summary>
        /// PPS21 - RF1反射功率限制
        /// </summary>
        public ConfigPropertyAccessor PPS21_Rf1ReflectedPowerLimit =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS21);

        /// <summary>
        /// PPS22 - RF2反射功率限制
        /// </summary>
        public ConfigPropertyAccessor PPS22_Rf2ReflectedPowerLimit =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS22);

        /// <summary>
        /// PPS23 - 温度设定点
        /// </summary>
        public ConfigPropertyAccessor PPS23_TemperatureSetpoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS23);

        /// <summary>
        /// PPS24 - 温度偏差
        /// </summary>
        public ConfigPropertyAccessor PPS24_TemperatureDeviation =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS24);

        /// <summary>
        /// PPS25 - 加热器功率限制
        /// </summary>
        public ConfigPropertyAccessor PPS25_HeaterPowerLimit =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS25);

        /// <summary>
        /// PPS26 - 压力控制控制线(设定点)
        /// </summary>
        public ConfigPropertyAccessor PPS26_PressureControlLine =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS26);

        /// <summary>
        /// PPS27 - 压力控制超出控制线点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS27_PressureControlOutOfLinePoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS27);

        /// <summary>
        /// PPS28 - 温度伺服最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS28_TemperatureServoMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS28);

        /// <summary>
        /// PPS29 - 低温度偏差
        /// </summary>
        public ConfigPropertyAccessor PPS29_LowTemperatureDeviation =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS29);

        /// <summary>
        /// PPS30 - 高温度偏差
        /// </summary>
        public ConfigPropertyAccessor PPS30_HighTemperatureDeviation =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS30);

        /// <summary>
        /// PPS31 - 晶圆接收腔室温度偏差
        /// </summary>
        public ConfigPropertyAccessor PPS31_ChamberTemperatureDeviationForWaferReceive =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS31);

        /// <summary>
        /// PPS32 - 腔室温度控制线
        /// </summary>
        public ConfigPropertyAccessor PPS32_ChamberTemperatureControlLine =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS32);

        /// <summary>
        /// PPS33 - 温度超出控制线点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS33_TemperatureOutOfControlLinePoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS33);

        /// <summary>
        /// PPS34 - 节流阀运动最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS34_ThrottleValveMotionMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS34);

        /// <summary>
        /// PPS35 - RF前向功率偏差故障
        /// </summary>
        public ConfigPropertyAccessor PPS35_RfForwardPowerDeviationFault =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS35);

        /// <summary>
        /// PPS36 - RF反射器超出限制
        /// </summary>
        public ConfigPropertyAccessor PPS36_RfReflectorOutOfLimit =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS36);

        /// <summary>
        /// PPS37 - RF前向功率超出控制线点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS37_RfForwardPowerOutOfControlLinePoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS37);

        /// <summary>
        /// PPS38 - RF反射功率超出控制线点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS38_RfReflectorPowerOutOfControlLinePoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS38);

        /// <summary>
        /// PPS39 - RF前向功率控制线
        /// </summary>
        public ConfigPropertyAccessor PPS39_RfForwardPowerControlLine =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS39);

        /// <summary>
        /// PPS40 - RF反射功率控制线
        /// </summary>
        public ConfigPropertyAccessor PPS40_RfReflectorPowerControlLine =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS40);

        /// <summary>
        /// PPS41 - 等离子体开启检查
        /// </summary>
        public ConfigPropertyAccessor PPS41_PlasmaOnCheck =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS41);

        /// <summary>
        /// PPS42 - RF稳定最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS42_RfStableMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS42);

        /// <summary>
        /// PPS43 - 等离子体开/关控制线跳闸点数/10点
        /// </summary>
        public ConfigPropertyAccessor PPS43_PlasmaOnOffTrippedControlLinePoint =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS43);

        /// <summary>
        /// PPS44 - 工艺腔抽真空最大时间
        /// </summary>
        public ConfigPropertyAccessor PPS44_ProcessChamberPumpDownMaxTime =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS44);

        /// <summary>
        /// PPS45 - 活动腔室
        /// </summary>
        public ConfigPropertyAccessor PPS45_ActiveChamber =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS45);

        /// <summary>
        /// PPS46 - 工艺腔抽真空压力偏差
        /// </summary>
        public ConfigPropertyAccessor PPS46_ProcessChamberPumpingDownPressureDeviation =>
            GetOrCreateAccessor(EnuChbConfigParameterCodes.PPS46);

        #endregion ChamberB配置参数访问器

        private ConfigPropertyAccessor GetOrCreateAccessor(EnuChbConfigParameterCodes enumValue)
        {
            string key = enumValue.ToString();
            return _cache.GetOrAdd(key, _ =>
            {
                try
                {
                    // 根据参数类型获取相应的值
                    object value = GetParameterValue(enumValue);
                    var setting = new ConfigureSetting
                    {
                        Code = enumValue.ToString(),
                        Value = value,
                        Description = enumValue.GetDescription(),
                        Unit = GetUnitForParameter(enumValue)
                    };
                    return new ConfigPropertyAccessor(setting);
                }
                catch
                {
                    return null;
                }
            });
        }

        /// <summary>
        /// 根据参数类型获取相应的值
        /// </summary>
        /// <param name="enumValue">参数枚举</param>
        /// <returns>参数值</returns>
        private object GetParameterValue(EnuChbConfigParameterCodes enumValue)
        {
            // 需要使用double类型的参数
            switch (enumValue)
            {
                case EnuChbConfigParameterCodes.PPS1:  // 狭缝门运动最小时间
                case EnuChbConfigParameterCodes.PPS2:  // 狭缝门运动最大时间
                case EnuChbConfigParameterCodes.PPS3:  // 升降针运动最小时间
                case EnuChbConfigParameterCodes.PPS4:  // 升降针运动最大时间
                case EnuChbConfigParameterCodes.PPS46: // 工艺腔室抽真空压力偏差

                    return _provider.GetDoubleSettingValue(enumValue);

                default:
                    return _provider.GetIntSettingValue(enumValue);
            }
        }

        private string GetUnitForParameter(EnuChbConfigParameterCodes parameter)
        {
            return parameter switch
            {
                EnuChbConfigParameterCodes.PPS1 or EnuChbConfigParameterCodes.PPS2 or
                EnuChbConfigParameterCodes.PPS3 or EnuChbConfigParameterCodes.PPS4 or
                EnuChbConfigParameterCodes.PPS5 or EnuChbConfigParameterCodes.PPS6 or
                EnuChbConfigParameterCodes.PPS7 or EnuChbConfigParameterCodes.PPS8 => "sec",

                EnuChbConfigParameterCodes.PPS9 or EnuChbConfigParameterCodes.PPS10 or
                EnuChbConfigParameterCodes.PPS11 or EnuChbConfigParameterCodes.PPS12 => "mTorr",

                EnuChbConfigParameterCodes.PPS13 or EnuChbConfigParameterCodes.PPS14 or
                EnuChbConfigParameterCodes.PPS15 or EnuChbConfigParameterCodes.PPS16 => "sccm",

                EnuChbConfigParameterCodes.PPS17 or EnuChbConfigParameterCodes.PPS18 => "MHz",

                EnuChbConfigParameterCodes.PPS19 or EnuChbConfigParameterCodes.PPS20 or
                EnuChbConfigParameterCodes.PPS21 or EnuChbConfigParameterCodes.PPS22 or
                EnuChbConfigParameterCodes.PPS25 => "W",

                EnuChbConfigParameterCodes.PPS23 or EnuChbConfigParameterCodes.PPS24 => "°C",

                _ => ""
            };
        }
    }

    /// <summary>
    /// Shuttle配置访问器
    /// </summary>
    public class ShuttleConfigureAccessor
    {
        private readonly ShuttleConfigParametersProvider _provider;
        private readonly ConcurrentDictionary<string, ConfigPropertyAccessor> _cache = new();

        public ShuttleConfigureAccessor()
        {
            _provider = ShuttleConfigParametersProvider.Instance;
        }

        #region Shuttle配置参数访问器

        /// <summary>
        /// SPS1 - 卡匣巢伸展/缩回最小时间
        /// </summary>
        public ConfigPropertyAccessor SPS1_CassetteNestExtendRetractMinTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS1);

        /// <summary>
        /// SPS2 - 卡匣巢伸展/缩回最大时间
        /// </summary>
        public ConfigPropertyAccessor SPS2_CassetteNestExtendRetractMaxTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS2);

        /// <summary>
        /// SPS3 - Shuttle上下最小时间
        /// </summary>
        public ConfigPropertyAccessor SPS3_ShuttleUpDownMinTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS3);

        /// <summary>
        /// SPS4 - Shuttle上下最大时间
        /// </summary>
        public ConfigPropertyAccessor SPS4_ShuttleUpDownMaxTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS4);

        /// <summary>
        /// SPS5 - Shuttle旋转最大时间
        /// </summary>
        public ConfigPropertyAccessor SPS5_ShuttleRotateMaxTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS5);

        /// <summary>
        /// SPS6 - 卡匣门运动最小时间
        /// </summary>
        public ConfigPropertyAccessor SPS6_CassetteDoorMotionMinTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS6);

        /// <summary>
        /// SPS7 - 卡匣门运动最大时间
        /// </summary>
        public ConfigPropertyAccessor SPS7_CassetteDoorMotionMaxTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS7);

        /// <summary>
        /// SPS8 - 备用
        /// </summary>
        public ConfigPropertyAccessor SPS8_Spare =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS8);

        /// <summary>
        /// SPS9 - ISO阀门运动最大时间
        /// </summary>
        public ConfigPropertyAccessor SPS9_ISOValveMotionMaxTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS9);

        /// <summary>
        /// SPS10 - 滑出前传感器使能
        /// </summary>
        public ConfigPropertyAccessor SPS10_SlideOutFrontSensorEnable =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS10);

        /// <summary>
        /// SPS11 - 滑出后传感器使能
        /// </summary>
        public ConfigPropertyAccessor SPS11_SlideOutBackSensorEnable =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS11);

        /// <summary>
        /// SPS12 - Shuttle传输压力
        /// </summary>
        public ConfigPropertyAccessor SPS12_ShuttleTransferPressure =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS12);

        /// <summary>
        /// SPS13 - Shuttle上下压差
        /// </summary>
        public ConfigPropertyAccessor SPS13_DeltaPressureForShuttleUpDown =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS13);

        /// <summary>
        /// SPS14 - 装载锁大气压力最小值
        /// </summary>
        public ConfigPropertyAccessor SPS14_LoadlockAtmPressureMinimum =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS14);

        /// <summary>
        /// SPS15 - Shuttle大气压力最小值
        /// </summary>
        public ConfigPropertyAccessor SPS15_ShuttleAtmPressureMinimum =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS15);

        /// <summary>
        /// SPS16 - 传输速率
        /// </summary>
        public ConfigPropertyAccessor SPS16_TransferRate =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS16);

        /// <summary>
        /// SPS17 - Shuttle抽真空最大时间
        /// </summary>
        public ConfigPropertyAccessor SPS17_PumpDownShuttleMaxTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS17);

        /// <summary>
        /// SPS18 - 装载锁大气压力设定点
        /// </summary>
        public ConfigPropertyAccessor SPS18_LoadlockAtmPressureSetpoint =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS18);

        /// <summary>
        /// SPS19 - Shuttle大气压力最小设定点
        /// </summary>
        public ConfigPropertyAccessor SPS19_ShuttleAtmPressureMinimumSetpoint =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS19);

        /// <summary>
        /// SPS20 - Shuttle压力偏移
        /// </summary>
        public ConfigPropertyAccessor SPS20_ShuttlePressureOffset =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS20);

        /// <summary>
        /// SPS21 - 装载锁压力偏移
        /// </summary>
        public ConfigPropertyAccessor SPS21_LoadlockPressureOffset =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS21);

        /// <summary>
        /// SPS22 - Shuttle回填最大时间
        /// </summary>
        public ConfigPropertyAccessor SPS22_ShuttleBackfillMaxTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS22);

        /// <summary>
        /// SPS23 - 装载锁回填最大时间
        /// </summary>
        public ConfigPropertyAccessor SPS23_LoadlockBackfillMaxTime =>
            GetOrCreateAccessor(EnuShuttleConfigParameterCodes.SPS23);

        #endregion Shuttle配置参数访问器

        private ConfigPropertyAccessor GetOrCreateAccessor(EnuShuttleConfigParameterCodes enumValue)
        {
            string key = enumValue.ToString();
            return _cache.GetOrAdd(key, _ =>
            {
                try
                {
                    // 根据参数类型获取相应的值
                    object value = GetParameterValue(enumValue);
                    var setting = new ConfigureSetting
                    {
                        Code = enumValue.ToString(),
                        Value = value,
                        Description = enumValue.GetDescription(),
                        Unit = GetUnitForParameter(enumValue)
                    };
                    return new ConfigPropertyAccessor(setting);
                }
                catch
                {
                    return null;
                }
            });
        }

        /// <summary>
        /// 根据参数类型获取相应的值（支持数字型和字符串型）
        /// </summary>
        /// <param name="enumValue">参数枚举值</param>
        /// <returns>参数值</returns>
        private object GetParameterValue(EnuShuttleConfigParameterCodes enumValue)
        {
            // 字符串类型参数
            if (IsStringParameter(enumValue))
            {
                return _provider.GetSettingValue<string>(enumValue.ToString());
            }

            // 数字类型参数
            return _provider.GetIntSettingValue(enumValue);
        }

        /// <summary>
        /// 判断参数是否为字符串类型
        /// </summary>
        /// <param name="enumValue">参数枚举值</param>
        /// <returns>是否为字符串类型</returns>
        private bool IsStringParameter(EnuShuttleConfigParameterCodes enumValue)
        {
            return enumValue switch
            {
                EnuShuttleConfigParameterCodes.SPS8 => true,   // spare (N/A)
                EnuShuttleConfigParameterCodes.SPS10 => true,  // slide out front sensor enable (Y/N)
                EnuShuttleConfigParameterCodes.SPS11 => true,  // slide out back sensor enable (Y/N)
                _ => false
            };
        }

        private string GetUnitForParameter(EnuShuttleConfigParameterCodes parameter)
        {
            return parameter switch
            {
                EnuShuttleConfigParameterCodes.SPS1 or EnuShuttleConfigParameterCodes.SPS2 or
                EnuShuttleConfigParameterCodes.SPS3 or EnuShuttleConfigParameterCodes.SPS4 or
                EnuShuttleConfigParameterCodes.SPS5 or EnuShuttleConfigParameterCodes.SPS6 or
                EnuShuttleConfigParameterCodes.SPS7 or EnuShuttleConfigParameterCodes.SPS8 or
                EnuShuttleConfigParameterCodes.SPS17 or EnuShuttleConfigParameterCodes.SPS22 or
                EnuShuttleConfigParameterCodes.SPS23 => "sec",

                EnuShuttleConfigParameterCodes.SPS12 or EnuShuttleConfigParameterCodes.SPS13 or
                EnuShuttleConfigParameterCodes.SPS14 or EnuShuttleConfigParameterCodes.SPS15 or
                EnuShuttleConfigParameterCodes.SPS18 or EnuShuttleConfigParameterCodes.SPS19 or
                EnuShuttleConfigParameterCodes.SPS20 or EnuShuttleConfigParameterCodes.SPS21 => "mTorr",

                EnuShuttleConfigParameterCodes.SPS16 => "sccm",

                EnuShuttleConfigParameterCodes.SPS9 or EnuShuttleConfigParameterCodes.SPS10 or
                EnuShuttleConfigParameterCodes.SPS11 => "",

                _ => ""
            };
        }
    }

    /// <summary>
    /// MainSystem配置访问器
    /// </summary>
    public class MainSystemConfigureAccessor
    {
        private readonly MainSystemConfigParametersProvider _provider;
        private readonly ConcurrentDictionary<string, ConfigPropertyAccessor> _cache = new();

        public MainSystemConfigureAccessor()
        {
            _provider = MainSystemConfigParametersProvider.Instance;
        }

        #region MainSystem配置参数访问器

        /// <summary>
        /// SSC1 - Shuttle 1晶圆尺寸
        /// </summary>
        public ConfigPropertyAccessor SSC1_Shuttle1WaferSize =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC1);

        /// <summary>
        /// SSC2 - Shuttle 2晶圆尺寸
        /// </summary>
        public ConfigPropertyAccessor SSC2_Shuttle2WaferSize =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC2);

        /// <summary>
        /// SSC3 - 腔室位置
        /// </summary>
        public ConfigPropertyAccessor SSC3_ChamberLocation =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC3);

        /// <summary>
        /// SSC4 - 腔室A工艺
        /// </summary>
        public ConfigPropertyAccessor SSC4_ChamberAProcess =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC4);

        /// <summary>
        /// SSC5 - 腔室B工艺
        /// </summary>
        public ConfigPropertyAccessor SSC5_ChamberBProcess =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC5);

        /// <summary>
        /// SSC6 - 卡匣巢类型
        /// </summary>
        public ConfigPropertyAccessor SSC6_CassetteNestType =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC6);

        /// <summary>
        /// SSC7 - 狭缝门类型
        /// </summary>
        public ConfigPropertyAccessor SSC7_SlitDoorType =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC7);

        /// <summary>
        /// SSC8 - 优先级生效时间
        /// </summary>
        public ConfigPropertyAccessor SSC8_PriorityEffective =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC8);

        /// <summary>
        /// SSC9 - 映射功能
        /// </summary>
        public ConfigPropertyAccessor SSC9_MappingFunction =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC9);

        /// <summary>
        /// SSC10 - 跳过空槽位
        /// </summary>
        public ConfigPropertyAccessor SSC10_SkipEmptySlot =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC10);

        /// <summary>
        /// SSC11 - 冷却腔温度
        /// </summary>
        public ConfigPropertyAccessor SSC11_TemperatureForCoolingChamber =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC11);

        /// <summary>
        /// SSC12 - 腔室温度偏差
        /// </summary>
        public ConfigPropertyAccessor SSC12_DeviationForChamberTemperature =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC12);

        /// <summary>
        /// SSC13 - 传输压力
        /// </summary>
        public ConfigPropertyAccessor SSC13_TransferPressure =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC13);

        /// <summary>
        /// SSC14 - 槽位产品顺序
        /// </summary>
        public ConfigPropertyAccessor SSC14_SlotProductOrder =>
            GetOrCreateAccessor(EnuMainSystemConfigParameterCodes.SSC14);

        #endregion MainSystem配置参数访问器

        private ConfigPropertyAccessor GetOrCreateAccessor(EnuMainSystemConfigParameterCodes enumValue)
        {
            string key = enumValue.ToString();
            return _cache.GetOrAdd(key, _ =>
            {
                try
                {
                    int value = _provider.GetSettingValue<int>(enumValue.ToString());
                    var setting = new ConfigureSetting
                    {
                        Code = enumValue.ToString(),
                        Value = value,
                        Description = enumValue.GetDescription(),
                        Unit = GetUnitForParameter(enumValue)
                    };
                    return new ConfigPropertyAccessor(setting);
                }
                catch
                {
                    return null;
                }
            });
        }

        private string GetUnitForParameter(EnuMainSystemConfigParameterCodes parameter)
        {
            return parameter switch
            {
                EnuMainSystemConfigParameterCodes.SSC1 or EnuMainSystemConfigParameterCodes.SSC2 => "inch",
                EnuMainSystemConfigParameterCodes.SSC8 => "min",
                EnuMainSystemConfigParameterCodes.SSC11 or EnuMainSystemConfigParameterCodes.SSC12 => "°C",
                EnuMainSystemConfigParameterCodes.SSC13 => "mTorr",
                _ => ""
            };
        }
    }

    /// <summary>
    /// 子系统配置访问器 - 统一访问所有子系统配置
    /// </summary>
    public class SubsystemConfigureAccessor
    {
        public RobotConfigureAccessor Robot { get; }
        public ChamberAConfigureAccessor ChamberA { get; }
        public ChamberBConfigureAccessor ChamberB { get; }
        public ShuttleConfigureAccessor Shuttle { get; }
        public MainSystemConfigureAccessor MainSystem { get; }

        public SubsystemConfigureAccessor()
        {
            Robot = new RobotConfigureAccessor();
            ChamberA = new ChamberAConfigureAccessor(); // 使用ChamberA专用配置访问器和EnuChaConfigParameterCodes枚举
            ChamberB = new ChamberBConfigureAccessor(); // 使用ChamberB专用配置访问器和EnuChbConfigParameterCodes枚举
            Shuttle = new ShuttleConfigureAccessor();
            MainSystem = new MainSystemConfigureAccessor();
        }
    }

    #endregion 配置访问器

    #region 状态访问器

    /// <summary>
    /// Robot状态访问器 - 提供直接访问完整Robot状态实体对象的方式
    /// </summary>
    public class RobotStatusAccessor
    {
        private readonly Func<RobotSubsystemStatus> _statusGetter;

        public RobotStatusAccessor(Func<RobotSubsystemStatus> statusGetter)
        {
            _statusGetter = statusGetter ?? throw new ArgumentNullException(nameof(statusGetter));
        }

        /// <summary>
        /// 直接访问完整的Robot状态实体对象
        /// 使用方式：
        /// var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
        /// var robotMainStatus = robotStatus.EnuRobotStatus;
        /// var tAxisDestination = robotStatus.EnuTAxisSmoothDestination;
        /// </summary>
        public RobotSubsystemStatus Status => _statusGetter();

        /// <summary>
        /// 获取Robot状态的字符串表示（用于调试和日志）
        /// </summary>
        public string StatusString => Status?.ToString() ?? "Robot状态未初始化";

        /// <summary>
        /// 检查Robot状态是否已初始化
        /// </summary>
        public bool IsInitialized => Status != null;
    }

    /// <summary>
    /// Chamber状态访问器 - 提供直接访问完整Chamber状态实体对象的方式
    /// </summary>
    public class ChamberStatusAccessor
    {
        private readonly Func<ChamberSubsystemStatus> _statusGetter;

        public ChamberStatusAccessor(Func<ChamberSubsystemStatus> statusGetter)
        {
            _statusGetter = statusGetter ?? throw new ArgumentNullException(nameof(statusGetter));
        }

        /// <summary>
        /// 直接访问完整的Chamber状态实体对象
        /// 使用方式：
        /// var chamberStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberA.Status;
        /// var slitDoorStatus = chamberStatus.SlitDoorStatus;
        /// var liftPinStatus = chamberStatus.LiftPinStatus;
        /// </summary>
        public ChamberSubsystemStatus Status => _statusGetter();

        /// <summary>
        /// 获取Chamber状态的字符串表示（用于调试和日志）
        /// </summary>
        public string StatusString => Status?.ToString() ?? "Chamber状态未初始化";

        /// <summary>
        /// 检查Chamber状态是否已初始化
        /// </summary>
        public bool IsInitialized => Status != null;
    }

    /// <summary>
    /// Shuttle状态访问器 - 提供直接访问完整Shuttle状态实体对象的方式
    /// </summary>
    public class ShuttleStatusAccessor
    {
        private readonly Func<ShuttleSubsystemStatus> _statusGetter;

        public ShuttleStatusAccessor(Func<ShuttleSubsystemStatus> statusGetter)
        {
            _statusGetter = statusGetter ?? throw new ArgumentNullException(nameof(statusGetter));
        }

        /// <summary>
        /// 直接访问完整的Shuttle状态实体对象
        /// 使用方式：
        /// var shuttleStatus = SS200InterLockMain.Instance.SubsystemStatus.Shuttle.Status;
        /// var shuttlePosition = shuttleStatus.ShuttlePosition;
        /// var cassetteStatus = shuttleStatus.CassetteStatus;
        /// </summary>
        public ShuttleSubsystemStatus Status => _statusGetter();

        /// <summary>
        /// 获取Shuttle状态的字符串表示（用于调试和日志）
        /// </summary>
        public string StatusString => Status?.ToString() ?? "Shuttle状态未初始化";

        /// <summary>
        /// 检查Shuttle状态是否已初始化
        /// </summary>
        public bool IsInitialized => Status != null;
    }

    #endregion 状态访问器

    #region 主访问器类

    /// <summary>
    /// IO接口访问器
    /// </summary>
    public class IOInterfaceAccessor
    {
        public RobotIOAccessor Robot { get; }
        public ChamberIOAccessor ChamberA { get; }
        public ChamberIOAccessor ChamberB { get; }
        public ShuttleIOAccessor Shuttle { get; }

        public IOInterfaceAccessor(CoilStatusHelper coilHelper)
        {
            Robot = new RobotIOAccessor(coilHelper);
            ChamberA = new ChamberIOAccessor(coilHelper, EnuMcuDeviceType.ChamberA);
            ChamberB = new ChamberIOAccessor(coilHelper, EnuMcuDeviceType.ChamberB);
            Shuttle = new ShuttleIOAccessor(coilHelper);
        }
    }

    /// <summary>
    /// 报警代码访问器
    /// </summary>
    public class AlarmCodeAccessor
    {
        public RobotAlarmAccessor Robot { get; }
        public ChamberAlarmAccessor ChamberA { get; }
        public ChamberAlarmAccessor ChamberB { get; }
        public ShuttleAlarmAccessor Shuttle { get; }

        public AlarmCodeAccessor()
        {
            Robot = new RobotAlarmAccessor();
            ChamberA = new ChamberAlarmAccessor();
            ChamberB = new ChamberAlarmAccessor();
            Shuttle = new ShuttleAlarmAccessor();
        }
    }

    /// <summary>
    /// 子系统状态访问器
    /// </summary>
    public class SubsystemStatusAccessor
    {
        public RobotStatusAccessor Robot { get; }
        public ChamberStatusAccessor ChamberA { get; }
        public ChamberStatusAccessor ChamberB { get; }
        public ShuttleStatusAccessor Shuttle { get; }

        public SubsystemStatusAccessor(
            Func<RobotSubsystemStatus> robotStatusGetter,
            Func<ChamberSubsystemStatus> chamberAStatusGetter,//添加了子类ChamberA和ChamberB，这边还有父类 ChamberSubsystemStatus，会不会有影响？
            Func<ChamberSubsystemStatus> chamberBStatusGetter,
            Func<ShuttleSubsystemStatus> shuttleStatusGetter)
        {
            Robot = new RobotStatusAccessor(robotStatusGetter);
            ChamberA = new ChamberStatusAccessor(chamberAStatusGetter);
            ChamberB = new ChamberStatusAccessor(chamberBStatusGetter);
            Shuttle = new ShuttleStatusAccessor(shuttleStatusGetter);
        }
    }

    #endregion 主访问器类

    #region 主单例类

    /// <summary>
    /// SS200InterLockMain - 单例模式的统一访问入口
    /// 提供对设备IO、报警信息、配置信息、状态信息、RTZ轴位置信息的统一访问
    ///
    /// 使用示例：
    /// // IO调用
    /// bool robotDI1Value = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Value;
    /// string robotDI1Content = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSensor1Left.Content;
    ///
    /// // 报警调用
    /// string alarmContent = SS200InterLockMain.Instance.AlarmCode.Robot.RA1_SystemBusyReject.Content;
    /// string alarmChsContent = SS200InterLockMain.Instance.AlarmCode.Robot.RA1_SystemBusyReject.ChsContent;
    ///
    /// // 配置调用
    /// int positionValue = SS200InterLockMain.Instance.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Value;
    ///
    /// // RTZ轴位置调用
    /// int tAxisStep = SS200InterLockMain.Instance.RTZAxisPosition.CurrentTAxisStep;
    /// double tAxisDegree = SS200InterLockMain.Instance.RTZAxisPosition.CurrentTAxisDegree;
    /// var (t, r, z) = SS200InterLockMain.Instance.RTZAxisPosition.GetCurrentRTZSteps();
    /// string displayText = SS200InterLockMain.Instance.RTZAxisPosition.GetRTZPositionDisplayText();
    ///
    /// // 状态调用 - 直接访问完整状态实体对象
    /// var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
    /// var robotMainStatus = robotStatus.EnuRobotStatus;
    /// var tAxisDestination = robotStatus.EnuTAxisSmoothDestination;
    /// var paddleStatus = robotStatus.SmoothPaddleP1Status;
    ///
    /// var chamberAStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberA.Status;
    /// var slitDoorStatus = chamberAStatus.SlitDoorStatus;
    ///
    /// var shuttleStatus = SS200InterLockMain.Instance.SubsystemStatus.Shuttle.Status;
    /// var shuttlePosition = shuttleStatus.ShuttlePosition;
    /// </summary>
    public class SS200InterLockMain
    {
        #region 单例实现

        private static readonly Lazy<SS200InterLockMain> _instance =
            new Lazy<SS200InterLockMain>(() => new SS200InterLockMain(), true);

        /// <summary>
        /// 获取SS200InterLockMain的单例实例
        /// </summary>
        public static SS200InterLockMain Instance => _instance.Value;

        #endregion 单例实现

        #region 字段

        private readonly ILog _logger = LogManager.GetLogger(typeof(SS200InterLockMain));
        private readonly IS200McuCmdService _mcuCmdService;
        private readonly CoilStatusHelper _coilHelper;

        /// <summary>
        /// 集合同步锁对象
        /// </summary>
        private readonly object _logListLock = new object();

        #endregion 字段

        #region 统一访问入口

        /// <summary>
        /// IO接口访问入口
        /// </summary>
        public IOInterfaceAccessor IOInterface { get; }

        /// <summary>
        /// 报警代码访问入口
        /// </summary>
        public AlarmCodeAccessor AlarmCode { get; }

        /// <summary>
        /// 子系统配置访问入口
        /// </summary>
        public SubsystemConfigureAccessor SubsystemConfigure { get; }

        /// <summary>
        /// 子系统状态访问入口
        /// </summary>
        public SubsystemStatusAccessor SubsystemStatus { get; }

        /// <summary>
        /// RTZ轴位置访问入口
        /// 提供统一的RTZ轴位置访问接口，包括：
        /// - 基本位置访问（步进值）
        /// - 物理单位转换（角度、长度、高度）
        /// - 组合访问和格式化显示
        /// - 数据有效性检查和安全范围验证
        ///
        /// 使用示例：
        /// int tAxisStep = SS200InterLockMain.Instance.RTZAxisPosition.CurrentTAxisStep;
        /// double tAxisDegree = SS200InterLockMain.Instance.RTZAxisPosition.CurrentTAxisDegree;
        /// var (t, r, z) = SS200InterLockMain.Instance.RTZAxisPosition.GetCurrentRTZSteps();
        /// string displayText = SS200InterLockMain.Instance.RTZAxisPosition.GetRTZPositionDisplayText();
        /// </summary>
        public RTZAxisPositionAccessor RTZAxisPosition { get; }

        #endregion 统一访问入口

        #region 原有属性（保持兼容性）

        // public Dictionary<EnuRobotErrorCode, AlarmItem> DicRobotAlarmItems { get; set; } = new();
        // public List<AlarmItem> RobotAlarmItems { get; set; } = new();
        // public List<AlarmItem> ShuttleAlarmItems { get; set; } = new();
        // public List<AlarmItem> ChaAlarmItems { get; set; } = new();
        // public List<AlarmItem> ChbAlarmItems { get; set; } = new();

        public List<ConfigureSetting> RobotPositionValue { get; set; } = new();
        // public List<ConfigureSetting> RobotConfigureSettings { get; set; } = new();
        // public List<ConfigureSetting> ChaConfigureSettings { get; set; } = new();
        // public List<ConfigureSetting> ChbConfigureSettings { get; set; } = new();
        // public List<ConfigureSetting> CoolingConfigureSettings { get; set; } = new();
        // public List<ConfigureSetting> ShuttleConfigureSettings { get; set; } = new();
        // public List<ConfigureSetting> MainSystemConfigureSettings { get; set; } = new();

        public ObservableCollection<RobotSubsystemStatus> RobotStatuValue { get; set; } = new();

        // 状态实例（用于状态访问器）
        private RobotSubsystemStatus _currentRobotStatus;

        private ChamberSubsystemStatus _currentChamberAStatus;
        private ChamberSubsystemStatus _currentChamberBStatus;
        private ShuttleSubsystemStatus _currentShuttleStatus;

        #endregion 原有属性（保持兼容性）

        #region 构造函数

        /// <summary>
        /// 私有构造函数，确保单例模式
        /// </summary>
        private SS200InterLockMain()
        {
            try
            {
                // 从IOC容器获取服务实例
                _mcuCmdService = App.GetInstance<IS200McuCmdService>();
                _coilHelper = new CoilStatusHelper(_mcuCmdService);

                // 从IOC容器获取状态实例
                _currentRobotStatus = App.GetInstance<RobotSubsystemStatus>();
                // 修复：获取具体的子类实例，而不是抽象基类
                _currentChamberAStatus = App.GetInstance<ChamberASubsystemStatus>();
                _currentChamberBStatus = App.GetInstance<ChamberBSubsystemStatus>();
                _currentShuttleStatus = App.GetInstance<ShuttleSubsystemStatus>();

                // 加载机器人位置配置数据
                LoadRobotPositionConfiguration();

                // 初始化访问器
                IOInterface = new IOInterfaceAccessor(_coilHelper);
                AlarmCode = new AlarmCodeAccessor();
                SubsystemConfigure = new SubsystemConfigureAccessor();
                SubsystemStatus = new SubsystemStatusAccessor(
                    () => _currentRobotStatus,
                    () => _currentChamberAStatus,
                    () => _currentChamberBStatus,
                    () => _currentShuttleStatus);
                RTZAxisPosition = new RTZAxisPositionAccessor(_mcuCmdService);

                _logger.Info("SS200InterLockMain单例实例初始化成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"SS200InterLockMain初始化失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 加载机器人位置配置数据
        /// </summary>
        private void LoadRobotPositionConfiguration()
        {
            try
            {
                var provider = RobotPositionParametersProvider.Instance;

                // 尝试从JSON文件加载配置
                provider.LoadFromJson();

                // 将配置数据转换为ConfigureSetting列表
                RobotPositionValue.Clear();

                // 获取所有RP参数（RP1到RP28）
                for (int i = 1; i <= 28; i++)
                {
                    string code = $"RP{i}";
                    try
                    {
                        // 将字符串转换为枚举
                        if (Enum.TryParse<EnuRobotPositionParameterCodes>(code, out var enumCode))
                        {
                            int value = provider.GetParameterValue(enumCode);
                            var setting = new ConfigureSetting
                            {
                                Id = i,
                                Code = code,
                                Description = GetRobotPositionDescription(code),
                                Value = value,
                                Unit = "step",
                                AxisType = GetAxisTypeForRP(code)
                            };
                            RobotPositionValue.Add(setting);
                            _logger.Debug($"加载机器人位置参数: {code} = {value}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warn($"加载机器人位置参数 {code} 失败: {ex.Message}");
                    }
                }

                _logger.Info($"成功加载 {RobotPositionValue.Count} 个机器人位置配置参数");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载机器人位置配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取机器人位置参数的描述
        /// </summary>
        private string GetRobotPositionDescription(string code)
        {
            return code switch
            {
                "RP1" => "T-axis smooth to CHA",
                "RP2" => "T-axis smooth to CHB",
                "RP3" => "T-axis smooth to cooling chamber",
                "RP4" => "T-axis smooth to cassette",
                "RP5" => "T-axis nose to CHA",
                "RP6" => "T-axis nose to CHB",
                "RP7" => "T-axis nose to cooling chamber",
                "RP8" => "T-axis nose to cassette",
                "RP9" => "T-axis zero position",
                "RP10" => "R-axis smooth to CHA",
                "RP11" => "R-axis smooth to CHB",
                "RP12" => "R-axis smooth to cooling chamber",
                "RP13" => "R-axis smooth to cassette",
                "RP14" => "R-axis nose to CHA",
                "RP15" => "R-axis nose to CHB",
                "RP16" => "R-axis nose to cooling chamber",
                "RP17" => "R-axis nose to cassette",
                "RP18" => "R-axis zero position",
                "RP19" => "Z-axis smooth get CHA",
                "RP20" => "Z-axis smooth get CHB",
                "RP21" => "Z-axis smooth get cooling top",
                "RP22" => "Z-axis smooth get cooling bottom",
                "RP23" => "Z-axis nose get CHA",
                "RP24" => "Z-axis nose get CHB",
                "RP25" => "Z-axis nose get cooling top",
                "RP26" => "Z-axis nose get cooling bottom",
                "RP27" => "Z-axis zero position",
                "RP28" => "Z-axis pin search position",
                _ => $"Robot position parameter {code}"
            };
        }

        /// <summary>
        /// 获取RP参数对应的轴类型
        /// </summary>
        private int GetAxisTypeForRP(string code)
        {
            return code switch
            {
                "RP1" or "RP2" or "RP3" or "RP4" or "RP5" or "RP6" or "RP7" or "RP8" or "RP9" => 1, // T轴
                "RP10" or "RP11" or "RP12" or "RP13" or "RP14" or "RP15" or "RP16" or "RP17" or "RP18" => 2, // R轴
                "RP19" or "RP20" or "RP21" or "RP22" or "RP23" or "RP24" or "RP25" or "RP26" or "RP27" or "RP28" => 3, // Z轴
                _ => 0 // 未知
            };
        }

        #endregion 构造函数

        #region 公共方法

        /// <summary>
        /// 更新Robot状态
        /// </summary>
        /// <param name="status">新的Robot状态</param>
        public void UpdateRobotStatus(RobotSubsystemStatus status)
        {
            _currentRobotStatus = status ?? throw new ArgumentNullException(nameof(status));
        }

        /// <summary>
        /// 更新Chamber A状态
        /// </summary>
        /// <param name="status">新的Chamber A状态</param>
        public void UpdateChamberAStatus(ChamberSubsystemStatus status)
        {
            _currentChamberAStatus = status ?? throw new ArgumentNullException(nameof(status));
        }

        /// <summary>
        /// 更新Chamber B状态
        /// </summary>
        /// <param name="status">新的Chamber B状态</param>
        public void UpdateChamberBStatus(ChamberSubsystemStatus status)
        {
            _currentChamberBStatus = status ?? throw new ArgumentNullException(nameof(status));
        }

        /// <summary>
        /// 更新Shuttle状态
        /// </summary>
        /// <param name="status">新的Shuttle状态</param>
        public void UpdateShuttleStatus(ShuttleSubsystemStatus status)
        {
            _currentShuttleStatus = status ?? throw new ArgumentNullException(nameof(status));
        }

        /// <summary>
        /// 获取CoilStatusHelper实例（用于高级操作）
        /// </summary>
        /// <returns>CoilStatusHelper实例</returns>
        public CoilStatusHelper GetCoilStatusHelper()
        {
            return _coilHelper;
        }

        /// <summary>
        /// 获取MCU命令服务实例（用于高级操作）
        /// </summary>
        /// <returns>IS200McuCmdService实例</returns>
        public IS200McuCmdService GetMcuCmdService()
        {
            return _mcuCmdService;
        }

        #endregion 公共方法
    }

    #endregion 主单例类
}