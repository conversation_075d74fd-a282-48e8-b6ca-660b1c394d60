using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle
{
    /// <summary>
    /// Shuttle阀门状态枚举 (SSD14-SSD23)
    /// </summary>
    public enum EnuShuttleValveStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 阀门打开
        /// </summary>
        [Description("打开")]
        Open = 1,

        /// <summary>
        /// 阀门关闭
        /// </summary>
        [Description("关闭")]
        Close = 2
    }
}