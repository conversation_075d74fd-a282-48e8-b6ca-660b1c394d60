﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Models.Shared
{
    /// <summary>
    /// 操作者
    /// </summary>
    public class Operator
    {
        public int UserId
        { get { return (int)(Property?.Id); } }
        /// <summary>
        /// 当前操作者UserName
        /// </summary>
        public string UserName { get; set; }

        public string Avatar { get; set; }

        public Base_User Property { get; set; }

        public List<string> Permissions { get; set; }

        //菜单树
        //public ObservableCollection<AMenuItem> MenuItems { get; set; }

        //打平用于查询的菜单
        //public ObservableCollection<AMenuItem> SearchMenus { get; set; }
    }
}