using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Docs.Examples;

namespace Zishan.SS200.Cmd.Docs.Test
{
    /// <summary>
    /// 测试运行器
    /// 用于运行线圈查找功能的各种测试
    /// </summary>
    public class TestRunner
    {
        /// <summary>
        /// 运行所有测试的主方法
        /// </summary>
        public static async Task RunTests()
        {
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine("线圈查找解决方案测试程序");
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine();

            try
            {
                // 创建测试实例
                var tests = new CoilLookupTests();

                // 运行SS200InterLockMain配置测试
                Console.WriteLine("1. 运行SS200InterLockMain配置测试...");
                SS200InterLockMainConfigTest.RunAllTests();

                // 运行基本功能测试
                Console.WriteLine("\n2. 运行基本功能测试...");
                tests.RunAllTests();

                Console.WriteLine("\n" + "-".PadRight(60, '-'));

                // 运行性能测试
                Console.WriteLine("2. 运行性能测试...");
                tests.PerformanceTest();

                Console.WriteLine("-".PadRight(60, '-'));

                // 显示使用建议
                ShowUsageRecommendations();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试运行出错: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }

            Console.WriteLine("\n测试完成，按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 显示使用建议
        /// </summary>
        private static void ShowUsageRecommendations()
        {
            Console.WriteLine("3. 使用建议和最佳实践:");
            Console.WriteLine();

            Console.WriteLine("📋 在SubSystemStatus中的推荐用法:");
            Console.WriteLine("   1. 在构造函数中创建CoilStatusHelper实例");
            Console.WriteLine("   2. 使用GetCoilValue()方法获取单个线圈状态");
            Console.WriteLine("   3. 使用内置的Calculate方法进行复杂状态计算");
            Console.WriteLine("   4. 使用组合查询方法处理多个线圈的逻辑关系");
            Console.WriteLine();

            Console.WriteLine("⚡ 性能优化建议:");
            Console.WriteLine("   1. 重用CoilStatusHelper实例，避免重复创建");
            Console.WriteLine("   2. 对于频繁查询的线圈，考虑缓存结果");
            Console.WriteLine("   3. 使用批量查询方法减少单次查询开销");
            Console.WriteLine();

            Console.WriteLine("🔧 调试建议:");
            Console.WriteLine("   1. 使用GetAllCoilStatus()方法查看所有线圈状态");
            Console.WriteLine("   2. 检查线圈的IoCode是否与枚举名称匹配");
            Console.WriteLine("   3. 确认设备连接状态和线圈初始化");
            Console.WriteLine();

            Console.WriteLine("📚 相关文档:");
            Console.WriteLine("   - 详细使用示例: Examples/CoilStatusUsageExample.cs");
            Console.WriteLine("   - 完整文档: Docs/CoilLookupSolution.md");
            Console.WriteLine("   - 扩展方法: Extensions/IOEnumExtensions.cs");
            Console.WriteLine();
        }

        /// <summary>
        /// 交互式测试菜单
        /// </summary>
        public static async Task InteractiveMenu()
        {
            var tests = new CoilLookupTests();

            while (true)
            {
                Console.Clear();
                Console.WriteLine("=".PadRight(60, '='));
                Console.WriteLine("线圈查找解决方案 - 交互式测试菜单");
                Console.WriteLine("=".PadRight(60, '='));
                Console.WriteLine();
                Console.WriteLine("请选择要运行的测试:");
                Console.WriteLine("1. IO代码提取测试");
                Console.WriteLine("2. 枚举类型判断测试");
                Console.WriteLine("3. 线圈查找测试");
                Console.WriteLine("4. CoilStatusHelper功能测试");
                Console.WriteLine("5. 所有线圈状态获取测试");
                Console.WriteLine("6. 性能测试");
                Console.WriteLine("7. 运行所有测试");
                Console.WriteLine("8. 显示使用建议");
                Console.WriteLine("0. 退出");
                Console.WriteLine();
                Console.Write("请输入选项 (0-8): ");

                var input = Console.ReadLine();
                Console.WriteLine();

                try
                {
                    switch (input)
                    {
                        case "1":
                            tests.TestIOCodeExtraction();
                            break;

                        case "2":
                            tests.TestEnumTypeDetection();
                            break;

                        case "3":
                            tests.TestCoilLookup();
                            break;

                        case "4":
                            tests.TestCoilStatusHelper();
                            break;

                        case "5":
                            tests.TestGetAllCoilStatus();
                            break;

                        case "6":
                            tests.PerformanceTest();
                            break;

                        case "7":
                            tests.RunAllTests();
                            break;

                        case "8":
                            ShowUsageRecommendations();
                            break;

                        case "0":
                            return;

                        default:
                            Console.WriteLine("无效选项，请重新选择。");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"测试执行出错: {ex.Message}");
                }

                Console.WriteLine("\n按任意键继续...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 程序入口点（如果作为独立程序运行）
        /// </summary>
        public static async Task Main(string[] args)
        {
            if (args.Length > 0 && args[0] == "--interactive")
            {
                await InteractiveMenu();
            }
            else
            {
                await RunTests();
            }
        }
    }
}