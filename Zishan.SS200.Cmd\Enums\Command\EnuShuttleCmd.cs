using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.Command;

/// <summary>
/// Robot 逻辑命令索引枚举
/// </summary>
[TypeConverter(typeof(EnumDescriptionTypeConverter))]
public enum EnuShuttleCmd
{
    /// <summary>
    /// Shuttle down - Shuttle下降
    /// </summary>
    [Description("S1 SD")]
    S1_SD = 0,

    /// <summary>
    /// Shuttle up - Shuttle上升
    /// </summary>
    [Description("S2 SU")]
    S2_SU = 1,

    /// <summary>
    /// Shuttle 1 rotate out - Shuttle1旋转出
    /// </summary>
    [Description("S3 SR1")]
    S3_SR1 = 2,

    /// <summary>
    /// Shuttle 2 rotate out - Shuttle2旋转出
    /// </summary>
    [Description("S4 SR2")]
    S4_SR2 = 3,

    /// <summary>
    /// Close cassette door - 关闭晶圆盒门
    /// </summary>
    [Description("S5 CD CD")]
    S5_CD_CD = 4,

    /// <summary>
    /// Open cassette door - 打开晶圆盒门
    /// </summary>
    [Description("S6 OD CD")]
    S6_OD_CD = 5,

    /// <summary>
    /// Cassette nest extend - 晶圆盒巢穴伸出
    /// </summary>
    [Description("S7 TS1")]
    S7_TS1 = 6,

    /// <summary>
    /// Cassette nest retract - 晶圆盒巢穴收回
    /// </summary>
    [Description("S8 TS2")]
    S8_TS2 = 7,

    /// <summary>
    /// Swap cassette - 交换晶圆盒
    /// </summary>
    [Description("S9 SC")]
    S9_SC = 8,

    /// <summary>
    /// Return cassette - 返回晶圆盒
    /// </summary>
    [Description("S10 RC")]
    S10_RC = 9,

    /// <summary>
    /// Open SV valve - 打开SV阀门
    /// </summary>
    [Description("S11 OV SV")]
    S11_OV_SV = 10,

    /// <summary>
    /// Close SV valve - 关闭SV阀门
    /// </summary>
    [Description("S12 CV SV")]
    S12_CV_SV = 11,

    /// <summary>
    /// Open SB valve - 打开SB阀门
    /// </summary>
    [Description("S13 OV SB")]
    S13_OV_SB = 12,

    /// <summary>
    /// Close SB valve - 关闭SB阀门
    /// </summary>
    [Description("S14 CV SB")]
    S14_CV_SB = 13,

    /// <summary>
    /// Open cross valve - 打开交叉阀门
    /// </summary>
    [Description("S15 OV XV")]
    S15_OV_XV = 14,

    /// <summary>
    /// Close cross valve - 关闭交叉阀门
    /// </summary>
    [Description("S16 CV XV")]
    S16_CV_XV = 15,

    /// <summary>
    /// Open loadlock purge valve - 打开装载锁清洗阀门
    /// </summary>
    [Description("S17 OV BL")]
    S17_OV_BL = 16,

    /// <summary>
    /// Close loadlock purge valve - 关闭装载锁清洗阀门
    /// </summary>
    [Description("S18 CV BL")]
    S18_CV_BL = 17,

    /// <summary>
    /// Open loadlock vent valve - 打开装载锁排气阀门
    /// </summary>
    [Description("S19 OV LB")]
    S19_OV_LB = 18,

    /// <summary>
    /// Close loadlock vent valve - 关闭装载锁排气阀门
    /// </summary>
    [Description("S20 CV LB")]
    S20_CV_LB = 19,

    /// <summary>
    /// Close loadlock vent valve
    /// </summary>
    S20,

    /// <summary>
    /// Pump down shuttle
    /// </summary>
    S21,

    /// <summary>
    /// Backfill shuttle
    /// </summary>
    S22,

    /// <summary>
    /// Pump down loadlock
    /// </summary>
    S23,

    /// <summary>
    /// Backfill loadlock
    /// </summary>
    S24
}