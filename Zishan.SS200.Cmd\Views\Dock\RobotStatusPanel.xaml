<UserControl
    x:Class="Zishan.SS200.Cmd.Views.Dock.RobotStatusPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:common="clr-namespace:Zishan.SS200.Cmd.Common"
    xmlns:converters="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:designViewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels.Dock.DesignViewModels"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    d:DesignHeight="850"
    d:DesignWidth="750"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="Transparent"
    mc:Ignorable="d">

    <UserControl.Resources>
        <!--  转换器  -->
        <converters:IntToStringConverter x:Key="IntToStringConverter" />
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
        <converters:MultiCommandParameterConverter x:Key="MultiCommandParameterConverter" />
        <converters:StringToBooleanConverter x:Key="StringToBooleanConverter" />
        <converters:StringToEnumConverter x:Key="StringToEnumConverter" />
        <converters:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter" />
        <converters:MultiEnumToVisibilityConverter x:Key="MultiEnumToVisibilityConverter" />

        <!--  错误值转颜色转换器  -->
        <converters:ValueToColorConverter
            x:Key="ValueToColorConverter"
            DefaultValue="Black"
            FalseValue="Green"
            TrueValue="Red" />

        <!--  样式  -->
        <Style x:Key="StatusHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,0,0,15" />
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="Foreground" Value="DarkSlateBlue" />
        </Style>

        <Style x:Key="AxisCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="8" />
            <Setter Property="Padding" Value="16" />
            <Setter Property="Background" Value="#EEEEEE" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2" />
            <Setter Property="UniformCornerRadius" Value="8" />
        </Style>

        <Style x:Key="AxisLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,0,0,12" />
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="Foreground" Value="#9C27B0" />
        </Style>

        <Style x:Key="StatusLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Regular" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Margin" Value="0,4" />
            <Setter Property="Foreground" Value="Black" />
        </Style>

        <Style x:Key="StatusValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Margin" Value="8,4" />
            <Setter Property="HorizontalAlignment" Value="Right" />
            <Setter Property="Foreground" Value="LightBlue" />
        </Style>
    </UserControl.Resources>

    <d:UserControl.DataContext>
        <designViewModels:RobotStatusPanelDesignViewModel />
    </d:UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--
            标题区域
            标题内容通过直接绑定实现，不使用AxisInfoHeader，$"【T轴：{TAxisStep} Step;R轴：{RAxisStep} Step;Z轴：{ZAxisStep}
            PinSearch：Smooth：{McuCmdService.SmoothBasePinSearchValue}；Nose：{McuCmdService.NoseBasePinSearchValue}】"
        -->
        <TextBlock
            Grid.Row="0"
            Margin="0,12,0,8"
            d:Text="【T轴:1,234;R轴:5,678;Z轴:2,345；PinSearch:Smooth:1000:Nose:1001】">
            <TextBlock.Style>
                <Style BasedOn="{StaticResource StatusHeaderStyle}" TargetType="TextBlock">
                    <!--  默认状态：不展开时显示详细信息  -->
                    <Setter Property="Text">
                        <Setter.Value>
                            <MultiBinding StringFormat="【T轴:{0,5};R轴:{1,5};Z轴:{2,5}:PinSearch:Smooth:{3,5}；Nose:{4,5}】">
                                <Binding Path="TAxisStep" StringFormat="{}{0:N0}" />
                                <Binding Path="RAxisStep" StringFormat="{}{0:N0}" />
                                <Binding Path="ZAxisStep" StringFormat="{}{0:N0}" />
                                <Binding Path="McuCmdService.SmoothBasePinSearchValue" />
                                <Binding Path="McuCmdService.NoseBasePinSearchValue" />
                            </MultiBinding>
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <!--  展开时显示简化文本  -->
                        <DataTrigger Binding="{Binding IsAxisInfoExpanded}" Value="True">
                            <Setter Property="Text" Value="Robot轴位置信息" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </TextBlock.Style>
            <TextBlock.ContextMenu>
                <ContextMenu>
                    <MenuItem Command="{Binding CopyAllAxisPositionsCommand}" Header="复制所有轴位置值" />
                    <MenuItem Command="{Binding CopyAllAxisErrorInfoCommand}" Header="复制所有轴错误信息" />
                </ContextMenu>
            </TextBlock.ContextMenu>
        </TextBlock>

        <!--  内容区域  -->
        <Expander
            Grid.Row="1"
            Margin="0,10,0,10"
            d:Header="Robot轴位置信息"
            d:IsExpanded="True"
            ExpandDirection="Down"
            Header="{Binding Title}"
            IsExpanded="{Binding IsAxisInfoExpanded, Mode=TwoWay}">
            <GroupBox Margin="5" Header="轴位置详细信息">
                <Border CornerRadius="4">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  T轴信息  -->
                        <materialDesign:Card Grid.Column="0" Style="{StaticResource AxisCardStyle}">
                            <StackPanel>
                                <TextBlock Style="{StaticResource AxisLabelStyle}" Text="T轴旋转" />

                                <!--  报警错误码  -->
                                <Grid Margin="0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="{Binding RobotAlarmRegisters[0].Value, Converter={StaticResource ValueToColorConverter}, ConverterParameter=0}"
                                        Kind="AlertCircleOutline" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="报警错误码" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Foreground="{Binding RobotAlarmRegisters[0].Value, Converter={StaticResource ValueToColorConverter}, ConverterParameter=0}"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[0].Value, StringFormat='0x{0:X4}'}"
                                        ToolTip="{Binding TAxisErrorInfo}">
                                        <TextBlock.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem Command="{Binding CopyCurrentErrorInfoCommand}" Header="复制当前轴错误信息">
                                                    <MenuItem.CommandParameter>
                                                        <MultiBinding Converter="{StaticResource MultiCommandParameterConverter}">
                                                            <Binding Path="PlacementTarget.Text" RelativeSource="{RelativeSource AncestorType=ContextMenu}" />
                                                            <Binding Source="T轴" />
                                                        </MultiBinding>
                                                    </MenuItem.CommandParameter>
                                                </MenuItem>
                                                <MenuItem Command="{Binding CopyAllAxisErrorInfoCommand}" Header="复制所有轴错误信息" />
                                            </ContextMenu>
                                        </TextBlock.ContextMenu>
                                    </TextBlock>
                                </Grid>

                                <!--  分隔线  -->
                                <Separator Margin="0,4" Background="#444444" />

                                <!--  位置信息  -->
                                <TextBlock
                                    Margin="0,8,0,4"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Foreground="Black"
                                    Text="位置信息：" />

                                <!--  高位  -->
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="#7B1FA2"
                                        Kind="ArrowUpBold" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="检测位置(H)" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[3].Value, StringFormat='0x{0:X4}'}" />
                                </Grid>

                                <!--  低位  -->
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="#7B1FA2"
                                        Kind="ArrowDownBold" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="检测位置(L)" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[4].Value, StringFormat='0x{0:X4}'}" />
                                </Grid>

                                <!--  合并值  -->
                                <Grid
                                    Margin="8,8,8,4"
                                    Background="#444444"
                                    Opacity="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,10,0"
                                        VerticalAlignment="Center"
                                        Foreground="#8BC34A"
                                        Kind="AxisArrow" />

                                    <TextBlock
                                        x:Name="TAxisPosition"
                                        Grid.Column="1"
                                        HorizontalAlignment="Left"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Foreground="#FFEB3B"
                                        Style="{StaticResource StatusValueStyle}"
                                        ToolTip="{Binding TAxisStep, StringFormat='原始步进值: {0}'}">
                                        <TextBlock.Text>
                                            <MultiBinding StringFormat="步进：{0} Step&#x0a;角度：{1:F2} °">
                                                <Binding Path="TAxisStep" />
                                                <Binding Path="TAxisDegree" />
                                            </MultiBinding>
                                        </TextBlock.Text>
                                        <TextBlock.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem
                                                    Command="{Binding CopyCurrentAxisValueCommand}"
                                                    CommandParameter="{Binding PlacementTarget.Text, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                                    Header="复制当前轴值" />
                                                <MenuItem Command="{Binding CopyAllAxisPositionsCommand}" Header="复制所有轴值" />
                                            </ContextMenu>
                                        </TextBlock.ContextMenu>
                                    </TextBlock>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!--  R轴信息  -->
                        <materialDesign:Card Grid.Column="1" Style="{StaticResource AxisCardStyle}">
                            <StackPanel>
                                <TextBlock Style="{StaticResource AxisLabelStyle}" Text="R轴伸缩" />

                                <!--  报警错误码  -->
                                <Grid Margin="0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="{Binding RobotAlarmRegisters[1].Value, Converter={StaticResource ValueToColorConverter}, ConverterParameter=0}"
                                        Kind="AlertCircleOutline" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="报警错误码" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Foreground="{Binding RobotAlarmRegisters[1].Value, Converter={StaticResource ValueToColorConverter}, ConverterParameter=0}"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[1].Value, StringFormat='0x{0:X4}'}"
                                        ToolTip="{Binding RAxisErrorInfo}">
                                        <TextBlock.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem Command="{Binding CopyCurrentErrorInfoCommand}" Header="复制当前轴错误信息">
                                                    <MenuItem.CommandParameter>
                                                        <MultiBinding Converter="{StaticResource MultiCommandParameterConverter}">
                                                            <Binding Path="PlacementTarget.Text" RelativeSource="{RelativeSource AncestorType=ContextMenu}" />
                                                            <Binding Source="R轴" />
                                                        </MultiBinding>
                                                    </MenuItem.CommandParameter>
                                                </MenuItem>
                                                <MenuItem Command="{Binding CopyAllAxisErrorInfoCommand}" Header="复制所有轴错误信息" />
                                            </ContextMenu>
                                        </TextBlock.ContextMenu>
                                    </TextBlock>
                                </Grid>

                                <!--  分隔线  -->
                                <Separator Margin="0,4" Background="#444444" />

                                <!--  位置信息  -->
                                <TextBlock
                                    Margin="0,8,0,4"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Foreground="Black"
                                    Text="位置信息：" />

                                <!--  高位  -->
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="#7B1FA2"
                                        Kind="ArrowUpBold" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="检测位置(H)" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[5].Value, StringFormat='0x{0:X4}'}" />
                                </Grid>

                                <!--  低位  -->
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="#7B1FA2"
                                        Kind="ArrowDownBold" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="检测位置(L)" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[6].Value, StringFormat='0x{0:X4}'}" />
                                </Grid>

                                <!--  合并值  -->
                                <Grid
                                    Margin="8,8,8,4"
                                    Background="#444444"
                                    Opacity="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,10,0"
                                        VerticalAlignment="Center"
                                        Foreground="#8BC34A"
                                        Kind="AxisArrow" />

                                    <TextBlock
                                        x:Name="RAxisPosition"
                                        Grid.Column="1"
                                        HorizontalAlignment="Left"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Foreground="#FFEB3B"
                                        Style="{StaticResource StatusValueStyle}"
                                        ToolTip="{Binding RAxisStep, StringFormat='原始步进值: {0}'}">
                                        <TextBlock.Text>
                                            <MultiBinding StringFormat="步进：{0} Step&#x0a;距离：{1:F2} mm">
                                                <Binding Path="RAxisStep" />
                                                <Binding Path="RAxisLength" />
                                            </MultiBinding>
                                        </TextBlock.Text>
                                        <TextBlock.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem
                                                    Command="{Binding CopyCurrentAxisValueCommand}"
                                                    CommandParameter="{Binding PlacementTarget.Text, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                                    Header="复制当前轴值" />
                                                <MenuItem Command="{Binding CopyAllAxisPositionsCommand}" Header="复制所有轴值" />
                                            </ContextMenu>
                                        </TextBlock.ContextMenu>
                                    </TextBlock>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!--  Z轴信息  -->
                        <materialDesign:Card Grid.Column="2" Style="{StaticResource AxisCardStyle}">
                            <StackPanel>
                                <TextBlock Style="{StaticResource AxisLabelStyle}" Text="Z轴升降" />

                                <!--  报警错误码  -->
                                <Grid Margin="0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="{Binding RobotAlarmRegisters[2].Value, Converter={StaticResource ValueToColorConverter}, ConverterParameter=0}"
                                        Kind="AlertCircleOutline" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="报警错误码" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Foreground="{Binding RobotAlarmRegisters[2].Value, Converter={StaticResource ValueToColorConverter}, ConverterParameter=0}"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[2].Value, StringFormat='0x{0:X4}'}"
                                        ToolTip="{Binding ZAxisErrorInfo}">
                                        <TextBlock.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem Command="{Binding CopyCurrentErrorInfoCommand}" Header="复制当前轴错误信息">
                                                    <MenuItem.CommandParameter>
                                                        <MultiBinding Converter="{StaticResource MultiCommandParameterConverter}">
                                                            <Binding Path="PlacementTarget.Text" RelativeSource="{RelativeSource AncestorType=ContextMenu}" />
                                                            <Binding Source="Z轴" />
                                                        </MultiBinding>
                                                    </MenuItem.CommandParameter>
                                                </MenuItem>
                                                <MenuItem Command="{Binding CopyAllAxisErrorInfoCommand}" Header="复制所有轴错误信息" />
                                            </ContextMenu>
                                        </TextBlock.ContextMenu>
                                    </TextBlock>
                                </Grid>

                                <!--  分隔线  -->
                                <Separator Margin="0,4" Background="#444444" />

                                <!--  位置信息  -->
                                <TextBlock
                                    Margin="0,8,0,4"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Foreground="Black"
                                    Text="位置信息：" />

                                <!--  高位  -->
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="#7B1FA2"
                                        Kind="ArrowUpBold" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="检测位置(H)" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[7].Value, StringFormat='0x{0:X4}'}" />
                                </Grid>

                                <!--  低位  -->
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        VerticalAlignment="Center"
                                        Foreground="#7B1FA2"
                                        Kind="ArrowDownBold" />

                                    <TextBlock
                                        Grid.Column="1"
                                        Style="{StaticResource StatusLabelStyle}"
                                        Text="检测位置(L)" />

                                    <TextBlock
                                        Grid.Column="2"
                                        Style="{StaticResource StatusValueStyle}"
                                        Text="{Binding RobotAlarmRegisters[8].Value, StringFormat='0x{0:X4}'}" />
                                </Grid>

                                <!--  合并值  -->
                                <Grid
                                    Margin="8,8,8,4"
                                    Background="#444444"
                                    Opacity="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,10,0"
                                        VerticalAlignment="Center"
                                        Foreground="#8BC34A"
                                        Kind="AxisArrow" />

                                    <TextBlock
                                        x:Name="ZAxisPosition"
                                        Grid.Column="1"
                                        HorizontalAlignment="Left"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Foreground="#FFEB3B"
                                        Style="{StaticResource StatusValueStyle}"
                                        ToolTip="{Binding ZAxisStep, StringFormat='原始步进值: {0}'}">
                                        <TextBlock.Text>
                                            <MultiBinding StringFormat="步进：{0} Step&#x0a;高度：{1:F2} mm">
                                                <Binding Path="ZAxisStep" />
                                                <Binding Path="ZAxisHeight" />
                                            </MultiBinding>
                                        </TextBlock.Text>
                                        <TextBlock.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem
                                                    Command="{Binding CopyCurrentAxisValueCommand}"
                                                    CommandParameter="{Binding PlacementTarget.Text, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                                    Header="复制当前轴值" />
                                                <MenuItem Command="{Binding CopyAllAxisPositionsCommand}" Header="复制所有轴值" />
                                            </ContextMenu>
                                        </TextBlock.ContextMenu>
                                    </TextBlock>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!--  Pin Search结果信息  -->
                        <materialDesign:Card
                            Grid.Row="1"
                            Grid.ColumnSpan="3"
                            Style="{StaticResource AxisCardStyle}">
                            <StackPanel>
                                <TextBlock Style="{StaticResource AxisLabelStyle}" Text="Pin Search点位信息">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Command="{Binding CopyAllPinSearchValuesCommand}" Header="复制所有Pin Search值" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*" />
                                        <ColumnDefinition Width="2*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!--  P1点位信息  -->
                                    <StackPanel Grid.Column="0" Margin="4,0">
                                        <TextBlock
                                            Margin="0,8,0,4"
                                            FontSize="18"
                                            FontWeight="Medium"
                                            Foreground="Black"
                                            Text="P1点位信息：" />

                                        <!--  P1位置值（水平紧凑布局）  -->
                                        <Grid
                                            Margin="8,8,8,4"
                                            Background="#444444"
                                            Opacity="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>

                                            <!--  高位16进制值  -->
                                            <TextBlock
                                                Grid.Column="0"
                                                Margin="8,4"
                                                FontSize="16"
                                                Foreground="White"
                                                Text="H:" />
                                            <TextBlock
                                                Grid.Column="1"
                                                Margin="0,4,12,4"
                                                d:Text="{Binding RobotAlarmRegisters[9].Value, StringFormat='0x{0:X4}'}"
                                                FontSize="16"
                                                Foreground="#FFEB3B"
                                                Text="{Binding PinSearchP1_H, StringFormat='0x{0:X4}'}" />

                                            <!--  低位16进制值  -->
                                            <TextBlock
                                                Grid.Column="2"
                                                Margin="8,4"
                                                FontSize="16"
                                                Foreground="White"
                                                Text="L:" />
                                            <TextBlock
                                                Grid.Column="3"
                                                Margin="0,4,12,4"
                                                d:Text="{Binding RobotAlarmRegisters[10].Value, StringFormat='0x{0:X4}'}"
                                                FontSize="16"
                                                Foreground="#FFEB3B"
                                                Text="{Binding PinSearchP1_L, StringFormat='0x{0:X4}'}" />

                                            <!--  32位合并值  -->
                                            <TextBlock
                                                Grid.Column="4"
                                                Margin="8,4"
                                                FontSize="16"
                                                Foreground="White"
                                                Text="步进:" />
                                            <TextBlock
                                                Grid.Column="5"
                                                Margin="0,4,12,4"
                                                HorizontalAlignment="Right"
                                                d:Text="{Binding RobotAlarmRegisters[9].Combinevalue}"
                                                FontSize="16"
                                                FontWeight="Bold"
                                                Foreground="#FFEB3B"
                                                Text="{Binding PinSearchP1Value}">
                                                <TextBlock.ContextMenu>
                                                    <ContextMenu>
                                                        <MenuItem
                                                            Command="{Binding CopyCurrentAxisValueCommand}"
                                                            CommandParameter="{Binding PlacementTarget.Text, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                                            Header="复制当前值" />
                                                        <MenuItem Command="{Binding CopyAllPinSearchValuesCommand}" Header="复制所有Pin Search值" />
                                                    </ContextMenu>
                                                </TextBlock.ContextMenu>
                                            </TextBlock>
                                        </Grid>
                                    </StackPanel>

                                    <!--  P2点位信息  -->
                                    <StackPanel Grid.Column="1" Margin="4,0">
                                        <TextBlock
                                            Margin="0,8,0,4"
                                            FontSize="18"
                                            FontWeight="Medium"
                                            Foreground="Black"
                                            Text="P2点位信息：" />

                                        <!--  P2位置值（水平紧凑布局）  -->
                                        <Grid
                                            Margin="8,8,8,4"
                                            Background="#444444"
                                            Opacity="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>

                                            <!--  高位16进制值  -->
                                            <TextBlock
                                                Grid.Column="0"
                                                Margin="8,4"
                                                FontSize="16"
                                                Foreground="White"
                                                Text="H:" />
                                            <TextBlock
                                                Grid.Column="1"
                                                Margin="0,4,12,4"
                                                d:Text="{Binding RobotAlarmRegisters[11].Value, StringFormat='0x{0:X4}'}"
                                                FontSize="16"
                                                Foreground="#FFEB3B"
                                                Text="{Binding PinSearchP2_H, StringFormat='0x{0:X4}'}" />

                                            <!--  低位16进制值  -->
                                            <TextBlock
                                                Grid.Column="2"
                                                Margin="8,4"
                                                FontSize="16"
                                                Foreground="White"
                                                Text="L:" />
                                            <TextBlock
                                                Grid.Column="3"
                                                Margin="0,4,12,4"
                                                d:Text="{Binding RobotAlarmRegisters[12].Value, StringFormat='0x{0:X4}'}"
                                                FontSize="16"
                                                Foreground="#FFEB3B"
                                                Text="{Binding PinSearchP2_L, StringFormat='0x{0:X4}'}" />

                                            <!--  32位合并值  -->
                                            <TextBlock
                                                Grid.Column="4"
                                                Margin="8,4"
                                                FontSize="16"
                                                Foreground="White"
                                                Text="步进:" />
                                            <TextBlock
                                                Grid.Column="5"
                                                Margin="0,4,12,4"
                                                HorizontalAlignment="Right"
                                                d:Text="{Binding RobotAlarmRegisters[11].Combinevalue}"
                                                FontSize="16"
                                                FontWeight="Bold"
                                                Foreground="#FFEB3B"
                                                Text="{Binding PinSearchP2Value}">
                                                <TextBlock.ContextMenu>
                                                    <ContextMenu>
                                                        <MenuItem
                                                            Command="{Binding CopyCurrentAxisValueCommand}"
                                                            CommandParameter="{Binding PlacementTarget.Text, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                                            Header="复制当前值" />
                                                        <MenuItem Command="{Binding CopyAllPinSearchValuesCommand}" Header="复制所有Pin Search值" />
                                                    </ContextMenu>
                                                </TextBlock.ContextMenu>
                                            </TextBlock>
                                        </Grid>
                                    </StackPanel>

                                    <!--  Pin Search 计算结果值  Smooth、Nose 计数结果值应该按顺序更新，然后方法改了异步，这2个做完了才一起更新，要求先做完，就立即更新  -->
                                    <StackPanel Grid.Column="2" Margin="4,0">
                                        <TextBlock
                                            Margin="0,8,0,4"
                                            FontSize="18"
                                            FontWeight="Medium"
                                            Foreground="Black">
                                            <Run Text="Smooth：" />
                                            <Run Text="{Binding McuCmdService.SmoothBasePinSearchValue, Mode=OneWay}" />
                                        </TextBlock>

                                        <TextBlock
                                            Margin="0,8,0,4"
                                            FontSize="18"
                                            FontWeight="Medium"
                                            Foreground="Black">
                                            <Run Text="    Nose：" />
                                            <Run Text="{Binding McuCmdService.NoseBasePinSearchValue, Mode=OneWay}" />
                                        </TextBlock>
                                        <ToggleButton
                                            Margin="0,5,0,0"
                                            HorizontalAlignment="Left"
                                            Command="{Binding UpdateShuttleSubsystemStatusCommand}"
                                            Content="Shuttle状态解析测试"
                                            Style="{StaticResource ToggleButtonInfo.Small}" />
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Border>
            </GroupBox>
        </Expander>

        <!--  Robot位置展示结果信息  -->
        <materialDesign:Card
            Grid.Row="2"
            Grid.ColumnSpan="99"
            Margin="8,8,8,8"
            Padding="16"
            Style="{StaticResource AxisCardStyle}">
            <StackPanel>
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBlock
                        Grid.Column="0"
                        Foreground="#3F51B5"
                        Style="{StaticResource AxisLabelStyle}"
                        Text="实时状态表" />

                    <materialDesign:PackIcon
                        Grid.Column="1"
                        Width="24"
                        Height="24"
                        VerticalAlignment="Center"
                        Foreground="#3F51B5"
                        Kind="TableLarge" />
                </Grid>

                <!--  搜索和刷新区域  -->
                <Grid Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!--  设备类型过滤  -->
                    <ComboBox
                        Grid.Column="0"
                        Width="180"
                        Margin="5,0,8,0"
                        hc:InfoElement.Title="设备类型："
                        hc:InfoElement.TitlePlacement="Left"
                        hc:TitleElement.HorizontalAlignment="Left"
                        hc:TitleElement.TitleWidth="70"
                        Cursor="Hand"
                        DisplayMemberPath="DisplayName"
                        ItemsSource="{Binding DeviceTypeOptions}"
                        SelectedValue="{Binding SelectedDeviceType, UpdateSourceTrigger=PropertyChanged}"
                        SelectedValuePath="DeviceType"
                        Style="{StaticResource ComboBoxExtend}" />

                    <!--  搜索框  -->
                    <TextBox
                        Grid.Column="1"
                        Margin="5,0,8,0"
                        hc:InfoElement.Placeholder="模糊查询过滤"
                        hc:InfoElement.Title="过滤查询："
                        hc:InfoElement.TitlePlacement="Left"
                        hc:TitleElement.HorizontalAlignment="Left"
                        hc:TitleElement.TitleWidth="80"
                        materialDesign:HintAssist.Hint="输入关键字过滤..."
                        materialDesign:TextFieldAssist.UnderlineBrush="#3F51B5"
                        FontSize="14"
                        Foreground="#212121"
                        Style="{StaticResource TextBoxExtend}"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />

                    <!--  复制按钮  -->
                    <Button
                        Grid.Column="2"
                        Margin="0,0,5,0"
                        Background="#3F51B5"
                        Command="{Binding CopyStatusInfoCommand}"
                        Foreground="White"
                        ToolTip="复制状态信息">
                        <materialDesign:PackIcon Kind="ContentCopy" />
                    </Button>

                    <!--  刷新按钮  -->
                    <Button
                        Grid.Column="3"
                        Background="#3F51B5"
                        Command="{Binding UpdateRobotSubsystemStatusCommand}"
                        Content="刷新状态"
                        Foreground="White"
                        Style="{StaticResource MaterialDesignRaisedButton}" />

                    <!--  Chamber状态更新按钮  -->
                    <!--<Button Grid.Column="4" Margin="5,0,0,0" Background="#4CAF50" Command="{Binding UpdateChamberSubsystemStatusCommand}" Content="更新Chamber" Foreground="White" Style="{StaticResource MaterialDesignRaisedButton}" />-->

                    <!--
                        是否手动触发状态信号：
                        手动触发启用后参数状态就不会在每次Robot状态RTZ 3个轴值更新时自动调用计算位置状态等，
                        改为在状态表格中需要手动点击或者针对枚举手动选，修改触发状态信号修改，用与模拟调试状态表格参数值
                    -->
                    <StackPanel
                        Grid.Column="4"
                        Margin="5"
                        Orientation="Horizontal">
                        <TextBlock Style="{StaticResource TextBlockDefault}" Text="手动触发：" />
                        <ToggleButton
                            Grid.Column="3"
                            Width="40"
                            Margin="5,0,0,0"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            d:IsChecked="True"
                            d:IsEnabled="True"
                            hc:InfoElement.Title="手动触发"
                            hc:InfoElement.TitlePlacement="Left"
                            hc:TitleElement.Foreground="#3F51B5"
                            hc:TitleElement.HorizontalAlignment="Left"
                            hc:TitleElement.TitleWidth="80"
                            IsChecked="{Binding TriggerStatusSingalByHand}"
                            Style="{StaticResource ToggleButtonSwitch}"
                            ToolTip="切换手动触发" />
                    </StackPanel>
                </Grid>

                <Border
                    Padding="0"
                    BorderBrush="#3F51B5"
                    BorderThickness="1"
                    CornerRadius="4">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  批量操作按钮  -->
                        <StackPanel
                            Grid.Row="0"
                            Margin="8,8,8,4"
                            Orientation="Horizontal"
                            Visibility="{Binding TriggerStatusSingalByHand, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button
                                Margin="0,0,8,0"
                                Padding="12,4"
                                Command="{Binding UpdateAllPropertiesCommand}"
                                Content="全部更新"
                                Style="{StaticResource ButtonPrimary}" />
                            <Button
                                Padding="12,4"
                                Command="{Binding RestoreAllPropertiesCommand}"
                                Content="全部恢复"
                                Style="{StaticResource ButtonWarning}" />
                        </StackPanel>

                        <DataGrid
                            Grid.Row="1"
                            MaxHeight="500"
                            AlternatingRowBackground="#E0E0E0"
                            AutoGenerateColumns="False"
                            Background="#FFFFFF"
                            BorderBrush="Transparent"
                            BorderThickness="0"
                            CanUserSortColumns="True"
                            GridLinesVisibility="All"
                            HeadersVisibility="Column"
                            IsReadOnly="False"
                            ItemsSource="{Binding StatusProperties}"
                            RowHeight="35"
                            ScrollViewer.CanContentScroll="True"
                            ScrollViewer.HorizontalScrollBarVisibility="Auto"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            VirtualizingPanel.IsVirtualizing="True"
                            VirtualizingPanel.VirtualizationMode="Recycling">
                            <DataGrid.Resources>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#3F51B5" />
                                    <Setter Property="Foreground" Value="White" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Padding" Value="8,4" />
                                </Style>
                                <Style TargetType="DataGridCell">
                                    <Setter Property="Padding" Value="8,4" />
                                    <Setter Property="Foreground" Value="#212121" />
                                    <Setter Property="VerticalContentAlignment" Value="Center" />
                                    <Setter Property="HorizontalContentAlignment" Value="Left" />
                                    <Style.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#90CAF9" />
                                            <Setter Property="Foreground" Value="#212121" />
                                            <Setter Property="BorderBrush" Value="Transparent" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                                <Style TargetType="DataGridRow">
                                    <Style.Triggers>
                                        <!--  已修改行的背景色  -->
                                        <DataTrigger Binding="{Binding IsModified}" Value="True">
                                            <Setter Property="Background" Value="#FFF9C4" />
                                        </DataTrigger>
                                        <!--  null值的警告色  -->
                                        <DataTrigger Binding="{Binding IsNullValue}" Value="True">
                                            <Setter Property="Background" Value="#FFECB3" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.Resources>
                            <DataGrid.Columns>
                                <!--  设备类型列  -->
                                <DataGridTextColumn
                                    Width="100"
                                    Binding="{Binding DeviceType}"
                                    Header="设备类型"
                                    IsReadOnly="True">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="VerticalAlignment" Value="Center" />
                                            <Setter Property="HorizontalAlignment" Value="Left" />
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!--  参数名称列  -->
                                <DataGridTextColumn
                                    Width="200"
                                    Binding="{Binding Name}"
                                    Header="参数名称"
                                    IsReadOnly="True"
                                    SortDirection="Ascending">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="VerticalAlignment" Value="Center" />
                                            <Setter Property="HorizontalAlignment" Value="Left" />
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!--  编辑控件列  -->
                                <DataGridTemplateColumn Width="150" Header="编辑值">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <!--  自动模式下显示只读文本  -->
                                                <TextBlock
                                                    Margin="4"
                                                    VerticalAlignment="Center"
                                                    Text="{Binding Value}"
                                                    Visibility="{Binding DataContext.TriggerStatusSingalByHand, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />

                                                <!--  手动模式下的编辑控件  -->
                                                <Grid Visibility="{Binding DataContext.TriggerStatusSingalByHand, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BoolToVisibilityConverter}}">

                                                    <!--  Bool类型 - CheckBox  -->
                                                    <CheckBox
                                                        HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        IsChecked="{Binding EditValue, UpdateSourceTrigger=PropertyChanged}"
                                                        Visibility="{Binding TypeCategory, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Boolean}" />

                                                    <!--  枚举类型 - ComboBox  -->
                                                    <ComboBox
                                                        Margin="2"
                                                        VerticalAlignment="Center"
                                                        Cursor="Hand"
                                                        ItemsSource="{Binding EnumOptions}"
                                                        SelectedItem="{Binding EditValue, UpdateSourceTrigger=PropertyChanged}"
                                                        Visibility="{Binding TypeCategory, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Enum}" />

                                                    <!--  数值和字符串类型 - TextBox  -->
                                                    <TextBox
                                                        Margin="2"
                                                        VerticalAlignment="Center"
                                                        Text="{Binding EditValue, UpdateSourceTrigger=PropertyChanged}"
                                                        Visibility="{Binding TypeCategory, Converter={StaticResource MultiEnumToVisibilityConverter}, ConverterParameter='Numeric,String'}" />
                                                </Grid>
                                            </Grid>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  更新值按钮列  -->
                                <DataGridTemplateColumn Width="60" Header="更新">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button
                                                Padding="4,2"
                                                Command="{Binding DataContext.UpdateSinglePropertyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Content="更新"
                                                FontSize="10"
                                                IsEnabled="{Binding IsModified}"
                                                Style="{StaticResource ButtonSuccess}"
                                                Visibility="{Binding DataContext.TriggerStatusSingalByHand, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BoolToVisibilityConverter}}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  恢复值按钮列  -->
                                <DataGridTemplateColumn Width="60" Header="恢复">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button
                                                Padding="4,2"
                                                Command="{Binding DataContext.RestoreSinglePropertyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Content="恢复"
                                                FontSize="10"
                                                IsEnabled="{Binding IsModified}"
                                                Style="{StaticResource ButtonWarning}"
                                                Visibility="{Binding DataContext.TriggerStatusSingalByHand, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BoolToVisibilityConverter}}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>