﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

//using Zishan.Robot.Shared.Models;
//using Zishan.Robot.Shared.Models.Recipe.Plc;

namespace Zishan.SS200.Cmd.Models.Shared
{
    /// <summary>
    /// 共享工序配方名列表信息，包括CH配方名列表、Cooling配方名列表、配方名列表，用于路径规划Run配方
    /// </summary>
    public class RecipeNames : BindableBase
    {
        /// <summary>
        /// CH配方名列表
        /// </summary>
        public ObservableCollection<RecipeKeyValue> ChRecipeNameList { get => _ChRecipeNameList; set => SetProperty(ref _ChRecipeNameList, value); }
        private ObservableCollection<RecipeKeyValue> _ChRecipeNameList;

        /// <summary>
        /// Cooling配方名列表
        /// </summary>
        public ObservableCollection<RecipeKeyValue> CoolingRecipeNameList { get => _CoolingRecipeNameList; set => SetProperty(ref _CoolingRecipeNameList, value); }
        private ObservableCollection<RecipeKeyValue> _CoolingRecipeNameList;

        /// <summary>
        /// 配方名列表
        /// </summary>
        public ObservableCollection<RecipeKeyValue> RecipeNameList { get => _RecipeNameList; set => SetProperty(ref _RecipeNameList, value); }
        private ObservableCollection<RecipeKeyValue> _RecipeNameList;

        /// <summary>
        /// 当前选中的配方名
        /// </summary>
        public RecipeKeyValue CurSelectedRecipeName { get => _CurSelectedRecipeName; set => SetProperty(ref _CurSelectedRecipeName, value); }
        private RecipeKeyValue _CurSelectedRecipeName;

        /// <summary>
        /// 当前选中的配方名 是否正在运作中
        /// </summary>
        public bool IsRunning { get => _IsRunning; set => SetProperty(ref _IsRunning, value); }
        private bool _IsRunning;

        /// <summary>
        /// 数据库中的流程配方存储信息
        /// </summary>
        public List<RecipeSeqInfo> DbRecipeSeqInfo
        {
            get => _DbRecipeSeqInfo;
            set
            {
                if (SetProperty(ref _DbRecipeSeqInfo, value))
                {
                    // 更新配方名列表
                    RecipeNameList.Clear();
                    int id = 0;
                    foreach (var recipeSeqInfo in DbRecipeSeqInfo)
                    {
                        RecipeNameList.Add(new RecipeKeyValue(id++, recipeSeqInfo.RecipeName));
                    }
                }
            }
        }
        private List<RecipeSeqInfo> _DbRecipeSeqInfo;

        public RecipeNames()
        {
            ChRecipeNameList = new ObservableCollection<RecipeKeyValue>();
            CoolingRecipeNameList = new ObservableCollection<RecipeKeyValue>();
            RecipeNameList = new ObservableCollection<RecipeKeyValue>();
        }
    }
}