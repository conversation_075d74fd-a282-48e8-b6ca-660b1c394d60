# Chamber 子系统状态解析功能

## 概述

本功能实现了根据 S200McuCmdService 中的 DI、DO 信息，通过 I/O 分布逻辑解析到 ChamberSubsystemStatus 的完整功能。

## 功能特性

### 已实现的状态解析

#### 1. 位置状态 (SP1-SP10)
- **SP1-SP3**: Slit Door 状态
  - SP1: PDI12=1 PDI13=0 → 打开
  - SP2: PDI12=0 PDI13=1 → 关闭  
  - SP3: PDI12=0 PDI13=0 → 开关之间

- **SP4-SP6**: Lift Pin 状态
  - SP4: PDI14=1 PDI15=0 → 上升
  - SP5: PDI14=0 PDI15=1 → 下降
  - SP6: PDI14=0 PDI15=0 → 上下之间

- **SP7-SP10**: 晶圆准备状态（组合状态）
  - SP7: SP1 AND SP5 → 准备接收（slit door 打开 + lift pin 下降）
  - SP8: SP1 AND SP4 → 准备输出（slit door 打开 + lift pin 上升）
  - SP9: SP2 AND SP4 → 已接收（slit door 关闭 + lift pin 上升）
  - SP10: SP2 AND SP5 → 输出（slit door 关闭 + lift pin 下降）

#### 2. 压力状态 (SP11-SP22)
- **SP11-SP12**: 腔体真空状态（基础版本，不包含模拟量）
  - 基于 PDI5 和 PDI6 的数字输入判断
  - 完整版本需要 PAI1 和 PPS6 参数

- **SP13-SP14**: 负载锁真空状态（预留接口）
  - 需要 PAI6 和 PPS7 参数

- **SP15-SP17**: ISO 阀门状态
  - SP15: PDI1=0 PDI2=1 → 打开
  - SP16: PDI1=1 PDI2=0 → 关闭
  - SP17: PDI1=1 PDI2=1 → 开关之间

- **SP18-SP20**: 节流阀状态
  - SP18: PDI13=0 PDI4=1 → 打开
  - SP19: PDI13=1 PDI4=0 → 关闭
  - SP20: PDI13=1 PDI4=1 → 开关之间

- **SP21-SP22**: 前级真空状态
  - SP21: PDI5=0 → 无真空
  - SP22: PDI5=1 → 有真空

#### 3. 气体状态 (SP23-SP32)
- **SP23-SP24**: CM 阀门状态
  - SP23: PDO9=0 → 打开
  - SP24: PDO9=1 → 关闭

- **SP25-SP32**: C1-C4 阀门状态
  - 类似 CM 阀门，基于对应的 PDO 输出

### 预留接口

#### 1. 触发状态 (MPS1-MPS2)
- MPS1: 无报警
- MPS2: 报警
- 需要实现报警状态检查逻辑

#### 2. 运行状态 (MPS3A-MPS5)
- MPS3A: 忙碌A（chamber slit door 动作中，或 robot 于 chamber 交互中）
- MPS3B: 忙碌B（lift pin 动作中）
- MPS4: 空闲（slit door/lift pin 不在动作中，或不在与 robot 交互中）
- MPS5: 处理中（recipe running，RF on）
- 需要实现设备运行状态、RF 状态检查逻辑

## 使用方法

### 1. 在 ViewModel 中调用

```csharp
// 手动更新 Chamber 状态
UpdateChamberSubsystemStatus(true);

// 或使用命令
OnUpdateChamberSubsystemStatusCommand.Execute(null);
```

### 2. 在其他类中使用

```csharp
// 创建辅助类实例
var coilHelper = new CoilStatusHelper(S200McuCmdService.Instance);

// 计算特定状态
var slitDoorStatus = coilHelper.CalculateSlitDoorStatus(EnuMcuDeviceType.ChamberA);
var waferReadyStatus = coilHelper.CalculateWaferReadyStatus(EnuMcuDeviceType.ChamberA);
var c1ValveStatus = coilHelper.CalculateC1ValveStatus(EnuMcuDeviceType.ChamberA);
```

## 技术细节

### 传感器逻辑
- **NPN 传感器**: 到位时为 0（false），未到位时为 1（true）
- **数字输出**: 阀门控制时 0=开，1=关

### 错误处理
- 所有状态计算方法都包含异常处理
- 发生错误时返回默认值，不影响系统稳定性
- 错误信息记录到日志中

### 扩展性
- 支持 ChamberA 和 ChamberB 设备类型
- 预留了模拟量输入的接口
- 可以轻松添加新的状态计算方法

## 待完善功能

1. **模拟量支持**: 需要实现 PAI1、PAI6 等模拟量输入的读取
2. **参数配置**: 需要集成 PPS6、PPS7 等参数的读取
3. **报警状态**: 需要实现 MPS1-MPS2 的报警检查逻辑
4. **运行状态**: 需要实现 MPS3A-MPS5 的运行状态检查逻辑
5. **RF 状态**: 需要集成 RF 设备状态检查

## 相关文件

- `Services/CoilStatusHelper.cs`: 核心状态计算逻辑
- `ViewModels/Dock/RobotStatusPanelViewModel.cs`: ViewModel 集成
- `Models/SS200/SubSystemStatus/Chamber/`: 状态枚举定义
- `Enums/SS200/IOInterface/Chamber/`: DI/DO 代码枚举
