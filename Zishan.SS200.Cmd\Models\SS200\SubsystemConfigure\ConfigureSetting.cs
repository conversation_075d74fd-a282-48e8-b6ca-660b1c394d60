﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure
{
    /// <summary>
    /// 配置参数模型，支持多种数据类型的配置值
    /// </summary>
    public class ConfigureSetting
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }

        /// <summary>
        /// 配置值，支持多种数据类型（int, string, bool，double等）
        /// </summary>
        public object Value { get; set; }

        public string Unit { get; set; }

        /// <summary>
        /// MCU RTZ轴类型代码
        /// </summary>
        public int AxisType { get; set; }

        /// <summary>
        /// 获取强类型的配置值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <returns>转换后的值</returns>
        public T GetValue<T>()
        {
            if (Value == null)
                return default(T);

            if (Value is T directValue)
                return directValue;

            try
            {
                return (T)Convert.ChangeType(Value, typeof(T));
            }
            catch
            {
                return default(T);
            }
        }

        /// <summary>
        /// 获取整数值（向后兼容）
        /// </summary>
        public int IntValue => GetValue<int>();

        /// <summary>
        /// 获取字符串值
        /// </summary>
        public string StringValue => GetValue<string>();

        /// <summary>
        /// 获取布尔值
        /// </summary>
        public bool BoolValue => GetValue<bool>();
    }
}