# 统一位置枚举类型使用指南

## 概述

为了解决不同位置枚举类型之间无法直接比较的问题，我们将所有相关的位置枚举统一为 `EnuLocationStationType`。这是一个根本性的解决方案，彻底消除了枚举类型不一致的问题。

## 问题背景

在机器人晶圆操作系统中，之前存在多个表示位置的枚举类型：

- ~~`EnuDestination`~~ - 已删除
- ~~`EnuTAxisDestination`~~ - 已删除
- ~~`EnuTRAxisCombinedPositionStatus`~~ - 已删除
- `EnuLocationStationType` - **统一的位置枚举**

这些枚举的值定义不一致，导致无法直接比较，例如：

```csharp
// 问题代码 - 无法直接比较
if (robotStatus.EnuTAxisSmoothDestination == stationType) // 编译错误
{
    // ...
}

// 之前的解决方案 - 字符串比较不可靠
if (robotStatus.EnuTAxisSmoothDestination.ToString() == stationType.ToString())
{
    // ...
}
```

## 解决方案

**统一所有位置枚举为 `EnuLocationStationType`**

现在所有位置相关的属性都使用统一的 `EnuLocationStationType` 枚举，可以直接进行比较：

### 1. 直接比较（推荐）

```csharp
using Zishan.SS200.Cmd.Enums.Basic;

// 判断T轴是否已到达指定位置 - 现在可以直接比较！
bool isAtLocation = robotStatus.EnuTAxisSmoothDestination == stationType;

// 判断Nose端是否已到达指定位置
bool isAtLocation = robotStatus.EnuTAxisNoseDestination == stationType;

// 判断TR轴组合位置
bool isAtLocation = robotStatus.EnuTAndRAxisSmoothExtendDestination == stationType;
```

### 2. 枚举值定义

```csharp
public enum EnuLocationStationType
{
    /// <summary>
    /// 无/未定义位置
    /// </summary>
    None = -1,

    /// <summary>
    /// 晶圆盒
    /// </summary>
    Cassette = 0,

    /// <summary>
    /// 工艺腔室A (原CHA)
    /// </summary>
    ChamberA = 1,

    /// <summary>
    /// 工艺腔室B (原CHB)
    /// </summary>
    ChamberB = 2,

    /// <summary>
    /// 冷却TOP (原CoolingChamber的一部分)
    /// </summary>
    CoolingTop = 3,

    /// <summary>
    /// 冷却BOTTOM (原CoolingChamber的一部分)
    /// </summary>
    CoolingBottom = 4
}
```

### 3. 特殊判断方法

```csharp
// 判断是否为冷却腔室
bool isCooling = (stationType == EnuLocationStationType.CoolingTop ||
                  stationType == EnuLocationStationType.CoolingBottom);

// 判断是否为工艺腔室
bool isProcess = (stationType == EnuLocationStationType.ChamberA ||
                  stationType == EnuLocationStationType.ChamberB);

// 判断是否为有效位置（非None）
bool isValidLocation = stationType != EnuLocationStationType.None;
```

## 统一后的枚举值

| EnuLocationStationType | 数值 | 说明 | 原对应枚举 |
|----------------------|-----|------|-----------|
| None | -1 | 无/未定义位置 | EnuTAxisDestination.None |
| Cassette | 0 | 晶圆盒位置 | EnuDestination.Cassette, EnuTAxisDestination.Cassette |
| ChamberA | 1 | 工艺腔室A | EnuDestination.CHA, EnuTAxisDestination.CHA |
| ChamberB | 2 | 工艺腔室B | EnuDestination.CHB, EnuTAxisDestination.CHB |
| CoolingTop | 3 | 冷却腔室顶部 | EnuDestination.CoolingChamber, EnuTAxisDestination.CoolingChamber |
| CoolingBottom | 4 | 冷却腔室底部 | EnuDestination.CoolingChamber, EnuTAxisDestination.CoolingChamber |

## 实际应用示例

### 在 RobotWaferOperationsExtensions 中的使用

```csharp
// 修改前的问题代码
if (SS200InterLockMain.Instance.SubsystemStatus.Robot.Status.EnuTAxisSmoothDestination.ToString() == stationType.ToString())
{
    // 字符串比较不可靠
}

// 修改后的正确代码 - 直接比较枚举值！
bool blHasToLocation = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status.EnuTAxisSmoothDestination == stationType;
if (blHasToLocation)
{
    UILogService.AddSuccessLog($"T轴已到达{stationType}位置，无需移动");
    return (true, $"T轴已到达{stationType}位置，无需移动");
}
```

### 在状态检查中的使用

```csharp
public bool CheckRobotPosition(EnuRobotEndType endType, EnuLocationStationType targetLocation)
{
    var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;

    switch (endType)
    {
        case EnuRobotEndType.Smooth:
            return robotStatus.EnuTAxisSmoothDestination == targetLocation;

        case EnuRobotEndType.Nose:
            return robotStatus.EnuTAxisNoseDestination == targetLocation;

        default:
            return false;
    }
}
```

### 在配置参数获取中的使用

```csharp
public int GetPositionParameter(EnuLocationStationType locationType)
{
    // 直接使用统一的枚举类型
    return locationType switch
    {
        EnuLocationStationType.ChamberA => GetCHAPosition(),
        EnuLocationStationType.ChamberB => GetCHBPosition(),
        EnuLocationStationType.Cassette => GetCassettePosition(),
        EnuLocationStationType.CoolingTop => GetCoolingTopPosition(),
        EnuLocationStationType.CoolingBottom => GetCoolingBottomPosition(),
        EnuLocationStationType.None => throw new ArgumentException("无效的位置类型: None"),
        _ => throw new ArgumentException($"不支持的位置类型: {locationType}")
    };
}
```

### 在Robot状态更新中的使用

```csharp
public void UpdateRobotPosition(EnuRobotEndType endType, EnuLocationStationType newLocation)
{
    var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;

    switch (endType)
    {
        case EnuRobotEndType.Smooth:
            robotStatus.EnuTAxisSmoothDestination = newLocation;
            robotStatus.EnuTAndRAxisSmoothExtendDestination = newLocation;
            break;

        case EnuRobotEndType.Nose:
            robotStatus.EnuTAxisNoseDestination = newLocation;
            robotStatus.EnuTAndRAxisNoseExtendDestination = newLocation;
            break;
    }

    UILogService.AddLog($"{endType}端已移动到{newLocation}位置");
}
```

## 注意事项

1. **None值处理**：`EnuLocationStationType.None` 表示未定义或无效位置，在业务逻辑中需要适当处理

2. **冷却腔室区分**：现在有 `CoolingTop` 和 `CoolingBottom` 两个独立的枚举值，可以精确区分冷却腔室的上下位置

3. **类型安全**：所有位置比较现在都是编译时类型安全的，不会出现运行时类型错误

4. **性能优化**：直接枚举比较比字符串比较性能更好，也更可靠

## 迁移指南

如果您的代码中还有使用已删除枚举的地方，请按以下方式迁移：

```csharp
// 旧代码
EnuTAxisDestination.CHA → EnuLocationStationType.ChamberA
EnuTAxisDestination.CHB → EnuLocationStationType.ChamberB
EnuTAxisDestination.Cassette → EnuLocationStationType.Cassette
EnuTAxisDestination.CoolingChamber → EnuLocationStationType.CoolingTop 或 CoolingBottom
EnuTAxisDestination.None → EnuLocationStationType.None

// 旧代码
EnuDestination.CHA → EnuLocationStationType.ChamberA
EnuDestination.CHB → EnuLocationStationType.ChamberB
EnuDestination.Cassette → EnuLocationStationType.Cassette
EnuDestination.CoolingChamber → EnuLocationStationType.CoolingTop 或 CoolingBottom
```

## 总结

通过统一所有位置枚举为 `EnuLocationStationType`，我们彻底解决了不同位置枚举类型之间无法直接比较的问题。这是一个根本性的解决方案，具有以下优势：

- **类型安全**：编译时就能发现类型不匹配问题
- **代码简洁**：直接使用 `==` 进行比较，无需转换方法
- **易于维护**：只有一个枚举类型需要维护
- **性能更好**：直接枚举比较比字符串比较更高效
- **扩展性强**：添加新位置只需在一个枚举中添加即可
