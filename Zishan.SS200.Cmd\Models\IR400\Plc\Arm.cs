﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400.Plc
{
    /// <summary>
    /// RobotArmNose 类表示机械臂, 用于移动 Wafer
    /// </summary>
    public class Arm : BaseContainer
    {
        private string _strMsg = string.Empty;

        /// <summary>
        /// 机械臂抓取面类型：A面 Nose端、B面 Smooth端
        /// </summary>
        public EnuArmFetchSide ArmFetchSide { get; set; }

        /// <summary>
        /// 构造指定的机械臂
        /// </summary>
        /// <param name="enuArmFetchSide"></param>
        public Arm(EnuArmFetchSide enuArmFetchSide)
        {
            ArmFetchSide = enuArmFetchSide;
        }
    }
}