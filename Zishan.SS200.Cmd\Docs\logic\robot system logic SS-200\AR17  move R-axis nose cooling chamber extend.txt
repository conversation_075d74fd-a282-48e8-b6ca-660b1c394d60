﻿AR17 
move R-axis nose cooling chamber extend
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			cooling chamber trigger status review
MCS1~MCS2
				MCS1 normal
					cooling chamber run status review
MCS3~MCS5
						MCS3 busy
							RA35 ALARM
						MCS4 idle
							slide out sensor installation review
SPS11
								SPS11=Y
									slide out sensor status reiew
										DI19=0 DI20=0
											Robot T-axis position review
RS7 or others position
												RS7
													Robot Z-axis position review
RS25/RS26/RS31/RS32 or others position
														RS25 or RS26 or RS31 or RS32
															AR20-RP16
																slide out sensor status reiew
																	DI19=0 DI20=0
																		command done
																	DI19=0 DI20=1
																		RA20 ALARM
																	DI19=1 DI20=0
																		RA19 ALARM
																	DI19=1 DI20=1
																		RA21 ALARM
														others position
															RA10 ALARM
																confirm
																	AR20-RP16
																		slide out sensor status reiew
																			DI19=0 DI20=0
																				command done
																			DI19=0 DI20=1
																				RA20 ALARM
																			DI19=1 DI20=0
																				RA19 ALARM
																			DI19=1 DI20=1
																				RA21 ALARM
																cancel
																	command cancel
												others position
													RA4 ALARM
														confirm
															Robot Z-axis position review
RS25/RS26/RS31/RS32 or others position
																RS25 or RS26 or RS31 or RS32
																	AR20-RP16
																		slide out sensor status reiew
																			DI19=0 DI20=0
																				command done
																			DI19=0 DI20=1
																				RA20 ALARM
																			DI19=1 DI20=0
																				RA19 ALARM
																			DI19=1 DI20=1
																				RA21 ALARM
																others position
																	RA10 ALARM
																		confirm
																			AR20-RP16
																				slide out sensor status reiew
																					DI19=0 DI20=0
																						command done
																					DI19=0 DI20=1
																						RA20 ALARM
																					DI19=1 DI20=0
																						RA19 ALARM
																					DI19=1 DI20=1
																						RA21 ALARM
																		cancel
																			command cancel
														cancel
															command cancel
										DI19=0 DI20=1
											RA20 ALARM
										DI19=1 DI20=0
											RA19 ALARM
										DI19=1 DI20=1
											RA21 ALARM
								SPS11=N
									Robot T-axis position review
RS7 or others position
										RS7
											Robot Z-axis position review
RS25/RS26/RS31/RS32 or others position
												RS25 or RS26 or RS31 or RS32
													AR20-RP16
														slide out sensor status reiew
															DI19=0 DI20=0
																command done
															DI19=0 DI20=1
																RA20 ALARM
															DI19=1 DI20=0
																RA19 ALARM
															DI19=1 DI20=1
																RA21 ALARM
												others position
													RA10 ALARM
														confirm
															AR20-RP16
																slide out sensor status reiew
																	DI19=0 DI20=0
																		command done
																	DI19=0 DI20=1
																		RA20 ALARM
																	DI19=1 DI20=0
																		RA19 ALARM
																	DI19=1 DI20=1
																		RA21 ALARM
														cancel
															command cancel
										others position
											RA4 ALARM
												confirm
													Robot Z-axis position review
RS25/RS26/RS31/RS32 or others position
														RS25 or RS26 or RS31 or RS32
															AR20-RP16
																slide out sensor status reiew
																	DI19=0 DI20=0
																		command done
																	DI19=0 DI20=1
																		RA20 ALARM
																	DI19=1 DI20=0
																		RA19 ALARM
																	DI19=1 DI20=1
																		RA21 ALARM
														others position
															RA10 ALARM
																confirm
																	AR20-RP16
																		slide out sensor status reiew
																			DI19=0 DI20=0
																				command done
																			DI19=0 DI20=1
																				RA20 ALARM
																			DI19=1 DI20=0
																				RA19 ALARM
																			DI19=1 DI20=1
																				RA21 ALARM
																cancel
																	command cancel
												cancel
													command cancel
						MCS5 processing
							RA35 ALARM
				MCS2 alarm
					RA34 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM