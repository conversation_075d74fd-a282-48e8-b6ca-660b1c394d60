﻿using Prism.Mvvm;
using <PERSON><PERSON>an.SS200.Cmd.Enums;
using <PERSON>ishan.SS200.Cmd.Models.IR400;

namespace Zishan.SS200.Cmd.Models
{
    public class BaseContainer : BindableBase
    {
        /// <summary>
        /// 腔标题
        /// </summary>
        public EnuChamberName Name { get; set; }

        /// <summary>
        /// 腔体总容量
        /// </summary>
        public int Capacity { get; set; }

        /// <summary>
        /// 左边Wafer
        /// </summary>
        public WaferAction LeftWaferAction { get => _LeftWaferAction; set => SetProperty(ref _LeftWaferAction, value); }
        private WaferAction _LeftWaferAction;

        /// <summary>
        /// 右边Wafer
        /// </summary>
        public WaferAction RightWaferActon { get => _RightWaferActon; set => SetProperty(ref _RightWaferActon, value); }
        private WaferAction _RightWaferActon;

        ///// <summary>
        ///// 工作模式【子类派生】
        ///// </summary>
        //public EnuMode Mode { get => _Mode; set => SetProperty(ref _Mode, value); }
        //private EnuMode _Mode;

        /// <summary>
        /// 工作状态
        /// </summary>
        public EnuWorkStatus WorkStatus { get => _WorkStauts; set => SetProperty(ref _WorkStauts, value); }
        private EnuWorkStatus _WorkStauts;

        /// <summary>
        /// 工作Step进度百分比数
        /// </summary>
        public int StepProgressPercentage { get => _StepProgressPercentage; set => SetProperty(ref _StepProgressPercentage, value); }
        private int _StepProgressPercentage;

        /// <summary>
        /// 报警状态
        /// </summary>
        public EnuWarnStatus WarnStatus { get => _WarnStatus; set => SetProperty(ref _WarnStatus, value); }
        private EnuWarnStatus _WarnStatus;

        /// <summary>
        /// LoadLock门状态:是否已经打开
        /// </summary>
        public bool IsOpen { get => _IsOpen; set => SetProperty(ref _IsOpen, value); }
        private bool _IsOpen;

        public BaseContainer()
        {
        }
    }
}