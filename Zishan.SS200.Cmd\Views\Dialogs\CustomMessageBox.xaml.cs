﻿using System.Windows;
using System.Windows.Input;

namespace Zishan.SS200.Cmd.Views.Dialogs
{
    public partial class CustomMessageBox : Window
    {
        public enum CustomMessageBoxResult
        {
            Retry,
            Exit,
            Continue
        }

        public CustomMessageBoxResult Result { get; private set; }

        public CustomMessageBox(string message, string title = "CustomMessageBox", Visibility visibility = Visibility.Collapsed)
        {
            InitializeComponent();
            ContinueButton.Visibility = visibility;
            CustomTitle.Text = title;
            MessageTextBlock.Text = message;
        }

        private void RetryButton_Click(object sender, RoutedEventArgs e)
        {
            Result = CustomMessageBoxResult.Retry;
            this.DialogResult = true;
        }

        private void ExitButton_Click(object sender, RoutedEventArgs e)
        {
            Result = CustomMessageBoxResult.Exit;
            this.DialogResult = true;
        }

        private void ContinueButton_Click(object sender, RoutedEventArgs e)
        {
            Result = CustomMessageBoxResult.Continue;
            this.DialogResult = true;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
        }

        private void TitleBar_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
            {
                this.DragMove();
            }
        }
    }
}