using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace $NAMESPACE$
{
    /// <summary>
    /// $CLASS_NAME$ 服务接口
    /// </summary>
    public interface I$CLASS_NAME$
    {
        /// <summary>
        /// 示例异步方法
        /// </summary>
        /// <param name="parameter">参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        Task<bool> ExecuteOperationAsync(string parameter, CancellationToken cancellationToken = default);

/// <summary>
/// 示例同步方法
/// </summary>
/// <param name="parameter">参数</param>
/// <returns>操作结果</returns>
string GetData(string parameter);

/// <summary>
/// 服务状态事件
/// </summary>
event EventHandler<ServiceStatusEventArgs> StatusChanged;
}

/// <summary>
/// $CLASS_NAME$ 服务实现
/// </summary>
public class $CLASS_NAME$ : I$CLASS_NAME$
    {
        private readonly ILogger<$CLASS_NAME$> _logger;

/// <summary>
/// 服务状态事件
/// </summary>
public event EventHandler<ServiceStatusEventArgs> StatusChanged;

/// <summary>
/// 构造函数
/// </summary>
/// <param name="logger">日志服务</param>
public $CLASS_NAME$(ILogger<$CLASS_NAME$> logger)
        {
            _logger = logger;
_logger.LogInformation("$CLASS_NAME$ 服务已创建");
        }
        
        /// <summary>
        /// 示例异步方法实现
        /// </summary>
        /// <param name="parameter">参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public async Task<bool> ExecuteOperationAsync(string parameter, CancellationToken cancellationToken = default)
{
    if (string.IsNullOrEmpty(parameter))
    {
        _logger.LogWarning("参数为空");
        return false;
    }

    try
    {
        _logger.LogInformation("开始执行操作: {Parameter}", parameter);
        OnStatusChanged(new ServiceStatusEventArgs { Status = ServiceStatus.Processing });

        // 模拟异步操作
        await Task.Delay(1000, cancellationToken);

        // 检查取消状态
        if (cancellationToken.IsCancellationRequested)
        {
            _logger.LogInformation("操作已取消");
            OnStatusChanged(new ServiceStatusEventArgs { Status = ServiceStatus.Cancelled });
            return false;
        }

        _logger.LogInformation("操作完成");
        OnStatusChanged(new ServiceStatusEventArgs { Status = ServiceStatus.Completed });
        return true;
    }
    catch (OperationCanceledException)
    {
        _logger.LogInformation("操作已取消");
        OnStatusChanged(new ServiceStatusEventArgs { Status = ServiceStatus.Cancelled });
        return false;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "操作执行失败");
        OnStatusChanged(new ServiceStatusEventArgs
        {
            Status = ServiceStatus.Error,
            ErrorMessage = ex.Message
        });
        return false;
    }
}

/// <summary>
/// 示例同步方法实现
/// </summary>
/// <param name="parameter">参数</param>
/// <returns>操作结果</returns>
public string GetData(string parameter)
{
    try
    {
        _logger.LogInformation("获取数据: {Parameter}", parameter);
        // 实现获取数据的逻辑
        return $"处理结果: {parameter}";
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "获取数据失败");
        return string.Empty;
    }
}

/// <summary>
/// 触发状态变更事件
/// </summary>
/// <param name="args">事件参数</param>
protected virtual void OnStatusChanged(ServiceStatusEventArgs args)
{
    StatusChanged?.Invoke(this, args);
}
    }
    
    /// <summary>
    /// 服务状态枚举
    /// </summary>
    public enum ServiceStatus
{
    /// <summary>
    /// 空闲
    /// </summary>
    Idle,

    /// <summary>
    /// 处理中
    /// </summary>
    Processing,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled,

    /// <summary>
    /// 错误
    /// </summary>
    Error
}

/// <summary>
/// 服务状态事件参数
/// </summary>
public class ServiceStatusEventArgs : EventArgs
{
    /// <summary>
    /// 服务状态
    /// </summary>
    public ServiceStatus Status { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; }
}
} 