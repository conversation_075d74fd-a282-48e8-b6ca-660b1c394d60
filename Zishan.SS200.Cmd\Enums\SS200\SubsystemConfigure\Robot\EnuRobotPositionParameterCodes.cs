﻿using System.ComponentModel;
using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot
{
    /// <summary>
    /// Robot 位置参数设置代码枚举类型
    /// </summary>
    [TypeConverter(typeof(EnuRobotPositionParameterCodes))]
    public enum EnuRobotPositionParameterCodes
    {
        /// <summary>
        /// T-axis smooth to CHA
        /// </summary>
        [Description("T-axis smooth to CHA")]
        RP1 = 0,

        /// <summary>
        /// T-axis smooth to CHB
        /// </summary>
        [Description("T-axis smooth to CHB")]
        RP2 = 1,

        /// <summary>
        /// T-axis smooth to cooling chamber
        /// </summary>
        [Description("T-axis smooth to cooling chamber")]
        RP3 = 2,

        /// <summary>
        /// T-axis smooth to cassette
        /// </summary>
        [Description("T-axis smooth to cassette")]
        RP4 = 3,

        /// <summary>
        /// T-axis nose to CHA
        /// </summary>
        [Description("T-axis nose to CHA")]
        RP5 = 4,

        /// <summary>
        /// T-axis nose to CHB
        /// </summary>
        [Description("T-axis nose to CHB")]
        RP6 = 5,

        /// <summary>
        /// T-axis nose to cooling chamber
        /// </summary>
        [Description("T-axis nose to cooling chamber")]
        RP7 = 6,

        /// <summary>
        /// T-axis nose to cassette
        /// </summary>
        [Description("T-axis nose to cassette")]
        RP8 = 7,

        /// <summary>
        /// T-axis zero
        /// </summary>
        [Description("T-axis zero")]
        RP9 = 8,

        /// <summary>
        /// R-axis smooth extend and face to CHA
        /// </summary>
        [Description("R-axis smooth extend and face to CHA")]
        RP10 = 9,

        /// <summary>
        /// R-axis smooth extend and face to CHB
        /// </summary>
        [Description("R-axis smooth extend and face to CHB")]
        RP11 = 10,

        /// <summary>
        /// R-axis nose extend and face to CHA
        /// </summary>
        [Description("R-axis nose extend and face to CHA")]
        RP12 = 11,

        /// <summary>
        /// R-axis nose extend and face to CHB
        /// </summary>
        [Description("R-axis nose extend and face to CHB")]
        RP13 = 12,

        /// <summary>
        /// R-axis smooth face to cooling chamber and extend
        /// </summary>
        [Description("R-axis smooth face to cooling chamber and extend")]
        RP14 = 13,

        /// <summary>
        /// R-axis nose extend and face to cooling chamber
        /// </summary>
        [Description("R-axis nose extend and face to cooling chamber")]
        RP15 = 14,

        /// <summary>
        /// R-axis smooth face to cassette and extend
        /// </summary>
        [Description("R-axis smooth face to cassette and extend")]
        RP16 = 15,

        /// <summary>
        /// R-axis nose face to cassette and extend
        /// </summary>
        [Description("R-axis nose face to cassette and extend")]
        RP17 = 16,

        /// <summary>
        /// R-axis zero position
        /// </summary>
        [Description("R-axis zero position")]
        RP18 = 17,

        /// <summary>
        /// Z-axis height at smooth to CHA
        /// </summary>
        [Description("Z-axis height at smooth to CHA")]
        RP19 = 18,

        /// <summary>
        /// Z-axis height at smooth to CHB
        /// </summary>
        [Description("Z-axis height at smooth to CHB")]
        RP20 = 19,

        /// <summary>
        /// Z-axis height at smooth to CT
        /// </summary>
        [Description("Z-axis height at smooth to CT")]
        RP21 = 20,

        /// <summary>
        /// Z-axis height at smooth to CB
        /// </summary>
        [Description("Z-axis height at smooth to CB")]
        RP22 = 21,

        /// <summary>
        /// Z-axis height at nose to CHA
        /// </summary>
        [Description("Z-axis height at nose to CHA")]
        RP23 = 22,

        /// <summary>
        /// Z-axis height at nose to CHB
        /// </summary>
        [Description("Z-axis height at nose to CHB")]
        RP24 = 23,

        /// <summary>
        /// Z-axis height at nose to CT get
        /// </summary>
        [Description("Z-axis height at nose to CT get")]
        RP25 = 24,

        /// <summary>
        /// Z-axis height at nose to CB get
        /// </summary>
        [Description("Z-axis height at nose to CB get")]
        RP26 = 25,

        /// <summary>
        /// Z-axis zero position
        /// </summary>
        [Description("Z-axis zero position")]
        RP27 = 26,

        /// <summary>
        /// Z-axis height to pin search
        /// </summary>
        [Description("Z-axis height to pin search")]
        RP28 = 27
    }
}