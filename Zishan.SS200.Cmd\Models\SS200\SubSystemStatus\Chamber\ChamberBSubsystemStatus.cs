using System;

namespace Zish<PERSON>.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// ChamberB子系统状态模型
    /// 继承自ChamberSubsystemStatus，确保ChamberB有独立的实例
    /// </summary>
    public class ChamberBSubsystemStatus : ChamberSubsystemStatus
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ChamberBSubsystemStatus() : base()
        {
            // ChamberB特有的初始化逻辑（如果需要）
        }

        /// <summary>
        /// 重写ToString方法，标识这是ChamberB的状态
        /// </summary>
        /// <returns>ChamberB状态的字符串表示</returns>
        public override string ToString()
        {
            var baseString = base.ToString();
            return $"=== ChamberB 子系统状态 ===\n{baseString}";
        }
    }
}
