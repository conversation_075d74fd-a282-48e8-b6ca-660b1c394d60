# R轴零位状态初始化问题修复

## 问题描述

在系统初始化阶段，如果在构造函数中立即调用`UpdateRAxisZeroStatus()`方法，会遇到以下问题：

```csharp
// ❌ 问题代码
public BasicCommandTestViewModel(IS200McuCmdService mcuCmdService)
{
    InitializeViewModel();
    
    // 一开始就调用，状态表还未更新，导致 IsRAxisAtZero 为 false
    UpdateRAxisZeroStatus(); 
}
```

**问题原因：**
- 实时状态表（SS200InterLockMain）可能尚未完全初始化
- Robot状态数据还没有准备就绪
- 导致获取到错误的状态值（通常为false）

## 解决方案

### 1. 智能延迟初始化

```csharp
// ✅ 修复后的代码
public BasicCommandTestViewModel(IS200McuCmdService mcuCmdService)
{
    InitializeViewModel();
    
    // 延迟初始化R轴状态，等待实时状态表准备就绪
    _ = Task.Run(async () =>
    {
        // 智能等待实时状态表准备就绪
        int maxRetries = 10;
        int retryCount = 0;
        
        while (retryCount < maxRetries && !IsRealTimeStatusReady())
        {
            await Task.Delay(500); // 每500ms检查一次
            retryCount++;
        }
        
        // 在UI线程上更新状态
        Application.Current?.Dispatcher.Invoke(() =>
        {
            if (IsRealTimeStatusReady())
            {
                UpdateRAxisZeroStatus();
                _logger.Info("R轴状态已通过实时状态表初始化");
            }
            else
            {
                _logger.Warn("实时状态表未准备就绪，使用默认R轴状态");
            }
        });
    });
}
```

### 2. 状态就绪检查方法

```csharp
/// <summary>
/// 检查实时状态表是否已准备就绪并有有效数据
/// </summary>
private bool IsRealTimeStatusReady()
{
    try
    {
        // 检查Robot连接状态
        if (_mcuCmdService?.Robot?.IsConnected != true)
        {
            return false;
        }

        // 检查实时状态表对象是否存在
        var robotStatus = SS200InterLockMain.Instance?.SubsystemStatus?.Robot?.Status;
        if (robotStatus == null)
        {
            return false;
        }

        // 检查是否有Robot报警寄存器数据（表示通信正常）
        var robotAlarmRegisters = _mcuCmdService.RobotAlarmRegisters;
        if (robotAlarmRegisters == null || robotAlarmRegisters.Count == 0)
        {
            return false;
        }

        // 检查Robot线圈数据是否存在（表示I/O通信正常）
        var robotCoils = _mcuCmdService.RobotCoils;
        if (robotCoils == null || robotCoils.Count == 0)
        {
            return false;
        }

        // 如果所有检查都通过，认为实时状态表已准备就绪
        return true;
    }
    catch
    {
        return false;
    }
}
```

### 3. 优化后的状态更新方法

```csharp
/// <summary>
/// 更新R轴零位状态 - 使用混合方式确保准确性
/// </summary>
private void UpdateRAxisZeroStatus()
{
    try
    {
        // 优先使用实时状态表的计算结果
        var robotStatus = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status;
        if (robotStatus != null)
        {
            IsRAxisAtZero = robotStatus.RAxisIsZeroPosition;
            _logger.Debug($"使用实时状态表更新R轴零位状态: {IsRAxisAtZero}");
        }
        else
        {
            _logger.Debug($"实时状态表暂无数据，保持本地R轴零位状态: {IsRAxisAtZero}");
        }
    }
    catch (Exception ex)
    {
        _logger.Error($"更新R轴零位状态失败: {ex.Message}");
    }
}
```

## 核心优势

### 1. 解决初始化时序问题
- ✅ 避免过早访问未准备就绪的状态表
- ✅ 智能等待机制确保数据可用性
- ✅ 优雅的降级处理策略

### 2. 提高系统可靠性
- ✅ 异步初始化不阻塞UI线程
- ✅ 完整的错误处理和日志记录
- ✅ 合理的超时和重试机制

### 3. 保持性能优化
- ✅ 非阻塞的后台初始化
- ✅ 最多5秒的等待时间（10次 × 500ms）
- ✅ UI线程安全的状态更新

## 使用场景

### 1. 系统启动时
- 自动进行智能初始化
- 等待实时状态表准备就绪
- 获取准确的初始状态

### 2. 运行时状态检查
```csharp
// 在关键操作前使用智能获取方法
if (IsSafetyModeEnabled && !GetCurrentRAxisZeroStatus())
{
    // 处理R轴未归零的情况
}
```

### 3. 操作完成后同步
```csharp
// 操作完成后更新状态
if (result.Success)
{
    IsRAxisAtZero = true;
    UpdateRAxisZeroStatus(); // 同时更新实时状态表
}
```

## 注意事项

### 1. 线程安全
- 使用`Application.Current.Dispatcher.Invoke`确保UI线程安全
- 异步操作不会阻塞主线程

### 2. 超时处理
- 最大等待时间为5秒（可调整）
- 超时后使用默认状态，不会无限等待

### 3. 错误处理
- 完整的异常捕获和日志记录
- 优雅的降级处理机制

## 测试验证

### 1. 启动测试
- 验证系统启动时状态初始化正确
- 检查日志确认初始化过程

### 2. 状态同步测试
- 验证操作后状态正确更新
- 确认实时状态表和本地状态一致

### 3. 异常情况测试
- 测试实时状态表不可用时的处理
- 验证超时机制的正确性

## 总结

通过智能延迟初始化策略，我们成功解决了R轴零位状态在系统启动时的初始化问题：

1. **避免了时序问题**：不再在构造函数中立即调用状态更新
2. **提高了可靠性**：智能等待确保数据准确性
3. **保持了性能**：异步处理不影响UI响应
4. **增强了健壮性**：完整的错误处理和降级策略

这个解决方案既解决了初始化问题，又保持了运行时的高性能和可靠性。
