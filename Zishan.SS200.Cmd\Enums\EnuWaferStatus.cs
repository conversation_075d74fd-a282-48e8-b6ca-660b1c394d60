﻿using System.ComponentModel;

using Wu.Wpf.Converters;

namespace Zishan.SS200.Cmd.Enums
{
    /// <summary>
    /// Wafer真实状态：无片、有片、故障
    /// </summary>
    [TypeConverter(typeof(EnuWaferStatus))]
    public enum EnuWaferStatus
    {
        /// <summary>
        /// 无片
        /// </summary>
        [Description("无片")]
        None = 0,

        /// <summary>
        /// 有片
        /// </summary>
        [Description("有片")]
        Have = 1,

        /// <summary>
        /// 自动完成
        /// </summary>
        [Description("自动完成")]
        AutoFinished = 2,

        /// <summary>
        /// 手动完成
        /// </summary>
        [Description("手动完成")]
        ManuFinished = 3,

        /// <summary>
        /// 故障：碎片、丢片等
        /// </summary>
        [Description("故障：碎片、丢片等")]
        Others = 4
    }
}