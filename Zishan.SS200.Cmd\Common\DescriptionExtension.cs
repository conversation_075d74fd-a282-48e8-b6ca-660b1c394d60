﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Reflection;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 描述特性的读取扩展类
    /// </summary>
    public static class DescriptionExtension
    {
        /// <summary>
        /// 获取枚举的描述信息
        /// </summary>
        public static string GetDescription(this Enum em)
        {
            Type type = em.GetType();
            FieldInfo fd = type.GetField(em.ToString());
            string des = fd.GetDescription();
            return des;
        }

        /// <summary>
        /// 获取属性的描述信息
        /// </summary>
        public static string GetDescription(this Type type, string proName)
        {
            PropertyInfo pro = type.GetProperty(proName);
            string des = proName;
            if (pro != null)
            {
                des = pro.GetDescription();
            }
            return des;
        }

        /// <summary>
        /// 获取属性的描述信息
        /// </summary>
        public static string GetDescription(this MemberInfo info)
        {
            var attrs = (DescriptionAttribute[])info.GetCustomAttributes(typeof(DescriptionAttribute), false);
            string des = info.Name;
            foreach (DescriptionAttribute attr in attrs)
            {
                des = attr.Description;
            }
            return des;
        }
    }
}

/*

使用方法：
    [Description("测试枚举名")]
enum TestEnum
{
    [Description("测试")]
    Test1
}

[Description("测试类名")]
class TestClass
{
    [Description("测试class")]
    public int Test1 { get; set; }
}

//获取枚举类型的描述特性
string str1 = typeof(TestEnum).GetDescription();

//获取枚举值的描述特性
string str2 = TestEnum.Test1.GetDescription();
str2 = typeof(TestEnum).GetDescription(nameof(TestEnum.Test1));
str2 = typeof(TestEnum).GetDescription(TestEnum.Test1.ToString());

//获取类的描述特性
string str3 = typeof(TestClass).GetDescription();

//获取属性的描述特性（也可以反射遍历属性列表）
string str4 = typeof(TestClass).GetDescription();
TestClass test = new TestClass();
str4 = typeof(TestClass).GetDescription(nameof(test.Test1));

*/