# 层次化日志快速入门

## 问题描述

原有的日志输出是平铺的，没有层次感，查看不友好：

```
执行搬运，Robot连接状态: True，服务实例: McuCmdService
搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2) 机械臂端口: Nose
取Wafer: 从ChamberA(SLOT:1)获取Wafer
开始取Wafer...
结束取Wafer...
放Wafer: 将Wafer放置到ChamberB(SLOT:2)
开始放Wafer...
结束放Wafer...
搬运Wafer成功: Wafer已成功从ChamberA(SLOT:1)搬运到ChamberB(SLOT:2)
```

## 解决方案

使用新的层次化日志功能，通过缩进显示调用层级：

```
执行搬运，Robot连接状态: True，服务实例: McuCmdService
    搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2) 机械臂端口: Nose
        取Wafer: 从ChamberA(SLOT:1)获取Wafer
            开始取Wafer...
            结束取Wafer...
        放Wafer: 将Wafer放置到ChamberB(SLOT:2)
            开始放Wafer...
            结束放Wafer...
✅ 搬运Wafer成功: Wafer已成功从ChamberA(SLOT:1)搬运到ChamberB(SLOT:2)
```

## 快速使用

### 方法1：使用 using 语句（推荐）

```csharp
UILogService.AddLog("开始主要操作");

using (UILogService.CreateIndentScope())
{
    UILogService.AddLog("子操作1");
    
    using (UILogService.CreateIndentScope())
    {
        UILogService.AddLog("子操作1的详细步骤");
        UILogService.AddLog("另一个详细步骤");
    }
    
    UILogService.AddLog("子操作2");
}

UILogService.AddSuccessLog("主要操作完成");
```

### 方法2：使用便捷方法

```csharp
UILogService.AddLogAndIncreaseIndent("开始主要操作");

UILogService.AddLogAndIncreaseIndent("子操作1");
UILogService.AddLog("子操作1的详细步骤");
UILogService.DecreaseIndentAndAddSuccessLog("子操作1完成");

UILogService.AddLogAndIncreaseIndent("子操作2");
UILogService.AddLog("子操作2的详细步骤");
UILogService.DecreaseIndentAndAddSuccessLog("子操作2完成");

UILogService.DecreaseIndentAndAddSuccessLog("主要操作完成");
```

## 在现有代码中应用

### 修改前（原有代码）

```csharp
public async Task<bool> TransferWaferAsync()
{
    UILogService.AddLog("开始晶圆传输");
    
    UILogService.AddLog("初始化机器人");
    var initResult = await InitializeRobotAsync();
    if (!initResult.Success)
    {
        UILogService.AddErrorLog($"初始化失败: {initResult.Message}");
        return false;
    }
    UILogService.AddSuccessLog("初始化成功");
    
    UILogService.AddLog("获取晶圆");
    var getResult = await GetWaferAsync();
    if (!getResult.Success)
    {
        UILogService.AddErrorLog($"获取晶圆失败: {getResult.Message}");
        return false;
    }
    UILogService.AddSuccessLog("获取晶圆成功");
    
    UILogService.AddSuccessLog("晶圆传输完成");
    return true;
}
```

### 修改后（层次化日志）

```csharp
public async Task<bool> TransferWaferAsync()
{
    UILogService.AddLogAndIncreaseIndent("开始晶圆传输");
    
    try
    {
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog("第一步：机器人初始化（三轴归零）");
            var initResult = await InitializeRobotAsync();
            if (!initResult.Success)
            {
                UILogService.AddErrorLog($"初始化失败: {initResult.Message}");
                UILogService.DecreaseIndentAndAddErrorLog("晶圆传输失败");
                return false;
            }
            UILogService.AddSuccessLog("机器人初始化成功");
        }
        
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog("获取晶圆");
            var getResult = await GetWaferAsync();
            if (!getResult.Success)
            {
                UILogService.AddErrorLog($"获取晶圆失败: {getResult.Message}");
                UILogService.DecreaseIndentAndAddErrorLog("晶圆传输失败");
                return false;
            }
            UILogService.AddSuccessLog("获取晶圆成功");
        }
        
        UILogService.DecreaseIndentAndAddSuccessLog("晶圆传输完成");
        return true;
    }
    catch (Exception ex)
    {
        UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输异常: {ex.Message}");
        return false;
    }
}
```

## 配置选项

### 更改缩进字符串

```csharp
// 使用2个空格
UILogService.SetIndentString("  ");

// 使用Tab
UILogService.SetIndentString("\t");

// 恢复默认（4个空格）
UILogService.SetIndentString("    ");
```

### 设置最大缩进层级

```csharp
// 限制最大缩进层级为5
UILogService.SetMaxIndentLevel(5);
```

### 启用/禁用缩进功能

```csharp
// 禁用缩进功能（恢复原有平铺日志）
UILogService.SetIndentEnabled(false);

// 启用缩进功能
UILogService.SetIndentEnabled(true);
```

## 最佳实践

### 1. 在方法开始和结束使用缩进

```csharp
public async Task SomeMethodAsync()
{
    UILogService.AddLogAndIncreaseIndent("开始执行某个方法");
    
    try
    {
        // 方法实现
        UILogService.DecreaseIndentAndAddSuccessLog("方法执行成功");
    }
    catch (Exception ex)
    {
        UILogService.DecreaseIndentAndAddErrorLog($"方法执行失败: {ex.Message}");
        throw;
    }
}
```

### 2. 使用 using 语句管理子操作

```csharp
using (UILogService.CreateIndentScope())
{
    UILogService.AddLog("执行子操作");
    // 子操作代码
    UILogService.AddSuccessLog("子操作完成");
}
```

### 3. 错误处理中正确管理缩进

```csharp
UILogService.AddLogAndIncreaseIndent("开始复杂操作");

try
{
    // 操作代码
    UILogService.DecreaseIndentAndAddSuccessLog("操作成功");
}
catch (Exception ex)
{
    UILogService.DecreaseIndentAndAddErrorLog($"操作失败: {ex.Message}");
    throw;
}
```

## 注意事项

1. **线程安全**：缩进层级是线程独立的，多线程环境下安全使用
2. **向后兼容**：现有代码无需修改，只是没有缩进效果
3. **异常处理**：确保在异常情况下也能正确管理缩进层级
4. **适度使用**：避免过深的嵌套，建议不超过5层缩进

## 机器人初始化示例

InitializeRobotAsync方法的层次化日志效果：

```csharp
public async Task<(bool Success, string Message)> InitializeRobotAsync()
{
    // 使用层次化日志记录机器人初始化流程
    UILogService.AddLogAndIncreaseIndent("开始初始化机器人（三轴归零）");

    try
    {
        // 1. T轴归零
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog("执行T轴归零");
            var tResult = await ZeroTAxisAsync();
            if (!tResult.Success)
            {
                UILogService.AddErrorLog($"T轴归零失败: {tResult.Message}");
                UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化失败: {tResult.Message}");
                return tResult;
            }
            UILogService.AddSuccessLog("T轴归零成功");
        }

        // 2. R轴归零
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog("执行R轴归零");
            var rResult = await ZeroRAxisAsync();
            if (!rResult.Success)
            {
                UILogService.AddErrorLog($"R轴归零失败: {rResult.Message}");
                UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化失败: {rResult.Message}");
                return rResult;
            }
            UILogService.AddSuccessLog("R轴归零成功");
        }

        // 3. Z轴归零
        using (UILogService.CreateIndentScope())
        {
            UILogService.AddLog("执行Z轴归零");
            var zResult = await ZeroZAxisAsync();
            if (!zResult.Success)
            {
                UILogService.AddErrorLog($"Z轴归零失败: {zResult.Message}");
                UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化失败: {zResult.Message}");
                return zResult;
            }
            UILogService.AddSuccessLog("Z轴归零成功");
        }

        UILogService.DecreaseIndentAndAddSuccessLog("机器人三轴归零成功");
        return (true, "机器人三轴归零成功");
    }
    catch (Exception ex)
    {
        UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化异常: {ex.Message}");
        return (false, $"机器人初始化异常: {ex.Message}");
    }
}
```

**输出效果：**
```
开始初始化机器人（三轴归零）
    执行T轴归零
    ✅ T轴归零成功
    执行R轴归零
    ✅ R轴归零成功
    执行Z轴归零
    ✅ Z轴归零成功
✅ 机器人三轴归零成功
```

## 测试代码

可以运行以下测试代码查看效果：

```csharp
// 运行基本测试
await HierarchicalLoggingTest.TestBasicHierarchicalLogging();

// 运行机器人初始化测试
await HierarchicalLoggingTest.TestRobotInitializationWithHierarchy();

// 运行所有测试
await HierarchicalLoggingTest.RunAllTests();
```

## 总结

层次化日志功能通过简单的缩进机制，大大提升了日志的可读性和层次感。使用 `using` 语句可以自动管理缩进层级，避免手动管理的复杂性。在现有代码中逐步应用这个功能，可以让日志输出更加清晰友好。
