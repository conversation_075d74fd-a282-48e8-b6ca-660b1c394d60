﻿AR53
GW from CT
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			paddle status review
RS53~RS54
				RS53=0 RS54=0
					compare ABS(RTF-RP3) with ABS(RTF-RP7)
						ABS(RTF-RP3)≤ABS(RTF-RP7)
							AR3
T-axis smooth to CT
								AR23
Move Z-axis height smooth to CT get
									AR13
R-axis smooth to CT extend
										AR65
wafer status CT to smooth paddle
											AR29
Move Z-axis height smooth to CT put
												AR19
R-axis zero position
													command done
						ABS(RTF-RP3)>ABS(RTF-RP7)
							AR7
T-axis nose to CT
								AR27
Move Z-axis height nose to CT get
									AR17
R-axis nose to CT extend
										AR76
wafer status CT to nose paddle
											AR31
Move Z-axis height smooth to CT put
												AR19
R-axis zero position
													command done
				RS53=1 RS54=0
					AR7
T-axis nose to CT
						AR27
Move Z-axis height nose to CT get
							AR17
R-axis nose to CT extend
								AR76
wafer status CT to nose paddle
									AR31
Move Z-axis height smooth to CT put
										AR19
R-axis zero position
											command done
				RS53=0 RS54=1
					AR3
T-axis smooth to CT
						AR23
Move Z-axis height smooth to CT get
							AR13
R-axis smooth to CT extend
								AR65
wafer status CT to smooth paddle
									AR29
Move Z-axis height smooth to CT put
										AR19
R-axis zero position
											command done
				RS53=1 RS54=1
					RA17 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM