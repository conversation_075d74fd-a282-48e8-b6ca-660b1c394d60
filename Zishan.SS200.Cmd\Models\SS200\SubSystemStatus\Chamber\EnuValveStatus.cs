using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// 阀门状态枚举
    /// </summary>
    public enum EnuValveStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// 阀门打开状态
        /// </summary>
        [Description("打开")]
        Open = 1,

        /// <summary>
        /// 阀门关闭状态
        /// </summary>
        [Description("关闭")]
        Close = 2,

        /// <summary>
        /// 阀门在打开与关闭状态之间
        /// </summary>
        [Description("开关之间")]
        BetweenOpenClose = 3
    }
}
