using System;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Shuttle;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// Shuttle报警访问器测试类
    /// 用于验证所有Shuttle报警代码访问器是否正常工作
    /// </summary>
    public class ShuttleAlarmAccessorTest
    {
        /// <summary>
        /// 简单测试Shuttle报警代码访问器
        /// </summary>
        public static void SimpleTest()
        {
            try
            {
                Console.WriteLine("=== Shuttle报警代码访问器简单测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var shuttleAlarm = interLock.AlarmCode.Shuttle;

                // 测试前10个报警代码
                Console.WriteLine("\n测试前10个Shuttle报警代码:");
                TestAccessor("SA1", shuttleAlarm.SA1_SystemBusyReject);
                TestAccessor("SA2", shuttleAlarm.SA2_SystemAlarmReject);
                TestAccessor("SA3", shuttleAlarm.SA3_CassetteNestMoveTimeout);
                TestAccessor("SA4", shuttleAlarm.SA4_CassetteNestSpeedTooHigh);
                TestAccessor("SA5", shuttleAlarm.SA5_CassetteNestPositionFailure);
                TestAccessor("SA6", shuttleAlarm.SA6_ShuttleMoveTimeout);
                TestAccessor("SA7", shuttleAlarm.SA7_ShuttleMoveTooFast);
                TestAccessor("SA8", shuttleAlarm.SA8_ShuttleUpDownPositionFailure);
                TestAccessor("SA9", shuttleAlarm.SA9_ShuttleRotateTimeout);
                TestAccessor("SA10", shuttleAlarm.SA10_ShuttleRotateTooFast);

                // 测试中间的一些报警代码
                Console.WriteLine("\n测试中间的一些Shuttle报警代码:");
                TestAccessor("SA15", shuttleAlarm.SA15_CassetteDoorCloseTimeout);
                TestAccessor("SA20", shuttleAlarm.SA20_PressureDeltaOutOfRange);
                TestAccessor("SA25", shuttleAlarm.SA25_ChamberPressureReviewReturnReject);
                TestAccessor("SA30", shuttleAlarm.SA30_CHATriggerAlarmPumpDownError);

                // 测试最后几个报警代码
                Console.WriteLine("\n测试最后几个Shuttle报警代码:");
                TestAccessor("SA35", shuttleAlarm.SA35_CHAPressureHighPumpDownError);
                TestAccessor("SA36", shuttleAlarm.SA36_CHBPressureHighPumpDownError);
                TestAccessor("SA37", shuttleAlarm.SA37_SystemTriggerAlarmReject);
                TestAccessor("SA38", shuttleAlarm.SA38_LoadlockNotIdleReject);
                TestAccessor("SA39", shuttleAlarm.SA39_ShuttlePressureNotATMBackfillFailure);

                Console.WriteLine("\n=== Shuttle报警代码访问器简单测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 统计测试 - 验证所有38个访问器都已定义
        /// </summary>
        public static void CountTest()
        {
            try
            {
                Console.WriteLine("=== Shuttle报警代码访问器统计测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var shuttleAlarm = interLock.AlarmCode.Shuttle;
                var shuttleAlarmType = shuttleAlarm.GetType();
                
                // 获取所有以SA开头的属性
                var properties = shuttleAlarmType.GetProperties();
                int saPropertyCount = 0;
                
                foreach (var property in properties)
                {
                    if (property.Name.StartsWith("SA") && property.Name.Contains("_"))
                    {
                        saPropertyCount++;
                        Console.WriteLine($"  发现访问器: {property.Name}");
                    }
                }
                
                Console.WriteLine($"\n总计发现 {saPropertyCount} 个Shuttle报警代码访问器");

                if (saPropertyCount == 39)
                {
                    Console.WriteLine("✅ 所有39个Shuttle报警代码访问器都已正确定义！");
                }
                else
                {
                    Console.WriteLine($"❌ 预期39个访问器，实际发现{saPropertyCount}个");
                }
                
                Console.WriteLine("=== Shuttle报警代码访问器统计测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"统计测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 按功能分类测试Shuttle报警代码访问器
        /// </summary>
        public static void CategoryTest()
        {
            try
            {
                Console.WriteLine("=== Shuttle报警代码访问器分类测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var shuttleAlarm = interLock.AlarmCode.Shuttle;

                // 系统状态报警
                Console.WriteLine("\n系统状态报警 (SA1-SA2, SA37):");
                TestAccessor("SA1", shuttleAlarm.SA1_SystemBusyReject);
                TestAccessor("SA2", shuttleAlarm.SA2_SystemAlarmReject);
                TestAccessor("SA37", shuttleAlarm.SA37_SystemTriggerAlarmReject);

                // 卡匣巢控制报警
                Console.WriteLine("\n卡匣巢控制报警 (SA3-SA5, SA24):");
                TestAccessor("SA3", shuttleAlarm.SA3_CassetteNestMoveTimeout);
                TestAccessor("SA4", shuttleAlarm.SA4_CassetteNestSpeedTooHigh);
                TestAccessor("SA5", shuttleAlarm.SA5_CassetteNestPositionFailure);
                TestAccessor("SA24", shuttleAlarm.SA24_CassetteNestNotHomeShuttleError);

                // Shuttle移动控制报警
                Console.WriteLine("\nShuttle移动控制报警 (SA6-SA11):");
                TestAccessor("SA6", shuttleAlarm.SA6_ShuttleMoveTimeout);
                TestAccessor("SA7", shuttleAlarm.SA7_ShuttleMoveTooFast);
                TestAccessor("SA8", shuttleAlarm.SA8_ShuttleUpDownPositionFailure);
                TestAccessor("SA9", shuttleAlarm.SA9_ShuttleRotateTimeout);
                TestAccessor("SA10", shuttleAlarm.SA10_ShuttleRotateTooFast);
                TestAccessor("SA11", shuttleAlarm.SA11_ShuttleRotatePositionFailure);

                // 卡匣门控制报警
                Console.WriteLine("\n卡匣门控制报警 (SA12-SA16):");
                TestAccessor("SA12", shuttleAlarm.SA12_CassetteDoorOpenTimeout);
                TestAccessor("SA13", shuttleAlarm.SA13_CassetteDoorOpenTooFast);
                TestAccessor("SA14", shuttleAlarm.SA14_CassetteDoorPositionFailure);
                TestAccessor("SA15", shuttleAlarm.SA15_CassetteDoorCloseTimeout);
                TestAccessor("SA16", shuttleAlarm.SA16_CassetteDoorCloseTooFast);

                // 阀门控制报警
                Console.WriteLine("\n阀门控制报警 (SA17-SA19, SA26-SA27):");
                TestAccessor("SA17", shuttleAlarm.SA17_OpenXVTimeout);
                TestAccessor("SA18", shuttleAlarm.SA18_OpenXVTooFast);
                TestAccessor("SA19", shuttleAlarm.SA19_CloseXVTimeout);
                TestAccessor("SA26", shuttleAlarm.SA26_ForelineNoVacuumCVError);
                TestAccessor("SA27", shuttleAlarm.SA27_CassetteDoorNotCloseVacuumError);

                // 压力控制报警
                Console.WriteLine("\n压力控制报警 (SA20-SA21, SA25, SA28-SA29):");
                TestAccessor("SA20", shuttleAlarm.SA20_PressureDeltaOutOfRange);
                TestAccessor("SA21", shuttleAlarm.SA21_ChamberPressureReviewNo);
                TestAccessor("SA25", shuttleAlarm.SA25_ChamberPressureReviewReturnReject);
                TestAccessor("SA28", shuttleAlarm.SA28_ShuttlePumpDownTimeout);
                TestAccessor("SA29", shuttleAlarm.SA29_ShuttleBackfillTimeout);

                // 位置检测报警
                Console.WriteLine("\n位置检测报警 (SA22-SA23):");
                TestAccessor("SA22", shuttleAlarm.SA22_PositionUnknownCassetteDoorError);
                TestAccessor("SA23", shuttleAlarm.SA23_PositionUnknownSwapCassetteError);

                // 腔室状态报警
                Console.WriteLine("\n腔室状态报警 (SA30-SA36, SA38):");
                TestAccessor("SA30", shuttleAlarm.SA30_CHATriggerAlarmPumpDownError);
                TestAccessor("SA31", shuttleAlarm.SA31_CHBTriggerAlarmPumpDownError);
                TestAccessor("SA32", shuttleAlarm.SA32_CHANotIdlePumpDownError);
                TestAccessor("SA33", shuttleAlarm.SA33_CHBNotIdlePumpDownError);
                TestAccessor("SA34", shuttleAlarm.SA34_CHABPressureHighPumpDownError);
                TestAccessor("SA35", shuttleAlarm.SA35_CHAPressureHighPumpDownError);
                TestAccessor("SA36", shuttleAlarm.SA36_CHBPressureHighPumpDownError);
                TestAccessor("SA38", shuttleAlarm.SA38_LoadlockNotIdleReject);

                Console.WriteLine("\n=== Shuttle报警代码访问器分类测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分类测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试单个访问器
        /// </summary>
        /// <param name="code">报警代码</param>
        /// <param name="accessor">访问器</param>
        private static void TestAccessor(string code, AlarmPropertyAccessor accessor)
        {
            try
            {
                if (accessor != null)
                {
                    string content = accessor.Content ?? "未定义";
                    string chsContent = accessor.ChsContent ?? "未定义";
                    Console.WriteLine($"  {code}: {content}");
                    Console.WriteLine($"       中文: {chsContent}");
                }
                else
                {
                    Console.WriteLine($"  {code}: 访问器为空");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  {code}: 访问器测试失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            SimpleTest();
            Console.WriteLine();
            CountTest();
            Console.WriteLine();
            CategoryTest();
        }
    }
}
