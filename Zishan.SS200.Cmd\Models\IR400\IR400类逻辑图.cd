﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="Zishan.RobotIR400Ui.Models.IR400.Cassette">
    <Position X="2.5" Y="7" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAABAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Models\IR400\Cassette.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR400Ui.Models.IR400.Container">
    <Position X="7.5" Y="0.5" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AABAAAKAABAQAEAEAAABAAACAQAAFAAIAAGAYigAAAA=</HashCode>
      <FileName>Models\IR400\Container.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR400Ui.Models.IR400.CoolingChamber">
    <Position X="4.5" Y="7" Width="1.75" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Models\IR400\CoolingChamber.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR400Ui.Models.IR400.Wafer">
    <Position X="10.5" Y="0.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAgAgARBAQEAgSABIQAAIhAAAAAAAAKQQAAAAA=</HashCode>
      <FileName>Models\IR400\Wafer.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR400Ui.Models.IR400.WaferAction">
    <Position X="12.25" Y="0.75" Width="1.75" />
    <TypeIdentifier>
      <HashCode>HhDA4AAkAABgBQBk/gFFICACACAgFKgKEKLAKyyJQAM=</HashCode>
      <FileName>Models\IR400\WaferAction.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR400Ui.Models.IR400.RobotArmNew">
    <Position X="8.75" Y="7" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAEAAAAQAIAAAIAAAQAAAAAAAAAAAAgAAAAA=</HashCode>
      <FileName>Models\IR400\RobotArmNew.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR400Ui.Models.IR400.Chamber">
    <Position X="6.75" Y="7" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Models\IR400\Chamber.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Zishan.RobotIR400Ui.Models.IR400.BContainer" Collapsed="true">
    <Position X="5.5" Y="5.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>KQDAQAqABJAwYkRECAABAIAaAQAgFAEoAE2AYiiBAIE=</HashCode>
      <FileName>Models\IR400\BContainer.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Enum Name="Zishan.RobotIR400Ui.Enums.EnuArmFetchSide">
    <Position X="3.5" Y="2.25" Width="1.75" />
    <TypeIdentifier>
      <HashCode>IAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAEAAAA=</HashCode>
      <FileName>Enums\EnuArmFetchSide.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.RobotIR400Ui.Enums.EnuChamberName">
    <Position X="5.5" Y="2.25" Width="1.75" />
    <TypeIdentifier>
      <HashCode>AAAAACAAIAAAAgAAgIAAABAAAAQACAAAAAAAAAAAQAA=</HashCode>
      <FileName>Enums\EnuChamberName.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Zishan.RobotIR400Ui.Enums.EnuChamberType">
    <Position X="3.5" Y="3.5" Width="1.75" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAQAAAAAIAAABAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Enums\EnuChamberType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Font Name="Microsoft YaHei UI" Size="9" />
</ClassDiagram>