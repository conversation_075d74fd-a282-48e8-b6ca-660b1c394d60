using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 将值转换为颜色的转换器
    /// 如果值等于参数值，返回TrueValue对应的颜色
    /// 否则返回FalseValue对应的颜色
    /// </summary>
    public class ValueToColorConverter : IValueConverter
    {
        /// <summary>
        /// 值为True时返回的颜色
        /// </summary>
        public object TrueValue { get; set; } = "Red";

        /// <summary>
        /// 值为False时返回的颜色
        /// </summary>
        public object FalseValue { get; set; } = "Green";

        /// <summary>
        /// 默认颜色
        /// </summary>
        public object DefaultValue { get; set; } = "Black";

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value == null)
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString(DefaultValue.ToString()));

                // 如果参数不为空，且值不等于参数值，返回FalseValue
                if (parameter != null)
                {
                    var numValue = System.Convert.ToInt32(value);
                    var paramValue = System.Convert.ToInt32(parameter);

                    // 如果值等于参数值，返回FalseValue（表示没有错误）
                    // 否则返回TrueValue（表示有错误）
                    if (numValue == paramValue)
                        return new SolidColorBrush((Color)ColorConverter.ConvertFromString(FalseValue.ToString()));
                    else
                        return new SolidColorBrush((Color)ColorConverter.ConvertFromString(TrueValue.ToString()));
                }
                
                bool result = false;
                if (value is bool boolValue)
                    result = boolValue;
                else if (value is int intValue)
                    result = intValue != 0;
                else if (value is string strValue)
                    result = !string.IsNullOrEmpty(strValue);
                
                var color = result ? TrueValue : FalseValue;
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString(color.ToString()));
            }
            catch
            {
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString(DefaultValue.ToString()));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return DependencyProperty.UnsetValue;
        }
    }
} 