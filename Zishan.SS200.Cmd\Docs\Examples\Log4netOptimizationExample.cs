using System;
using System.Diagnostics;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Examples
{
    /// <summary>
    /// Log4net性能优化示例和测试代码
    /// </summary>
    public class Log4netOptimizationExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(Log4netOptimizationExample));
        private static readonly ILog _perfLogger = LogManager.GetLogger("Performance");

        /// <summary>
        /// 演示不同环境配置的切换
        /// </summary>
        public static void DemonstrateConfigurationSwitching()
        {
            Console.WriteLine("=== Log4net配置切换演示 ===\n");

            // 显示当前配置
            Console.WriteLine("当前配置信息:");
            Console.WriteLine(Log4netConfigManager.GetCurrentConfigurationInfo());
            Console.WriteLine();

            // 测试不同环境配置
            var environments = new[]
            {
                Log4netConfigManager.EnvironmentType.Development,
                Log4netConfigManager.EnvironmentType.Production,
                Log4netConfigManager.EnvironmentType.Optimized
            };

            foreach (var env in environments)
            {
                Console.WriteLine($"切换到 {env} 环境...");
                
                if (Log4netConfigManager.SwitchConfiguration(env))
                {
                    Console.WriteLine($"✓ 成功切换到 {env} 环境");
                    
                    // 测试日志记录
                    TestLoggingPerformance(env.ToString(), 100);
                }
                else
                {
                    Console.WriteLine($"✗ 切换到 {env} 环境失败");
                }
                
                Console.WriteLine();
            }
        }

        /// <summary>
        /// 测试日志记录性能
        /// </summary>
        /// <param name="configName">配置名称</param>
        /// <param name="logCount">日志条数</param>
        public static void TestLoggingPerformance(string configName, int logCount = 1000)
        {
            Console.WriteLine($"=== {configName} 配置性能测试 ===");
            Console.WriteLine($"测试日志条数: {logCount}");

            var stopwatch = Stopwatch.StartNew();

            // 测试不同级别的日志
            for (int i = 0; i < logCount; i++)
            {
                if (i % 100 == 0)
                    _logger.Error($"错误日志测试 {i}");
                else if (i % 50 == 0)
                    _logger.Warn($"警告日志测试 {i}");
                else if (i % 10 == 0)
                    _logger.Info($"信息日志测试 {i}");
                else
                    _logger.Debug($"调试日志测试 {i}");
            }

            stopwatch.Stop();

            var result = $"配置: {configName}, 日志数: {logCount}, 耗时: {stopwatch.ElapsedMilliseconds}ms, " +
                        $"平均: {(double)stopwatch.ElapsedMilliseconds / logCount:F3}ms/条";

            Console.WriteLine(result);
            _perfLogger.Info(result);
        }

        /// <summary>
        /// 并发日志记录性能测试
        /// </summary>
        /// <param name="threadCount">线程数</param>
        /// <param name="logsPerThread">每线程日志数</param>
        public static async Task TestConcurrentLoggingPerformance(int threadCount = 4, int logsPerThread = 250)
        {
            Console.WriteLine($"=== 并发日志性能测试 ===");
            Console.WriteLine($"线程数: {threadCount}, 每线程日志数: {logsPerThread}");

            var stopwatch = Stopwatch.StartNew();

            var tasks = new Task[threadCount];
            for (int t = 0; t < threadCount; t++)
            {
                int threadId = t;
                tasks[t] = Task.Run(() =>
                {
                    var threadLogger = LogManager.GetLogger($"Thread{threadId}");
                    
                    for (int i = 0; i < logsPerThread; i++)
                    {
                        if (i % 50 == 0)
                            threadLogger.Error($"线程{threadId} 错误日志 {i}");
                        else if (i % 25 == 0)
                            threadLogger.Warn($"线程{threadId} 警告日志 {i}");
                        else if (i % 5 == 0)
                            threadLogger.Info($"线程{threadId} 信息日志 {i}");
                        else
                            threadLogger.Debug($"线程{threadId} 调试日志 {i}");
                    }
                });
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            var totalLogs = threadCount * logsPerThread;
            var result = $"并发测试 - 线程数: {threadCount}, 总日志数: {totalLogs}, " +
                        $"耗时: {stopwatch.ElapsedMilliseconds}ms, " +
                        $"平均: {(double)stopwatch.ElapsedMilliseconds / totalLogs:F3}ms/条";

            Console.WriteLine(result);
            _perfLogger.Info(result);
        }

        /// <summary>
        /// 演示异步日志的优势
        /// </summary>
        public static void DemonstrateAsyncLoggingBenefits()
        {
            Console.WriteLine("=== 异步日志优势演示 ===\n");

            // 模拟业务操作
            var stopwatch = Stopwatch.StartNew();

            for (int i = 0; i < 100; i++)
            {
                // 模拟业务逻辑
                var businessStopwatch = Stopwatch.StartNew();
                
                // 记录日志（异步，不阻塞业务逻辑）
                _logger.Info($"处理业务操作 {i}");
                
                // 模拟一些计算
                var sum = 0;
                for (int j = 0; j < 1000; j++)
                {
                    sum += j;
                }
                
                businessStopwatch.Stop();
                
                if (i % 20 == 0)
                {
                    Console.WriteLine($"业务操作 {i} 完成，耗时: {businessStopwatch.ElapsedMilliseconds}ms");
                }
            }

            stopwatch.Stop();
            Console.WriteLine($"总耗时: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine("注意: 由于使用异步日志，业务操作几乎不受日志记录影响");
        }

        /// <summary>
        /// 性能对比测试
        /// </summary>
        public static void PerformanceComparison()
        {
            Console.WriteLine("=== 性能对比测试 ===\n");

            var testConfigs = new[]
            {
                (Log4netConfigManager.EnvironmentType.Development, "开发环境(DEBUG级别)"),
                (Log4netConfigManager.EnvironmentType.Optimized, "优化配置(INFO级别)"),
                (Log4netConfigManager.EnvironmentType.Production, "生产环境(WARN级别)")
            };

            foreach (var (envType, description) in testConfigs)
            {
                if (Log4netConfigManager.ValidateConfigurationExists(envType))
                {
                    Log4netConfigManager.SwitchConfiguration(envType);
                    Console.WriteLine($"测试 {description}:");
                    TestLoggingPerformance(description, 500);
                    Console.WriteLine();
                }
                else
                {
                    Console.WriteLine($"配置文件不存在: {envType}");
                }
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static async Task RunAllExamples()
        {
            try
            {
                Console.WriteLine("开始Log4net性能优化演示...\n");

                // 1. 配置切换演示
                DemonstrateConfigurationSwitching();

                // 2. 性能对比测试
                PerformanceComparison();

                // 3. 并发测试
                await TestConcurrentLoggingPerformance();

                // 4. 异步日志优势演示
                DemonstrateAsyncLoggingBenefits();

                Console.WriteLine("\n演示完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示过程中发生错误: {ex.Message}");
                _logger.Error("演示过程中发生错误", ex);
            }
        }
    }
}
