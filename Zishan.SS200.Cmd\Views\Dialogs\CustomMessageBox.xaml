﻿<Window
    x:Class="Zishan.SS200.Cmd.Views.Dialogs.CustomMessageBox"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="CustomMessageBox"
    Width="400"
    Height="200"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Padding" Value="5" />
            <Setter Property="FontSize" Value="18" />
            <Setter Property="Margin" Value="5" />
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style
            x:Key="RetryButtonStyle"
            BasedOn="{StaticResource ButtonStyle}"
            TargetType="Button">
            <Setter Property="Background" Value="#FF007ACC" />
        </Style>
        <Style
            x:Key="ExitButtonStyle"
            BasedOn="{StaticResource ButtonStyle}"
            TargetType="Button">
            <Setter Property="Background" Value="#FF00CC00" />
        </Style>
        <Style
            x:Key="ContinueButtonStyle"
            BasedOn="{StaticResource ButtonStyle}"
            TargetType="Button">
            <Setter Property="Background" Value="#FFCC0000" />
        </Style>
    </Window.Resources>
    <Grid>
        <!--  自定义标题栏  -->
        <Border
            Height="30"
            VerticalAlignment="Top"
            Background="#FF2D2D30"
            MouseDown="TitleBar_MouseDown">
            <DockPanel>
                <TextBlock
                    x:Name="CustomTitle"
                    Margin="10,0,0,0"
                    VerticalAlignment="Center"
                    DockPanel.Dock="Left"
                    FontSize="16"
                    FontWeight="Bold"
                    Foreground="White"
                    Text="提示" />
                <Button
                    Width="30"
                    Height="30"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Click="CloseButton_Click"
                    Content="X"
                    DockPanel.Dock="Right"
                    FontWeight="Bold"
                    Foreground="White"
                    Visibility="Collapsed" />
            </DockPanel>
        </Border>

        <StackPanel
            Margin="10,40,10,10"
            HorizontalAlignment="Center"
            VerticalAlignment="Center">
            <TextBlock
                x:Name="MessageTextBlock"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                d:Text="检测门关闭状态超时，请确认！"
                FontSize="14"
                Foreground="#FF333333"
                TextAlignment="Center"
                TextWrapping="Wrap" />
        </StackPanel>

        <StackPanel
            Margin="10"
            HorizontalAlignment="Center"
            VerticalAlignment="Bottom"
            Orientation="Horizontal">
            <Button
                x:Name="RetryButton"
                Width="75"
                Click="RetryButton_Click"
                Content="重试"
                Style="{StaticResource ContinueButtonStyle}" />
            <!--  Style="{StaticResource RetryButtonStyle}"  -->
            <Button
                x:Name="ExitButton"
                Width="75"
                Click="ExitButton_Click"
                Content="退出"
                Style="{StaticResource ExitButtonStyle}" />
            <Button
                x:Name="ContinueButton"
                Width="75"
                Click="ContinueButton_Click"
                Content="忽略"
                Style="{StaticResource ContinueButtonStyle}" />
        </StackPanel>
    </Grid>
</Window>