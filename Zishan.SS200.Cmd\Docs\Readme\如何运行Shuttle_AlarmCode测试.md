# 如何运行 Shuttle AlarmCode 测试

## 概述

本文档说明如何运行 Shuttle AlarmCode 功能的测试和示例代码，验证实现是否正确。

## 文件位置

### 测试文件
- **路径**：`Zishan.SS200.Cmd\Docs\Test\TestShuttleAlarmCode.cs`
- **用途**：功能验证和单元测试
- **命名空间**：`Zishan.SS200.Cmd.Docs.Test`

### 示例文件
- **路径**：`Zishan.SS200.Cmd\Docs\Examples\ShuttleAlarmCodeExample.cs`
- **用途**：演示使用方法和最佳实践
- **命名空间**：`Zishan.SS200.Cmd.Docs.Examples`

## 运行测试

### 1. 基本功能测试

```csharp
using Zishan.SS200.Cmd.Docs.Test;

// 运行基本的 Shuttle 报警代码访问测试
TestShuttleAlarmCode.TestShuttleAlarmAccess();

// 运行完整的报警代码测试
TestShuttleAlarmCode.TestAllShuttleAlarmCodes();
```

### 2. 使用示例演示

```csharp
using Zishan.SS200.Cmd.Docs.Examples;

// 演示基本使用方法
ShuttleAlarmCodeExample.DemonstrateShuttleAlarmUsage();

// 演示报警状态检查
ShuttleAlarmCodeExample.CheckShuttleAlarmStatus();
```

## 测试内容

### TestShuttleAlarmCode.cs 测试内容

1. **TestShuttleAlarmAccess()** - 基本访问测试
   - 测试 SA1, SA2, SA3 报警代码的访问
   - 验证报警代码、英文描述、中文描述是否正确
   - 对比 Robot、Chamber、Shuttle 报警代码的一致性

2. **TestAllShuttleAlarmCodes()** - 完整性测试
   - 测试所有 9 个 Shuttle 报警代码（SA1-SA9）
   - 验证每个报警代码访问器是否正常工作
   - 统计成功访问的报警代码数量

### ShuttleAlarmCodeExample.cs 示例内容

1. **DemonstrateShuttleAlarmUsage()** - 基本使用演示
   - 展示如何获取 SS200InterLockMain 实例
   - 演示 Shuttle 报警代码的访问方法
   - 对比不同子系统的报警代码访问

2. **CheckShuttleAlarmStatus()** - 实际应用示例
   - 演示在实际应用中如何检查 Shuttle 报警状态
   - 展示报警处理的基本逻辑结构

## 预期输出

### 成功运行时的日志输出示例

```
INFO - 开始测试 Shuttle AlarmCode 访问...
INFO - === Shuttle 报警代码测试结果 ===
INFO - SA1 报警代码: SA1
INFO - SA1 英文描述: alarm 1 system busy command reject
INFO - SA1 中文描述: 报警1：系统忙碌，指令被拒绝
INFO - SA2 报警代码: SA2
INFO - SA2 英文描述: alarm 2 system alarm command reject
INFO - SA2 中文描述: 报警2：系统报警，指令被拒绝
INFO - SA3 报警代码: SA3
INFO - SA3 英文描述: alarm 3 cassette nest move time out
INFO - SA3 中文描述: 报警3：卡匣巢移动超时
INFO - === 对比其他子系统报警代码 ===
INFO - Robot RA1: Robot system status is busy, command reject
INFO - Chamber PAC1: chamber system status abnormal, command reject
INFO - Shuttle SA1: alarm 1 system busy command reject
INFO - Shuttle AlarmCode 测试完成！
```

## 故障排除

### 常见问题

1. **报警代码访问器为空**
   - 检查 `ShuttleErrorCodes.json` 配置文件是否存在
   - 验证 `ShuttleErrorCodesProvider` 是否正确加载配置
   - 确认 `SS200InterLockMain` 实例是否正确初始化

2. **找不到报警代码**
   - 检查枚举 `EnuShuttleAlarmCodes` 中是否定义了对应的报警代码
   - 验证 JSON 配置文件中的报警代码是否与枚举匹配

3. **中文描述显示异常**
   - 检查 JSON 配置文件的编码格式（应为 UTF-8）
   - 验证 `ChineseDescription` 字段是否正确设置

### 调试建议

1. **启用详细日志**
   ```csharp
   // 在测试前设置日志级别
   log4net.Config.BasicConfigurator.Configure();
   ```

2. **检查配置文件路径**
   ```csharp
   // 验证配置文件是否存在
   var provider = ShuttleErrorCodesProvider.Instance;
   bool loaded = provider.LoadFromJson();
   Console.WriteLine($"配置加载结果: {loaded}");
   ```

3. **单步调试**
   - 在 Visual Studio 中设置断点
   - 逐步检查每个报警代码访问器的创建过程

## 集成到现有测试框架

如果项目中使用了单元测试框架（如 NUnit、xUnit），可以将这些测试方法集成到测试套件中：

```csharp
[Test]
public void TestShuttleAlarmCodeAccess()
{
    // 调用测试方法
    TestShuttleAlarmCode.TestShuttleAlarmAccess();
    
    // 添加断言验证
    var interlock = SS200InterLockMain.Instance;
    var shuttleAlarm = interlock.AlarmCode.Shuttle.SA1_SystemBusyReject;
    
    Assert.IsNotNull(shuttleAlarm);
    Assert.AreEqual("SA1", shuttleAlarm.Code);
}
```

## 总结

通过运行这些测试和示例，可以验证 Shuttle AlarmCode 功能是否正确实现，并了解如何在实际项目中使用这些功能。建议在每次修改相关代码后都运行这些测试，确保功能的稳定性和可靠性。
