﻿AR13 
move R-axis smooth cooling chamber extend
	Robot status review
MRS1~MRS3
		MRS1 IDLE
			cooling chamber trigger status review
MCS1~MCS2
				MCS1 normal
					cooling chamber run status review
MCS3~MCS5
						MCS3 busy
							RA35 ALARM
						MCS4 idle
							slide out sensor installation review
SPS11
								SPS11=Y
									slide out sensor status review
DI19 / DI20
										DI19=0 DI20=0
											Robot T-axis position review
RS3 or others position
												RS3
													Robot Z-axis position review
RS21/RS22/RS29/RS30 or others position
														RS21 or RS22 or RS29 or RS30
															AR20-RP14
																slide out sensor status reView
DI19 / DI20
																	DI19=0 DI20=0
																		command done
																	DI19=0 DI20=1
																		RA20 ALARM
																	DI19=1 DI20=0
																		RA19 ALARM
																	DI19=1 DI20=1
																		RA21 ALARM
														others position
															RA10 ALARM
																confirm
																	AR20-RP14
																		slide out sensor status reView
DI19 / DI20
																			DI19=0 DI20=0
																				command done
																			DI19=0 DI20=1
																				RA20 ALARM
																			DI19=1 DI20=0
																				RA19 ALARM
																			DI19=1 DI20=1
																				RA21 ALARM
																cancel
																	command cancel
												others position
													RA4 ALARM
														confirm
															Robot Z-axis position review
RS21/RS22/RS29/RS30 or others position
																RS21 or RS22 or RS29 or RS30
																	AR20-RP14
																		slide out sensor status reView
DI19 / DI20
																			DI19=0 DI20=0
																				command done
																			DI19=0 DI20=1
																				RA20 ALARM
																			DI19=1 DI20=0
																				RA19 ALARM
																			DI19=1 DI20=1
																				RA21 ALARM
																others position
																	RA10 ALARM
																		confirm
																			AR20-RP14
																				slide out sensor status reView
DI19 / DI20
																					DI19=0 DI20=0
																						command done
																					DI19=0 DI20=1
																						RA20 ALARM
																					DI19=1 DI20=0
																						RA19 ALARM
																					DI19=1 DI20=1
																						RA21 ALARM
																		cancel
																			command cancel
														cancel
															command cancel
										DI19=0 DI20=1
											RA20 ALARM
										DI19=1 DI20=0
											RA19 ALARM
										DI19=1 DI20=1
											RA21 ALARM
								SPS11=N
									Robot T-axis position review
RS3 or others position
										RS3
											Robot Z-axis position review
RS21/RS22/RS29/RS30 or others position
												RS21 or RS22 or RS29 or RS30
													AR20-RP14
														slide out sensor status review
DI19 / DI20
															DI19=0 DI20=0
																command done
															DI19=0 DI20=1
																RA20 ALARM
															DI19=1 DI20=0
																RA19 ALARM
															DI19=1 DI20=1
																RA21 ALARM
												others position
													RA10 ALARM
										others position
											RA4 ALARM
												confirm
													Robot Z-axis position review
RS21/RS22/RS29/RS30 or others position
														RS21 or RS22 or RS29 or RS30
															AR20-RP14
																slide out sensor status reView
DI19 / DI20
																	DI19=0 DI20=0
																		command done
																	DI19=0 DI20=1
																		RA20 ALARM
																	DI19=1 DI20=0
																		RA19 ALARM
																	DI19=1 DI20=1
																		RA21 ALARM
														others position
															RA10 ALARM
																confirm
																	AR20-RP14
																		slide out sensor status reView
DI19 / DI20
																			DI19=0 DI20=0
																				command done
																			DI19=0 DI20=1
																				RA20 ALARM
																			DI19=1 DI20=0
																				RA19 ALARM
																			DI19=1 DI20=1
																				RA21 ALARM
																cancel
																	command cancel
												cancel
													command cancel
						MCS5 processing
							RA35 ALARM
				MCS2 alarm
					RA34 ALARM
		MRS2 BUSY
			RA1 ALARM
		MRS3 ALARM
			RA2 ALARM