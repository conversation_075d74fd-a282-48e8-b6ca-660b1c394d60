﻿using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using Zishan.SS200.Cmd.Models;
using System.Threading.Tasks;
using System;
using System.Linq;
using System.Collections.Generic;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Config;
using log4net;
using System.IO;
using System.Threading;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using System.Text;
using System.Windows.Threading;
using Prism.DryIoc;
using Prism.Ioc;
using Zishan.SS200.Cmd.Constants.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using Zishan.SS200.Cmd.Services;
using HandyControl.Tools.Extension;
using System.ComponentModel;
using System.Reflection;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using EnuSlitDoorStatus = Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber.EnuSlitDoorStatus;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle;
using System.Collections.Specialized;

namespace Zishan.SS200.Cmd.ViewModels.Dock
{
    /// <summary>
    /// 主窗口视图模型
    /// </summary>
    public partial class RobotStatusPanelViewModel : ObservableObject
    {
        #region 字段

        //private readonly IS200McuCmdService _mcuCmdService;
        private readonly ILog _logger = LogManager.GetLogger(typeof(RobotStatusPanelViewModel));

        // 创建电机告警信息解析器实例
        private readonly MotorAlarmInfoParser _motorAlarmInfoParser;

        /// <summary>
        /// 定时器，用于定时读取位置状态数据
        /// </summary>
        private readonly DispatcherTimer _readSubsystemStatusTimer;

        /// <summary>
        /// 创建线圈状态辅助类实例，用于处理线圈状态的读取和转换
        /// </summary>
        private readonly CoilStatusHelper _coilStatusHelper;

        /// <summary>
        /// ChamberA状态更新防抖定时器，避免频繁更新
        /// </summary>
        private readonly DispatcherTimer _chamberAStatusUpdateTimer;

        /// <summary>
        /// ChamberB状态更新防抖定时器，避免频繁更新
        /// </summary>
        private readonly DispatcherTimer _chamberBStatusUpdateTimer;

        /// <summary>
        /// Shuttle状态更新防抖定时器，避免频繁更新
        /// </summary>
        private readonly DispatcherTimer _shuttleStatusUpdateTimer;

        private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;

        #endregion 字段

        #region 属性

        /// <summary>
        /// 状态栏信息
        /// </summary>
        [ObservableProperty]
        private string title = "Robot位置信息";

        [ObservableProperty]
        private IS200McuCmdService _mcuCmdService;

        [ObservableProperty]
        private RobotSubsystemStatus _robotSubsystemStatus;

        [ObservableProperty]
        private ChamberASubsystemStatus _chamberASubsystemStatus;

        [ObservableProperty]
        private ChamberBSubsystemStatus _chamberBSubsystemStatus;

        [ObservableProperty]
        private ShuttleSubsystemStatus _shuttleSubsystemStatus;

        /// <summary>
        /// 状态栏信息
        /// </summary>
        [ObservableProperty]
        private string statusBarInfo;

        /// <summary>
        /// Shuttle设备是否支持线圈读取
        /// </summary>
        public bool IsShuttleCoilReadingSupported => _mcuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.Shuttle);

        /// <summary>
        /// Robot设备是否支持线圈读取
        /// </summary>
        public bool IsRobotCoilReadingSupported => _mcuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.Robot);

        /// <summary>
        /// Cha设备是否支持线圈读取
        /// </summary>
        public bool IsChaCoilReadingSupported => _mcuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.ChamberA);

        /// <summary>
        /// Chb设备是否支持线圈读取
        /// </summary>
        public bool IsChbCoilReadingSupported => _mcuCmdService.IsCoilReadingSupported(EnuMcuDeviceType.ChamberB);

        /// <summary>
        /// Robot电机寄存器映射区 报警、RTZ位置信息，使用ModbusRegister类型
        /// </summary>
        public ObservableCollection<ModbusRegister> RobotAlarmRegisters => _mcuCmdService.RobotAlarmRegisters;

        //T轴错误代码：RobotAlarmRegisters[0].Value
        //R轴错误代码：RobotAlarmRegisters[1].Value
        //Z轴错误代码：RobotAlarmRegisters[2].Value
        //定义T轴、R轴、Z轴的错误代码对应的报警信，错误信息根据上面的错误代码获取，用于UI ToolTip显示

        /// <summary>
        /// T轴错误信息，用于UI ToolTip显示
        /// </summary>
        public string TAxisErrorInfo => RobotAlarmRegisters.Count > 0 && RobotAlarmRegisters[0].Value != 0
            ? GetMotorErrorTooltip(RobotAlarmRegisters[0].Value.ToString("X4"))
            : null;

        /// <summary>
        /// R轴错误信息，用于UI ToolTip显示
        /// </summary>
        public string RAxisErrorInfo => RobotAlarmRegisters.Count > 1 && RobotAlarmRegisters[1].Value != 0
            ? GetMotorErrorTooltip(RobotAlarmRegisters[1].Value.ToString("X4"))
            : null;

        /// <summary>
        /// Z轴错误信息，用于UI ToolTip显示
        /// </summary>
        public string ZAxisErrorInfo => RobotAlarmRegisters.Count > 2 && RobotAlarmRegisters[2].Value != 0
            ? GetMotorErrorTooltip(RobotAlarmRegisters[2].Value.ToString("X4"))
            : null;

        public int TAxisStep => RobotAlarmRegisters.Count > 3 ? RobotAlarmRegisters[3].Combinevalue : 0;

        public int RAxisStep => RobotAlarmRegisters.Count > 5 ? RobotAlarmRegisters[5].Combinevalue : 0;

        public int ZAxisStep => RobotAlarmRegisters.Count > 7 ? RobotAlarmRegisters[7].Combinevalue : 0;

        /*
        步进马达：Step转换工时如下：
        T旋转轴： 步进马达100000Step 旋转360°
        R伸缩轴：  L = Sin($(步进数)/50000*360)*2*208.96
        Z上下轴：1000step/quan 导程5mm
        */

        /// <summary>
        /// T旋转轴角度值（度）
        /// 转换公式：步进值/100000*360
        /// </summary>
        public double TAxisDegree => RobotAlarmRegisters.Count > 3 ? (RobotAlarmRegisters[3].Combinevalue / 100000.0) * 360.0 : 0.0;

        /// <summary>
        /// R伸缩轴长度值（mm）
        /// 转换公式：L = Sin(步进值/50000*360)*2*208.96
        /// </summary>
        public double RAxisLength => RobotAlarmRegisters.Count > 5 ? Math.Sin((RobotAlarmRegisters[5].Combinevalue / 50000.0) * (Math.PI / 180) * 360) * 2 * 208.96 : 0.0;

        /// <summary>
        /// Z上下轴高度值（mm）
        /// 转换公式：步进值/1000*5
        /// </summary>
        public double ZAxisHeight => RobotAlarmRegisters.Count > 7 ? (RobotAlarmRegisters[7].Combinevalue / 1000.0) * 5.0 : 0.0;

        #region Robot轴位置信息Header

        /// <summary>
        /// 轴位置信息展开状态
        /// </summary>
        [ObservableProperty]
        private bool _isAxisInfoExpanded = false;

        /// <summary>
        /// IsAxisInfoExpanded属性变化时的处理方法
        /// </summary>
        partial void OnIsAxisInfoExpandedChanged(bool value)
        {
            // 不再需要通知AxisInfoHeader，因为现在使用直接绑定
        }

        #endregion Robot轴位置信息Header

        #region PinSearch P1 P2

        public ushort PinSearchP1_H => RobotAlarmRegisters.Count > 9 ? RobotAlarmRegisters[9].Value : (ushort)0;
        public ushort PinSearchP1_L => RobotAlarmRegisters.Count > 10 ? RobotAlarmRegisters[10].Value : (ushort)0;

        public ushort PinSearchP2_H => RobotAlarmRegisters.Count > 11 ? RobotAlarmRegisters[11].Value : (ushort)0;
        public ushort PinSearchP2_L => RobotAlarmRegisters.Count > 12 ? RobotAlarmRegisters[12].Value : (ushort)0;

        /// <summary>
        /// Pin Search P1点位合并值（32位整数）
        /// </summary>
        public int PinSearchP1Value => RobotAlarmRegisters.Count > 9 ? RobotAlarmRegisters[9].Combinevalue : 0;

        /// <summary>
        /// Pin Search P2点位合并值（32位整数）
        /// </summary>
        public int PinSearchP2Value => RobotAlarmRegisters.Count > 11 ? RobotAlarmRegisters[11].Combinevalue : 0;

        #endregion PinSearch P1 P2

        /// <summary>
        /// 获取封装对象以列表的形式返回，用于UI表格展示
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<StatusProperty> _statusProperties = new ObservableCollection<StatusProperty>();

        /// <summary>
        /// UI搜索文本，用于过滤状态和对应的属性值
        /// </summary>
        [ObservableProperty]
        private string _searchText;

        /// <summary>
        /// 选择的设备类型过滤器
        /// </summary>
        [ObservableProperty]
        private EnuMcuDeviceType? _selectedDeviceType;

        /// <summary>
        /// 可用的设备类型列表
        /// </summary>
        public List<DeviceTypeOption> DeviceTypeOptions { get; } = new List<DeviceTypeOption>
        {
            new DeviceTypeOption { DisplayName = "全部设备", DeviceType = null },
            new DeviceTypeOption { DisplayName = "Robot", DeviceType = EnuMcuDeviceType.Robot },
            new DeviceTypeOption { DisplayName = "Shuttle", DeviceType = EnuMcuDeviceType.Shuttle },
            new DeviceTypeOption { DisplayName = "ChamberA", DeviceType = EnuMcuDeviceType.ChamberA },
            new DeviceTypeOption { DisplayName = "ChamberB", DeviceType = EnuMcuDeviceType.ChamberB }
        };

        partial void OnSearchTextChanged(string value)
        {
            // 搜索文本变化时，无论是否在手动模式下都需要重新过滤显示
            // 因为这是用户主动的搜索操作，不是自动状态更新
            UpdateStatusPropertiesCore();
        }

        partial void OnSelectedDeviceTypeChanged(EnuMcuDeviceType? value)
        {
            // 设备类型切换时，无论是否在手动模式下都需要更新表格内容
            // 因为这是用户主动的过滤操作，不是自动状态更新
            UpdateStatusPropertiesForDeviceFilter();
        }

        /// <summary>
        /// 通过类型属性更新状态属性集合
        /// </summary>
        /// <param name="forceUpdate">强制更新，即使在手动模式下也更新</param>
        private void UpdateStatusProperties(bool forceUpdate = false)
        {
            // 如果是手动触发模式且不是强制更新，不自动更新状态表
            if (TriggerStatusSingalByHand && !forceUpdate)
                return;

            UpdateStatusPropertiesCore();
        }

        /// <summary>
        /// 用于设备类型过滤的状态属性更新（在手动模式下也会执行）
        /// </summary>
        private void UpdateStatusPropertiesForDeviceFilter()
        {
            UpdateStatusPropertiesCore();
        }

        /// <summary>
        /// 核心的状态属性更新逻辑 - 使用增量更新避免UI卡顿
        /// </summary>
        private void UpdateStatusPropertiesCore()
        {
            try
            {
                // 收集所有新的状态属性数据
                var newStatusData = new List<StatusPropertyData>();

                // 收集各子系统的状态属性数据
                CollectSubsystemProperties(RobotSubsystemStatus, EnuMcuDeviceType.Robot, newStatusData);
                CollectSubsystemProperties(ChamberASubsystemStatus, EnuMcuDeviceType.ChamberA, newStatusData);
                CollectSubsystemProperties(ChamberBSubsystemStatus, EnuMcuDeviceType.ChamberB, newStatusData);
                CollectSubsystemProperties(ShuttleSubsystemStatus, EnuMcuDeviceType.Shuttle, newStatusData);

                // 执行增量更新
                UpdateStatusPropertiesIncremental(newStatusData);
            }
            catch (Exception ex)
            {
                _logger?.Error($"更新状态属性时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 增量更新状态属性集合，避免全量刷新导致的UI卡顿
        /// </summary>
        private void UpdateStatusPropertiesIncremental(List<StatusPropertyData> newStatusData)
        {
            // 创建新数据的字典，用于快速查找
            var newDataDict = newStatusData.ToDictionary(x => x.UniqueKey, x => x);

            // 创建现有数据的字典，用于快速查找
            var existingDict = StatusProperties.ToDictionary(x => GetStatusPropertyKey(x), x => x);

            // 1. 更新现有项目或标记需要移除的项目
            var itemsToRemove = new List<StatusProperty>();
            foreach (var existingItem in StatusProperties.ToList())
            {
                var key = GetStatusPropertyKey(existingItem);
                if (newDataDict.TryGetValue(key, out var newData))
                {
                    // 项目存在，检查是否需要更新值
                    if (existingItem.Value != newData.Value)
                    {
                        existingItem.Value = newData.Value;
                        // 如果不是手动修改状态，更新原始值
                        if (!existingItem.IsModified)
                        {
                            existingItem.SaveAsOriginal();
                        }
                    }
                }
                else
                {
                    // 项目不再存在，标记为需要移除
                    itemsToRemove.Add(existingItem);
                }
            }

            // 2. 移除不再存在的项目
            foreach (var item in itemsToRemove)
            {
                StatusProperties.Remove(item);
            }

            // 3. 添加新的项目
            foreach (var newData in newStatusData)
            {
                if (!existingDict.ContainsKey(newData.UniqueKey))
                {
                    var statusProperty = CreateStatusPropertyFromData(newData);
                    StatusProperties.Add(statusProperty);
                }
            }
        }

        /// <summary>
        /// 获取状态属性的唯一键
        /// </summary>
        private string GetStatusPropertyKey(StatusProperty property)
        {
            return $"{property.DeviceType}_{property.PropertyPath}";
        }

        /// <summary>
        /// 收集子系统的状态属性数据
        /// </summary>
        private void CollectSubsystemProperties(object subsystemStatus, EnuMcuDeviceType deviceType, List<StatusPropertyData> statusData)
        {
            if (subsystemStatus == null) return;

            var subsystemType = deviceType.ToString();
            var properties = subsystemStatus.GetType().GetProperties();

            foreach (var property in properties)
            {
                // 应用设备类型过滤
                if (SelectedDeviceType.HasValue && SelectedDeviceType.Value != deviceType)
                    continue;

                // 应用搜索过滤
                if (!string.IsNullOrEmpty(SearchText) &&
                    !property.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase))
                    continue;

                try
                {
                    var value = property.GetValue(subsystemStatus);
                    var displayName = $"[{subsystemType}] {property.Name}";

                    var statusPropertyData = new StatusPropertyData
                    {
                        UniqueKey = $"{deviceType}_{property.Name}",
                        Name = displayName,
                        Value = value?.ToString() ?? "null",
                        DeviceType = deviceType,
                        SubsystemType = subsystemType,
                        IsEditable = IsSimpleEditableType(property.PropertyType),
                        PropertyType = property.PropertyType,
                        PropertyPath = property.Name,
                        TargetObject = subsystemStatus
                    };

                    // 如果是枚举类型，获取所有枚举值
                    if (property.PropertyType.IsEnum)
                    {
                        statusPropertyData.EnumOptions = Enum.GetValues(property.PropertyType).Cast<object>().ToList();
                    }

                    statusData.Add(statusPropertyData);
                }
                catch (Exception ex)
                {
                    _logger?.Error($"收集属性 {property.Name} 数据时发生错误: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 从状态属性数据创建StatusProperty对象
        /// </summary>
        private StatusProperty CreateStatusPropertyFromData(StatusPropertyData data)
        {
            var statusProperty = new StatusProperty
            {
                Name = data.Name,
                Value = data.Value,
                DeviceType = data.DeviceType,
                SubsystemType = data.SubsystemType,
                IsEditable = data.IsEditable,
                PropertyType = data.PropertyType,
                PropertyPath = data.PropertyPath,
                TargetObject = data.TargetObject,
                EnumOptions = data.EnumOptions
            };

            // 保存原始值
            statusProperty.SaveAsOriginal();
            return statusProperty;
        }

        /// <summary>
        /// 状态属性数据传输对象，用于增量更新
        /// </summary>
        private class StatusPropertyData
        {
            public string UniqueKey { get; set; }
            public string Name { get; set; }
            public string Value { get; set; }
            public EnuMcuDeviceType DeviceType { get; set; }
            public string SubsystemType { get; set; }
            public bool IsEditable { get; set; }
            public Type PropertyType { get; set; }
            public string PropertyPath { get; set; }
            public object TargetObject { get; set; }
            public List<object> EnumOptions { get; set; }
        }



        /// <summary>
        /// 增量更新指定子系统的状态属性（避免影响其他子系统）
        /// 已废弃：现在使用全局增量更新机制，此方法保留用于兼容性
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="subsystemStatus">子系统状态对象</param>
        [Obsolete("此方法已废弃，现在使用UpdateStatusPropertiesCore的全局增量更新机制")]
        private void UpdateSubsystemStatusProperties(EnuMcuDeviceType deviceType, object subsystemStatus)
        {
            // 直接调用全局增量更新，性能更好
            UpdateStatusPropertiesCore();
        }

        /// <summary>
        /// 获取设备类型的显示名称
        /// </summary>
        /// <param name="deviceType">设备类型枚举</param>
        /// <returns>显示名称</returns>
        private string GetDeviceTypeDisplayName(EnuMcuDeviceType deviceType)
        {
            // 优先从DeviceTypeOptions中查找对应的显示名称，如果找不到则使用枚举名称
            var option = DeviceTypeOptions.FirstOrDefault(opt => opt.DeviceType == deviceType);
            return option?.DisplayName ?? deviceType.ToString();
        }

        /// <summary>
        /// 验证ChamberA和ChamberB是否为独立的实例对象
        /// </summary>
        /// <returns>验证结果和详细信息</returns>
        private (bool IsIndependent, string Details) ValidateChamberInstanceIndependence()
        {
            try
            {
                bool isIndependent = !ReferenceEquals(ChamberASubsystemStatus, ChamberBSubsystemStatus);

                var details = new System.Text.StringBuilder();
                details.AppendLine("ChamberA和ChamberB实例独立性验证:");
                details.AppendLine($"  ChamberA实例哈希码: {ChamberASubsystemStatus?.GetHashCode() ?? 0}");
                details.AppendLine($"  ChamberB实例哈希码: {ChamberBSubsystemStatus?.GetHashCode() ?? 0}");
                details.AppendLine($"  是否为同一实例: {ReferenceEquals(ChamberASubsystemStatus, ChamberBSubsystemStatus)}");
                details.AppendLine($"  实例独立性验证: {(isIndependent ? "✓ 通过" : "✗ 失败")}");

                if (!isIndependent)
                {
                    details.AppendLine("  ❌ 严重问题：ChamberA和ChamberB使用了同一个实例对象！");
                    details.AppendLine("  这会导致一个Chamber的状态更新影响另一个Chamber。");
                    details.AppendLine("  请检查依赖注入配置，确保ChamberSubsystemStatus不是单例。");
                }

                return (isIndependent, details.ToString());
            }
            catch (Exception ex)
            {
                return (false, $"验证过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加子系统属性到状态集合
        /// </summary>
        /// <param name="subsystemStatus">子系统状态对象</param>
        /// <param name="deviceType">设备类型</param>
        /// <param name="subsystemType">子系统类型名称（可选，如果为null则使用设备类型名称）</param>
        private void AddSubsystemProperties(object subsystemStatus, EnuMcuDeviceType deviceType, string subsystemType = null)
        {
            if (subsystemStatus == null) return;

            // 如果没有提供子系统类型名称，使用设备类型名称
            if (string.IsNullOrEmpty(subsystemType))
            {
                subsystemType = GetDeviceTypeDisplayName(deviceType);
            }

            // 设备类型过滤
            if (SelectedDeviceType.HasValue && SelectedDeviceType.Value != deviceType)
            {
                return;
            }

            // 获取子系统状态的所有属性
            var properties = subsystemStatus.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                // 获取属性值
                var value = property.GetValue(subsystemStatus);

                // 获取属性描述（如果有）
                string displayName = property.Name;
                var descAttr = property.GetCustomAttribute<DisplayNameAttribute>();
                if (descAttr != null)
                {
                    displayName = descAttr.DisplayName;
                }

                // 添加子系统前缀
                string fullDisplayName = $"{displayName}";//$"[{subsystemType}] {displayName}";

                // 如果有搜索文本，则过滤
                if (!string.IsNullOrEmpty(SearchText))
                {
                    // 如果属性名或值不包含搜索文本，则跳过
                    if (!fullDisplayName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) &&
                        !(value?.ToString()?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false))
                    {
                        continue;
                    }
                }

                // 创建状态属性
                var statusProperty = new StatusProperty
                {
                    Name = fullDisplayName,
                    Value = value?.ToString() ?? "null",
                    DeviceType = deviceType,
                    SubsystemType = subsystemType,
                    IsEditable = IsSimpleEditableType(property.PropertyType),
                    PropertyType = property.PropertyType,
                    PropertyPath = property.Name,
                    TargetObject = subsystemStatus
                };

                // 如果是枚举类型，获取所有枚举值
                if (property.PropertyType.IsEnum)
                {
                    statusProperty.EnumOptions = Enum.GetValues(property.PropertyType).Cast<object>().ToList();
                }

                // 保存原始值
                statusProperty.SaveAsOriginal();

                // 添加到集合
                StatusProperties.Add(statusProperty);
            }
        }

        /// <summary>
        /// 是否手动触发状态信号
        /// </summary>
        [ObservableProperty]
        private bool _triggerStatusSingalByHand;

        partial void OnTriggerStatusSingalByHandChanged(bool value)
        {
            if (value)
            {
                // 切换到手动模式时，保存当前所有值作为原始值
                SaveAllCurrentValuesAsOriginal();
            }
            else
            {
                // 切换回自动模式时，清除所有手动修改，恢复自动更新
                RestoreAutoMode();
            }
        }

        #endregion 属性

        #region 构造函数

        public RobotStatusPanelViewModel() : this(new S200McuCmdService(), new RobotSubsystemStatus(), new ChamberASubsystemStatus(), new ChamberBSubsystemStatus(), new ShuttleSubsystemStatus())
        {
            // 注意：现在ChamberSubsystemStatus是抽象类，只能通过具体的子类实例化
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mcuCmdService">MCU命令服务</param>
        /// <param name="robotSubsystemStatus">Robot子系统状态</param>
        /// <param name="chamberASubsystemStatus">ChamberA子系统状态</param>
        /// <param name="chamberBSubsystemStatus">ChamberB子系统状态</param>
        /// <param name="shuttleSubsystemStatus">Shuttle子系统状态</param>
        public RobotStatusPanelViewModel(IS200McuCmdService mcuCmdService, RobotSubsystemStatus robotSubsystemStatus, ChamberASubsystemStatus chamberASubsystemStatus, ChamberBSubsystemStatus chamberBSubsystemStatus, ShuttleSubsystemStatus shuttleSubsystemStatus)
        {
            McuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
            RobotSubsystemStatus = robotSubsystemStatus ?? throw new ArgumentNullException(nameof(robotSubsystemStatus));
            ChamberASubsystemStatus = chamberASubsystemStatus ?? throw new ArgumentNullException(nameof(chamberASubsystemStatus));
            ChamberBSubsystemStatus = chamberBSubsystemStatus ?? throw new ArgumentNullException(nameof(chamberBSubsystemStatus));
            ShuttleSubsystemStatus = shuttleSubsystemStatus ?? throw new ArgumentNullException(nameof(shuttleSubsystemStatus));

            // 创建线圈状态辅助类实例
            _coilStatusHelper = new CoilStatusHelper(McuCmdService);

            #region 使用举例说明

            var robotCoils = McuCmdService.RobotInputCoils;
            // Robot设备的输入线圈集合，通过 EnuRobotDICodes枚举做为Key，对象放到字典中去

            // 使用新的App.GetInstance<T>方法获取IOC容器中的实例
            var mcuService = App.GetInstance<IS200McuCmdService>();
            var mainViewModel = App.GetInstance<MainWindowViewModel>();

            // 原来的方式（已废弃）
            // var a = System.Windows.Application.Current as App;
            // IContainerProvider c = a.Container;
            // var ss2 = c.Resolve<SS200InterLock>();

            //添加读取测试数据，子元素一开始要实例化，否则会报错,使用索引，不存在容易报错，通过字典：key使用枚举类型，对应的值对象

            /*
            var test = Ss200InterLock.IoInterface.ShuttleInputCoils[0]?.Coilvalue ?? false; // 测试获取Shuttle设备的第一个输入线圈值

            Ss200InterLock.RobotAlarmItems[0].Code = "T轴错误"; // 测试设置Robot设备的第一个报警项内容，不存在容易报错，通过字典：key使用枚举类型，对应的值对象
            var test2 = Ss200InterLock.DicRobotAlarmItems[EnuRobotErrorCode.RA1_Robot系统忙被拒绝].Code;//确保每个枚举值都有对应的报警项
            */

            #endregion 使用举例说明

            //根据当前的RTZ位置信息，更新SS200子系统状态模型的值

            // 初始化电机告警信息解析器
            try
            {
                string configPath = "Configs/AlarmInfo/MotorAlarmInfo.json";

                // 使用配置辅助类获取配置文件路径
                configPath = App.ConfigHelper.GetConfigFilePath(configPath);

                // 检查文件是否存在
                if (!File.Exists(configPath))
                {
                    _logger?.Error($"电机告警信息配置文件不存在: {configPath}");
                    throw new FileNotFoundException($"找不到电机告警信息配置文件: {configPath}");
                }

                _logger?.Info($"初始化电机告警信息解析器，配置文件路径: {configPath}");
                _motorAlarmInfoParser = new MotorAlarmInfoParser(configPath);

                // 设置错误提示提供器为自身的GetMotorErrorTooltip方法
                _logger?.Info("电机告警信息解析器初始化成功");

                // 初始化 DispatcherTimer
                _readSubsystemStatusTimer = new DispatcherTimer();
                _readSubsystemStatusTimer.Interval = TimeSpan.FromSeconds(1);
                _readSubsystemStatusTimer.Tick += async (sender, e) => await ReadSubsystemStatusTimer_TickAsync(sender, e);

                // 初始化ChamberA状态更新防抖定时器
                _chamberAStatusUpdateTimer = new DispatcherTimer();
                _chamberAStatusUpdateTimer.Interval = TimeSpan.FromMilliseconds(500); // 500ms防抖延迟
                _chamberAStatusUpdateTimer.Tick += (sender, e) =>
                {
                    _chamberAStatusUpdateTimer.Stop();
                    UpdateChamberASubsystemStatus();
                };

                // 初始化ChamberB状态更新防抖定时器
                _chamberBStatusUpdateTimer = new DispatcherTimer();
                _chamberBStatusUpdateTimer.Interval = TimeSpan.FromMilliseconds(500); // 500ms防抖延迟
                _chamberBStatusUpdateTimer.Tick += (sender, e) =>
                {
                    _chamberBStatusUpdateTimer.Stop();
                    UpdateChamberBSubsystemStatus();
                };

                // 初始化Shuttle状态更新防抖定时器
                _shuttleStatusUpdateTimer = new DispatcherTimer();
                _shuttleStatusUpdateTimer.Interval = TimeSpan.FromMilliseconds(500); // 500ms防抖延迟
                _shuttleStatusUpdateTimer.Tick += (sender, e) =>
                {
                    _shuttleStatusUpdateTimer.Stop();
                    UpdateShuttleSubsystemStatus(true);
                };
            }
            catch (Exception ex)
            {
                _logger?.Error($"初始化电机告警信息解析器失败: {ex.Message}", ex);
            }

            // 初始化寄存器属性变化处理器
            InitializeRegisterPropertyChangeHandlers();

            // 初始化Chamber线圈变化监听器
            InitializeChamberCoilChangeHandlers();

            // 初始化Shuttle线圈变化监听器
            InitializeShuttleCoilChangeHandlers();

            // 验证ChamberA和ChamberB实例独立性（关键修复验证）
            var (isIndependent, details) = ValidateChamberInstanceIndependence();
            if (!isIndependent)
            {
                _logger?.Error($"ChamberA和ChamberB实例独立性验证失败:\n{details}");
                throw new InvalidOperationException("ChamberA和ChamberB使用了同一个实例对象，这会导致状态更新相互影响。请检查依赖注入配置。");
            }
            else
            {
                _logger?.Info($"ChamberA和ChamberB实例独立性验证通过:\n{details}");
            }

            // 初始化时更新一次子系统状态
            UpdateRobotSubsystemStatus();
            UpdateChamberSubsystemStatus();
            UpdateShuttleSubsystemStatus();

            // 初始化StatusProperties集合（强制更新）
            UpdateStatusProperties(forceUpdate: true);

            // 在开发调试模式下启用手动触发模式
            if (Golbal.IsDevDebug)
            {
                TriggerStatusSingalByHand = false;  // true才是手动模式，false是自动模式
            }
        }

        #endregion 构造函数

        #region 命令

        /// <summary>
        /// 复制当前轴值到剪贴板（异步版本）
        /// ✅ 优化报告修复：使用异步版本避免UI线程阻塞
        /// </summary>
        /// <param name="text">要复制的文本</param>
        [RelayCommand]
        private async Task CopyCurrentAxisValue(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                StatusBarInfo = "无法复制空文本";
                HcGrowlExtensions.Warning(StatusBarInfo);
                _logger?.Warn($"尝试复制空文本");
                return;
            }

            try
            {
                if (await TrySetClipboardTextAsync(text))
                {
                    StatusBarInfo = $"已复制值 {text} 到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info($"成功复制值 {text} 到剪贴板");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Error(StatusBarInfo);
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制到剪贴板失败: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error($"复制错误信息到剪贴板失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 复制所有轴位置到剪贴板（异步版本）
        /// ✅ 优化报告修复：使用异步版本避免UI线程阻塞
        /// </summary>
        [RelayCommand]
        private async Task CopyAllAxisPositions()
        {
            try
            {
                // 获取三个轴的位置值
                var tAxisValue = RobotAlarmRegisters[3].Combinevalue.ToString();
                var rAxisValue = RobotAlarmRegisters[5].Combinevalue.ToString();
                var zAxisValue = RobotAlarmRegisters[7].Combinevalue.ToString();

                // 组合成格式化的字符串
                var allValues = $"T轴: {tAxisValue}, R轴: {rAxisValue}, Z轴: {zAxisValue}";

                // 复制到剪贴板
                if (await TrySetClipboardTextAsync(allValues))
                {
                    StatusBarInfo = $"已复制所有轴位置 {allValues} 到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info($"成功复制所有轴位置到剪贴板: {allValues}");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Warn("复制所有轴位置到剪贴板失败");
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制所有轴位置到剪贴板时发生错误: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error("复制所有轴位置到剪贴板时发生错误", ex);
            }
        }

        /// <summary>
        /// 复制所有Pin Search位置值到剪贴板
        /// </summary>
        [RelayCommand]
        private void CopyAllPinSearchValues()
        {
            try
            {
                // 获取Pin Search的位置值，使用整数格式
                var p1Value = PinSearchP1Value.ToString();
                var p2Value = PinSearchP2Value.ToString();

                // 组合成格式化的字符串
                var allValues = $"P1: {p1Value}, P2: {p2Value}";

                // 复制到剪贴板
                if (TrySetClipboardText(allValues))
                {
                    StatusBarInfo = $"已复制所有Pin Search值 {allValues} 到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info($"成功复制所有Pin Search值到剪贴板: {allValues}");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Warn("复制所有Pin Search位置值到剪贴板失败");
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制所有Pin Search位置值到剪贴板时发生错误: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error("复制所有Pin Search位置值到剪贴板时发生错误", ex);
            }
        }

        /// <summary>
        /// 复制当前轴错误信息到剪贴板
        /// </summary>
        /// <param name="parameters">包含错误代码和轴名称的参数</param>
        [RelayCommand]
        private void CopyCurrentErrorInfo(object parameters)
        {
            try
            {
                if (parameters is not object[] args || args.Length < 2 ||
                    args[0] is not string errorCode || args[1] is not string axisName)
                {
                    StatusBarInfo = "参数错误，无法复制";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Warn($"复制错误信息参数无效: parameters={parameters}");
                    return;
                }

                if (string.IsNullOrEmpty(errorCode) || errorCode == "0x0000")
                {
                    StatusBarInfo = "当前无错误信息可复制";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Info($"尝试复制空错误信息: axisName={axisName}, errorCode={errorCode}");
                    return;
                }

                string errorInfo = GetMotorErrorTooltip(errorCode.Replace("0x", ""));
                if (string.IsNullOrEmpty(errorInfo))
                {
                    StatusBarInfo = $"未找到 {axisName} 对应的错误信息";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Warn($"未找到错误信息: axisName={axisName}, errorCode={errorCode}");
                    return;
                }

                // 组织格式化的信息
                string formattedInfo = $"{axisName}错误 {errorCode}：\r\n{errorInfo}";

                if (TrySetClipboardText(formattedInfo))
                {
                    StatusBarInfo = $"已复制{axisName}错误信息到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info($"成功复制错误信息到剪贴板: axisName={axisName}, errorCode={errorCode}");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Error(StatusBarInfo);
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制到剪贴板失败: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error($"复制错误信息到剪贴板失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 复制所有轴的错误信息到剪贴板
        /// </summary>
        [RelayCommand]
        private void CopyAllAxisErrorInfo()
        {
            try
            {
                var builder = new System.Text.StringBuilder();
                bool hasError = false;

                // 获取T轴错误信息
                if (RobotAlarmRegisters.Count > 0 && RobotAlarmRegisters[0].Value != 0)
                {
                    string tErrorCode = RobotAlarmRegisters[0].Value.ToString("X4");
                    string tErrorInfo = GetMotorErrorTooltip(tErrorCode);
                    builder.AppendLine($"T轴错误(0x{tErrorCode})：");
                    builder.AppendLine(tErrorInfo);
                    builder.AppendLine();
                    hasError = true;
                    _logger?.Debug($"获取到T轴错误信息: errorCode=0x{tErrorCode}");
                }

                // 获取R轴错误信息
                if (RobotAlarmRegisters.Count > 1 && RobotAlarmRegisters[1].Value != 0)
                {
                    string rErrorCode = RobotAlarmRegisters[1].Value.ToString("X4");
                    string rErrorInfo = GetMotorErrorTooltip(rErrorCode);
                    builder.AppendLine($"R轴错误(0x{rErrorCode})：");
                    builder.AppendLine(rErrorInfo);
                    builder.AppendLine();
                    hasError = true;
                    _logger?.Debug($"获取到R轴错误信息: errorCode=0x{rErrorCode}");
                }

                // 获取Z轴错误信息
                if (RobotAlarmRegisters.Count > 2 && RobotAlarmRegisters[2].Value != 0)
                {
                    string zErrorCode = RobotAlarmRegisters[2].Value.ToString("X4");
                    string zErrorInfo = GetMotorErrorTooltip(zErrorCode);
                    builder.AppendLine($"Z轴错误(0x{zErrorCode})：");
                    builder.AppendLine(zErrorInfo);
                    hasError = true;
                    _logger?.Debug($"获取到Z轴错误信息: errorCode=0x{zErrorCode}");
                }

                if (!hasError)
                {
                    StatusBarInfo = "当前无错误信息可复制";
                    HcGrowlExtensions.Warning(StatusBarInfo);
                    _logger?.Info("尝试复制错误信息时发现无错误");
                    return;
                }

                if (TrySetClipboardText(builder.ToString()))
                {
                    StatusBarInfo = "已复制所有轴错误信息到剪贴板";
                    HcGrowlExtensions.Success(StatusBarInfo);
                    _logger?.Info("成功复制所有轴错误信息到剪贴板");
                }
                else
                {
                    StatusBarInfo = "复制到剪贴板失败";
                    HcGrowlExtensions.Error(StatusBarInfo);
                }
            }
            catch (Exception ex)
            {
                StatusBarInfo = $"复制到剪贴板失败: {ex.Message}";
                HcGrowlExtensions.Error(StatusBarInfo);
                _logger?.Error($"复制所有轴错误信息到剪贴板失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 解析状态测试命令
        /// </summary>
        [RelayCommand]
        private void OnParseStatusTest()
        {
            McuCmdService.GetRobotSpecilModbusCoil(EnuRobotDICodes.RDI1_PaddleSensor1Left, out var robotStatus);
            if (robotStatus == null)
            {
                return;
            }

            // ChamberA解析状态测试命令
            ChamberASubsystemStatus.TriggerStatus = EnuTriggerStatus.NoAlarm;
            ChamberASubsystemStatus.RunStatus = EnuRunStatus.Idle;
            ChamberASubsystemStatus.SlitDoorStatus = EnuSlitDoorStatus.Open;
            ChamberASubsystemStatus.C1ValveStatus = EnuValveStatus.Open;
            ChamberASubsystemStatus.C2ValveStatus = EnuValveStatus.Close;
            ChamberASubsystemStatus.C3ValveStatus = EnuValveStatus.BetweenOpenClose;
            ChamberASubsystemStatus.C4ValveStatus = EnuValveStatus.None;

            // ChamberB解析状态测试命令
            ChamberBSubsystemStatus.TriggerStatus = EnuTriggerStatus.Alarm;
            ChamberBSubsystemStatus.RunStatus = EnuRunStatus.Processing;
            ChamberBSubsystemStatus.SlitDoorStatus = EnuSlitDoorStatus.Close;
            ChamberBSubsystemStatus.C1ValveStatus = EnuValveStatus.Close;
            ChamberBSubsystemStatus.C2ValveStatus = EnuValveStatus.Open;
            ChamberBSubsystemStatus.C3ValveStatus = EnuValveStatus.Open;
            ChamberBSubsystemStatus.C4ValveStatus = EnuValveStatus.BetweenOpenClose;

            // Shuttle解析状态测试命令
            ShuttleSubsystemStatus.ShuttleStatus = EnuShuttleStatus.Idle;
            ShuttleSubsystemStatus.ShuttlePositionStatus = EnuShuttlePositionStatus.ShuttleUpShuttle1Outer;
            ShuttleSubsystemStatus.Shuttle1Cassette1LotStatus = EnuLotStatus.HasLot;

            // 访问槽位状态
            ShuttleSubsystemStatus.SlotStatusManager.Shuttle1Cassette1SlotStatus[1] = EnuSlotWaferStatus.HasWafer;
            ShuttleSubsystemStatus.SlotStatusManager.ChalWaferStatus = EnuSlotWaferStatus.NoWafer;

            var msg = $"子系统状态:RobotStatus：\r\n{robotStatus}r\n\r\nChamberASubsystemStatus：\r\n{ChamberASubsystemStatus}\r\n\r\nChamberBSubsystemStatus：\r\n{ChamberBSubsystemStatus}\r\n\r\nShuttle子系统状态:\r\n{ShuttleSubsystemStatus}";
            HcGrowlExtensions.Info(msg);
        }

        /// <summary>
        /// 更新Robot子系统状态
        /// </summary>
        [RelayCommand]
        private void OnUpdateRobotSubsystemStatus()
        {
            //UpdateRobotSubsystemStatus(true);

            var msg = $"Robot子系统状态已更新:\r\n{RobotSubsystemStatus}";
            HcGrowlExtensions.Info(msg);

            // 更新状态表格数据，自动触发，可以注释掉
            //UpdateStatusProperties();
        }

        /// <summary>
        /// 更新Chamber子系统状态
        /// </summary>
        [RelayCommand]
        private void OnUpdateChamberSubsystemStatus()
        {
            UpdateChamberSubsystemStatus(true);

            var msg = $"Chamber子系统状态已更新:\r\nChamberA:\r\n{ChamberASubsystemStatus}\r\n\r\nChamberB:\r\n{ChamberBSubsystemStatus}";
            HcGrowlExtensions.Info(msg);
        }

        /// <summary>
        /// 更新Shuttle子系统状态
        /// </summary>
        [RelayCommand]
        private async Task OnUpdateShuttleSubsystemStatus()
        {
            await SavaDataToRedis();

            //UpdateShuttleSubsystemStatus(true);

            /*
             var msg = $"Shuttle子系统状态已更新:\r\n" +
                $"ShuttleStatus: {ShuttleSubsystemStatus.ShuttleStatus}\r\n" +
                $"Ssc6Config: {ShuttleSubsystemStatus.Ssc6Config}\r\n" +
                $"ShuttlePositionStatus: {ShuttleSubsystemStatus.ShuttlePositionStatus}\r\n" +
                $"CassetteDoorNestStatus: {ShuttleSubsystemStatus.CassetteDoorNestStatus}\r\n" +
                $"ShuttleIsoValveStatus: {ShuttleSubsystemStatus.ShuttleIsoValveStatus}\r\n" +
                $"ShuttleXvValveStatus: {ShuttleSubsystemStatus.ShuttleXvValveStatus}\r\n" +
                $"ShuttleBackfillValveStatus: {ShuttleSubsystemStatus.ShuttleBackfillValveStatus}\r\n" +
                $"LoadlockBleedValveStatus: {ShuttleSubsystemStatus.LoadlockBleedValveStatus}\r\n" +
                $"LoadlockBackfillValveStatus: {ShuttleSubsystemStatus.LoadlockBackfillValveStatus}\r\n" +
                $"批次状态: \r\n" +
                $"LSD1={ShuttleSubsystemStatus.Shuttle1Cassette1LotStatus}, " +
                $"LSD2={ShuttleSubsystemStatus.Shuttle1Cassette2LotStatus}, " +
                $"LSD3={ShuttleSubsystemStatus.Shuttle2Cassette1LotStatus}, " +
                $"LSD4={ShuttleSubsystemStatus.Shuttle2Cassette2LotStatus}";
            HcGrowlExtensions.Info(msg);
            */
        }

        #region 保存当前InterLock数据到Redis，便于实时监控和数据分析

        /// <summary>
        /// 保存当前InterLock数据到Redis，便于实时监控和数据分析
        /// </summary>
        /// <param name="includeIOData">是否包含IO接口数据</param>
        /// <param name="includeAlarmData">是否包含报警数据</param>
        /// <param name="includeConfigData">是否包含配置数据</param>
        /// <param name="includeAxisData">是否包含轴位置数据</param>
        /// <returns>保存操作结果</returns>
        private async Task<bool> SavaDataToRedis(bool includeIOData = true, bool includeAlarmData = true,
            bool includeConfigData = false, bool includeAxisData = true)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var timestamp = DateTime.Now;
            var timestampStr = timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var unixTimestamp = ((DateTimeOffset)timestamp).ToUnixTimeSeconds();

            try
            {
                if (!RedisHelper.IsConnected)
                {
                    _logger.Warn("Redis连接未建立，跳过数据保存");
                    return false;
                }

                // 1. 保存完整状态快照
                var success = await SaveCompleteStatusSnapshot(timestampStr, unixTimestamp);

                // 2. 保存IO接口数据（可选）
                if (includeIOData && success)
                {
                    success &= await SaveIOInterfaceData(timestampStr);
                }

                // 3. 保存报警数据（可选）
                if (includeAlarmData && success)
                {
                    success &= await SaveAlarmData(timestampStr);
                }

                // 4. 保存配置数据（可选）
                if (includeConfigData && success)
                {
                    success &= await SaveConfigurationData(timestampStr);
                }

                // 5. 保存轴位置数据（可选）
                if (includeAxisData && success)
                {
                    success &= await SaveAxisPositionData(timestampStr);
                }

                // 6. 保存时间序列数据用于趋势分析
                if (success)
                {
                    success &= await SaveTimeSeriesData(unixTimestamp);
                }

                stopwatch.Stop();

                if (success)
                {
                    _logger.Info($"InterLock数据保存到Redis成功，耗时: {stopwatch.ElapsedMilliseconds}ms");
                }
                else
                {
                    _logger.Error($"InterLock数据保存到Redis失败，耗时: {stopwatch.ElapsedMilliseconds}ms");
                }

                return success;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.Error($"保存InterLock数据到Redis异常，耗时: {stopwatch.ElapsedMilliseconds}ms", ex);
                return false;
            }
        }

        /// <summary>
        /// 保存完整状态快照到Redis
        /// </summary>
        /// <param name="timestampStr">时间戳字符串</param>
        /// <param name="unixTimestamp">Unix时间戳</param>
        /// <returns>保存是否成功</returns>
        private async Task<bool> SaveCompleteStatusSnapshot(string timestampStr, long unixTimestamp)
        {
            try
            {
                // 构建完整的状态快照数据
                var statusSnapshot = new
                {
                    Timestamp = timestampStr,
                    UnixTimestamp = unixTimestamp,
                    DeviceInfo = new
                    {
                        DeviceType = "SS200",
                        Version = "1.0.0",
                        Location = "RD Dept"
                    },
                    SubsystemStatus = new
                    {
                        Robot = _interlock.SubsystemStatus.Robot.Status,
                        ChamberA = _interlock.SubsystemStatus.ChamberA.Status,
                        ChamberB = _interlock.SubsystemStatus.ChamberB.Status,
                        Shuttle = _interlock.SubsystemStatus.Shuttle.Status
                    }
                };

                // 保存当前状态快照（不过期）
                var currentSuccess = await Task.Run(() =>
                    RedisHelper.StringSet("SS200:Status:Current", statusSnapshot, -1, RedisFolderEnum.Root, RedisDBEnum.Six));

                // 保存历史状态快照（24小时过期）
                var historyKey = $"SS200:Status:History:{timestampStr.Replace(":", "-").Replace(" ", "_")}";
                var historySuccess = await Task.Run(() =>
                    RedisHelper.StringSet(historyKey, statusSnapshot, 1440, RedisFolderEnum.Root, RedisDBEnum.Six));

                // 保存到Hash结构便于字段级查询
                var hashSuccess = await Task.Run(() =>
                    RedisHelper.HashSet("SS200:Status:Hash", timestampStr, statusSnapshot, RedisFolderEnum.Root, RedisDBEnum.Six));

                return currentSuccess && historySuccess && hashSuccess;
            }
            catch (Exception ex)
            {
                _logger.Error("保存完整状态快照失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 保存IO接口数据到Redis
        /// </summary>
        /// <param name="timestampStr">时间戳字符串</param>
        /// <returns>保存是否成功</returns>
        private async Task<bool> SaveIOInterfaceData(string timestampStr)
        {
            try
            {
                // 构建完整的IO接口数据
                var ioData = new
                {
                    Timestamp = timestampStr,
                    Robot = new
                    {
                        // Robot数字输入状态 (RDI1-RDI4)
                        DI = new
                        {
                            RDI1_PaddleSensor1Left = new
                            {
                                Value = _interlock.IOInterface.Robot.RDI1_PaddleSensor1Left.Value,
                                Content = _interlock.IOInterface.Robot.RDI1_PaddleSensor1Left.Content,
                                IsActive = _interlock.IOInterface.Robot.RDI1_PaddleSensor1Left.IsActive
                            },
                            RDI2_PaddleSensor2Right = new
                            {
                                Value = _interlock.IOInterface.Robot.RDI2_PaddleSensor2Right.Value,
                                Content = _interlock.IOInterface.Robot.RDI2_PaddleSensor2Right.Content,
                                IsActive = _interlock.IOInterface.Robot.RDI2_PaddleSensor2Right.IsActive
                            },
                            RDI3_PinSearch1 = new
                            {
                                Value = _interlock.IOInterface.Robot.RDI3_PinSearch1.Value,
                                Content = _interlock.IOInterface.Robot.RDI3_PinSearch1.Content,
                                IsActive = _interlock.IOInterface.Robot.RDI3_PinSearch1.IsActive
                            },
                            RDI4_PinSearch2 = new
                            {
                                Value = _interlock.IOInterface.Robot.RDI4_PinSearch2.Value,
                                Content = _interlock.IOInterface.Robot.RDI4_PinSearch2.Content,
                                IsActive = _interlock.IOInterface.Robot.RDI4_PinSearch2.IsActive
                            }
                        }
                    },
                    ChamberA = new
                    {
                        // Chamber A数字输入状态 (PDI1-PDI15)
                        DI = new
                        {
                            PDI1_CvOpenSensor = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDI1_CvOpenSensor.Value,
                                Content = _interlock.IOInterface.ChamberA.PDI1_CvOpenSensor.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDI1_CvOpenSensor.IsActive
                            },
                            PDI2_CvCloseSensor = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDI2_CvCloseSensor.Value,
                                Content = _interlock.IOInterface.ChamberA.PDI2_CvCloseSensor.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDI2_CvCloseSensor.IsActive
                            },
                            PDI12_SlitDoorOpenSensor = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value,
                                Content = _interlock.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.IsActive
                            },
                            PDI13_SlitDoorCloseSensor = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor.Value,
                                Content = _interlock.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDI13_SlitDoorCloseSensor.IsActive
                            },
                            PDI14_LiftPinUpSensor = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDI14_LiftPinUpSensor.Value,
                                Content = _interlock.IOInterface.ChamberA.PDI14_LiftPinUpSensor.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDI14_LiftPinUpSensor.IsActive
                            },
                            PDI15_LiftPinDownSensor = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDI15_LiftPinDownSensor.Value,
                                Content = _interlock.IOInterface.ChamberA.PDI15_LiftPinDownSensor.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDI15_LiftPinDownSensor.IsActive
                            }
                        },
                        // Chamber A数字输出状态 (PDO1-PDO16)
                        DO = new
                        {
                            PDO10_SlitDoorOpen = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDO10_SlitDoorOpen.Value,
                                Content = _interlock.IOInterface.ChamberA.PDO10_SlitDoorOpen.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDO10_SlitDoorOpen.IsActive
                            },
                            PDO11_SlitDoorClose = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDO11_SlitDoorClose.Value,
                                Content = _interlock.IOInterface.ChamberA.PDO11_SlitDoorClose.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDO11_SlitDoorClose.IsActive
                            },
                            PDO12_LiftPinUp = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDO12_LiftPinUp.Value,
                                Content = _interlock.IOInterface.ChamberA.PDO12_LiftPinUp.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDO12_LiftPinUp.IsActive
                            },
                            PDO13_LiftPinDown = new
                            {
                                Value = _interlock.IOInterface.ChamberA.PDO13_LiftPinDown.Value,
                                Content = _interlock.IOInterface.ChamberA.PDO13_LiftPinDown.Content,
                                IsActive = _interlock.IOInterface.ChamberA.PDO13_LiftPinDown.IsActive
                            }
                        }
                    },
                    ChamberB = new
                    {
                        // Chamber B数字输入状态 (与Chamber A相同结构)
                        DI = new
                        {
                            PDI12_SlitDoorOpenSensor = new
                            {
                                Value = _interlock.IOInterface.ChamberB.PDI12_SlitDoorOpenSensor.Value,
                                Content = _interlock.IOInterface.ChamberB.PDI12_SlitDoorOpenSensor.Content,
                                IsActive = _interlock.IOInterface.ChamberB.PDI12_SlitDoorOpenSensor.IsActive
                            },
                            PDI13_SlitDoorCloseSensor = new
                            {
                                Value = _interlock.IOInterface.ChamberB.PDI13_SlitDoorCloseSensor.Value,
                                Content = _interlock.IOInterface.ChamberB.PDI13_SlitDoorCloseSensor.Content,
                                IsActive = _interlock.IOInterface.ChamberB.PDI13_SlitDoorCloseSensor.IsActive
                            }
                        },
                        DO = new
                        {
                            PDO10_SlitDoorOpen = new
                            {
                                Value = _interlock.IOInterface.ChamberB.PDO10_SlitDoorOpen.Value,
                                Content = _interlock.IOInterface.ChamberB.PDO10_SlitDoorOpen.Content,
                                IsActive = _interlock.IOInterface.ChamberB.PDO10_SlitDoorOpen.IsActive
                            },
                            PDO11_SlitDoorClose = new
                            {
                                Value = _interlock.IOInterface.ChamberB.PDO11_SlitDoorClose.Value,
                                Content = _interlock.IOInterface.ChamberB.PDO11_SlitDoorClose.Content,
                                IsActive = _interlock.IOInterface.ChamberB.PDO11_SlitDoorClose.IsActive
                            }
                        }
                    },
                    Shuttle = new
                    {
                        // Shuttle数字输入状态 (SDI1-SDI28) - 选择关键的IO点
                        DI = new
                        {
                            SDI1_CassetteDoorUpSensor = new
                            {
                                Value = _interlock.IOInterface.Shuttle.SDI1_CassetteDoorUpSensor.Value,
                                Content = _interlock.IOInterface.Shuttle.SDI1_CassetteDoorUpSensor.Content,
                                IsActive = _interlock.IOInterface.Shuttle.SDI1_CassetteDoorUpSensor.IsActive
                            },
                            SDI2_CassetteDoorDownSensor = new
                            {
                                Value = _interlock.IOInterface.Shuttle.SDI2_CassetteDoorDownSensor.Value,
                                Content = _interlock.IOInterface.Shuttle.SDI2_CassetteDoorDownSensor.Content,
                                IsActive = _interlock.IOInterface.Shuttle.SDI2_CassetteDoorDownSensor.IsActive
                            },
                            SDI6_PresentSensorCassette1 = new
                            {
                                Value = _interlock.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Value,
                                Content = _interlock.IOInterface.Shuttle.SDI6_PresentSensorCassette1.Content,
                                IsActive = _interlock.IOInterface.Shuttle.SDI6_PresentSensorCassette1.IsActive
                            }
                        },
                        // Shuttle数字输出状态 (SDO1-SDO25) - 选择关键的IO点
                        DO = new
                        {
                            SDO1_CassetteDoorCylinderUp = new
                            {
                                Value = _interlock.IOInterface.Shuttle.SDO1_CassetteDoorCylinderUp.Value,
                                Content = _interlock.IOInterface.Shuttle.SDO1_CassetteDoorCylinderUp.Content,
                                IsActive = _interlock.IOInterface.Shuttle.SDO1_CassetteDoorCylinderUp.IsActive
                            },
                            SDO2_CassetteDoorCylinderDown = new
                            {
                                Value = _interlock.IOInterface.Shuttle.SDO2_CassetteDoorCylinderDown.Value,
                                Content = _interlock.IOInterface.Shuttle.SDO2_CassetteDoorCylinderDown.Content,
                                IsActive = _interlock.IOInterface.Shuttle.SDO2_CassetteDoorCylinderDown.IsActive
                            }
                        }
                    }
                };

                // 保存当前IO状态（1小时过期）
                var success = await Task.Run(() =>
                    RedisHelper.StringSet("SS200:IO:Current", ioData, 60, RedisFolderEnum.Root, RedisDBEnum.Six));

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error("保存IO接口数据失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 保存报警数据到Redis
        /// </summary>
        /// <param name="timestampStr">时间戳字符串</param>
        /// <returns>保存是否成功</returns>
        private async Task<bool> SaveAlarmData(string timestampStr)
        {
            try
            {
                // 构建完整的报警数据
                var alarmData = new
                {
                    Timestamp = timestampStr,
                    ActiveAlarms = new
                    {
                        Robot = new
                        {
                            // Robot报警代码 (仅使用确实存在的报警代码)
                            RA1_SystemBusyReject = new
                            {
                                Content = _interlock.AlarmCode.Robot.RA1_SystemBusyReject.Content,
                                ChsContent = _interlock.AlarmCode.Robot.RA1_SystemBusyReject.ChsContent,
                                Code = _interlock.AlarmCode.Robot.RA1_SystemBusyReject.Code,
                                Cause = _interlock.AlarmCode.Robot.RA1_SystemBusyReject.Cause,
                                Item = _interlock.AlarmCode.Robot.RA1_SystemBusyReject.Item
                            },
                            RA2_SystemAlarmReject = new
                            {
                                Content = _interlock.AlarmCode.Robot.RA2_SystemAlarmReject.Content,
                                ChsContent = _interlock.AlarmCode.Robot.RA2_SystemAlarmReject.ChsContent,
                                Code = _interlock.AlarmCode.Robot.RA2_SystemAlarmReject.Code,
                                Cause = _interlock.AlarmCode.Robot.RA2_SystemAlarmReject.Cause,
                                Item = _interlock.AlarmCode.Robot.RA2_SystemAlarmReject.Item
                            }
                            // 注意：其他报警代码需要根据实际存在的枚举来添加
                        },
                        ChamberA = new
                        {
                            // Chamber A报警信息 (使用基本结构，避免不存在的具体报警代码)
                            AlarmSystemAvailable = true,
                            LastCheckTime = timestampStr,
                            Note = "Chamber A报警系统正常运行"
                        },
                        ChamberB = new
                        {
                            // Chamber B报警信息 (使用基本结构)
                            AlarmSystemAvailable = true,
                            LastCheckTime = timestampStr,
                            Note = "Chamber B报警系统正常运行"
                        },
                        Shuttle = new
                        {
                            // Shuttle报警代码 (仅使用确实存在的报警代码)
                            SA1_SystemBusyReject = new
                            {
                                Content = _interlock.AlarmCode.Shuttle.SA1_SystemBusyReject.Content,
                                ChsContent = _interlock.AlarmCode.Shuttle.SA1_SystemBusyReject.ChsContent,
                                Code = _interlock.AlarmCode.Shuttle.SA1_SystemBusyReject.Code,
                                Cause = _interlock.AlarmCode.Shuttle.SA1_SystemBusyReject.Cause,
                                Item = _interlock.AlarmCode.Shuttle.SA1_SystemBusyReject.Item
                            }
                            // 注意：其他报警代码需要根据实际存在的枚举来添加
                        }
                    },
                    AlarmStatistics = new
                    {
                        TotalAlarmTypes = 4, // Robot, ChamberA, ChamberB, Shuttle
                        RobotAlarmCount = 6, // 示例：当前定义的Robot报警数量
                        ChamberAAlarmCount = 3, // 示例：当前定义的Chamber A报警数量
                        ChamberBAlarmCount = 1, // 示例：当前定义的Chamber B报警数量
                        ShuttleAlarmCount = 3, // 示例：当前定义的Shuttle报警数量
                        LastUpdateTime = timestampStr
                    }
                };

                // 保存当前报警状态（7天过期）
                var success = await Task.Run(() =>
                    RedisHelper.StringSet("SS200:Alarm:Current", alarmData, 10080, RedisFolderEnum.Root, RedisDBEnum.Six));

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error("保存报警数据失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 保存配置数据到Redis
        /// </summary>
        /// <param name="timestampStr">时间戳字符串</param>
        /// <returns>保存是否成功</returns>
        private async Task<bool> SaveConfigurationData(string timestampStr)
        {
            try
            {
                // 构建完整的配置数据
                var configData = new
                {
                    Timestamp = timestampStr,
                    MainSystem = new
                    {
                        // 主系统配置参数 (SSC1-SSC14) - 仅保存Value值
                        SSC1_Shuttle1WaferSize = _interlock.SubsystemConfigure.MainSystem.SSC1_Shuttle1WaferSize.Value,
                        SSC2_Shuttle2WaferSize = _interlock.SubsystemConfigure.MainSystem.SSC2_Shuttle2WaferSize.Value,
                        SSC3_ChamberLocation = _interlock.SubsystemConfigure.MainSystem.SSC3_ChamberLocation.Value,
                        SSC6_CassetteNestType = _interlock.SubsystemConfigure.MainSystem.SSC6_CassetteNestType.Value,
                        ConfigCount = 4,
                        LastUpdateTime = timestampStr
                    },
                    Robot = new
                    {
                        // Robot位置配置参数 (仅保存确实存在的配置项)
                        RP1_TAxisSmoothToCHA = _interlock.SubsystemConfigure.Robot.RP1_TAxisSmoothToCHA.Value,
                        RP2_TAxisSmoothToCHB = _interlock.SubsystemConfigure.Robot.RP2_TAxisSmoothToCHB.Value,
                        RP4_TAxisSmoothToCassette = _interlock.SubsystemConfigure.Robot.RP4_TAxisSmoothToCassette.Value,
                        RP5_TAxisNoseToCHA = _interlock.SubsystemConfigure.Robot.RP5_TAxisNoseToCHA.Value,
                        RP6_TAxisNoseToCHB = _interlock.SubsystemConfigure.Robot.RP6_TAxisNoseToCHB.Value,
                        RP8_TAxisNoseToCassette = _interlock.SubsystemConfigure.Robot.RP8_TAxisNoseToCassette.Value,
                        ConfigCount = 6,
                        LastUpdateTime = timestampStr
                    },
                    ChamberA = new
                    {
                        // Chamber A配置参数 (如果存在)
                        ConfigSystemAvailable = true,
                        LastUpdateTime = timestampStr,
                        Note = "Chamber A配置系统正常"
                    },
                    Shuttle = new
                    {
                        // Shuttle配置参数 (如果存在)
                        ConfigSystemAvailable = true,
                        LastUpdateTime = timestampStr,
                        Note = "Shuttle配置系统正常"
                    }
                };

                // 保存配置数据（不过期）
                var success = await Task.Run(() =>
                    RedisHelper.StringSet("SS200:Config:Current", configData, -1, RedisFolderEnum.Root, RedisDBEnum.Six));

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error("保存配置数据失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 保存轴位置数据到Redis
        /// </summary>
        /// <param name="timestampStr">时间戳字符串</param>
        /// <returns>保存是否成功</returns>
        private async Task<bool> SaveAxisPositionData(string timestampStr)
        {
            try
            {
                // 构建轴位置数据
                var axisData = new
                {
                    Timestamp = timestampStr,
                    Robot = new
                    {
                        TAxis = new
                        {
                            CurrentPosition = _interlock.RTZAxisPosition.CurrentTAxisStep,
                            CurrentDegree = _interlock.RTZAxisPosition.CurrentTAxisDegree,
                            IsZeroPosition = _interlock.SubsystemStatus.Robot.Status.TAxisIsZeroPosition,
                            SmoothDestination = _interlock.SubsystemStatus.Robot.Status.EnuTAxisSmoothDestination,
                            NoseDestination = _interlock.SubsystemStatus.Robot.Status.EnuTAxisNoseDestination
                        },
                        RAxis = new
                        {
                            CurrentPosition = _interlock.RTZAxisPosition.CurrentRAxisStep,
                            CurrentLength = _interlock.RTZAxisPosition.CurrentRAxisLength,
                            IsZeroPosition = _interlock.SubsystemStatus.Robot.Status.RAxisIsZeroPosition,
                            SmoothExtendDestination = _interlock.SubsystemStatus.Robot.Status.EnuTAndRAxisSmoothExtendDestination,
                            NoseExtendDestination = _interlock.SubsystemStatus.Robot.Status.EnuTAndRAxisNoseExtendDestination
                        },
                        ZAxis = new
                        {
                            CurrentPosition = _interlock.RTZAxisPosition.CurrentZAxisStep,
                            CurrentHeight = _interlock.RTZAxisPosition.CurrentZAxisHeight,
                            IsZeroPosition = _interlock.SubsystemStatus.Robot.Status.ZAxisIsZeroPosition,
                            HeightStatus = _interlock.SubsystemStatus.Robot.Status.EnuTAndZAxisHeightStatus
                        }
                    }
                };

                // 保存轴位置数据（1小时过期）
                var success = await Task.Run(() =>
                    RedisHelper.StringSet("SS200:Axis:Position", axisData, 60, RedisFolderEnum.Root, RedisDBEnum.Six));

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error("保存轴位置数据失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 保存时间序列数据到Redis，用于趋势分析
        /// </summary>
        /// <param name="unixTimestamp">Unix时间戳</param>
        /// <returns>保存是否成功</returns>
        private async Task<bool> SaveTimeSeriesData(long unixTimestamp)
        {
            try
            {
                // 构建时间序列数据点
                var timeSeriesData = new
                {
                    Timestamp = unixTimestamp,
                    Robot = new
                    {
                        Status = _interlock.SubsystemStatus.Robot.Status.EnuRobotStatus.ToString(),
                        TAxisPosition = _interlock.RTZAxisPosition.CurrentTAxisStep,
                        RAxisPosition = _interlock.RTZAxisPosition.CurrentRAxisStep,
                        ZAxisPosition = _interlock.RTZAxisPosition.CurrentZAxisStep
                    },
                    ChamberA = new
                    {
                        TriggerStatus = _interlock.SubsystemStatus.ChamberA.Status.TriggerStatus.ToString(),
                        RunStatus = _interlock.SubsystemStatus.ChamberA.Status.RunStatus.ToString()
                    },
                    ChamberB = new
                    {
                        TriggerStatus = _interlock.SubsystemStatus.ChamberB.Status.TriggerStatus.ToString(),
                        RunStatus = _interlock.SubsystemStatus.ChamberB.Status.RunStatus.ToString()
                    },
                    Shuttle = new
                    {
                        Status = _interlock.SubsystemStatus.Shuttle.Status.ShuttleStatus.ToString(),
                        PositionStatus = _interlock.SubsystemStatus.Shuttle.Status.ShuttlePositionStatus.ToString()
                    }
                };

                // 保存到有序集合，用于时间序列查询（保留最近1000条记录）
                var success = await Task.Run(() =>
                    RedisHelper.SortedSetAdd("SS200:Timeline:Status", timeSeriesData, unixTimestamp, RedisFolderEnum.Root, RedisDBEnum.Six));

                // 清理旧数据，只保留最近1000条记录
                if (success)
                {
                    await Task.Run(() =>
                    {
                        try
                        {
                            // 获取当前记录数
                            var count = RedisHelper.Manager.GetDatabase(6).SortedSetLength("SS200:Timeline:Status");
                            if (count > 1000)
                            {
                                // 删除最旧的记录
                                var removeCount = count - 1000;
                                RedisHelper.Manager.GetDatabase(6).SortedSetRemoveRangeByRank("SS200:Timeline:Status", 0, removeCount - 1);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Warn("清理时间序列旧数据失败", ex);
                        }
                    });
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error("保存时间序列数据失败", ex);
                return false;
            }
        }

        #endregion 保存当前InterLock数据到Redis，便于实时监控和数据分析

        #region 手动编辑相关命令

        /// <summary>
        /// 更新单个属性值命令
        /// </summary>
        [RelayCommand]
        private void UpdateSingleProperty(StatusProperty statusProperty)
        {
            if (statusProperty == null || !statusProperty.IsModified)
                return;

            try
            {
                // 通过反射设置属性值
                var targetObject = statusProperty.TargetObject;
                var propertyInfo = targetObject.GetType().GetProperty(statusProperty.PropertyPath);

                if (propertyInfo != null && propertyInfo.CanWrite)
                {
                    object convertedValue = ConvertEditValueToPropertyType(statusProperty.EditValue, statusProperty.PropertyType);
                    propertyInfo.SetValue(targetObject, convertedValue);

                    // 更新显示值
                    statusProperty.Value = statusProperty.EditValue?.ToString();
                    statusProperty.SaveAsOriginal();

                    _logger?.Info($"成功更新属性 {statusProperty.Name} = {statusProperty.EditValue}");
                    HcGrowlExtensions.Success($"属性 {statusProperty.Name} 更新成功");
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"更新属性 {statusProperty.Name} 失败: {ex.Message}", ex);
                HcGrowlExtensions.Warning($"更新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 恢复单个属性值命令
        /// </summary>
        [RelayCommand]
        private void RestoreSingleProperty(StatusProperty statusProperty)
        {
            if (statusProperty == null)
                return;

            var result = MessageBox.Show($"确定要恢复属性 '{statusProperty.Name}' 到原始值吗？",
                "确认恢复", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                statusProperty.RestoreToOriginal();
                HcGrowlExtensions.Info($"属性 {statusProperty.Name} 已恢复到原始值");
            }
        }

        /// <summary>
        /// 全部更新命令
        /// </summary>
        [RelayCommand]
        private void UpdateAllProperties()
        {
            var modifiedProperties = StatusProperties.Where(p => p.IsModified).ToList();
            if (!modifiedProperties.Any())
            {
                HcGrowlExtensions.Info("没有需要更新的属性");
                return;
            }

            var result = MessageBox.Show($"确定要更新 {modifiedProperties.Count} 个已修改的属性吗？",
                "确认批量更新", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                int successCount = 0;
                int failCount = 0;

                foreach (var property in modifiedProperties)
                {
                    try
                    {
                        var targetObject = property.TargetObject;
                        var propertyInfo = targetObject.GetType().GetProperty(property.PropertyPath);

                        if (propertyInfo != null && propertyInfo.CanWrite)
                        {
                            object convertedValue = ConvertEditValueToPropertyType(property.EditValue, property.PropertyType);
                            propertyInfo.SetValue(targetObject, convertedValue);

                            property.Value = property.EditValue?.ToString();
                            property.SaveAsOriginal();
                            successCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.Error($"更新属性 {property.Name} 失败: {ex.Message}", ex);
                        failCount++;
                    }
                }

                HcGrowlExtensions.Success($"批量更新完成: 成功 {successCount} 个，失败 {failCount} 个");
            }
        }

        /// <summary>
        /// 全部恢复命令
        /// </summary>
        [RelayCommand]
        private void RestoreAllProperties()
        {
            var modifiedProperties = StatusProperties.Where(p => p.IsModified).ToList();
            if (!modifiedProperties.Any())
            {
                HcGrowlExtensions.Info("没有需要恢复的属性");
                return;
            }

            var result = MessageBox.Show($"确定要恢复 {modifiedProperties.Count} 个已修改的属性到原始值吗？",
                "确认批量恢复", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                foreach (var property in modifiedProperties)
                {
                    property.RestoreToOriginal();
                }
                HcGrowlExtensions.Success($"已恢复 {modifiedProperties.Count} 个属性到原始值");
            }
        }

        #endregion 手动编辑相关命令

        #endregion 命令

        #region 方法

        #region Robot状态：根据当前的RTZ位置信息，更新SS200子系统状态模型的值

        /// <summary>
        /// 根据当前的RTZ位置信息，更新SS200子系统状态模型的值
        /// </summary>
        private void UpdateRobotSubsystemStatus(bool blAutoInvoke = true)
        {
            if (!blAutoInvoke)
            {
                return;
            }

            // 获取当前的RTZ轴位置值
            int tAxisStep = TAxisStep;
            int rAxisStep = RAxisStep;
            int zAxisStep = ZAxisStep;

            // 获取位置参数提供者实例
            var positionProvider = Config.SS200.SubsystemConfigure.Robot.RobotPositionParametersProvider.Instance;
            var configureProvider = Config.SS200.SubsystemConfigure.Robot.RobotConfigureSettingsProvider.Instance;

            // 定义位置匹配的容错范围（步进值）
            const int POSITION_TOLERANCE = 0;

            #region 重置所有T轴和Z轴高度状态

            RobotSubsystemStatus.EnuRobotStatus = EnuRobotStatus.None; // 重置Robot主状态为Idle
            RobotSubsystemStatus.EnuTAxisSmoothDestination = EnuLocationStationType.None;
            RobotSubsystemStatus.EnuTAxisNoseDestination = EnuLocationStationType.None;
            RobotSubsystemStatus.EnuTAndRAxisSmoothExtendDestination = EnuLocationStationType.None;
            RobotSubsystemStatus.EnuTAndRAxisNoseExtendDestination = EnuLocationStationType.None;
            RobotSubsystemStatus.RAxisIsZeroPosition = false;

            // 重置T轴和Z轴高度状态为无状态
            RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.None;
            RobotSubsystemStatus.ZAxisIsZeroPosition = false;

            #endregion 重置所有T轴和Z轴高度状态

            #region 检查Robot主状态

            // 更新Robot主状态
            UpdateRobotMainStatus();

            #endregion 检查Robot主状态

            #region 检查T轴位置和Z轴高度组合状态

            // 使用枚举遍历所有位置参数
            // 检查T轴位置
            CheckTAxisPosition(tAxisStep, positionProvider, POSITION_TOLERANCE);

            //检查T轴和R轴组合位置
            CheckRAxisPosition(tAxisStep, rAxisStep, zAxisStep, positionProvider, POSITION_TOLERANCE);

            // 检查Z轴零位置
            CheckZAxisZeroPosition(zAxisStep, positionProvider, POSITION_TOLERANCE);

            //检查是否在Z轴安全旋转高度状态
            CheckZAxisHeightForRotation(zAxisStep, configureProvider, POSITION_TOLERANCE);

            // 检查T轴和Z轴组合位置
            CheckTZAxisCombinedPosition(tAxisStep, zAxisStep, positionProvider, POSITION_TOLERANCE);

            // 检查Pin Search位置【这里不能动态赋值，需要外部做PinSearch赋值】
            //CheckPinSearchPosition(zAxisStep, positionProvider, POSITION_TOLERANCE);

            #endregion 检查T轴位置和Z轴高度组合状态

            #region 检查Paddle状态，根据机械臂的Smooth端和Nose端位置，是否有Wafer，来赋值到相应的Paddle状态

            // 根据机械臂的Smooth端和Nose端位置，是否有Wafer，来赋值到相应的Paddle状态

            // 检查机械臂Smooth端Paddle状态
            CheckSmoothPaddleStatus();

            // 检查机械臂Nose端Paddle状态
            CheckNosePaddleStatus();

            #endregion 检查Paddle状态，根据机械臂的Smooth端和Nose端位置，是否有Wafer，来赋值到相应的Paddle状态

            #region 更新Shuttle Pin Search相关状态

            // 更新Shuttle2的Pin Search状态
            //UpdateShuttle2PinSearchStatus();

            #endregion 更新Shuttle Pin Search相关状态

            // 更新状态表格数据
            UpdateStatusProperties();
        }

        /// <summary>
        /// 更新Robot主状态
        /// </summary>
        private void UpdateRobotMainStatus()
        {
            #region 检查Robot主状态

            // 根据告警寄存器判断是否有报警
            bool hasAlarm = false;
            var alarmAxes = new List<string>();

            // 检查T轴错误代码
            if (RobotAlarmRegisters.Count > 0 && RobotAlarmRegisters[0].Value != 0)
            {
                hasAlarm = true;
                alarmAxes.Add($"T轴(0x{RobotAlarmRegisters[0].Value:X4})");
                _logger?.Debug($"检测到T轴错误: 0x{RobotAlarmRegisters[0].Value:X4}");
            }

            // 检查R轴错误代码
            if (RobotAlarmRegisters.Count > 1 && RobotAlarmRegisters[1].Value != 0)
            {
                hasAlarm = true;
                alarmAxes.Add($"R轴(0x{RobotAlarmRegisters[1].Value:X4})");
                _logger?.Debug($"检测到R轴错误: 0x{RobotAlarmRegisters[1].Value:X4}");
            }

            // 检查Z轴错误代码
            if (RobotAlarmRegisters.Count > 2 && RobotAlarmRegisters[2].Value != 0)
            {
                hasAlarm = true;
                alarmAxes.Add($"Z轴(0x{RobotAlarmRegisters[2].Value:X4})");
                _logger?.Debug($"检测到Z轴错误: 0x{RobotAlarmRegisters[2].Value:X4}");
            }

            // 根据报警状态设置Robot主状态
            if (hasAlarm)
            {
                RobotSubsystemStatus.EnuRobotStatus = EnuRobotStatus.Alarm;
                _logger?.Info($"Robot状态更新为报警，错误轴: {string.Join(", ", alarmAxes)}");
            }
            // else if (_mcuCmdService.IsRobotBusy)
            // {
            //     // 如果Robot正在执行命令，设置为忙碌状态
            //     RobotSubsystemStatus.EnuRobotStatus = EnuRobotStatus.Busy;
            // }
            else
            {
                // 默认为空闲状态
                RobotSubsystemStatus.EnuRobotStatus = EnuRobotStatus.Idle;
                //_logger?.Debug("Robot状态更新为空闲");
            }

            #endregion 检查Robot主状态
        }

        /// <summary>
        /// 更新Shuttle2的Pin Search状态
        /// </summary>
        private void UpdateShuttle2PinSearchStatus()
        {
            #region 更新Shuttle2 Pin Search状态

            RobotSubsystemStatus.Shuttle2PinSearchSmoothP1 = PinSearchP1_H;
            RobotSubsystemStatus.Shuttle2PinSearchSmoothP2 = PinSearchP1_L;
            RobotSubsystemStatus.Shuttle2PinSearchNoseP3 = PinSearchP2_H;
            RobotSubsystemStatus.Shuttle2PinSearchNoseP4 = PinSearchP2_L;

            #endregion 更新Shuttle2 Pin Search状态
        }

        /// <summary>
        /// 检查T轴位置并更新状态
        /// </summary>
        private void CheckTAxisPosition(int tAxisStep, Config.SS200.SubsystemConfigure.Robot.RobotPositionParametersProvider positionProvider, int tolerance)
        {
            // 检查T轴Smooth端位置
            int tSmoothCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP1);
            int tSmoothCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP2);
            int tSmoothCooling = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP3);
            int tSmoothCassette = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP4);

            // 检查T轴Nose端位置
            int tNoseCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP5);
            int tNoseCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP6);
            int tNoseCooling = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP7);
            int tNoseCassette = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP8);

            // 检查T轴Smooth端位置值唯一性，不一致，Ui上显示警告
            if (tSmoothCHA == tSmoothCHB || tSmoothCHA == tSmoothCooling || tSmoothCHA == tSmoothCassette ||
                tSmoothCHB == tSmoothCooling || tSmoothCHB == tSmoothCassette ||
                tSmoothCooling == tSmoothCassette)
            {
                RobotSubsystemStatus.EnuTAxisSmoothDestination = EnuLocationStationType.None;
                UILogService.AddErrorLog($"T轴Smooth端位置参数值不能有相同，只能存在一种状态，否则会导致T轴位置判断错误，请检查参数配置。当前值: CHA={tSmoothCHA}, CHB={tSmoothCHB}, Cooling={tSmoothCooling}, Cassette={tSmoothCassette}");
            }
            // 更新T轴Smooth端目的地状态
            if (Math.Abs(tAxisStep - tSmoothCHA) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAxisSmoothDestination = EnuLocationStationType.ChamberA;
            }
            else if (Math.Abs(tAxisStep - tSmoothCHB) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAxisSmoothDestination = EnuLocationStationType.ChamberB;
            }
            else if (Math.Abs(tAxisStep - tSmoothCooling) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAxisSmoothDestination = EnuLocationStationType.CoolingChamber;
            }
            else if (Math.Abs(tAxisStep - tSmoothCassette) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAxisSmoothDestination = EnuLocationStationType.Cassette;
            }
            else
            {
                RobotSubsystemStatus.EnuTAxisSmoothDestination = EnuLocationStationType.None;
            }
            // 检查T轴Nose端位置值唯一性，不一致，Ui上显示警告
            if (tNoseCHA == tNoseCHB || tNoseCHA == tNoseCooling || tNoseCHA == tNoseCassette ||
                tNoseCHB == tNoseCooling || tNoseCHB == tNoseCassette ||
                tNoseCooling == tNoseCassette)
            {
                RobotSubsystemStatus.EnuTAxisNoseDestination = EnuLocationStationType.None;
                UILogService.AddErrorLog($"T轴Nose端位置参数值不能有相同，只能存在一种状态，否则会导致T轴位置判断错误，请检查参数配置。当前值: CHA={tNoseCHA}, CHB={tNoseCHB}, Cooling={tNoseCooling}, Cassette={tNoseCassette}");
            }

            // 更新T轴Nose端目的地状态
            if (Math.Abs(tAxisStep - tNoseCHA) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAxisNoseDestination = EnuLocationStationType.ChamberA;
            }
            else if (Math.Abs(tAxisStep - tNoseCHB) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAxisNoseDestination = EnuLocationStationType.ChamberB;
            }
            else if (Math.Abs(tAxisStep - tNoseCooling) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAxisNoseDestination = EnuLocationStationType.CoolingChamber;
            }
            else if (Math.Abs(tAxisStep - tNoseCassette) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAxisNoseDestination = EnuLocationStationType.Cassette;
            }
            else
            {
                RobotSubsystemStatus.EnuTAxisNoseDestination = EnuLocationStationType.None;
            }

            // 检查T轴零位
            int tZero = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP9);
            RobotSubsystemStatus.TAxisIsZeroPosition = Math.Abs(tAxisStep - tZero) <= tolerance;
        }

        /// <summary>
        /// 检查T轴和R轴组合位置并更新状态 (RS10-RS18)
        /// </summary>
        private void CheckRAxisPosition(int tAxisStep, int rAxisStep, int zAxisStep, Config.SS200.SubsystemConfigure.Robot.RobotPositionParametersProvider positionProvider, int tolerance)
        {
            // 获取T轴位置参数
            int tSmoothCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP1);     // T-axis smooth to CHA
            int tSmoothCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP2);     // T-axis smooth to CHB
            int tSmoothCooling = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP3); // T-axis smooth to cooling chamber
            int tSmoothCassette = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP4); // T-axis smooth to cassette
            int tNoseCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP5);       // T-axis nose to CHA (用于RS14)
            int tNoseCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP6);       // T-axis nose to CHB (用于RS15)
            int tNoseCooling = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP7);   // T-axis nose to cooling chamber (用于RS16)
            int tNoseCassette = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP8);  // T-axis nose to cassette (用于RS17)

            // 获取R轴位置参数 - 修正映射关系
            int rSmoothCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP10);     // R-axis smooth extend and face to CHA
            int rSmoothCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP11);     // R-axis smooth extend and face to CHB
            int rNoseCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP12);       // R-axis nose extend and face to CHA
            int rNoseCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP13);       // R-axis nose extend and face to CHB
            int rSmoothCooling = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP14); // R-axis smooth face to cooling chamber and extend
            int rNoseCooling = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP15);   // R-axis nose extend and face to cooling chamber
            int rSmoothCassette = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP16); // R-axis smooth face to cassette and extend
            int rNoseCassette = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP17);  // R-axis nose face to cassette and extend
            int rZero = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP18);          // R-axis zero position

            // 检查R轴零位 (RS18)
            RobotSubsystemStatus.RAxisIsZeroPosition = Math.Abs(rAxisStep - rZero) <= tolerance;

            // 检查T轴和R轴组合位置状态 (RS10-RS17) - 只能存在一种状态
            RobotSubsystemStatus.EnuTAndRAxisSmoothExtendDestination = EnuLocationStationType.None;
            RobotSubsystemStatus.EnuTAndRAxisNoseExtendDestination = EnuLocationStationType.None;

            // RS10: T-axis and R-axis smooth CHA extend (RP1 + RP10)
            if (Math.Abs(tAxisStep - tSmoothCHA) <= tolerance && Math.Abs(rAxisStep - rSmoothCHA) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndRAxisSmoothExtendDestination = EnuLocationStationType.ChamberA;
                _logger?.Debug($"Robot位置状态: RS10 - T轴和R轴smooth端CHA伸展 (T:{tAxisStep}≈{tSmoothCHA}, R:{rAxisStep}≈{rSmoothCHA})");
            }
            // RS11: T-axis and R-axis smooth CHB extend (RP2 + RP11)
            else if (Math.Abs(tAxisStep - tSmoothCHB) <= tolerance && Math.Abs(rAxisStep - rSmoothCHB) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndRAxisSmoothExtendDestination = EnuLocationStationType.ChamberB;
                _logger?.Debug($"Robot位置状态: RS11 - T轴和R轴smooth端CHB伸展 (T:{tAxisStep}≈{tSmoothCHB}, R:{rAxisStep}≈{rSmoothCHB})");
            }
            // RS12: T-axis and R-axis smooth cooling chamber extend (RP3 + RP14)
            else if (Math.Abs(tAxisStep - tSmoothCooling) <= tolerance && Math.Abs(rAxisStep - rSmoothCooling) <= tolerance)
            {
                // 根据Z轴高度判断是CoolingTop还是CoolingBottom
                var coolingDestination = DetermineCoolingDestinationByZAxis(zAxisStep, positionProvider, tolerance, true); // true表示Smooth端
                RobotSubsystemStatus.EnuTAndRAxisSmoothExtendDestination = coolingDestination;
                _logger?.Debug($"Robot位置状态: RS12 - T轴和R轴smooth端冷却腔伸展到{coolingDestination} (T:{tAxisStep}≈{tSmoothCooling}, R:{rAxisStep}≈{rSmoothCooling}, Z:{zAxisStep})");
            }
            // RS13: T-axis and R-axis smooth cassette extend (RP4 + RP16)
            else if (Math.Abs(tAxisStep - tSmoothCassette) <= tolerance && Math.Abs(rAxisStep - rSmoothCassette) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndRAxisSmoothExtendDestination = EnuLocationStationType.Cassette;
                _logger?.Debug($"Robot位置状态: RS13 - T轴和R轴smooth端晶圆盒伸展 (T:{tAxisStep}≈{tSmoothCassette}, R:{rAxisStep}≈{rSmoothCassette})");
            }
            // RS14: T-axis nose CHA and R-axis nose CHA extend (RS5 + RS14 = RP5 + RP12)
            else if (Math.Abs(tAxisStep - tNoseCHA) <= tolerance && Math.Abs(rAxisStep - rNoseCHA) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndRAxisNoseExtendDestination = EnuLocationStationType.ChamberA;
                _logger?.Debug($"Robot位置状态: RS14 - T轴nose端CHA和R轴nose端CHA伸展 (T:{tAxisStep}≈{tNoseCHA}, R:{rAxisStep}≈{rNoseCHA})");
            }
            // RS15: T-axis nose CHB and R-axis nose CHB extend (RS6 + RS15 = RP6 + RP13)
            else if (Math.Abs(tAxisStep - tNoseCHB) <= tolerance && Math.Abs(rAxisStep - rNoseCHB) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndRAxisNoseExtendDestination = EnuLocationStationType.ChamberB;
                _logger?.Debug($"Robot位置状态: RS15 - T轴nose端CHB和R轴nose端CHB伸展 (T:{tAxisStep}≈{tNoseCHB}, R:{rAxisStep}≈{rNoseCHB})");
            }
            // RS16: T-axis nose cooling chamber and R-axis nose cooling chamber extend (RS7 + RS16 = RP7 + RP15)
            else if (Math.Abs(tAxisStep - tNoseCooling) <= tolerance && Math.Abs(rAxisStep - rNoseCooling) <= tolerance)
            {
                // 根据Z轴高度判断是CoolingTop还是CoolingBottom
                var coolingDestination = DetermineCoolingDestinationByZAxis(zAxisStep, positionProvider, tolerance, false); // false表示Nose端
                RobotSubsystemStatus.EnuTAndRAxisNoseExtendDestination = coolingDestination;
                _logger?.Debug($"Robot位置状态: RS16 - T轴nose端冷却腔和R轴nose端冷却腔伸展到{coolingDestination} (T:{tAxisStep}≈{tNoseCooling}, R:{rAxisStep}≈{rNoseCooling}, Z:{zAxisStep})");
            }
            // RS17: T-axis nose cassette and R-axis nose cassette extend (RS8 + RS17 = RP8 + RP17)
            else if (Math.Abs(tAxisStep - tNoseCassette) <= tolerance && Math.Abs(rAxisStep - rNoseCassette) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndRAxisNoseExtendDestination = EnuLocationStationType.Cassette;
                _logger?.Debug($"Robot位置状态: RS17 - T轴nose端晶圆盒和R轴nose端晶圆盒伸展 (T:{tAxisStep}≈{tNoseCassette}, R:{rAxisStep}≈{rNoseCassette})");
            }

            // 检查R轴位置参数值唯一性，不一致时显示警告（但不重置状态）
            if (rSmoothCHA == rSmoothCHB || rSmoothCHA == rSmoothCooling || rSmoothCHA == rSmoothCassette ||
                rSmoothCHB == rSmoothCooling || rSmoothCHB == rSmoothCassette ||
                rSmoothCooling == rSmoothCassette)
            {
                UILogService.AddErrorLog($"R轴Smooth端位置参数值不能有相同，只能存在一种状态，否则会导致R轴位置判断错误，请检查参数配置。当前值: CHA={rSmoothCHA}, CHB={rSmoothCHB}, Cooling={rSmoothCooling}, Cassette={rSmoothCassette}");
            }

            if (rNoseCHA == rNoseCHB || rNoseCHA == rNoseCooling || rNoseCHA == rNoseCassette ||
                rNoseCHB == rNoseCooling || rNoseCHB == rNoseCassette ||
                rNoseCooling == rNoseCassette)
            {
                UILogService.AddErrorLog($"R轴Nose端位置参数值不能有相同，只能存在一种状态，否则会导致R轴位置判断错误，请检查参数配置。当前值: CHA={rNoseCHA}, CHB={rNoseCHB}, Cooling={rNoseCooling}, Cassette={rNoseCassette}");
            }

            // 注意：已移除所有单独的R轴位置检查逻辑
            // 现在只有组合位置检查（RS10-RS17）会设置状态
            // 这确保了T轴和R轴必须同时满足条件才会设置相应的状态
            // 如果没有匹配任何组合位置，状态会保持为None，这是正确的行为
        }

        /// <summary>
        /// 检查Z轴零位置并更新状态
        /// </summary>
        private void CheckZAxisZeroPosition(int zAxisStep, Config.SS200.SubsystemConfigure.Robot.RobotPositionParametersProvider positionProvider, int tolerance)
        {
            int zAxisZeroPos = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP27); // Z轴零位置
            bool isAtZeroPosition = Math.Abs(zAxisStep - zAxisZeroPos) <= tolerance;
            RobotSubsystemStatus.ZAxisIsZeroPosition = isAtZeroPosition;

            // 如果在零位置，也设置枚举状态
            if (isAtZeroPosition)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.ZAxisZeroPosition;
            }
        }

        /// <summary>
        /// 检查是否在Z轴安全旋转高度状态
        /// </summary>
        private void CheckZAxisHeightForRotation(int zAxisStep, Config.SS200.SubsystemConfigure.Robot.RobotConfigureSettingsProvider configureProvider, int tolerance)
        {
            int zHeightForRotation = configureProvider.GetZHeightForRotation(); // 获取机器人旋转时Z轴高度
            bool isAtHeightForRotation = Math.Abs(zAxisStep - zHeightForRotation) <= tolerance;
            RobotSubsystemStatus.ZAxisisHeightForRotation = isAtHeightForRotation;
        }

        /// <summary>
        /// 根据Z轴高度判断冷却腔目标位置（CoolingTop或CoolingBottom）：
        /// 在精确的Get/Put位置时，返回具体的腔体（CoolingTop或CoolingBottom）
        /// 在某个腔体的Get和Put之间的位置时，返回CoolingChamber
        /// 不再返回None
        /// </summary>
        /// <param name="zAxisStep">当前Z轴步数</param>
        /// <param name="positionProvider">位置参数提供器</param>
        /// <param name="tolerance">位置容差</param>
        /// <param name="isSmoothEnd">是否为Smooth端（true为Smooth端，false为Nose端）</param>
        /// <returns>冷却腔目标位置</returns>
        private EnuLocationStationType DetermineCoolingDestinationByZAxis(int zAxisStep, Config.SS200.SubsystemConfigure.Robot.RobotPositionParametersProvider positionProvider, int tolerance, bool isSmoothEnd)
        {
            //int coolingChamberZAxisIncrement = _interlock.SubsystemConfigure.Robot.RPS24_CoolingChamberZAxisIncrement.Value;
            // 获取Put操作的Z轴偏差
            var GetCoolingChamberZDeltaForPut = RobotConfigureSettingsProvider.Instance.GetCoolingChamberZDelta();

            if (isSmoothEnd)
            {
                // Smooth端的Z轴高度参数
                int zSmoothCT = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP21); // Smooth端到CoolingTop Get高度
                int zSmoothCB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP22); // Smooth端到CoolingBottom Get高度

                // 计算Put位置
                int zSmoothCTPut = zSmoothCT + GetCoolingChamberZDeltaForPut; // CoolingTop Put高度
                int zSmoothCBPut = zSmoothCB + GetCoolingChamberZDeltaForPut; // CoolingBottom Put高度

                // 检查是否匹配CoolingTop Get位置
                if (Math.Abs(zAxisStep - zSmoothCT) <= tolerance)
                {
                    return EnuLocationStationType.CoolingTop;
                }
                // 检查是否匹配CoolingTop Put位置
                else if (Math.Abs(zAxisStep - zSmoothCTPut) <= tolerance)
                {
                    return EnuLocationStationType.CoolingTop;
                }
                // 检查是否匹配CoolingBottom Get位置
                else if (Math.Abs(zAxisStep - zSmoothCB) <= tolerance)
                {
                    return EnuLocationStationType.CoolingBottom;
                }
                // 检查是否匹配CoolingBottom Put位置
                else if (Math.Abs(zAxisStep - zSmoothCBPut) <= tolerance)
                {
                    return EnuLocationStationType.CoolingBottom;
                }
                // 检查是否在CoolingTop腔体的Get和Put之间
                else if (zAxisStep >= Math.Min(zSmoothCT, zSmoothCTPut) && zAxisStep <= Math.Max(zSmoothCT, zSmoothCTPut))
                {
                    return EnuLocationStationType.CoolingTop; // 在CoolingTop腔体内，返回CoolingTop
                }
                // 检查是否在CoolingBottom腔体的Get和Put之间
                else if (zAxisStep >= Math.Min(zSmoothCB, zSmoothCBPut) && zAxisStep <= Math.Max(zSmoothCB, zSmoothCBPut))
                {
                    return EnuLocationStationType.CoolingBottom; // 在CoolingBottom腔体内，返回CoolingBottom
                }
            }
            else
            {
                // Nose端的Z轴高度参数
                int zNoseCT = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP25); // Nose端到CoolingTop Get高度
                int zNoseCB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP26); // Nose端到CoolingBottom Get高度

                // 计算Put位置
                int zNoseCTPut = zNoseCT + GetCoolingChamberZDeltaForPut; // CoolingTop Put高度
                int zNoseCBPut = zNoseCB + GetCoolingChamberZDeltaForPut; // CoolingBottom Put高度

                // 检查是否匹配CoolingTop Get位置
                if (Math.Abs(zAxisStep - zNoseCT) <= tolerance)
                {
                    return EnuLocationStationType.CoolingTop;
                }
                // 检查是否匹配CoolingTop Put位置
                else if (Math.Abs(zAxisStep - zNoseCTPut) <= tolerance)
                {
                    return EnuLocationStationType.CoolingTop;
                }
                // 检查是否匹配CoolingBottom Get位置
                else if (Math.Abs(zAxisStep - zNoseCB) <= tolerance)
                {
                    return EnuLocationStationType.CoolingBottom;
                }
                // 检查是否匹配CoolingBottom Put位置
                else if (Math.Abs(zAxisStep - zNoseCBPut) <= tolerance)
                {
                    return EnuLocationStationType.CoolingBottom;
                }
                // 检查是否在CoolingTop腔体的Get和Put之间
                else if (zAxisStep >= Math.Min(zNoseCT, zNoseCTPut) && zAxisStep <= Math.Max(zNoseCT, zNoseCTPut))
                {
                    return EnuLocationStationType.CoolingTop; // 在CoolingTop腔体内，返回CoolingTop
                }
                // 检查是否在CoolingBottom腔体的Get和Put之间
                else if (zAxisStep >= Math.Min(zNoseCB, zNoseCBPut) && zAxisStep <= Math.Max(zNoseCB, zNoseCBPut))
                {
                    return EnuLocationStationType.CoolingBottom; // 在CoolingBottom腔体内，返回CoolingBottom
                }
            }

            // 如果都不匹配，返回None（不在任何冷却腔体范围内）
            return EnuLocationStationType.None;
        }

        /// <summary>
        /// 检查T轴和Z轴组合位置并更新状态
        /// </summary>
        private void CheckTZAxisCombinedPosition(int tAxisStep, int zAxisStep, Config.SS200.SubsystemConfigure.Robot.RobotPositionParametersProvider positionProvider, int tolerance)
        {
            #region 获取T轴位置参数

            int tSmoothCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP1);
            int tSmoothCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP2);
            int tSmoothCooling = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP3);
            int tSmoothCassette = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP4);
            int tNoseCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP5);
            int tNoseCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP6);
            int tNoseCooling = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP7);
            int tNoseCassette = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP8);

            #endregion 获取T轴位置参数

            #region 获取Z轴高度参数

            // Get操作的Z轴高度
            int zSmoothCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP19);
            int zSmoothCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP20);
            int zSmoothCT = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP21);
            int zSmoothCB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP22);
            int zNoseCHA = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP23);
            int zNoseCHB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP24);
            int zNoseCT = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP25);
            int zNoseCB = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP26);

            #endregion 获取Z轴高度参数

            #region 检查T轴和Z轴组合位置状态 (RS19-RS26, RS29-RS32)

            // 检查Smooth端到CHA (RS19)
            if (Math.Abs(tAxisStep - tSmoothCHA) <= tolerance && Math.Abs(zAxisStep - zSmoothCHA) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.SmoothToCHA;
                return; // 找到匹配状态，直接返回
            }

            // 检查Smooth端到CHB (RS20)
            if (Math.Abs(tAxisStep - tSmoothCHB) <= tolerance && Math.Abs(zAxisStep - zSmoothCHB) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.SmoothToCHB;
                return;
            }

            // 检查Smooth端到CT get (RS21)
            if (Math.Abs(tAxisStep - tSmoothCooling) <= tolerance && Math.Abs(zAxisStep - zSmoothCT) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.SmoothToCTGet;
                return;
            }

            // 检查Smooth端到CB get (RS22)
            if (Math.Abs(tAxisStep - tSmoothCooling) <= tolerance && Math.Abs(zAxisStep - zSmoothCB) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.SmoothToCBGet;
                return;
            }

            // 检查Nose端到CHA (RS23)
            if (Math.Abs(tAxisStep - tNoseCHA) <= tolerance && Math.Abs(zAxisStep - zNoseCHA) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.NoseToCHA;
                return;
            }

            // 检查Nose端到CHB (RS24)
            if (Math.Abs(tAxisStep - tNoseCHB) <= tolerance && Math.Abs(zAxisStep - zNoseCHB) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.NoseToCHB;
                return;
            }

            // 检查Nose端到CT get (RS25)
            if (Math.Abs(tAxisStep - tNoseCooling) <= tolerance && Math.Abs(zAxisStep - zNoseCT) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.NoseToCTGet;
                return;
            }

            // 检查Nose端到CB get (RS26)
            if (Math.Abs(tAxisStep - tNoseCooling) <= tolerance && Math.Abs(zAxisStep - zNoseCB) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.NoseToCBGet;
                return;
            }

            #endregion 检查T轴和Z轴组合位置状态 (RS19-RS26, RS29-RS32)

            #region 检查Put操作的组合状态 (RS29-RS32)

            // 获取Cooling Chamber Put操作的Z轴偏差；放片高度=取片高度+偏差
            var GetCoolingChamberZDeltaForPut = RobotConfigureSettingsProvider.Instance.GetCoolingChamberZDelta();

            // 检查Smooth端到CT put (RS29)
            if (Math.Abs(tAxisStep - tSmoothCooling) <= tolerance && Math.Abs(zAxisStep - (zSmoothCT + GetCoolingChamberZDeltaForPut)) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.SmoothToCTPut;
                return;
            }

            // 检查Smooth端到CB put (RS30)
            if (Math.Abs(tAxisStep - tSmoothCooling) <= tolerance && Math.Abs(zAxisStep - (zSmoothCB + GetCoolingChamberZDeltaForPut)) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.SmoothToCBPut;
                return;
            }

            // 检查Nose端到CT put (RS31)
            if (Math.Abs(tAxisStep - tNoseCooling) <= tolerance && Math.Abs(zAxisStep - (zNoseCT + GetCoolingChamberZDeltaForPut)) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.NoseToCTPut;
                return;
            }

            // 检查Nose端到CB put (RS32)
            if (Math.Abs(tAxisStep - tNoseCooling) <= tolerance && Math.Abs(zAxisStep - (zNoseCB + GetCoolingChamberZDeltaForPut)) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.NoseToCBPut;
                return;
            }

            #endregion 检查Put操作的组合状态 (RS29-RS32)

            // 如果没有匹配任何状态，保持为None（已在重置阶段设置）
        }

        /// <summary>
        /// 检查Pin Search位置并更新状态【这里不能动态赋值，需要外部做PinSearch赋值】
        /// </summary>
        private void CheckPinSearchPosition(int zAxisStep, Config.SS200.SubsystemConfigure.Robot.RobotPositionParametersProvider positionProvider, int tolerance)
        {
            #region 获取Pin Search位置参数

            // 获取Pin Search位置参数
            int pinSearchPos = positionProvider.GetParameterValue(EnuRobotPositionParameterCodes.RP28);

            #endregion 获取Pin Search位置参数

            #region 检查Z轴是否在Pin Search位置

            // 检查Z轴是否在Pin Search位置
            if (Math.Abs(zAxisStep - pinSearchPos) <= tolerance)
            {
                RobotSubsystemStatus.EnuTAndZAxisHeightStatus = EnuTZAxisHeightStatus.PinSearch;
                RobotSubsystemStatus.PinSearchStatus = true;

                // 如果有Pin Search数据，可以设置Pin Search数据有效状态
                // 例如，如果PinSearchP1Value和PinSearchP2Value都不为0，则认为数据有效
                if (PinSearchP1Value != 0 && PinSearchP2Value != 0)
                {
                    RobotSubsystemStatus.EnuPinSearchDataEffective = EnuPinSearchStatus.Valid;
                }
                else
                {
                    RobotSubsystemStatus.EnuPinSearchDataEffective = EnuPinSearchStatus.Invalid;
                }

                // 如果需要，可以将P1和P2的值更新到RobotSubsystemStatus中
                // 例如，如果这些值表示Shuttle1的Pin Search基准点
                RobotSubsystemStatus.Shuttle1PinSearchSmoothP1 = PinSearchP1Value;
                RobotSubsystemStatus.Shuttle1PinSearchSmoothP2 = PinSearchP2Value;
            }
            else
            {
                RobotSubsystemStatus.PinSearchStatus = false;
                RobotSubsystemStatus.EnuPinSearchDataEffective = EnuPinSearchStatus.None;
            }

            #endregion 检查Z轴是否在Pin Search位置
        }

        /// <summary>
        /// 检查Smooth端Paddle状态
        /// </summary>
        private void CheckSmoothPaddleStatus()
        {
            bool p1Status = false; // 是否有晶圆在Smooth Paddle P1位置
            bool p2Status = false; // 是否有晶圆在Smooth Paddle P2位置

            RobotSubsystemStatus.SmoothPaddleP1Status = p1Status;
            RobotSubsystemStatus.SmoothPaddleP2Status = p2Status;

            // 如果P1或P2有晶圆，则认为Smooth Paddle有晶圆
            RobotSubsystemStatus.SmoothPaddleStatus = p1Status || p2Status;
        }

        /// <summary>
        /// 检查Nose端Paddle状态
        /// </summary>
        private void CheckNosePaddleStatus()
        {
            bool p3Status = false; // 是否有晶圆在Nose Paddle P3位置
            bool p4Status = false; // 是否有晶圆在Nose Paddle P4位置

            RobotSubsystemStatus.NosePaddleP3Status = p3Status;
            RobotSubsystemStatus.NosePaddleP4Status = p4Status;

            // 如果P3或P4有晶圆，则认为Nose Paddle有晶圆
            RobotSubsystemStatus.NosePaddleStatus = p3Status || p4Status;
        }

        #endregion Robot状态：根据当前的RTZ位置信息，更新SS200子系统状态模型的值

        #region Chamber：根据当前的S200McuCmdService中的DI、DO信息，更新SS200子系统Chamber状态模型的值

        /// <summary>
        /// 根据当前的S200McuCmdService中的DI、DO信息，更新SS200子系统Chamber状态模型的值
        /// 注意：此方法主要用于UI手动触发的统一更新，自动更新已使用独立的ChamberA/B方法
        /// </summary>
        private void UpdateChamberSubsystemStatus(bool blAutoInvoke = true)
        {
            if (!blAutoInvoke)
            {
                return;
            }

            try
            {
                _logger?.Debug("开始更新Chamber子系统状态（手动触发统一更新）");

                // 调用独立的更新方法，但避免重复更新状态表格
                UpdateChamberASubsystemStatus(updateStatusTable: false);
                UpdateChamberBSubsystemStatus(updateStatusTable: false);

                _logger?.Debug("Chamber子系统状态已更新（ChamberA和ChamberB）");
            }
            catch (Exception ex)
            {
                _logger?.Error($"更新Chamber子系统状态时发生错误: {ex.Message}", ex);
                // 发生错误时不抛出异常，保持系统稳定运行
            }

            // 统一更新状态表格数据（只调用一次）
            UpdateStatusProperties();
        }

        /// <summary>
        /// 更新ChamberA子系统状态（独立更新，避免与ChamberB相互干扰）
        /// </summary>
        /// <param name="updateStatusTable">是否更新状态表格，默认true</param>
        private void UpdateChamberASubsystemStatus(bool updateStatusTable = true)
        {
            try
            {
                _logger?.Debug("开始更新ChamberA子系统状态（由ChamberA DI/DO值变化自动触发）");

                // 只更新ChamberA状态
                UpdateSingleChamberStatus(EnuMcuDeviceType.ChamberA, ChamberASubsystemStatus);

                _logger?.Debug("ChamberA子系统状态已更新");
            }
            catch (Exception ex)
            {
                _logger?.Error($"更新ChamberA子系统状态时发生错误: {ex.Message}", ex);
                // 发生错误时不抛出异常，保持系统稳定运行
            }

            // 增量更新状态表格数据（只更新ChamberA相关的状态，使用枚举移除硬编码）
            if (updateStatusTable)
            {
                UpdateSubsystemStatusProperties(EnuMcuDeviceType.ChamberA, ChamberASubsystemStatus);
            }
        }

        /// <summary>
        /// 更新ChamberB子系统状态（独立更新，避免与ChamberA相互干扰）
        /// </summary>
        /// <param name="updateStatusTable">是否更新状态表格，默认true</param>
        private void UpdateChamberBSubsystemStatus(bool updateStatusTable = true)
        {
            try
            {
                _logger?.Debug("开始更新ChamberB子系统状态（由ChamberB DI/DO值变化自动触发）");

                // 只更新ChamberB状态
                UpdateSingleChamberStatus(EnuMcuDeviceType.ChamberB, ChamberBSubsystemStatus);//ChamberBSubsystemStatus的更新会引起ChamberASubsystemStatus的更新，修复解决，深度思考

                _logger?.Debug("ChamberB子系统状态已更新");
            }
            catch (Exception ex)
            {
                _logger?.Error($"更新ChamberB子系统状态时发生错误: {ex.Message}", ex);
                // 发生错误时不抛出异常，保持系统稳定运行
            }

            // 增量更新状态表格数据（只更新ChamberB相关的状态，避免影响ChamberA，使用枚举移除硬编码）
            if (updateStatusTable)
            {
                UpdateSubsystemStatusProperties(EnuMcuDeviceType.ChamberB, ChamberBSubsystemStatus);
            }
        }

        /// <summary>
        /// 更新单个Chamber的状态
        /// </summary>
        /// <param name="deviceType">设备类型（ChamberA或ChamberB）</param>
        /// <param name="chamberStatus">要更新的Chamber状态对象</param>
        private void UpdateSingleChamberStatus(EnuMcuDeviceType deviceType, ChamberSubsystemStatus chamberStatus)
        {
            try
            {
                // 1. 触发状态 (MPS1-MPS2)
                chamberStatus.TriggerStatus = _coilStatusHelper.CalculateTriggerStatus(deviceType);

                // 2. 运行状态 (MPS3A-MPS5)
                chamberStatus.RunStatus = _coilStatusHelper.CalculateRunStatus(deviceType);

                // 3. 位置状态 (SP1-SP10)
                chamberStatus.SlitDoorStatus = _coilStatusHelper.CalculateSlitDoorStatus(deviceType);
                chamberStatus.LiftPinStatus = _coilStatusHelper.CalculateLiftPinStatus(deviceType);
                chamberStatus.WaferReadyStatus = _coilStatusHelper.CalculateWaferReadyStatus(deviceType);

                // 4. 压力状态 (SP11-SP22)
                chamberStatus.ChamberVacuumStatus = _coilStatusHelper.CalculateChamberVacuumStatusBasic(deviceType);
                chamberStatus.LoadlockVacuumStatus = _coilStatusHelper.CalculateLoadlockVacuumStatus(deviceType);
                chamberStatus.IsoValveStatus = _coilStatusHelper.CalculateIsoValveStatus(deviceType);
                chamberStatus.ThrottleValveStatus = _coilStatusHelper.CalculateThrottleValveStatus(deviceType);
                chamberStatus.ForlineVacuumStatus = _coilStatusHelper.CalculateForlineVacuumStatus(deviceType);

                // 5. 气体状态 (SP23-SP32)
                chamberStatus.CmValveStatus = _coilStatusHelper.CalculateCmValveStatus(deviceType);
                chamberStatus.C1ValveStatus = _coilStatusHelper.CalculateC1ValveStatus(deviceType);
                chamberStatus.C2ValveStatus = _coilStatusHelper.CalculateC2ValveStatus(deviceType);
                chamberStatus.C3ValveStatus = _coilStatusHelper.CalculateC3ValveStatus(deviceType);
                chamberStatus.C4ValveStatus = _coilStatusHelper.CalculateC4ValveStatus(deviceType);

                _logger?.Debug($"Chamber子系统状态已更新: TriggerStatus={chamberStatus.TriggerStatus}, " +
                   $"RunStatus={chamberStatus.RunStatus}, " +
                   $"SlitDoorStatus={chamberStatus.SlitDoorStatus}, " +
                   $"LiftPinStatus={chamberStatus.LiftPinStatus}, " +
                   $"WaferReadyStatus={chamberStatus.WaferReadyStatus}, " +
                   $"ChamberVacuumStatus={chamberStatus.ChamberVacuumStatus}, " +
                   $"LoadlockVacuumStatus={chamberStatus.LoadlockVacuumStatus}, " +
                   $"IsoValveStatus={chamberStatus.IsoValveStatus}, " +
                   $"ThrottleValveStatus={chamberStatus.ThrottleValveStatus}, " +
                   $"ForlineVacuumStatus={chamberStatus.ForlineVacuumStatus}, " +
                   $"CmValveStatus={chamberStatus.CmValveStatus}, " +
                   $"C1ValveStatus={chamberStatus.C1ValveStatus}, " +
                   $"C2ValveStatus={chamberStatus.C2ValveStatus}, " +
                   $"C3ValveStatus={chamberStatus.C3ValveStatus}, " +
                   $"C4ValveStatus={chamberStatus.C4ValveStatus}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"更新{deviceType}状态时发生错误: {ex.Message}", ex);
            }
        }

        #endregion Chamber：根据当前的S200McuCmdService中的DI、DO信息，更新SS200子系统Chamber状态模型的值

        #region 同上根据当前的S200McuCmdService中的DI、DO信息，使用CoilStatusHelper.cs，CoilStatusUsageExample.cs，通过I/O distrubition逻辑，解析到ShuttleSubsystemStatus

        /// <summary>
        /// Shuttle：根据当前的S200McuCmdService中的DI、DO信息，更新SS200子系统Shuttle状态模型的值
        /// </summary>
        private void UpdateShuttleSubsystemStatus(bool blAutoInvoke = true)
        {
            if (!blAutoInvoke)
            {
                return;
            }

            try
            {
                _logger?.Debug("开始更新Shuttle子系统状态（由DI/DO值变化自动触发）");

                // 1. 基本Shuttle状态 (MSD1-MSD3)
                ShuttleSubsystemStatus.ShuttleStatus = _coilStatusHelper.CalculateShuttleStatus();

                // 2. Shuttle位置状态 (SSD1-SSD7) - 需要考虑SSC6配置
                ShuttleSubsystemStatus.ShuttlePositionStatus = _coilStatusHelper.CalculateShuttlePositionStatus(
                    ShuttleSubsystemStatus.Ssc6Config);

                // 3. 晶圆盒门和巢状态 (SSD8-SSD13) - 需要考虑SSC6配置
                ShuttleSubsystemStatus.CassetteDoorNestStatus = _coilStatusHelper.CalculateCassetteDoorNestStatus(
                    ShuttleSubsystemStatus.Ssc6Config);

                // 4. 阀门状态 (SSD14-SSD23)
                ShuttleSubsystemStatus.ShuttleIsoValveStatus = _coilStatusHelper.CalculateShuttleValveStatus(
                    Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleIso);

                ShuttleSubsystemStatus.ShuttleXvValveStatus = _coilStatusHelper.CalculateShuttleValveStatus(
                    Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleXv);

                ShuttleSubsystemStatus.ShuttleBackfillValveStatus = _coilStatusHelper.CalculateShuttleValveStatus(
                    Enums.SS200.IOInterface.Shuttle.ShuttleValveType.ShuttleBackfill);

                ShuttleSubsystemStatus.LoadlockBleedValveStatus = _coilStatusHelper.CalculateShuttleValveStatus(
                    Enums.SS200.IOInterface.Shuttle.ShuttleValveType.LoadlockBleed);

                ShuttleSubsystemStatus.LoadlockBackfillValveStatus = _coilStatusHelper.CalculateShuttleValveStatus(
                    Enums.SS200.IOInterface.Shuttle.ShuttleValveType.LoadlockBackfill);

                // 5. 批次状态 (LSD1-LSD4) - 基于晶圆盒存在传感器 (SDI6-SDI9)
                ShuttleSubsystemStatus.Shuttle1Cassette1LotStatus = _coilStatusHelper.CalculateLotStatus(1, 1); // LSD1
                ShuttleSubsystemStatus.Shuttle1Cassette2LotStatus = _coilStatusHelper.CalculateLotStatus(1, 2); // LSD2
                ShuttleSubsystemStatus.Shuttle2Cassette1LotStatus = _coilStatusHelper.CalculateLotStatus(2, 1); // LSD3
                ShuttleSubsystemStatus.Shuttle2Cassette2LotStatus = _coilStatusHelper.CalculateLotStatus(2, 2); // LSD4

                // 6. 槽位状态 (LSS1-LSS16) - 这里需要根据实际逻辑实现【ShuttleSlotStatusManager类中定义】
                // 目前保持现有值，实际实现需要根据具体的槽位检测逻辑

                _logger?.Debug($"Shuttle子系统状态更新完成: " +
                    $"位置状态={ShuttleSubsystemStatus.ShuttlePositionStatus}, " +
                    $"门巢状态={ShuttleSubsystemStatus.CassetteDoorNestStatus}, " +
                    $"ISO阀门={ShuttleSubsystemStatus.ShuttleIsoValveStatus}, " +
                    $"批次状态: LSD1={ShuttleSubsystemStatus.Shuttle1Cassette1LotStatus}, " +
                    $"LSD2={ShuttleSubsystemStatus.Shuttle1Cassette2LotStatus}, " +
                    $"LSD3={ShuttleSubsystemStatus.Shuttle2Cassette1LotStatus}, " +
                    $"LSD4={ShuttleSubsystemStatus.Shuttle2Cassette2LotStatus}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"更新Shuttle子系统状态时发生错误: {ex.Message}", ex);
                // 发生错误时不抛出异常，保持系统稳定运行
            }

            // 更新状态表格数据
            UpdateStatusProperties();
        }

        #endregion 同上根据当前的S200McuCmdService中的DI、DO信息，使用CoilStatusHelper.cs，CoilStatusUsageExample.cs，通过I/O distrubition逻辑，解析到ShuttleSubsystemStatus

        #region 寄存器处理配置

        /// <summary>
        /// 寄存器处理器配置
        /// </summary>
        private static readonly Dictionary<int, RegisterHandlerConfig> RegisterHandlerConfigs = new()
        {
            // 错误代码寄存器 (需要状态更新)
            { 0, new RegisterHandlerConfig(new[] { nameof(TAxisErrorInfo) }, true, "T轴错误代码") },
            { 1, new RegisterHandlerConfig(new[] { nameof(RAxisErrorInfo) }, true, "R轴错误代码") },
            { 2, new RegisterHandlerConfig(new[] { nameof(ZAxisErrorInfo) }, true, "Z轴错误代码") },

            // 位置寄存器 (需要状态更新)
            { 3, new RegisterHandlerConfig(new[] { nameof(TAxisStep), nameof(TAxisDegree) }, true, "T轴步进值") },
            { 5, new RegisterHandlerConfig(new[] { nameof(RAxisStep), nameof(RAxisLength) }, true, "R轴步进值") },
            { 7, new RegisterHandlerConfig(new[] { nameof(ZAxisStep), nameof(ZAxisHeight) }, true, "Z轴步进值") },

            // Pin Search寄存器 (需要状态更新 - Pin Search值变化会影响Robot位置状态和基准点计算)
            { 9, new RegisterHandlerConfig(new[] { nameof(PinSearchP1_H), nameof(PinSearchP1Value) }, true, "PinSearchP1_H") },
            { 10, new RegisterHandlerConfig(new[] { nameof(PinSearchP1_L), nameof(PinSearchP1Value) }, true, "PinSearchP1_L") },
            { 11, new RegisterHandlerConfig(new[] { nameof(PinSearchP2_H), nameof(PinSearchP2Value) }, true, "PinSearchP2_H") },
            { 12, new RegisterHandlerConfig(new[] { nameof(PinSearchP2_L), nameof(PinSearchP2Value) }, true, "PinSearchP2_L") }
        };

        /// <summary>
        /// 寄存器处理器配置类
        /// </summary>
        private record RegisterHandlerConfig(
            string[] PropertyNames,
            bool RequiresStatusUpdate,
            string Description,
            bool MonitorCombineValue = false)
        {
            public RegisterHandlerConfig(string[] propertyNames, bool requiresStatusUpdate, string description)
                : this(propertyNames, requiresStatusUpdate, description,
                       // 位置寄存器需要监听CombineValue
                       requiresStatusUpdate && (description.Contains("步进值")))
            {
            }
        }

        #endregion 寄存器处理配置

        /// <summary>
        /// 监听RobotAlarmRegisters变化，更新轴错误信息
        /// </summary>
        private void InitializeRegisterPropertyChangeHandlers()
        {
            try
            {
                _logger?.Info("开始初始化寄存器属性变化监听器");

                // 监听集合变化，处理动态添加的寄存器
                if (McuCmdService.RobotAlarmRegisters is ObservableCollection<ModbusRegister> collection)
                {
                    collection.CollectionChanged += OnRobotAlarmRegistersCollectionChanged;
                    _logger?.Debug("已设置RobotAlarmRegisters集合变化监听器");
                }

                // 为现有寄存器设置监听器
                SetupExistingRegistersHandlers();

                _logger?.Info($"寄存器属性变化监听器初始化完成，共处理 {RobotAlarmRegisters.Count} 个寄存器");
            }
            catch (Exception ex)
            {
                _logger?.Error($"初始化寄存器属性变化监听器失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理RobotAlarmRegisters集合变化事件
        /// </summary>
        private void OnRobotAlarmRegistersCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            try
            {
                // 处理新增的寄存器
                if (e.NewItems != null)
                {
                    foreach (ModbusRegister register in e.NewItems)
                    {
                        SetupRegisterHandler(register);
                        _logger?.Debug($"为新增寄存器设置监听器: Address={register.Address}");
                    }
                }

                // 处理移除的寄存器 (如果需要清理事件订阅)
                if (e.OldItems != null)
                {
                    _logger?.Debug($"检测到 {e.OldItems.Count} 个寄存器被移除");
                    // 注意：PropertyChanged事件会在对象被移除时自动清理，通常不需要手动取消订阅
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"处理RobotAlarmRegisters集合变化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 为现有寄存器设置监听器
        /// </summary>
        private void SetupExistingRegistersHandlers()
        {
            // 原来的逻辑是基于索引而不是地址，需要保持一致
            for (int i = 0; i < RobotAlarmRegisters.Count; i++)
            {
                var register = RobotAlarmRegisters[i];
                SetupRegisterHandlerByIndex(register, i);
            }
        }

        /// <summary>
        /// 为单个寄存器设置属性变化监听器（基于地址）
        /// </summary>
        /// <param name="register">寄存器对象</param>
        private void SetupRegisterHandler(ModbusRegister register)
        {
            if (register == null) return;

            // 检查是否有对应的处理配置
            if (!RegisterHandlerConfigs.TryGetValue(register.Address, out var config))
            {
                _logger?.Debug($"寄存器地址 {register.Address} 没有配置处理器，跳过");
                return;
            }

            // 设置属性变化监听器
            register.PropertyChanged += (sender, args) =>
            {
                try
                {
                    // 检查是否是我们关心的属性变化
                    bool shouldHandle = args.PropertyName == nameof(ModbusRegister.Value) ||
                                       (config.MonitorCombineValue && args.PropertyName == nameof(ModbusRegister.Combinevalue));

                    if (!shouldHandle) return;

                    _logger?.Debug($"处理寄存器变化: Address={register.Address}, Property={args.PropertyName}, Value={register.Value}");

                    // 通知相关UI属性更新
                    foreach (var propertyName in config.PropertyNames)
                    {
                        OnPropertyChanged(propertyName);
                    }

                    // 如果需要，更新子系统状态
                    if (config.RequiresStatusUpdate)
                    {
                        UpdateRobotSubsystemStatus();
                    }
                }
                catch (Exception ex)
                {
                    _logger?.Error($"处理寄存器 {register.Address} 属性变化失败: {ex.Message}", ex);
                }
            };

            _logger?.Debug($"已设置寄存器监听器: Address={register.Address}, Description={config.Description}");
        }

        /// <summary>
        /// 为单个寄存器设置属性变化监听器（基于索引，保持与原逻辑一致）
        /// </summary>
        /// <param name="register">寄存器对象</param>
        /// <param name="index">寄存器在集合中的索引</param>
        private void SetupRegisterHandlerByIndex(ModbusRegister register, int index)
        {
            if (register == null) return;

            // 根据索引确定处理逻辑（保持与原来的逻辑一致）
            switch (index)
            {
                case 0: // T轴错误代码
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value))
                        {
                            OnPropertyChanged(nameof(TAxisErrorInfo));
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 1: // R轴错误代码
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value))
                        {
                            OnPropertyChanged(nameof(RAxisErrorInfo));
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 2: // Z轴错误代码
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value))
                        {
                            OnPropertyChanged(nameof(ZAxisErrorInfo));
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 3: // T轴步进值
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value) || args.PropertyName == nameof(ModbusRegister.Combinevalue))
                        {
                            OnPropertyChanged(nameof(TAxisStep));
                            OnPropertyChanged(nameof(TAxisDegree));
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 5: // R轴步进值
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value) || args.PropertyName == nameof(ModbusRegister.Combinevalue))
                        {
                            OnPropertyChanged(nameof(RAxisStep));
                            OnPropertyChanged(nameof(RAxisLength));
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 7: // Z轴步进值
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value) || args.PropertyName == nameof(ModbusRegister.Combinevalue))
                        {
                            OnPropertyChanged(nameof(ZAxisStep));
                            OnPropertyChanged(nameof(ZAxisHeight));
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 9: // PinSearchP1_H
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value))
                        {
                            OnPropertyChanged(nameof(PinSearchP1_H));
                            OnPropertyChanged(nameof(PinSearchP1Value));
                            // Pin Search值变化需要更新子系统状态（影响位置状态和基准点计算）
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 10: // PinSearchP1_L
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value))
                        {
                            OnPropertyChanged(nameof(PinSearchP1_L));
                            OnPropertyChanged(nameof(PinSearchP1Value));
                            // Pin Search值变化需要更新子系统状态（影响位置状态和基准点计算）
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 11: // PinSearchP2_H
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value))
                        {
                            OnPropertyChanged(nameof(PinSearchP2_H));
                            OnPropertyChanged(nameof(PinSearchP2Value));
                            // Pin Search值变化需要更新子系统状态（影响位置状态和基准点计算）
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                case 12: // PinSearchP2_L
                    register.PropertyChanged += (sender, args) =>
                    {
                        if (args.PropertyName == nameof(ModbusRegister.Value))
                        {
                            OnPropertyChanged(nameof(PinSearchP2_L));
                            OnPropertyChanged(nameof(PinSearchP2Value));
                            // Pin Search值变化需要更新子系统状态（影响位置状态和基准点计算）
                            UpdateRobotSubsystemStatus();
                        }
                    };
                    break;

                default:
                    _logger?.Debug($"索引 {index} 没有配置处理器，跳过");
                    break;
            }

            _logger?.Debug($"已设置寄存器监听器: Index={index}, Address={register.Address}");
        }

        /// <summary>
        /// 初始化Chamber线圈变化监听器，当DI、DO值有变化时自动更新Chamber子系统状态
        /// </summary>
        private void InitializeChamberCoilChangeHandlers()
        {
            try
            {
                // 监听ChamberA输入线圈变化
                if (_mcuCmdService.ChaInputCoils is ObservableCollection<ModbusCoil> chaInputCollection)
                {
                    chaInputCollection.CollectionChanged += OnChamberCoilCollectionChanged;
                    // 为现有线圈添加监听
                    foreach (var coil in chaInputCollection)
                    {
                        coil.PropertyChanged += OnChamberCoilPropertyChanged;
                    }
                }

                // 监听ChamberA控制线圈变化
                if (_mcuCmdService.ChaCoils is ObservableCollection<ModbusCoil> chaControlCollection)
                {
                    chaControlCollection.CollectionChanged += OnChamberCoilCollectionChanged;
                    // 为现有线圈添加监听
                    foreach (var coil in chaControlCollection)
                    {
                        coil.PropertyChanged += OnChamberCoilPropertyChanged;
                    }
                }

                // 监听ChamberB输入线圈变化
                if (_mcuCmdService.ChbInputCoils is ObservableCollection<ModbusCoil> chbInputCollection)
                {
                    chbInputCollection.CollectionChanged += OnChamberCoilCollectionChanged;
                    // 为现有线圈添加监听
                    foreach (var coil in chbInputCollection)
                    {
                        coil.PropertyChanged += OnChamberCoilPropertyChanged;
                    }
                }

                // 监听ChamberB控制线圈变化
                if (_mcuCmdService.ChbCoils is ObservableCollection<ModbusCoil> chbControlCollection)
                {
                    chbControlCollection.CollectionChanged += OnChamberCoilCollectionChanged;
                    // 为现有线圈添加监听
                    foreach (var coil in chbControlCollection)
                    {
                        coil.PropertyChanged += OnChamberCoilPropertyChanged;
                    }
                }

                _logger?.Info("Chamber线圈变化监听器初始化成功");
            }
            catch (Exception ex)
            {
                _logger?.Error($"初始化Chamber线圈变化监听器失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理Chamber线圈集合变化事件
        /// </summary>
        private void OnChamberCoilCollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            try
            {
                // 为新添加的线圈添加监听
                if (e.NewItems != null)
                {
                    foreach (ModbusCoil coil in e.NewItems)
                    {
                        coil.PropertyChanged += OnChamberCoilPropertyChanged;
                    }
                }

                // 移除已删除线圈的监听
                if (e.OldItems != null)
                {
                    foreach (ModbusCoil coil in e.OldItems)
                    {
                        coil.PropertyChanged -= OnChamberCoilPropertyChanged;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"处理Chamber线圈集合变化事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理Chamber线圈属性变化事件
        /// </summary>
        private void OnChamberCoilPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            try
            {
                // 只监听Coilvalue属性的变化
                if (e.PropertyName == nameof(ModbusCoil.Coilvalue))
                {
                    var coil = sender as ModbusCoil;
                    if (coil != null)
                    {
                        // 根据设备类型触发对应的防抖定时器，确保ChamberA和ChamberB独立更新
                        if (coil.DeviceType == EnuMcuDeviceType.ChamberA)
                        {
                            _chamberAStatusUpdateTimer.Stop();
                            _chamberAStatusUpdateTimer.Start();
                            _logger?.Debug($"检测到ChamberA线圈值变化: {coil.IoCode} = {coil.Coilvalue}");
                        }
                        else if (coil.DeviceType == EnuMcuDeviceType.ChamberB)
                        {
                            _chamberBStatusUpdateTimer.Stop();
                            _chamberBStatusUpdateTimer.Start();
                            _logger?.Debug($"检测到ChamberB线圈值变化: {coil.IoCode} = {coil.Coilvalue}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"处理Chamber线圈属性变化事件失败: {ex.Message}", ex);
            }
        }

        #region 根据当前的S200McuCmdService中的DI、DO信息，更新SS200子系统Shuttle状态模型的值

        /// <summary>
        /// 初始化Shuttle线圈变化监听器，当DI、DO值有变化时自动更新Shuttle子系统状态
        /// </summary>
        private void InitializeShuttleCoilChangeHandlers()
        {
            try
            {
                // 监听Shuttle输入线圈变化
                if (_mcuCmdService.ShuttleInputCoils is ObservableCollection<ModbusCoil> shuttleInputCollection)
                {
                    shuttleInputCollection.CollectionChanged += OnShuttleCoilCollectionChanged;
                    // 为现有线圈添加监听
                    foreach (var coil in shuttleInputCollection)
                    {
                        coil.PropertyChanged += OnShuttleCoilPropertyChanged;
                    }
                }

                // 监听Shuttle控制线圈变化
                if (_mcuCmdService.ShuttleCoils is ObservableCollection<ModbusCoil> shuttleControlCollection)
                {
                    shuttleControlCollection.CollectionChanged += OnShuttleCoilCollectionChanged;
                    // 为现有线圈添加监听
                    foreach (var coil in shuttleControlCollection)
                    {
                        coil.PropertyChanged += OnShuttleCoilPropertyChanged;
                    }
                }

                _logger?.Info("Shuttle线圈变化监听器初始化成功");
            }
            catch (Exception ex)
            {
                _logger?.Error($"初始化Shuttle线圈变化监听器失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理Shuttle线圈集合变化事件
        /// </summary>
        private void OnShuttleCoilCollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            try
            {
                // 为新添加的线圈添加监听
                if (e.NewItems != null)
                {
                    foreach (ModbusCoil coil in e.NewItems)
                    {
                        coil.PropertyChanged += OnShuttleCoilPropertyChanged;
                    }
                }

                // 移除已删除线圈的监听
                if (e.OldItems != null)
                {
                    foreach (ModbusCoil coil in e.OldItems)
                    {
                        coil.PropertyChanged -= OnShuttleCoilPropertyChanged;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"处理Shuttle线圈集合变化事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理Shuttle线圈属性变化事件
        /// </summary>
        private void OnShuttleCoilPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            try
            {
                // 只监听Coilvalue属性的变化
                if (e.PropertyName == nameof(ModbusCoil.Coilvalue))
                {
                    var coil = sender as ModbusCoil;
                    if (coil != null && coil.DeviceType == EnuMcuDeviceType.Shuttle)
                    {
                        // 使用防抖机制，避免频繁更新
                        _shuttleStatusUpdateTimer.Stop();
                        _shuttleStatusUpdateTimer.Start();

                        _logger?.Debug($"检测到Shuttle线圈值变化: {coil.DeviceType} - {coil.IoCode} = {coil.Coilvalue}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"处理Shuttle线圈属性变化事件失败: {ex.Message}", ex);
            }
        }

        #endregion 根据当前的S200McuCmdService中的DI、DO信息，更新SS200子系统Shuttle状态模型的值

        /// <summary>
        /// 刷新所有设备线圈支持状态
        /// </summary>
        public void RefreshCoilSupportStatus()
        {
            OnPropertyChanged(nameof(IsShuttleCoilReadingSupported));
            OnPropertyChanged(nameof(IsRobotCoilReadingSupported));
            OnPropertyChanged(nameof(IsChaCoilReadingSupported));
            OnPropertyChanged(nameof(IsChbCoilReadingSupported));
        }

        /// <summary>
        /// 启动线圈监控
        /// </summary>
        public void StartCoilsMonitoring()
        {
            _mcuCmdService.StartCoilsMonitoring();
        }

        /// <summary>
        /// 停止线圈监控
        /// </summary>
        public void StopCoilsMonitoring()
        {
            _mcuCmdService.StopCoilsMonitoring();
        }

        /// <summary>
        /// 尝试将文本设置到剪贴板，包含重试逻辑（异步版本）
        /// ✅ 优化报告修复：使用异步延迟和异步Dispatcher调用，避免UI线程阻塞
        /// </summary>
        /// <param name="text">要复制到剪贴板的文本</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试间隔（毫秒）</param>
        /// <returns>是否成功</returns>
        private async Task<bool> TrySetClipboardTextAsync(string text, int maxRetries = 5, int retryDelayMs = 100)
        {
            if (string.IsNullOrEmpty(text))
            {
                _logger?.Warn("尝试复制空文本到剪贴板");
                return false;
            }

            Exception lastException = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    if (attempt > 0)
                    {
                        _logger?.Debug($"尝试复制到剪贴板，第 {attempt} 次重试");
                    }

                    // 使用异步Dispatcher调用，避免死锁
                    await Application.Current.Dispatcher.InvokeAsync(() => Clipboard.SetText(text));

                    _logger?.Debug("成功复制到剪贴板");
                    return true;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _logger?.Debug($"复制到剪贴板失败，重试 {attempt}/{maxRetries}: {ex.Message}");

                    // 如果这不是最后一次尝试，则异步等待一段时间再重试
                    if (attempt < maxRetries)
                    {
                        await Task.Delay(retryDelayMs);  // 异步延迟，不阻塞UI线程
                    }
                }
            }

            // 所有重试都失败
            _logger?.Error($"多次尝试后复制到剪贴板失败: {lastException?.Message}", lastException);
            return false;
        }

        /// <summary>
        /// 尝试将文本设置到剪贴板，包含重试逻辑（同步版本 - 保持向后兼容）
        /// ⚠️ 已过时：此方法会阻塞UI线程，请使用TrySetClipboardTextAsync()异步版本
        /// </summary>
        /// <param name="text">要复制到剪贴板的文本</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试间隔（毫秒）</param>
        /// <returns>是否成功</returns>
        [Obsolete("此方法会阻塞UI线程，请使用TrySetClipboardTextAsync()异步版本")]
        private bool TrySetClipboardText(string text, int maxRetries = 5, int retryDelayMs = 100)
        {
            if (string.IsNullOrEmpty(text))
            {
                _logger?.Warn("尝试复制空文本到剪贴板");
                return false;
            }

            Exception lastException = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    if (attempt > 0)
                    {
                        _logger?.Debug($"尝试复制到剪贴板，第 {attempt} 次重试");
                    }

                    // 在UI线程上执行操作
                    if (Application.Current.Dispatcher.CheckAccess())
                    {
                        Clipboard.SetText(text);
                    }
                    else
                    {
                        Application.Current.Dispatcher.Invoke(() => Clipboard.SetText(text));
                    }

                    _logger?.Debug("成功复制到剪贴板");
                    return true;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _logger?.Debug($"复制到剪贴板失败，重试 {attempt}/{maxRetries}: {ex.Message}");

                    // 如果这不是最后一次尝试，则等待一段时间再重试
                    if (attempt < maxRetries)
                    {
                        Thread.Sleep(retryDelayMs);
                    }
                }
            }

            // 所有重试都失败
            _logger?.Error($"多次尝试后复制到剪贴板失败: {lastException?.Message}", lastException);
            return false;
        }

        /// <summary>
        /// 获取电机错误信息
        /// </summary>
        /// <param name="code">错误代码</param>
        /// <returns>格式化的错误信息</returns>
        public string GetMotorErrorInfo(string code)
        {
            try
            {
                _logger?.Debug($"开始获取电机错误信息: code={code}");
                if (_motorAlarmInfoParser != null && _motorAlarmInfoParser.TryGetAlarmInfo(code, out var motorAlarmInfo))
                {
                    var causeText = motorAlarmInfo.Cause != null && motorAlarmInfo.Cause.Count > 0
                        ? string.Join("; ", motorAlarmInfo.Cause)
                        : "未知原因";

                    var disposeText = motorAlarmInfo.Dispose != null && motorAlarmInfo.Dispose.Count > 0
                        ? string.Join("; ", motorAlarmInfo.Dispose)
                        : "无处理方法";

                    var resetInfo = motorAlarmInfo.AlarmResetClear ? "可通过复位清除" : "不可通过复位清除";

                    var result = $"错误: [{motorAlarmInfo.Code}] {motorAlarmInfo.Kind}\n原因: {causeText}\n处理: {disposeText}\n{resetInfo}";
                    _logger?.Debug($"成功获取电机错误信息: code={code}, kind={motorAlarmInfo.Kind}");
                    return result;
                }
                _logger?.Warn($"未找到对应的电机错误信息: code={code}");
                return $"未知电机错误: {code}";
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取电机错误信息失败: {ex.Message}", ex);
                return $"错误信息查询失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取电机错误工具提示信息
        /// </summary>
        /// <param name="hexCode">错误代码（十六进制）</param>
        /// <returns>格式化的工具提示内容</returns>
        public string GetMotorErrorTooltip(string hexCode)
        {
            try
            {
                _logger?.Debug($"开始解析错误代码: {hexCode}");

                // 移除可能的前缀
                hexCode = hexCode.Replace("0x", "").Trim();

                // 确保大写
                hexCode = hexCode.ToUpper();

                _logger?.Debug($"格式化后的错误代码: {hexCode}");

                if (_motorAlarmInfoParser != null && _motorAlarmInfoParser.TryGetAlarmInfo(hexCode, out var motorAlarmInfo))
                {
                    _logger?.Debug($"成功解析错误代码: {hexCode}, 错误类型: {motorAlarmInfo.Kind}");

                    var sb = new System.Text.StringBuilder();
                    sb.AppendLine($"错误: [{motorAlarmInfo.Code}] {motorAlarmInfo.Kind}");

                    // 添加原因
                    if (motorAlarmInfo.Cause != null && motorAlarmInfo.Cause.Count > 0)
                    {
                        sb.AppendLine("\n可能原因:");
                        foreach (var cause in motorAlarmInfo.Cause)
                        {
                            sb.AppendLine($"• {cause}");
                        }
                    }

                    // 添加处理方法
                    if (motorAlarmInfo.Dispose != null && motorAlarmInfo.Dispose.Count > 0)
                    {
                        sb.AppendLine("\n处理方法:");
                        foreach (var dispose in motorAlarmInfo.Dispose)
                        {
                            sb.AppendLine($"• {dispose}");
                        }
                    }

                    // 添加是否可复位信息
                    sb.AppendLine($"\n{(motorAlarmInfo.AlarmResetClear ? "✓ 可通过复位清除" : "✗ 不可通过复位清除")}");

                    var result = sb.ToString();
                    _logger?.Debug($"生成的错误提示: {result}");
                    return result;
                }
                else
                {
                    if (_motorAlarmInfoParser == null)
                    {
                        _logger?.Error("电机告警信息解析器未初始化");
                    }
                    else
                    {
                        _logger?.Warn($"未找到匹配的错误信息: {hexCode}");
                    }
                    return $"未找到匹配的错误信息: {hexCode}";
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取电机错误工具提示失败: {ex.Message}", ex);
                return $"错误信息查询失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取可通过复位清除的告警列表
        /// </summary>
        /// <returns>可复位告警列表</returns>
        public IEnumerable<MotorAlarmInfo> GetResetClearableAlarms()
        {
            try
            {
                if (_motorAlarmInfoParser != null)
                {
                    return _motorAlarmInfoParser.GetResetClearableAlarmInfos();
                }
                return Enumerable.Empty<MotorAlarmInfo>();
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取可复位告警列表失败: {ex.Message}", ex);
                return Enumerable.Empty<MotorAlarmInfo>();
            }
        }

        /// <summary>
        ///  根据当前的RTZ位置信息，更新SS200子系统状态模型的值

        private async Task ReadSubsystemStatusTimer_TickAsync(object sender, EventArgs e)
        {
            await ParseSubsystemStatus();
        }

        /// </summary>
        /// <returns></returns>
        public async Task<RobotSubsystemStatus> ParseSubsystemStatus()
        {
            RobotSubsystemStatus status = new RobotSubsystemStatus();

            await Task.CompletedTask;
            return status;
        }

        [RelayCommand]
        private void CopyStatusInfo()
        {
            var sb = new System.Text.StringBuilder();

            foreach (var item in StatusProperties)
            {
                sb.AppendLine($"{item.Name} = {item.Value}");
            }

            try
            {
                // 确保在UI线程上执行剪贴板操作
                if (Application.Current.Dispatcher.CheckAccess())
                {
                    // 当前已经在UI线程上
                    SafeSetClipboardText(sb.ToString());
                }
                else
                {
                    // 在其他线程上，需要切换到UI线程
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        SafeSetClipboardText(sb.ToString());
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"复制状态信息到剪贴板失败: {ex.Message}", ex);
                HcGrowlExtensions.Error($"复制失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全地设置剪贴板文本，包含重试逻辑
        /// </summary>
        private void SafeSetClipboardText(string text, int maxRetries = 3)
        {
            Exception lastException = null;

            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    Clipboard.SetText(text);
                    HcGrowlExtensions.Success("已复制状态信息到剪贴板");
                    _logger?.Info("成功复制状态信息到剪贴板");
                    return;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _logger?.Warn($"第 {i + 1} 次尝试复制失败: {ex.Message}");

                    if (i < maxRetries - 1)
                    {
                        // 如果不是最后一次尝试，等待一段时间再重试
                        Thread.Sleep(100);
                    }
                }
            }

            // 如果所有重试都失败了
            string errorMessage = $"复制失败 (已重试{maxRetries}次): {lastException?.Message}";
            _logger?.Error(errorMessage, lastException);
            HcGrowlExtensions.Error(errorMessage);
        }

        #region 手动编辑辅助方法

        /// <summary>
        /// 判断类型是否为可编辑的简单类型
        /// </summary>
        private bool IsSimpleEditableType(Type type)
        {
            if (type == null) return false;

            // 处理可空类型
            Type underlyingType = Nullable.GetUnderlyingType(type) ?? type;

            return underlyingType == typeof(bool) ||
                   underlyingType == typeof(int) ||
                   underlyingType == typeof(double) ||
                   underlyingType == typeof(float) ||
                   underlyingType == typeof(decimal) ||
                   underlyingType == typeof(ushort) ||
                   underlyingType == typeof(short) ||
                   underlyingType == typeof(uint) ||
                   underlyingType == typeof(long) ||
                   underlyingType == typeof(ulong) ||
                   underlyingType == typeof(string) ||
                   underlyingType.IsEnum;
        }

        /// <summary>
        /// 将编辑值转换为指定的属性类型
        /// </summary>
        private object ConvertEditValueToPropertyType(object editValue, Type targetType)
        {
            if (editValue == null)
            {
                // 如果是值类型且非可空，返回默认实例
                if (targetType.IsValueType && Nullable.GetUnderlyingType(targetType) == null)
                    return Activator.CreateInstance(targetType);
                return null;
            }

            // 如果已经是目标类型，直接返回
            if (targetType.IsAssignableFrom(editValue.GetType()))
                return editValue;

            // 如果是字符串，使用原有的字符串转换方法
            if (editValue is string stringValue)
                return ConvertStringToPropertyType(stringValue, targetType);

            // 其他情况，尝试转换
            try
            {
                return Convert.ChangeType(editValue, targetType);
            }
            catch
            {
                return editValue;
            }
        }

        /// <summary>
        /// 将字符串转换为指定的属性类型
        /// </summary>
        private object ConvertStringToPropertyType(string value, Type targetType)
        {
            if (string.IsNullOrEmpty(value) || value == "null")
            {
                // 如果是值类型且非可空，返回默认实例
                if (targetType.IsValueType && Nullable.GetUnderlyingType(targetType) == null)
                    return Activator.CreateInstance(targetType);
                return null;
            }

            Type underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            if (underlyingType == typeof(bool))
                return bool.Parse(value);

            if (underlyingType == typeof(int))
                return int.Parse(value);

            if (underlyingType == typeof(double))
                return double.Parse(value);

            if (underlyingType == typeof(float))
                return float.Parse(value);

            if (underlyingType == typeof(decimal))
                return decimal.Parse(value);

            if (underlyingType == typeof(ushort))
                return ushort.Parse(value);

            if (underlyingType == typeof(short))
                return short.Parse(value);

            if (underlyingType == typeof(uint))
                return uint.Parse(value);

            if (underlyingType == typeof(long))
                return long.Parse(value);

            if (underlyingType == typeof(ulong))
                return ulong.Parse(value);

            if (underlyingType == typeof(string))
                return value;

            if (underlyingType.IsEnum)
                return Enum.Parse(underlyingType, value);

            throw new NotSupportedException($"不支持的类型转换: {targetType}");
        }

        /// <summary>
        /// 保存当前所有值作为原始值
        /// </summary>
        private void SaveAllCurrentValuesAsOriginal()
        {
            foreach (var property in StatusProperties)
            {
                property.SaveAsOriginal();
            }
        }

        /// <summary>
        /// 恢复自动模式
        /// </summary>
        private void RestoreAutoMode()
        {
            try
            {
                _logger?.Info("开始恢复自动模式，重新计算子系统状态");

                // 立即重新计算所有子系统状态，基于当前RTZ轴、DI、DO值
                UpdateRobotSubsystemStatus();
                UpdateChamberSubsystemStatus();
                UpdateShuttleSubsystemStatus();

                // 强制更新StatusProperties集合，显示最新计算的状态
                UpdateStatusPropertiesCore();

                _logger?.Info("自动模式恢复完成，所有状态已基于当前RTZ轴、DI、DO值重新计算");
            }
            catch (Exception ex)
            {
                _logger?.Error($"恢复自动模式时发生错误: {ex.Message}", ex);
                // 如果出错，至少清除手动修改状态
                foreach (var property in StatusProperties)
                {
                    property.RestoreToOriginal();
                }
                UpdateStatusProperties();
            }
        }

        #endregion 手动编辑辅助方法

        #endregion 方法
    }
}