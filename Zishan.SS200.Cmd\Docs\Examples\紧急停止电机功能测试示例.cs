using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Examples
{
    /// <summary>
    /// 紧急停止电机功能测试示例
    /// 演示如何直接调用底层Modbus命令停止机器人三轴电机
    /// </summary>
    public class EmergencyStopMotorExample
    {
        private readonly IS200McuCmdService _mcuCmdService;

        public EmergencyStopMotorExample(IS200McuCmdService mcuCmdService)
        {
            _mcuCmdService = mcuCmdService;
        }

        /// <summary>
        /// 示例1: 基本紧急停止功能
        /// 按照T轴→R轴→Z轴的顺序停止所有电机
        /// </summary>
        public async Task<bool> BasicEmergencyStopExample()
        {
            try
            {
                Console.WriteLine("开始执行紧急停止命令...");

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    Console.WriteLine("❌ Robot未连接，无法执行紧急停止命令");
                    return false;
                }

                // 轴名称映射
                string[] axisNames = { "T轴", "R轴", "Z轴" };
                bool allSuccess = true;

                // 依次停止三个轴的电机
                for (int axis = 1; axis <= 3; axis++)
                {
                    Console.WriteLine($"正在停止{axisNames[axis - 1]}电机...");

                    var result = await _mcuCmdService.Robot.Run(
                        EnuRobotCmdIndex.MotorStop,
                        new List<ushort> { (ushort)axis }
                    );

                    if (result.ReturnInfo == 0)
                    {
                        Console.WriteLine($"✅ {axisNames[axis - 1]}电机停止成功");
                    }
                    else
                    {
                        Console.WriteLine($"❌ {axisNames[axis - 1]}电机停止失败: {result.Response}");
                        allSuccess = false;
                    }
                }

                Console.WriteLine($"紧急停止命令执行完成，结果: {(allSuccess ? "全部成功" : "部分失败")}");
                return allSuccess;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 紧急停止命令执行异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 示例2: 单轴停止功能
        /// 停止指定的单个轴电机
        /// </summary>
        /// <param name="axisNumber">轴编号 (1:T轴, 2:R轴, 3:Z轴)</param>
        public async Task<bool> SingleAxisStopExample(int axisNumber)
        {
            try
            {
                if (axisNumber < 1 || axisNumber > 3)
                {
                    Console.WriteLine("❌ 无效的轴编号，必须是1-3之间");
                    return false;
                }

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    Console.WriteLine("❌ Robot未连接，无法执行停止命令");
                    return false;
                }

                string[] axisNames = { "T轴", "R轴", "Z轴" };
                string axisName = axisNames[axisNumber - 1];

                Console.WriteLine($"正在停止{axisName}电机...");

                var result = await _mcuCmdService.Robot.Run(
                    EnuRobotCmdIndex.MotorStop,
                    new List<ushort> { (ushort)axisNumber }
                );

                if (result.ReturnInfo == 0)
                {
                    Console.WriteLine($"✅ {axisName}电机停止成功");
                    return true;
                }
                else
                {
                    Console.WriteLine($"❌ {axisName}电机停止失败: {result.Response}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 单轴停止命令执行异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 示例3: 带超时控制的紧急停止
        /// 为每个轴的停止操作设置超时时间
        /// </summary>
        public async Task<bool> EmergencyStopWithTimeoutExample()
        {
            try
            {
                Console.WriteLine("开始执行带超时控制的紧急停止命令...");

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    Console.WriteLine("❌ Robot未连接，无法执行紧急停止命令");
                    return false;
                }

                string[] axisNames = { "T轴", "R轴", "Z轴" };
                bool allSuccess = true;
                int timeoutMs = 1000; // 根据RobotParameter.json中的配置

                for (int axis = 1; axis <= 3; axis++)
                {
                    Console.WriteLine($"正在停止{axisNames[axis - 1]}电机 (超时: {timeoutMs}ms)...");

                    try
                    {
                        // 创建超时任务
                        var stopTask = _mcuCmdService.Robot.Run(
                            EnuRobotCmdIndex.MotorStop,
                            new List<ushort> { (ushort)axis }
                        );

                        var timeoutTask = Task.Delay(timeoutMs);
                        var completedTask = await Task.WhenAny(stopTask, timeoutTask);

                        if (completedTask == timeoutTask)
                        {
                            Console.WriteLine($"⏰ {axisNames[axis - 1]}电机停止超时");
                            allSuccess = false;
                        }
                        else
                        {
                            var result = await stopTask;
                            if (result.ReturnInfo == 0)
                            {
                                Console.WriteLine($"✅ {axisNames[axis - 1]}电机停止成功");
                            }
                            else
                            {
                                Console.WriteLine($"❌ {axisNames[axis - 1]}电机停止失败: {result.Response}");
                                allSuccess = false;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ {axisNames[axis - 1]}电机停止异常: {ex.Message}");
                        allSuccess = false;
                    }
                }

                Console.WriteLine($"带超时控制的紧急停止命令执行完成，结果: {(allSuccess ? "全部成功" : "部分失败")}");
                return allSuccess;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 带超时控制的紧急停止命令执行异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 示例4: 检查电机状态后停止
        /// 在停止前检查电机当前状态
        /// </summary>
        public async Task<bool> StopWithStatusCheckExample()
        {
            try
            {
                Console.WriteLine("开始执行带状态检查的紧急停止命令...");

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    Console.WriteLine("❌ Robot未连接，无法执行紧急停止命令");
                    return false;
                }

                // 这里可以添加电机状态检查逻辑
                // 例如检查电机是否正在运行、是否有报警等
                Console.WriteLine("📊 检查电机当前状态...");

                // 模拟状态检查
                await Task.Delay(100);
                Console.WriteLine("✅ 电机状态检查完成，开始执行停止命令");

                // 执行停止命令
                return await BasicEmergencyStopExample();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 带状态检查的紧急停止命令执行异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 示例5: 完整的测试流程
        /// 演示完整的测试和验证流程
        /// </summary>
        public async Task RunCompleteTestExample()
        {
            Console.WriteLine("=== 紧急停止电机功能完整测试 ===");

            // 测试1: 基本紧急停止
            Console.WriteLine("\n--- 测试1: 基本紧急停止 ---");
            await BasicEmergencyStopExample();

            // 等待一段时间
            await Task.Delay(2000);

            // 测试2: 单轴停止 (T轴)
            Console.WriteLine("\n--- 测试2: 单轴停止 (T轴) ---");
            await SingleAxisStopExample(1);

            // 等待一段时间
            await Task.Delay(1000);

            // 测试3: 带超时控制的停止
            Console.WriteLine("\n--- 测试3: 带超时控制的停止 ---");
            await EmergencyStopWithTimeoutExample();

            // 等待一段时间
            await Task.Delay(1000);

            // 测试4: 带状态检查的停止
            Console.WriteLine("\n--- 测试4: 带状态检查的停止 ---");
            await StopWithStatusCheckExample();

            Console.WriteLine("\n=== 测试完成 ===");
        }
    }

    /// <summary>
    /// 测试运行器
    /// </summary>
    public class EmergencyStopTestRunner
    {
        public static async Task RunTests(IS200McuCmdService mcuCmdService)
        {
            var example = new EmergencyStopMotorExample(mcuCmdService);
            await example.RunCompleteTestExample();
        }
    }
}
