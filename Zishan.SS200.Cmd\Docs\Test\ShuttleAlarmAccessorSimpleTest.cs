using System;
using <PERSON><PERSON>an.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Shuttle;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// Shuttle报警访问器简单测试类
    /// 用于验证Shuttle报警代码访问器的基本功能
    /// </summary>
    public class ShuttleAlarmAccessorSimpleTest
    {
        /// <summary>
        /// 简单测试Shuttle报警代码访问器
        /// </summary>
        public static void SimpleTest()
        {
            try
            {
                Console.WriteLine("=== Shuttle报警代码访问器简单测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var shuttleAlarm = interLock.AlarmCode.Shuttle;

                // 测试前10个报警代码
                Console.WriteLine("\n测试前10个Shuttle报警代码:");
                TestAccessor("SA1", shuttleAlarm.SA1_SystemBusyReject);
                TestAccessor("SA2", shuttleAlarm.SA2_SystemAlarmReject);
                TestAccessor("SA3", shuttleAlarm.SA3_CassetteNestMoveTimeout);
                TestAccessor("SA4", shuttleAlarm.SA4_CassetteNestSpeedTooHigh);
                TestAccessor("SA5", shuttleAlarm.SA5_CassetteNestPositionFailure);
                TestAccessor("SA6", shuttleAlarm.SA6_ShuttleMoveTimeout);
                TestAccessor("SA7", shuttleAlarm.SA7_ShuttleMoveTooFast);
                TestAccessor("SA8", shuttleAlarm.SA8_ShuttleUpDownPositionFailure);
                TestAccessor("SA9", shuttleAlarm.SA9_ShuttleRotateTimeout);
                TestAccessor("SA10", shuttleAlarm.SA10_ShuttleRotateTooFast);

                // 测试中间的一些报警代码
                Console.WriteLine("\n测试中间的一些Shuttle报警代码:");
                TestAccessor("SA15", shuttleAlarm.SA15_CassetteDoorCloseTimeout);
                TestAccessor("SA20", shuttleAlarm.SA20_PressureDeltaOutOfRange);
                TestAccessor("SA25", shuttleAlarm.SA25_ChamberPressureReviewReturnReject);
                TestAccessor("SA30", shuttleAlarm.SA30_CHATriggerAlarmPumpDownError);

                // 测试最后几个报警代码
                Console.WriteLine("\n测试最后几个Shuttle报警代码:");
                TestAccessor("SA35", shuttleAlarm.SA35_CHAPressureHighPumpDownError);
                TestAccessor("SA36", shuttleAlarm.SA36_CHBPressureHighPumpDownError);
                TestAccessor("SA37", shuttleAlarm.SA37_SystemTriggerAlarmReject);
                TestAccessor("SA38", shuttleAlarm.SA38_LoadlockNotIdleReject);
                TestAccessor("SA39", shuttleAlarm.SA39_ShuttlePressureNotATMBackfillFailure);

                Console.WriteLine("\n=== Shuttle报警代码访问器简单测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试单个访问器
        /// </summary>
        /// <param name="code">报警代码</param>
        /// <param name="accessor">访问器</param>
        private static void TestAccessor(string code, AlarmPropertyAccessor accessor)
        {
            try
            {
                if (accessor != null)
                {
                    string content = accessor.Content ?? "未定义";
                    string chsContent = accessor.ChsContent ?? "未定义";
                    Console.WriteLine($"  {code}: {content}");
                    Console.WriteLine($"       中文: {chsContent}");
                }
                else
                {
                    Console.WriteLine($"  {code}: 访问器为空");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  {code}: 访问器测试失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 统计测试 - 验证所有38个访问器都已定义
        /// </summary>
        public static void CountTest()
        {
            try
            {
                Console.WriteLine("=== Shuttle报警代码访问器统计测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                var shuttleAlarm = interLock.AlarmCode.Shuttle;
                var shuttleAlarmType = shuttleAlarm.GetType();
                
                // 获取所有以SA开头的属性
                var properties = shuttleAlarmType.GetProperties();
                int saPropertyCount = 0;
                
                foreach (var property in properties)
                {
                    if (property.Name.StartsWith("SA") && property.Name.Contains("_"))
                    {
                        saPropertyCount++;
                        Console.WriteLine($"  发现访问器: {property.Name}");
                    }
                }
                
                Console.WriteLine($"\n总计发现 {saPropertyCount} 个Shuttle报警代码访问器");

                if (saPropertyCount == 39)
                {
                    Console.WriteLine("✅ 所有39个Shuttle报警代码访问器都已正确定义！");
                }
                else
                {
                    Console.WriteLine($"❌ 预期39个访问器，实际发现{saPropertyCount}个");
                }
                
                Console.WriteLine("=== Shuttle报警代码访问器统计测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"统计测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 综合统计测试 - 验证所有子系统的报警代码访问器
        /// </summary>
        public static void ComprehensiveCountTest()
        {
            try
            {
                Console.WriteLine("=== 所有子系统报警代码访问器综合统计测试 ===");
                
                var interLock = SS200InterLockMain.Instance;
                
                // 统计Robot报警代码访问器
                var robotAlarm = interLock.AlarmCode.Robot;
                var robotAlarmType = robotAlarm.GetType();
                var robotProperties = robotAlarmType.GetProperties();
                int robotCount = 0;
                foreach (var property in robotProperties)
                {
                    if (property.Name.StartsWith("RA") && property.Name.Contains("_"))
                        robotCount++;
                }
                
                // 统计Chamber报警代码访问器
                var chamberAlarm = interLock.AlarmCode.ChamberA;
                var chamberAlarmType = chamberAlarm.GetType();
                var chamberProperties = chamberAlarmType.GetProperties();
                int chamberCount = 0;
                foreach (var property in chamberProperties)
                {
                    if (property.Name.StartsWith("PAC") && property.Name.Contains("_"))
                        chamberCount++;
                }
                
                // 统计Shuttle报警代码访问器
                var shuttleAlarm = interLock.AlarmCode.Shuttle;
                var shuttleAlarmType = shuttleAlarm.GetType();
                var shuttleProperties = shuttleAlarmType.GetProperties();
                int shuttleCount = 0;
                foreach (var property in shuttleProperties)
                {
                    if (property.Name.StartsWith("SA") && property.Name.Contains("_"))
                        shuttleCount++;
                }
                
                Console.WriteLine($"\n📊 报警代码访问器统计结果:");
                Console.WriteLine($"  🤖 Robot报警代码访问器: {robotCount} 个 (预期67个)");
                Console.WriteLine($"  🏭 Chamber报警代码访问器: {chamberCount} 个 (预期65个)");
                Console.WriteLine($"  🚀 Shuttle报警代码访问器: {shuttleCount} 个 (预期39个)");
                Console.WriteLine($"  📈 总计: {robotCount + chamberCount + shuttleCount} 个 (预期171个)");

                bool allComplete = (robotCount == 67) && (chamberCount == 65) && (shuttleCount == 39);
                
                if (allComplete)
                {
                    Console.WriteLine("\n✅ 所有子系统的报警代码访问器都已完整实现！");
                    Console.WriteLine("🎉 SS200InterLockMain报警管理体系已完成！");
                }
                else
                {
                    Console.WriteLine("\n❌ 部分子系统的报警代码访问器尚未完成:");
                    if (robotCount != 67) Console.WriteLine($"   - Robot: 缺少 {67 - robotCount} 个");
                    if (chamberCount != 65) Console.WriteLine($"   - Chamber: 缺少 {65 - chamberCount} 个");
                    if (shuttleCount != 39) Console.WriteLine($"   - Shuttle: 缺少 {39 - shuttleCount} 个");
                }
                
                Console.WriteLine("=== 综合统计测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"综合统计测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            SimpleTest();
            Console.WriteLine();
            CountTest();
            Console.WriteLine();
            ComprehensiveCountTest();
        }
    }
}
