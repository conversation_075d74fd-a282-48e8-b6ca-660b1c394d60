using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using Zishan.SS200.Cmd.Models;

namespace Zishan.SS200.Cmd.Config
{
    /// <summary>
    /// 批量命令配置解析器
    /// </summary>
    public class BatchCommandParser
    {
        private readonly string _configFilePath;
        private BatchCommandConfig _batchConfig;

        /// <summary>
        /// 初始化批量命令解析器
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        public BatchCommandParser(string configFilePath)
        {
            _configFilePath = configFilePath;
            _batchConfig = new BatchCommandConfig();
            LoadConfig();
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    // 如果配置文件不存在，创建一个默认的空配置
                    _batchConfig = new BatchCommandConfig();
                    return;
                }

                var jsonString = File.ReadAllText(_configFilePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                _batchConfig = JsonSerializer.Deserialize<BatchCommandConfig>(jsonString, options) ?? new BatchCommandConfig();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载批量命令配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取批量配置
        /// </summary>
        /// <returns>批量命令配置</returns>
        public BatchCommandConfig GetBatchConfig()
        {
            return _batchConfig;
        }

        /// <summary>
        /// 获取所有批量序列
        /// </summary>
        /// <returns>批量序列列表</returns>
        public List<BatchCommandSequence> GetAllBatchSequences()
        {
            return _batchConfig.BatchSequences;
        }

        /// <summary>
        /// 根据名称获取批量序列
        /// </summary>
        /// <param name="name">批量序列名称</param>
        /// <returns>批量序列</returns>
        public BatchCommandSequence GetBatchSequence(string name)
        {
            var sequence = _batchConfig.BatchSequences.FirstOrDefault(x => x.Name == name);
            if (sequence == null)
            {
                throw new KeyNotFoundException($"未找到名为 '{name}' 的批量命令序列");
            }
            return sequence;
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfig()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                string jsonString = JsonSerializer.Serialize(_batchConfig, options);
                File.WriteAllText(_configFilePath, jsonString);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"保存批量命令配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 添加或更新批量序列
        /// </summary>
        /// <param name="sequence">批量序列</param>
        public void AddOrUpdateBatchSequence(BatchCommandSequence sequence)
        {
            var existingSequence = _batchConfig.BatchSequences.FirstOrDefault(x => x.Name == sequence.Name);
            if (existingSequence != null)
            {
                // 更新现有序列
                int index = _batchConfig.BatchSequences.IndexOf(existingSequence);
                _batchConfig.BatchSequences[index] = sequence;
            }
            else
            {
                // 添加新序列
                _batchConfig.BatchSequences.Add(sequence);
            }
            SaveConfig();
        }

        /// <summary>
        /// 删除批量序列
        /// </summary>
        /// <param name="name">批量序列名称</param>
        public void DeleteBatchSequence(string name)
        {
            var sequence = _batchConfig.BatchSequences.FirstOrDefault(x => x.Name == name);
            if (sequence != null)
            {
                _batchConfig.BatchSequences.Remove(sequence);
                SaveConfig();
            }
        }
    }
} 