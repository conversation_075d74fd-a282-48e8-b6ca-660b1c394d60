using System;
using System.Threading;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// PinSearch取消功能示例
    /// 演示如何使用CancellationToken来取消PinSearch操作
    /// </summary>
    public class PinSearchCancellationExample
    {
        private readonly IS200McuCmdService _mcuCmdService;
        private CancellationTokenSource _cancellationTokenSource;

        public PinSearchCancellationExample(IS200McuCmdService mcuCmdService)
        {
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
        }

        /// <summary>
        /// 示例1：基本的PinSearch取消功能
        /// </summary>
        public async Task BasicCancellationExample()
        {
            UILogService.AddLogAndIncreaseIndent("=== PinSearch取消功能示例 ===");

            try
            {
                // 创建取消令牌源
                _cancellationTokenSource = new CancellationTokenSource();

                // 设置5秒后自动取消
                _cancellationTokenSource.CancelAfter(TimeSpan.FromSeconds(5));

                UILogService.AddLog("开始执行PinSearch操作，5秒后将自动取消...");

                // 执行PinSearch操作（支持取消）
                var result = await _mcuCmdService.PinSearchAsync(
                    EnuRobotEndType.Smooth, 
                    false, 
                    _cancellationTokenSource.Token);

                if (result.Success)
                {
                    UILogService.AddSuccessLog($"PinSearch执行成功: {result.Message}");
                    UILogService.AddLog($"P1值: {result.PinSearchP1Value}, P2值: {result.PinSearchP2Value}");
                }
                else
                {
                    UILogService.AddErrorLog($"PinSearch执行失败: {result.Message}");
                }
            }
            catch (OperationCanceledException)
            {
                UILogService.AddWarningLog("PinSearch操作已被取消");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"PinSearch操作异常: {ex.Message}");
            }
            finally
            {
                _cancellationTokenSource?.Dispose();
                UILogService.DecreaseIndentAndAddLog("=== PinSearch取消功能示例结束 ===");
            }
        }

        /// <summary>
        /// 示例2：手动取消PinSearch操作
        /// </summary>
        public async Task ManualCancellationExample()
        {
            UILogService.AddLogAndIncreaseIndent("=== 手动取消PinSearch示例 ===");

            try
            {
                // 创建取消令牌源
                _cancellationTokenSource = new CancellationTokenSource();

                UILogService.AddLog("开始执行PinSearch操作...");
                UILogService.AddLog("提示：可以调用CancelOperation()方法来手动取消操作");

                // 在后台任务中执行PinSearch
                var pinSearchTask = _mcuCmdService.PinSearchAsync(
                    EnuRobotEndType.Nose, 
                    false, 
                    _cancellationTokenSource.Token);

                // 模拟用户在2秒后手动取消
                _ = Task.Run(async () =>
                {
                    await Task.Delay(2000);
                    UILogService.AddLog("模拟用户手动取消操作...");
                    CancelOperation();
                });

                // 等待PinSearch完成或被取消
                var result = await pinSearchTask;

                if (result.Success)
                {
                    UILogService.AddSuccessLog($"PinSearch执行成功: {result.Message}");
                    UILogService.AddLog($"P1值: {result.PinSearchP1Value}, P2值: {result.PinSearchP2Value}");
                }
                else
                {
                    UILogService.AddErrorLog($"PinSearch执行失败: {result.Message}");
                }
            }
            catch (OperationCanceledException)
            {
                UILogService.AddWarningLog("PinSearch操作已被手动取消");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"PinSearch操作异常: {ex.Message}");
            }
            finally
            {
                _cancellationTokenSource?.Dispose();
                UILogService.DecreaseIndentAndAddLog("=== 手动取消PinSearch示例结束 ===");
            }
        }

        /// <summary>
        /// 示例3：多个PinSearch操作的批量取消
        /// </summary>
        public async Task BatchCancellationExample()
        {
            UILogService.AddLogAndIncreaseIndent("=== 批量取消PinSearch示例 ===");

            try
            {
                // 创建取消令牌源
                _cancellationTokenSource = new CancellationTokenSource();

                UILogService.AddLog("开始执行多个PinSearch操作...");

                // 创建多个PinSearch任务
                var smoothTask = _mcuCmdService.PinSearchAsync(
                    EnuRobotEndType.Smooth, 
                    false, 
                    _cancellationTokenSource.Token);

                var noseTask = _mcuCmdService.PinSearchAsync(
                    EnuRobotEndType.Nose, 
                    false, 
                    _cancellationTokenSource.Token);

                // 设置3秒后取消所有操作
                _cancellationTokenSource.CancelAfter(TimeSpan.FromSeconds(3));

                // 等待所有任务完成或被取消
                var tasks = new Task[] { smoothTask, noseTask };
                
                try
                {
                    await Task.WhenAll(tasks);
                    
                    var smoothResult = await smoothTask;
                    var noseResult = await noseTask;
                    
                    UILogService.AddSuccessLog("所有PinSearch操作完成");
                    UILogService.AddLog($"Smooth端结果: {smoothResult.Message}");
                    UILogService.AddLog($"Nose端结果: {noseResult.Message}");
                }
                catch (OperationCanceledException)
                {
                    UILogService.AddWarningLog("所有PinSearch操作已被取消");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"批量PinSearch操作异常: {ex.Message}");
            }
            finally
            {
                _cancellationTokenSource?.Dispose();
                UILogService.DecreaseIndentAndAddLog("=== 批量取消PinSearch示例结束 ===");
            }
        }

        /// <summary>
        /// 手动取消当前操作
        /// </summary>
        public void CancelOperation()
        {
            try
            {
                _cancellationTokenSource?.Cancel();
                UILogService.AddLog("已发送取消请求");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"取消操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查操作是否已被取消
        /// </summary>
        public bool IsCancellationRequested => _cancellationTokenSource?.Token.IsCancellationRequested ?? false;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _cancellationTokenSource?.Dispose();
        }
    }

    /// <summary>
    /// PinSearch取消功能使用说明
    /// </summary>
    public static class PinSearchCancellationUsage
    {
        /// <summary>
        /// 使用说明和最佳实践
        /// </summary>
        public static void ShowUsageInstructions()
        {
            UILogService.AddLogAndIncreaseIndent("=== PinSearch取消功能使用说明 ===");

            UILogService.AddLog("1. 基本用法:");
            UILogService.AddLog("   var cts = new CancellationTokenSource();");
            UILogService.AddLog("   var result = await mcuCmdService.PinSearchAsync(endType, false, cts.Token);");

            UILogService.AddLog("");
            UILogService.AddLog("2. 设置超时取消:");
            UILogService.AddLog("   cts.CancelAfter(TimeSpan.FromSeconds(10)); // 10秒后自动取消");

            UILogService.AddLog("");
            UILogService.AddLog("3. 手动取消:");
            UILogService.AddLog("   cts.Cancel(); // 立即取消");

            UILogService.AddLog("");
            UILogService.AddLog("4. 异常处理:");
            UILogService.AddLog("   try { ... } catch (OperationCanceledException) { /* 处理取消 */ }");

            UILogService.AddLog("");
            UILogService.AddLog("5. 资源清理:");
            UILogService.AddLog("   finally { cts?.Dispose(); }");

            UILogService.AddLog("");
            UILogService.AddLog("注意事项:");
            UILogService.AddLog("- 取消操作是协作式的，需要代码检查取消令牌");
            UILogService.AddLog("- 取消后会抛出OperationCanceledException异常");
            UILogService.AddLog("- 务必在finally块中释放CancellationTokenSource资源");
            UILogService.AddLog("- 取消操作不会立即停止硬件动作，只是停止等待和轮询");

            UILogService.DecreaseIndentAndAddLog("=== 使用说明结束 ===");
        }
    }
}
