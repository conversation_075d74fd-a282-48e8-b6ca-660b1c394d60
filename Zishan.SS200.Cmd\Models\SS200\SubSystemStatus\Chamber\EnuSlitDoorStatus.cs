using System.ComponentModel;

namespace Zish<PERSON>.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// Slit Door状态枚举
    /// </summary>
    public enum EnuSlitDoorStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        [Description("未知")]
        None = 0,

        /// <summary>
        /// SlitDoor打开状态 (SP1: PDI12=1 PDI13=0)
        /// </summary>
        [Description("打开")]
        Open = 1,

        /// <summary>
        /// SlitDoor关闭状态 (SP2: PDI12=0 PDI13=1)
        /// </summary>
        [Description("关闭")]
        Close = 2,

        /// <summary>
        /// SlitDoor在打开与关闭状态之间 (SP3: PDI12=0 PDI13=0)
        /// </summary>
        [Description("开关之间")]
        BetweenOpenClose = 3
    }
}