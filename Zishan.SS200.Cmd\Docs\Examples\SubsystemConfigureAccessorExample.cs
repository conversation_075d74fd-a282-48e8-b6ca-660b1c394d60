using System;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// 子系统配置访问器使用示例
    /// 演示如何使用完善的配置访问器按SubsystemConfigure分类访问各子系统配置参数
    /// </summary>
    public class SubsystemConfigureAccessorExample
    {
        /// <summary>
        /// 演示Robot配置访问器的使用
        /// </summary>
        public static void DemonstrateRobotConfigureAccessor()
        {
            Console.WriteLine("=== Robot配置访问器示例 ===");
            
            // 获取SS200InterLockMain实例
            var ss200Main = SS200InterLockMain.Instance;
            
            // 访问Robot配置参数
            var robotConfig = ss200Main.SubsystemConfigure.Robot;
            
            // 访问Robot配置设置参数
            Console.WriteLine("Robot配置设置参数:");
            Console.WriteLine($"RPS1 - 机器人旋转速度: {robotConfig.RPS1_RobotRotateSpeed?.Value} {robotConfig.RPS1_RobotRotateSpeed?.Unit}");
            Console.WriteLine($"RPS2 - 机器人伸展速度: {robotConfig.RPS2_RobotExtendSpeed?.Value} {robotConfig.RPS2_RobotExtendSpeed?.Unit}");
            Console.WriteLine($"RPS3 - 机器人上下速度: {robotConfig.RPS3_RobotUpDownSpeed?.Value} {robotConfig.RPS3_RobotUpDownSpeed?.Unit}");
            Console.WriteLine($"RPS8 - 机器人旋转超时: {robotConfig.RPS8_RobotRotateTimeout?.Value} {robotConfig.RPS8_RobotRotateTimeout?.Unit}");
            Console.WriteLine($"RPS27 - 机器人旋转时Z轴高度: {robotConfig.RPS27_ZAxisHeightForRobotRotation?.Value} {robotConfig.RPS27_ZAxisHeightForRobotRotation?.Unit}");
            Console.WriteLine($"RPS30 - 插销搜索Z轴高度: {robotConfig.RPS30_ZAxisHeightForPinSearch?.Value} {robotConfig.RPS30_ZAxisHeightForPinSearch?.Unit}");
            
            // 访问Robot位置参数
            Console.WriteLine("\nRobot位置参数:");

            // T轴位置参数
            Console.WriteLine("T轴位置参数:");
            Console.WriteLine($"RP1 - T轴smooth到CHA: {robotConfig.RP1_TAxisSmoothToCHA?.Value} {robotConfig.RP1_TAxisSmoothToCHA?.Unit}");
            Console.WriteLine($"RP2 - T轴smooth到CHB: {robotConfig.RP2_TAxisSmoothToCHB?.Value} {robotConfig.RP2_TAxisSmoothToCHB?.Unit}");
            Console.WriteLine($"RP3 - T轴smooth到冷却腔: {robotConfig.RP3_TAxisSmoothToCoolingChamber?.Value} {robotConfig.RP3_TAxisSmoothToCoolingChamber?.Unit}");
            Console.WriteLine($"RP4 - T轴smooth到卡匣: {robotConfig.RP4_TAxisSmoothToCassette?.Value} {robotConfig.RP4_TAxisSmoothToCassette?.Unit}");
            Console.WriteLine($"RP5 - T轴nose到CHA: {robotConfig.RP5_TAxisNoseToCHA?.Value} {robotConfig.RP5_TAxisNoseToCHA?.Unit}");
            Console.WriteLine($"RP9 - T轴零位: {robotConfig.RP9_TAxisZero?.Value} {robotConfig.RP9_TAxisZero?.Unit}");

            // R轴位置参数
            Console.WriteLine("\nR轴位置参数:");
            Console.WriteLine($"RP10 - R轴smooth伸展面向CHA: {robotConfig.RP10_RAxisSmoothExtendFaceToCHA?.Value} {robotConfig.RP10_RAxisSmoothExtendFaceToCHA?.Unit}");
            Console.WriteLine($"RP11 - R轴smooth伸展面向CHB: {robotConfig.RP11_RAxisSmoothExtendFaceToCHB?.Value} {robotConfig.RP11_RAxisSmoothExtendFaceToCHB?.Unit}");
            Console.WriteLine($"RP18 - R轴零位: {robotConfig.RP18_RAxisZeroPosition?.Value} {robotConfig.RP18_RAxisZeroPosition?.Unit}");

            // Z轴位置参数
            Console.WriteLine("\nZ轴位置参数:");
            Console.WriteLine($"RP19 - Z轴smooth到CHA高度: {robotConfig.RP19_ZAxisHeightAtSmoothToCHA?.Value} {robotConfig.RP19_ZAxisHeightAtSmoothToCHA?.Unit}");
            Console.WriteLine($"RP20 - Z轴smooth到CHB高度: {robotConfig.RP20_ZAxisHeightAtSmoothToCHB?.Value} {robotConfig.RP20_ZAxisHeightAtSmoothToCHB?.Unit}");
            Console.WriteLine($"RP27 - Z轴零位: {robotConfig.RP27_ZAxisZeroPosition?.Value} {robotConfig.RP27_ZAxisZeroPosition?.Unit}");
            Console.WriteLine($"RP28 - Z轴插销搜索高度: {robotConfig.RP28_ZAxisHeightToPinSearch?.Value} {robotConfig.RP28_ZAxisHeightToPinSearch?.Unit}");
        }

        /// <summary>
        /// 演示Chamber配置访问器的使用
        /// </summary>
        public static void DemonstrateChamberConfigureAccessor()
        {
            Console.WriteLine("\n=== Chamber配置访问器示例 ===");
            
            var ss200Main = SS200InterLockMain.Instance;
            
            // 访问ChamberA配置参数
            var chamberAConfig = ss200Main.SubsystemConfigure.ChamberA;
            
            Console.WriteLine("ChamberA配置参数:");
            Console.WriteLine($"PPS1 - 狭缝门运动最小时间: {chamberAConfig.PPS1_SlitDoorMotionMinTime?.Value} {chamberAConfig.PPS1_SlitDoorMotionMinTime?.Unit}");
            Console.WriteLine($"PPS2 - 狭缝门运动最大时间: {chamberAConfig.PPS2_SlitDoorMotionMaxTime?.Value} {chamberAConfig.PPS2_SlitDoorMotionMaxTime?.Unit}");
            Console.WriteLine($"PPS7 - 腔室抽真空最大时间: {chamberAConfig.PPS7_ChamberPumpDownMaxTime?.Value} {chamberAConfig.PPS7_ChamberPumpDownMaxTime?.Unit}");
            Console.WriteLine($"PPS9 - 腔室真空压力设定点: {chamberAConfig.PPS9_ChamberVacuumPressureSetpoint?.Value} {chamberAConfig.PPS9_ChamberVacuumPressureSetpoint?.Unit}");
            Console.WriteLine($"PPS19 - RF1功率设定点: {chamberAConfig.PPS19_Rf1PowerSetpoint?.Value} {chamberAConfig.PPS19_Rf1PowerSetpoint?.Unit}");
            Console.WriteLine($"PPS23 - 温度设定点: {chamberAConfig.PPS23_TemperatureSetpoint?.Value} {chamberAConfig.PPS23_TemperatureSetpoint?.Unit}");
        }

        /// <summary>
        /// 演示Shuttle配置访问器的使用
        /// </summary>
        public static void DemonstrateShuttleConfigureAccessor()
        {
            Console.WriteLine("\n=== Shuttle配置访问器示例 ===");
            
            var ss200Main = SS200InterLockMain.Instance;
            
            // 访问Shuttle配置参数
            var shuttleConfig = ss200Main.SubsystemConfigure.Shuttle;
            
            Console.WriteLine("Shuttle配置参数:");
            Console.WriteLine($"SPS1 - 卡匣巢伸展/缩回最小时间: {shuttleConfig.SPS1_CassetteNestExtendRetractMinTime?.Value} {shuttleConfig.SPS1_CassetteNestExtendRetractMinTime?.Unit}");
            Console.WriteLine($"SPS3 - Shuttle上下最小时间: {shuttleConfig.SPS3_ShuttleUpDownMinTime?.Value} {shuttleConfig.SPS3_ShuttleUpDownMinTime?.Unit}");
            Console.WriteLine($"SPS12 - Shuttle传输压力: {shuttleConfig.SPS12_ShuttleTransferPressure?.Value} {shuttleConfig.SPS12_ShuttleTransferPressure?.Unit}");
            Console.WriteLine($"SPS17 - Shuttle抽真空最大时间: {shuttleConfig.SPS17_PumpDownShuttleMaxTime?.Value} {shuttleConfig.SPS17_PumpDownShuttleMaxTime?.Unit}");
        }

        /// <summary>
        /// 演示MainSystem配置访问器的使用
        /// </summary>
        public static void DemonstrateMainSystemConfigureAccessor()
        {
            Console.WriteLine("\n=== MainSystem配置访问器示例 ===");
            
            var ss200Main = SS200InterLockMain.Instance;
            
            // 访问MainSystem配置参数
            var mainSystemConfig = ss200Main.SubsystemConfigure.MainSystem;
            
            Console.WriteLine("MainSystem配置参数:");
            Console.WriteLine($"SSC1 - Shuttle 1晶圆尺寸: {mainSystemConfig.SSC1_Shuttle1WaferSize?.Value} {mainSystemConfig.SSC1_Shuttle1WaferSize?.Unit}");
            Console.WriteLine($"SSC2 - Shuttle 2晶圆尺寸: {mainSystemConfig.SSC2_Shuttle2WaferSize?.Value} {mainSystemConfig.SSC2_Shuttle2WaferSize?.Unit}");
            Console.WriteLine($"SSC6 - 卡匣巢类型: {mainSystemConfig.SSC6_CassetteNestType?.Value}");
            Console.WriteLine($"SSC8 - 优先级生效时间: {mainSystemConfig.SSC8_PriorityEffective?.Value} {mainSystemConfig.SSC8_PriorityEffective?.Unit}");
            Console.WriteLine($"SSC11 - 冷却腔温度: {mainSystemConfig.SSC11_TemperatureForCoolingChamber?.Value} {mainSystemConfig.SSC11_TemperatureForCoolingChamber?.Unit}");
        }

        /// <summary>
        /// 演示配置参数的详细信息访问
        /// </summary>
        public static void DemonstrateConfigParameterDetails()
        {
            Console.WriteLine("\n=== 配置参数详细信息示例 ===");
            
            var ss200Main = SS200InterLockMain.Instance;
            
            // 获取配置参数的详细信息
            var robotRotateSpeed = ss200Main.SubsystemConfigure.Robot.RPS1_RobotRotateSpeed;
            var tAxisPosition = ss200Main.SubsystemConfigure.Robot.RP1_TAxisSmoothToCHA;

            if (robotRotateSpeed != null)
            {
                Console.WriteLine("RPS1 - 机器人旋转速度详细信息:");
                Console.WriteLine($"  代码: {robotRotateSpeed.Code}");
                Console.WriteLine($"  值: {robotRotateSpeed.Value}");
                Console.WriteLine($"  描述: {robotRotateSpeed.Content}");
                Console.WriteLine($"  单位: {robotRotateSpeed.Unit}");
                Console.WriteLine($"  轴类型: {robotRotateSpeed.AxisType}");
                Console.WriteLine($"  范围: {robotRotateSpeed.Range}");
            }

            if (tAxisPosition != null)
            {
                Console.WriteLine("\nRP1 - T轴smooth到CHA位置详细信息:");
                Console.WriteLine($"  代码: {tAxisPosition.Code}");
                Console.WriteLine($"  值: {tAxisPosition.Value}");
                Console.WriteLine($"  描述: {tAxisPosition.Content}");
                Console.WriteLine($"  单位: {tAxisPosition.Unit}");
                Console.WriteLine($"  轴类型: {tAxisPosition.AxisType}");
                Console.WriteLine($"  范围: {tAxisPosition.Range}");
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static void RunAllExamples()
        {
            try
            {
                DemonstrateRobotConfigureAccessor();
                DemonstrateChamberConfigureAccessor();
                DemonstrateShuttleConfigureAccessor();
                DemonstrateMainSystemConfigureAccessor();
                DemonstrateConfigParameterDetails();
                
                Console.WriteLine("\n=== 配置访问器示例完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"示例运行出错: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }
    }
}
