using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Examples
{
    /// <summary>
    /// StopwatchHelper 性能分析使用示例
    /// 演示如何使用增强的 StopwatchHelper 进行性能监控和分析
    /// </summary>
    public class StopwatchHelperPerformanceExample
    {
        /// <summary>
        /// 示例1：基本使用方式 - 手动控制计时
        /// </summary>
        public void BasicUsageExample()
        {
            // 创建计时器，自动记录调用位置信息
            var stopwatch = new StopwatchHelper("数据库查询操作");
            
            // 开始计时（会自动记录开始日志）
            stopwatch.Start();
            
            // 模拟耗时操作
            SimulateWork(500);
            
            // 停止计时并获取结果（会自动记录结果日志）
            var elapsed = stopwatch.Stop();
            
            Console.WriteLine($"操作耗时: {elapsed.TotalMilliseconds:F2}ms");
        }

        /// <summary>
        /// 示例2：设置警告阈值 - 监控性能瓶颈
        /// </summary>
        public void WarningThresholdExample()
        {
            var stopwatch = new StopwatchHelper("文件处理操作");
            
            stopwatch.Start();
            
            // 模拟较长耗时操作
            SimulateWork(1500); // 超过默认1000ms阈值
            
            // 停止计时，设置警告阈值为800ms
            var elapsed = stopwatch.Stop(logResult: true, warnThresholdMs: 800);
            
            // 由于耗时超过800ms，会记录警告日志
        }

        /// <summary>
        /// 示例3：重启计时器 - 连续操作监控
        /// </summary>
        public void RestartExample()
        {
            var stopwatch = new StopwatchHelper("批量数据处理");
            
            stopwatch.Start();
            
            // 第一阶段：数据加载
            SimulateWork(300);
            var loadTime = stopwatch.Restart(); // 记录加载时间并重启
            
            // 第二阶段：数据处理
            SimulateWork(800);
            var processTime = stopwatch.Restart(); // 记录处理时间并重启
            
            // 第三阶段：数据保存
            SimulateWork(200);
            var saveTime = stopwatch.Stop(); // 记录保存时间
            
            Console.WriteLine($"加载: {loadTime.TotalMilliseconds:F2}ms");
            Console.WriteLine($"处理: {processTime.TotalMilliseconds:F2}ms");
            Console.WriteLine($"保存: {saveTime.TotalMilliseconds:F2}ms");
        }

        /// <summary>
        /// 示例4：静态方法 - 简化代码块计时
        /// </summary>
        public void StaticMeasureExample()
        {
            // 使用静态方法测量代码块执行时间
            var elapsed = StopwatchHelper.Measure(() =>
            {
                // 要测量的代码块
                SimulateWork(600);
                ProcessData();
            }, "复杂计算操作", warnThresholdMs: 500);
            
            // 计算耗时使用 Utility.GetHourMinutesInfo 方法，用户更加直观查看
            var readableTime = Utility.GetHourMinutesInfo(elapsed);
            Console.WriteLine($"代码块执行耗时: {elapsed.TotalMilliseconds:F2}ms ({readableTime})");
        }

        /// <summary>
        /// 示例5：异步操作计时
        /// </summary>
        public async Task AsyncMeasureExample()
        {
            // 测量异步操作执行时间
            var elapsed = await StopwatchHelper.MeasureAsync(async () =>
            {
                // 异步操作
                await SimulateAsyncWork(800);
                await ProcessDataAsync();
            }, "异步数据处理", warnThresholdMs: 1000);
            
            // 计算耗时使用 Utility.GetHourMinutesInfo 方法，用户更加直观查看
            var readableTime = Utility.GetHourMinutesInfo(elapsed);
            Console.WriteLine($"异步操作耗时: {elapsed.TotalMilliseconds:F2}ms ({readableTime})");
        }

        /// <summary>
        /// 示例6：性能对比分析
        /// </summary>
        public void PerformanceComparisonExample()
        {
            Console.WriteLine("=== 性能对比分析 ===");
            
            // 方法A性能测试
            var methodATime = StopwatchHelper.Measure(() =>
            {
                MethodA();
            }, "方法A性能测试");
            
            // 方法B性能测试
            var methodBTime = StopwatchHelper.Measure(() =>
            {
                MethodB();
            }, "方法B性能测试");
            
            // 性能对比
            var improvement = methodATime.TotalMilliseconds - methodBTime.TotalMilliseconds;
            Console.WriteLine($"方法A耗时: {methodATime.TotalMilliseconds:F2}ms");
            Console.WriteLine($"方法B耗时: {methodBTime.TotalMilliseconds:F2}ms");
            Console.WriteLine($"性能提升: {improvement:F2}ms ({improvement / methodATime.TotalMilliseconds * 100:F1}%)");
        }

        /// <summary>
        /// 示例7：循环操作性能监控
        /// </summary>
        public void LoopPerformanceExample()
        {
            var totalStopwatch = new StopwatchHelper("整体循环操作");
            totalStopwatch.Start();
            
            for (int i = 0; i < 10; i++)
            {
                // 每次循环都进行性能监控
                StopwatchHelper.Measure(() =>
                {
                    SimulateWork(100 + i * 50); // 模拟逐渐变慢的操作
                }, $"循环第{i + 1}次", warnThresholdMs: 200);
            }
            
            var totalTime = totalStopwatch.Stop();
            Console.WriteLine($"循环总耗时: {totalTime.TotalMilliseconds:F2}ms");
        }

        /// <summary>
        /// 示例8：异常情况下的性能监控
        /// </summary>
        public void ExceptionHandlingExample()
        {
            try
            {
                // 即使发生异常，也会记录执行时间
                StopwatchHelper.Measure(() =>
                {
                    SimulateWork(300);
                    throw new InvalidOperationException("模拟异常");
                }, "异常操作测试");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"捕获异常: {ex.Message}");
                // 异常信息和执行时间都会被记录到日志中
            }
        }

        #region 辅助方法

        /// <summary>
        /// 模拟同步工作
        /// </summary>
        /// <param name="milliseconds">模拟耗时（毫秒）</param>
        private void SimulateWork(int milliseconds)
        {
            System.Threading.Thread.Sleep(milliseconds);
        }

        /// <summary>
        /// 模拟异步工作
        /// </summary>
        /// <param name="milliseconds">模拟耗时（毫秒）</param>
        private async Task SimulateAsyncWork(int milliseconds)
        {
            await Task.Delay(milliseconds);
        }

        /// <summary>
        /// 模拟数据处理
        /// </summary>
        private void ProcessData()
        {
            // 模拟数据处理逻辑
            for (int i = 0; i < 1000000; i++)
            {
                var result = Math.Sqrt(i);
            }
        }

        /// <summary>
        /// 模拟异步数据处理
        /// </summary>
        private async Task ProcessDataAsync()
        {
            await Task.Run(() =>
            {
                ProcessData();
            });
        }

        /// <summary>
        /// 方法A - 性能对比用
        /// </summary>
        private void MethodA()
        {
            SimulateWork(400);
        }

        /// <summary>
        /// 方法B - 性能对比用
        /// </summary>
        private void MethodB()
        {
            SimulateWork(250);
        }

        #endregion

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public async Task RunAllExamples()
        {
            Console.WriteLine("=== StopwatchHelper 性能分析示例 ===\n");
            
            Console.WriteLine("1. 基本使用示例:");
            BasicUsageExample();
            
            Console.WriteLine("\n2. 警告阈值示例:");
            WarningThresholdExample();
            
            Console.WriteLine("\n3. 重启计时器示例:");
            RestartExample();
            
            Console.WriteLine("\n4. 静态方法示例:");
            StaticMeasureExample();
            
            Console.WriteLine("\n5. 异步操作示例:");
            await AsyncMeasureExample();
            
            Console.WriteLine("\n6. 性能对比示例:");
            PerformanceComparisonExample();
            
            Console.WriteLine("\n7. 循环性能示例:");
            LoopPerformanceExample();
            
            Console.WriteLine("\n8. 异常处理示例:");
            ExceptionHandlingExample();
            
            Console.WriteLine("\n=== 所有示例执行完成 ===");
            Console.WriteLine("请查看日志文件获取详细的性能分析数据:");
            Console.WriteLine("- Logs/perf.log - 性能专用日志");
            Console.WriteLine("- Logs/info.log - 一般信息日志");
            Console.WriteLine("- Logs/debug.log - 调试详细日志");
        }
    }
}
