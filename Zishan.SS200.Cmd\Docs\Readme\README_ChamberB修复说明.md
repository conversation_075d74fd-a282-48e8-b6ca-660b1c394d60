# ChamberB状态表格数据修复说明

## 问题描述

在Robot状态面板的实时状态表格中，选择ChamberB设备类型时没有对应的数据显示，只能看到ChamberA的数据。

## 问题原因

1. **缺少ChamberB状态对象**: `RobotStatusPanelViewModel`中只有一个`ChamberSubsystemStatus`对象，用于ChamberA，没有单独的ChamberB状态对象。

2. **状态更新逻辑不完整**: `UpdateChamberSubsystemStatus`方法只更新ChamberA的状态，没有更新ChamberB。

3. **表格数据源不完整**: `UpdateStatusPropertiesCore`方法中只添加了ChamberA的状态属性，没有添加ChamberB。

## 修复方案

### 1. 改进命名规范并添加ChamberB状态对象

为了更好地区分ChamberA和ChamberB，改进了命名规范：

```csharp
/// <summary>
/// ChamberA子系统状态
/// </summary>
[ObservableProperty]
private ChamberSubsystemStatus _chamberASubsystemStatus;

/// <summary>
/// ChamberB子系统状态
/// </summary>
[ObservableProperty]
private ChamberSubsystemStatus _chamberBSubsystemStatus;
```

### 2. 更新构造函数

修改构造函数以接受ChamberA和ChamberB状态参数，并使用更清晰的命名：

```csharp
public RobotStatusPanelViewModel(IS200McuCmdService mcuCmdService,
    RobotSubsystemStatus robotSubsystemStatus,
    ChamberSubsystemStatus chamberASubsystemStatus,  // 重命名
    ChamberSubsystemStatus chamberBSubsystemStatus,  // 新增
    ShuttleSubsystemStatus shuttleSubsystemStatus)
{
    ChamberASubsystemStatus = chamberASubsystemStatus ?? throw new ArgumentNullException(nameof(chamberASubsystemStatus));
    ChamberBSubsystemStatus = chamberBSubsystemStatus ?? throw new ArgumentNullException(nameof(chamberBSubsystemStatus));
}
```

### 3. 重构状态更新逻辑

创建了`UpdateSingleChamberStatus`方法来统一处理单个Chamber的状态更新：

```csharp
private void UpdateSingleChamberStatus(EnuMcuDeviceType deviceType, ChamberSubsystemStatus chamberStatus)
{
    // 1. 触发状态 (MPS1-MPS2)
    chamberStatus.TriggerStatus = _coilStatusHelper.CalculateTriggerStatus(deviceType);
    
    // 2. 运行状态 (MPS3A-MPS5)
    chamberStatus.RunStatus = _coilStatusHelper.CalculateRunStatus(deviceType);
    
    // ... 其他状态更新
}
```

修改`UpdateChamberSubsystemStatus`方法同时更新ChamberA和ChamberB：

```csharp
private void UpdateChamberSubsystemStatus(bool blAutoInvoke = true)
{
    // 更新ChamberA状态
    UpdateSingleChamberStatus(EnuMcuDeviceType.ChamberA, ChamberASubsystemStatus);

    // 更新ChamberB状态
    UpdateSingleChamberStatus(EnuMcuDeviceType.ChamberB, ChamberBSubsystemStatus);
}
```

### 4. 更新状态表格数据源

修改`UpdateStatusPropertiesCore`方法以包含ChamberB的状态：

```csharp
private void UpdateStatusPropertiesCore()
{
    StatusProperties.Clear();

    // 添加Robot子系统状态
    AddSubsystemProperties(RobotSubsystemStatus, EnuMcuDeviceType.Robot, "Robot");

    // 添加ChamberA子系统状态
    AddSubsystemProperties(ChamberASubsystemStatus, EnuMcuDeviceType.ChamberA, "ChamberA");

    // 添加ChamberB子系统状态 (新增)
    AddSubsystemProperties(ChamberBSubsystemStatus, EnuMcuDeviceType.ChamberB, "ChamberB");

    // 添加Shuttle子系统状态
    AddSubsystemProperties(ShuttleSubsystemStatus, EnuMcuDeviceType.Shuttle, "Shuttle");
}
```

### 5. 添加测试数据

在测试命令中为ChamberB添加了不同的测试数据以便区分：

```csharp
// ChamberB解析状态测试命令
ChamberBSubsystemStatus.TriggerStatus = EnuTriggerStatus.Alarm;
ChamberBSubsystemStatus.RunStatus = EnuRunStatus.Processing;
ChamberBSubsystemStatus.SlitDoorStatus = EnuSlitDoorStatus.Close;
ChamberBSubsystemStatus.C1ValveStatus = EnuValveStatus.Close;
ChamberBSubsystemStatus.C2ValveStatus = EnuValveStatus.Open;
ChamberBSubsystemStatus.C3ValveStatus = EnuValveStatus.Open;
ChamberBSubsystemStatus.C4ValveStatus = EnuValveStatus.BetweenOpenClose;
```

## 修复效果

修复后，在Robot状态面板的实时状态表格中：

1. **设备类型过滤器**现在可以正确显示ChamberB的数据
2. **ChamberA和ChamberB**有独立的状态对象和数据
3. **状态更新**会同时更新两个Chamber的状态
4. **测试数据**显示不同的状态值以便区分

## 验证方法

1. 运行应用程序
2. 打开Robot状态面板
3. 在设备类型下拉框中选择"ChamberB"
4. 确认表格中显示ChamberB相关的状态数据
5. 切换到"ChamberA"确认显示不同的数据
6. 选择"全部设备"确认同时显示两个Chamber的数据

## 命名规范改进

为了更好地区分ChamberA和ChamberB，统一了命名规范：

- **属性命名**: `ChamberASubsystemStatus` 和 `ChamberBSubsystemStatus`
- **参数命名**: `chamberASubsystemStatus` 和 `chamberBSubsystemStatus`
- **日志消息**: 明确标识ChamberA和ChamberB
- **示例代码**: 更新了所有示例以使用新的命名规范

## 注意事项

- ChamberA和ChamberB使用相同的`ChamberSubsystemStatus`类型，但是独立的实例
- 状态更新逻辑会根据设备类型（`EnuMcuDeviceType.ChamberA`或`EnuMcuDeviceType.ChamberB`）调用相应的CoilStatusHelper方法
- 测试数据中ChamberB使用了不同的状态值以便在UI中区分
- 所有相关的示例代码和文档都已更新以反映新的命名规范
