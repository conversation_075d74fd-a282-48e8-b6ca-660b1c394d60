using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// R轴归零修复使用示例
    /// 展示如何使用修复后的R轴归零功能
    /// </summary>
    public class RAxisZeroFixUsageExample
    {
        private readonly IS200McuCmdService _cmdService;
        private readonly ILogger _logger;

        public RAxisZeroFixUsageExample(IS200McuCmdService cmdService)
        {
            _cmdService = cmdService;
            _logger = LogManager.GetCurrentClassLogger();
        }

        /// <summary>
        /// 示例1: 基本R轴归零使用
        /// </summary>
        public async Task Example1_BasicRAxisZeroAsync()
        {
            _logger.Info("=== 示例1: 基本R轴归零使用 ===");

            try
            {
                // 使用修复后的R轴归零方法
                // 现在会自动等待移动完成并验证位置
                var result = await _cmdService.ZeroRAxisAsync();

                if (result.Success)
                {
                    _logger.Info($"✅ R轴归零成功: {result.Message}");
                    
                    // 现在可以安全地进行后续操作，确保R轴真正在零位
                    await PerformSubsequentOperations();
                }
                else
                {
                    _logger.Error($"❌ R轴归零失败: {result.Message}");
                    // 处理失败情况
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"R轴归零异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 示例2: 自定义R轴移动参数
        /// </summary>
        public async Task Example2_CustomRAxisMovementAsync()
        {
            _logger.Info("=== 示例2: 自定义R轴移动参数 ===");

            try
            {
                // 获取R轴零位配置
                var interLock = SS200InterLockMain.Instance;
                int rAxisZeroPosition = interLock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition.Value;

                // 使用增强版移动方法，自定义参数
                var result = await _cmdService.MoveRAxisToPositionAsync(
                    positionValue: rAxisZeroPosition,
                    waitForCompletion: true,    // 等待移动完成
                    verifyPosition: true,       // 验证最终位置
                    timeoutMs: 20000           // 20秒超时
                );

                if (result.Success)
                {
                    _logger.Info($"✅ R轴移动成功: {result.Message}");
                }
                else
                {
                    _logger.Error($"❌ R轴移动失败: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"R轴移动异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 示例3: 快速移动模式（不等待完成）
        /// </summary>
        public async Task Example3_QuickMovementModeAsync()
        {
            _logger.Info("=== 示例3: 快速移动模式 ===");

            try
            {
                var interLock = SS200InterLockMain.Instance;
                int rAxisZeroPosition = interLock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition.Value;

                // 快速移动模式：只发送命令，不等待完成
                var result = await _cmdService.MoveRAxisToPositionAsync(
                    positionValue: rAxisZeroPosition,
                    waitForCompletion: false,   // 不等待移动完成
                    verifyPosition: false       // 不验证位置
                );

                if (result.Success)
                {
                    _logger.Info($"✅ R轴移动命令发送成功: {result.Message}");
                    
                    // 如果使用快速模式，需要手动等待和验证
                    _logger.Info("手动等待R轴移动完成...");
                    await Task.Delay(5000); // 等待5秒
                    
                    // 手动验证位置
                    bool isAtZero = await ManuallyVerifyRAxisZeroPosition();
                    _logger.Info($"手动验证结果: {(isAtZero ? "在零位" : "不在零位")}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"快速移动异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 示例4: 错误处理和重试机制
        /// </summary>
        public async Task Example4_ErrorHandlingAndRetryAsync()
        {
            _logger.Info("=== 示例4: 错误处理和重试机制 ===");

            const int maxRetries = 3;
            int retryCount = 0;

            while (retryCount < maxRetries)
            {
                try
                {
                    _logger.Info($"尝试R轴归零 (第 {retryCount + 1}/{maxRetries} 次)");

                    var result = await _cmdService.ZeroRAxisAsync();

                    if (result.Success)
                    {
                        _logger.Info($"✅ R轴归零成功 (第 {retryCount + 1} 次尝试)");
                        
                        // 额外验证确保状态一致性
                        bool statusConsistent = await VerifyStatusConsistency();
                        if (statusConsistent)
                        {
                            _logger.Info("✅ 状态一致性验证通过");
                            return; // 成功退出
                        }
                        else
                        {
                            _logger.Warning("⚠️ 状态一致性验证失败，将重试");
                        }
                    }
                    else
                    {
                        _logger.Error($"❌ R轴归零失败: {result.Message}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"R轴归零异常: {ex.Message}", ex);
                }

                retryCount++;
                
                if (retryCount < maxRetries)
                {
                    _logger.Info($"等待 2 秒后重试...");
                    await Task.Delay(2000);
                }
            }

            _logger.Error($"❌ R轴归零在 {maxRetries} 次尝试后仍然失败");
        }

        /// <summary>
        /// 示例5: 状态监控和诊断
        /// </summary>
        public async Task Example5_StatusMonitoringAndDiagnosticsAsync()
        {
            _logger.Info("=== 示例5: 状态监控和诊断 ===");

            try
            {
                var interLock = SS200InterLockMain.Instance;

                // 记录归零前状态
                var beforeStatus = await GetDetailedRAxisStatus();
                _logger.Info($"归零前状态: {beforeStatus}");

                // 执行R轴归零
                var result = await _cmdService.ZeroRAxisAsync();

                // 记录归零后状态
                var afterStatus = await GetDetailedRAxisStatus();
                _logger.Info($"归零后状态: {afterStatus}");

                // 状态对比分析
                await AnalyzeStatusChanges(beforeStatus, afterStatus);

                if (result.Success)
                {
                    _logger.Info("✅ R轴归零完成，状态分析已记录");
                }
                else
                {
                    _logger.Error($"❌ R轴归零失败，请查看状态分析: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"状态监控异常: {ex.Message}", ex);
            }
        }

        #region 辅助方法

        /// <summary>
        /// 执行后续操作（示例）
        /// </summary>
        private async Task PerformSubsequentOperations()
        {
            _logger.Info("执行后续操作...");
            
            // 现在可以安全地执行需要R轴在零位的操作
            // 例如：T轴旋转、Z轴移动等
            
            await Task.Delay(100); // 模拟操作
            _logger.Info("后续操作完成");
        }

        /// <summary>
        /// 手动验证R轴零位位置
        /// </summary>
        private async Task<bool> ManuallyVerifyRAxisZeroPosition()
        {
            await Task.Delay(100); // 确保状态更新

            var interLock = SS200InterLockMain.Instance;
            var currentPosition = interLock.RTZAxisPosition.GetCurrentRTZSteps();
            var robotStatus = interLock.SubsystemStatus.Robot.Status;
            int zeroPosition = interLock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition.Value;

            bool physicallyAtZero = Math.Abs(currentPosition.RAxisStep - zeroPosition) <= 0;
            bool statusAtZero = robotStatus.RAxisIsZeroPosition;

            _logger.Info($"位置验证: 当前位置 {currentPosition.RAxisStep}, 零位配置 {zeroPosition}");
            _logger.Info($"物理上在零位: {physicallyAtZero}, 状态表显示: {statusAtZero}");

            return physicallyAtZero && statusAtZero;
        }

        /// <summary>
        /// 验证状态一致性
        /// </summary>
        private async Task<bool> VerifyStatusConsistency()
        {
            return await ManuallyVerifyRAxisZeroPosition();
        }

        /// <summary>
        /// 获取详细的R轴状态
        /// </summary>
        private async Task<string> GetDetailedRAxisStatus()
        {
            await Task.Delay(100);

            var interLock = SS200InterLockMain.Instance;
            var currentPosition = interLock.RTZAxisPosition.GetCurrentRTZSteps();
            var robotStatus = interLock.SubsystemStatus.Robot.Status;
            int zeroPosition = interLock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition.Value;

            return $"位置: {currentPosition.RAxisStep}, 零位配置: {zeroPosition}, " +
                   $"状态表零位: {robotStatus.RAxisIsZeroPosition}, " +
                   $"差值: {Math.Abs(currentPosition.RAxisStep - zeroPosition)}";
        }

        /// <summary>
        /// 分析状态变化
        /// </summary>
        private async Task AnalyzeStatusChanges(string beforeStatus, string afterStatus)
        {
            _logger.Info("=== 状态变化分析 ===");
            _logger.Info($"变化前: {beforeStatus}");
            _logger.Info($"变化后: {afterStatus}");
            
            // 这里可以添加更复杂的状态分析逻辑
            await Task.Delay(10);
        }

        #endregion

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public async Task RunAllExamplesAsync()
        {
            _logger.Info("开始运行R轴归零修复使用示例");

            await Example1_BasicRAxisZeroAsync();
            await Task.Delay(1000);

            await Example2_CustomRAxisMovementAsync();
            await Task.Delay(1000);

            await Example3_QuickMovementModeAsync();
            await Task.Delay(1000);

            await Example4_ErrorHandlingAndRetryAsync();
            await Task.Delay(1000);

            await Example5_StatusMonitoringAndDiagnosticsAsync();

            _logger.Info("所有示例运行完成");
        }
    }
}
