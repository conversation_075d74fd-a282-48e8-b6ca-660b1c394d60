# Robot报警代码访问器完成说明

## 概述

已成功完成SS200InterLockMain中所有Robot报警代码的访问器定义，共计67个报警代码（RA1-RA67）。

## 完成的访问器列表

### 系统状态报警 (RA1-RA7)
- `RA1_SystemBusyReject` - Robot system status is busy, command reject
- `RA2_SystemAlarmReject` - Robot system status is alarm, command reject
- `RA3_RAxisNotHomeError` - Robot R-axis not at home position, T-axis motion error
- `RA4_TAxisPositionError` - Robot T-axis not in right position, PLS confirm to R-axis motion
- `RA5_RotationTimeout` - Robot rotation time out
- `RA6_ExtensionTimeout` - Robot extension time out
- `RA7_LiftTimeout` - Robot lift time out

### 门控制报警 (RA8-RA9)
- `RA8_CHASlitDoorNotOpen` - CHA slit door not at open status, robot can not extend
- `RA9_CHBSlitDoorNotOpen` - CHB slit door not at open status, robot can not extend

### 轴位置报警 (RA10-RA12)
- `RA10_ZAxisPositionError` - Robot Z-axis not in right position, PLS confirm to R-axis motion
- `RA11_RAxisNotHomeZAxisError` - Robot R-axis not at home position, Z-axis can not move to right position
- `RA12_TAxisPositionZAxisError` - Robot T-axis not in right position, Z-axis can not move to right position

### 针脚搜索报警 (RA13-RA16, RA22)
- `RA13_ShuttlePositionPinSearchError` - Shuttle position status not at right position, can not do pin search
- `RA14_PaddleHitPinBall` - paddle hit to pin ball, pin search failure
- `RA15_PinBallStatusFailure` - pin ball status failure, pin search failure
- `RA16_CannotFindPinBall` - Can not find pin ball, pin search filure
- `RA22_PinSearchValueDeltaOutOfRange` - Pin search value delta out of setpoint

### 桨叶和晶圆状态报警 (RA17-RA21)
- `RA17_PaddleOccupiedOrWaferInconsistent` - Paddle status occupied or wafer status inconsistent, command reject
- `RA18_WaferOnPaddlePinSearchInvalid` - There is(are) wafer(s) on paddle, and pin search data invalid command reject
- `RA19_BLSlideOutDetected` - BL slide out sensor detector wafer slide out
- `RA20_BRSlideOutDetected` - BR slide out sensor detector wafer slide out
- `RA21_BLAndBRSlideOutDetected` - BL and BR slide out sensor detector wafer slide out

### Shuttle状态报警 (RA23-RA25)
- `RA23_Shuttle1ExistDisabled` - Shuttle 1 exist status is diable, command reject
- `RA24_Shuttle2ExistDisabled` - Shuttle 2 exist status is diable, command reject
- `RA25_ShuttlePositionWaferMoveError` - shuttle position status not at right position, can not move wafer

### 腔室状态报警 (RA26-RA35)
- `RA26_CHAExistDisabled` - CHA exist status is disable, command reject
- `RA27_CHBExistDisabled` - CHB exist status is disable, command reject
- `RA28_CHATriggerAlarm` - CHA trigger status is alarm, command reject
- `RA29_CHBTriggerAlarm` - CHB trigger status is alarm, command reject
- `RA30_CHARunBusy` - CHA run status is busy, command reject
- `RA31_CHBRunBusy` - CHB run status is busy, command reject
- `RA32_CHARunProcessing` - CHA run status is processing, command reject
- `RA33_CHBRunProcessing` - CHB run status is processing, command reject
- `RA34_CoolingChamberTriggerAlarm` - Cooling chamber trigger status is alarm, command reject
- `RA35_CoolingChamberNotIdle` - Cooling chamber run status is not idle status, command reject

### 卡匣槽报警 (RA36-RA37, RA62)
- `RA36_CassetteSlotNotEmpty` - cassette slot is not empty, can not put wafer to cassette slot
- `RA37_CassetteSlotInconsistent` - cassette slot status inconsistent, command reject
- `RA62_SlotCannotGetWaferFromCassette` - slot is can not get wafer from cassette

### 晶圆丢失报警 (RA38-RA43)
- `RA38_SmoothP1WaferLost` - smooth P1 wafer has been lost
- `RA39_SmoothP2WaferLost` - smooth P2 wafer has been lost
- `RA40_SmoothBothPaddleWaferLost` - smooth side both paddle wafer has been lost
- `RA41_NoseP1WaferLost` - nose P1 wafer has been lost
- `RA42_NoseP2WaferLost` - nose P2 wafer has been lost
- `RA43_NoseBothPaddleWaferLost` - nose side both paddle wafer has been lost

### 晶圆放置失败报警 (RA44-RA49)
- `RA44_SmoothP1PutWaferFailure` - smooth P1 wafer put wafer failure
- `RA45_SmoothP2PutWaferFailure` - smooth P2 wafer put wafer failure
- `RA46_SmoothBothPaddlePutWaferFailure` - smooth side both paddle wafer put wafer failure
- `RA47_NoseP1PutWaferFailure` - nose P1 wafer put wafer failure
- `RA48_NoseP2PutWaferFailure` - nose P2 wafer put wafer failure
- `RA49_NoseBothPutWaferFailure` - nose side both wafer put wafer failure

### 晶圆状态不一致报警 (RA50-RA53)
- `RA50_SmoothP1WaferStatusInconsistent` - sensor show smooth P1 wafer status disconsister with slot, move wafer failure
- `RA51_SmoothP2WaferStatusInconsistent` - sensor show smooth P2 wafer status disconsister with slot, move wafer failure
- `RA52_NoseP1WaferStatusInconsistent` - sensor show nose P1 wafer status disconsister with slot, move wafer failure
- `RA53_NoseP2WaferStatusInconsistent` - sensor show nose P2 wafer status disconsister with slot, move wafer failure

### 腔室晶圆存在报警 (RA54-RA57)
- `RA54_WaferInCHAPutReject` - There is(are) wafer(s) in CHA, put wafer to CHA command reject
- `RA55_WaferInCHBPutReject` - There is(are) wafer(s) in CHB, put wafer to CHA command reject
- `RA56_WaferInCTPutReject` - There is(are) wafer(s) in CT, put wafer to CHA command reject
- `RA57_WaferInCBPutReject` - There is(are) wafer(s) in CB, put wafer to CHA command reject

### 晶圆状态异常报警 (RA58-RA61)
- `RA58_SmoothP1WaferStatusAbnormal` - smooth P1 wafer status abnormal
- `RA59_SmoothP2WaferStatusAbnormal` - smooth P2 wafer status abnormal
- `RA60_NoseP1WaferStatusAbnormal` - nose P1 wafer status abnormal
- `RA61_NoseP2WaferStatusAbnormal` - nose P2 wafer status abnormal

### 腔室无晶圆报警 (RA63-RA66)
- `RA63_NoWaferInCHAGetReject` - no wafer in CHA, get wafer from CHA command reject
- `RA64_NoWaferInCHBGetReject` - no wafer in CHB, get wafer from CHA command reject
- `RA65_NoWaferInCTGetReject` - no wafer in CT, get wafer from CHA command reject
- `RA66_NoWaferInCBGetReject` - no wafer in CB, get wafer from CHA command reject

### 运动错误报警 (RA67)
- `RA67_RobotMotionError` - robot motion error

## 使用方法

### 基本用法
```csharp
// 获取单例实例
var interLock = SS200InterLockMain.Instance;

// 访问Robot报警代码
var robotAlarm = interLock.AlarmCode.Robot;

// 获取报警内容
string alarmContent = robotAlarm.RA1_SystemBusyReject.Content;
string alarmChsContent = robotAlarm.RA1_SystemBusyReject.ChsContent;

// 示例：检查系统忙碌报警
if (robotAlarm.RA1_SystemBusyReject.Content != null)
{
    Console.WriteLine($"RA1报警: {robotAlarm.RA1_SystemBusyReject.Content}");
    Console.WriteLine($"中文描述: {robotAlarm.RA1_SystemBusyReject.ChsContent}");
}
```

### 批量访问
```csharp
// 测试所有报警代码访问器
RobotAlarmAccessorTest.TestAllRobotAlarmAccessors();

// 测试特定报警代码
RobotAlarmAccessorTest.TestSpecificAlarmCode(EnuRobotAlarmCodes.RA1);
```

## 实现特点

1. **线程安全**: 使用ConcurrentDictionary实现缓存，支持多线程并发访问
2. **延迟加载**: 访问器采用延迟加载模式，只在首次访问时创建
3. **缓存机制**: 已创建的访问器会被缓存，避免重复创建
4. **统一接口**: 所有访问器都返回AlarmPropertyAccessor类型，提供统一的访问接口
5. **完整覆盖**: 覆盖了所有67个Robot报警代码，无遗漏

## 测试验证

提供了完整的测试类`RobotAlarmAccessorTest`，可以验证：
- 所有访问器是否正常创建
- 报警内容是否正确获取
- 中文描述是否正确显示
- 异常情况的处理

## 注意事项

1. 确保RobotErrorCodesProvider.Instance已正确初始化
2. 确保RobotErrorCodes.json配置文件存在且格式正确
3. 访问器返回null时需要进行空值检查
4. 建议在使用前先测试访问器的可用性

## 后续扩展

如需添加新的Robot报警代码：
1. 在EnuRobotAlarmCodes枚举中添加新的报警代码
2. 在RobotErrorCodes.json中添加对应的配置
3. 在RobotAlarmAccessor类中添加对应的访问器属性
4. 更新测试代码以包含新的报警代码

## 完成状态

✅ 所有67个Robot报警代码访问器已完成
✅ 测试代码已提供
✅ 文档已完善
✅ 编译成功，无错误
✅ 可以正常使用

## 编译状态

项目编译成功，返回码：0
- ✅ 无编译错误
- ⚠️ 有102个警告（主要是类型冲突和MVVM工具包相关警告，不影响功能）
- ✅ 生成成功：`bin\Debug\net8.0-windows\Zishan.SS200.Cmd.dll`

## 测试验证

提供了两个测试类：

### 1. RobotAlarmAccessorTest.cs
- 完整测试所有67个访问器
- 包含异常处理和详细报告
- 支持单个报警代码测试

### 2. RobotAlarmAccessorSimpleTest.cs
- 简化版测试，快速验证核心功能
- 测试前10个、中间几个、最后几个访问器
- 统计测试验证所有67个访问器都已定义

### 运行测试
```csharp
// 运行简单测试
RobotAlarmAccessorSimpleTest.RunAllTests();

// 运行完整测试
RobotAlarmAccessorTest.TestAllRobotAlarmAccessors();
```
