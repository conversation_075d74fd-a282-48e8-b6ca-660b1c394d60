# 循环次数递减功能说明

## 概述

改进了公共循环次数功能，使其在执行后能够递减，直到0结束。用户可以实时看到剩余的循环次数，提供更好的执行进度反馈。

## 功能特性

### 1. 循环次数设置
- **默认值**: 1（执行一次）
- **无限循环**: -1（保持不变）
- **有限循环**: 任何正整数（如：5表示执行5次，会递减到0）

### 2. 递减逻辑
- **-1（无限循环）**: 剩余次数始终显示为"无限循环"，不会递减
- **正整数**: 每次执行完成后递减1，直到0结束
- **0**: 显示为"已完成"，循环停止

### 3. UI显示改进
- **输入框**: 用于设置初始循环次数
- **剩余次数显示**: 实时显示当前剩余的循环次数
- **状态指示**: 通过颜色和文本清晰显示循环状态

## 使用示例

### 示例1：执行5次循环
1. 在"公共循环次数"输入框中输入：`5`
2. 剩余次数显示：`剩余: 5次`
3. 点击"Pin Search测试"或"搬运"按钮
4. 执行过程中的显示变化：
   - 第1次执行后：`剩余: 4次`
   - 第2次执行后：`剩余: 3次`
   - 第3次执行后：`剩余: 2次`
   - 第4次执行后：`剩余: 1次`
   - 第5次执行后：`已完成`

### 示例2：无限循环
1. 在"公共循环次数"输入框中输入：`-1`
2. 剩余次数显示：`无限循环`
3. 点击执行按钮
4. 执行过程中始终显示：`无限循环`
5. 需要手动点击"公共停止循环"按钮停止

### 示例3：执行1次
1. 在"公共循环次数"输入框中输入：`1`（默认值）
2. 剩余次数显示：`剩余: 1次`
3. 点击执行按钮
4. 执行完成后显示：`已完成`

## 日志记录改进

### 循环进度显示
```
=== 第1次 (剩余5次) PinSearch 测试开始 ===
PinSearch 测试完成
剩余循环次数: 4

=== 第2次 (剩余4次) PinSearch 测试开始 ===
PinSearch 测试完成
剩余循环次数: 3
```

### 无限循环显示
```
=== 第1次 PinSearch 测试开始 ===
PinSearch 测试完成

=== 第2次 PinSearch 测试开始 ===
PinSearch 测试完成
```

## 技术实现

### 1. 新增属性
```csharp
/// <summary>
/// 剩余循环次数：-1代表无限循环，0表示循环结束
/// </summary>
[ObservableProperty]
private int remainingLoopCount = 1;
```

### 2. 循环控制逻辑
```csharp
// 初始化剩余循环次数
RemainingLoopCount = LoopCount;

// 循环执行 - 使用剩余循环次数控制
while (RemainingLoopCount != 0 && !cancellationToken.IsCancellationRequested)
{
    // 执行具体操作...
    
    // 递减剩余循环次数（无限循环时保持-1）
    if (!isInfiniteLoop && RemainingLoopCount > 0)
    {
        RemainingLoopCount--;
    }
}
```

### 3. UI转换器
```csharp
public class LoopCountDisplayConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int loopCount)
        {
            return loopCount switch
            {
                -1 => "无限循环",
                0 => "已完成",
                _ => $"剩余: {loopCount}次"
            };
        }
        return "未知";
    }
}
```

## 优势

1. **实时反馈**: 用户可以实时看到剩余的循环次数
2. **进度可视化**: 通过递减显示，用户能够清楚了解执行进度
3. **状态明确**: 区分"进行中"、"已完成"和"无限循环"三种状态
4. **向后兼容**: 保持原有的-1无限循环功能不变
5. **用户友好**: 提供直观的UI反馈和清晰的状态指示

## 注意事项

1. **-1特殊处理**: 无限循环时RemainingLoopCount始终保持-1
2. **0停止条件**: 当RemainingLoopCount为0时，循环自动停止
3. **手动停止**: 用户可以随时通过"公共停止循环"按钮中断执行
4. **状态同步**: UI显示与内部逻辑保持同步更新
