using System;
using System.Linq;
using <PERSON><PERSON><PERSON>.SS200.Cmd.Enums.SS200.IOInterface.Chamber;
using <PERSON><PERSON>an.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using <PERSON><PERSON>an.SS200.Cmd.Enums.SS200.IOInterface.Shuttle;

namespace Zishan.SS200.Cmd.Extensions
{
    /// <summary>
    /// IO枚举扩展方法，用于提取IO代码和类型信息
    /// </summary>
    public static class IOEnumExtensions
    {
        /// <summary>
        /// 从枚举名称中提取IO代码
        /// </summary>
        /// <param name="enumValue">枚举值</param>
        /// <returns>IO代码字符串，如SDI1、PDI12、RDI1等</returns>
        public static string GetIOCode(this Enum enumValue)
        {
            if (enumValue == null)
                return string.Empty;

            var enumName = enumValue.ToString();

            // 查找第一个下划线的位置
            var underscoreIndex = enumName.IndexOf('_');

            if (underscoreIndex > 0)
            {
                // 返回下划线前的部分作为IO代码
                return enumName.Substring(0, underscoreIndex);
            }

            // 如果没有下划线，返回整个枚举名称
            return enumName;
        }

        /// <summary>
        /// 判断枚举是否为数字输入(DI)类型
        /// </summary>
        /// <param name="enumValue">枚举值</param>
        /// <returns>是否为DI类型</returns>
        public static bool IsDigitalInput(this Enum enumValue)
        {
            return enumValue is EnuShuttleDICodes or EnuChamberDICodes or EnuRobotDICodes;
        }

        /// <summary>
        /// 判断枚举是否为数字输出(DO)类型
        /// </summary>
        /// <param name="enumValue">枚举值</param>
        /// <returns>是否为DO类型</returns>
        public static bool IsDigitalOutput(this Enum enumValue)
        {
            return enumValue is EnuShuttleDOCodes or EnuChamberDOCodes;
        }

        /// <summary>
        /// 获取IO类型字符串
        /// </summary>
        /// <param name="enumValue">枚举值</param>
        /// <returns>"DI"或"DO"</returns>
        public static string GetIOType(this Enum enumValue)
        {
            if (enumValue.IsDigitalInput())
                return "DI";
            else if (enumValue.IsDigitalOutput())
                return "DO";
            else
                return "Unknown";
        }

        #region Shuttle枚举扩展

        /// <summary>
        /// 获取Shuttle DI枚举的IO代码
        /// </summary>
        public static string GetIOCode(this EnuShuttleDICodes diCode)
        {
            return ((Enum)diCode).GetIOCode();
        }

        /// <summary>
        /// 获取Shuttle DO枚举的IO代码
        /// </summary>
        public static string GetIOCode(this EnuShuttleDOCodes doCode)
        {
            return ((Enum)doCode).GetIOCode();
        }

        #endregion

        #region Chamber枚举扩展

        /// <summary>
        /// 获取Chamber DI枚举的IO代码
        /// </summary>
        public static string GetIOCode(this EnuChamberDICodes diCode)
        {
            return ((Enum)diCode).GetIOCode();
        }

        /// <summary>
        /// 获取Chamber DO枚举的IO代码
        /// </summary>
        public static string GetIOCode(this EnuChamberDOCodes doCode)
        {
            return ((Enum)doCode).GetIOCode();
        }

        #endregion

        #region Robot枚举扩展

        /// <summary>
        /// 获取Robot DI枚举的IO代码
        /// </summary>
        public static string GetIOCode(this EnuRobotDICodes diCode)
        {
            return ((Enum)diCode).GetIOCode();
        }

        #endregion

        #region 验证方法

        /// <summary>
        /// 验证枚举值是否有效
        /// </summary>
        /// <param name="enumValue">枚举值</param>
        /// <returns>是否为有效的IO枚举</returns>
        public static bool IsValidIOEnum(this Enum enumValue)
        {
            return enumValue.IsDigitalInput() || enumValue.IsDigitalOutput();
        }



        #endregion
    }
}
