# 机器人状态输出示例

## 修改前的输出格式

```
RobotStatus = Alarm
TAxisSmoothDestination = Cassette
TAxisNoseDestination = None
TAxisIsZero = True
EnuRAxisSmoothExtendDestination = None
EnuRAxisNoseExtendDestination = None
RAxisIsZero = True
ZAxisIsZero = True
EnuTAndRAxisPositionStatus = TAndRAxisNone
EnuTAndZAxisHeightStatus = TAndZAxisNone
```

## 修改后的输出格式

```
RobotStatus = Alarm
TAxisSmoothDestination = Cassette
TAxisNoseDestination = None
TAxisIsZeroPosition = True
EnuRAxisSmoothExtendDestination = None
EnuRAxisNoseExtendDestination = None
RAxisIsZeroPosition = True
ZAxisIsZeroPosition = True
EnuTAndRAxisPositionStatus = TAndRAxisNone
EnuTAndZAxisHeightStatus = TAndZAxisNone
```

## 主要变化

### 1. 属性名称统一化

**修改前：**
- `TAxisIsZero`
- `RAxisIsZero` 
- `ZAxisIsZero`

**修改后：**
- `TAxisIsZeroPosition`
- `RAxisIsZeroPosition`
- `ZAxisIsZeroPosition`

### 2. 命名规范说明

所有轴的零位置属性都统一使用 `Position` 后缀，使命名更加一致和清晰：

- **T轴零位置**：`TAxisIsZeroPosition` - 表示T轴是否在零位置
- **R轴零位置**：`RAxisIsZeroPosition` - 表示R轴是否在零位置  
- **Z轴零位置**：`ZAxisIsZeroPosition` - 表示Z轴是否在零位置

### 3. 使用示例

```csharp
// 检查所有轴是否都在零位置
if (robotStatus.TAxisIsZeroPosition && 
    robotStatus.RAxisIsZeroPosition && 
    robotStatus.ZAxisIsZeroPosition)
{
    Console.WriteLine("所有轴都在零位置");
}

// 检查特定轴的零位置状态
if (robotStatus.TAxisIsZeroPosition)
{
    Console.WriteLine("T轴在零位置");
}
```

### 4. 日志输出示例

```
2024-01-15 10:30:25 [INFO] Robot状态更新:
  - TAxisIsZeroPosition: True
  - RAxisIsZeroPosition: True  
  - ZAxisIsZeroPosition: False
  - 当前位置状态: TAndRAxisNone
  - 当前高度状态: TAndZAxisNone
```

### 5. UI显示效果

在状态面板中的显示：

```
┌─────────────────────────────────┐
│ Robot轴位置状态                  │
├─────────────────────────────────┤
│ T轴零位置: ✓ 是                  │
│ R轴零位置: ✓ 是                  │
│ Z轴零位置: ✗ 否                  │
│                                 │
│ T&R轴位置: 无定位                │
│ T&Z轴高度: 无定位                │
└─────────────────────────────────┘
```

## 代码实现对比

### 修改前的实现

```csharp
public class RobotSubsystemStatus
{
    public bool TAxisIsZero { get; set; }
    public bool RAxisIsZero { get; set; }
    public bool ZAxisIsZero { get; set; }
    
    public override string ToString()
    {
        return $"TAxisIsZero = {TAxisIsZero}\n" +
               $"RAxisIsZero = {RAxisIsZero}\n" +
               $"ZAxisIsZero = {ZAxisIsZero}";
    }
}
```

### 修改后的实现

```csharp
public class RobotSubsystemStatus
{
    public bool TAxisIsZeroPosition { get; set; }
    public bool RAxisIsZeroPosition { get; set; }
    public bool ZAxisIsZeroPosition { get; set; }
    
    public override string ToString()
    {
        return $"TAxisIsZeroPosition = {TAxisIsZeroPosition}\n" +
               $"RAxisIsZeroPosition = {RAxisIsZeroPosition}\n" +
               $"ZAxisIsZeroPosition = {ZAxisIsZeroPosition}";
    }
}
```

## 测试验证

### 单元测试示例

```csharp
[Test]
public void TestRobotZeroPositionProperties()
{
    var robotStatus = new RobotSubsystemStatus();
    
    // 设置零位置状态
    robotStatus.TAxisIsZeroPosition = true;
    robotStatus.RAxisIsZeroPosition = true;
    robotStatus.ZAxisIsZeroPosition = false;
    
    // 验证属性值
    Assert.IsTrue(robotStatus.TAxisIsZeroPosition);
    Assert.IsTrue(robotStatus.RAxisIsZeroPosition);
    Assert.IsFalse(robotStatus.ZAxisIsZeroPosition);
    
    // 验证字符串输出
    var output = robotStatus.ToString();
    Assert.IsTrue(output.Contains("TAxisIsZeroPosition = True"));
    Assert.IsTrue(output.Contains("RAxisIsZeroPosition = True"));
    Assert.IsTrue(output.Contains("ZAxisIsZeroPosition = False"));
}
```

### 集成测试示例

```csharp
[Test]
public void TestRobotStatusIntegration()
{
    // 模拟Robot归零操作
    var robotService = new RobotService();
    
    // 执行归零命令
    var result = await robotService.MoveToZeroPositionAsync();
    
    // 验证归零结果
    Assert.IsTrue(result.Success);
    
    // 检查状态更新
    var status = robotService.GetCurrentStatus();
    Assert.IsTrue(status.TAxisIsZeroPosition);
    Assert.IsTrue(status.RAxisIsZeroPosition);
    Assert.IsTrue(status.ZAxisIsZeroPosition);
}
```

## 迁移指南

### 1. 代码搜索替换

使用以下正则表达式进行批量替换：

```
查找: TAxisIsZero(?!Position)
替换: TAxisIsZeroPosition

查找: RAxisIsZero(?!Position)
替换: RAxisIsZeroPosition

查找: ZAxisIsZero(?!Position)
替换: ZAxisIsZeroPosition
```

### 2. 配置文件更新

如果有配置文件引用了这些属性名，也需要相应更新：

```json
// 修改前
{
  "robotStatus": {
    "checkProperties": ["TAxisIsZero", "RAxisIsZero", "ZAxisIsZero"]
  }
}

// 修改后
{
  "robotStatus": {
    "checkProperties": ["TAxisIsZeroPosition", "RAxisIsZeroPosition", "ZAxisIsZeroPosition"]
  }
}
```

### 3. 数据库字段更新

如果数据库中存储了这些字段，需要执行相应的迁移脚本：

```sql
-- 更新字段名
ALTER TABLE RobotStatus RENAME COLUMN TAxisIsZero TO TAxisIsZeroPosition;
ALTER TABLE RobotStatus RENAME COLUMN RAxisIsZero TO RAxisIsZeroPosition;
ALTER TABLE RobotStatus RENAME COLUMN ZAxisIsZero TO ZAxisIsZeroPosition;
```

## 注意事项

1. **向后兼容性**：确保现有的API调用不会因为属性名变更而中断
2. **文档更新**：更新所有相关的技术文档和用户手册
3. **测试覆盖**：确保所有使用这些属性的代码都有相应的测试
4. **部署协调**：与运维团队协调，确保生产环境的平滑升级

## 总结

通过统一轴位置属性的命名规范，使代码更加清晰和一致。`Position` 后缀明确表达了这些属性表示的是位置状态，提高了代码的可读性和维护性。
