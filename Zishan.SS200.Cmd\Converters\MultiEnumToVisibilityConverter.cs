using System;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;
using Zishan.SS200.Cmd.Models;

namespace Zishan.SS200.Cmd.Converters
{
    /// <summary>
    /// 多枚举值转可见性转换器
    /// </summary>
    public class MultiEnumToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PropertyTypeCategory category && parameter is string paramStr)
            {
                // 支持逗号分隔的多个枚举值
                var targetCategories = paramStr.Split(',')
                    .Select(s => s.Trim())
                    .Where(s => Enum.TryParse<PropertyTypeCategory>(s, out _))
                    .Select(s => Enum.Parse<PropertyTypeCategory>(s))
                    .ToList();

                return targetCategories.Contains(category) ? Visibility.Visible : Visibility.Collapsed;
            }
            
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
