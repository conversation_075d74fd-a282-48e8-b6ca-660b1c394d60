# Chamber 和 Shuttle IO 访问器完成总结

## 概述

已成功完成所有 Chamber IO 访问器和 Shuttle IO 访问器的实现，包括数字输入(DI)和数字输出(DO)的完整访问器定义。

## 完成的工作

### 1. Chamber IO 访问器

#### Chamber DI 访问器 (PDI1-PDI15)
- **PDI1** - CV打开传感器
- **PDI2** - CV关闭传感器  
- **PDI3** - T/V打开传感器
- **PDI4** - T/V关闭传感器
- **PDI5** - 前级开关
- **PDI6** - Torr开关
- **PDI7** - 等离子传感器1
- **PDI8** - 等离子传感器2
- **PDI9** - 匹配互锁开关1
- **PDI10** - 匹配互锁开关2
- **PDI11** - 温控器
- **PDI12** - Slit Door打开传感器
- **PDI13** - Slit Door关闭传感器
- **PDI14** - Lift Pin上升传感器
- **PDI15** - Lift Pin下降传感器

#### Chamber DO 访问器 (PDO1-PDO16)
- **PDO1** - T/V打开
- **PDO2** - T/V关闭
- **PDO3** - T/V FRZ
- **PDO4** - CV控制
- **PDO5** - 气体C1控制
- **PDO6** - 气体C2控制
- **PDO7** - 气体C3控制
- **PDO8** - 气体C4控制
- **PDO9** - 气体CM阀门控制
- **PDO10** - Slit Door打开
- **PDO11** - Slit Door关闭
- **PDO12** - Lift Pin上升
- **PDO13** - Lift Pin下降
- **PDO14** - RF1使能
- **PDO15** - RF2使能
- **PDO16** - 加热器使能

### 2. Shuttle IO 访问器

#### Shuttle DI 访问器 (SDI1-SDI28)
- **SDI1** - 晶圆盒门上升传感器
- **SDI2** - 晶圆盒门下降传感器
- **SDI3** - 晶圆盒巢伸出传感器
- **SDI4** - 晶圆盒巢收回传感器
- **SDI5** - 晶圆盒巢原位开关
- **SDI6** - 存在传感器晶圆盒1
- **SDI7** - 存在传感器晶圆盒2
- **SDI8** - 存在传感器晶圆盒3
- **SDI9** - 存在传感器晶圆盒4
- **SDI10** - 水流开关
- **SDI11** - 光幕1
- **SDI12** - 光幕2
- **SDI13** - Shuttle旋转传感器1
- **SDI14** - Shuttle旋转传感器2
- **SDI15** - Shuttle上升传感器
- **SDI16** - Shuttle下降传感器
- **SDI17** - 晶圆滑出传感器1 FL
- **SDI18** - 晶圆滑出传感器2 FR
- **SDI19** - 晶圆滑出传感器3 BL
- **SDI20** - 晶圆滑出传感器4 BR
- **SDI21** - Shuttle前级开关
- **SDI22** - Shuttle真空ISO阀门打开传感器
- **SDI23** - Shuttle真空ISO阀门关闭传感器
- **SDI24** - XV交叉阀门打开传感器
- **SDI25** - XV交叉阀门关闭传感器
- **SDI26** - 冷却腔泄漏传感器
- **SDI27** - 备用
- **SDI28** - 备用

#### Shuttle DO 访问器 (SDO1-SDO25)
- **SDO1** - 晶圆盒门气缸上升
- **SDO2** - 晶圆盒门气缸下降
- **SDO3** - 晶圆盒门运动使能
- **SDO4** - 晶圆盒巢气缸伸出
- **SDO5** - 晶圆盒巢气缸收回
- **SDO6** - Shuttle真空ISO阀门
- **SDO7** - Shuttle回填阀门
- **SDO8** - XV交叉阀门
- **SDO9** - 负载锁排气阀门
- **SDO10** - 负载锁回填阀门
- **SDO11** - Shuttle组件顺时针旋转
- **SDO12** - Shuttle组件逆时针旋转
- **SDO13** - Shuttle电机上升
- **SDO14** - Shuttle电机下降
- **SDO15** - Shuttle制动器
- **SDO16** - PCWS开关
- **SDO17** - LOAD_READY1
- **SDO18** - UNLOAD_READY1
- **SDO19** - LOAD_READY2
- **SDO20** - UNLOAD_READY2
- **SDO21** - 紫色蜂鸣器
- **SDO22** - 蓝色LED
- **SDO23** - 绿色LED
- **SDO24** - 橙色LED
- **SDO25** - 红色LED

## 技术实现

### 1. 访问器方法
为每个设备类型实现了专门的访问器方法：

```csharp
// Chamber DI 访问器
private IOPropertyAccessor<EnuChamberDICodes> GetOrCreateAccessor(EnuChamberDICodes enumValue)

// Chamber DO 访问器  
private IOPropertyAccessor<EnuChamberDOCodes> GetOrCreateDOAccessor(EnuChamberDOCodes enumValue)

// Shuttle DI 访问器
private IOPropertyAccessor<EnuShuttleDICodes> GetOrCreateAccessor(EnuShuttleDICodes enumValue)

// Shuttle DO 访问器
private IOPropertyAccessor<EnuShuttleDOCodes> GetOrCreateDOAccessor(EnuShuttleDOCodes enumValue)
```

### 2. 描述信息获取
所有访问器都使用枚举的 `DescriptionAttribute` 自动获取描述信息，无需手动传递描述参数。

### 3. 缓存机制
使用 `ConcurrentDictionary` 实现线程安全的访问器缓存，避免重复创建实例。

### 4. 代码组织
使用 `#region` 标记清晰地组织代码结构：
- Chamber IO访问器
  - Chamber DI 访问器
  - Chamber DO 访问器
- Shuttle IO访问器
  - Shuttle DI 访问器
  - Shuttle DO 访问器

## 使用示例

### Chamber IO 访问
```csharp
// 获取 Chamber A 的 Slit Door 状态
var chamberA = SS200InterLockMain.Instance.IOInterface.ChamberA;
bool slitDoorOpen = chamberA.PDI12_SlitDoorOpenSensor.Value;
string description = chamberA.PDI12_SlitDoorOpenSensor.Content; // "Slit Door打开传感器"

// 控制 Chamber A 的 Slit Door
chamberA.PDO10_SlitDoorOpen.Value = true; // 打开 Slit Door
```

### Shuttle IO 访问
```csharp
// 获取 Shuttle 的传感器状态
var shuttle = SS200InterLockMain.Instance.IOInterface.Shuttle;
bool cassettePresent = shuttle.SDI6_PresentSensorCassette1.Value;
string description = shuttle.SDI6_PresentSensorCassette1.Content; // "存在传感器晶圆盒1"

// 控制 Shuttle 的气缸
shuttle.SDO1_CassetteDoorCylinderUp.Value = true; // 晶圆盒门气缸上升
```

## 验证状态

- ✅ 所有 Chamber DI 访问器 (15个)
- ✅ 所有 Chamber DO 访问器 (16个)  
- ✅ 所有 Shuttle DI 访问器 (28个)
- ✅ 所有 Shuttle DO 访问器 (25个)
- ✅ 编译检查通过
- ✅ 描述信息自动获取
- ✅ 缓存机制正常工作

## 总计

**完成的访问器总数：84个**
- Chamber: 31个 (15 DI + 16 DO)
- Shuttle: 53个 (28 DI + 25 DO)

所有访问器都遵循统一的命名规范和实现模式，提供了完整的 IO 点访问能力。
