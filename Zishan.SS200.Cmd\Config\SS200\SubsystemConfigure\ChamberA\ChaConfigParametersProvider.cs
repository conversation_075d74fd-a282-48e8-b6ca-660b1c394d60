using System;
using System.Collections.Generic;
using System.IO;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.ChamberA;
using Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.ChamberA
{
    /// <summary>
    /// 处理室配置参数根配置
    /// </summary>
    public class ChaConfigParametersConfig
    {
        public List<ConfigureSetting> ConfigureSettings { get; set; }
    }

    /// <summary>
    /// 处理室配置参数提供者 - 从JSON配置文件加载参数
    /// </summary>
    public class ChaConfigParametersProvider : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ChaConfigParametersProvider));
        private readonly Dictionary<string, object> _settings = new Dictionary<string, object>();
        private readonly FileSystemWatcher _configWatcher;

        private static readonly Lazy<ChaConfigParametersProvider> _instance =
            new Lazy<ChaConfigParametersProvider>(() => new ChaConfigParametersProvider());

        // 配置文件路径
        private const string CONFIG_PATH = "Configs/SS200/SubsystemConfigure/ChamberA/ChaConfigParameters.json";

        // 最后修改时间，用于监测配置文件变化
        private DateTime _lastModifiedTime = DateTime.MinValue;

        public static ChaConfigParametersProvider Instance => _instance.Value;

        // 私有构造函数
        private ChaConfigParametersProvider()
        {
            // 初始化文件系统监视器
            string configDir = Path.GetDirectoryName(GetConfigFilePath());
            string configFileName = Path.GetFileName(CONFIG_PATH);

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            _configWatcher = new FileSystemWatcher(configDir, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            // 注册文件变化事件处理
            _configWatcher.Changed += OnConfigFileChanged;
            _configWatcher.Created += OnConfigFileChanged;

            // 初始化默认值
            InitializeDefaultValues();

            // 尝试加载配置文件
            LoadFromJson();
        }

        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 由于文件系统事件可能会触发多次，这里添加简单的防抖动处理
                if ((DateTime.Now - _lastModifiedTime).TotalMilliseconds < 100)
                {
                    return;
                }

                _logger.Info($"检测到配置文件变化: {e.FullPath}, 变化类型: {e.ChangeType}");
                LoadFromJson();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理配置文件变化事件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaultValues()
        {
            // 处理室门控制参数
            _settings["PPS1"] = 0.3;  // 处理室门开关最小时间(秒) - 支持小数
            _settings["PPS2"] = 5;    // 处理室门开关最大时间(秒)

            // 处理室ISO阀控制参数
            _settings["PPS3"] = 0.3;  // 处理室ISO阀开关最小时间(秒) - 支持小数
            _settings["PPS4"] = 4;    // 处理室ISO阀开关最大时间(秒)

            // 处理室真空系统参数
            _settings["PPS5"] = 10;  // 处理室抽真空最大时间(分钟)
            _settings["PPS6"] = 5;   // 处理室回填最大时间(分钟)
            _settings["PPS7"] = 2;   // 处理室传输压力(Torr)
            _settings["PPS8"] = 730; // 处理室大气压力最小值(Torr)
            _settings["PPS9"] = 0;   // 处理室压力偏移(Torr)
            _settings["PPS10"] = 760; // 处理室大气压力设定点(Torr)

            // 处理室电源控制参数
            _settings["PPS11"] = 2;  // 处理室电源开关最小时间(秒)
            _settings["PPS12"] = 5;  // 处理室电源开关最大时间(秒)

            // 处理室阀门控制参数
            _settings["PPS13"] = 1;  // 处理室阀门开关最小时间(秒)
            _settings["PPS14"] = 3;  // 处理室阀门开关最大时间(秒)

            // 处理室RF参数
            _settings["PPS15"] = 0;   // 处理室RF功率最小值(W)
            _settings["PPS16"] = 1000; // 处理室RF功率最大值(W)
            _settings["PPS17"] = 13.56; // 处理室RF频率最小值(MHz)
            _settings["PPS18"] = 13.56; // 处理室RF频率最大值(MHz)

            // 处理室温度参数
            _settings["PPS19"] = 20;  // 处理室温度最小值(℃)
            _settings["PPS20"] = 100; // 处理室温度最大值(℃)

            // 添加其他参数的默认值 (PPS21-PPS45)
            for (int i = 21; i <= 45; i++)
            {
                _settings[$"PPS{i}"] = 0; // 默认值为0
            }

            // 处理室压力控制参数
            _settings["PPS46"] = 0.5; // 处理室抽真空压力偏差(Torr) - 支持小数
        }

        /// <summary>
        /// 从JSON配置文件加载参数
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool LoadFromJson()
        {
            try
            {
                string jsonFilePath = GetConfigFilePath();
                if (!File.Exists(jsonFilePath))
                {
                    _logger.Warn($"处理室配置参数文件不存在: {jsonFilePath}，将使用默认值");
                    return false;
                }

                // 获取文件最后修改时间
                DateTime currentModified = File.GetLastWriteTime(jsonFilePath);

                // 如果文件未修改，则不重新加载
                if (currentModified == _lastModifiedTime)
                {
                    return true;
                }

                _lastModifiedTime = currentModified;
                string jsonContent;
                using (var fileStream = new FileStream(jsonFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    jsonContent = reader.ReadToEnd();
                }
                var config = JsonConvert.DeserializeObject<ChaConfigParametersConfig>(jsonContent);

                if (config?.ConfigureSettings == null || config.ConfigureSettings.Count == 0)
                {
                    _logger.Warn("未找到有效的配置参数，将使用默认值");
                    return false;
                }

                // 临时字典，验证成功后再替换
                var tempSettings = new Dictionary<string, object>();
                foreach (var setting in config.ConfigureSettings)
                {
                    if (string.IsNullOrEmpty(setting.Code))
                    {
                        _logger.Warn($"参数ID {setting.Id} 缺少代码标识，已跳过");
                        continue;
                    }

                    tempSettings[setting.Code] = setting.Value;
                    _logger.Debug($"加载参数 {setting.Code} = {setting.Value} ({setting.Description})");
                }

                // 验证所有必要参数都存在
                for (int i = 1; i <= 46; i++)
                {
                    string code = $"PPS{i}";
                    if (!tempSettings.ContainsKey(code))
                    {
                        _logger.Warn($"配置文件中缺少必要参数 {code}，将使用默认值");
                        tempSettings[code] = _settings[code]; // 使用默认值
                    }
                }

                // 更新参数字典
                foreach (var kvp in tempSettings)
                {
                    _settings[kvp.Key] = kvp.Value;
                }

                _logger.Info($"成功从 {jsonFilePath} 加载 {tempSettings.Count} 个处理室配置参数");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载处理室配置参数文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        private string GetConfigFilePath()
        {
            try
            {
                return App.ConfigHelper.GetConfigFilePath(CONFIG_PATH);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取配置文件路径失败: {ex.Message}", ex);

                // 回退策略 - 尝试直接拼接路径
                string fallbackPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    CONFIG_PATH);

                _logger.Info($"使用回退路径: {fallbackPath}");
                return fallbackPath;
            }
        }

        /// <summary>
        /// 获取参数值（泛型方法）
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="code">参数代码 (如 "PPS1")</param>
        /// <returns>参数值</returns>
        public T GetSettingValue<T>(string code)
        {
            if (_settings.TryGetValue(code, out object value))
            {
                if (value is T typedValue)
                {
                    return typedValue;
                }

                try
                {
                    // 尝试转换类型
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch (Exception ex)
                {
                    _logger.Error($"参数类型转换失败: {code}, 期望类型: {typeof(T).Name}, 实际值: {value}", ex);
                    throw new InvalidCastException($"参数类型不匹配: {code}");
                }
            }

            throw new KeyNotFoundException($"找不到处理室配置参数: {code}");
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        /// <param name="enuChaConfigParameterCodes">参数代码枚举</param>
        /// <returns>参数值</returns>
        public T GetSettingValue<T>(EnuChaConfigParameterCodes enuChaConfigParameterCodes)
        {
            return GetSettingValue<T>(enuChaConfigParameterCodes.ToString());
        }

        /// <summary>
        /// 获取int类型参数值
        /// </summary>
        /// <param name="enuChaConfigParameterCodes">参数代码枚举</param>
        /// <returns>int类型参数值</returns>
        public int GetIntSettingValue(EnuChaConfigParameterCodes enuChaConfigParameterCodes)
        {
            return GetSettingValue<int>(enuChaConfigParameterCodes);
        }

        /// <summary>
        /// 获取double类型参数值
        /// </summary>
        /// <param name="enuChaConfigParameterCodes">参数代码枚举</param>
        /// <returns>double类型参数值</returns>
        public double GetDoubleSettingValue(EnuChaConfigParameterCodes enuChaConfigParameterCodes)
        {
            return GetSettingValue<double>(enuChaConfigParameterCodes);
        }

        /// <summary>
        /// 获取string类型参数值
        /// </summary>
        /// <param name="enuChaConfigParameterCodes">参数代码枚举</param>
        /// <returns>string类型参数值</returns>
        public string GetStringSettingValue(EnuChaConfigParameterCodes enuChaConfigParameterCodes)
        {
            return GetSettingValue<string>(enuChaConfigParameterCodes);
        }

        #region 辅助方法 - 获取特定配置参数

        /// <summary>
        /// 获取处理室门开关最小时间
        /// </summary>
        /// <returns>处理室门开关最小时间(秒)</returns>
        public int GetChamberDoorMinTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS1);
        }

        /// <summary>
        /// 获取处理室门开关最大时间
        /// </summary>
        /// <returns>处理室门开关最大时间(秒)</returns>
        public int GetChamberDoorMaxTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS2);
        }

        /// <summary>
        /// 获取处理室ISO阀开关最小时间
        /// </summary>
        /// <returns>处理室ISO阀开关最小时间(秒)</returns>
        public int GetChamberISOValveMinTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS3);
        }

        /// <summary>
        /// 获取处理室ISO阀开关最大时间
        /// </summary>
        /// <returns>处理室ISO阀开关最大时间(秒)</returns>
        public int GetChamberISOValveMaxTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS4);
        }

        /// <summary>
        /// 获取处理室抽真空最大时间
        /// </summary>
        /// <returns>处理室抽真空最大时间(分钟)</returns>
        public int GetChamberPumpDownMaxTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS5);
        }

        /// <summary>
        /// 获取处理室回填最大时间
        /// </summary>
        /// <returns>处理室回填最大时间(分钟)</returns>
        public int GetChamberBackfillMaxTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS6);
        }

        /// <summary>
        /// 获取处理室传输压力
        /// </summary>
        /// <returns>处理室传输压力(Torr)</returns>
        public int GetChamberTransferPressure()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS7);
        }

        /// <summary>
        /// 获取处理室大气压力最小值
        /// </summary>
        /// <returns>处理室大气压力最小值(Torr)</returns>
        public int GetChamberATMPressureMinimum()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS8);
        }

        /// <summary>
        /// 获取处理室压力偏移
        /// </summary>
        /// <returns>处理室压力偏移(Torr)</returns>
        public int GetChamberPressureOffset()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS9);
        }

        /// <summary>
        /// 获取处理室大气压力设定点
        /// </summary>
        /// <returns>处理室大气压力设定点(Torr)</returns>
        public int GetChamberATMPressureSetpoint()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS10);
        }

        /// <summary>
        /// 获取处理室电源开关最小时间
        /// </summary>
        /// <returns>处理室电源开关最小时间(秒)</returns>
        public int GetChamberPowerMinTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS11);
        }

        /// <summary>
        /// 获取处理室电源开关最大时间
        /// </summary>
        /// <returns>处理室电源开关最大时间(秒)</returns>
        public int GetChamberPowerMaxTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS12);
        }

        /// <summary>
        /// 获取处理室阀门开关最小时间
        /// </summary>
        /// <returns>处理室阀门开关最小时间(秒)</returns>
        public int GetChamberValveMinTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS13);
        }

        /// <summary>
        /// 获取处理室阀门开关最大时间
        /// </summary>
        /// <returns>处理室阀门开关最大时间(秒)</returns>
        public int GetChamberValveMaxTime()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS14);
        }

        /// <summary>
        /// 获取处理室RF功率最小值
        /// </summary>
        /// <returns>处理室RF功率最小值(W)</returns>
        public int GetChamberRFPowerMin()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS15);
        }

        /// <summary>
        /// 获取处理室RF功率最大值
        /// </summary>
        /// <returns>处理室RF功率最大值(W)</returns>
        public int GetChamberRFPowerMax()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS16);
        }

        /// <summary>
        /// 获取处理室RF频率最小值
        /// </summary>
        /// <returns>处理室RF频率最小值(MHz)</returns>
        public double GetChamberRFFrequencyMin()
        {
            return GetDoubleSettingValue(EnuChaConfigParameterCodes.PPS17);
        }

        /// <summary>
        /// 获取处理室RF频率最大值
        /// </summary>
        /// <returns>处理室RF频率最大值(MHz)</returns>
        public double GetChamberRFFrequencyMax()
        {
            return GetDoubleSettingValue(EnuChaConfigParameterCodes.PPS18);
        }

        /// <summary>
        /// 获取处理室温度最小值
        /// </summary>
        /// <returns>处理室温度最小值(℃)</returns>
        public int GetChamberTemperatureMin()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS19);
        }

        /// <summary>
        /// 获取处理室温度最大值
        /// </summary>
        /// <returns>处理室温度最大值(℃)</returns>
        public int GetChamberTemperatureMax()
        {
            return GetIntSettingValue(EnuChaConfigParameterCodes.PPS20);
        }

        /// <summary>
        /// 获取处理室抽真空压力偏差
        /// </summary>
        /// <returns>处理室抽真空压力偏差(Torr)</returns>
        public double GetChamberPumpDownPressureDeviation()
        {
            return GetDoubleSettingValue(EnuChaConfigParameterCodes.PPS46);
        }

        #endregion 辅助方法 - 获取特定配置参数

        public void Dispose()
        {
            _configWatcher?.Dispose();
        }
    }
}