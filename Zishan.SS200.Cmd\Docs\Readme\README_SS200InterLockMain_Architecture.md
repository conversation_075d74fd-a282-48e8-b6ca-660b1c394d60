# SS200InterLockMain 架构设计文档

## 概述

`SS200InterLockMain` 是一个基于单例模式和工厂模式的统一访问入口系统，为 SS200 设备的 IO 接口、报警信息、配置信息和状态信息提供了类型安全、高性能的访问方式。

## 架构设计

### 1. 设计模式

#### 单例模式 (Singleton Pattern)
- **实现**: 使用 `Lazy<T>` 实现线程安全的懒加载单例
- **目的**: 确保全局唯一实例，避免重复初始化
- **优势**: 线程安全、延迟初始化、内存效率高

#### 工厂模式 (Factory Pattern)
- **实现**: `AccessorFactory` 类负责创建各种访问器实例
- **目的**: 封装对象创建逻辑，提供统一的创建接口
- **优势**: 解耦创建逻辑、支持缓存、易于扩展

#### 访问器模式 (Accessor Pattern)
- **实现**: 为不同类型的数据提供专门的访问器类
- **目的**: 提供统一的访问接口，隐藏底层实现细节
- **优势**: 类型安全、智能提示、易于维护

### 2. 核心组件

```mermaid
classDiagram
    class SS200InterLockMain {
        -Lazy~SS200InterLockMain~ _instance
        +Instance: SS200InterLockMain
        +IOInterface: IOInterfaceAccessor
        +AlarmCode: AlarmCodeAccessor
        +SubsystemConfigure: SubsystemConfigureAccessor
        +SubsystemStatus: SubsystemStatusAccessor
        +UpdateRobotStatus(status)
        +GetCoilStatusHelper()
    }

    class IOInterfaceAccessor {
        +Robot: RobotIOAccessor
        +ChamberA: ChamberIOAccessor
        +ChamberB: ChamberIOAccessor
        +Shuttle: ShuttleIOAccessor
    }

    class IOPropertyAccessor~TEnum~ {
        +Value: bool
        +Content: string
        +ValueNullable: bool?
        +IsActive: bool
    }

    class AlarmCodeAccessor {
        +Robot: RobotAlarmAccessor
        +ChamberA: ChamberAlarmAccessor
        +ChamberB: ChamberAlarmAccessor
    }

    class AlarmPropertyAccessor {
        +Content: string
        +ChsContent: string
        +Cause: string
        +Code: string
        +Item: int
    }

    class AccessorFactory {
        +CreateIOAccessor~TEnum~()
        +CreateDeviceIOAccessor()
        +GetSupportedDeviceTypes()
        +ClearCache()
    }

    SS200InterLockMain --> IOInterfaceAccessor
    SS200InterLockMain --> AlarmCodeAccessor
    IOInterfaceAccessor --> IOPropertyAccessor
    AlarmCodeAccessor --> AlarmPropertyAccessor
    AccessorFactory --> IOPropertyAccessor
```

### 3. 数据流架构

```mermaid
flowchart TD
    A[用户代码] --> B[SS200InterLockMain.Instance]
    B --> C{访问类型}
    
    C -->|IO访问| D[IOInterfaceAccessor]
    C -->|报警访问| E[AlarmCodeAccessor]
    C -->|配置访问| F[SubsystemConfigureAccessor]
    C -->|状态访问| G[SubsystemStatusAccessor]
    
    D --> H[设备IO访问器]
    H --> I[IOPropertyAccessor]
    I --> J[CoilStatusHelper]
    J --> K[IS200McuCmdService]
    K --> L[实际设备]
    
    E --> M[设备报警访问器]
    M --> N[AlarmPropertyAccessor]
    N --> O[ErrorCodesProvider]
    O --> P[配置文件]
    
    F --> Q[ConfigPropertyAccessor]
    Q --> R[ConfigureSetting]
    
    G --> S[状态访问器]
    S --> T[StatusPropertyAccessor]
    T --> U[SubsystemStatus实例]
```

## 核心特性

### 1. 类型安全
- 使用强类型枚举作为访问键
- 编译时类型检查
- 智能提示支持

### 2. 高性能
- 使用 `ConcurrentDictionary` 缓存访问器实例
- 避免重复创建对象
- 延迟初始化

### 3. 线程安全
- 单例模式使用 `Lazy<T>` 实现
- 缓存使用 `ConcurrentDictionary`
- 无锁设计

### 4. 易于扩展
- 工厂模式支持新设备类型
- 访问器模式支持新数据类型
- 插件化架构

## 使用场景

### 1. 实时IO监控
```csharp
// 监控Robot Paddle传感器
bool smoothPaddle = SS200InterLockMain.Instance.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;
bool nosePaddle = SS200InterLockMain.Instance.IOInterface.Robot.RDI2_PaddleNoseSensor.Value;

if (smoothPaddle && nosePaddle)
{
    // 两个Paddle都检测到晶圆，可能有问题
    var alarm = SS200InterLockMain.Instance.AlarmCode.Robot.RA1_SystemBusyReject;
    LogError($"Paddle冲突: {alarm.ChsContent}");
}
```

### 2. 状态机控制
```csharp
// 检查Chamber状态决定下一步操作
var chamberStatus = SS200InterLockMain.Instance.SubsystemStatus.ChamberA;
var slitDoor = chamberStatus.SlitDoorStatus.Value;
var liftPin = chamberStatus.LiftPinStatus.Value;

if (slitDoor == EnuSlitDoorStatus.Close && liftPin == EnuLiftPinStatus.Down)
{
    // Chamber准备就绪，可以开始处理
    StartProcessing();
}
```

### 3. 报警处理
```csharp
// 统一的报警处理
void HandleRobotAlarm(EnuRobotAlarmCodes alarmCode)
{
    var alarmAccessor = SS200InterLockMain.Instance.AlarmCode.Robot;
    var alarm = alarmCode switch
    {
        EnuRobotAlarmCodes.RA1 => alarmAccessor.RA1_SystemBusyReject,
        EnuRobotAlarmCodes.RA2 => alarmAccessor.RA2_SystemAlarmReject,
        EnuRobotAlarmCodes.RA3 => alarmAccessor.RA3_RAxisNotHomeError,
        _ => null
    };

    if (alarm != null)
    {
        ShowAlarmDialog(alarm.ChsContent, alarm.Cause);
    }
}
```

## 扩展指南

### 1. 添加新设备类型

1. **定义枚举**:
```csharp
public enum EnuNewDeviceDICodes
{
    NDI1_NewSensor,
    NDI2_AnotherSensor
}
```

2. **创建访问器**:
```csharp
public class NewDeviceIOAccessor
{
    private readonly CoilStatusHelper _coilHelper;

    public IOPropertyAccessor<EnuNewDeviceDICodes> NDI1_NewSensor =>
        GetOrCreateAccessor(EnuNewDeviceDICodes.NDI1_NewSensor);
}
```

3. **集成到主访问器**:
```csharp
public class IOInterfaceAccessor
{
    public NewDeviceIOAccessor NewDevice { get; }
    
    public IOInterfaceAccessor(CoilStatusHelper coilHelper)
    {
        NewDevice = new NewDeviceIOAccessor(coilHelper);
    }
}
```

### 2. 添加新数据类型

1. **创建属性访问器**:
```csharp
public class CustomPropertyAccessor
{
    public string CustomValue { get; }
    public string CustomContent { get; }
}
```

2. **创建设备访问器**:
```csharp
public class CustomAccessor
{
    public CustomPropertyAccessor CustomProperty =>
        GetOrCreateAccessor("custom_key");
}
```

3. **集成到主类**:
```csharp
public class SS200InterLockMain
{
    public CustomAccessor Custom { get; }
}
```

## 性能优化

### 1. 缓存策略
- 使用 `ConcurrentDictionary` 缓存访问器实例
- 按设备类型和枚举值组合作为缓存键
- 支持缓存清理和统计

### 2. 内存管理
- 延迟初始化，只在需要时创建对象
- 避免重复创建相同的访问器
- 使用弱引用避免内存泄漏

### 3. 并发优化
- 无锁设计，使用线程安全的集合类
- 读多写少的场景优化
- 避免不必要的同步

## 最佳实践

### 1. 使用建议
- 优先使用统一访问入口，避免直接访问底层服务
- 合理使用缓存，避免频繁创建对象
- 在UI线程中使用时注意性能影响

### 2. 错误处理
- 访问不存在的枚举时返回null，而不是抛出异常
- 提供详细的日志记录
- 支持降级处理

### 3. 测试策略
- 单元测试覆盖所有访问器
- 集成测试验证端到端功能
- 性能测试确保缓存效果

## 总结

`SS200InterLockMain` 通过单例模式和工厂模式的结合，提供了一个统一、类型安全、高性能的访问入口。该设计不仅简化了对SS200设备各种信息的访问，还提供了良好的扩展性和维护性。

主要优势：
- **统一性**: 所有访问都通过一个入口
- **类型安全**: 强类型访问，编译时检查
- **高性能**: 缓存机制，避免重复创建
- **可扩展**: 支持新设备和数据类型
- **易维护**: 清晰的架构和职责分离
