using System;
using CommunityToolkit.Mvvm.ComponentModel;
using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber
{
    /// <summary>
    /// Chamber子系统状态模型（抽象基类）
    /// 不能直接实例化，必须通过ChamberASubsystemStatus或ChamberBSubsystemStatus等子类实例化
    /// </summary>
    public abstract partial class ChamberSubsystemStatus : ObservableObject
    {
        #region 1. 触发状态 (Trigger Status)

        /// <summary>
        /// 触发状态 (MPS1-MPS2)
        /// </summary>
        [ObservableProperty]
        private EnuTriggerStatus triggerStatus = EnuTriggerStatus.None;

        #endregion 1. 触发状态 (Trigger Status)

        #region 2. 运行状态 (Run Status)

        /// <summary>
        /// 运行状态 (MPS3A-MPS5)
        /// </summary>
        [ObservableProperty]
        private EnuRunStatus runStatus = EnuRunStatus.None;

        #endregion 2. 运行状态 (Run Status)

        #region 3. 位置状态 (Position Status)

        /// <summary>
        /// Slit Door状态 (SP1-SP3)
        /// </summary>
        [ObservableProperty]
        private EnuSlitDoorStatus slitDoorStatus = EnuSlitDoorStatus.None;

        /// <summary>
        /// Lift Pin状态 (SP4-SP6)
        /// </summary>
        [ObservableProperty]
        private EnuLiftPinStatus liftPinStatus = EnuLiftPinStatus.None;

        /// <summary>
        /// 晶圆准备状态 (SP7-SP10)
        /// </summary>
        [ObservableProperty]
        private EnuWaferReadyStatus waferReadyStatus = EnuWaferReadyStatus.None;

        #endregion 3. 位置状态 (Position Status)

        #region 4. 压力状态 (Pressure Status)

        /// <summary>
        /// 腔体真空状态 (SP11-SP12)
        /// </summary>
        [ObservableProperty]
        private EnuChamberVacuumStatus chamberVacuumStatus = EnuChamberVacuumStatus.None;

        /// <summary>
        /// 负载锁真空状态 (SP13-SP14)
        /// </summary>
        [ObservableProperty]
        private EnuLoadlockVacuumStatus loadlockVacuumStatus = EnuLoadlockVacuumStatus.None;

        /// <summary>
        /// ISO阀门状态 (SP15-SP17: PDI1, PDI2)
        /// </summary>
        [ObservableProperty]
        private EnuValveStatus isoValveStatus = EnuValveStatus.None;

        /// <summary>
        /// 节流阀状态 (SP18-SP20: PDI13, PDI4)
        /// </summary>
        [ObservableProperty]
        private EnuValveStatus throttleValveStatus = EnuValveStatus.None;

        /// <summary>
        /// 前级真空状态 (SP21-SP22)
        /// </summary>
        [ObservableProperty]
        private EnuForlineVacuumStatus forlineVacuumStatus = EnuForlineVacuumStatus.None;

        #endregion 4. 压力状态 (Pressure Status)

        #region 5. 气体状态 (Gas Status)

        /// <summary>
        /// CM阀门状态 (SP23-SP24: PDO9)
        /// </summary>
        [ObservableProperty]
        private EnuValveStatus cmValveStatus = EnuValveStatus.None;

        /// <summary>
        /// C1阀门状态 (SP25-SP26: PDO5)
        /// </summary>
        [ObservableProperty]
        private EnuValveStatus c1ValveStatus = EnuValveStatus.None;

        /// <summary>
        /// C2阀门状态 (SP27-SP28: PDO6)
        /// </summary>
        [ObservableProperty]
        private EnuValveStatus c2ValveStatus = EnuValveStatus.None;

        /// <summary>
        /// C3阀门状态 (SP29-SP30: PDO7)
        /// </summary>
        [ObservableProperty]
        private EnuValveStatus c3ValveStatus = EnuValveStatus.None;

        /// <summary>
        /// C4阀门状态 (SP31-SP32: PDO8)
        /// </summary>
        [ObservableProperty]
        private EnuValveStatus c4ValveStatus = EnuValveStatus.None;

        #endregion 5. 气体状态 (Gas Status)

        public override string ToString()
        {
            var sb = new System.Text.StringBuilder();

            // 1. 触发状态
            sb.AppendLine($"TriggerStatus = {TriggerStatus}");

            // 2. 运行状态
            sb.AppendLine($"RunStatus = {RunStatus}");

            // 3. 位置状态
            sb.AppendLine($"SlitDoorStatus = {SlitDoorStatus}");
            sb.AppendLine($"LiftPinStatus = {LiftPinStatus}");
            sb.AppendLine($"WaferReadyStatus = {WaferReadyStatus}");

            // 4. 压力状态
            sb.AppendLine($"ChamberVacuumStatus = {ChamberVacuumStatus}");
            sb.AppendLine($"LoadlockVacuumStatus = {LoadlockVacuumStatus}");
            sb.AppendLine($"IsoValveStatus = {IsoValveStatus}");
            sb.AppendLine($"ThrottleValveStatus = {ThrottleValveStatus}");
            sb.AppendLine($"ForlineVacuumStatus = {ForlineVacuumStatus}");

            // 5. 气体状态
            sb.AppendLine($"CmValveStatus = {CmValveStatus}");
            sb.AppendLine($"C1ValveStatus = {C1ValveStatus}");
            sb.AppendLine($"C2ValveStatus = {C2ValveStatus}");
            sb.AppendLine($"C3ValveStatus = {C3ValveStatus}");
            sb.AppendLine($"C4ValveStatus = {C4ValveStatus}");

            return sb.ToString();
        }
    }
}