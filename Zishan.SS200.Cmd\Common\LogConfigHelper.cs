using System;
using System.IO;
using log4net;
using log4net.Appender;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 日志配置助手类
    /// 用于替代AppLog.cs中的日志配置功能
    /// </summary>
    public static class LogConfigHelper
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(LogConfigHelper));

        /// <summary>
        /// 更新日志文件夹路径
        /// 替代AppLog.UpdateFolder方法
        /// </summary>
        /// <param name="folder">新的日志文件夹路径</param>
        public static void UpdateLogFolder(string folder)
        {
            try
            {
                var repository = LogManager.GetRepository();
                var appenders = repository.GetAppenders();

                if (appenders == null)
                {
                    _logger.Warn("未找到任何日志Appender，无法更新日志文件夹");
                    return;
                }

                foreach (var appender in appenders)
                {
                    if (appender is RollingFileAppender rollingFileAppender)
                    {
                        var originalFileName = new FileInfo(rollingFileAppender.File).Name;
                        rollingFileAppender.File = Path.Combine(folder, originalFileName);
                        rollingFileAppender.ActivateOptions();

                        _logger.Info($"已更新日志文件路径: {rollingFileAppender.File}");
                    }
                }

                _logger.Info($"日志文件夹已更新为: {folder}");
            }
            catch (Exception ex)
            {
                _logger.Error($"更新日志文件夹失败: {folder}", ex);
            }
        }

        /// <summary>
        /// 设置附加日志路径
        /// 替代AppLog.SetAdditionalLogPath方法
        /// </summary>
        /// <param name="additionalPath">附加日志路径</param>
        public static void SetAdditionalLogPath(string additionalPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(additionalPath))
                {
                    _logger.Warn("附加日志路径为空，跳过设置");
                    return;
                }

                // 确保附加日志目录存在
                Directory.CreateDirectory(additionalPath);

                var repository = LogManager.GetRepository();
                var appenders = repository.GetAppenders();

                if (appenders == null) return;

                foreach (var appender in appenders)
                {
                    if (appender is RollingFileAppender rollingFileAppender)
                    {
                        var originalFile = rollingFileAppender.File;

                        if (!string.IsNullOrWhiteSpace(originalFile))
                        {
                            // 复制历史日志文件到附加路径
                            var logFiles = Directory.GetFiles(Path.GetDirectoryName(originalFile) ?? string.Empty, "*.log");
                            foreach (var logFile in logFiles)
                            {
                                var destFile = Path.Combine(additionalPath, Path.GetFileName(logFile));
                                if (!File.Exists(destFile))
                                {
                                    File.Copy(logFile, destFile);
                                }
                            }

                            // 删除附加路径中的多余日志文件
                            var additionalLogFiles = Directory.GetFiles(additionalPath, "*.log");
                            foreach (var additionalLogFile in additionalLogFiles)
                            {
                                var fileName = Path.GetFileName(additionalLogFile);
                                var exists = false;
                                foreach (var logFile in logFiles)
                                {
                                    if (Path.GetFileName(logFile) == fileName)
                                    {
                                        exists = true;
                                        break;
                                    }
                                }
                                if (!exists)
                                {
                                    File.Delete(additionalLogFile);
                                }
                            }
                        }
                    }
                }

                // 重新配置log4net
                repository.Configured = true;
                _logger.Info($"附加日志路径已设置为: {additionalPath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"设置附加日志路径失败: {additionalPath}", ex);
            }
        }

        /// <summary>
        /// 获取当前日志配置信息
        /// </summary>
        /// <returns>配置信息字符串</returns>
        public static string GetLogConfigInfo()
        {
            try
            {
                var repository = LogManager.GetRepository();
                var appenders = repository.GetAppenders();

                var info = $"Log4net配置信息:\n";
                info += $"- 仓库名称: {repository.Name}\n";
                info += $"- Appender数量: {appenders.Length}\n";

                foreach (var appender in appenders)
                {
                    info += $"- Appender: {appender.Name} ({appender.GetType().Name})";
                    if (appender is RollingFileAppender rfa)
                    {
                        info += $" -> {rfa.File}";
                    }
                    info += "\n";
                }

                return info;
            }
            catch (Exception ex)
            {
                return $"获取日志配置信息失败: {ex.Message}";
            }
        }
    }
}

//自定义log4net扩展
namespace log4net.Appender
{
    /// <summary>
    /// 解决Log4Net生成空日志文件的方法
    /// 最后在log4net.config配置文件中将类插入完成调用 <lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
    /// </summary>
    public class MinimalLockDeleteEmpty : FileAppender.MinimalLock
    {
        public override void ReleaseLock()
        {
            base.ReleaseLock();

            var logFile = new FileInfo(CurrentAppender.File);
            if (!logFile.Exists) return;
            var arrText = File.ReadAllText(CurrentAppender.File);
            if (string.IsNullOrWhiteSpace(arrText))
            {
                logFile.Delete();
            }
        }
    }
}