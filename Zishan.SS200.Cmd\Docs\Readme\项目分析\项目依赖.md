# Zishan.SS200.Cmd 项目依赖

## 🏗️ 系统架构概览

**<PERSON>ishan.SS200.Cmd** 采用分层架构设计，遵循依赖倒置原则和单一职责原则，通过依赖注入实现松耦合的模块化系统。整个系统分为六个主要层次，每层都有明确的职责和边界。

### 🎯 架构特点
- **分层清晰**：六层架构设计，职责分离明确
- **松耦合**：基于接口的依赖注入设计
- **高内聚**：功能模块内部高度内聚
- **可扩展**：模块化设计支持功能扩展

### 🎯 依赖管理原则
- **依赖倒置**：高层模块不依赖低层模块，都依赖于抽象
- **接口隔离**：使用专门的接口，避免不必要的依赖
- **单一职责**：每个模块只负责一个功能领域
- **开闭原则**：对扩展开放，对修改关闭
- **控制反转**：通过IoC容器管理对象生命周期

## 📊 整体系统架构图

```mermaid
graph TB
    subgraph "🖥️ 用户界面层 (Presentation Layer)"
        A[MainWindow<br/>主窗口] --> A1[TabControl容器<br/>标签页管理]
        A1 --> A2[设备控制Tab<br/>S200McuCmdPanel]
        A1 --> A3[批量命令Tab<br/>BasicCommandTest]
        A1 --> A4[晶圆搬运Tab<br/>TransferWafer]
        A1 --> A5[状态监控Tab<br/>ModbusDICoilsPanel]
        A1 --> A6[日志查看Tab<br/>LogViewer]
    end

    subgraph "🎯 视图模型层 (ViewModel Layer)"
        B[MainWindowViewModel<br/>主窗口视图模型] --> B1[应用程序状态管理<br/>全局状态控制]
        C[S200McuCmdPanelViewModel<br/>设备命令视图模型] --> C1[设备命令控制<br/>命令执行管理]
        D[TransferWaferViewModel<br/>晶圆传输视图模型] --> D1[晶圆传输控制<br/>拖拽操作处理]
        E[UiViewModel<br/>UI状态视图模型] --> E1[UI状态同步<br/>实时数据绑定]
        F[BasicCommandTestViewModel<br/>命令测试视图模型] --> F1[命令测试功能<br/>批量测试管理]
    end

    subgraph "⚙️ 业务服务层 (Business Service Layer)"
        G[S200McuCmdService<br/>设备命令服务] --> G1[设备管理中心<br/>4设备统一管理]
        H[BatchCommandParser<br/>批量命令解析器] --> H1[批量命令解析<br/>JSON命令处理]
        I[ConfigurationService<br/>配置管理服务] --> I1[配置管理中心<br/>多格式配置支持]
        J[CoilStatusHelper<br/>线圈状态助手] --> J1[线圈状态管理<br/>IO状态映射]
        K[UILogService<br/>UI日志服务] --> K1[UI日志服务<br/>界面日志显示]
    end

    subgraph "设备抽象层 (Device Abstraction Layer)"
        L[McuDevice-Shuttle] --> L1[Shuttle设备封装]
        M[McuDevice-Robot] --> M1[Robot设备封装]
        N[McuDevice-ChamberA] --> N1[ChamberA设备封装]
        O[McuDevice-ChamberB] --> O1[ChamberB设备封装]
    end

    subgraph "命令处理层 (Command Processing Layer)"
        P[DeviceCommandFactory] --> P1[命令处理器工厂]
        Q[ShuttleCommandHandler] --> Q1[Shuttle命令处理]
        R[RobotCommandHandler] --> R1[Robot命令处理]
        S[ChaCommandHandler] --> S1[ChamberA命令处理]
        T[ChbCommandHandler] --> T1[ChamberB命令处理]
    end

    subgraph "通信层 (Communication Layer)"
        U[ModbusClientService] --> U1[Modbus通信管理]
        V[ModbusRegisterService] --> V1[寄存器服务]
        W[ModbusClientFactory] --> W1[连接工厂]
    end

    subgraph "数据访问层 (Data Access Layer)"
        X[NModbus Library] --> X1[Modbus协议实现]
        Y[Configuration Files] --> Y1[配置文件存储]
        Z[Log Files] --> Z1[日志文件存储]
        AA[Cache Storage] --> AA1[缓存存储]
    end

    subgraph "外部设备层 (External Device Layer)"
        BB[Shuttle MCU] --> BB1[传送设备]
        CC[Robot MCU] --> CC1[机械臂设备]
        DD[ChamberA MCU] --> DD1[工艺腔体A]
        EE[ChamberB MCU] --> EE1[工艺腔体B]
    end

    %% 层间依赖关系
    A2 --> C
    A3 --> C
    A4 --> D
    A5 --> E
    A6 --> E

    B --> G
    C --> G
    C --> H
    D --> G
    E --> G
    F --> G

    G --> L
    G --> M
    G --> N
    G --> O
    G --> I
    G --> J

    L --> P
    M --> P
    N --> P
    O --> P

    P --> Q
    P --> R
    P --> S
    P --> T

    Q --> U
    R --> U
    S --> U
    T --> U

    U --> X
    V --> X
    W --> X

    I --> Y
    K --> Z
    J --> AA

    X --> BB
    X --> CC
    X --> DD
    X --> EE
```

## 依赖注入架构图

```mermaid
graph LR
    subgraph "IoC容器 (DryIoc Container)"
        A[Container Registry] --> B[Service Registration]
        B --> C[Lifetime Management]
        C --> D[Dependency Resolution]
    end

    subgraph "服务注册 (Service Registration)"
        E[Singleton Services] --> E1[IModbusClientService]
        E --> E2[IS200McuCmdService]
        E --> E3[IConfigurationService]

        F[Transient Services] --> F1[IDeviceCommandHandler]
        F --> F2[IBatchCommandParser]
        F --> F3[ICoilStatusHelper]

        G[Scoped Services] --> G1[IUILogService]
        G --> G2[IModbusRegisterService]
    end

    subgraph "工厂模式 (Factory Pattern)"
        H[DeviceCommandFactory] --> H1[ShuttleCommandHandler]
        H --> H2[RobotCommandHandler]
        H --> H3[ChaCommandHandler]
        H --> H4[ChbCommandHandler]

        I[ModbusClientFactory] --> I1[TcpModbusClient]
        I --> I2[RtuModbusClient]
        I --> I3[AsciiModbusClient]
    end

    subgraph "配置注入 (Configuration Injection)"
        J[IConfiguration] --> J1[ConnectionStrings]
        J --> J2[DeviceSettings]
        J --> J3[LoggingSettings]
        J --> J4[CacheSettings]
    end

    A --> E
    A --> F
    A --> G
    A --> H
    A --> I
    A --> J
```

## 核心模块依赖关系图

```mermaid
classDiagram
    class App {
        +Main() void
        +OnStartup(StartupEventArgs) void
        +RegisterTypes(IContainerRegistry) void
        +ConfigureModuleCatalog(IModuleCatalog) void
        +CreateShell() Window
        -InitializeLogging() void
        -ConfigureExceptionHandling() void
    }

    class MainWindowViewModel {
        -IS200McuCmdService _mcuCmdService
        -IRegionManager _regionManager
        -IEventAggregator _eventAggregator
        -IConfigurationService _configService
        +DeviceStatusCollection ObservableCollection~DeviceStatus~
        +SelectedTabIndex int
        +Initialize() Task
        +UpdateDeviceStatusAsync() Task
        +OnDeviceStatusChanged(DeviceStatusChangedEvent) void
    }

    class S200McuCmdService {
        -Dictionary~string,McuDevice~ _devices
        -IModbusClientService _modbusClientService
        -IDeviceCommandFactory _commandFactory
        -IConfigurationService _configurationService
        -Timer _statusMonitorTimer
        +McuDevice Shuttle
        +McuDevice Robot
        +McuDevice ChamberA
        +McuDevice ChamberB
        +ConnectAllAsync(CancellationToken) Task~OperateResult~
        +DisconnectAllAsync() Task~OperateResult~
        +ExecuteCommandAsync(string,string,object[]) Task~OperateResult~
        +GetDeviceStatus(string) DeviceStatus
        +StartStatusMonitoring() void
        +StopStatusMonitoring() void
    }

    class McuDevice {
        -IModbusClientService _modbusClientService
        -CmdTaskHandler _cmdTaskHandler
        -IDeviceCommandHandler _commandHandler
        -DeviceConfiguration _configuration
        -CancellationTokenSource _cancellationTokenSource
        +string DeviceName
        +DeviceType DeviceType
        +ConnectionStatus Status
        +DateTime LastUpdateTime
        +ConnectAsync(CancellationToken) Task~bool~
        +DisconnectAsync() Task~bool~
        +ExecuteCommandAsync(string,object[]) Task~OperateResult~
        +ReadStatusAsync() Task~DeviceStatus~
        +StartMonitoring() void
        +StopMonitoring() void
    }

    class ModbusClientService {
        -ConcurrentDictionary~string,IModbusMaster~ _masters
        -ConcurrentDictionary~string,TcpClient~ _tcpClients
        -ILogger _logger
        -ModbusConfiguration _configuration
        +ConnectAsync(string,int,byte) Task~bool~
        +DisconnectAsync(string) Task~bool~
        +ReadHoldingRegistersAsync(string,ushort,ushort) Task~ushort[]~
        +WriteHoldingRegistersAsync(string,ushort,ushort[]) Task~bool~
        +ReadCoilsAsync(string,ushort,ushort) Task~bool[]~
        +WriteSingleCoilAsync(string,ushort,bool) Task~bool~
        +GetConnectionStatus(string) ConnectionStatus
        +Dispose() void
    }

    class DeviceCommandFactory {
        -IServiceProvider _serviceProvider
        -Dictionary~DeviceType,Type~ _handlerTypes
        +CreateCommandHandler(DeviceType) IDeviceCommandHandler
        +RegisterHandler~T~(DeviceType) void
        +GetSupportedCommands(DeviceType) IEnumerable~string~
    }

    class BaseDeviceCommandHandler {
        #IModbusClientService _modbusClientService
        #ILogger _logger
        #DeviceConfiguration _configuration
        +ExecuteCommandAsync(string,object[],CancellationToken) Task~OperateResult~
        +ValidateParameters(string,object[]) ValidationResult
        #WriteCommandAsync(ushort,ushort[]) Task~bool~
        #ReadStatusAsync(ushort,ushort) Task~ushort[]~
        #WaitForCompletionAsync(ushort,TimeSpan) Task~bool~
    }

    class ShuttleCommandHandler {
        +ExecuteCommandAsync(string,object[],CancellationToken) Task~OperateResult~
        -MoveToPositionAsync(double,double,double) Task~OperateResult~
        -SetSpeedAsync(double) Task~OperateResult~
        -HomeAxisAsync(AxisType) Task~OperateResult~
        -GetCurrentPositionAsync() Task~Position~
    }

    class RobotCommandHandler {
        +ExecuteCommandAsync(string,object[],CancellationToken) Task~OperateResult~
        -PickWaferAsync(int,int) Task~OperateResult~
        -PlaceWaferAsync(int,int) Task~OperateResult~
        -MoveToStationAsync(int) Task~OperateResult~
        -GetArmStatusAsync() Task~ArmStatus~
    }

    class ConfigurationService {
        -IConfiguration _configuration
        -FileSystemWatcher _configWatcher
        -ConcurrentDictionary~string,object~ _configCache
        +GetConfiguration~T~(string) T
        +UpdateConfiguration~T~(string,T) Task~bool~
        +ReloadConfigurationAsync() Task~void~
        +OnConfigurationChanged EventHandler~ConfigChangedEventArgs~
    }

    class CoilStatusHelper {
        -IModbusClientService _modbusClientService
        -Dictionary~string,CoilMapping~ _coilMappings
        -Timer _updateTimer
        +GetCoilStatus(string,string) bool
        +SetCoilStatus(string,string,bool) Task~bool~
        +GetAllCoilStatus(string) Dictionary~string,bool~
        +StartMonitoring() void
        +StopMonitoring() void
        +OnCoilStatusChanged EventHandler~CoilStatusChangedEventArgs~
    }

    %% 依赖关系
    App --> MainWindowViewModel : creates
    App --> S200McuCmdService : registers
    App --> ModbusClientService : registers
    App --> ConfigurationService : registers

    MainWindowViewModel --> S200McuCmdService : uses
    MainWindowViewModel --> ConfigurationService : uses

    S200McuCmdService --> McuDevice : manages
    S200McuCmdService --> ModbusClientService : uses
    S200McuCmdService --> DeviceCommandFactory : uses
    S200McuCmdService --> ConfigurationService : uses
    S200McuCmdService --> CoilStatusHelper : uses

    McuDevice --> ModbusClientService : uses
    McuDevice --> BaseDeviceCommandHandler : uses

    DeviceCommandFactory --> BaseDeviceCommandHandler : creates
    DeviceCommandFactory --> ShuttleCommandHandler : creates
    DeviceCommandFactory --> RobotCommandHandler : creates

    BaseDeviceCommandHandler --> ModbusClientService : uses
    BaseDeviceCommandHandler --> ConfigurationService : uses

    ShuttleCommandHandler --|> BaseDeviceCommandHandler : inherits
    RobotCommandHandler --|> BaseDeviceCommandHandler : inherits

    CoilStatusHelper --> ModbusClientService : uses
```

### 第三方库依赖图

```mermaid
graph TB
    subgraph "应用程序核心"
        A[Zishan.SS200.Cmd]
    end

    subgraph "UI框架依赖"
        B[WPF Framework]
        C[HandyControl 3.5.1]
        D[Microsoft.Xaml.Behaviors.Wpf 1.1.135]
        E[gong-wpf-dragdrop 4.0.0]
        F[Wu.Wpf.ControlLibrary 1.0.1]
    end

    subgraph "MVVM框架依赖"
        G[CommunityToolkit.Mvvm 8.4.0]
        H[Prism.DryIoc 8.1.97]
        I[Prism.Wpf 8.1.97]
    end

    subgraph "通信协议依赖"
        J[NModbus 3.0.81]
        K[System.Net.Sockets]
    end

    subgraph "数据处理依赖"
        L[Newtonsoft.Json 13.0.3]
        M[ini-parser-netstandard 2.5.3]
    end

    subgraph "数据库和缓存依赖"
        N[SqlSugarCore 5.1.4.195]
        O[StackExchange.Redis 2.8.37]
    end

    subgraph "日志和工具依赖"
        P[log4net 3.0.4]
        Q[Wu 1.0.7]
        R[Wu.Wpf 1.0.11]
    end

    %% 依赖关系
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    A --> I
    A --> J
    A --> L
    A --> M
    A --> N
    A --> O
    A --> P
    A --> Q
    A --> R

    J --> K
    H --> G
```

## 数据流依赖分析图

```mermaid
flowchart TD
    subgraph "数据输入层 (Data Input Layer)"
        A[用户界面输入] --> A1[命令参数]
        B[配置文件] --> B1[设备配置]
        C[外部API] --> C1[系统状态]
    end

    subgraph "数据验证层 (Data Validation Layer)"
        A1 --> D[参数验证器]
        B1 --> E[配置验证器]
        C1 --> F[状态验证器]
    end

    subgraph "数据转换层 (Data Transformation Layer)"
        D --> G[命令转换器]
        E --> H[配置转换器]
        F --> I[状态转换器]
    end

    subgraph "业务处理层 (Business Processing Layer)"
        G --> J[命令处理器]
        H --> K[配置管理器]
        I --> L[状态管理器]
    end

    subgraph "数据传输层 (Data Transmission Layer)"
        J --> M[Modbus协议栈]
        K --> N[文件系统]
        L --> O[内存缓存]
    end

    subgraph "数据存储层 (Data Storage Layer)"
        M --> P[设备寄存器]
        N --> Q[配置文件]
        O --> R[状态缓存]
    end

    subgraph "数据反馈层 (Data Feedback Layer)"
        P --> S[设备响应]
        Q --> T[配置更新]
        R --> U[状态变化]
    end

    subgraph "数据输出层 (Data Output Layer)"
        S --> V[UI状态更新]
        T --> W[配置重载]
        U --> X[事件通知]
    end

    V --> Y[界面刷新]
    W --> Z[系统重配置]
    X --> AA[状态监控]
```

## 第三方库依赖关系图

```mermaid
graph TB
    subgraph "UI框架依赖 (UI Framework Dependencies)"
        A[WPF Framework] --> A1[Microsoft.WindowsDesktop.App]
        A --> A2[System.Windows.Forms]

        B[HandyControl] --> B1[现代化控件]
        B --> B2[主题系统]
        B --> B3[动画效果]

        C[Wu.Wpf系列] --> C1[自定义控件]
        C --> C2[扩展功能]

        D[gong-wpf-dragdrop] --> D1[拖拽功能]
        D --> D2[视觉反馈]
    end

    subgraph "MVVM框架依赖 (MVVM Framework Dependencies)"
        E[CommunityToolkit.Mvvm] --> E1[源生成器]
        E --> E2[ObservableObject]
        E --> E3[RelayCommand]
        E --> E4[Messenger]

        F[Prism.Wpf] --> F1[区域管理]
        F --> F2[导航系统]
        F --> F3[事件聚合]

        G[Prism.DryIoc] --> G1[依赖注入]
        G --> G2[生命周期管理]
        G --> G3[模块系统]
    end

    subgraph "通信协议依赖 (Communication Protocol Dependencies)"
        H[NModbus] --> H1[Modbus TCP]
        H --> H2[Modbus RTU]
        H --> H3[Modbus ASCII]
        H --> H4[System.IO.Ports]
        H --> H5[System.Net.Sockets]
    end

    subgraph "数据处理依赖 (Data Processing Dependencies)"
        I[Newtonsoft.Json] --> I1[JSON序列化]
        I --> I2[LINQ to JSON]
        I --> I3[JsonConverter]

        J[ini-parser-netstandard] --> J1[INI文件解析]
        J --> J2[配置管理]

        K[SqlSugarCore] --> K1[ORM映射]
        K --> K2[数据库访问]
        K --> K3[查询构建]

        L[StackExchange.Redis] --> L1[Redis客户端]
        L --> L2[连接池]
        L --> L3[序列化器]
    end

    subgraph "日志系统依赖 (Logging System Dependencies)"
        M[log4net] --> M1[日志记录器]
        M --> M2[日志格式化]
        M --> M3[日志输出器]
        M --> M4[配置系统]
    end

    subgraph "行为增强依赖 (Behavior Enhancement Dependencies)"
        N[Microsoft.Xaml.Behaviors.Wpf] --> N1[Behavior基类]
        N --> N2[Trigger系统]
        N --> N3[Action系统]
    end

    %% 依赖关系连接
    A --> E
    A --> F
    B --> A
    C --> A
    D --> A

    E --> G
    F --> G

    H --> I
    I --> K
    J --> K
    K --> L

    M --> I
    N --> A
```

## 数据流依赖图

```mermaid
flowchart TD
    A[用户界面输入] --> B[ViewModel命令]
    B --> C[参数验证]
    C --> D[服务层调用]
    
    D --> E{服务类型}
    E -->|设备控制| F[S200McuCmdService]
    E -->|配置管理| G[ConfigurationService]
    E -->|状态监控| H[CoilStatusHelper]
    
    F --> I[设备选择]
    I --> J[McuDevice]
    J --> K[CmdTaskHandler]
    K --> L[ModbusClientService]
    L --> M[NModbus]
    M --> N[TCP通信]
    N --> O[MCU设备]
    
    G --> P[配置文件读取]
    P --> Q[INI/JSON解析]
    
    H --> R[线圈状态读取]
    R --> L
    
    O --> S[设备响应]
    S --> T[数据处理]
    T --> U[状态更新]
    U --> V[UI刷新]
```

## 第三方库依赖图

```mermaid
graph TB
    subgraph "UI层依赖"
        A[WPF] --> B[Microsoft.Xaml.Behaviors.Wpf]
        A --> C[HandyControl]
        A --> D[gong-wpf-dragdrop]
        A --> E[Wu.Wpf系列]
    end
    
    subgraph "MVVM层依赖"
        F[CommunityToolkit.Mvvm] --> G[ObservableObject]
        F --> H[RelayCommand]
        I[Prism.Wpf] --> J[ViewModelLocator]
        I --> K[RegionManager]
        L[Prism.DryIoc] --> M[依赖注入容器]
    end
    
    subgraph "通信层依赖"
        N[NModbus] --> O[Modbus TCP/RTU]
        N --> P[System.IO.Ports]
    end
    
    subgraph "数据处理依赖"
        Q[Newtonsoft.Json] --> R[JSON序列化]
        S[ini-parser-netstandard] --> T[INI文件解析]
        U[SqlSugarCore] --> V[数据库ORM]
        W[StackExchange.Redis] --> X[Redis缓存]
    end
    
    subgraph "日志依赖"
        Y[log4net] --> Z[文件日志]
        Y --> AA[控制台日志]
    end
```

## 配置文件依赖关系

```mermaid
graph LR
    A[App.xaml.cs] --> B[Config.ini]
    A --> C[log4net.config]
    
    B --> D[设备IP配置]
    B --> E[数据库配置]
    B --> F[系统参数配置]
    
    G[ConfigurationService] --> H[ChamberAParameter.json]
    G --> I[RobotParameter.json]
    G --> J[ShuttleParameter.json]
    G --> K[BatchCommands.json]
    
    L[ErrorCodeInfoParser] --> M[RobotAlarmInfo.json]
    L --> N[SHTLAlarmInfo.json]
    L --> O[MotorAlarmInfo.json]
    
    P[CoilStatusHelper] --> Q[McuDeviceCoilName.json]
```

## 模块间通信依赖

```mermaid
sequenceDiagram
    participant UI as 用户界面
    participant VM as ViewModel
    participant SVC as 服务层
    participant DEV as 设备层
    participant COM as 通信层
    participant MCU as MCU设备
    
    UI->>VM: 用户操作
    VM->>VM: 数据验证
    VM->>SVC: 调用服务
    SVC->>DEV: 设备操作
    DEV->>COM: 通信请求
    COM->>MCU: Modbus命令
    MCU-->>COM: 设备响应
    COM-->>DEV: 响应数据
    DEV-->>SVC: 处理结果
    SVC-->>VM: 状态更新
    VM-->>UI: 界面刷新
```

## 异常处理依赖链

```mermaid
graph TD
    A[UI层异常] --> B[ViewModel异常处理]
    C[服务层异常] --> D[服务异常处理]
    E[通信层异常] --> F[通信异常处理]
    G[设备层异常] --> H[设备异常处理]
    
    B --> I[用户提示]
    D --> J[日志记录]
    F --> K[重连机制]
    H --> L[状态重置]
    
    J --> M[log4net]
    I --> N[HandyControl消息]
    K --> O[ModbusClientService]
    L --> P[McuDevice状态管理]
```

## 资源依赖关系

```mermaid
graph LR
    subgraph "静态资源"
        A[Images] --> B[logo.ico]
        A --> C[plate.png]
        A --> D[CalibrationContent.png]
    end
    
    subgraph "配置资源"
        E[Configs] --> F[JSON配置文件]
        E --> G[INI配置文件]
        E --> H[XML配置文件]
    end
    
    subgraph "文档资源"
        I[Docs] --> J[API文档]
        I --> K[用户手册]
        I --> L[技术文档]
    end
    
    subgraph "运行时依赖"
        M[bin/Debug] --> N[主程序集]
        M --> O[依赖程序集]
        M --> P[配置文件副本]
    end
```

## 编译时依赖分析

```mermaid
graph TB
    A[源代码文件] --> B[C#编译器]
    C[XAML文件] --> D[XAML编译器]
    E[资源文件] --> F[资源编译器]
    
    B --> G[IL代码]
    D --> H[BAML资源]
    F --> I[嵌入资源]
    
    G --> J[主程序集]
    H --> J
    I --> J
    
    K[NuGet包] --> L[依赖程序集]
    L --> M[输出目录]
    J --> M
    
    N[配置文件] --> O[复制到输出]
    O --> M
```

## 运行时依赖检查

```mermaid
flowchart TD
    A[应用程序启动] --> B{检查.NET运行时}
    B -->|缺失| C[提示安装.NET 8.0]
    B -->|存在| D[加载主程序集]
    
    D --> E{检查依赖程序集}
    E -->|缺失| F[NuGet包还原]
    E -->|存在| G[初始化依赖注入]
    
    G --> H{检查配置文件}
    H -->|缺失| I[创建默认配置]
    H -->|存在| J[加载配置]
    
    J --> K[启动应用程序]
    I --> K
    F --> G
    C --> L[退出应用程序]
```

## 部署依赖要求

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **运行时**: .NET 8.0 Desktop Runtime
- **内存**: 最小512MB，推荐1GB
- **存储**: 最小100MB可用空间

### 依赖文件清单
1. **主程序集**: Zishan.SS200.Cmd.exe
2. **配置文件**: Config.ini, log4net.config
3. **第三方库**: 所有NuGet包依赖
4. **资源文件**: 图片、图标等静态资源
5. **文档文件**: 用户手册、API文档

### 网络依赖
- **Modbus TCP**: 需要网络连接到MCU设备
- **Redis**: 可选，用于数据缓存
- **数据库**: 可选，用于历史数据存储

## 配置文件依赖关系分析

```mermaid
graph LR
    subgraph "应用程序配置 (Application Configuration)"
        A[App.config] --> A1[应用程序设置]
        B[appsettings.json] --> B1[环境配置]
        C[log4net.config] --> C1[日志配置]
    end

    subgraph "设备配置 (Device Configuration)"
        D[Config.ini] --> D1[设备连接配置]
        E[DeviceSettings.json] --> E1[设备参数配置]
        F[ModbusConfig.json] --> F1[通信配置]
    end

    subgraph "命令配置 (Command Configuration)"
        G[ShuttleParameter.json] --> G1[Shuttle命令参数]
        H[RobotParameter.json] --> H1[Robot命令参数]
        I[ChamberAParameter.json] --> I1[ChamberA命令参数]
        J[ChamberBParameter.json] --> J1[ChamberB命令参数]
    end

    subgraph "状态配置 (Status Configuration)"
        K[McuDeviceCoilName.json] --> K1[线圈名称映射]
        L[AlarmInfo.json] --> L1[报警信息配置]
        M[StatusMapping.json] --> M1[状态映射配置]
    end

    subgraph "批量操作配置 (Batch Operation Configuration)"
        N[BatchCommands.json] --> N1[批量命令序列]
        O[RecipeConfig.json] --> O1[配方配置]
        P[WorkflowConfig.json] --> P1[工作流配置]
    end

    %% 配置依赖关系
    A1 --> Q[ConfigurationService]
    B1 --> Q
    C1 --> R[LoggingService]

    D1 --> S[ModbusClientService]
    E1 --> S
    F1 --> S

    G1 --> T[ShuttleCommandHandler]
    H1 --> U[RobotCommandHandler]
    I1 --> V[ChaCommandHandler]
    J1 --> W[ChbCommandHandler]

    K1 --> X[CoilStatusHelper]
    L1 --> Y[AlarmService]
    M1 --> Z[StatusMonitorService]

    N1 --> AA[BatchCommandParser]
    O1 --> BB[RecipeService]
    P1 --> CC[WorkflowEngine]
```

## 编译时依赖分析

```mermaid
flowchart TD
    A[源代码文件] --> B[C#编译器 Roslyn]
    C[XAML文件] --> D[XAML编译器]
    E[资源文件] --> F[资源编译器]
    G[配置文件] --> H[内容复制]

    B --> I[IL代码生成]
    D --> J[BAML资源生成]
    F --> K[嵌入资源生成]
    H --> L[输出目录复制]

    I --> M[程序集生成]
    J --> M
    K --> M

    N[NuGet包引用] --> O[包还原]
    O --> P[依赖程序集]
    P --> Q[输出目录]

    M --> Q
    L --> Q

    Q --> R[可执行程序]

    S[源生成器] --> T[编译时代码生成]
    T --> B

    U[分析器] --> V[代码质量检查]
    V --> B
```

## 运行时依赖检查流程

```mermaid
flowchart TD
    A[应用程序启动] --> B{检查.NET运行时}
    B -->|缺失| C[显示错误信息]
    B -->|存在| D[加载主程序集]

    D --> E{检查依赖程序集}
    E -->|缺失| F[程序集加载失败]
    E -->|存在| G[初始化依赖注入容器]

    G --> H{检查配置文件}
    H -->|缺失| I[使用默认配置]
    H -->|存在| J[加载配置文件]

    I --> K[配置验证]
    J --> K

    K --> L{配置是否有效}
    L -->|无效| M[显示配置错误]
    L -->|有效| N[初始化服务]

    N --> O{服务初始化成功}
    O -->|失败| P[显示服务错误]
    O -->|成功| Q[启动主窗口]

    Q --> R[应用程序运行]

    C --> S[退出应用程序]
    F --> S
    M --> S
    P --> S
```

## 依赖管理最佳实践

### 1. 版本控制策略
**PackageReference管理**：
```xml
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
<PackageReference Include="Prism.DryIoc" Version="8.1.97" />
<PackageReference Include="NModbus" Version="3.0.81" />
```

**版本锁定原则**：
- 主要框架包使用精确版本号
- 工具类库允许小版本更新
- 安全补丁及时更新

### 2. 依赖隔离设计
**接口驱动架构**：
```csharp
// 服务接口定义
public interface IModbusClientService
{
    Task<bool> ConnectAsync(string ipAddress, int port);
    Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort count);
}

// 具体实现
public class ModbusClientService : IModbusClientService
{
    // 实现细节
}

// 依赖注入注册
containerRegistry.RegisterSingleton<IModbusClientService, ModbusClientService>();
```

### 3. 异常处理策略
**分层异常处理**：
- UI层：用户友好的错误提示
- 业务层：业务逻辑异常处理
- 数据层：数据访问异常处理
- 通信层：网络通信异常处理

### 4. 性能优化措施
**资源管理优化**：
- 使用对象池减少GC压力
- 异步操作避免线程阻塞
- 连接复用减少资源消耗
- 缓存机制提高响应速度

### 5. 安全性考虑
**依赖安全**：
- 定期扫描依赖项安全漏洞
- 使用可信的NuGet源
- 验证包的数字签名
- 最小权限原则

### 6. 监控和诊断
**依赖监控**：
- 运行时依赖检查
- 性能指标监控
- 异常统计分析
- 资源使用监控
