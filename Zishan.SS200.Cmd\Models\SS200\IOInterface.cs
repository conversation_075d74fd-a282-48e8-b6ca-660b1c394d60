﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Models.SS200
{
    /// <summary>
    /// 机器人IO接口配置：怎么从IOC 单例容器中获取IS200McuCmdService获取过来，双向映射过来
    /// </summary>
    public class IOInterface
    {
        /// <summary>
        /// Shuttle设备输入线圈集合
        /// </summary>
        public ObservableCollection<ModbusCoil> ShuttleInputCoils { get; } = new();

        /// <summary>
        /// Robot设备输入线圈集合
        /// </summary>
        public ObservableCollection<ModbusCoil> RobotInputCoils { get; } = new();

        /// <summary>
        /// Cha设备输入线圈集合
        /// </summary>
        public ObservableCollection<ModbusCoil> ChaInputCoils { get; } = new();

        /// <summary>
        /// Chb设备输入线圈集合
        /// </summary>
        public ObservableCollection<ModbusCoil> ChbInputCoils { get; } = new();

        /// <summary>
        /// Shuttle设备控制线圈集合，用于读写状态展示和控制
        /// </summary>
        public ObservableCollection<ModbusCoil> ShuttleCoils { get; } = new();

        /// <summary>
        /// Robot设备控制线圈集合，用于读写状态展示和控制
        /// </summary>
        public ObservableCollection<ModbusCoil> RobotCoils { get; } = new();

        /// <summary>
        /// Cha设备控制线圈集合，用于读写状态展示和控制
        /// </summary>
        public ObservableCollection<ModbusCoil> ChaCoils { get; } = new();

        /// <summary>
        /// Chb设备控制线圈集合，用于读写状态展示和控制
        /// </summary>
        public ObservableCollection<ModbusCoil> ChbCoils { get; } = new();
    }

    /// <summary>
    /// I/O点实体信息，需要添加到 ModbusCoil
    /// </summary>
    public class IoPoint
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int Item { get; set; }

        /// <summary>
        /// I/O代码
        /// </summary>
        public string IoCode { get; set; }

        /// <summary>
        /// I/O类型（DI/RS485等）
        /// </summary>
        public string IoType { get; set; }

        /// <summary>
        /// 内容描述
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}