# RTZ轴位置访问功能说明

## 概述

在S200McuCmdService中新增了RTZ轴位置访问功能，提供了便捷的实时轴位置获取接口。这些功能基于现有的`RobotAlarmRegisters`实时数据，无需额外的数据更新机制。

## 功能特性

### 1. 实时数据访问
- 直接访问`RobotAlarmRegisters`中的实时位置数据
- 无缓存，每次访问都获取最新值
- 基于现有的定时器更新机制，确保数据实时性

### 2. 便捷的属性接口
- 提供直观的属性名称，无需记住数组索引
- 支持步进值和物理单位值的获取
- 提供组合信息的一次性获取

### 3. 物理单位转换
- T轴：步进值转换为角度（度）
- R轴：步进值转换为长度（mm）
- Z轴：步进值转换为高度（mm）

## 新增接口

### IS200McuCmdService接口新增属性

```csharp
// 步进值属性
int CurrentTAxisStep { get; }           // T轴当前步进位置值
int CurrentRAxisStep { get; }           // R轴当前步进位置值  
int CurrentZAxisStep { get; }           // Z轴当前步进位置值

// 物理单位属性
double CurrentTAxisDegree { get; }      // T旋转轴角度值（度）
double CurrentRAxisLength { get; }      // R伸缩轴长度值（mm）
double CurrentZAxisHeight { get; }      // Z上下轴高度值（mm）

// 组合信息方法
(int TAxisStep, int RAxisStep, int ZAxisStep) GetCurrentRTZSteps();
(double TAxisDegree, double RAxisLength, double ZAxisHeight) GetCurrentRTZPhysicalValues();

// 数据有效性检查
bool IsRTZPositionDataValid { get; }
```

## 转换公式

### T轴（旋转轴）
- **公式**: `角度(度) = 步进值 / 100000 * 360`
- **说明**: 步进马达100000Step旋转360°

### R轴（伸缩轴）
- **公式**: `长度(mm) = Sin(步进值/50000*360) * 2 * 208.96`
- **说明**: 基于正弦函数的伸缩计算

### Z轴（上下轴）
- **公式**: `高度(mm) = 步进值 / 1000 * 5`
- **说明**: 1000step/圈，导程5mm

## 使用示例

### 基本用法

```csharp
// 获取服务实例
var mcuService = S200McuCmdService.Instance;

// 检查数据有效性
if (mcuService.IsRTZPositionDataValid)
{
    // 获取步进值
    int tAxisStep = mcuService.CurrentTAxisStep;
    int rAxisStep = mcuService.CurrentRAxisStep;
    int zAxisStep = mcuService.CurrentZAxisStep;
    
    // 获取物理单位值
    double tAxisDegree = mcuService.CurrentTAxisDegree;
    double rAxisLength = mcuService.CurrentRAxisLength;
    double zAxisHeight = mcuService.CurrentZAxisHeight;
    
    Console.WriteLine($"T轴: {tAxisStep} steps ({tAxisDegree:F2}°)");
    Console.WriteLine($"R轴: {rAxisStep} steps ({rAxisLength:F2}mm)");
    Console.WriteLine($"Z轴: {zAxisStep} steps ({zAxisHeight:F2}mm)");
}
```

### 组合信息获取

```csharp
// 获取步进值组合
var (t, r, z) = mcuService.GetCurrentRTZSteps();
Console.WriteLine($"RTZ轴步进值: T={t}, R={r}, Z={z}");

// 获取物理值组合
var (tDeg, rLen, zHeight) = mcuService.GetCurrentRTZPhysicalValues();
Console.WriteLine($"RTZ轴物理值: T={tDeg:F2}°, R={rLen:F2}mm, Z={zHeight:F2}mm");
```

### 实时监控

```csharp
// 定时监控位置变化
var timer = new System.Timers.Timer(1000); // 1秒间隔
timer.Elapsed += (s, e) =>
{
    if (mcuService.IsRTZPositionDataValid)
    {
        var currentSteps = mcuService.GetCurrentRTZSteps();
        Console.WriteLine($"当前位置: T={currentSteps.TAxisStep}, R={currentSteps.RAxisStep}, Z={currentSteps.ZAxisStep}");
    }
};
timer.Start();
```

## 数据来源

### 寄存器映射
- **T轴位置**: `RobotAlarmRegisters[3].Combinevalue` (地址0x103-0x104)
- **R轴位置**: `RobotAlarmRegisters[5].Combinevalue` (地址0x105-0x106)
- **Z轴位置**: `RobotAlarmRegisters[7].Combinevalue` (地址0x107-0x108)

### 数据格式
- **32位ABCD格式**: 高位在低地址，低位在高地址
- **有符号整数**: 支持正负值
- **实时更新**: 通过定时器从Modbus设备读取

## 实现优势

1. **向后兼容**: 不影响现有的`RobotAlarmRegisters`访问方式
2. **实时性**: 基于现有的实时更新机制
3. **便捷性**: 提供直观的属性名称和物理单位转换
4. **性能**: 直接访问属性，无额外开销
5. **类型安全**: 强类型接口定义
6. **可扩展**: 易于添加更多轴或转换功能

## 注意事项

1. **设备连接**: 确保Robot设备已连接
2. **监控启动**: 确保已调用`StartAlarmMonitoring()`启动报警监控
3. **数据有效性**: 使用前检查`IsRTZPositionDataValid`属性
4. **线程安全**: 属性访问是线程安全的
5. **精度**: 物理单位转换使用double类型，注意浮点精度

## 相关文件

- **接口定义**: `Services/Interfaces/IS200McuCmdService.cs`
- **实现代码**: `Services/S200McuCmdService.cs`
- **使用示例**: `Docs/Examples/RTZAxisPositionAccessExample.cs`
- **测试代码**: `Docs/Test/RTZAxisPositionAccessTest.cs`

## 后续扩展

这个实现为后续在SS200InterLockMain中集成RTZ轴位置信息提供了基础，可以通过以下方式进一步扩展：

1. 在SS200InterLockMain中添加RTZ轴位置访问器
2. 集成到统一的InterLock访问接口
3. 支持RTZ轴位置移动控制功能
4. 添加位置变化事件通知机制
