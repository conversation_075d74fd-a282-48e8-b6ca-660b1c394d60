# 线圈查找解决方案

## 概述

本解决方案提供了一种更好的方法来通过设备类型和对应的DI、DO枚举类型获取线圈实体对象，用于简化`SubSystemStatus\Shuttle\ShuttleSlotStatusManager.cs`和`SubSystemStatus\Chamber\ChamberSubsystemStatus.cs`中的状态计算逻辑。

## 核心组件

### 1. IOEnumExtensions.cs
枚举扩展方法类，提供以下功能：
- 获取枚举的设备类型
- 获取枚举的线圈地址
- 获取枚举的描述信息

### 2. CoilLookupHelper.cs
线圈查找助手类，提供以下功能：
- 通过设备类型和DI枚举获取DI线圈
- 通过设备类型和DO枚举获取DO线圈
- 缓存机制提高查找性能

## 使用方法

### 基本用法

```csharp
// 获取Shuttle设备的DI线圈
var diCoil = CoilLookupHelper.GetDICoil(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1);

// 获取Chamber设备的DO线圈
var doCoil = CoilLookupHelper.GetDOCoil(EnuMcuDeviceType.Chamber, EnuChamberDOCodes.PDO1);

// 检查线圈状态
if (diCoil?.Value == true)
{
    // 处理DI信号为真的情况
}
```

### 在状态管理器中的应用

```csharp
public class ShuttleSlotStatusManager
{
    public void UpdateSlotStatus()
    {
        // 使用新的线圈查找方法
        var sdi6 = CoilLookupHelper.GetDICoil(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI6);
        var sdi7 = CoilLookupHelper.GetDICoil(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI7);
        
        // 更新状态
        LSD1 = sdi6?.Value ?? false;
        LSD2 = sdi7?.Value ?? false;
    }
}
```

## 优势

1. **类型安全**：使用强类型枚举，避免硬编码字符串
2. **性能优化**：内置缓存机制，减少重复查找
3. **代码简洁**：统一的API接口，简化调用代码
4. **易于维护**：集中管理线圈查找逻辑
5. **扩展性强**：支持新的设备类型和枚举

## 实现细节

### 枚举扩展方法

```csharp
public static class IOEnumExtensions
{
    public static EnuMcuDeviceType GetDeviceType<T>(this T enumValue) where T : Enum
    {
        // 根据枚举类型返回对应的设备类型
    }
    
    public static int GetCoilAddress<T>(this T enumValue) where T : Enum
    {
        // 根据枚举值返回对应的线圈地址
    }
}
```

### 线圈查找助手

```csharp
public static class CoilLookupHelper
{
    private static readonly Dictionary<string, object> _cache = new();
    
    public static DICoilEntity GetDICoil<T>(EnuMcuDeviceType deviceType, T diEnum) where T : Enum
    {
        // 实现DI线圈查找逻辑
    }
    
    public static DOCoilEntity GetDOCoil<T>(EnuMcuDeviceType deviceType, T doEnum) where T : Enum
    {
        // 实现DO线圈查找逻辑
    }
}
```

## 测试验证

### 单元测试

```csharp
[Test]
public void TestCoilLookup()
{
    // 测试DI线圈查找
    var diCoil = CoilLookupHelper.GetDICoil(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1);
    Assert.IsNotNull(diCoil);
    Assert.AreEqual("SDI1", diCoil.Name);
    
    // 测试DO线圈查找
    var doCoil = CoilLookupHelper.GetDOCoil(EnuMcuDeviceType.Chamber, EnuChamberDOCodes.PDO1);
    Assert.IsNotNull(doCoil);
    Assert.AreEqual("PDO1", doCoil.Name);
}
```

### 性能测试

```csharp
[Test]
public void TestPerformance()
{
    var stopwatch = Stopwatch.StartNew();
    
    for (int i = 0; i < 10000; i++)
    {
        var coil = CoilLookupHelper.GetDICoil(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1);
    }
    
    stopwatch.Stop();
    Console.WriteLine($"10000次查找耗时: {stopwatch.ElapsedMilliseconds}ms");
}
```

## 迁移指南

### 从旧方法迁移

**旧方法：**
```csharp
var sdi1 = _interLock.CoilStatusHelper.GetDICoilByDeviceTypeAndName(EnuMcuDeviceType.Shuttle, "SDI1");
```

**新方法：**
```csharp
var sdi1 = CoilLookupHelper.GetDICoil(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1);
```

### 批量替换建议

1. 使用IDE的查找替换功能
2. 正则表达式模式匹配
3. 逐步迁移，确保测试通过

## 配置要求

### 枚举定义要求

```csharp
public enum EnuShuttleDICodes
{
    [Description("Shuttle DI 1")]
    [CoilAddress(1)]
    SDI1 = 1,
    
    [Description("Shuttle DI 2")]
    [CoilAddress(2)]
    SDI2 = 2,
    // ...
}
```

### 依赖注入配置

```csharp
// 在Startup.cs或Program.cs中注册
services.AddSingleton<ICoilLookupService, CoilLookupService>();
```

## 错误处理

### 异常类型

1. **EnumNotSupportedException**：不支持的枚举类型
2. **CoilNotFoundException**：找不到对应的线圈
3. **DeviceTypeMismatchException**：设备类型不匹配

### 错误处理示例

```csharp
try
{
    var coil = CoilLookupHelper.GetDICoil(EnuMcuDeviceType.Shuttle, EnuShuttleDICodes.SDI1);
    if (coil == null)
    {
        Logger.Warning("未找到指定的线圈");
        return;
    }
    
    // 使用线圈
    var value = coil.Value;
}
catch (EnumNotSupportedException ex)
{
    Logger.Error($"不支持的枚举类型: {ex.Message}");
}
catch (Exception ex)
{
    Logger.Error($"线圈查找异常: {ex.Message}");
}
```

## 扩展计划

### 未来功能

1. **异步查找**：支持异步线圈查找
2. **批量查找**：一次查找多个线圈
3. **状态监听**：线圈状态变化通知
4. **配置热更新**：运行时更新线圈配置

### 版本规划

- **v1.0**：基础查找功能
- **v1.1**：缓存优化
- **v1.2**：异常处理完善
- **v2.0**：异步支持和批量操作

## 总结

线圈查找解决方案通过统一的API和强类型枚举，显著简化了线圈访问逻辑，提高了代码的可维护性和性能。建议在新的开发中优先使用此方案，并逐步迁移现有代码。
