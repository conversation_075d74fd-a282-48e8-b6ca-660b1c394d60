ASP4
PD lift pin down
	chamber trigger status review
MPS1~MPS2
		MPS1 IDLE
no alarm
			chamber run status review
MPS3~MPS5
				MPS4 / MPS3A
					chamber lift pin status review
SP4~SP6
						SP5
lift pin down
							command done
						others status
							PAC14 ALARM
						SP4 / SP6
							slit door status review
SP1 / SP2
								SP1
slit door open
									robot run status review
MRS1~3
										MRS1 or MRS3
robot run status is busy or alarm
											PAC16 ALARM
										MRS2
robot run status is idle
											robot R-axis status review
RS10~18 or others status
												one position of (RS10~17)
													robot T-axis status review
RS1~9 or others status
														one position of (RS1~9)
															PDO12=0
PDO13=1
																SP4---SP6---SP5
slit door status change logic
																	PPS3≤time≤PPS4
																		command done
																	time>PPS4
																		PAC12 ALARM
																	time<PPS3
																		PAC13 ALARM
																others status change logic
																	PAC10 ALARM
														others status
															PAC15 ALARM
												RS18
robot R-axis at zero position
													PDO12=0
PDO13=1
														SP4---SP6---SP5
slit door status change logic
															PPS3≤time≤PPS4
																command done
															time>PPS4
																PAC12 ALARM
															time<PPS3
																PAC13 ALARM
														others status change logic
															PAC10 ALARM
												others status
													PAC15 ALARM
								SP2
slit door close
									PDO12=0
PDO13=1
										SP4---SP6---SP5
slit door status change logic
											PPS3≤time≤PPS4
												command done
											time>PPS4
												PAC12 ALARM
											time<PPS3
												PAC13 ALARM
										others status change logic
											PAC10 ALARM
				MPS3B
					PAC2 ALARM
				MPS5
					PAC3 ALARM
		MPS2
alarm
			PAC1 ALARM