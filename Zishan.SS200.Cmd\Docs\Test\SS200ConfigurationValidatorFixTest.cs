using System;
using System.Linq;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Tests
{
    /// <summary>
    /// SS200ConfigurationValidator修复验证测试
    /// 验证RA31-RA67报警代码访问器是否正确添加
    /// </summary>
    public class SS200ConfigurationValidatorFixTest
    {
        /// <summary>
        /// 测试修复后的Robot报警代码验证
        /// </summary>
        public static void TestRobotAlarmCodesFix()
        {
            try
            {
                Console.WriteLine("=== SS200ConfigurationValidator修复验证测试 ===");
                Console.WriteLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine();

                // 创建验证器实例
                var validator = new SS200ConfigurationValidator();
                
                Console.WriteLine("1. 测试Robot报警代码验证（修复后）...");
                var result = validator.ValidateAlarmCodes();
                
                Console.WriteLine($"验证结果: {(result.IsValid ? "✅ 通过" : "❌ 失败")}");
                Console.WriteLine($"错误数量: {result.Errors.Count}");
                Console.WriteLine($"警告数量: {result.Warnings.Count}");
                Console.WriteLine($"信息数量: {result.InfoMessages.Count}");
                Console.WriteLine();

                // 分析验证结果
                AnalyzeValidationResults(result);
                
                Console.WriteLine("2. 测试GetAllRobotAlarmAccessors方法...");
                TestGetAllRobotAlarmAccessors();
                
                Console.WriteLine("\n=== 修复验证测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 分析验证结果
        /// </summary>
        private static void AnalyzeValidationResults(SS200ConfigurationValidator.ValidationResult result)
        {
            Console.WriteLine("验证结果分析:");
            
            // 统计RA31-RA66相关的警告
            var missingAccessorWarnings = result.Warnings
                .Where(w => w.Contains("在代码中没有对应的访问器"))
                .ToList();
                
            var inconsistentWarnings = result.Warnings
                .Where(w => w.Contains("描述不一致") || w.Contains("数值不一致"))
                .ToList();
                
            var missingJsonWarnings = result.Warnings
                .Where(w => w.Contains("在JSON文件中不存在"))
                .ToList();

            Console.WriteLine($"  缺少访问器警告: {missingAccessorWarnings.Count}");
            Console.WriteLine($"  描述/数值不一致警告: {inconsistentWarnings.Count}");
            Console.WriteLine($"  JSON中缺失警告: {missingJsonWarnings.Count}");
            
            // 显示前5个缺少访问器的警告（如果有）
            if (missingAccessorWarnings.Count > 0)
            {
                Console.WriteLine("\n前5个缺少访问器的警告:");
                for (int i = 0; i < Math.Min(5, missingAccessorWarnings.Count); i++)
                {
                    Console.WriteLine($"    {i + 1}. {missingAccessorWarnings[i]}");
                }
                if (missingAccessorWarnings.Count > 5)
                {
                    Console.WriteLine($"    ... 还有 {missingAccessorWarnings.Count - 5} 个类似警告");
                }
            }
            else
            {
                Console.WriteLine("  ✅ 没有发现缺少访问器的警告！");
            }
            
            // 显示验证通过的信息统计
            var passedInfos = result.InfoMessages
                .Where(m => m.Contains("验证通过"))
                .ToList();
                
            Console.WriteLine($"  验证通过的项目: {passedInfos.Count}");
        }

        /// <summary>
        /// 测试GetAllRobotAlarmAccessors方法
        /// </summary>
        private static void TestGetAllRobotAlarmAccessors()
        {
            try
            {
                var interlock = SS200InterLockMain.Instance;
                var validator = new SS200ConfigurationValidator();
                
                // 使用反射调用私有方法
                var method = typeof(SS200ConfigurationValidator)
                    .GetMethod("GetAllRobotAlarmAccessors", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (method != null)
                {
                    var accessors = method.Invoke(validator, new object[] { interlock }) 
                        as System.Collections.Generic.List<(string code, object accessor)>;
                    
                    if (accessors != null)
                    {
                        Console.WriteLine($"GetAllRobotAlarmAccessors返回了 {accessors.Count} 个访问器");
                        
                        // 检查是否包含RA31-RA67
                        var ra31to67 = accessors.Where(a => 
                        {
                            if (int.TryParse(a.code.Substring(2), out int num))
                            {
                                return num >= 31 && num <= 67;
                            }
                            return false;
                        }).ToList();
                        
                        Console.WriteLine($"其中RA31-RA67范围内的访问器: {ra31to67.Count} 个");
                        
                        if (ra31to67.Count == 37) // RA31到RA67共37个
                        {
                            Console.WriteLine("✅ RA31-RA67访问器数量正确！");
                        }
                        else
                        {
                            Console.WriteLine($"❌ RA31-RA67访问器数量不正确，预期37个，实际{ra31to67.Count}个");
                        }
                        
                        // 显示前5个和后5个RA31-RA67访问器
                        if (ra31to67.Count > 0)
                        {
                            Console.WriteLine("\n前5个RA31-RA67访问器:");
                            for (int i = 0; i < Math.Min(5, ra31to67.Count); i++)
                            {
                                Console.WriteLine($"    {ra31to67[i].code}");
                            }
                            
                            if (ra31to67.Count > 5)
                            {
                                Console.WriteLine("\n后5个RA31-RA67访问器:");
                                for (int i = Math.Max(0, ra31to67.Count - 5); i < ra31to67.Count; i++)
                                {
                                    Console.WriteLine($"    {ra31to67[i].code}");
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine("❌ GetAllRobotAlarmAccessors返回null");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 未找到GetAllRobotAlarmAccessors方法");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试GetAllRobotAlarmAccessors时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 快速验证测试
        /// </summary>
        public static void QuickValidationTest()
        {
            try
            {
                Console.WriteLine("=== 快速验证测试 ===");
                
                var validator = new SS200ConfigurationValidator();
                var result = validator.ValidateAlarmCodes();
                
                var missingAccessorCount = result.Warnings
                    .Count(w => w.Contains("在代码中没有对应的访问器"));
                
                Console.WriteLine($"缺少访问器的警告数量: {missingAccessorCount}");
                
                if (missingAccessorCount == 0)
                {
                    Console.WriteLine("✅ 修复成功！没有发现缺少访问器的警告");
                }
                else
                {
                    Console.WriteLine($"⚠️ 仍有 {missingAccessorCount} 个缺少访问器的警告");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 快速验证测试失败: {ex.Message}");
            }
        }
    }
}
