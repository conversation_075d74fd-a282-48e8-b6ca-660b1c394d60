# PinSearch失败自动重试机制

## 📋 概述

本文档描述了PinSearch操作失败时的自动重试处理机制。当PinSearch命令执行失败时，系统会自动执行一次重试：重新移动Z轴到PinSearch高度，再完整执行一次PinSearch流程。

## 🎯 功能特性

### 自动重试流程

当PinSearch命令执行失败时，系统会自动执行以下步骤：

1. **记录失败信息**
   - 记录失败的返回值和响应信息
   - 输出警告日志，说明开始自动重试

2. **重新移动Z轴到PinSearch高度**
   - 获取PinSearch高度参数
   - 执行Z轴移动命令
   - 验证移动结果

3. **再次执行PinSearch命令**
   - 获取PinSearch参数
   - 执行PinSearch命令
   - 检查执行结果

4. **如果重试成功，执行完整的成功后处理流程**
   - 保存Pin Search结果值（P1、P2值及基准值）
   - 重新移动Z轴到PinSearch高度
   - R轴回原点

5. **如果重试仍然失败**
   - 记录重试失败信息
   - 返回失败结果

## 🔧 实现原理

### 自动重试逻辑

```csharp
else
{
    // PinSearch命令执行失败，自动重试一次
    UILogService.AddWarningLog($"{endType}端 PinSearch命令执行失败，返回值: 0x{cmdResult.ReturnInfo:X4}, 响应: {cmdResult.Response}");
    UILogService.AddLog($"自动重试：重新移动Z轴到PinSearch高度，完整执行一次PinSearch流程");

    // 第一步：重新移动Z轴到PinSearch高度
    // 第二步：再次执行PinSearch命令
    // 第三步：如果成功，执行完整的成功后处理流程
    // 第四步：如果失败，返回失败结果
}
```

### 重试流程详细实现

#### 1. 失败检测和日志记录
```csharp
UILogService.AddWarningLog($"{endType}端 PinSearch命令执行失败，返回值: 0x{cmdResult.ReturnInfo:X4}, 响应: {cmdResult.Response}");
UILogService.AddLog($"自动重试：重新移动Z轴到PinSearch高度，完整执行一次PinSearch流程");
```

#### 2. Z轴重新定位
```csharp
var pinSearchHeight = RobotPositionParameters.GetZAxisPinSearchPosition();
var zAxisResult = await cmdService.ExecuteRobotZAxisCommandAsync(pinSearchHeight);
```

#### 3. PinSearch命令重新执行
```csharp
var retryCmdResult = await S200McuCmdServiceExtensions.ExecuteDeviceCommandAsync(cmdService,
    EnuMcuDeviceType.Robot,
    EnuRobotCmdIndex.PinSearch.ToString(),
    parameters);
```

#### 4. 成功后的完整处理流程
```csharp
if (retryCmdResult.ReturnInfo == 0)
{
    // 保存Pin Search结果值
    // 重新移动Z轴到PinSearch高度
    // R轴回原点
    return (true, "成功完成Pin Search测试（自动重试后成功）", pinSearchP1Value, pinSearchP2Value);
}
```

## 📊 流程图

```
PinSearch命令执行
        ↓
    执行成功？
    ↙     ↘
  是       否
  ↓        ↓
正常流程   弹出对话框
          ↓
      用户选择？
    ↙   ↓   ↘
 Retry 继续  停止
   ↓    ↓    ↓
递归调用 完整  返回失败
整个流程 重做
        ↓
    Z轴移动→PinSearch→成功处理
    (保存值→Z轴复位→R轴回零)
```

## 🔍 Continue选项详细流程

### 第一步：重新移动Z轴到PinSearch高度
```csharp
var pinSearchHeight = RobotPositionParameters.GetZAxisPinSearchPosition();
var zAxisResult = await cmdService.ExecuteRobotZAxisCommandAsync(pinSearchHeight);
```

### 第二步：再次执行PinSearch命令
```csharp
var retryCmdResult = await S200McuCmdServiceExtensions.ExecuteDeviceCommandAsync(cmdService,
    EnuMcuDeviceType.Robot,
    EnuRobotCmdIndex.PinSearch.ToString(),
    parameters);
```

### 第三步：成功后的完整处理流程
1. **触发寄存器更新**
   ```csharp
   await cmdService.RefreshAlarmRegistersAsync();
   ```

2. **获取和保存Pin Search结果值**
   ```csharp
   int pinSearchP1Value = pinSearchP1Reg?.Combinevalue ?? 0;
   int pinSearchP2Value = pinSearchP2Reg?.Combinevalue ?? 0;
   cmdService.SmoothBasePinSearchValue = (pinSearchP1Value + pinSearchP2Value) / 2;
   ```

3. **重新移动Z轴到PinSearch高度**
   ```csharp
   var zAxisFinalResult = await cmdService.ExecuteRobotZAxisCommandAsync(pinSearchHeightFinal);
   ```

4. **R轴回原点**
   ```csharp
   var rAxisResult = await ZeroRAxisAsync(cmdService);
   ```

## 🎯 使用场景

### 适用情况

1. **设备通信异常**：PinSearch命令发送失败
2. **机械故障**：Pin Search机械动作异常
3. **传感器问题**：Pin Search传感器读取异常
4. **参数错误**：PinSearch参数设置不当

### 用户操作指导

1. **选择Retry**：当怀疑是系统性问题，需要重新初始化整个流程时
2. **选择继续**：当认为只是PinSearch命令执行失败，但前置条件正常时
3. **选择停止**：当确认存在硬件问题需要人工干预时

## 📝 注意事项

1. **完整性**：Continue选项确保执行完整的成功后处理流程
2. **一致性**：重试后的处理流程与正常成功流程完全一致
3. **日志记录**：每个步骤都有详细的日志记录
4. **错误处理**：每个步骤都有完整的错误处理机制

## 🔧 技术细节

### 变量名冲突处理
```csharp
// 原始PinSearch中使用
int lowestStepForPinSearch = RobotConfigureSettings.GetPinSearchLowestStep();

// Continue重试中使用
int retryLowestStepForPinSearch = RobotConfigureSettings.GetPinSearchLowestStep();
```

### UI线程安全
```csharp
var dialogResult = await Application.Current.Dispatcher.InvokeAsync(() =>
{
    // 创建和显示对话框
});
```

这个机制确保了PinSearch失败时用户有灵活的处理选择，特别是Continue选项提供了完整的重做流程，与成功处理逻辑保持一致。
