using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Models.SS200;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// Redis数据保存功能使用示例
    /// 演示如何使用完善的SavaDataToRedis方法进行数据保存
    /// </summary>
    public class RedisDataSavingExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RedisDataSavingExample));
        private DispatcherTimer _autoSaveTimer;

        /// <summary>
        /// 演示基本的数据保存功能
        /// </summary>
        public async Task DemonstrateBasicDataSaving()
        {
            try
            {
                _logger.Info("=== Redis数据保存基本示例 ===");

                // 检查Redis连接状态
                if (!RedisHelper.IsConnected)
                {
                    _logger.Error("Redis连接未建立，无法进行数据保存演示");
                    return;
                }

                _logger.Info("Redis连接正常，开始演示数据保存功能...");

                // 1. 完整数据保存（默认选项）
                _logger.Info("--- 完整数据保存 ---");
                var success = await SaveCompleteData();
                _logger.Info($"完整数据保存结果: {(success ? "成功" : "失败")}");

                // 2. 选择性数据保存
                _logger.Info("--- 选择性数据保存 ---");
                success = await SaveSelectiveData();
                _logger.Info($"选择性数据保存结果: {(success ? "成功" : "失败")}");

                // 3. 仅保存关键数据
                _logger.Info("--- 关键数据保存 ---");
                success = await SaveCriticalDataOnly();
                _logger.Info($"关键数据保存结果: {(success ? "成功" : "失败")}");

                _logger.Info("Redis数据保存演示完成");
            }
            catch (Exception ex)
            {
                _logger.Error("Redis数据保存演示失败", ex);
            }
        }

        /// <summary>
        /// 完整数据保存示例
        /// </summary>
        private async Task<bool> SaveCompleteData()
        {
            try
            {
                // 模拟调用ViewModel中的方法
                // 注意：这里需要在实际的ViewModel实例中调用
                // return await viewModel.SavaDataToRedis();

                // 示例：手动构建数据并保存
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var interlock = SS200InterLockMain.Instance;

                var completeData = new
                {
                    Timestamp = timestamp,
                    DeviceInfo = new
                    {
                        DeviceType = "SS200",
                        Version = "1.0.0",
                        Location = "Production Line"
                    },
                    SubsystemStatus = new
                    {
                        Robot = interlock.SubsystemStatus.Robot.Status,
                        ChamberA = interlock.SubsystemStatus.ChamberA.Status,
                        ChamberB = interlock.SubsystemStatus.ChamberB.Status,
                        Shuttle = interlock.SubsystemStatus.Shuttle.Status
                    },
                    RTZAxisPosition = new
                    {
                        TAxis = interlock.RTZAxisPosition.CurrentTAxisStep,
                        RAxis = interlock.RTZAxisPosition.CurrentRAxisStep,
                        ZAxis = interlock.RTZAxisPosition.CurrentZAxisStep
                    }
                };

                // 保存到Redis
                var success = RedisHelper.StringSet("SS200:Complete:Example", completeData, 60,
                    RedisFolderEnum.Root, RedisDBEnum.Six);

                _logger.Info($"完整数据保存: {(success ? "成功" : "失败")}");
                return success;
            }
            catch (Exception ex)
            {
                _logger.Error("完整数据保存失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 选择性数据保存示例
        /// </summary>
        private async Task<bool> SaveSelectiveData()
        {
            try
            {
                // 根据业务需求选择保存的数据类型
                // 例如：生产环境不保存配置数据，测试环境保存所有数据
                bool isProductionMode = App.AppIniConfig.DatabaseAccessType == EnuDatabaseAccessType.Product_In;

                // 这里应该调用实际的ViewModel方法
                // return await viewModel.SavaDataToRedis(
                //     includeIOData: true,
                //     includeAlarmData: true,
                //     includeConfigData: !isProductionMode, // 生产环境不保存配置
                //     includeAxisData: true
                // );

                _logger.Info($"选择性保存配置: 生产模式={isProductionMode}");
                return true; // 示例返回
            }
            catch (Exception ex)
            {
                _logger.Error("选择性数据保存失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 仅保存关键数据示例
        /// </summary>
        private async Task<bool> SaveCriticalDataOnly()
        {
            try
            {
                // 仅保存最关键的状态和轴位置数据
                // return await viewModel.SavaDataToRedis(
                //     includeIOData: false,
                //     includeAlarmData: false,
                //     includeConfigData: false,
                //     includeAxisData: true
                // );

                _logger.Info("仅保存关键数据（状态和轴位置）");
                return true; // 示例返回
            }
            catch (Exception ex)
            {
                _logger.Error("关键数据保存失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 演示自动定时保存功能
        /// </summary>
        public void DemonstrateAutoSaving()
        {
            try
            {
                _logger.Info("=== 自动定时保存演示 ===");

                // 创建定时器，每30秒保存一次数据
                _autoSaveTimer = new DispatcherTimer();
                _autoSaveTimer.Interval = TimeSpan.FromSeconds(30);
                _autoSaveTimer.Tick += async (sender, e) => await AutoSaveTimerTick();

                _logger.Info("启动自动保存定时器，间隔: 30秒");
                _autoSaveTimer.Start();
            }
            catch (Exception ex)
            {
                _logger.Error("自动定时保存设置失败", ex);
            }
        }

        /// <summary>
        /// 自动保存定时器事件处理
        /// </summary>
        private async Task AutoSaveTimerTick()
        {
            try
            {
                if (!RedisHelper.IsConnected)
                {
                    _logger.Warn("Redis连接断开，跳过自动保存");
                    return;
                }

                _logger.Info("执行自动数据保存...");

                // 这里应该调用实际的保存方法
                // var success = await viewModel.SavaDataToRedis();
                var success = await SaveCompleteData(); // 示例调用

                if (success)
                {
                    _logger.Info("自动数据保存成功");
                }
                else
                {
                    _logger.Warn("自动数据保存失败");
                }
            }
            catch (Exception ex)
            {
                _logger.Error("自动保存过程中发生异常", ex);
            }
        }

        /// <summary>
        /// 演示条件保存功能
        /// </summary>
        public async Task DemonstrateConditionalSaving()
        {
            try
            {
                _logger.Info("=== 条件保存演示 ===");

                var interlock = SS200InterLockMain.Instance;

                // 条件1：仅在Robot状态为Busy时保存详细数据
                if (interlock.SubsystemStatus.Robot.Status.EnuRobotStatus == Models.SS200.SubSystemStatus.Robot.EnuRobotStatus.Busy)
                {
                    _logger.Info("Robot忙碌状态，保存详细数据");
                    await SaveCompleteData();
                }

                // 条件2：仅在有报警时保存报警数据
                // if (HasActiveAlarms())
                // {
                //     _logger.Info("检测到活跃报警，保存报警数据");
                //     await SaveAlarmDataOnly();
                // }

                // 条件3：定期保存配置数据（例如每小时一次）
                var now = DateTime.Now;
                if (now.Minute == 0) // 每小时的0分钟
                {
                    _logger.Info("定期保存配置数据");
                    // await SaveConfigDataOnly();
                }

                _logger.Info("条件保存检查完成");
            }
            catch (Exception ex)
            {
                _logger.Error("条件保存演示失败", ex);
            }
        }

        /// <summary>
        /// 停止自动保存
        /// </summary>
        public void StopAutoSaving()
        {
            try
            {
                if (_autoSaveTimer != null)
                {
                    _autoSaveTimer.Stop();
                    _autoSaveTimer = null;
                    _logger.Info("自动保存定时器已停止");
                }
            }
            catch (Exception ex)
            {
                _logger.Error("停止自动保存失败", ex);
            }
        }

        /// <summary>
        /// 演示数据查询功能
        /// </summary>
        public async Task DemonstrateDataQuerying()
        {
            try
            {
                _logger.Info("=== 数据查询演示 ===");

                // 查询当前状态
                var currentStatus = RedisHelper.StringGet<object>("SS200:Status:Current", RedisFolderEnum.Root, RedisDBEnum.Six);
                _logger.Info($"当前状态查询结果: {(currentStatus != null ? "有数据" : "无数据")}");

                // 查询时间序列数据
                var timeSeriesCount = RedisHelper.Manager.GetDatabase(6).SortedSetLength("SS200:Timeline:Status");
                _logger.Info($"时间序列数据条数: {timeSeriesCount}");

                // 查询最新的几条时间序列数据
                var latestData = RedisHelper.Manager.GetDatabase(6).SortedSetRangeByScore("SS200:Timeline:Status",
                    order: StackExchange.Redis.Order.Descending, take: 5);
                _logger.Info($"最新5条时间序列数据: {latestData.Length}条");

                _logger.Info("数据查询演示完成");
            }
            catch (Exception ex)
            {
                _logger.Error("数据查询演示失败", ex);
            }
        }
    }
}