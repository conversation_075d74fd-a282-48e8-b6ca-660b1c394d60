# 忽略编译输出目录
**/bin/
**/obj/

# 忽略IDE文件
**/.vs/
**/.idea/
**/.vscode/

# 忽略包管理器目录
**/packages/
**/node_modules/

# 忽略临时文件
**/*.tmp
**/*.temp
**/*.bak
**/*.log
**/*.cache

# 忽略大型媒体文件
**/*.mp4
**/*.avi
**/*.mov
**/*.wmv
**/*.flv
**/*.mkv
**/*.iso
**/*.zip
**/*.rar
**/*.7z
**/*.tar
**/*.gz

# 忽略大型图片文件
**/*.psd
**/*.ai
**/*.tif
**/*.tiff

# 忽略数据库文件
**/*.mdf
**/*.ldf
**/*.sqlite

# 忽略用户特定文件
**/*.user
**/*.suo

# 忽略文档目录中的大型文件，但保留文本文件
Docs/**/*.pdf
Docs/**/*.docx
Docs/**/*.pptx
Docs/**/*.xlsx
Docs/**/备份/
Docs/**/Report/

# 忽略大型测试数据
**/TestData/

# 忽略特定目录
Assets/Images/

# 忽略临时文件和备份
*.swp
*~

# 忽略日志文件
*.log

# 忽略配置文件备份
Configs/Log4netConfig/备份/ 