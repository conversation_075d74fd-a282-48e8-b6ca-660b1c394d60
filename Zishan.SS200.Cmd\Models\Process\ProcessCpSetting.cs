using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.Process;

//using Zishan.Robot.Shared.AttributeExtend;
//using Zishan.Robot.Shared.Enums;
//using Zishan.SS200.Cmd.Enums.UI.Process;

namespace Zishan.SS200.Cmd.Models.Process
{
    /// <summary>
    /// CP腔体工艺流程参数
    /// </summary>
    public class ProcessCpSetting : BindableBase
    {
        #region 当前运行反馈

        /// <summary>
        /// 当前步计时
        /// </summary>
        public TimeSpan CurrentStepTiming { get => _currentStepTiming; set => SetProperty(ref _currentStepTiming, value); }
        private TimeSpan _currentStepTiming;

        /// <summary>
        /// 温度检查值冷板L，单位：C
        /// </summary>

        public double TemperatureCheckValueL { get => _temperatureCheckValueL; set => SetProperty(ref _temperatureCheckValueL, value); }
        private double _temperatureCheckValueL;

        /// <summary>
        /// 温度_冷板L，单位：℃
        /// </summary>

        public double TemperatureL { get => _temperatureL; set => SetProperty(ref _temperatureL, value); }
        private double _temperatureL;

        /// <summary>
        /// 温度检查值冷板R，单位：C
        /// </summary>

        public double TemperatureCheckValueR { get => _temperatureCheckValueR; set => SetProperty(ref _temperatureCheckValueR, value); }
        private double _temperatureCheckValueR;

        /// <summary>
        /// 温度_冷板R，单位：C
        /// </summary>

        public double TemperatureR { get => _temperatureR; set => SetProperty(ref _temperatureR, value); }
        private double _temperatureR;

        #endregion 当前运行反馈

        #region 当前配方输出

        /// <summary>
        /// 当前步号
        /// </summary>

        public int CurrentStepNumber { get => _currentStepNumber; set => SetProperty(ref _currentStepNumber, value); }
        private int _currentStepNumber;

        /// <summary>
        /// GoToNextStep【PLC里面不是枚举类型】
        /// 0	No
        /// 1	Yes
        /// </summary>

        public double GoToNextStep { get => _goToNextStep; set => SetProperty(ref _goToNextStep, value); }
        private double _goToNextStep;

        /// <summary>
        /// 冷却时间，单位：s
        /// </summary>

        public double CoolingTime { get => _coolingTime; set => SetProperty(ref _coolingTime, value); }
        private double _coolingTime;

        /// <summary>
        /// 温度检查值，单位：C
        /// </summary>

        public double RecipeTemperatureCheckValue { get => _recipeTemperatureCheckValue; set => SetProperty(ref _recipeTemperatureCheckValue, value); }
        private double _recipeTemperatureCheckValue;

        /// <summary>
        /// 当前配方名称
        /// </summary>

        public string CurrentRecipeName { get => _currentRecipeName; set => SetProperty(ref _currentRecipeName, value); }
        private string _currentRecipeName;

        /// <summary>
        /// 预备配方名称
        /// </summary>

        public string PreRecipeName { get => _preRecipeName; set => SetProperty(ref _preRecipeName, value); }
        private string _preRecipeName;

        #endregion 当前配方输出

        #region 单元状态

        /// <summary>
        /// 单元状态
        /// 0	Idle	单元空闲
        /// 1	Busy	单元动作中
        /// 2	Process	单元工艺中
        /// 10	Alarm	单元故障
        /// 20	Null	单元不存在
        /// </summary>

        public EnuUnitWorkStatus UnitStatus { get => _unitStatus; set => SetProperty(ref _unitStatus, value); }
        private EnuUnitWorkStatus _unitStatus;

        #endregion 单元状态

        #region 运行控制

        /// <summary>
        /// 运行状态
        /// </summary>

        public bool IsBusy { get => _isBusy; set => SetProperty(ref _isBusy, value); }
        private bool _isBusy;

        /// <summary>
        /// 运行结果
        /// </summary>

        public bool IsDone { get => _isDone; set => SetProperty(ref _isDone, value); }
        private bool _isDone;

        /// <summary>
        /// 运行控制
        /// </summary>

        public bool Start { get => _start; set => SetProperty(ref _start, value); }
        private bool _start;

        /// <summary>
        /// 停止控制
        /// </summary>

        public bool Stop { get => _stop; set => SetProperty(ref _stop, value); }
        private bool _stop;

        #endregion 运行控制
    }
}