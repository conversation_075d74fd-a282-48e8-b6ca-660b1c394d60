﻿namespace Zishan.SS200.Cmd.Models.IR400
{
    public class RobotCmdDecompose
    {
        public string Cmd { get; set; }
        public string Parameter1 { get; set; }
        public string Parameter2 { get; set; }
        public string Parameter3 { get; set; }

        public int ParameterCount { get; set; }

        public bool IsFullCmdFinished()
        {
            int curCount = 0;
            if (!string.IsNullOrEmpty(Parameter1))
                curCount++;
            if (!string.IsNullOrEmpty(Parameter2))
                curCount++;
            if (!string.IsNullOrEmpty(Parameter3))
                curCount++;
            return curCount == ParameterCount;
        }

        public string FullCmd
        {
            get { return ToString(); }
        }

        public void Clear()
        {
            Cmd = string.Empty;
            Parameter1 = string.Empty;
            Parameter2 = string.Empty;
            Parameter3 = string.Empty;
        }

        public override string ToString()
        {
            return $"{Cmd} {Parameter1},{Parameter2},{Parameter3}".Trim(',');
        }
    }
}