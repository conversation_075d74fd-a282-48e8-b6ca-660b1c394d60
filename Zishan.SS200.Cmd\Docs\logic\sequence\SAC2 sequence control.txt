SAC2
run sequence SN xx
	main system status review
SS1 / SS2
		SS1 automatic mode
			main system trigger status review
SS15 / SS16
				SS15 alarm
					SLR11 ALARM
				SS16 normal
					SSD1/SSD2?
						SSD2/SHTL1
							LSS5~8=0
paddle slot status all no wafer
								SQL1
ban PL1==1 or not
									SQL1==0, add sequence unfinished wafer to sequence again
										check lot sequence finished
sequence[SQ3]
											all sequence[SQ3]==2
												command done
												LSS9-LSS16=0
													sequence done，SHTL1 unload ready
SS51
											others
												Check first wafer[xx].PL1==0 in sequence[SQ3]， return XX
													SAC3 (W[xx])
									SQL1==1, NOT add sequence unfinished wafer to sequence again
										check lot sequence finished
sequence[SQ3]
											all sequence[SQ3]!=0
												command done
											others
												Check first wafer[xx].PL1==0 in suqence[SQ3]， returen XX
													SAC3 (W[xx])
							(LSS5 xx=1 or/and LSS6 xx=1) or (LSS7 xx=1 or/and LSS8 xx=1)
ther is/are wafer(s) on smooth or nose paddle
								W[XX].re.PL2[SQ7+1]
check XX last recipe done or not
									W[XX].re.PL2[SQ7+1]==2
									W[XX].re.PL2[W[XX].SQ6]==0
										W[XX].re.SQ7++;
W[XX].SQ6++
						SSD1/SHTL2
							LSS5~8=0
paddle slot status all no wafer
								SQL1
ban PL1==1 or not
									SQL1==0, add sequence unfinished wafer to sequence again
										check lot sequence finished
sequence[SQ3]
											all sequence[SQ3]==2
												command done
												LSS9-LSS16=0
													sequence done，SHTL2 unload ready
SS52
											others
												Check first wafer[xx].PL1==0 in sequence[SQ3]， return XX
													SAC3 (W[xx])
									SQL1==1, NOT add sequence unfinished wafer to sequence again
										check lot sequence finished
sequence[SQ3]
											all sequence[SQ3]!=0
												command done
											others
												Check first wafer[xx].PL1==0 in suqence[SQ3]， returen XX
													SAC3 (W[xx])
							(LSS5 xx=1 or/and LSS6 xx=1) or (LSS7 xx=1 or/and LSS8 xx=1)
ther is/are wafer(s) on smooth or nose paddle
								W[XX].re.PL2[SQ7+1]
check XX last recipe done or not
									W[XX].re.PL2[SQ7+1]==2
									W[XX].re.PL2[W[XX].SQ6]==0
										W[XX].re.SQ7++;
W[XX].SQ6++
		SS2 manual mode
			SLR2 ALARM