using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Docs.Examples
{
    /// <summary>
    /// Robot晶圆取放操作示例
    /// 演示完善后的晶圆取放操作的使用方法
    /// </summary>
    public class WaferOperationsExample
    {
        private readonly IS200McuCmdService _cmdService;

        public WaferOperationsExample(IS200McuCmdService cmdService)
        {
            _cmdService = cmdService;
        }

        /// <summary>
        /// 示例1：从晶圆盒取片到工艺腔体
        /// </summary>
        public async Task Example1_CassetteToChambersAsync()
        {
            Console.WriteLine("=== 示例1：从晶圆盒取片到工艺腔体 ===\n");

            try
            {
                // 1. 从晶圆盒Slot 5取片（使用Nose端）
                Console.WriteLine("步骤1：从晶圆盒Slot 5取片...");
                var getResult = await _cmdService.GetWaferAsync(
                    EnuRobotEndType.Nose,
                    EnuLocationStationType.Cassette,
                    5);

                if (!getResult.Success)
                {
                    Console.WriteLine($"❌ 取片失败: {getResult.Message}");
                    return;
                }
                Console.WriteLine($"✅ 取片成功: {getResult.Message}\n");

                // 2. 放片到ChamberA
                Console.WriteLine("步骤2：放片到ChamberA...");
                var putResult = await _cmdService.PutWaferAsync(
                    EnuRobotEndType.Nose,
                    EnuLocationStationType.ChamberA);

                if (!putResult.Success)
                {
                    Console.WriteLine($"❌ 放片失败: {putResult.Message}");
                    return;
                }
                Console.WriteLine($"✅ 放片成功: {putResult.Message}\n");

                Console.WriteLine("🎉 示例1完成：晶圆已从晶圆盒Slot 5传输到ChamberA\n");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 示例1异常: {ex.Message}\n");
            }
        }

        /// <summary>
        /// 示例2：工艺腔体间晶圆传输
        /// </summary>
        public async Task Example2_ChamberToChamberAsync()
        {
            Console.WriteLine("=== 示例2：工艺腔体间晶圆传输 ===\n");

            try
            {
                // 1. 从ChamberA取片（使用Smooth端）
                Console.WriteLine("步骤1：从ChamberA取片...");
                var getResult = await _cmdService.GetWaferAsync(
                    EnuRobotEndType.Smooth,
                    EnuLocationStationType.ChamberA);

                if (!getResult.Success)
                {
                    Console.WriteLine($"❌ 取片失败: {getResult.Message}");
                    return;
                }
                Console.WriteLine($"✅ 取片成功: {getResult.Message}\n");

                // 2. 放片到ChamberB
                Console.WriteLine("步骤2：放片到ChamberB...");
                var putResult = await _cmdService.PutWaferAsync(
                    EnuRobotEndType.Smooth,
                    EnuLocationStationType.ChamberB);

                if (!putResult.Success)
                {
                    Console.WriteLine($"❌ 放片失败: {putResult.Message}");
                    return;
                }
                Console.WriteLine($"✅ 放片成功: {putResult.Message}\n");

                Console.WriteLine("🎉 示例2完成：晶圆已从ChamberA传输到ChamberB\n");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 示例2异常: {ex.Message}\n");
            }
        }

        /// <summary>
        /// 示例3：冷却腔操作
        /// </summary>
        public async Task Example3_CoolingChamberOperationsAsync()
        {
            Console.WriteLine("=== 示例3：冷却腔操作 ===\n");

            try
            {
                // 1. 从ChamberA取片
                Console.WriteLine("步骤1：从ChamberA取片...");
                var getFromChamberResult = await _cmdService.GetWaferAsync(
                    EnuRobotEndType.Nose,
                    EnuLocationStationType.ChamberA);

                if (!getFromChamberResult.Success)
                {
                    Console.WriteLine($"❌ 从ChamberA取片失败: {getFromChamberResult.Message}");
                    return;
                }
                Console.WriteLine($"✅ 从ChamberA取片成功: {getFromChamberResult.Message}\n");

                // 2. 放片到CoolingTop
                Console.WriteLine("步骤2：放片到CoolingTop...");
                var putToCoolingTopResult = await _cmdService.PutWaferAsync(
                    EnuRobotEndType.Nose,
                    EnuLocationStationType.CoolingTop);

                if (!putToCoolingTopResult.Success)
                {
                    Console.WriteLine($"❌ 放片到CoolingTop失败: {putToCoolingTopResult.Message}");
                    return;
                }
                Console.WriteLine($"✅ 放片到CoolingTop成功: {putToCoolingTopResult.Message}\n");

                // 等待冷却
                Console.WriteLine("步骤3：等待冷却...");
                await Task.Delay(2000); // 模拟冷却时间
                Console.WriteLine("✅ 冷却完成\n");

                // 3. 从CoolingTop取片
                Console.WriteLine("步骤4：从CoolingTop取片...");
                var getFromCoolingResult = await _cmdService.GetWaferAsync(
                    EnuRobotEndType.Nose,
                    EnuLocationStationType.CoolingTop);

                if (!getFromCoolingResult.Success)
                {
                    Console.WriteLine($"❌ 从CoolingTop取片失败: {getFromCoolingResult.Message}");
                    return;
                }
                Console.WriteLine($"✅ 从CoolingTop取片成功: {getFromCoolingResult.Message}\n");

                // 4. 放片到晶圆盒
                Console.WriteLine("步骤5：放片到晶圆盒Slot 10...");
                var putToCassetteResult = await _cmdService.PutWaferAsync(
                    EnuRobotEndType.Nose,
                    EnuLocationStationType.Cassette,
                    10);

                if (!putToCassetteResult.Success)
                {
                    Console.WriteLine($"❌ 放片到晶圆盒失败: {putToCassetteResult.Message}");
                    return;
                }
                Console.WriteLine($"✅ 放片到晶圆盒成功: {putToCassetteResult.Message}\n");

                Console.WriteLine("🎉 示例3完成：晶圆已完成冷却流程并返回晶圆盒\n");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 示例3异常: {ex.Message}\n");
            }
        }

        /// <summary>
        /// 示例4：双端口操作演示
        /// </summary>
        public async Task Example4_DualEndOperationsAsync()
        {
            Console.WriteLine("=== 示例4：双端口操作演示 ===\n");

            try
            {
                // 并行操作：Nose端和Smooth端同时工作
                Console.WriteLine("开始并行操作...");

                var noseTask = Task.Run(async () =>
                {
                    Console.WriteLine("Nose端：从晶圆盒Slot 1取片...");
                    var getResult = await _cmdService.GetWaferAsync(
                        EnuRobotEndType.Nose,
                        EnuLocationStationType.Cassette,
                        1);

                    if (getResult.Success)
                    {
                        Console.WriteLine("Nose端：放片到CoolingTop...");
                        var putResult = await _cmdService.PutWaferAsync(
                            EnuRobotEndType.Nose,
                            EnuLocationStationType.CoolingTop);
                        return putResult;
                    }
                    return getResult;
                });

                var smoothTask = Task.Run(async () =>
                {
                    Console.WriteLine("Smooth端：从CoolingBottom取片...");
                    var getResult = await _cmdService.GetWaferAsync(
                        EnuRobotEndType.Smooth,
                        EnuLocationStationType.CoolingBottom);

                    if (getResult.Success)
                    {
                        Console.WriteLine("Smooth端：放片到晶圆盒Slot 20...");
                        var putResult = await _cmdService.PutWaferAsync(
                            EnuRobotEndType.Smooth,
                            EnuLocationStationType.Cassette,
                            20);
                        return putResult;
                    }
                    return getResult;
                });

                // 等待两个任务完成
                var results = await Task.WhenAll(noseTask, smoothTask);

                Console.WriteLine($"Nose端操作结果: {(results[0].Success ? "✅ 成功" : "❌ 失败")} - {results[0].Message}");
                Console.WriteLine($"Smooth端操作结果: {(results[1].Success ? "✅ 成功" : "❌ 失败")} - {results[1].Message}");

                if (results[0].Success && results[1].Success)
                {
                    Console.WriteLine("🎉 示例4完成：双端口并行操作成功\n");
                }
                else
                {
                    Console.WriteLine("⚠️ 示例4部分失败\n");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 示例4异常: {ex.Message}\n");
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public async Task RunAllExamplesAsync()
        {
            Console.WriteLine("🚀 开始运行Robot晶圆取放操作示例...\n");

            await Example1_CassetteToChambersAsync();
            await Example2_ChamberToChamberAsync();
            await Example3_CoolingChamberOperationsAsync();
            await Example4_DualEndOperationsAsync();

            Console.WriteLine("✨ 所有示例运行完成！");
            Console.WriteLine("\n📝 总结：");
            Console.WriteLine("1. ✅ 增强了安全检查机制");
            Console.WriteLine("2. ✅ 完善了晶圆状态验证");
            Console.WriteLine("3. ✅ 优化了操作流程");
            Console.WriteLine("4. ✅ 支持多种位置类型");
            Console.WriteLine("5. ✅ 提供详细的错误处理");
        }
    }
}
