# 晶圆搬运性能监控功能说明

## 📋 概述

本文档说明了为晶圆搬运操作添加的全面性能监控功能，通过 `StopwatchHelper` 类实现分层性能监控，帮助开发者和运维人员：

- 🎯 **快速定位性能瓶颈**
- 📊 **建立性能基线和趋势分析**  
- ⚠️ **及时发现性能退化**
- 🔧 **为性能优化提供数据支撑**

## 🚀 性能监控架构

### 分层监控策略

我们采用了四层性能监控架构，从粗粒度到细粒度全面覆盖：

```
1. 整体传输流程监控 (TrasferWaferAsync)
   ├── 2. 循环级别监控 (ExecuteTrasferWaferLoopLogicAsync)
   │   └── 3. 单次搬运监控 (ExecuteSingleTrasferWaferAsync)
   │       ├── 4a. 机器人初始化监控 (InitializeRobotAsync)
   │       ├── 4b. GetWafer 操作监控 (GetWaferAsync)
   │       └── 4c. PutWafer 操作监控 (PutWaferAsync)
```

### 性能阈值设置

| 监控层级 | 警告阈值 | 说明 |
|---------|---------|------|
| 整体传输流程 | 30000ms (30秒) | 完整的晶圆传输操作 |
| 循环级别 | 40000ms (40秒) | 包含UI更新的单次循环 |
| 单次搬运 | 35000ms (35秒) | 纯搬运操作时间 |
| 机器人初始化 | 15000ms (15秒) | 三轴回零操作 |
| GetWafer 操作 | 12000ms (12秒) | 从源位置获取晶圆 |
| PutWafer 操作 | 12000ms (12秒) | 将晶圆放到目标位置 |
| API 调用 | 5000ms (5秒) | 单个 API 调用 |

## 📁 涉及的文件

### 核心文件

1. **`Extensions/RobotWaferOperationsExtensions.cs`**
   - 整体传输流程性能监控
   - GetWafer 和 PutWafer 操作监控
   - 机器人初始化监控

2. **`ViewModels/BasicCommandTestViewModel.cs`**
   - 循环级别性能监控
   - 单次搬运性能监控

3. **`Models/IR400/RobotArmNew.cs`**
   - TransferWafer 方法整体监控
   - GetWafer 和 PutWafer 操作的详细监控

4. **`Common/StopwatchHelper.cs`**
   - 性能监控核心工具类
   - 支持同步和异步操作监控

## 🔧 实现细节

### 1. 整体传输流程监控

在 `RobotWaferOperationsExtensions.cs` 中的 `TrasferWaferAsync` 方法：

```csharp
// 🔥 整体晶圆传输性能监控开始
var transferStopwatch = new StopwatchHelper($"晶圆传输总流程-{endType}端-{sourceStationType}(Slot:{sourceSlotNumber})→{targetStationType}(Slot:{targetSlotNumber})");
transferStopwatch.Start();

try
{
    // 传输操作...
    
    // 🔥 整体晶圆传输性能监控结束 - 成功
    transferStopwatch.Stop(warnThresholdMs: 30000);
    return (true, "成功完成传输");
}
catch (Exception ex)
{
    // 🔥 整体晶圆传输性能监控结束 - 异常
    transferStopwatch.Stop(warnThresholdMs: 30000);
    throw;
}
```

### 2. 分步骤监控

#### 机器人初始化监控
```csharp
// 🔥 机器人初始化性能监控
var initStopwatch = new StopwatchHelper($"机器人初始化(三轴回零)-{endType}端");
initStopwatch.Start();

var initResult = await InitializeRobotAsync(cmdService, cancellationToken);

initStopwatch.Stop(warnThresholdMs: 15000);
```

#### GetWafer 操作监控
```csharp
// 🔥 GetWafer 操作性能监控
var getWaferStopwatch = new StopwatchHelper($"GetWafer操作-{endType}端-{sourceStationType}(Slot:{sourceSlotNumber})");
getWaferStopwatch.Start();

var getResult = await GetWaferAsync(cmdService, endType, sourceStationType, sourceSlotNumber, cancellationToken);

getWaferStopwatch.Stop(warnThresholdMs: 12000);
```

#### PutWafer 操作监控
```csharp
// 🔥 PutWafer 操作性能监控
var putWaferStopwatch = new StopwatchHelper($"PutWafer操作-{endType}端-{targetStationType}(Slot:{targetSlotNumber})");
putWaferStopwatch.Start();

var putResult = await PutWaferAsync(cmdService, endType, targetStationType, targetSlotNumber, cancellationToken);

putWaferStopwatch.Stop(warnThresholdMs: 12000);
```

### 3. 循环级别监控

在 `BasicCommandTestViewModel.cs` 中：

```csharp
// 🔥 循环级别晶圆搬运性能监控
var loopStopwatch = new StopwatchHelper($"晶圆搬运循环-第{currentLoop}次-{SelectedFromChamber?.ChamberName}→{SelectedToChamber?.ChamberName}");
loopStopwatch.Start();

try
{
    // 执行搬运操作...
    
    // 🔥 循环级别性能监控结束 - 成功
    loopStopwatch.Stop(warnThresholdMs: 40000);
    return handleResult;
}
catch (Exception ex)
{
    // 🔥 循环级别性能监控结束 - 异常
    loopStopwatch.Stop(warnThresholdMs: 40000);
    throw;
}
```

## 📊 日志输出格式

### 性能日志示例

```
2024-01-15 10:30:15.123 [性能计时结果] 操作: 晶圆传输总流程-Nose端-Cassette(Slot:1)→ChamberA(Slot:1) | 耗时: 25245.67ms (25秒245毫秒) | 位置: RobotWaferOperationsExtensions.cs:TrasferWaferAsync:52
```

### 警告日志示例

```
2024-01-15 10:30:20.456 [性能警告] 操作: GetWafer操作-Nose端-Cassette(Slot:1) | 耗时: 15234.56ms (15秒234毫秒) | 位置: RobotWaferOperationsExtensions.cs:GetWaferAsync:104 | 超过阈值: 12000ms
```

### 异常日志示例

```
2024-01-15 10:30:25.789 [性能计时异常] 操作: PutWafer操作-Nose端-ChamberA(Slot:1) | 耗时: 8567.89ms (8秒567毫秒) | 异常: Connection timeout
```

## 🎯 使用场景

### 1. 性能基线建立

通过收集正常运行时的性能数据，建立各个操作的性能基线：

- 不同腔体间的搬运时间差异
- 不同 Slot 位置的影响
- 机械臂端口（Nose/Smooth）的性能差异

### 2. 性能瓶颈识别

当某个操作超过警告阈值时，可以快速定位：

- 是整体流程慢还是某个步骤慢
- 是机器人初始化慢还是实际搬运慢
- 是 GetWafer 慢还是 PutWafer 慢

### 3. 性能趋势分析

通过长期收集性能数据，可以：

- 发现设备老化导致的性能退化
- 识别特定时间段的性能异常
- 为设备维护提供数据支撑

## ⚠️ 注意事项

1. **性能监控开销**：性能监控本身会有微小的开销，但相比于搬运操作的时间（通常几秒到几十秒），这个开销可以忽略不计。

2. **日志文件大小**：长期运行会产生大量性能日志，建议定期清理或归档。

3. **阈值调整**：根据实际设备性能和业务需求，可能需要调整警告阈值。

4. **异常处理**：所有性能监控都包含了异常处理，确保即使在异常情况下也能记录性能数据。

## 🔄 后续优化建议

1. **性能数据可视化**：可以考虑将性能数据导出到图表工具进行可视化分析。

2. **自动化报告**：定期生成性能报告，包含平均耗时、最大耗时、异常次数等统计信息。

3. **动态阈值调整**：根据历史数据自动调整警告阈值。

4. **性能对比**：不同设备或不同时间段的性能对比分析。

## 📝 版本信息

- **实现日期**: 2025-01-23
- **实现版本**: v1.0
- **负责模块**: 晶圆搬运模块
- **依赖组件**: StopwatchHelper、UILogService、RobotWaferOperationsExtensions
