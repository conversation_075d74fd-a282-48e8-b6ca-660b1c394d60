# Robot取放片操作规则详细说明

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| **文档名称** | Robot取放片操作规则详细说明 |
| **版本** | v1.2 |
| **创建日期** | 2025-01-17 |
| **适用系统** | SS200半导体设备控制系统 |
| **文档类型** | 操作手册 |
| **维护部门** | 技术开发部 |

## 📖 目录

- [1. 概述](#1-概述)
- [2. 系统架构](#2-系统架构)
- [3. 操作模式详解](#3-操作模式详解)
  - [3.1 工艺腔体模式](#31-工艺腔体模式)
  - [3.2 晶圆盒模式](#32-晶圆盒模式)
  - [3.3 冷却腔模式](#33-冷却腔模式)
- [4. 安全注意事项](#4-安全注意事项)
- [5. 技术参数](#5-技术参数)
- [6. 故障排除](#6-故障排除)
- [7. 版本历史](#7-版本历史)

## 1. 概述

Robot取放片操作是SS200半导体设备控制系统的核心功能之一，负责在不同工位之间精确、安全地传输晶圆。本文档详细描述了Robot系统的三种主要操作模式及其具体流程。

### 1.1 主要功能
- **精确定位**：通过T、R、Z三轴协调运动实现精确定位
- **安全传输**：内置InterLock安全检查机制
- **多模式支持**：支持工艺腔体、晶圆盒、冷却腔三种操作模式
- **状态监控**：实时监控传感器状态和位置反馈

### 1.2 适用范围
- 工艺腔体 (ChamberA、ChamberB)
- 晶圆盒 (Cassette)
- 冷却腔 (CoolingTop、CoolingBottom)
  - CoolingTop：冷却腔上层
  - CoolingBottom：冷却腔下层

## 2. 系统架构

### 2.1 Robot轴系统
- **T轴 (Theta)**：旋转轴，控制Robot朝向
- **R轴 (Radius)**：径向轴，控制伸缩距离
- **Z轴 (Vertical)**：垂直轴，控制高度位置

### 2.2 端口类型
- **Nose端**：前端操作端口
- **Smooth端**：后端操作端口

## 3. 操作模式详解

## 3.1 工艺腔体模式

### 🔧 **特点**
R轴伸入腔体的Z轴高度固定，取放片通过腔体内部顶针升降机构完成

### 📥 **取片流程**

| 步骤 | 操作内容 | 关键点 |
|------|----------|--------|
| ① | **预备阶段** | 确认腔体内有Wafer，顶针处于下降位置 |
| ② | **顶针上升** | 腔体内顶针上升，将Wafer顶起到取片高度 |
| ③ | **Robot进入** | T轴旋转到目标腔体方向，R轴伸入腔体到固定取片位置 |
| ④ | **顶针下降** | 顶针缓慢下降，将Wafer平稳放置到Robot机械臂上 |
| ⑤ | **Robot退出** | R轴缩回到原点位置，T轴旋转到安全角度 |
| ⑥ | **完成取片** | Robot机械臂上现在承载着Wafer |

### 📤 **放片流程**

| 步骤 | 操作内容 | 关键点 |
|------|----------|--------|
| ① | **预备阶段** | 确认腔体内无Wafer，顶针处于下降位置，Robot机械臂上有Wafer |
| ② | **Robot进入** | T轴旋转到目标腔体方向，R轴伸入腔体到固定放片位置 |
| ③ | **顶针上升** | 顶针上升，从Robot机械臂下方托起Wafer |
| ④ | **Robot退出** | R轴缩回到原点位置，T轴旋转到安全角度 |
| ⑤ | **顶针下降** | 顶针下降到工艺位置，将Wafer放置到腔体内 |
| ⑥ | **完成放片** | Wafer现在位于腔体内，Robot机械臂空载 |

## 3.2 晶圆盒模式

### 🔧 **特点**
R轴伸入晶圆盒的深度固定，取放片通过Robot的Z轴上下移动完成

### 📥 **取片流程**

| 步骤 | 操作内容 | 关键点 |
|------|----------|--------|
| ① | **预备阶段** | 确认目标Slot有Wafer，Z轴移动到该Slot的取片高度 |
| ② | **Robot进入** | T轴旋转到Cassette方向，R轴伸入晶圆盒到固定深度 |
| ③ | **Z轴上升** | Z轴向上移动，Robot机械臂从Wafer下方托起 |
| ④ | **Robot退出** | R轴缩回到原点位置，T轴旋转到安全角度 |
| ⑤ | **完成取片** | Robot机械臂上现在承载着Wafer |

### 📤 **放片流程**

| 步骤 | 操作内容 | 关键点 |
|------|----------|--------|
| ① | **预备阶段** | 确认目标Slot为空，Robot机械臂上有Wafer，Z轴移动到该Slot上方的放片高度 |
| ② | **Robot进入** | T轴旋转到Cassette方向，R轴伸入晶圆盒到固定深度 |
| ③ | **Z轴下降** | Z轴向下移动，将Wafer平稳放置到目标Slot中 |
| ④ | **Robot退出** | R轴缩回到原点位置，T轴旋转到安全角度 |
| ⑤ | **完成放片** | Wafer现在位于目标Slot中，Robot机械臂空载 |

## 3.3 冷却腔模式

### 🔧 **特点**
与Cassette操作原理相同，通过Robot的Z轴上下移动完成取放片。冷却腔有两个独立的层级：CoolingTop（上层）和CoolingBottom（下层）

### 🎯 **层级区分**
- **T轴和R轴**：只需要旋转到CoolingChamber方向，不区分上下层
- **Z轴**：需要精确控制高度，区分CoolingTop和CoolingBottom两个不同的高度位置

### 📥 **取片流程**

#### 🔸 **CoolingTop（上层）取片**

| 步骤 | 操作内容 | 关键点 |
|------|----------|--------|
| ① | **预备阶段** | 确认CoolingTop有Wafer，Z轴移动到上层取片高度 |
| ② | **Robot进入** | T轴旋转到CoolingChamber方向，R轴伸入冷却腔到固定深度 |
| ③ | **Z轴上升** | Z轴向上移动，Robot机械臂从Wafer下方托起 |
| ④ | **Robot退出** | R轴缩回到原点位置，T轴旋转到安全角度 |
| ⑤ | **完成取片** | Robot机械臂上现在承载着Wafer |

#### 🔹 **CoolingBottom（下层）取片**

| 步骤 | 操作内容 | 关键点 |
|------|----------|--------|
| ① | **预备阶段** | 确认CoolingBottom有Wafer，Z轴移动到下层取片高度 |
| ② | **Robot进入** | T轴旋转到CoolingChamber方向，R轴伸入冷却腔到固定深度 |
| ③ | **Z轴上升** | Z轴向上移动，Robot机械臂从Wafer下方托起 |
| ④ | **Robot退出** | R轴缩回到原点位置，T轴旋转到安全角度 |
| ⑤ | **完成取片** | Robot机械臂上现在承载着Wafer |

### 📤 **放片流程**

#### 🔸 **CoolingTop（上层）放片**

| 步骤 | 操作内容 | 关键点 |
|------|----------|--------|
| ① | **预备阶段** | 确认CoolingTop为空，Robot机械臂上有Wafer，Z轴移动到上层放片高度 |
| ② | **Robot进入** | T轴旋转到CoolingChamber方向，R轴伸入冷却腔到固定深度 |
| ③ | **Z轴下降** | Z轴向下移动，将Wafer平稳放置到上层位置 |
| ④ | **Robot退出** | R轴缩回到原点位置，T轴旋转到安全角度 |
| ⑤ | **完成放片** | Wafer现在位于CoolingTop，Robot机械臂空载 |

#### 🔹 **CoolingBottom（下层）放片**

| 步骤 | 操作内容 | 关键点 |
|------|----------|--------|
| ① | **预备阶段** | 确认CoolingBottom为空，Robot机械臂上有Wafer，Z轴移动到下层放片高度 |
| ② | **Robot进入** | T轴旋转到CoolingChamber方向，R轴伸入冷却腔到固定深度 |
| ③ | **Z轴下降** | Z轴向下移动，将Wafer平稳放置到下层位置 |
| ④ | **Robot退出** | R轴缩回到原点位置，T轴旋转到安全角度 |
| ⑤ | **完成放片** | Wafer现在位于CoolingBottom，Robot机械臂空载 |

## 4. 安全注意事项

### 🛡️ **强制性安全检查**

| 检查项目 | 要求 | 说明 |
|----------|------|------|
| **InterLock检查** | 所有操作前必须进行 | 确保系统处于安全状态 |
| **位置状态确认** | 目标位置状态与操作类型匹配 | 取片时确认有片，放片时确认无片 |
| **传感器监控** | 实时监控位置传感器状态 | Robot轴运动过程中持续监控 |
| **异常处理** | 立即停止操作并报警 | 发现异常情况时的应急响应 |
| **状态反馈** | 每个步骤完成后确认 | 确保操作成功完成 |

### ⚠️ **操作警告**
- 禁止在未完成InterLock检查的情况下进行任何Robot操作
- 禁止在传感器状态异常时强制执行操作
- 禁止在系统报警状态下进行晶圆传输
- 禁止手动干预正在执行的自动化操作流程

## 5. 技术参数

### 5.1 轴运动参数
- **T轴旋转范围**：0° - 360°
- **R轴伸缩范围**：根据配置参数确定
- **Z轴升降范围**：根据配置参数确定

### 5.2 定位精度
- **重复定位精度**：±0.1mm
- **角度定位精度**：±0.1°

### 5.3 运动速度
- **T轴旋转速度**：可配置
- **R轴伸缩速度**：可配置
- **Z轴升降速度**：可配置

### 5.4 冷却腔体位置参数

#### 5.4.1 T轴和R轴位置
- **CoolingChamber方向**：T轴和R轴统一使用CoolingChamber枚举值
- **T轴位置**：通过配置参数RP3（Smooth端）或RP7（Nose端）确定
- **R轴位置**：通过配置参数RP14（Smooth端）或RP15（Nose端）确定

#### 5.4.2 Z轴高度位置
- **CoolingTop（上层）**：
  - Smooth端取片高度：配置参数确定
  - Nose端取片高度：配置参数确定
  - 放片高度：取片高度 + RPS24（冷却腔Z轴增量）
- **CoolingBottom（下层）**：
  - Smooth端取片高度：配置参数确定
  - Nose端取片高度：配置参数确定
  - 放片高度：取片高度 + RPS24（冷却腔Z轴增量）

#### 5.4.3 位置状态映射
- **RS12**：T轴和R轴Smooth端冷却腔伸展 → 根据Z轴高度动态判断CoolingTop或CoolingBottom
- **RS16**：T轴和R轴Nose端冷却腔伸展 → 根据Z轴高度动态判断CoolingTop或CoolingBottom
- **RS21**：Smooth端到CT get → SmoothToCTGet
- **RS22**：Smooth端到CB get → SmoothToCBGet
- **RS25**：Nose端到CT get → NoseToCTGet
- **RS26**：Nose端到CB get → NoseToCBGet
- **RS29**：Smooth端到CT put → SmoothToCTPut
- **RS30**：Smooth端到CB put → SmoothToCBPut
- **RS31**：Nose端到CT put → NoseToCTPut
- **RS32**：Nose端到CB put → NoseToCBPut

## 6. 故障排除

### 6.1 常见问题

| 问题现象 | 可能原因 | 解决方案 |
|----------|----------|----------|
| Robot无法移动到目标位置 | InterLock检查失败 | 检查安全条件，排除故障后重试 |
| 取片失败 | 目标位置无晶圆 | 确认晶圆位置，检查传感器状态 |
| 放片失败 | 目标位置已有晶圆 | 确认目标位置状态，清理后重试 |
| 轴运动异常 | 传感器故障或机械卡死 | 检查传感器连接，检查机械部件 |
| 冷却腔层级识别错误 | Z轴高度参数配置错误 | 检查CoolingTop和CoolingBottom的Z轴配置参数 |
| T轴无法定位到冷却腔 | CoolingChamber方向参数错误 | 检查RP3（Smooth）或RP7（Nose）配置参数 |
| R轴伸入冷却腔失败 | R轴伸缩参数配置错误 | 检查RP14（Smooth）或RP15（Nose）配置参数 |
| 冷却腔放片高度不正确 | RPS24增量参数配置错误 | 检查冷却腔Z轴增量配置参数RPS24 |
| RS12/RS16状态识别错误 | Z轴高度与冷却腔层级不匹配 | 检查RP21/RP22（Smooth端）或RP25/RP26（Nose端）Z轴高度参数 |

### 6.2 紧急处理
- 立即按下急停按钮
- 检查系统状态和报警信息
- 联系技术支持人员

## 7. 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-01-17 | 初始版本，完整的操作规则说明 | 系统开发团队 |
| v1.1 | 2025-01-17 | 完善冷却腔体CoolingTop和CoolingBottom的区分说明，添加详细的层级操作流程和技术参数 | 系统开发团队 |
| v1.2 | 2025-01-17 | 修复RS12/RS16状态解析逻辑，现在根据Z轴高度动态判断CoolingTop或CoolingBottom | 系统开发团队 |

---

**注意**：本文档基于SS200系统RobotWaferOperationsExtensions.cs实现，如有技术问题请参考源代码或联系开发团队。
