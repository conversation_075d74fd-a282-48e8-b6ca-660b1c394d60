using System;
using System.Threading.Tasks;
using NModbus;

namespace Zishan.SS200.Cmd.Services
{
    public interface IModbusClientService : IDisposable
    {
        bool IsConnected { get; }

        IModbusMaster Master { get; }

        Task<bool> ConnectAsync(string ipAddress, int port, int timeout = 3000);

        Task DisconnectAsync();

        Task<ushort[]> ReadHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints);

        Task<ushort[]> ReadInputRegistersAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints);

        Task WriteHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort[] values);

        Task WriteSingleRegisterAsync(byte slaveAddress, ushort address, ushort value);

        Task<bool[]> ReadCoilsAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints);

        Task<bool[]> ReadDiscreteInputsAsync(byte slaveAddress, ushort startAddress, ushort numberOfPoints);

        Task WriteCoilsAsync(byte slaveAddress, ushort startAddress, bool[] values);

        Task WriteSingleCoilAsync(byte slaveAddress, ushort address, bool value);
    }
}