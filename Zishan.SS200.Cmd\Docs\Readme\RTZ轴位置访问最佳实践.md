# RTZ轴位置访问最佳实践

## 📋 概述

本文档提供了使用方案二（SS200InterLockMain统一访问入口）进行RTZ轴位置访问的最佳实践指南，帮助开发者正确、高效地使用新的RTZ轴位置访问功能。

## 🎯 核心原则

### 1. 统一访问原则
**始终通过SS200InterLockMain统一入口访问RTZ轴位置信息**

```csharp
// ✅ 正确：统一访问入口
var interlock = SS200InterLockMain.Instance;
int tAxisStep = interlock.RTZAxisPosition.CurrentTAxisStep;

// ❌ 错误：直接访问底层服务
var mcuService = S200McuCmdService.Instance;
int tAxisStep = mcuService.CurrentTAxisStep;
```

### 2. 数据验证原则
**使用前始终检查数据有效性**

```csharp
// ✅ 正确：先验证再使用
var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
if (rtzPosition.IsRTZPositionDataValid)
{
    int tAxisStep = rtzPosition.CurrentTAxisStep;
    // 使用数据
}

// ❌ 错误：直接使用未验证的数据
int tAxisStep = rtzPosition.CurrentTAxisStep; // 可能无效
```

### 3. 安全检查原则
**在执行关键操作前检查轴位置安全性**

```csharp
// ✅ 正确：安全检查
var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
if (rtzPosition.AreAllAxesInSafeRange)
{
    // 执行操作
}

// 单轴检查
if (rtzPosition.IsAxisPositionInSafeRange("T"))
{
    // 执行T轴相关操作
}
```

## 🚀 使用模式

### 1. 基本访问模式

```csharp
public class RobotControlService
{
    private readonly RTZAxisPositionAccessor _rtzPosition;

    public RobotControlService()
    {
        _rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
    }

    public void CheckCurrentPosition()
    {
        if (!_rtzPosition.IsRTZPositionDataValid)
        {
            throw new InvalidOperationException("RTZ轴位置数据无效");
        }

        var (t, r, z) = _rtzPosition.GetCurrentRTZSteps();
        Console.WriteLine($"当前位置: T={t}, R={r}, Z={z}");
    }
}
```

### 2. UI绑定模式

```csharp
public class RobotPositionViewModel : ObservableObject
{
    private static RTZAxisPositionAccessor RTZPosition => 
        SS200InterLockMain.Instance.RTZAxisPosition;

    // 属性绑定
    public int TAxisStep => RTZPosition.CurrentTAxisStep;
    public double TAxisDegree => RTZPosition.CurrentTAxisDegree;
    public bool IsDataValid => RTZPosition.IsRTZPositionDataValid;
    public bool IsAllSafe => RTZPosition.AreAllAxesInSafeRange;

    // 格式化显示
    public string PositionDisplayText => RTZPosition.GetRTZPositionDisplayText();

    // 命令方法
    [RelayCommand]
    private void CopyPosition()
    {
        if (IsDataValid)
        {
            string text = RTZPosition.GetRTZPositionSimpleText();
            Clipboard.SetText(text);
        }
    }
}
```

### 3. 监控模式

```csharp
public class RTZPositionMonitor
{
    private readonly RTZAxisPositionAccessor _rtzPosition;
    private readonly Timer _monitorTimer;

    public RTZPositionMonitor()
    {
        _rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
        _monitorTimer = new Timer(MonitorPosition, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));
    }

    private void MonitorPosition(object state)
    {
        if (!_rtzPosition.IsRTZPositionDataValid)
            return;

        var info = _rtzPosition.GetDetailedPositionInfo();
        
        // 检查安全状态
        if (!info.IsAllAxesSafe)
        {
            OnPositionUnsafe?.Invoke(info);
        }

        // 记录位置历史
        LogPositionHistory(info);
    }

    public event Action<RTZAxisPositionInfo> OnPositionUnsafe;
}
```

## 🔧 性能优化

### 1. 缓存策略

```csharp
public class OptimizedRTZAccess
{
    private readonly RTZAxisPositionAccessor _rtzPosition;
    private RTZAxisPositionInfo _cachedInfo;
    private DateTime _lastCacheTime;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMilliseconds(100);

    public OptimizedRTZAccess()
    {
        _rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
    }

    public RTZAxisPositionInfo GetCachedPositionInfo()
    {
        var now = DateTime.Now;
        if (_cachedInfo == null || (now - _lastCacheTime) > _cacheExpiry)
        {
            _cachedInfo = _rtzPosition.GetDetailedPositionInfo();
            _lastCacheTime = now;
        }
        return _cachedInfo;
    }
}
```

### 2. 批量访问

```csharp
// ✅ 推荐：批量获取
var (t, r, z) = rtzPosition.GetCurrentRTZSteps();
var (tDeg, rLen, zHeight) = rtzPosition.GetCurrentRTZPhysicalValues();

// ❌ 不推荐：多次单独访问
int t = rtzPosition.CurrentTAxisStep;
int r = rtzPosition.CurrentRAxisStep;
int z = rtzPosition.CurrentZAxisStep;
double tDeg = rtzPosition.CurrentTAxisDegree;
double rLen = rtzPosition.CurrentRAxisLength;
double zHeight = rtzPosition.CurrentZAxisHeight;
```

## 🛡️ 错误处理

### 1. 异常处理策略

```csharp
public class SafeRTZAccess
{
    private readonly RTZAxisPositionAccessor _rtzPosition;
    private readonly ILog _logger;

    public SafeRTZAccess()
    {
        _rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
        _logger = LogManager.GetLogger(typeof(SafeRTZAccess));
    }

    public (bool Success, int TAxisStep) GetTAxisStepSafely()
    {
        try
        {
            if (!_rtzPosition.IsRTZPositionDataValid)
            {
                _logger.Warn("RTZ轴位置数据无效");
                return (false, 0);
            }

            int tAxisStep = _rtzPosition.CurrentTAxisStep;
            return (true, tAxisStep);
        }
        catch (Exception ex)
        {
            _logger.Error($"获取T轴位置失败: {ex.Message}", ex);
            return (false, 0);
        }
    }
}
```

### 2. 数据验证

```csharp
public static class RTZPositionValidator
{
    public static bool ValidatePosition(RTZAxisPositionInfo info)
    {
        if (!info.IsDataValid)
            return false;

        // 检查步进值范围
        if (Math.Abs(info.TAxisStep) > 180000) return false;
        if (Math.Abs(info.RAxisStep) > 100000) return false;
        if (Math.Abs(info.ZAxisStep) > 50000) return false;

        // 检查物理值范围
        if (Math.Abs(info.TAxisDegree) > 360) return false;
        if (Math.Abs(info.RAxisLength) > 500) return false;
        if (Math.Abs(info.ZAxisHeight) > 250) return false;

        return true;
    }
}
```

## 📊 调试和诊断

### 1. 诊断信息获取

```csharp
public class RTZDiagnostics
{
    public static void PrintDiagnosticInfo()
    {
        var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
        
        Console.WriteLine("=== RTZ轴位置诊断信息 ===");
        Console.WriteLine(rtzPosition.GetDiagnosticInfo());
        
        Console.WriteLine("\n=== 详细位置信息 ===");
        var info = rtzPosition.GetDetailedPositionInfo();
        Console.WriteLine(info.ToString());
        
        Console.WriteLine("\n=== JSON格式信息 ===");
        Console.WriteLine(rtzPosition.GetRTZPositionJsonText());
    }
}
```

### 2. 性能监控

```csharp
public class RTZPerformanceMonitor
{
    public static void MeasureAccessPerformance()
    {
        var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
        var sw = Stopwatch.StartNew();
        
        const int iterations = 1000;
        
        // 测试基本访问性能
        sw.Restart();
        for (int i = 0; i < iterations; i++)
        {
            var _ = rtzPosition.CurrentTAxisStep;
        }
        sw.Stop();
        Console.WriteLine($"基本访问平均时间: {(double)sw.ElapsedMilliseconds / iterations:F6}ms");
        
        // 测试组合访问性能
        sw.Restart();
        for (int i = 0; i < iterations; i++)
        {
            var _ = rtzPosition.GetCurrentRTZSteps();
        }
        sw.Stop();
        Console.WriteLine($"组合访问平均时间: {(double)sw.ElapsedMilliseconds / iterations:F6}ms");
    }
}
```

## 🧪 测试建议

### 1. 单元测试

```csharp
[TestClass]
public class RTZPositionAccessorTests
{
    private RTZAxisPositionAccessor _rtzPosition;

    [TestInitialize]
    public void Setup()
    {
        _rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
    }

    [TestMethod]
    public void TestDataValidityCheck()
    {
        // 测试数据有效性检查
        bool isValid = _rtzPosition.IsRTZPositionDataValid;
        Assert.IsNotNull(isValid);
    }

    [TestMethod]
    public void TestSafetyChecks()
    {
        if (!_rtzPosition.IsRTZPositionDataValid)
            return;

        // 测试安全检查功能
        bool allSafe = _rtzPosition.AreAllAxesInSafeRange;
        bool tSafe = _rtzPosition.IsAxisPositionInSafeRange("T");
        bool rSafe = _rtzPosition.IsAxisPositionInSafeRange("R");
        bool zSafe = _rtzPosition.IsAxisPositionInSafeRange("Z");

        Assert.AreEqual(allSafe, tSafe && rSafe && zSafe);
    }
}
```

### 2. 集成测试

```csharp
[TestClass]
public class RTZIntegrationTests
{
    [TestMethod]
    public void TestConsistencyWithMcuService()
    {
        var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
        var mcuService = S200McuCmdService.Instance;

        if (!rtzPosition.IsRTZPositionDataValid)
            return;

        // 验证与MCU服务的一致性
        Assert.AreEqual(rtzPosition.CurrentTAxisStep, mcuService.CurrentTAxisStep);
        Assert.AreEqual(rtzPosition.CurrentRAxisStep, mcuService.CurrentRAxisStep);
        Assert.AreEqual(rtzPosition.CurrentZAxisStep, mcuService.CurrentZAxisStep);
    }
}
```

## 📚 常见问题

### Q1: 如何处理数据无效的情况？
```csharp
var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
if (!rtzPosition.IsRTZPositionDataValid)
{
    // 1. 检查设备连接状态
    // 2. 重新启动监控
    // 3. 使用默认值或缓存值
    // 4. 通知用户
}
```

### Q2: 如何优化频繁访问的性能？
```csharp
// 使用缓存策略，避免过于频繁的访问
private static DateTime _lastAccess;
private static RTZAxisPositionInfo _cachedInfo;

public RTZAxisPositionInfo GetOptimizedInfo()
{
    var now = DateTime.Now;
    if ((now - _lastAccess).TotalMilliseconds > 50) // 50ms缓存
    {
        _cachedInfo = rtzPosition.GetDetailedPositionInfo();
        _lastAccess = now;
    }
    return _cachedInfo;
}
```

### Q3: 如何在多线程环境中安全使用？
```csharp
// RTZAxisPositionAccessor本身是线程安全的
// 但在多线程环境中建议使用局部变量
public void ThreadSafeAccess()
{
    var rtzPosition = SS200InterLockMain.Instance.RTZAxisPosition;
    
    // 获取快照，避免多次访问时数据不一致
    var positionSnapshot = rtzPosition.GetDetailedPositionInfo();
    
    // 使用快照数据进行后续处理
    ProcessPosition(positionSnapshot);
}
```

## 🎉 总结

遵循这些最佳实践，可以确保：
- ✅ 正确、安全地访问RTZ轴位置信息
- ✅ 获得最佳的性能表现
- ✅ 编写可维护、可测试的代码
- ✅ 充分利用新架构的优势

记住：**统一访问、数据验证、安全检查** 是使用RTZ轴位置访问器的三大核心原则。
