using System.Collections.ObjectModel;
using System.Collections.Generic;
using Zishan.SS200.Cmd.DTO;
using Zishan.SS200.Cmd.ViewModels;

namespace Zishan.SS200.Cmd.ViewModels.DesignViewModels
{
    public class RunRecipeDesignViewModel : RunRecipeViewModel
    {
        public RunRecipeDesignViewModel()
        {
            LeftBarcode = "123456789";
            RightBarcode = "987654321";
            WaferCountList = new ObservableCollection<int> { 1, 2, 3, 4, 5 };
            RecipeList = new ObservableCollection<string> { "配方A", "配方B", "配方C" };
            LeftSelectedItems = new ObservableCollection<int> { 1, 2 };
            RightSelectedItems = new ObservableCollection<int> { 3, 4 };
            Top = 2;
            Bottom = 3;
        }
    }
} 