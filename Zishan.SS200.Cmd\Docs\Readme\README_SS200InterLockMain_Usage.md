# SS200InterLockMain 统一访问入口使用指南

## 概述

`SS200InterLockMain` 是一个单例模式的统一访问入口，通过工厂模式实现对设备IO、报警信息、配置信息、状态信息的统一访问。

## 设计特点

- **单例模式**: 确保全局唯一实例，线程安全
- **工厂模式**: 为不同类型的访问提供工厂方法
- **统一接口**: 提供一致的访问方式
- **类型安全**: 强类型访问，智能提示友好
- **缓存机制**: 使用 `ConcurrentDictionary` 缓存访问器，提高性能

## 基本用法

### 1. 获取单例实例

```csharp
// 获取SS200InterLockMain的单例实例
var ss200 = SS200InterLockMain.Instance;
```

### 2. IO接口访问

```csharp
// Robot IO访问
bool robotDI1Value = ss200.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;        // 动态计算属性
string robotDI1Content = ss200.IOInterface.Robot.RDI1_PaddleSmoothSensor.Content;  // 预先设置值
bool? robotDI1Nullable = ss200.IOInterface.Robot.RDI1_PaddleSmoothSensor.ValueNullable;
bool robotDI1IsActive = ss200.IOInterface.Robot.RDI1_PaddleSmoothSensor.IsActive;

// Chamber A IO访问
bool chamberDI12Value = ss200.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
string chamberDI12Content = ss200.IOInterface.ChamberA.PDI12_SlitDoorOpenSensor.Content;

// Chamber B IO访问
bool chamberBDI13Value = ss200.IOInterface.ChamberB.PDI13_SlitDoorCloseSensor.Value;

// Shuttle IO访问
bool shuttleDI6Value = ss200.IOInterface.Shuttle.SDI6_Shuttle1Cassette1Sensor.Value;
string shuttleDI6Content = ss200.IOInterface.Shuttle.SDI6_Shuttle1Cassette1Sensor.Content;
```

### 3. 报警信息访问

```csharp
// Robot报警访问
string robotAlarmContent = ss200.AlarmCode.Robot.RA1_SystemBusyReject.Content;      // 英文描述
string robotAlarmChsContent = ss200.AlarmCode.Robot.RA1_SystemBusyReject.ChsContent; // 中文描述
string robotAlarmCause = ss200.AlarmCode.Robot.RA1_SystemBusyReject.Cause;         // 报警原因
string robotAlarmCode = ss200.AlarmCode.Robot.RA1_SystemBusyReject.Code;           // 报警代码
int robotAlarmItem = ss200.AlarmCode.Robot.RA1_SystemBusyReject.Item;              // 报警项ID

// Chamber报警访问
string chamberAlarmContent = ss200.AlarmCode.ChamberA.PAC1_SystemAbnormalReject.Content;
string chamberAlarmChsContent = ss200.AlarmCode.ChamberA.PAC1_SystemAbnormalReject.ChsContent;
```

### 4. 子系统配置访问

```csharp
// 位置值配置访问
int rp1Value = ss200.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Value;     // 配置值
string rp1Content = ss200.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Content; // 配置描述
string rp1Unit = ss200.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Unit;    // 配置单位
string rp1Code = ss200.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Code;    // 配置代码
string rp1Range = ss200.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Range;  // 配置范围

// R轴位置配置
int rp2Value = ss200.SubsystemConfigure.PositionValue.RP2_RAxisPosition.Value;
```

### 5. 子系统状态访问

```csharp
// Robot状态访问
var robotStatus = ss200.SubsystemStatus.Robot.RS1_RobotStatus.Value;               // 动态计算属性
string robotStatusContent = ss200.SubsystemStatus.Robot.RS1_RobotStatus.Content;   // 预先设置值

var tAxisDestination = ss200.SubsystemStatus.Robot.RS2_TAxisSmoothDestination.Value;
string tAxisContent = ss200.SubsystemStatus.Robot.RS2_TAxisSmoothDestination.Content;

// Chamber状态访问
var slitDoorStatus = ss200.SubsystemStatus.ChamberA.SlitDoorStatus.Value;
string slitDoorContent = ss200.SubsystemStatus.ChamberA.SlitDoorStatus.Content;

var liftPinStatus = ss200.SubsystemStatus.ChamberA.LiftPinStatus.Value;
string liftPinContent = ss200.SubsystemStatus.ChamberA.LiftPinStatus.Content;
```

## 高级用法

### 1. 状态更新

```csharp
// 更新Robot状态
var newRobotStatus = new RobotSubsystemStatus
{
    EnuRobotStatus = EnuRobotStatus.Busy,
    EnuTAxisSmoothDestination = EnuTAxisDestination.CHA
};
ss200.UpdateRobotStatus(newRobotStatus);

// 更新Chamber A状态
var newChamberAStatus = new ChamberSubsystemStatus
{
    SlitDoorStatus = EnuSlitDoorStatus.Open,
    LiftPinStatus = EnuLiftPinStatus.Up
};
ss200.UpdateChamberAStatus(newChamberAStatus);
```

### 2. 获取底层服务

```csharp
// 获取CoilStatusHelper实例进行高级操作
var coilHelper = ss200.GetCoilStatusHelper();
bool customValue = coilHelper.GetCoilValue(EnuMcuDeviceType.Robot, EnuRobotDICodes.RDI1_PaddleSmoothSensor);

// 获取MCU命令服务实例
var mcuService = ss200.GetMcuCmdService();
var deviceStatus = mcuService.GetAllDeviceStatus();
```

## 实际应用示例

### 1. 在ViewModel中使用

```csharp
public class RobotControlViewModel : ObservableObject
{
    private readonly SS200InterLockMain _ss200;

    public RobotControlViewModel()
    {
        _ss200 = SS200InterLockMain.Instance;
    }

    public void CheckRobotStatus()
    {
        // 检查Robot状态
        var robotStatus = _ss200.SubsystemStatus.Robot.RS1_RobotStatus.Value;
        
        if (robotStatus == EnuRobotStatus.Busy)
        {
            // 获取忙碌报警信息
            var busyAlarm = _ss200.AlarmCode.Robot.RA1_SystemBusyReject;
            MessageBox.Show($"Robot忙碌: {busyAlarm.ChsContent}");
            return;
        }

        // 检查Paddle传感器
        bool smoothPaddleDetected = _ss200.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;
        bool nosePaddleDetected = _ss200.IOInterface.Robot.RDI2_PaddleNoseSensor.Value;

        if (smoothPaddleDetected)
        {
            Console.WriteLine("Smooth Paddle检测到晶圆");
        }

        if (nosePaddleDetected)
        {
            Console.WriteLine("Nose Paddle检测到晶圆");
        }
    }
}
```

### 2. 在状态监控中使用

```csharp
public class StatusMonitor
{
    private readonly SS200InterLockMain _ss200;
    private readonly Timer _timer;

    public StatusMonitor()
    {
        _ss200 = SS200InterLockMain.Instance;
        _timer = new Timer(MonitorStatus, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));
    }

    private void MonitorStatus(object state)
    {
        // 监控Chamber A状态
        var slitDoorStatus = _ss200.SubsystemStatus.ChamberA.SlitDoorStatus.Value;
        var liftPinStatus = _ss200.SubsystemStatus.ChamberA.LiftPinStatus.Value;

        Console.WriteLine($"Chamber A - Slit Door: {slitDoorStatus}, Lift Pin: {liftPinStatus}");

        // 监控Shuttle状态
        bool shuttle1Cassette1 = _ss200.IOInterface.Shuttle.SDI6_Shuttle1Cassette1Sensor.Value;
        bool shuttle1Cassette2 = _ss200.IOInterface.Shuttle.SDI7_Shuttle1Cassette2Sensor.Value;

        Console.WriteLine($"Shuttle - Cassette1: {shuttle1Cassette1}, Cassette2: {shuttle1Cassette2}");
    }
}
```

## 扩展指南

### 1. 添加新的IO枚举

要添加新的IO枚举访问，需要在对应的访问器类中添加新属性：

```csharp
// 在RobotIOAccessor中添加新的DI
public IOPropertyAccessor<EnuRobotDICodes> RDI5_NewSensor =>
    GetOrCreateAccessor(EnuRobotDICodes.RDI5_NewSensor);
```

### 2. 添加新的报警代码

在对应的报警访问器中添加新属性：

```csharp
// 在RobotAlarmAccessor中添加新的报警
public AlarmPropertyAccessor RA10_NewAlarm =>
    GetOrCreateAccessor(EnuRobotAlarmCodes.RA10);
```

### 3. 添加新的状态枚举

在对应的状态访问器中添加新属性：

```csharp
// 在RobotStatusAccessor中添加新的状态
public StatusPropertyAccessor<EnuNewStatus> RS10_NewStatus =>
    GetOrCreateStatusAccessor("RS10", () => _statusGetter()?.NewStatus ?? EnuNewStatus.None, "新状态描述");
```

## 注意事项

1. **线程安全**: 单例实现是线程安全的，可以在多线程环境中使用
2. **性能优化**: 使用了缓存机制，重复访问同一属性不会重复创建对象
3. **错误处理**: 当访问不存在的报警代码或配置时，会返回null
4. **兼容性**: 保留了原有的属性，确保向后兼容
5. **扩展性**: 设计支持轻松添加新的设备类型和枚举

## 总结

`SS200InterLockMain` 提供了一个统一、类型安全、高性能的访问入口，简化了对SS200设备各种信息的访问。通过单例模式和工厂模式的结合，既保证了全局一致性，又提供了灵活的扩展能力。
