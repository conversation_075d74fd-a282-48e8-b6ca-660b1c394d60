# Zishan.SS200.Cmd 配置和设置机制

## 配置系统概述

Zishan.SS200.Cmd应用程序采用多层次配置机制，包括INI配置文件、JSON配置文件以及工作目录标记文件，灵活支持不同类型的配置需求。配置系统的主要目标是实现以下功能：

1. **通信参数配置**：Modbus服务器IP地址和端口
2. **应用程序行为配置**：如自动启动、调试模式等
3. **设备参数配置**：如超时设置、命令参数等
4. **运行时特性控制**：通过标记文件启用或禁用特定功能

## 配置文件结构

### 1. INI配置文件

主配置文件位于 `Configs/Config.ini`，使用标准INI格式：

```ini
[General] 
;Modbus 服务端IP地址
Ip = 127.0.0.1

;Modbus 服务端端口
Port = 502

;是否自动启动，默认：True
AutoStart = True
```

这种配置格式具有以下特点：
- 简单易读，适合基础配置
- 支持分组（节）和注释
- 便于手动编辑

### 2. JSON配置文件

对于更复杂的参数配置，项目使用JSON格式：

```json
{
  "DeviceParameters": {
    "CommandTimeout": 120,
    "RetryCount": 3,
    "PollingInterval": 100
  },
  "UIConfiguration": {
    "ShowAdvancedOptions": false,
    "DefaultTab": "Shuttle"
  }
}
```

JSON配置提供了以下优势：
- 支持复杂嵌套结构
- 支持多种数据类型
- 便于序列化和反序列化

### 3. 标记文件机制

项目使用特殊的标记文件启用调试和特殊功能：

```csharp
/// <summary>
/// 是否开放调试功能
/// </summary>
[ObservableProperty]
private bool isDebug = File.Exists(Path.Combine(Golbal.WorkRootPath, "debug.tag"));

/// <summary>
/// 是否允许手动点击回复
/// </summary>
[ObservableProperty]
private bool isReply = File.Exists(Path.Combine(Golbal.WorkRootPath, "Reply.tag"));

/// <summary>
/// 是否允许清除命令回复，PLC无法清除，开发调试需要清理
/// </summary>
[ObservableProperty]
private bool isClearCmdResponse = File.Exists(Path.Combine(Golbal.WorkRootPath, "ClearReply.tag"));
```

这种机制的优点：
- 无需修改代码即可启用/禁用功能
- 适合开发和调试场景
- 防止生产环境意外修改配置

## 配置加载过程

### 1. 启动时配置加载

应用程序在启动时加载配置文件，代码位于 `App.xaml.cs` 的 `OnStartup` 方法中：

```csharp
protected override void OnStartup(StartupEventArgs e)
{
    try
    {
        // 配置log4net
        var log4netConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs", "Log4netConfig", "log4net.config");
        XmlConfigurator.Configure(new FileInfo(log4netConfigPath));
        
        //创建日志路径
        var lotRootPath = Path.Combine(Golbal.WorkRootPath + @"\Logs");
        if (!Directory.Exists(lotRootPath))
            Directory.CreateDirectory(lotRootPath);
        AppLog.UpdateFolder(lotRootPath);
        
        Golbal.IsDevDebug = File.Exists(Path.Combine(Golbal.WorkRootPath, "debug.tag"));
        
        var iniFilePath = $@"{Golbal.WorkRootPath}\Config.ini";
        if (!File.Exists(iniFilePath))
        {
            //复制默认Ini配置档
            string strSrcConfig = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs", "Config.ini");
            File.Copy(strSrcConfig, iniFilePath);
        }
        
        //读取Modbus配置文件
        AppIniConfig = PubHelper.GetAppIniConfig(iniFilePath);
        
        // 其他启动逻辑...
    }
    catch (Exception ex)
    {
        _logger.Error("读取App配置文件失败，请联系开发人员！错误信息", ex);
        MessageBox.Show($"读取App配置文件失败，请联系开发人员！\r\n错误信息：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        Shutdown(); // 关闭应用程序
    }
}
```

这一过程确保：
- 在应用程序启动前完成配置加载
- 配置文件不存在时会复制默认配置
- 加载失败时给出明确错误提示

### 2. 配置类模型

配置数据通过专用类模型表示，如 `IniConfig` 类：

```csharp
public class IniConfig
{
    /// <summary>
    /// Modbus 服务端IP地址
    /// </summary>
    public string Ip { get; set; }

    /// <summary>
    /// Modbus 服务端端口
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    /// 是否自动启动，默认：True
    /// </summary>
    public bool AutoStart { get; set; }

    public override string ToString()
    {
        var strMsg = $"Ip: {Ip}, Port: {Port}, AutoStart: {AutoStart}";
        return strMsg;
    }
}
```

### 3. 全局配置访问

通过 `App` 类的静态属性提供全局访问：

```csharp
/// <summary>
/// 应用程序配置信息
/// </summary>
public static IniConfig AppIniConfig = new IniConfig();
```

视图模型中的使用方式：

```csharp
private static readonly string ModbusHost = App.AppIniConfig.Ip;
private static readonly int ModbusPort = App.AppIniConfig.Port;
```

## 全局常量和设置

应用程序在 `Common/Golbal.cs` 中定义全局常量和设置：

```csharp
/// <summary>
/// 定义静态全局变量
/// </summary>
public static class Golbal
{
    /// <summary>
    /// 工作根目录路径,默认：C:\SS200Modbus
    /// </summary>
    public static string WorkRootPath = @"C:\SS200Modbus";

    /// <summary>
    /// 是否为开发调试模式
    /// </summary>
    public static bool IsDevDebug = false;

    /// <summary>
    /// Debug命令运行超时时间，默认：3秒
    /// </summary>
    public static int DebugCommandRunTimeout = 1;

    /// <summary>
    /// 命令运行超时时间,【已通过PLC传过来的超时修改】
    /// </summary>
    public static int CommandRunTimeout = 120;

    // 其他全局设置...
}
```

这种方式的特点：
- 集中管理全局常量
- 提供默认值
- 可在运行时修改部分值

## 配置工具类

项目使用辅助类解析和管理配置文件，例如：

```csharp
/// <summary>
/// 获取应用程序INI配置
/// </summary>
public static IniConfig GetAppIniConfig(string iniPath)
{
    try
    {
        var parser = new FileIniDataParser();
        IniData data = parser.ReadFile(iniPath);

        var config = new IniConfig
        {
            Ip = data["General"]["Ip"],
            Port = int.Parse(data["General"]["Port"]),
            AutoStart = bool.Parse(data["General"]["AutoStart"])
        };

        return config;
    }
    catch (Exception ex)
    {
        throw new Exception($"解析INI配置文件失败: {ex.Message}", ex);
    }
}
```

## 运行时配置修改

某些配置可在运行时通过UI修改，例如：

```csharp
// 设置命令超时时间
public void SetCommandTimeout(int timeoutSeconds)
{
    if (timeoutSeconds > 0 && timeoutSeconds <= 300)
    {
        Golbal.CommandRunTimeout = timeoutSeconds;
        // 可能需要持久化此更改
        SaveSettings();
    }
}

// 保存设置到配置文件
private void SaveSettings()
{
    try
    {
        var parser = new FileIniDataParser();
        IniData data = parser.ReadFile(_iniFilePath);

        // 更新数据
        data["General"]["CommandTimeout"] = Golbal.CommandRunTimeout.ToString();

        // 写入文件
        parser.WriteFile(_iniFilePath, data);
    }
    catch (Exception ex)
    {
        _logger.Error($"保存配置失败: {ex.Message}", ex);
    }
}
```

## 配置验证机制

应用程序实现了配置验证机制，确保配置值有效：

```csharp
private bool ValidateConfiguration(IniConfig config)
{
    // 验证IP地址
    if (string.IsNullOrWhiteSpace(config.Ip) || !IsValidIpAddress(config.Ip))
    {
        _logger.Error($"无效的IP地址: {config.Ip}");
        return false;
    }

    // 验证端口
    if (config.Port <= 0 || config.Port > 65535)
    {
        _logger.Error($"无效的端口号: {config.Port}");
        return false;
    }

    return true;
}

private bool IsValidIpAddress(string ipAddress)
{
    return System.Net.IPAddress.TryParse(ipAddress, out _);
}
```

## 配置安全性考虑

项目在配置管理方面考虑了安全性：

1. **默认配置备份**：应用程序包含默认配置，确保在用户配置损坏时可以恢复
2. **配置访问控制**：一些关键配置（如调试模式）通过特殊机制控制
3. **错误处理**：配置加载和解析过程有完善的错误处理
4. **日志记录**：配置相关操作都有详细日志

## 总结

Zishan.SS200.Cmd应用程序实现了灵活而全面的配置管理机制，具有以下特点：

1. **多格式支持**：使用INI、JSON和标记文件满足不同配置需求
2. **类型安全**：通过强类型配置类确保类型安全
3. **全局访问**：提供简单的全局访问机制
4. **验证机制**：实现配置验证确保有效性
5. **默认值**：提供合理默认值保证系统可运行
6. **错误处理**：完善的错误捕获和恢复机制

这种配置设计使得应用程序具有良好的可配置性和灵活性，能够适应不同的部署环境和用户需求。 