# T轴摆正偏差计算方法测试文档

## 方法概述

`CalculateAlignmentDeviation` 方法用于计算T轴当前位置到最近预设位置的偏差，实现T轴摆正功能。

## 方法签名

```csharp
private static TAxisAlignmentResult CalculateAlignmentDeviation(int currentTAxisPosition)
```

## 参数说明

- `currentTAxisPosition`: 当前T轴位置（步数）

## 返回值

返回 `TAxisAlignmentResult` 对象，包含：
- `NeedsAlignment`: 是否需要摆正（bool）
- `TargetPosition`: 目标摆正位置（int，步数）
- `MinDistance`: 最小距离（int）
- `ClosestParameterName`: 最接近的参数名称（string）

## 重要改进

**解决了目标位置为0的逻辑问题**：
- 之前：单纯判断返回值是否为0来决定是否摆正
- 问题：如果最接近的标准位置恰好是0（如RP4晶圆盒位置），会错误地认为不需要摆正
- 现在：使用 `NeedsAlignment` 标志位明确指示是否需要摆正，`TargetPosition` 单纯表示目标位置

## 算法逻辑

1. 获取所有T轴位置参数（RP1-RP8）：
   - RP1: T轴Smooth端到工艺腔室A
   - RP2: T轴Smooth端到工艺腔室B
   - RP3: T轴Smooth端到冷却腔（面向方向，无上下之分）
   - RP4: T轴Smooth端到晶圆盒
   - RP5: T轴Nose端到工艺腔室A
   - RP6: T轴Nose端到工艺腔室B
   - RP7: T轴Nose端到冷却腔（面向方向，无上下之分）
   - RP8: T轴Nose端到晶圆盒

### 重要设计理念

**T轴 vs Z轴的位置精度区别**：
- **T轴旋转**：只需要面向目标方向，对于冷却腔不区分上下层（Top/Bottom）
- **Z轴升降**：需要精确区分上下层位置（CoolingTop/CoolingBottom）

这是因为T轴负责旋转定位到目标区域，而Z轴负责精确的高度控制。

2. 计算当前位置到每个预设位置的绝对距离：`ABS(currentTAxisPosition - RPx)`

3. 找到最小距离及其对应的位置参数值

4. 返回最接近的位置参数值

## 测试用例

### 测试用例1：当前位置接近RP1
```
输入：currentTAxisPosition = 50000
预期：
- NeedsAlignment = true
- TargetPosition = 50100 (RP1)
- MinDistance = 100
- ClosestParameterName = "RP1"
```

### 测试用例2：当前位置接近RP4（重要：目标位置为0的情况）
```
输入：currentTAxisPosition = 100
预期：
- NeedsAlignment = true  ← 关键：即使目标位置是0，仍需要摆正
- TargetPosition = 0 (RP4)
- MinDistance = 100
- ClosestParameterName = "RP4"
```

### 测试用例3：当前位置正好在RP2
```
输入：currentTAxisPosition = 25000
预期：
- NeedsAlignment = false ← 距离为0，不需要摆正
- TargetPosition = 25000 (RP2)
- MinDistance = 0
- ClosestParameterName = "RP2"
```

### 测试用例4：当前位置在两个RP之间
```
输入：currentTAxisPosition = 37500
预期：
- NeedsAlignment = true
- TargetPosition = 25000 (RP2，更接近)
- MinDistance = 12500
- ClosestParameterName = "RP2"
```

## 实际配置值（来自RobotPositionParameters.json）

- RP1: 50100步 (T轴Smooth端到工艺腔室A)
- RP2: 25000步 (T轴Smooth端到工艺腔室B)
- RP3: 75000步 (T轴Smooth端到冷却腔)
- RP4: 0步 (T轴Smooth端到晶圆盒)
- RP5: 50步 (T轴Nose端到工艺腔室A)
- RP6: 75000步 (T轴Nose端到工艺腔室B)
- RP7: 25000步 (T轴Nose端到冷却腔)
- RP8: 50050步 (T轴Nose端到晶圆盒)

## 使用场景

该方法在T轴归零流程中使用，用于：

1. **摆正处理**：当机器人在chamber中时，先摆正再缩回
2. **安全考虑**：在L/L中摆正时不会撞到slit door
3. **精确定位**：确保T轴移动到最近的标准位置

## 日志输出示例

```
开始计算T轴摆正偏差，当前T轴位置: 50000
RP1位置: 50100, 距离: 100
RP2位置: 25000, 距离: 25000
RP3位置: 75000, 距离: 25000
RP4位置: 0, 距离: 50000
RP5位置: 50, 距离: 49950
RP6位置: 75000, 距离: 25000
RP7位置: 25000, 距离: 25000
RP8位置: 50050, 距离: 50
最接近的位置: RP8(50050), 最小距离: 50
需要摆正到位置: RP8(50050)
```

## 注意事项

1. 方法使用当前T轴位置作为输入参数，而不是R轴位置
2. 返回值是位置参数值（步数），不是RP编号
3. 异常情况下返回0，不执行摆正操作
4. 方法包含详细的日志输出，便于调试和监控
5. 冷却腔位置使用CoolingChamber，符合T轴旋转面向方向的设计理念

## 编译状态

✅ **编译成功** - 方法已成功实现并通过编译验证

## 单元测试

由于项目结构限制，单元测试文件应放置在独立的测试项目中，而不是主项目的Docs目录下。建议：

1. 创建独立的测试项目
2. 添加对主项目的引用
3. 使用反射或将方法改为internal来进行测试
