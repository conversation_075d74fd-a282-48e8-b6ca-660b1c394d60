﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Models.Aspen
{
    /// <summary>
    /// Aspen上料仿真
    /// </summary>
    public class AspenProcess
    {
        public CassetteSensorStatus LeftCassetteSensorStatus { get; set; } = new();
        public CassetteSensorStatus RightCassetteSensorStatus { get; set; } = new();

        public CassetteProcess ProcessUpCassetteCassette { get; set; } = new();
        public CassetteProcess ProcessDownCassetteCassette { get; set; } = new();

        public void Reset()
        {
            LeftCassetteSensorStatus.None = true;
            LeftCassetteSensorStatus.Inserted = false;
            LeftCassetteSensorStatus.Removed = false;

            RightCassetteSensorStatus.None = true;
            RightCassetteSensorStatus.Inserted = false;
            RightCassetteSensorStatus.Removed = false;

            ProcessUpCassetteCassette.None = true;
            ProcessUpCassetteCassette.ProcessStarted = false;
            ProcessUpCassetteCassette.UnloadMaterial = false;

            ProcessDownCassetteCassette.None = true;
            ProcessDownCassetteCassette.ProcessStarted = false;
            ProcessDownCassetteCassette.UnloadMaterial = false;
        }
    }
}