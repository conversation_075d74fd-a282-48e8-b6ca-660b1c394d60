using System.ComponentModel;

namespace Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle
{
    /// <summary>
    /// SSC6配置模式枚举
    /// 用于区分SMIF和FIXED两种配置模式，影响Shuttle位置状态和门巢状态的逻辑判断
    /// </summary>
    public enum EnuSSC6Config
    {
        /// <summary>
        /// SMIF配置模式
        /// - SSD3-SSD6包含cassette nest相关的I/O条件（DI4, DI5）
        /// - SSD8-SSD13全部6个状态都有效
        /// </summary>
        [Description("SMIF配置")]
        SMIF = 0,

        /// <summary>
        /// FIXED配置模式  
        /// - SSD3-SSD6不包含cassette nest相关条件
        /// - SSD8-SSD10只有3个门状态有效
        /// - SSD11-SSD13标记为N/A（不适用）
        /// </summary>
        [Description("FIXED配置")]
        FIXED = 1
    }
}
