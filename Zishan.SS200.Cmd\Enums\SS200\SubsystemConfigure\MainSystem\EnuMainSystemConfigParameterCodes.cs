using System;
using System.ComponentModel;

namespace Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.MainSystem
{
    /// <summary>
    /// 主系统配置代码枚举类型
    /// </summary>
    public enum EnuMainSystemConfigParameterCodes
    {
        /// <summary>
        /// shuttle 1 wafer size
        /// </summary>
        [Description("shuttle 1 wafer size")]
        SSC1 = 0,

        /// <summary>
        /// shuttle 2 wafer size
        /// </summary>
        [Description("shuttle 2 wafer size")]
        SSC2 = 1,

        /// <summary>
        /// chamber location
        /// </summary>
        [Description("chamber location")]
        SSC3 = 2,

        /// <summary>
        /// chamber A process
        /// </summary>
        [Description("chamber A process")]
        SSC4 = 3,

        /// <summary>
        /// chamber B process
        /// </summary>
        [Description("chamber B process")]
        SSC5 = 4,

        /// <summary>
        /// cassette nest type
        /// </summary>
        [Description("cassette nest type")]
        SSC6 = 5,

        /// <summary>
        /// slit door type
        /// </summary>
        [Description("slit door type")]
        SSC7 = 6,

        /// <summary>
        /// priority effective
        /// </summary>
        [Description("priority effective")]
        SSC8 = 7,

        /// <summary>
        /// mapping function
        /// </summary>
        [Description("mapping function")]
        SSC9 = 8,

        /// <summary>
        /// skip empty slot
        /// </summary>
        [Description("skip empty slot")]
        SSC10 = 9,

        /// <summary>
        /// temperature for cooling chamber
        /// </summary>
        [Description("temperature for cooling chamber")]
        SSC11 = 10,

        /// <summary>
        /// deviation for chamber temperature
        /// </summary>
        [Description("deviation for chamber temperature")]
        SSC12 = 11,

        /// <summary>
        /// transfer pressure
        /// </summary>
        [Description("transfer pressure")]
        SSC13 = 12,

        /// <summary>
        /// slot product order
        /// </summary>
        [Description("slot product order")]
        SSC14 = 13
    }
}