﻿using Prism.Mvvm;
using System.Collections.ObjectModel;
using System.Windows;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Models.IR400.PlcStatus
{
    /// <summary>
    /// 滑片信号,侦测到，PLC STOPA 停止运行，上位机不执行下条发送命令,路径规划流程停止并且退出
    /// </summary>
    public class WaferDropDetection : BindableBase
    {
        /// <summary>
        /// Robot 发送停止命令地址，用于判断是否存在停止命令
        /// </summary>
        public string StopACmdContentDetection { get => _StopACmdContentDetection; set => SetProperty(ref _StopACmdContentDetection, value); }
        private string _StopACmdContentDetection;

        /// <summary>
        /// CHA 是否有掉片
        /// </summary>
        public bool WaferDropDetection_CHA { get => _WaferDropDetection_CHA; set => SetProperty(ref _WaferDropDetection_CHA, value); }
        private bool _WaferDropDetection_CHA;

        /// <summary>
        /// CHB 是否有掉片
        /// </summary>
        public bool WaferDropDetection_CHB { get => _WaferDropDetection_CHB; set => SetProperty(ref _WaferDropDetection_CHB, value); }
        private bool _WaferDropDetection_CHB;

        /// <summary>
        /// CHC是否有掉片
        /// </summary>
        public bool WaferDropDetection_CHC { get => _WaferDropDetection_CHC; set => SetProperty(ref _WaferDropDetection_CHC, value); }
        private bool _WaferDropDetection_CHC;

        /// <summary>
        /// LoadLock否有掉片
        /// </summary>
        public bool WaferDropDetection_LoadLock_And_CP { get => _WaferDropDetection_LoadLock_And_CP; set => SetProperty(ref _WaferDropDetection_LoadLock_And_CP, value); }
        private bool _WaferDropDetection_LoadLock_And_CP;

        /// <summary>
        /// Stage_L 否有掉片
        /// </summary>
        public bool WaferDropDetection_Stage_L { get => _WaferDropDetection_Stage_L; set => SetProperty(ref _WaferDropDetection_Stage_L, value); }
        private bool _WaferDropDetection_Stage_L;

        /// <summary>
        /// Stage_R否有掉片
        /// </summary>
        public bool WaferDropDetection_Stage_R { get => _WaferDropDetection_Stage_R; set => SetProperty(ref _WaferDropDetection_Stage_R, value); }
        private bool _WaferDropDetection_Stage_R;

        /// <summary>
        /// 报警确认信息
        /// </summary>
        public bool AlarmConfirmed { get => _AlarmConfirmed; set => SetProperty(ref _AlarmConfirmed, value); }
        private bool _AlarmConfirmed;

        /// <summary>
        /// 是否有掉片
        /// </summary>
        public bool IsWaferDropDetection
        {
            get
            {
                return WaferDropDetection_CHA || WaferDropDetection_CHB || WaferDropDetection_CHC || WaferDropDetection_LoadLock_And_CP || WaferDropDetection_Stage_L || WaferDropDetection_Stage_R;
            }
        }

        /// <summary>
        /// 哪些有掉片，CHA、CHB、CHC、LoadLock、Stage_L、Stage_R字符串拼接
        /// </summary>
        public string WaferDropDetectionStr
        {
            get
            {
                string str = string.Empty;
                if (WaferDropDetection_CHA)
                {
                    str += "CHA,";
                }
                if (WaferDropDetection_CHB)
                {
                    str += "CHB,";
                }
                if (WaferDropDetection_CHC)
                {
                    str += "CHC,";
                }
                if (WaferDropDetection_LoadLock_And_CP)
                {
                    str += "LoadLock,";
                }
                if (WaferDropDetection_Stage_L)
                {
                    str += "Stage_L,";
                }
                if (WaferDropDetection_Stage_R)
                {
                    str += "Stage_R,";
                }
                if (str.Length > 0)
                {
                    str = str.Substring(0, str.Length - 1);
                }
                return str;
            }
        }
    }
}